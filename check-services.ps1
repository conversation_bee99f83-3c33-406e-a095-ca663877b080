# Quick service check script
Write-Host "Checking XAMPP Services..." -ForegroundColor Yellow

# Check Apache
$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host "✓ Apache: Running" -ForegroundColor Green
} else {
    Write-Host "✗ Apache: Not running" -ForegroundColor Red
}

# Check MySQL
$mysql = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysql) {
    Write-Host "✓ MySQL: Running" -ForegroundColor Green
} else {
    Write-Host "✗ MySQL: Not running" -ForegroundColor Red
}

# Test web server
Write-Host "Testing web server..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✓ Web server responding (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Web server not responding" -ForegroundColor Red
}

# Test project
Write-Host "Testing project..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/beersty-lovable" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✓ Project accessible" -ForegroundColor Green
} catch {
    Write-Host "✗ Project not accessible" -ForegroundColor Red
    Write-Host "  Make sure project files are in htdocs/beersty-lovable" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "URLs to test:" -ForegroundColor Cyan
Write-Host "  Main: http://localhost/beersty-lovable" -ForegroundColor White
Write-Host "  Admin: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
Write-Host "  phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
