<?php
/**
 * 404 Error Page - SEO Optimized
 * Beersty Platform
 */

$pageTitle = '404 - Page Not Found - ' . APP_NAME;
$additionalCSS = ['/assets/css/social-dark.css'];

require_once 'config/config.php';
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-lg-6 col-md-8 text-center">
            <!-- 404 Hero Section -->
            <div class="error-hero mb-5">
                <div class="error-icon mb-4">
                    <i class="fas fa-beer" style="font-size: 8rem; color: var(--beer-gold); opacity: 0.8;"></i>
                </div>
                <h1 class="display-1 fw-bold mb-3" style="color: var(--beer-gold);">404</h1>
                <h2 class="h3 mb-4" style="color: var(--text-primary);">Oops! This beer seems to have gone flat...</h2>
                <p class="lead mb-5" style="color: var(--text-secondary);">
                    The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
                </p>
            </div>

            <!-- Search Section -->
            <div class="card mb-5" style="background: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <div class="card-body p-4">
                    <h4 class="card-title mb-3" style="color: var(--text-primary);">
                        <i class="fas fa-search me-2" style="color: var(--beer-gold);"></i>
                        Find what you're looking for
                    </h4>
                    <form action="/search/" method="GET" class="mb-3">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   name="q" 
                                   placeholder="Search for beers, breweries, or users..."
                                   style="background: var(--bg-tertiary); border: 1px solid var(--border-primary); color: var(--text-primary);">
                            <button class="btn btn-primary btn-lg" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Navigation -->
            <div class="row g-3 mb-5">
                <div class="col-md-6">
                    <div class="card h-100" style="background: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <div class="card-body text-center">
                            <i class="fas fa-home fa-2x mb-3" style="color: var(--beer-gold);"></i>
                            <h5 class="card-title" style="color: var(--text-primary);">Go Home</h5>
                            <p class="card-text" style="color: var(--text-secondary);">Return to the homepage and start fresh</p>
                            <a href="/" class="btn btn-outline-primary">
                                <i class="fas fa-home me-2"></i>Homepage
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100" style="background: var(--bg-secondary); border: 1px solid var(--border-primary);">
                        <div class="card-body text-center">
                            <i class="fas fa-compass fa-2x mb-3" style="color: var(--beer-amber);"></i>
                            <h5 class="card-title" style="color: var(--text-primary);">Discover</h5>
                            <p class="card-text" style="color: var(--text-secondary);">Explore breweries and beers near you</p>
                            <a href="/breweries/discover/" class="btn btn-outline-primary">
                                <i class="fas fa-compass me-2"></i>Explore
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Popular Links -->
            <div class="card" style="background: var(--bg-secondary); border: 1px solid var(--border-primary);">
                <div class="card-header" style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-primary);">
                    <h5 class="mb-0" style="color: var(--text-primary);">
                        <i class="fas fa-star me-2" style="color: var(--beer-gold);"></i>
                        Popular Destinations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <h6 style="color: var(--beer-gold);">Discover</h6>
                            <ul class="list-unstyled">
                                <li><a href="/beers/discover/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-beer me-2"></i>Discover Beers
                                </a></li>
                                <li><a href="/breweries/discover/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-industry me-2"></i>Find Breweries
                                </a></li>
                                <li><a href="/beers/trending/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-fire me-2"></i>Trending Beers
                                </a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 style="color: var(--beer-gold);">Social</h6>
                            <ul class="list-unstyled">
                                <li><a href="/social/feed/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-stream me-2"></i>Activity Feed
                                </a></li>
                                <li><a href="/social/checkin/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-check-circle me-2"></i>Check In
                                </a></li>
                                <li><a href="/social/discover-users/" style="color: var(--text-secondary);" class="text-decoration-none">
                                    <i class="fas fa-users me-2"></i>Find Friends
                                </a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6 style="color: var(--beer-gold);">Account</h6>
                            <ul class="list-unstyled">
                                <?php if (isLoggedIn()): ?>
                                    <li><a href="/profile/edit/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-user me-2"></i>My Profile
                                    </a></li>
                                    <li><a href="/profile/preferences/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-cog me-2"></i>Settings
                                    </a></li>
                                    <li><a href="/profile/badges/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-medal me-2"></i>My Badges
                                    </a></li>
                                <?php else: ?>
                                    <li><a href="/account/login/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </a></li>
                                    <li><a href="/account/register/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-user-plus me-2"></i>Sign Up
                                    </a></li>
                                    <li><a href="/learn/beer-styles/" style="color: var(--text-secondary);" class="text-decoration-none">
                                        <i class="fas fa-graduation-cap me-2"></i>Learn About Beer
                                    </a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Support -->
            <div class="mt-5">
                <p style="color: var(--text-muted);">
                    Still can't find what you're looking for? 
                    <a href="/contact/" style="color: var(--beer-gold);" class="text-decoration-none">Contact our support team</a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Add some beer-themed animations -->
<style>
.error-hero {
    position: relative;
}

.error-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.card a:hover {
    color: var(--beer-gold) !important;
    transition: color 0.3s ease;
}

/* Beer bubble animation in background */
.error-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="bubbles" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,179,71,0.1)" opacity="0.6"><animate attributeName="r" values="0.5;2;0.5" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="80" r="1.5" fill="rgba(255,179,71,0.08)" opacity="0.4"><animate attributeName="r" values="0.8;2.5;0.8" dur="4s" repeatCount="indefinite"/></circle><circle cx="50" cy="60" r="1" fill="rgba(255,179,71,0.06)" opacity="0.3"><animate attributeName="r" values="0.3;1.8;0.3" dur="2.5s" repeatCount="indefinite"/></circle></pattern></defs><rect width="100" height="100" fill="url(%23bubbles)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
    z-index: -1;
}
</style>

<?php require_once 'includes/footer.php'; ?>
