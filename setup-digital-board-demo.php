<?php
/**
 * Digital Beer Board Demo Setup Script
 * Creates all necessary tables and demo data
 */

require_once 'config/config.php';

$pageTitle = 'Digital Beer Board Demo Setup';

// Check if already logged in
$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;

$setupComplete = false;
$setupMessages = [];
$errors = [];

// Handle setup execution
if (isset($_POST['run_setup'])) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        // Read and execute the SQL file
        $sqlFile = 'setup-digital-board-demo.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $conn->exec($statement);
                    } catch (Exception $e) {
                        // Log but don't stop for non-critical errors
                        error_log("SQL Warning: " . $e->getMessage());
                    }
                }
            }
            
            $setupMessages[] = "✅ Database tables created successfully";
            $setupMessages[] = "✅ Demo brewery 'Demo Craft Brewery' created";
            $setupMessages[] = "✅ Sample beer menu with 10 beers added";
            $setupMessages[] = "✅ Digital board configuration created";
            $setupMessages[] = "✅ Demo users created";
            
            $setupComplete = true;
            
        } else {
            $errors[] = "❌ SQL setup file not found: $sqlFile";
        }
        
    } catch (Exception $e) {
        $errors[] = "❌ Setup error: " . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .setup-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .demo-card { border: 2px solid #e9ecef; border-radius: 10px; transition: all 0.3s ease; }
        .demo-card:hover { border-color: #007bff; transform: translateY(-2px); }
        .feature-icon { font-size: 2rem; margin-bottom: 1rem; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="setup-container p-5">
                    <!-- Header -->
                    <div class="text-center mb-5">
                        <h1 class="display-4 mb-3">
                            <i class="fas fa-tv text-primary me-3"></i>
                            Digital Beer Board Demo
                        </h1>
                        <p class="lead text-muted">
                            Complete setup and demo of the Digital Beer Board system for business users
                        </p>
                    </div>
                    
                    <?php if (!$setupComplete && empty($_POST)): ?>
                        <!-- Setup Introduction -->
                        <div class="row mb-5">
                            <div class="col-md-4 mb-4">
                                <div class="demo-card p-4 text-center h-100">
                                    <i class="fas fa-database feature-icon text-primary"></i>
                                    <h5>Database Setup</h5>
                                    <p class="text-muted">Creates all necessary tables and relationships for the digital board system</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="demo-card p-4 text-center h-100">
                                    <i class="fas fa-building feature-icon text-success"></i>
                                    <h5>Demo Brewery</h5>
                                    <p class="text-muted">Sets up a complete demo brewery with sample beer menu and digital board</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="demo-card p-4 text-center h-100">
                                    <i class="fas fa-users feature-icon text-warning"></i>
                                    <h5>Test Users</h5>
                                    <p class="text-muted">Creates demo accounts for testing brewery and admin functionality</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current User Info -->
                        <?php if ($isLoggedIn): ?>
                            <div class="alert alert-info mb-4">
                                <h6><i class="fas fa-user me-2"></i>Currently Logged In</h6>
                                <p class="mb-0">
                                    <strong>Email:</strong> <?php echo htmlspecialchars($currentUser['email']); ?> |
                                    <strong>Role:</strong> <?php echo htmlspecialchars($currentUser['role']); ?>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Setup Form -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-play me-2"></i>Run Demo Setup
                                </h5>
                            </div>
                            <div class="card-body">
                                <p>This will create:</p>
                                <ul>
                                    <li><strong>Database Tables:</strong> digital_boards, beer_menu, beer_styles</li>
                                    <li><strong>Demo Brewery:</strong> "Demo Craft Brewery" with complete setup</li>
                                    <li><strong>Sample Beer Menu:</strong> 10 different beers with tap numbers and pricing</li>
                                    <li><strong>Digital Board:</strong> Pre-configured board ready for display</li>
                                    <li><strong>Test Users:</strong> Brewery user and admin user accounts</li>
                                </ul>
                                
                                <form method="POST" class="mt-4">
                                    <div class="d-grid">
                                        <button type="submit" name="run_setup" class="btn btn-primary btn-lg">
                                            <i class="fas fa-rocket me-2"></i>Setup Digital Beer Board Demo
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Setup Results -->
                    <?php if (!empty($setupMessages) || !empty($errors)): ?>
                        <div class="row">
                            <div class="col-12">
                                <?php foreach ($setupMessages as $message): ?>
                                    <div class="alert alert-success"><?php echo $message; ?></div>
                                <?php endforeach; ?>
                                
                                <?php foreach ($errors as $error): ?>
                                    <div class="alert alert-danger"><?php echo $error; ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Success and Next Steps -->
                    <?php if ($setupComplete): ?>
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-check-circle me-2"></i>Setup Complete!
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6>Demo Accounts Created:</h6>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-building me-2 text-success"></i>Brewery User
                                                </h6>
                                                <p class="card-text">
                                                    <strong>Email:</strong> <EMAIL><br>
                                                    <strong>Password:</strong> demo123<br>
                                                    <strong>Role:</strong> Brewery Manager
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-user-shield me-2 text-danger"></i>Admin User
                                                </h6>
                                                <p class="card-text">
                                                    <strong>Email:</strong> <EMAIL><br>
                                                    <strong>Password:</strong> demo123<br>
                                                    <strong>Role:</strong> System Admin
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <h6>Next Steps:</h6>
                                <div class="d-grid gap-2">
                                    <?php if (!$isLoggedIn): ?>
                                        <a href="login.php" class="btn btn-primary">
                                            <i class="fas fa-sign-in-alt me-2"></i>Login to Test the System
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="business/digital-board/index.php" class="btn btn-success">
                                        <i class="fas fa-tv me-2"></i>Access Digital Beer Board
                                    </a>
                                    
                                    <a href="business/digital-board/display.php?brewery_id=demo-brewery-1&board_id=demo-board-12345" 
                                       class="btn btn-info" target="_blank">
                                        <i class="fas fa-eye me-2"></i>View Live Display Demo
                                    </a>
                                    
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-home me-2"></i>Back to Homepage
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Demo Features Overview -->
                        <div class="mt-5">
                            <h5 class="mb-4">
                                <i class="fas fa-star me-2"></i>Demo Features Available
                            </h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6><i class="fas fa-cog me-2 text-primary"></i>Digital Board Management</h6>
                                            <ul class="small">
                                                <li>Enable/disable digital board</li>
                                                <li>Real-time beer availability control</li>
                                                <li>Display settings and themes</li>
                                                <li>Menu statistics and analytics</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6><i class="fas fa-tv me-2 text-success"></i>Live Display Features</h6>
                                            <ul class="small">
                                                <li>Full-screen kiosk mode</li>
                                                <li>Auto-refresh functionality</li>
                                                <li>Dark/light themes</li>
                                                <li>Grid/list layout options</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
