<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beersty Setup Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .step-card { margin-bottom: 1.5rem; }
        .step-number { 
            background: #0d6efd; 
            color: white; 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: bold;
        }
        .status-check { color: #198754; }
        .status-error { color: #dc3545; }
        .code-block { 
            background: #f8f9fa; 
            padding: 1rem; 
            border-radius: 0.375rem; 
            border-left: 4px solid #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1><i class="fas fa-beer me-3"></i>Beersty Setup Guide</h1>
                    <p class="lead">Get your brewery management system running in minutes!</p>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Current Issue:</strong> "Site can't be reached" means XAMPP needs to be installed and started.
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                
                <!-- Step 1: Install XAMPP -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">1</div>
                            <div class="flex-grow-1">
                                <h5 class="card-title">Install XAMPP</h5>
                                <p class="card-text">Download and install XAMPP to run PHP and MySQL locally.</p>
                                
                                <div class="mb-3">
                                    <strong>Download Link:</strong><br>
                                    <a href="https://www.apachefriends.org/download.html" target="_blank" class="btn btn-primary">
                                        <i class="fas fa-download me-2"></i>Download XAMPP
                                    </a>
                                </div>
                                
                                <div class="code-block">
                                    <strong>Installation Notes:</strong><br>
                                    • Install to: <code>C:\xampp</code><br>
                                    • Select: Apache, MySQL, PHP, phpMyAdmin<br>
                                    • Run installer as Administrator
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Start Services -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">2</div>
                            <div class="flex-grow-1">
                                <h5 class="card-title">Start XAMPP Services</h5>
                                <p class="card-text">Launch XAMPP Control Panel and start Apache and MySQL.</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Steps:</strong>
                                        <ol>
                                            <li>Open <code>C:\xampp\xampp-control.exe</code></li>
                                            <li>Run as Administrator</li>
                                            <li>Click "Start" for Apache</li>
                                            <li>Click "Start" for MySQL</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Test URLs:</strong>
                                        <ul>
                                            <li><a href="http://localhost/" target="_blank">http://localhost/</a></li>
                                            <li><a href="http://localhost/phpmyadmin/" target="_blank">http://localhost/phpmyadmin/</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Copy Files -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">3</div>
                            <div class="flex-grow-1">
                                <h5 class="card-title">Copy Project Files</h5>
                                <p class="card-text">Copy the Beersty project to XAMPP's web directory.</p>
                                
                                <div class="code-block">
                                    <strong>Copy this entire project folder to:</strong><br>
                                    <code>C:\xampp\htdocs\beersty\</code>
                                </div>
                                
                                <div class="mt-3">
                                    <strong>Or run this PowerShell command (as Administrator):</strong>
                                    <div class="code-block mt-2">
                                        <code>Copy-Item -Path "." -Destination "C:\xampp\htdocs\beersty" -Recurse -Force</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 4: Database Setup -->
                <div class="card step-card">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3">4</div>
                            <div class="flex-grow-1">
                                <h5 class="card-title">Set Up Database</h5>
                                <p class="card-text">Create the database and import the schema.</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Option A: phpMyAdmin</strong>
                                        <ol>
                                            <li>Go to <a href="http://localhost/phpmyadmin/" target="_blank">phpMyAdmin</a></li>
                                            <li>Click "New" to create database</li>
                                            <li>Name: <code>beersty_db</code></li>
                                            <li>Import <code>database/schema.sql</code></li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Option B: PowerShell</strong>
                                        <div class="code-block">
                                            <code>.\setup-database.ps1</code>
                                        </div>
                                        <small class="text-muted">Run from project directory</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 5: Access App -->
                <div class="card step-card border-success">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <div class="step-number me-3" style="background: #198754;">5</div>
                            <div class="flex-grow-1">
                                <h5 class="card-title text-success">Access Your Application</h5>
                                <p class="card-text">Once everything is set up, access your brewery management system!</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Application URLs:</strong>
                                        <ul>
                                            <li><a href="http://localhost/beersty/" target="_blank">Main App</a></li>
                                            <li><a href="http://localhost/beersty/admin/dashboard.php" target="_blank">Admin Dashboard</a></li>
                                            <li><a href="http://localhost/beersty/test-connection.php" target="_blank">Connection Test</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Default Login:</strong>
                                        <div class="code-block">
                                            Email: <code><EMAIL></code><br>
                                            Password: <code>admin123</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-wrench me-2"></i>Troubleshooting</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Apache Won't Start:</strong>
                                <ul>
                                    <li>Close Skype (uses port 80)</li>
                                    <li>Disable IIS if installed</li>
                                    <li>Run XAMPP as Administrator</li>
                                    <li>Change Apache port to 8080</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <strong>MySQL Won't Start:</strong>
                                <ul>
                                    <li>Stop other MySQL services</li>
                                    <li>Check port 3306 availability</li>
                                    <li>Run XAMPP as Administrator</li>
                                    <li>Restart computer if needed</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <strong>Still having issues?</strong> Check the <code>INSTALL-XAMPP-FIRST.md</code> file for detailed troubleshooting steps.
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        
        <div class="row mt-5">
            <div class="col-12 text-center">
                <p class="text-muted">
                    <i class="fas fa-beer me-2"></i>
                    Once XAMPP is running, your brewery management system will be ready to use!
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test if localhost is accessible
        fetch('http://localhost/')
            .then(response => {
                if (response.ok) {
                    document.querySelectorAll('a[href^="http://localhost"]').forEach(link => {
                        link.innerHTML += ' <i class="fas fa-check-circle status-check"></i>';
                    });
                }
            })
            .catch(error => {
                document.querySelectorAll('a[href^="http://localhost"]').forEach(link => {
                    link.innerHTML += ' <i class="fas fa-times-circle status-error"></i>';
                });
            });
    </script>
</body>
</html>
