# Start XAMPP Services
# PowerShell script to start MySQL and Apache services for Beersty

Write-Host "🚀 Starting XAMPP Services for Beersty" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Common XAMPP installation paths
$xamppPaths = @(
    "C:\xampp",
    "C:\Program Files\XAMPP",
    "C:\Program Files (x86)\XAMPP"
)

$xamppPath = $null
foreach ($path in $xamppPaths) {
    if (Test-Path "$path\xampp-control.exe") {
        $xamppPath = $path
        Write-Host "✅ Found XAMPP at: $path" -ForegroundColor Green
        break
    }
}

if (-not $xamppPath) {
    Write-Host "❌ XAMPP not found in common locations" -ForegroundColor Red
    Write-Host "Please install XAMPP or update the path in this script" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📥 Download XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🔍 Searching for XAMPP in other locations..." -ForegroundColor Yellow
    
    # Search for XAMPP in other drives
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 } | Select-Object -ExpandProperty DeviceID
    foreach ($drive in $drives) {
        $searchPath = "$drive\xampp"
        if (Test-Path "$searchPath\xampp-control.exe") {
            Write-Host "✅ Found XAMPP at: $searchPath" -ForegroundColor Green
            $xamppPath = $searchPath
            break
        }
    }
    
    if (-not $xamppPath) {
        Write-Host "❌ XAMPP still not found. Please install XAMPP first." -ForegroundColor Red
        pause
        exit 1
    }
}

Write-Host ""
Write-Host "📁 XAMPP Directory: $xamppPath" -ForegroundColor Cyan

# Check if MySQL is already running
Write-Host ""
Write-Host "🔍 Checking MySQL status..." -ForegroundColor Yellow

$mysqlRunning = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysqlRunning) {
    Write-Host "✅ MySQL is already running (PID: $($mysqlRunning.Id))" -ForegroundColor Green
} else {
    Write-Host "❌ MySQL is not running" -ForegroundColor Red
    Write-Host "🚀 Starting MySQL..." -ForegroundColor Yellow
    
    # Start MySQL service
    try {
        Start-Process -FilePath "$xamppPath\mysql\bin\mysqld.exe" -ArgumentList "--defaults-file=$xamppPath\mysql\bin\my.ini" -WindowStyle Hidden
        Start-Sleep -Seconds 3
        
        $mysqlRunning = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
        if ($mysqlRunning) {
            Write-Host "✅ MySQL started successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to start MySQL" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error starting MySQL: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Check if Apache is running
Write-Host ""
Write-Host "🔍 Checking Apache status..." -ForegroundColor Yellow

$apacheRunning = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apacheRunning) {
    Write-Host "✅ Apache is already running (PID: $($apacheRunning.Id))" -ForegroundColor Green
} else {
    Write-Host "❌ Apache is not running" -ForegroundColor Red
    Write-Host "ℹ️ Apache not needed for PHP development server" -ForegroundColor Cyan
}

# Test MySQL connection
Write-Host ""
Write-Host "🧪 Testing MySQL connection..." -ForegroundColor Yellow

try {
    $testConnection = New-Object System.Data.Odbc.OdbcConnection
    $testConnection.ConnectionString = "DRIVER={MySQL ODBC 8.0 Driver};SERVER=localhost;PORT=3306;UID=root;PWD=;"
    $testConnection.Open()
    $testConnection.Close()
    Write-Host "✅ MySQL connection test successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ MySQL connection test failed" -ForegroundColor Red
    Write-Host "Trying alternative connection test..." -ForegroundColor Yellow
    
    # Alternative test using mysql command line
    try {
        $mysqlTest = & "$xamppPath\mysql\bin\mysql.exe" -u root -e "SELECT 1;" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ MySQL command line test successful!" -ForegroundColor Green
        } else {
            Write-Host "❌ MySQL command line test failed: $mysqlTest" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Could not test MySQL connection" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open XAMPP Control Panel: $xamppPath\xampp-control.exe" -ForegroundColor White
Write-Host "2. Make sure MySQL is started (green)" -ForegroundColor White
Write-Host "3. Test database setup: http://localhost:8000/setup-database.php" -ForegroundColor White
Write-Host "4. Try login: http://localhost:8000/auth/login.php" -ForegroundColor White

Write-Host ""
Write-Host "🔧 Manual XAMPP Control:" -ForegroundColor Yellow
$response = Read-Host "Open XAMPP Control Panel now? (y/N)"
if ($response -eq "y" -or $response -eq "Y") {
    Start-Process -FilePath "$xamppPath\xampp-control.exe"
    Write-Host "✅ XAMPP Control Panel opened" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ XAMPP startup script completed!" -ForegroundColor Green
