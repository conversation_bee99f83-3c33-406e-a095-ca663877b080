@echo off
REM Beersty XAMPP Setup Batch File
REM This script helps set up the Beersty application with XAMPP

echo ========================================
echo    Beersty XAMPP Setup Assistant
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Checking XAMPP installation...

REM Check if XAMPP is installed
if not exist "C:\xampp\xampp-control.exe" (
    echo ERROR: XAMPP not found at C:\xampp\
    echo.
    echo Please install XAMPP first:
    echo 1. Download from: https://www.apachefriends.org/download.html
    echo 2. Install to C:\xampp
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✓ XAMPP found at C:\xampp\
echo.

REM Check if project is in correct location
if not exist "C:\xampp\htdocs\beersty\index.php" (
    echo Setting up project files...
    
    REM Create beersty directory
    if not exist "C:\xampp\htdocs\beersty" (
        mkdir "C:\xampp\htdocs\beersty"
    )
    
    REM Copy files if we're not already in the target directory
    if not "%CD%" == "C:\xampp\htdocs\beersty" (
        echo Copying project files to C:\xampp\htdocs\beersty\...
        xcopy "%~dp0*" "C:\xampp\htdocs\beersty\" /E /I /Y /Q
        echo ✓ Project files copied
    )
) else (
    echo ✓ Project files found in correct location
)

echo.

REM Check XAMPP services
echo Checking XAMPP services...

REM Check if Apache is running
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Apache is running
) else (
    echo ⚠ Apache is not running
    echo   Please start Apache in XAMPP Control Panel
)

REM Check if MySQL is running
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ MySQL is running
) else (
    echo ⚠ MySQL is not running
    echo   Please start MySQL in XAMPP Control Panel
)

echo.

REM Ask if user wants to open XAMPP Control Panel
set /p openXAMPP="Open XAMPP Control Panel to start services? (y/n): "
if /i "%openXAMPP%"=="y" (
    start "" "C:\xampp\xampp-control.exe"
    echo.
    echo Please start Apache and MySQL services, then press any key to continue...
    pause >nul
)

echo.

REM Run database setup
echo Setting up database...
cd /d "C:\xampp\htdocs\beersty"

if exist "setup-database.ps1" (
    echo Running PowerShell database setup script...
    powershell -ExecutionPolicy Bypass -File "setup-database.ps1"
    
    if %errorLevel% equ 0 (
        echo ✓ Database setup completed successfully
    ) else (
        echo ✗ Database setup failed
        echo Please check the error messages above
        echo.
        pause
        exit /b 1
    )
) else (
    echo ERROR: setup-database.ps1 not found
    echo Please ensure all project files are present
    pause
    exit /b 1
)

echo.

REM Set permissions for uploads directory
echo Setting up file permissions...
if exist "uploads" (
    icacls "uploads" /grant "Everyone:(OI)(CI)F" >nul 2>&1
    echo ✓ Upload directory permissions set
) else (
    mkdir "uploads"
    icacls "uploads" /grant "Everyone:(OI)(CI)F" >nul 2>&1
    echo ✓ Upload directory created with permissions
)

echo.
echo ========================================
echo           Setup Complete!
echo ========================================
echo.
echo Your Beersty application is ready!
echo.
echo URLs:
echo   Application: http://localhost/beersty/
echo   Admin Panel: http://localhost/beersty/admin/dashboard.php
echo   phpMyAdmin:  http://localhost/phpmyadmin/
echo.
echo Default Admin Account:
echo   Email:    <EMAIL>
echo   Password: admin123
echo.
echo Next Steps:
echo 1. Make sure Apache and MySQL are running in XAMPP
echo 2. Open http://localhost/beersty/ in your browser
echo 3. Log in with the admin account
echo 4. Start managing your breweries!
echo.

set /p openBrowser="Open application in browser now? (y/n): "
if /i "%openBrowser%"=="y" (
    start "" "http://localhost/beersty/"
)

echo.
echo Happy brewing! 🍺
echo.
pause
