<?php
require_once 'config/config.php';

$pageTitle = 'Dark Mode Test - ' . APP_NAME;
$additionalCSS = ['assets/css/auth.css'];

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">🌙 Dark Mode Test</h2>
                        <p class="text-muted">Test dark mode functionality and form styling</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5>🔧 Current Status:</h5>
                        <ul class="mb-0">
                            <li><strong>Server:</strong> ✅ PHP Development Server Running</li>
                            <li><strong>Database:</strong> ✅ SQLite Fallback Active</li>
                            <li><strong>Dark Mode:</strong> 🔄 Toggle in navigation menu</li>
                            <li><strong>Login:</strong> <EMAIL> / admin123</li>
                        </ul>
                    </div>
                    
                    <h4>🎨 Form Styling Test</h4>
                    <form class="mb-4">
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="testPassword" placeholder="Password">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testSelect" class="form-label">Select Option</label>
                            <select class="form-select" id="testSelect">
                                <option>Choose...</option>
                                <option value="1">Option 1</option>
                                <option value="2">Option 2</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="testTextarea" class="form-label">Textarea</label>
                            <textarea class="form-control" id="testTextarea" rows="3" placeholder="Enter text here..."></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="testCheck">
                            <label class="form-check-label" for="testCheck">
                                Check me out
                            </label>
                        </div>
                        
                        <button type="button" class="btn btn-primary">Test Button</button>
                        <button type="button" class="btn btn-secondary">Secondary</button>
                    </form>
                    
                    <h4>🌓 Dark Mode Instructions</h4>
                    <ol>
                        <li>Click your profile icon in the top navigation</li>
                        <li>Look for the "Theme" section in the dropdown</li>
                        <li>Click the "Dark" button to enable dark mode</li>
                        <li>Forms should automatically switch to dark styling</li>
                    </ol>
                    
                    <div class="text-center mt-4">
                        <a href="/auth/login.php" class="btn btn-success me-2">
                            <i class="fas fa-sign-in-alt me-2"></i>Test Login
                        </a>
                        <a href="/admin/dashboard.php" class="btn btn-warning me-2">
                            <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                        </a>
                        <a href="/" class="btn btn-info">
                            <i class="fas fa-home me-2"></i>Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add some JavaScript to test dark mode detection
document.addEventListener('DOMContentLoaded', function() {
    function checkDarkMode() {
        const isDark = document.documentElement.classList.contains('dark-mode');
        const status = isDark ? '🌙 Dark Mode Active' : '☀️ Light Mode Active';
        
        // Create or update status indicator
        let indicator = document.getElementById('mode-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'mode-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--bg-secondary);
                color: var(--text-primary);
                padding: 10px 15px;
                border-radius: 5px;
                border: 1px solid var(--border-primary);
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            `;
            document.body.appendChild(indicator);
        }
        
        indicator.textContent = status;
    }
    
    // Check initially
    checkDarkMode();
    
    // Check every second for changes
    setInterval(checkDarkMode, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
