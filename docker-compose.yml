version: '3.8'

services:
  # Web Server (Nginx)
  nginx:
    image: nginx:alpine
    container_name: beersty_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites-available:/etc/nginx/sites-available
      - ./docker/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - php
      - mysql
      - redis
    networks:
      - beersty_network
    restart: unless-stopped

  # PHP-FPM
  php:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: beersty_php
    volumes:
      - ./:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - ./docker/php/php-fpm.conf:/usr/local/etc/php-fpm.conf
      - ./uploads:/var/www/html/uploads
      - ./logs/php:/var/log/php
    environment:
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME:-beersty_production}
      - DB_USER=${DB_USER:-beersty_user}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - APP_ENV=production
      - APP_DEBUG=false
    depends_on:
      - mysql
      - redis
    networks:
      - beersty_network
    restart: unless-stopped

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: beersty_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME:-beersty_production}
      MYSQL_USER: ${DB_USER:-beersty_user}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./logs/mysql:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - beersty_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:alpine
    container_name: beersty_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - beersty_network
    restart: unless-stopped

  # Elasticsearch (for analytics and search)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: beersty_elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - beersty_network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: beersty_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - beersty_network
    restart: unless-stopped

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: beersty_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - beersty_network
    restart: unless-stopped

  # Node Exporter for system metrics
  node_exporter:
    image: prom/node-exporter:latest
    container_name: beersty_node_exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - beersty_network
    restart: unless-stopped

  # Backup service
  backup:
    build:
      context: .
      dockerfile: docker/backup/Dockerfile
    container_name: beersty_backup
    volumes:
      - mysql_data:/backup/mysql
      - ./uploads:/backup/uploads
      - ./backups:/backups
      - ./docker/backup/scripts:/scripts
    environment:
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME:-beersty_production}
      - DB_USER=${DB_USER:-beersty_user}
      - DB_PASSWORD=${DB_PASSWORD}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
    depends_on:
      - mysql
    networks:
      - beersty_network
    restart: unless-stopped

  # SSL Certificate Management
  certbot:
    image: certbot/certbot
    container_name: beersty_certbot
    volumes:
      - ./docker/ssl:/etc/letsencrypt
      - ./:/var/www/html
    command: certonly --webroot --webroot-path=/var/www/html --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}
    networks:
      - beersty_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  beersty_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
