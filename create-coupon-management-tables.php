<?php
// Create Coupon Management System Tables
require_once 'config/config.php';

echo "=== Creating Coupon Management System Tables ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Create coupon_categories table
    echo "Creating coupon_categories table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS coupon_categories (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50),
            color VARCHAR(7) DEFAULT '#FFC107',
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_active (is_active),
            INDEX idx_sort_order (sort_order)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created coupon_categories table" . PHP_EOL;
    
    // Create coupons table
    echo "Creating coupons table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS coupons (
            id VARCHAR(36) PRIMARY KEY,
            place_id VARCHAR(36) NOT NULL,
            category_id VARCHAR(36),
            code VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            discount_type ENUM('percentage', 'fixed_amount', 'buy_x_get_y', 'free_item') NOT NULL,
            discount_value DECIMAL(10,2) NOT NULL,
            minimum_purchase DECIMAL(10,2) DEFAULT 0,
            maximum_discount DECIMAL(10,2),
            usage_limit INT,
            usage_limit_per_user INT DEFAULT 1,
            used_count INT DEFAULT 0,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_of_week SET('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'),
            start_time TIME,
            end_time TIME,
            terms_conditions TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_featured BOOLEAN DEFAULT 0,
            requires_approval BOOLEAN DEFAULT 0,
            approved_by VARCHAR(36),
            approved_at TIMESTAMP NULL,
            created_by VARCHAR(36) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (place_id) REFERENCES breweries(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES coupon_categories(id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_place (place_id),
            INDEX idx_category (category_id),
            INDEX idx_code (code),
            INDEX idx_active (is_active),
            INDEX idx_featured (is_featured),
            INDEX idx_dates (start_date, end_date),
            INDEX idx_created (created_at),
            UNIQUE KEY unique_place_code (place_id, code)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created coupons table" . PHP_EOL;
    
    // Create coupon_redemptions table
    echo "Creating coupon_redemptions table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS coupon_redemptions (
            id VARCHAR(36) PRIMARY KEY,
            coupon_id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36) NOT NULL,
            redemption_code VARCHAR(100),
            redeemed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            order_amount DECIMAL(10,2),
            discount_amount DECIMAL(10,2),
            notes TEXT,
            status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
            FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_coupon (coupon_id),
            INDEX idx_user (user_id),
            INDEX idx_redeemed (redeemed_at),
            INDEX idx_status (status)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created coupon_redemptions table" . PHP_EOL;
    
    // Create coupon_views table
    echo "Creating coupon_views table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS coupon_views (
            id VARCHAR(36) PRIMARY KEY,
            coupon_id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36),
            ip_address VARCHAR(45),
            user_agent TEXT,
            viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_coupon (coupon_id),
            INDEX idx_user (user_id),
            INDEX idx_viewed (viewed_at)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created coupon_views table" . PHP_EOL;
    
    // Create coupon_favorites table
    echo "Creating coupon_favorites table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS coupon_favorites (
            id VARCHAR(36) PRIMARY KEY,
            coupon_id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_coupon (coupon_id),
            INDEX idx_user (user_id),
            UNIQUE KEY unique_coupon_favorite (coupon_id, user_id)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created coupon_favorites table" . PHP_EOL;
    
    // Insert default coupon categories
    echo "Creating default coupon categories..." . PHP_EOL;
    $default_categories = [
        ['Food & Drinks', 'Discounts on food and beverages', 'fas fa-utensils', '#FF6B35', 1],
        ['Happy Hour', 'Special happy hour deals', 'fas fa-clock', '#4ECDC4', 2],
        ['Beer Specials', 'Beer discounts and promotions', 'fas fa-beer', '#FFE66D', 3],
        ['Events', 'Event tickets and special occasions', 'fas fa-calendar-alt', '#A8E6CF', 4],
        ['Merchandise', 'Branded merchandise and retail items', 'fas fa-tshirt', '#FF8B94', 5],
        ['Loyalty Rewards', 'Customer loyalty and repeat visit rewards', 'fas fa-star', '#B4A7D6', 6],
        ['New Customer', 'First-time customer specials', 'fas fa-user-plus', '#D4A574', 7],
        ['Seasonal', 'Holiday and seasonal promotions', 'fas fa-leaf', '#87CEEB', 8]
    ];
    
    foreach ($default_categories as [$name, $description, $icon, $color, $sort_order]) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO coupon_categories (id, name, description, icon, color, sort_order, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$name, $description, $icon, $color, $sort_order]);
    }
    
    // Create sample coupons for first 5 places
    echo "Creating sample coupons..." . PHP_EOL;
    $stmt = $pdo->query("SELECT id FROM breweries LIMIT 5");
    $places = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $pdo->query("SELECT id FROM coupon_categories LIMIT 4");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $stmt = $pdo->query("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
    $admin_user = $stmt->fetchColumn();
    
    if ($admin_user && !empty($places) && !empty($categories)) {
        $sample_coupons = [
            ['WELCOME20', '20% Off First Visit', 'Get 20% off your first order with us!', 'percentage', 20.00, 25.00],
            ['HAPPYHOUR', 'Happy Hour Special', 'Buy one beer, get one 50% off during happy hour', 'percentage', 50.00, 0.00],
            ['FREEFRIES', 'Free Appetizer', 'Free appetizer with any entree purchase', 'free_item', 0.00, 15.00],
            ['SAVE10', '$10 Off $50', 'Save $10 when you spend $50 or more', 'fixed_amount', 10.00, 50.00]
        ];
        
        foreach ($places as $place_id) {
            foreach ($sample_coupons as $index => [$code, $title, $description, $type, $value, $min_purchase]) {
                $category_id = $categories[$index % count($categories)];
                $start_date = date('Y-m-d');
                $end_date = date('Y-m-d', strtotime('+30 days'));
                
                $stmt = $pdo->prepare("
                    INSERT INTO coupons (
                        id, place_id, category_id, code, title, description, 
                        discount_type, discount_value, minimum_purchase, 
                        start_date, end_date, usage_limit, created_by, created_at
                    ) VALUES (
                        UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 100, ?, NOW()
                    )
                ");
                $stmt->execute([
                    $place_id, $category_id, $code . '_' . substr($place_id, 0, 8), 
                    $title, $description, $type, $value, $min_purchase, 
                    $start_date, $end_date, $admin_user
                ]);
            }
        }
    }
    
    // Show statistics
    echo "\n=== Coupon Management System Statistics ===" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_categories");
    $categories_count = $stmt->fetchColumn();
    echo "Coupon categories created: $categories_count" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
    $coupons_count = $stmt->fetchColumn();
    echo "Sample coupons created: $coupons_count" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_redemptions");
    $redemptions_count = $stmt->fetchColumn();
    echo "Total redemptions: $redemptions_count" . PHP_EOL;
    
    echo "\n=== Coupon Management System Setup Complete ===" . PHP_EOL;
    echo "✓ All tables created successfully" . PHP_EOL;
    echo "✓ Default categories created" . PHP_EOL;
    echo "✓ Sample coupons created for testing" . PHP_EOL;
    echo "✓ Foreign key relationships established" . PHP_EOL;
    echo "✓ Indexes created for performance" . PHP_EOL;
    
    echo "\nFeatures available:" . PHP_EOL;
    echo "• Coupon creation and management" . PHP_EOL;
    echo "• Multiple discount types (%, fixed, BOGO, free item)" . PHP_EOL;
    echo "• Usage limits and restrictions" . PHP_EOL;
    echo "• Time-based restrictions (dates, days, hours)" . PHP_EOL;
    echo "• Coupon categories and organization" . PHP_EOL;
    echo "• Redemption tracking and analytics" . PHP_EOL;
    echo "• User favorites and views tracking" . PHP_EOL;
    echo "• Admin approval workflow" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
