#!/bin/bash

# Beersty Digital Board System - Production Deployment Script
# Phase 8 - Production Deployment & Optimization

set -e  # Exit on any error

# Configuration
PROJECT_NAME="beersty-digital-board"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.production"
BACKUP_DIR="backups/deployment"
LOG_FILE="logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found. Please create it first."
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running. Please start Docker first."
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    directories=(
        "logs"
        "backups"
        "uploads"
        "cache"
        "docker/nginx/sites-available"
        "docker/ssl"
        "docker/mysql"
        "docker/redis"
        "docker/php"
        "docker/backup/scripts"
        "docker/prometheus"
        "docker/grafana/provisioning"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "Created directory: $dir"
        fi
    done
    
    success "Directories created successfully"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database if container exists
    if docker ps -a | grep -q "${PROJECT_NAME}_mysql"; then
        log "Backing up database..."
        docker exec "${PROJECT_NAME}_mysql" mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$BACKUP_PATH/database_backup.sql"
    fi
    
    # Backup uploads directory
    if [ -d "uploads" ]; then
        log "Backing up uploads..."
        cp -r uploads "$BACKUP_PATH/"
    fi
    
    # Backup configuration files
    log "Backing up configuration files..."
    cp -r config "$BACKUP_PATH/" 2>/dev/null || true
    
    success "Backup created at $BACKUP_PATH"
}

# Build and deploy containers
deploy_containers() {
    log "Building and deploying containers..."
    
    # Load environment variables
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Build custom images
    log "Building custom images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    # Start services
    log "Starting services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Containers deployed successfully"
}

# Wait for services to be ready
wait_for_services() {
    log "Waiting for services to be ready..."
    
    # Wait for MySQL
    log "Waiting for MySQL to be ready..."
    timeout=60
    while ! docker exec "${PROJECT_NAME}_mysql" mysqladmin ping -h"localhost" --silent; do
        timeout=$((timeout - 1))
        if [ $timeout -eq 0 ]; then
            error "MySQL failed to start within 60 seconds"
        fi
        sleep 1
    done
    
    # Wait for Redis
    log "Waiting for Redis to be ready..."
    timeout=30
    while ! docker exec "${PROJECT_NAME}_redis" redis-cli ping | grep -q PONG; do
        timeout=$((timeout - 1))
        if [ $timeout -eq 0 ]; then
            error "Redis failed to start within 30 seconds"
        fi
        sleep 1
    done
    
    # Wait for Nginx
    log "Waiting for Nginx to be ready..."
    timeout=30
    while ! docker exec "${PROJECT_NAME}_nginx" nginx -t; do
        timeout=$((timeout - 1))
        if [ $timeout -eq 0 ]; then
            error "Nginx failed to start within 30 seconds"
        fi
        sleep 1
    done
    
    success "All services are ready"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Check if migration files exist
    if [ -d "database/migrations" ]; then
        for migration in database/migrations/*.sql; do
            if [ -f "$migration" ]; then
                log "Running migration: $(basename "$migration")"
                docker exec -i "${PROJECT_NAME}_mysql" mysql -u root -p"$MYSQL_ROOT_PASSWORD" "$DB_NAME" < "$migration"
            fi
        done
    fi
    
    success "Database migrations completed"
}

# Set up SSL certificates
setup_ssl() {
    log "Setting up SSL certificates..."
    
    if [ "$SSL_ENABLED" = "true" ] && [ -n "$DOMAIN_NAME" ]; then
        log "Requesting SSL certificate for $DOMAIN_NAME..."
        
        # Create webroot directory
        mkdir -p docker/ssl/webroot
        
        # Request certificate using Certbot
        docker run --rm \
            -v "$(pwd)/docker/ssl:/etc/letsencrypt" \
            -v "$(pwd)/docker/ssl/webroot:/var/www/html" \
            certbot/certbot certonly \
            --webroot \
            --webroot-path=/var/www/html \
            --email "$SSL_EMAIL" \
            --agree-tos \
            --no-eff-email \
            -d "$DOMAIN_NAME"
        
        if [ $? -eq 0 ]; then
            success "SSL certificate obtained successfully"
        else
            warning "SSL certificate request failed. Continuing without SSL."
        fi
    else
        log "SSL not enabled or domain not configured"
    fi
}

# Configure monitoring
setup_monitoring() {
    log "Setting up monitoring and alerting..."
    
    # Create Prometheus configuration
    cat > docker/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node_exporter:9100']

  - job_name: 'beersty-app'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/metrics'
    scrape_interval: 30s
EOF

    # Create Grafana provisioning
    mkdir -p docker/grafana/provisioning/dashboards
    mkdir -p docker/grafana/provisioning/datasources
    
    cat > docker/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    success "Monitoring configuration completed"
}

# Optimize performance
optimize_performance() {
    log "Applying performance optimizations..."
    
    # Set PHP-FPM configuration
    cat > docker/php/php-fpm.conf << EOF
[global]
pid = /var/run/php-fpm.pid
error_log = /var/log/php/php-fpm.log
daemonize = no

[www]
user = www-data
group = www-data
listen = 9000
listen.owner = www-data
listen.group = www-data
pm = dynamic
pm.max_children = 20
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
pm.max_requests = 500
EOF

    # Set MySQL configuration
    cat > docker/mysql/my.cnf << EOF
[mysqld]
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 32M
max_connections = 100
thread_cache_size = 8
table_open_cache = 2000
EOF

    # Set Redis configuration
    cat > docker/redis/redis.conf << EOF
maxmemory 256mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
EOF

    success "Performance optimizations applied"
}

# Run health checks
run_health_checks() {
    log "Running health checks..."
    
    # Check application health
    if curl -f http://localhost/health > /dev/null 2>&1; then
        success "Application health check passed"
    else
        warning "Application health check failed"
    fi
    
    # Check database connectivity
    if docker exec "${PROJECT_NAME}_mysql" mysqladmin ping -h"localhost" --silent; then
        success "Database health check passed"
    else
        error "Database health check failed"
    fi
    
    # Check Redis connectivity
    if docker exec "${PROJECT_NAME}_redis" redis-cli ping | grep -q PONG; then
        success "Redis health check passed"
    else
        error "Redis health check failed"
    fi
}

# Setup backup automation
setup_backup_automation() {
    log "Setting up backup automation..."
    
    # Create backup script
    cat > docker/backup/scripts/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="$BACKUP_DIR/automated_backup_$TIMESTAMP"

mkdir -p "$BACKUP_PATH"

# Backup database
mysqldump -h mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_PATH/database.sql"

# Backup uploads
cp -r /backup/uploads "$BACKUP_PATH/" 2>/dev/null || true

# Compress backup
tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "automated_backup_$TIMESTAMP"
rm -rf "$BACKUP_PATH"

# Clean old backups (keep last 30 days)
find "$BACKUP_DIR" -name "automated_backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_PATH.tar.gz"
EOF

    chmod +x docker/backup/scripts/backup.sh
    
    success "Backup automation configured"
}

# Main deployment function
main() {
    log "Starting Beersty Digital Board System deployment..."
    
    check_prerequisites
    create_directories
    backup_data
    optimize_performance
    setup_monitoring
    deploy_containers
    wait_for_services
    run_migrations
    setup_ssl
    setup_backup_automation
    run_health_checks
    
    success "Deployment completed successfully!"
    log "Access your application at: http://localhost (or https://$DOMAIN_NAME if SSL is configured)"
    log "Grafana monitoring: http://localhost:3000 (admin/admin)"
    log "Prometheus metrics: http://localhost:9090"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_data
        ;;
    "health")
        run_health_checks
        ;;
    "ssl")
        setup_ssl
        ;;
    "monitoring")
        setup_monitoring
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|ssl|monitoring}"
        exit 1
        ;;
esac
