<?php
/**
 * Simple Database Setup for Beersty
 * Creates database and user without requiring root access
 */

echo "<h2>🍺 Beersty Database Setup</h2>";

// Database configuration
$host = 'localhost';
$db_name = 'beersty_db';
$username = 'beersty_user';
$password = 'beersty_pass';

try {
    // First, try to connect without specifying database to create it
    echo "<p>📡 Attempting to connect to MySQL...</p>";
    
    // Try different connection methods
    $connection_methods = [
        ['host' => $host, 'user' => 'root', 'pass' => ''],
        ['host' => $host, 'user' => 'root', 'pass' => 'root'],
        ['host' => $host, 'user' => '', 'pass' => ''],
        ['host' => $host, 'user' => $username, 'pass' => $password]
    ];
    
    $pdo = null;
    $successful_method = null;
    
    foreach ($connection_methods as $method) {
        try {
            echo "<p>🔄 Trying connection with user: '{$method['user']}'...</p>";
            $dsn = "mysql:host={$method['host']};charset=utf8mb4";
            $pdo = new PDO($dsn, $method['user'], $method['pass']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $successful_method = $method;
            echo "<p>✅ Connected successfully with user: '{$method['user']}'</p>";
            break;
        } catch (PDOException $e) {
            echo "<p>❌ Failed with user '{$method['user']}': " . $e->getMessage() . "</p>";
            continue;
        }
    }
    
    if (!$pdo) {
        throw new Exception("Could not establish database connection with any method");
    }
    
    // Create database if it doesn't exist
    echo "<p>🗄️ Creating database '$db_name'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ Database '$db_name' created/verified</p>";
    
    // Try to create user (might fail if user already exists or no permissions)
    try {
        echo "<p>👤 Creating database user '$username'...</p>";
        $pdo->exec("CREATE USER IF NOT EXISTS '$username'@'localhost' IDENTIFIED BY '$password'");
        $pdo->exec("GRANT ALL PRIVILEGES ON `$db_name`.* TO '$username'@'localhost'");
        $pdo->exec("FLUSH PRIVILEGES");
        echo "<p>✅ Database user '$username' created/updated</p>";
    } catch (PDOException $e) {
        echo "<p>⚠️ Could not create user (might already exist): " . $e->getMessage() . "</p>";
    }
    
    // Connect to the specific database
    echo "<p>🔗 Connecting to database '$db_name'...</p>";
    $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4";
    
    // Try with the new user first, then fallback to the successful method
    try {
        $db_pdo = new PDO($dsn, $username, $password);
        echo "<p>✅ Connected to '$db_name' with user '$username'</p>";
    } catch (PDOException $e) {
        echo "<p>⚠️ Could not connect with '$username', using fallback method...</p>";
        $db_pdo = new PDO($dsn, $successful_method['user'], $successful_method['pass']);
        echo "<p>✅ Connected to '$db_name' with fallback user</p>";
    }
    
    $db_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create tables
    echo "<p>📋 Creating tables...</p>";
    
    // Users table
    $db_pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            email_verified BOOLEAN DEFAULT FALSE,
            last_login TIMESTAMP NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ Users table created</p>";
    
    // Profiles table
    $db_pdo->exec("
        CREATE TABLE IF NOT EXISTS profiles (
            id VARCHAR(36) PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            role ENUM('admin', 'brewery', 'customer') DEFAULT 'customer',
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            brewery_id VARCHAR(36) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ Profiles table created</p>";
    
    // Breweries table
    $db_pdo->exec("
        CREATE TABLE IF NOT EXISTS breweries (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            city VARCHAR(100),
            state VARCHAR(100),
            brewery_type ENUM('micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor') DEFAULT 'micro',
            claimed BOOLEAN DEFAULT FALSE,
            verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p>✅ Breweries table created</p>";
    
    // Check if admin user exists
    $stmt = $db_pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $adminExists = $stmt->fetchColumn();
    
    if ($adminExists == 0) {
        echo "<p>👑 Creating default admin user...</p>";
        
        $adminId = 'admin-user-id';
        $adminEmail = '<EMAIL>';
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        // Insert admin user
        $stmt = $db_pdo->prepare("INSERT INTO users (id, email, password_hash, email_verified) VALUES (?, ?, ?, 1)");
        $stmt->execute([$adminId, $adminEmail, $adminPassword]);
        
        // Insert admin profile
        $stmt = $db_pdo->prepare("INSERT INTO profiles (id, email, role, first_name, last_name) VALUES (?, ?, 'admin', 'Admin', 'User')");
        $stmt->execute([$adminId, $adminEmail]);
        
        echo "<p>✅ Default admin user created</p>";
        echo "<p>📧 <strong>Email:</strong> <EMAIL></p>";
        echo "<p>🔑 <strong>Password:</strong> admin123</p>";
    } else {
        echo "<p>ℹ️ Admin user already exists</p>";
    }
    
    // Create sample brewery
    $stmt = $db_pdo->prepare("SELECT COUNT(*) FROM breweries WHERE name = ?");
    $stmt->execute(['Demo Craft Brewery']);
    $breweryExists = $stmt->fetchColumn();
    
    if ($breweryExists == 0) {
        echo "<p>🏭 Creating sample brewery...</p>";
        $stmt = $db_pdo->prepare("
            INSERT INTO breweries (id, name, description, city, state, brewery_type, claimed, verified) 
            VALUES ('sample-brewery-1', 'Demo Craft Brewery', 'A sample brewery for testing the system', 'Demo City', 'Demo State', 'micro', TRUE, TRUE)
        ");
        $stmt->execute();
        echo "<p>✅ Sample brewery created</p>";
    } else {
        echo "<p>ℹ️ Sample brewery already exists</p>";
    }
    
    echo "<h3>🎉 Database Setup Complete!</h3>";
    echo "<p><strong>You can now:</strong></p>";
    echo "<ul>";
    echo "<li>🏠 <a href='/'>Visit the homepage</a></li>";
    echo "<li>🔐 <a href='/auth/login.php'><NAME_EMAIL> / admin123</a></li>";
    echo "<li>⚙️ <a href='/admin/dashboard.php'>Access the admin dashboard</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Suggestions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure MySQL/MariaDB is running</li>";
    echo "<li>Check if you have database creation permissions</li>";
    echo "<li>Try running: <code>sudo systemctl start mysql</code></li>";
    echo "</ul>";
}
?>
