<?php
/**
 * Debug Login Issues
 * Test login functionality step by step
 */

require_once 'config/config.php';

echo "<h1>🔍 Login Debug Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Testing Database Connection...</h2>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Test 2: Check if admin user exists
echo "<h2>2. Checking Admin User...</h2>";
try {
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ Admin user found</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . htmlspecialchars($user['id']) . "</li>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</li>";
        echo "<li><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</li>";
        echo "<li><strong>Password Hash:</strong> " . substr($user['password_hash'], 0, 20) . "...</li>";
        echo "</ul>";
    } else {
        echo "<p>❌ Admin user not found</p>";
        
        // Try to create admin user
        echo "<p>🔧 Creating admin user...</p>";
        try {
            $userId = bin2hex(random_bytes(16));
            $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
            
            $conn->beginTransaction();
            
            // Insert user
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
            
            // Insert profile
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
            $stmt->execute([$userId, '<EMAIL>']);
            
            $conn->commit();
            echo "<p>✅ Admin user created successfully</p>";
            
            // Re-fetch the user
            $stmt = $conn->prepare("
                SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
                FROM users u 
                JOIN profiles p ON u.id = p.id 
                WHERE u.email = ?
            ");
            $stmt->execute(['<EMAIL>']);
            $user = $stmt->fetch();
            
        } catch (Exception $e) {
            $conn->rollback();
            echo "<p>❌ Failed to create admin user: " . htmlspecialchars($e->getMessage()) . "</p>";
            exit;
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking admin user: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Test 3: Test password verification
echo "<h2>3. Testing Password Verification...</h2>";
$testPassword = 'admin123';
if (password_verify($testPassword, $user['password_hash'])) {
    echo "<p>✅ Password verification successful</p>";
} else {
    echo "<p>❌ Password verification failed</p>";
    
    // Fix password
    echo "<p>🔧 Fixing password...</p>";
    $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
    $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
    $stmt->execute([$newHash, '<EMAIL>']);
    echo "<p>✅ Password updated</p>";
}

// Test 4: Test session functions
echo "<h2>4. Testing Session Functions...</h2>";
if (function_exists('isLoggedIn')) {
    echo "<p>✅ isLoggedIn() function exists</p>";
} else {
    echo "<p>❌ isLoggedIn() function missing</p>";
}

if (function_exists('getCurrentUser')) {
    echo "<p>✅ getCurrentUser() function exists</p>";
} else {
    echo "<p>❌ getCurrentUser() function missing</p>";
}

if (function_exists('sanitizeInput')) {
    echo "<p>✅ sanitizeInput() function exists</p>";
} else {
    echo "<p>❌ sanitizeInput() function missing</p>";
}

// Test 5: Simulate login process
echo "<h2>5. Simulating Login Process...</h2>";
$email = '<EMAIL>';
$password = 'admin123';

echo "<p><strong>Testing with:</strong></p>";
echo "<ul>";
echo "<li><strong>Email:</strong> $email</li>";
echo "<li><strong>Password:</strong> $password</li>";
echo "</ul>";

try {
    // Get user by email
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute([$email]);
    $loginUser = $stmt->fetch();
    
    if ($loginUser && password_verify($password, $loginUser['password_hash'])) {
        echo "<p>✅ Login simulation successful!</p>";
        echo "<p>User would be redirected to: <strong>/admin/dashboard.php</strong></p>";
    } else {
        echo "<p>❌ Login simulation failed</p>";
        if (!$loginUser) {
            echo "<p>Reason: User not found</p>";
        } else {
            echo "<p>Reason: Password verification failed</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Login simulation error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 6: Check login page
echo "<h2>6. Checking Login Page...</h2>";
if (file_exists('auth/login.php')) {
    echo "<p>✅ Login page exists</p>";
} else {
    echo "<p>❌ Login page missing</p>";
}

// Test 7: Check for common issues
echo "<h2>7. Common Issues Check...</h2>";

// Check if sessions are started
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p>✅ Sessions are active</p>";
} else {
    echo "<p>❌ Sessions not started</p>";
}

// Check error reporting
$errorReporting = error_reporting();
echo "<p>Error reporting level: " . $errorReporting . "</p>";

// Check if error messages are being stored
if (isset($_SESSION['error_message'])) {
    echo "<p>⚠️ Session error message: " . htmlspecialchars($_SESSION['error_message']) . "</p>";
    unset($_SESSION['error_message']);
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Try Login Again:</h3>";
echo "<ul>";
echo "<li><strong>URL:</strong> <a href='auth/login.php'>http://localhost:8000/auth/login.php</a></li>";
echo "<li><strong>Email:</strong> <code><EMAIL></code></li>";
echo "<li><strong>Password:</strong> <code>admin123</code></li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔗 Quick Links</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>Go to Login Page</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "<li><a href='test-login.php' class='btn btn-info'>Run Login Test</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
