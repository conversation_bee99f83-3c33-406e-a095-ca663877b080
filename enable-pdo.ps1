# Enable PDO Extensions in XAMPP
# This script automatically enables PDO MySQL extensions

Write-Host "PDO Extension Enabler for XAMPP" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

# Find XAMPP installation
$xamppPaths = @(
    "C:\xampp",
    "C:\Program Files\XAMPP", 
    "C:\Program Files (x86)\XAMPP",
    "D:\xampp",
    "E:\xampp"
)

$xamppPath = $null
foreach ($path in $xamppPaths) {
    if (Test-Path $path) {
        $xamppPath = $path
        break
    }
}

if (-not $xamppPath) {
    Write-Host "ERROR: XAMPP not found in common locations." -ForegroundColor Red
    Write-Host "Please install XAMPP first." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found XAMPP at: $xamppPath" -ForegroundColor Green

# Find php.ini file
$phpIniPath = Join-Path $xamppPath "php\php.ini"
if (-not (Test-Path $phpIniPath)) {
    Write-Host "ERROR: php.ini not found at: $phpIniPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found php.ini at: $phpIniPath" -ForegroundColor Green

# Backup php.ini
$backupPath = $phpIniPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
try {
    Copy-Item $phpIniPath $backupPath
    Write-Host "Backup created: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Could not create backup: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Read php.ini content
try {
    $content = Get-Content $phpIniPath
    Write-Host "Reading php.ini file..." -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Could not read php.ini file: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Extensions to enable
$extensionsToEnable = @(
    "extension=pdo_mysql",
    "extension=mysqli", 
    "extension=pdo_sqlite",
    "extension=openssl"
)

$modified = $false

# Process each line
for ($i = 0; $i -lt $content.Length; $i++) {
    $line = $content[$i]
    
    foreach ($ext in $extensionsToEnable) {
        # Check if line is commented version of extension
        if ($line -match "^;\s*$([regex]::Escape($ext))") {
            Write-Host "Enabling: $ext" -ForegroundColor Yellow
            $content[$i] = $ext
            $modified = $true
            break
        }
    }
}

# Add extensions if not found
foreach ($ext in $extensionsToEnable) {
    $found = $false
    foreach ($line in $content) {
        if ($line -eq $ext -or $line -match "^;\s*$([regex]::Escape($ext))") {
            $found = $true
            break
        }
    }
    
    if (-not $found) {
        Write-Host "Adding: $ext" -ForegroundColor Yellow
        $content += $ext
        $modified = $true
    }
}

# Write changes if modified
if ($modified) {
    try {
        $content | Set-Content $phpIniPath -Encoding UTF8
        Write-Host "SUCCESS: php.ini updated successfully!" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Changes made:" -ForegroundColor Cyan
        foreach ($ext in $extensionsToEnable) {
            Write-Host "  - Enabled: $ext" -ForegroundColor White
        }
        
    } catch {
        Write-Host "ERROR: Could not write to php.ini: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You may need to run this script as Administrator" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "INFO: All required extensions are already enabled" -ForegroundColor Green
}

# Check if Apache is running and offer to restart
$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host ""
    Write-Host "Apache is currently running. You need to restart it for changes to take effect." -ForegroundColor Yellow
    $restart = Read-Host "Would you like to restart Apache now? (y/n)"
    
    if ($restart -eq "y" -or $restart -eq "Y") {
        Write-Host "Stopping Apache..." -ForegroundColor Cyan
        $apache | Stop-Process -Force
        Start-Sleep 3
        
        Write-Host "Starting Apache..." -ForegroundColor Cyan
        $apacheExe = Join-Path $xamppPath "apache\bin\httpd.exe"
        if (Test-Path $apacheExe) {
            Start-Process $apacheExe -WindowStyle Hidden
            Start-Sleep 5
            
            $newApache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
            if ($newApache) {
                Write-Host "SUCCESS: Apache restarted successfully!" -ForegroundColor Green
            } else {
                Write-Host "WARNING: Apache may not have started. Check XAMPP Control Panel." -ForegroundColor Yellow
            }
        } else {
            Write-Host "WARNING: Could not find Apache executable. Please restart manually." -ForegroundColor Yellow
        }
    }
} else {
    Write-Host ""
    Write-Host "Apache is not running. Please start it in XAMPP Control Panel." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Make sure Apache and MySQL are running in XAMPP Control Panel" -ForegroundColor White
Write-Host "2. Test PDO by visiting: http://localhost/beersty-lovable/check-pdo.php" -ForegroundColor White
Write-Host "3. Test User Management: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White

Write-Host ""
$openTest = Read-Host "Would you like to open the PDO test page now? (y/n)"
if ($openTest -eq "y" -or $openTest -eq "Y") {
    Start-Process "http://localhost/beersty-lovable/check-pdo.php"
}

Write-Host ""
Write-Host "PDO setup complete!" -ForegroundColor Green
Read-Host "Press Enter to exit"
