<?php
/**
 * Check Users and Login Credentials
 * Display existing users and create sample brewery users if needed
 */

require_once 'config/config.php';

echo "<h1>🔐 User Login Information</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Existing Users in Database:</h2>";
    
    // Get all users with their profiles
    $stmt = $conn->query("
        SELECT u.id, u.email, p.role, p.brewery_id, u.created_at,
               b.name as brewery_name
        FROM users u
        LEFT JOIN profiles p ON u.id = p.id
        LEFT JOIN breweries b ON p.brewery_id = b.id
        ORDER BY p.role, u.created_at
    ");
    
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p>❌ No users found in database</p>";
        echo "<p>Please run the database setup first:</p>";
        echo "<p><a href='setup-db-simple.php' class='btn btn-primary'>Run Database Setup</a></p>";
    } else {
        echo "<div class='users-table'>";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; border-collapse: collapse;'>";
        echo "<thead>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th>Email</th>";
        echo "<th>Role</th>";
        echo "<th>Brewery</th>";
        echo "<th>Password</th>";
        echo "<th>Created</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($users as $user) {
            $password = '';
            if ($user['email'] === '<EMAIL>') {
                $password = 'admin123';
            } elseif (strpos($user['email'], 'brewery') !== false) {
                $password = 'brewery123';
            } elseif ($user['role'] === 'brewery') {
                $password = 'brewery123';
            } else {
                $password = 'password123';
            }
            
            $roleColor = match($user['role']) {
                'admin' => '#dc3545',
                'brewery' => '#28a745',
                'beer_enthusiast' => '#007bff',
                'beer_expert' => '#6f42c1',
                default => '#6c757d'
            };
            
            echo "<tr>";
            echo "<td><strong>{$user['email']}</strong></td>";
            echo "<td><span style='color: $roleColor; font-weight: bold;'>{$user['role']}</span></td>";
            echo "<td>" . ($user['brewery_name'] ?: 'N/A') . "</td>";
            echo "<td><code style='background: #e9ecef; padding: 4px 8px; border-radius: 4px;'>$password</code></td>";
            echo "<td>" . date('M j, Y', strtotime($user['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
    // Check if we need to create brewery users
    $breweryUserCount = 0;
    foreach ($users as $user) {
        if ($user['role'] === 'brewery') {
            $breweryUserCount++;
        }
    }
    
    if ($breweryUserCount < 2) {
        echo "<h3>🏭 Creating Sample Brewery Users...</h3>";
        
        // Get some breweries to create users for
        $stmt = $conn->query("
            SELECT id, name, email 
            FROM breweries 
            WHERE claimed = 1 
            LIMIT 3
        ");
        $breweries = $stmt->fetchAll();
        
        if (!empty($breweries)) {
            foreach ($breweries as $brewery) {
                $breweryEmail = $brewery['email'] ?: strtolower(str_replace(' ', '', $brewery['name'])) . '@brewery.com';
                
                // Check if user already exists
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$breweryEmail]);
                
                if ($stmt->rowCount() == 0) {
                    try {
                        $userId = bin2hex(random_bytes(16));
                        $passwordHash = password_hash('brewery123', PASSWORD_DEFAULT);
                        
                        $conn->beginTransaction();
                        
                        // Insert user
                        $stmt = $conn->prepare("
                            INSERT INTO users (id, email, password_hash) 
                            VALUES (?, ?, ?)
                        ");
                        $stmt->execute([$userId, $breweryEmail, $passwordHash]);
                        
                        // Insert profile
                        $stmt = $conn->prepare("
                            INSERT INTO profiles (id, email, role, brewery_id) 
                            VALUES (?, ?, 'brewery', ?)
                        ");
                        $stmt->execute([$userId, $breweryEmail, $brewery['id']]);
                        
                        $conn->commit();
                        
                        echo "<p>✅ Created brewery user: <strong>$breweryEmail</strong> for <em>{$brewery['name']}</em></p>";
                        
                    } catch (Exception $e) {
                        $conn->rollback();
                        echo "<p>❌ Failed to create user for {$brewery['name']}: " . $e->getMessage() . "</p>";
                    }
                } else {
                    echo "<p>⏭️ User already exists: $breweryEmail</p>";
                }
            }
        }
    }
    
    echo "<h2>🔑 Login Credentials Summary:</h2>";
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    
    echo "<h3>👨‍💼 Admin Access:</h3>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <code><EMAIL></code></li>";
    echo "<li><strong>Password:</strong> <code>admin123</code></li>";
    echo "<li><strong>Access:</strong> Full admin dashboard, user management, system settings</li>";
    echo "</ul>";
    
    echo "<h3>🏭 Brewery Access:</h3>";
    echo "<ul>";
    
    // Get brewery users
    $stmt = $conn->query("
        SELECT u.email, b.name as brewery_name
        FROM users u
        JOIN profiles p ON u.id = p.id
        LEFT JOIN breweries b ON p.brewery_id = b.id
        WHERE p.role = 'brewery'
        ORDER BY u.email
    ");
    $breweryUsers = $stmt->fetchAll();
    
    if (!empty($breweryUsers)) {
        foreach ($breweryUsers as $breweryUser) {
            echo "<li><strong>Email:</strong> <code>{$breweryUser['email']}</code> | <strong>Password:</strong> <code>brewery123</code> | <strong>Brewery:</strong> {$breweryUser['brewery_name']}</li>";
        }
    } else {
        echo "<li>No brewery users found. Register as a brewery user or run brewery setup.</li>";
    }
    echo "</ul>";
    
    echo "<h3>🍺 Customer/Enthusiast Access:</h3>";
    echo "<ul>";
    echo "<li>Register new accounts at: <a href='auth/register.php'>Registration Page</a></li>";
    echo "<li>Default password for test accounts: <code>password123</code></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🚀 Quick Access Links:</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<ul>";
    echo "<li><a href='auth/login.php' class='btn btn-primary'>Login Page</a></li>";
    echo "<li><a href='auth/register.php' class='btn btn-success'>Register New Account</a></li>";
    echo "<li><a href='admin/dashboard.php' class='btn btn-danger'>Admin Dashboard</a> (requires admin login)</li>";
    echo "<li><a href='brewery/profile.php' class='btn btn-warning'>Brewery Dashboard</a> (requires brewery login)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please ensure the database is set up correctly.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='setup-db-simple.php'>Database Setup →</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-success { background-color: #28a745; }
.btn-danger { background-color: #dc3545; }
.btn-warning { background-color: #ffc107; color: #212529; }

table {
    font-size: 14px;
}

th {
    background-color: #f8f9fa !important;
    font-weight: bold;
    text-align: left;
}

td, th {
    padding: 12px;
    border: 1px solid #dee2e6;
}

code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 13px;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 8px;
}
</style>
