<?php
/**
 * XML Sitemap Generator for SEO
 * Beersty Platform
 */

require_once 'config/config.php';

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Base URL
$baseUrl = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'];

// Helper function to add URL to sitemap
function addUrl($loc, $lastmod = null, $changefreq = 'weekly', $priority = '0.5') {
    global $baseUrl;
    
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($baseUrl . $loc) . "</loc>\n";
    
    if ($lastmod) {
        echo "    <lastmod>" . date('Y-m-d', strtotime($lastmod)) . "</lastmod>\n";
    }
    
    echo "    <changefreq>" . $changefreq . "</changefreq>\n";
    echo "    <priority>" . $priority . "</priority>\n";
    echo "  </url>\n";
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Homepage
    addUrl('/', date('Y-m-d'), 'daily', '1.0');
    
    // Static pages
    $staticPages = [
        // Account pages
        '/account/login/' => ['changefreq' => 'monthly', 'priority' => '0.3'],
        '/account/register/' => ['changefreq' => 'monthly', 'priority' => '0.3'],
        
        // Discovery pages
        '/beers/discover/' => ['changefreq' => 'daily', 'priority' => '0.9'],
        '/beers/trending/' => ['changefreq' => 'daily', 'priority' => '0.8'],
        '/beers/styles/' => ['changefreq' => 'weekly', 'priority' => '0.7'],
        '/beers/recommendations/' => ['changefreq' => 'daily', 'priority' => '0.8'],
        
        '/breweries/discover/' => ['changefreq' => 'daily', 'priority' => '0.9'],
        '/breweries/map/' => ['changefreq' => 'weekly', 'priority' => '0.8'],
        
        // Social pages
        '/social/feed/' => ['changefreq' => 'hourly', 'priority' => '0.7'],
        '/social/checkin/' => ['changefreq' => 'daily', 'priority' => '0.6'],
        '/social/discover-users/' => ['changefreq' => 'daily', 'priority' => '0.6'],
        
        // Search pages
        '/search/' => ['changefreq' => 'weekly', 'priority' => '0.7'],
        
        // Educational content
        '/learn/beer-styles/' => ['changefreq' => 'monthly', 'priority' => '0.6'],
        '/learn/brewing-process/' => ['changefreq' => 'monthly', 'priority' => '0.6'],
        '/learn/tasting-notes/' => ['changefreq' => 'monthly', 'priority' => '0.6'],
        '/learn/food-pairing/' => ['changefreq' => 'monthly', 'priority' => '0.6'],
        '/learn/glossary/' => ['changefreq' => 'monthly', 'priority' => '0.6'],
    ];
    
    foreach ($staticPages as $url => $options) {
        addUrl(
            $url, 
            null, 
            $options['changefreq'] ?? 'weekly', 
            $options['priority'] ?? '0.5'
        );
    }
    
    // Dynamic brewery pages
    $stmt = $conn->prepare("
        SELECT id, name, updated_at, created_at 
        FROM breweries 
        WHERE status = 'active' 
        ORDER BY updated_at DESC
    ");
    $stmt->execute();
    $breweries = $stmt->fetchAll();
    
    foreach ($breweries as $brewery) {
        $slug = strtolower(str_replace([' ', '&', '.'], ['-', 'and', ''], $brewery['name']));
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        $lastmod = $brewery['updated_at'] ?: $brewery['created_at'];
        
        addUrl(
            '/breweries/' . $slug . '/',
            $lastmod,
            'weekly',
            '0.8'
        );
    }
    
    // Dynamic beer pages (if beer table exists)
    try {
        $stmt = $conn->prepare("
            SELECT id, name, updated_at, created_at 
            FROM beers 
            WHERE status = 'active' 
            ORDER BY updated_at DESC 
            LIMIT 1000
        ");
        $stmt->execute();
        $beers = $stmt->fetchAll();
        
        foreach ($beers as $beer) {
            $slug = strtolower(str_replace([' ', '&', '.'], ['-', 'and', ''], $beer['name']));
            $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
            $slug = preg_replace('/-+/', '-', $slug);
            $slug = trim($slug, '-');
            
            $lastmod = $beer['updated_at'] ?: $beer['created_at'];
            
            addUrl(
                '/beers/' . $slug . '/',
                $lastmod,
                'weekly',
                '0.7'
            );
        }
    } catch (Exception $e) {
        // Beer table might not exist yet
        error_log("Sitemap: Beer table not found - " . $e->getMessage());
    }
    
    // Public user profiles (if enabled)
    try {
        $stmt = $conn->prepare("
            SELECT p.id, p.username, p.updated_at, p.created_at 
            FROM profiles p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.public_profile = 1 
            AND p.username IS NOT NULL 
            AND p.username != ''
            ORDER BY p.updated_at DESC 
            LIMIT 500
        ");
        $stmt->execute();
        $profiles = $stmt->fetchAll();
        
        foreach ($profiles as $profile) {
            $lastmod = $profile['updated_at'] ?: $profile['created_at'];
            
            addUrl(
                '/profile/@' . $profile['username'] . '/',
                $lastmod,
                'weekly',
                '0.4'
            );
        }
    } catch (Exception $e) {
        // Profile table might not have public_profile column yet
        error_log("Sitemap: Public profiles not available - " . $e->getMessage());
    }
    
    // Location-based pages
    try {
        $stmt = $conn->prepare("
            SELECT DISTINCT city, state 
            FROM breweries 
            WHERE city IS NOT NULL 
            AND state IS NOT NULL 
            AND city != '' 
            AND state != ''
            ORDER BY state, city
        ");
        $stmt->execute();
        $locations = $stmt->fetchAll();
        
        foreach ($locations as $location) {
            $citySlug = strtolower(str_replace([' ', '&', '.'], ['-', 'and', ''], $location['city']));
            $citySlug = preg_replace('/[^a-z0-9\-]/', '', $citySlug);
            $citySlug = preg_replace('/-+/', '-', $citySlug);
            $citySlug = trim($citySlug, '-');
            
            $stateSlug = strtolower(str_replace([' ', '&', '.'], ['-', 'and', ''], $location['state']));
            $stateSlug = preg_replace('/[^a-z0-9\-]/', '', $stateSlug);
            $stateSlug = preg_replace('/-+/', '-', $stateSlug);
            $stateSlug = trim($stateSlug, '-');
            
            // City pages
            addUrl(
                '/location/city/' . $citySlug . '/',
                null,
                'weekly',
                '0.6'
            );
            
            // State pages
            addUrl(
                '/location/state/' . $stateSlug . '/',
                null,
                'weekly',
                '0.6'
            );
        }
    } catch (Exception $e) {
        error_log("Sitemap: Location pages error - " . $e->getMessage());
    }
    
    // Blog/content pages (if they exist)
    try {
        $stmt = $conn->prepare("
            SELECT slug, updated_at, created_at 
            FROM blog_posts 
            WHERE status = 'published' 
            ORDER BY updated_at DESC
        ");
        $stmt->execute();
        $posts = $stmt->fetchAll();
        
        foreach ($posts as $post) {
            $lastmod = $post['updated_at'] ?: $post['created_at'];
            
            addUrl(
                '/blog/' . $post['slug'] . '/',
                $lastmod,
                'monthly',
                '0.6'
            );
        }
    } catch (Exception $e) {
        // Blog table might not exist
        error_log("Sitemap: Blog table not found - " . $e->getMessage());
    }
    
} catch (Exception $e) {
    error_log("Sitemap generation error: " . $e->getMessage());
    
    // Still output basic sitemap even if database fails
    addUrl('/', date('Y-m-d'), 'daily', '1.0');
    addUrl('/beers/discover/', null, 'daily', '0.9');
    addUrl('/breweries/discover/', null, 'daily', '0.9');
    addUrl('/account/login/', null, 'monthly', '0.3');
    addUrl('/account/register/', null, 'monthly', '0.3');
}

// Close XML
echo '</urlset>' . "\n";
?>
