<?php
/**
 * Test JSON Database Implementation
 */

echo "<h1>🧪 Testing JSON Database</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    echo "<h2>Step 1: Testing Database Class</h2>";
    
    require_once 'config/database.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<p>✅ Database connection successful!</p>";
    
    // Check what type of database we're using
    $driver = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<p><strong>Database Type:</strong> " . strtoupper($driver) . "</p>";
    
    echo "<h2>Step 2: Testing Login Query</h2>";
    
    // Test the exact query used in login
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id
        FROM users u
        JOIN profiles p ON u.id = p.id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ Admin user found!</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</p>";
        echo "<p><strong>User ID:</strong> " . htmlspecialchars($user['id']) . "</p>";
        
        // Test password verification
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<p>✅ Password verification successful!</p>";
        } else {
            echo "<p>❌ Password verification failed!</p>";
        }
        
    } else {
        echo "<p>❌ Admin user not found!</p>";
    }
    
    echo "<h2>Step 3: Manual Login Test</h2>";
    
    // Simulate the exact login process
    $email = '<EMAIL>';
    $password = 'admin123';
    
    echo "<p>Testing login with:</p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> " . htmlspecialchars($email) . "</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    
    // Get user by email
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id
        FROM users u
        JOIN profiles p ON u.id = p.id
        WHERE u.email = ?
    ");
    $stmt->execute([$email]);
    $loginUser = $stmt->fetch();
    
    if ($loginUser && password_verify($password, $loginUser['password_hash'])) {
        echo "<p>✅ <strong>LOGIN SIMULATION SUCCESSFUL!</strong></p>";
        echo "<p>User would be redirected to admin dashboard</p>";
        
        // Test session variables that would be set
        echo "<h4>Session Variables that would be set:</h4>";
        echo "<ul>";
        echo "<li>user_id: " . htmlspecialchars($loginUser['id']) . "</li>";
        echo "<li>user_email: " . htmlspecialchars($loginUser['email']) . "</li>";
        echo "<li>user_role: " . htmlspecialchars($loginUser['role']) . "</li>";
        echo "<li>brewery_id: " . htmlspecialchars($loginUser['brewery_id'] ?? 'null') . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p>❌ <strong>LOGIN SIMULATION FAILED!</strong></p>";
        if (!$loginUser) {
            echo "<p>Reason: User not found</p>";
        } else {
            echo "<p>Reason: Password verification failed</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . htmlspecialchars($e->getLine()) . "</p>";
    echo "<p><strong>Stack Trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>Step 4: Check JSON Database File</h2>";

$dataFile = __DIR__ . '/database/data.json';
if (file_exists($dataFile)) {
    echo "<p>✅ JSON database file exists: <code>$dataFile</code></p>";
    
    $json = file_get_contents($dataFile);
    $data = json_decode($json, true);
    
    if ($data) {
        echo "<p>✅ JSON data is valid</p>";
        echo "<p><strong>Users count:</strong> " . count($data['users'] ?? []) . "</p>";
        echo "<p><strong>Profiles count:</strong> " . count($data['profiles'] ?? []) . "</p>";
        
        echo "<h4>Raw JSON Data:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT));
        echo "</pre>";
    } else {
        echo "<p>❌ JSON data is invalid</p>";
    }
} else {
    echo "<p>❌ JSON database file does not exist: <code>$dataFile</code></p>";
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
echo "<p>If the JSON database test is successful, you should be able to login with:</p>";
echo "<ul>";
echo "<li><strong>Email:</strong> <EMAIL></li>";
echo "<li><strong>Password:</strong> admin123</li>";
echo "</ul>";
echo "<p><a href='/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔐 Try Login Now</a></p>";
echo "</div>";
?>
