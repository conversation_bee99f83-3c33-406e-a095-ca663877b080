<?php
/**
 * Simple PDO Test
 */

echo "<h1>Simple PDO Test</h1>";

// Test 1: Check if PDO is loaded
echo "<h2>Test 1: PDO Extension</h2>";
if (extension_loaded('pdo')) {
    echo "<span style='color: green;'>✅ PDO is loaded</span><br>";
} else {
    echo "<span style='color: red;'>❌ PDO is NOT loaded</span><br>";
    echo "<p style='color: red;'>You need to enable PDO in php.ini</p>";
    exit;
}

// Test 2: Check PDO MySQL driver
echo "<h2>Test 2: PDO MySQL Driver</h2>";
if (extension_loaded('pdo_mysql')) {
    echo "<span style='color: green;'>✅ PDO MySQL driver is loaded</span><br>";
} else {
    echo "<span style='color: red;'>❌ PDO MySQL driver is NOT loaded</span><br>";
    echo "<p style='color: red;'>You need to enable pdo_mysql in php.ini</p>";
    exit;
}

// Test 3: Try to connect
echo "<h2>Test 3: Database Connection</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    echo "<span style='color: green;'>✅ PDO connection successful</span><br>";
    
    // Test 4: Try to use beersty_db
    echo "<h2>Test 4: Beersty Database</h2>";
    try {
        $pdo->exec("USE beersty_db");
        echo "<span style='color: green;'>✅ Connected to beersty_db</span><br>";
        
        // Test 5: Check users table
        echo "<h2>Test 5: Users Table</h2>";
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<span style='color: green;'>✅ Users table exists</span><br>";
            
            // Count users
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            echo "<p>Users in database: <strong>" . $result['count'] . "</strong></p>";
            
        } else {
            echo "<span style='color: red;'>❌ Users table does not exist</span><br>";
        }
        
    } catch (PDOException $e) {
        echo "<span style='color: red;'>❌ Database beersty_db not found</span><br>";
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>❌ PDO connection failed</span><br>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Quick Links</h2>";
echo "<a href='check-pdo.php'>Full PDO Diagnostic</a> | ";
echo "<a href='admin/user-management.php'>User Management</a> | ";
echo "<a href='phpinfo.php'>PHP Info</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
