<?php
require_once '../config/config.php';

$pageTitle = 'Search - ' . APP_NAME;
$additionalCSS = ['/assets/css/search.css'];

$query = sanitizeInput($_GET['q'] ?? '');
$type = sanitizeInput($_GET['type'] ?? 'all');

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-search me-3"></i>Search Beersty
            </h1>
            <p class="lead text-muted">
                Find beers, breweries, and fellow beer enthusiasts
            </p>
        </div>
    </div>
    
    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-card">
                <div class="card-body">
                    <form id="searchForm" class="search-form">
                        <div class="row g-3">
                            <!-- Search Input -->
                            <div class="col-md-8">
                                <div class="search-input-container">
                                    <input type="text" class="form-control search-input" 
                                           id="searchQuery" name="q" 
                                           placeholder="Search for beers, breweries, or users..."
                                           value="<?php echo htmlspecialchars($query); ?>"
                                           autocomplete="off">
                                    <div class="search-suggestions" id="searchSuggestions"></div>
                                </div>
                            </div>
                            
                            <!-- Search Type -->
                            <div class="col-md-3">
                                <select class="form-select" id="searchType" name="type">
                                    <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>All Results</option>
                                    <option value="beers" <?php echo $type === 'beers' ? 'selected' : ''; ?>>Beers</option>
                                    <option value="breweries" <?php echo $type === 'breweries' ? 'selected' : ''; ?>>Breweries</option>
                                    <?php if (isLoggedIn()): ?>
                                        <option value="users" <?php echo $type === 'users' ? 'selected' : ''; ?>>Users</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <!-- Search Button -->
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search Results -->
    <div class="row" id="searchResults">
        <?php if (!empty($query)): ?>
            <!-- Results will be loaded via JavaScript -->
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Searching...</span>
                    </div>
                    <p class="mt-3 text-muted">Searching for "<?php echo htmlspecialchars($query); ?>"...</p>
                </div>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-search fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">Start your search</h4>
                    <p class="text-muted mb-4">
                        Enter a search term to find beers, breweries, and users
                    </p>
                    
                    <!-- Popular Searches -->
                    <div class="popular-searches">
                        <h6 class="text-muted mb-3">Popular Searches:</h6>
                        <div class="d-flex flex-wrap gap-2 justify-content-center">
                            <button class="btn btn-outline-secondary btn-sm popular-search" data-query="IPA">IPA</button>
                            <button class="btn btn-outline-secondary btn-sm popular-search" data-query="Stout">Stout</button>
                            <button class="btn btn-outline-secondary btn-sm popular-search" data-query="Lager">Lager</button>
                            <button class="btn btn-outline-secondary btn-sm popular-search" data-query="Pale Ale">Pale Ale</button>
                            <button class="btn btn-outline-secondary btn-sm popular-search" data-query="Wheat">Wheat Beer</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Advanced Filters (Hidden by default) -->
    <div class="row mt-4" id="advancedFilters" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Advanced Filters
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- ABV Range -->
                        <div class="col-md-3">
                            <label class="form-label">ABV Range</label>
                            <div class="row g-1">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="abvMin" placeholder="Min" min="0" max="20" step="0.1">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="abvMax" placeholder="Max" min="0" max="20" step="0.1">
                                </div>
                            </div>
                        </div>
                        
                        <!-- IBU Range -->
                        <div class="col-md-3">
                            <label class="form-label">IBU Range</label>
                            <div class="row g-1">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="ibuMin" placeholder="Min" min="0" max="120">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="ibuMax" placeholder="Max" min="0" max="120">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Rating Filter -->
                        <div class="col-md-3">
                            <label class="form-label">Minimum Rating</label>
                            <select class="form-select form-select-sm" id="minRating">
                                <option value="">Any Rating</option>
                                <option value="4.5">4.5+ Stars</option>
                                <option value="4.0">4.0+ Stars</option>
                                <option value="3.5">3.5+ Stars</option>
                                <option value="3.0">3.0+ Stars</option>
                            </select>
                        </div>
                        
                        <!-- Location Filter -->
                        <div class="col-md-3">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control form-control-sm" 
                                   id="locationFilter" placeholder="City, State">
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary btn-sm" id="applyFilters">
                                <i class="fas fa-filter me-1"></i>Apply Filters
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2" id="clearFilters">
                                <i class="fas fa-times me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let searchTimeout;
let currentQuery = '';

document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchQuery = document.getElementById('searchQuery');
    const searchType = document.getElementById('searchType');
    const searchResults = document.getElementById('searchResults');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    // Initial search if query exists
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value.trim(), searchType.value);
    }
    
    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const query = searchQuery.value.trim();
        if (query.length >= 2) {
            performSearch(query, searchType.value);
            updateURL(query, searchType.value);
        }
    });
    
    // Real-time search suggestions
    searchQuery.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                showSuggestions(query);
            }, 300);
        } else {
            hideSuggestions();
        }
    });
    
    // Search type change
    searchType.addEventListener('change', function() {
        const query = searchQuery.value.trim();
        if (query.length >= 2) {
            performSearch(query, this.value);
            updateURL(query, this.value);
        }
    });
    
    // Popular search buttons
    document.querySelectorAll('.popular-search').forEach(button => {
        button.addEventListener('click', function() {
            const query = this.dataset.query;
            searchQuery.value = query;
            performSearch(query, searchType.value);
            updateURL(query, searchType.value);
        });
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-input-container')) {
            hideSuggestions();
        }
    });
    
    // Advanced filters toggle
    const toggleFilters = document.createElement('button');
    toggleFilters.className = 'btn btn-outline-secondary btn-sm ms-2';
    toggleFilters.innerHTML = '<i class="fas fa-sliders-h me-1"></i>Advanced Filters';
    toggleFilters.type = 'button';
    
    const searchCard = document.querySelector('.search-card .card-body');
    searchCard.appendChild(toggleFilters);
    
    toggleFilters.addEventListener('click', function() {
        const filters = document.getElementById('advancedFilters');
        if (filters.style.display === 'none') {
            filters.style.display = 'block';
            this.innerHTML = '<i class="fas fa-sliders-h me-1"></i>Hide Filters';
        } else {
            filters.style.display = 'none';
            this.innerHTML = '<i class="fas fa-sliders-h me-1"></i>Advanced Filters';
        }
    });
});

async function performSearch(query, type = 'all') {
    if (query === currentQuery) return;
    currentQuery = query;
    
    const searchResults = document.getElementById('searchResults');
    
    // Show loading
    searchResults.innerHTML = `
        <div class="col-12">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Searching...</span>
                </div>
                <p class="mt-3 text-muted">Searching for "${query}"...</p>
            </div>
        </div>
    `;
    
    try {
        const response = await fetch(`/api/global-search.php?q=${encodeURIComponent(query)}&type=${type}&limit=20`);
        const data = await response.json();
        
        displayResults(data, query);
        
    } catch (error) {
        console.error('Search error:', error);
        searchResults.innerHTML = `
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Search failed. Please try again.
                </div>
            </div>
        `;
    }
}

function displayResults(data, query) {
    const searchResults = document.getElementById('searchResults');
    
    if (data.total === 0) {
        searchResults.innerHTML = `
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No results found</h5>
                    <p class="text-muted">
                        No results found for "${query}". Try different keywords or check your spelling.
                    </p>
                </div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="col-12 mb-3">
            <h5>Found ${data.total} result${data.total !== 1 ? 's' : ''} for "${query}"</h5>
        </div>
    `;
    
    // Display beers
    if (data.beers.length > 0) {
        html += `
            <div class="col-12 mb-4">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-beer me-2"></i>Beers (${data.beers.length})
                </h6>
                <div class="row g-3">
        `;
        
        data.beers.forEach(beer => {
            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="card search-result-card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="result-image me-3">
                                    ${beer.thumbnail ? 
                                        `<img src="${beer.thumbnail}" alt="${beer.name}" class="beer-thumb">` :
                                        `<div class="beer-thumb-placeholder"><i class="fas fa-beer"></i></div>`
                                    }
                                </div>
                                <div class="result-info flex-grow-1">
                                    <h6 class="result-title">
                                        <a href="${beer.url}" class="text-decoration-none">${beer.display_name}</a>
                                    </h6>
                                    <p class="result-subtitle text-muted mb-2">${beer.subtitle}</p>
                                    ${beer.average_rating ? 
                                        `<div class="rating">
                                            ${generateStars(beer.average_rating)}
                                            <span class="ms-1">${parseFloat(beer.average_rating).toFixed(1)}</span>
                                        </div>` : ''
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    // Display breweries
    if (data.breweries.length > 0) {
        html += `
            <div class="col-12 mb-4">
                <h6 class="text-success mb-3">
                    <i class="fas fa-industry me-2"></i>Breweries (${data.breweries.length})
                </h6>
                <div class="row g-3">
        `;
        
        data.breweries.forEach(brewery => {
            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="card search-result-card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="result-image me-3">
                                    ${brewery.logo ? 
                                        `<img src="${brewery.logo}" alt="${brewery.name}" class="brewery-logo">` :
                                        `<div class="brewery-logo-placeholder"><i class="fas fa-industry"></i></div>`
                                    }
                                </div>
                                <div class="result-info flex-grow-1">
                                    <h6 class="result-title">
                                        <a href="${brewery.url}" class="text-decoration-none">${brewery.display_name}</a>
                                    </h6>
                                    <p class="result-subtitle text-muted">${brewery.subtitle}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    // Display users
    if (data.users.length > 0) {
        html += `
            <div class="col-12 mb-4">
                <h6 class="text-info mb-3">
                    <i class="fas fa-users me-2"></i>Users (${data.users.length})
                </h6>
                <div class="row g-3">
        `;
        
        data.users.forEach(user => {
            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="card search-result-card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="result-image me-3">
                                    ${user.avatar ? 
                                        `<img src="${user.avatar}" alt="${user.display_name}" class="user-avatar">` :
                                        `<div class="user-avatar-placeholder"><i class="fas fa-user"></i></div>`
                                    }
                                </div>
                                <div class="result-info flex-grow-1">
                                    <h6 class="result-title">
                                        <a href="${user.url}" class="text-decoration-none">${user.display_name}</a>
                                        <span class="badge bg-secondary ms-2">${user.role_label}</span>
                                    </h6>
                                    <p class="result-subtitle text-muted">${user.subtitle}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    searchResults.innerHTML = html;
}

async function showSuggestions(query) {
    try {
        const response = await fetch(`/api/global-search.php?q=${encodeURIComponent(query)}&limit=5`);
        const data = await response.json();
        
        const suggestions = document.getElementById('searchSuggestions');
        
        if (data.total === 0) {
            hideSuggestions();
            return;
        }
        
        let html = '';
        
        // Add top results from each category
        [...data.beers.slice(0, 3), ...data.breweries.slice(0, 2)].forEach(item => {
            html += `
                <div class="suggestion-item" data-query="${item.display_name}">
                    <i class="fas fa-${item.result_type === 'beer' ? 'beer' : 'industry'} me-2"></i>
                    ${item.display_name}
                    <small class="text-muted ms-2">${item.subtitle}</small>
                </div>
            `;
        });
        
        suggestions.innerHTML = html;
        suggestions.style.display = 'block';
        
        // Add click handlers
        suggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const query = this.dataset.query;
                document.getElementById('searchQuery').value = query;
                performSearch(query, document.getElementById('searchType').value);
                hideSuggestions();
            });
        });
        
    } catch (error) {
        console.error('Suggestions error:', error);
        hideSuggestions();
    }
}

function hideSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    suggestions.style.display = 'none';
    suggestions.innerHTML = '';
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        stars += `<i class="fas fa-star ${i <= Math.round(rating) ? 'text-warning' : 'text-muted'}"></i>`;
    }
    return stars;
}

function updateURL(query, type) {
    const url = new URL(window.location);
    url.searchParams.set('q', query);
    url.searchParams.set('type', type);
    window.history.pushState({}, '', url);
}
</script>

<?php include '../includes/footer.php'; ?>
