<?php
/**
 * Update Shared Database Configuration to Handle COUNT Queries
 */

$homeDir = $_SERVER['HOME'] ?? '/home/' . get_current_user();
$sharedConfigPath = $homeDir . '/.beersty-shared/config/user_shared_database.php';

$updatedConfig = '<?php
/**
 * User-Space Shared Database Configuration (Updated)
 */

class UserSharedDatabase {
    private $dataFile;
    private $lockFile;
    private $data;
    
    public function __construct() {
        $homeDir = $_SERVER["HOME"] ?? "/home/" . get_current_user();
        $this->dataFile = $homeDir . "/.beersty-shared/database/beersty_shared.json";
        $this->lockFile = $homeDir . "/.beersty-shared/database/beersty_shared.lock";
        
        if (!file_exists($this->dataFile)) {
            throw new Exception("Shared database file not found: " . $this->dataFile);
        }
        
        $this->loadData();
    }
    
    private function loadData() {
        // Use file locking for concurrent access
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_SH)) {
            $json = file_get_contents($this->dataFile);
            $this->data = json_decode($json, true) ?: [];
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    private function saveData() {
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_EX)) {
            $this->data["metadata"]["last_modified"] = date("Y-m-d H:i:s");
            $this->data["metadata"]["modified_by"] = get_current_user();
            
            file_put_contents($this->dataFile, json_encode($this->data, JSON_PRETTY_PRINT));
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    public function prepare($sql) {
        return new UserSharedStatement($this, $sql);
    }
    
    public function setAttribute($attribute, $value) {
        return true;
    }
    
    public function getAttribute($attribute) {
        if ($attribute === PDO::ATTR_DRIVER_NAME) {
            return "user_shared_json";
        }
        return null;
    }
    
    public function executeQuery($sql, $params = []) {
        $this->loadData(); // Reload for latest data
        
        // Simple query parser
        $sql = trim($sql);
        
        if (preg_match("/^SELECT\s+(.+?)\s+FROM\s+(\w+)(?:\s+(\w+))?\s*(?:JOIN\s+(\w+)\s+(\w+)\s+ON\s+(.+?))?\s*(?:WHERE\s+(.+?))?(?:\s+ORDER\s+BY\s+(.+?))?(?:\s+LIMIT\s+(\d+))?$/i", $sql, $matches)) {
            return $this->handleSelect($matches, $params);
        } elseif (preg_match("/^INSERT\s+INTO\s+(\w+)\s*\((.+?)\)\s*VALUES\s*\((.+?)\)$/i", $sql, $matches)) {
            return $this->handleInsert($matches, $params);
        } elseif (preg_match("/^UPDATE\s+(\w+)\s+SET\s+(.+?)(?:\s+WHERE\s+(.+?))?$/i", $sql, $matches)) {
            return $this->handleUpdate($matches, $params);
        }
        
        return [];
    }
    
    private function handleSelect($matches, $params) {
        $selectClause = $matches[1];
        $table = $matches[2];
        $whereClause = $matches[7] ?? "";
        $orderClause = $matches[8] ?? "";
        $limitClause = $matches[9] ?? "";
        
        $results = [];
        
        if (!isset($this->data[$table])) {
            // For COUNT queries, return 0 if table doesn\'t exist
            if (stripos($selectClause, "COUNT(") !== false) {
                return [["count" => 0]];
            }
            return [];
        }
        
        // Handle COUNT queries
        if (stripos($selectClause, "COUNT(") !== false) {
            $count = 0;
            foreach ($this->data[$table] as $row) {
                // Handle WHERE clause for COUNT
                if ($whereClause) {
                    if (strpos($whereClause, "email = ?") !== false) {
                        if (!isset($row["email"]) || $row["email"] !== $params[0]) {
                            continue;
                        }
                    } elseif (strpos($whereClause, "status = \'active\'") !== false) {
                        if (!isset($row["status"]) || $row["status"] !== "active") {
                            continue;
                        }
                    }
                    // Add more WHERE conditions as needed
                }
                $count++;
            }
            return [["count" => $count]];
        }
        
        foreach ($this->data[$table] as $row) {
            $result = $row;
            
            // Handle JOIN (simplified)
            if (isset($matches[4]) && $matches[4]) {
                $joinTable = $matches[4];
                if (isset($this->data[$joinTable])) {
                    foreach ($this->data[$joinTable] as $joinRow) {
                        if (isset($row["id"]) && isset($joinRow["id"]) && $row["id"] === $joinRow["id"]) {
                            $result = array_merge($result, $joinRow);
                            break;
                        }
                    }
                }
            }
            
            // Handle WHERE clause
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($result["email"]) || $result["email"] !== $params[0]) {
                    continue;
                }
            }
            
            $results[] = $result;
        }
        
        // Handle LIMIT
        if ($limitClause && is_numeric($limitClause)) {
            $results = array_slice($results, 0, (int)$limitClause);
        }
        
        return $results;
    }
    
    private function handleInsert($matches, $params) {
        $table = $matches[1];
        $fields = array_map("trim", explode(",", $matches[2]));
        
        if (!isset($this->data[$table])) {
            $this->data[$table] = [];
        }
        
        $row = [];
        for ($i = 0; $i < count($fields); $i++) {
            $field = trim($fields[$i]);
            $row[$field] = $params[$i] ?? null;
        }
        
        $this->data[$table][] = $row;
        $this->saveData();
        
        return true;
    }
    
    private function handleUpdate($matches, $params) {
        $table = $matches[1];
        $whereClause = $matches[3] ?? "";
        
        if (!isset($this->data[$table])) {
            return false;
        }
        
        foreach ($this->data[$table] as &$row) {
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($row["email"]) || $row["email"] !== $params[0]) {
                    continue;
                }
            }
            
            // Simple SET parsing
            if (strpos($matches[2], "last_login") !== false) {
                $row["last_login"] = date("Y-m-d H:i:s");
            }
        }
        
        $this->saveData();
        return true;
    }
}

class UserSharedStatement {
    private $db;
    private $sql;
    private $results;
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        $this->results = $this->db->executeQuery($this->sql, $params);
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}
?>';

// Write the updated configuration
if (file_put_contents($sharedConfigPath, $updatedConfig)) {
    echo "<h1>✅ Shared Database Configuration Updated!</h1>";
    echo "<p>The shared database configuration has been updated to properly handle COUNT queries.</p>";
    echo "<p><strong>Updated file:</strong> <code>$sharedConfigPath</code></p>";
    echo "<p><a href='/admin/dashboard.php'>🎯 Test Admin Dashboard</a></p>";
} else {
    echo "<h1>❌ Update Failed</h1>";
    echo "<p>Failed to update the shared database configuration.</p>";
}
?>
