# Beersty - XAMPP Setup Guide

Complete guide to set up the Beersty Brewery Management System using XAMPP on Windows.

## 🚀 Quick Start

### Step 1: Install XAMPP

1. **Download XAMPP** from [https://www.apachefriends.org/download.html](https://www.apachefriends.org/download.html)
2. **Install XAMPP** to `C:\xampp` (default location)
3. **Start XAMPP Control Panel** as Administrator

### Step 2: Start Services

In XAMPP Control Panel, start:
- ✅ **Apache** (Web Server)
- ✅ **MySQL** (Database)

### Step 3: Copy Project Files

1. **Copy this entire project** to `C:\xampp\htdocs\beersty\`
2. Your folder structure should look like:
   ```
   C:\xampp\htdocs\beersty\
   ├── index.php
   ├── config/
   ├── auth/
   ├── admin/
   ├── brewery/
   ├── breweries/
   ├── assets/
   └── ...
   ```

### Step 4: Set Up Database

Open **PowerShell as Administrator** and run:

```powershell
cd C:\xampp\htdocs\beersty
.\setup-database.ps1
```

### Step 5: Access Application

Open your browser and go to:
- **Application**: [http://localhost/beersty/](http://localhost/beersty/)
- **Admin Login**: 
  - Email: `<EMAIL>`
  - Password: `admin123`

## 📋 Detailed Setup Instructions

### Prerequisites

- **Windows 10/11**
- **Administrator privileges**
- **PowerShell** (included with Windows)

### XAMPP Installation

1. **Download XAMPP 8.2.x** (includes PHP 8.2, MySQL 8.0, Apache)
2. **Run installer** as Administrator
3. **Select components**:
   - ✅ Apache
   - ✅ MySQL
   - ✅ PHP
   - ✅ phpMyAdmin
   - ❌ Mercury (not needed)
   - ❌ Tomcat (not needed)

4. **Install to** `C:\xampp`
5. **Allow firewall** access when prompted

### XAMPP Configuration

#### Start Services
1. Open **XAMPP Control Panel** as Administrator
2. Click **Start** for Apache
3. Click **Start** for MySQL
4. Services should show **green** status

#### Verify Installation
- Visit [http://localhost/](http://localhost/) - should show XAMPP dashboard
- Visit [http://localhost/phpmyadmin/](http://localhost/phpmyadmin/) - should show phpMyAdmin

### Project Setup

#### 1. Copy Files
```powershell
# Copy project to XAMPP htdocs
Copy-Item -Path "C:\path\to\beersty-project\*" -Destination "C:\xampp\htdocs\beersty\" -Recurse
```

#### 2. Set Permissions
```powershell
# Set permissions for uploads directory
icacls "C:\xampp\htdocs\beersty\uploads" /grant "Everyone:(OI)(CI)F"
```

#### 3. Run Database Setup
```powershell
cd C:\xampp\htdocs\beersty
.\setup-database.ps1
```

The script will:
- ✅ Check XAMPP installation
- ✅ Verify MySQL service
- ✅ Create database and tables
- ✅ Insert default admin user
- ✅ Create configuration files

## 🔧 Configuration

### Database Configuration

The setup script creates `config/.env` with your database settings:

```env
DB_HOST=localhost
DB_NAME=beersty_db
DB_USER=root
DB_PASSWORD=

APP_NAME="Beersty - Brewery Management System"
APP_URL=http://localhost/beersty
```

### PHP Configuration

XAMPP's PHP is configured via `C:\xampp\php\php.ini`. Key settings:

```ini
upload_max_filesize = 5M
post_max_size = 10M
max_execution_time = 30
memory_limit = 128M
```

### Apache Configuration

The `.htaccess` file handles:
- URL rewriting
- Security headers
- File protection
- Caching rules

## 🗄️ Database Management

### phpMyAdmin Access
- **URL**: [http://localhost/phpmyadmin/](http://localhost/phpmyadmin/)
- **Username**: `root`
- **Password**: (empty by default)

### Database Structure
- **Database**: `beersty_db`
- **Tables**: 11 tables for complete brewery management
- **Default Admin**: `<EMAIL>` / `admin123`

### Backup Database
```powershell
C:\xampp\mysql\bin\mysqldump.exe -u root beersty_db > backup.sql
```

### Restore Database
```powershell
C:\xampp\mysql\bin\mysql.exe -u root beersty_db < backup.sql
```

## 🌐 Accessing the Application

### URLs
- **Homepage**: [http://localhost/beersty/](http://localhost/beersty/)
- **Admin Dashboard**: [http://localhost/beersty/admin/dashboard.php](http://localhost/beersty/admin/dashboard.php)
- **Login**: [http://localhost/beersty/auth/login.php](http://localhost/beersty/auth/login.php)
- **Brewery Listing**: [http://localhost/beersty/breweries/listing.php](http://localhost/beersty/breweries/listing.php)

### Default Accounts
- **Admin**: `<EMAIL>` / `admin123`
- **Demo Brewery**: Create via registration

## 🛠️ Development

### File Structure
```
C:\xampp\htdocs\beersty\
├── index.php              # Homepage
├── .htaccess              # Apache configuration
├── config/                # Configuration files
│   ├── database.php       # Database connection
│   ├── config.php         # App configuration
│   └── .env               # Environment variables
├── auth/                  # Authentication pages
├── admin/                 # Admin pages
├── brewery/               # Brewery management
├── breweries/             # Public brewery pages
├── assets/                # CSS, JS, images
├── uploads/               # File uploads
└── database/              # SQL schema
```

### Making Changes
1. **Edit PHP files** directly in `C:\xampp\htdocs\beersty\`
2. **Refresh browser** to see changes
3. **Check logs** in XAMPP Control Panel if errors occur

### Adding New Features
1. Create new PHP files in appropriate directories
2. Update navigation in `includes/header.php`
3. Add CSS/JS in `assets/` directory
4. Test functionality

## 🔍 Troubleshooting

### Common Issues

#### Apache Won't Start
- **Port 80 in use**: Change Apache port in XAMPP config
- **Skype conflict**: Disable Skype's port 80 usage
- **IIS conflict**: Disable IIS if installed

#### MySQL Won't Start
- **Port 3306 in use**: Change MySQL port in XAMPP config
- **Service conflict**: Stop other MySQL services

#### Application Errors
- **Check Apache error logs**: `C:\xampp\apache\logs\error.log`
- **Check PHP errors**: Enable error reporting in `config/config.php`
- **Database connection**: Verify MySQL is running

#### Permission Issues
```powershell
# Fix upload permissions
icacls "C:\xampp\htdocs\beersty\uploads" /grant "Everyone:(OI)(CI)F"

# Fix config permissions
icacls "C:\xampp\htdocs\beersty\config" /grant "Everyone:(OI)(CI)R"
```

### Getting Help

1. **Check XAMPP logs** in Control Panel
2. **Verify services** are running (green status)
3. **Test database** connection via phpMyAdmin
4. **Check file permissions** for uploads directory

## 🔒 Security Notes

### Development vs Production

This setup is for **development only**. For production:

1. **Change default passwords**
2. **Disable error reporting**
3. **Use HTTPS**
4. **Restrict file permissions**
5. **Update security headers**

### Default Security
- Input sanitization enabled
- SQL injection protection
- XSS protection headers
- File upload restrictions
- Directory access protection

## 📚 Next Steps

1. **Explore the application** with the admin account
2. **Add test breweries** and menu items
3. **Customize styling** in `assets/css/`
4. **Add new features** as needed
5. **Set up backups** for your data

---

**Happy brewing with XAMPP! 🍺**
