<?php
// Add Beer Styles for Menu Management
require_once 'config/config.php';

echo "=== Adding Beer Styles ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Check if beer styles already exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM beer_styles");
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "Found $count existing beer styles." . PHP_EOL;
    } else {
        echo "No beer styles found. Adding sample beer styles..." . PHP_EOL;
        
        // Sample beer styles
        $styles = [
            ['name' => 'American IPA', 'category' => 'IPA', 'description' => 'Hoppy American-style India Pale Ale'],
            ['name' => 'New England IPA', 'category' => 'IPA', 'description' => 'Hazy, juicy IPA with tropical hop flavors'],
            ['name' => 'Imperial Stout', 'category' => 'Stout', 'description' => 'Strong, dark beer with rich chocolate and coffee notes'],
            ['name' => 'Milk Stout', 'category' => 'Stout', 'description' => 'Sweet stout brewed with lactose'],
            ['name' => 'American Wheat', 'category' => 'Wheat', 'description' => 'Light, refreshing wheat beer'],
            ['name' => 'Hefeweizen', 'category' => 'Wheat', 'description' => 'Traditional German wheat beer'],
            ['name' => 'American Lager', 'category' => 'Lager', 'description' => 'Clean, crisp American-style lager'],
            ['name' => 'Pilsner', 'category' => 'Lager', 'description' => 'Light, hoppy Czech-style lager'],
            ['name' => 'American Pale Ale', 'category' => 'Pale Ale', 'description' => 'Balanced pale ale with American hops'],
            ['name' => 'Session IPA', 'category' => 'IPA', 'description' => 'Lower alcohol IPA with full hop flavor'],
            ['name' => 'Porter', 'category' => 'Porter', 'description' => 'Dark beer with roasted malt character'],
            ['name' => 'Brown Ale', 'category' => 'Ale', 'description' => 'Malty brown ale with caramel notes'],
            ['name' => 'Saison', 'category' => 'Farmhouse', 'description' => 'Belgian farmhouse ale with spicy yeast character'],
            ['name' => 'Sour Ale', 'category' => 'Sour', 'description' => 'Tart, acidic beer with fruit flavors'],
            ['name' => 'Amber Ale', 'category' => 'Ale', 'description' => 'Malty amber-colored ale']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO beer_styles (id, name, category, description, is_active, created_at) 
            VALUES (UUID(), ?, ?, ?, 1, NOW())
        ");
        
        foreach ($styles as $style) {
            $stmt->execute([
                $style['name'],
                $style['category'],
                $style['description']
            ]);
            echo "✓ Added: " . $style['name'] . " (" . $style['category'] . ")" . PHP_EOL;
        }
    }
    
    // Show current beer styles
    echo "\n--- Current Beer Styles ---" . PHP_EOL;
    $stmt = $pdo->query("SELECT name, category FROM beer_styles WHERE is_active = 1 ORDER BY category, name");
    $styles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $currentCategory = '';
    foreach ($styles as $style) {
        if ($style['category'] !== $currentCategory) {
            $currentCategory = $style['category'];
            echo "\n" . $currentCategory . ":" . PHP_EOL;
        }
        echo "  • " . $style['name'] . PHP_EOL;
    }
    
    echo "\n=== Beer Styles Ready ===" . PHP_EOL;
    echo "Total styles: " . count($styles) . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
