<?php
/**
 * Fix COUNT query column alias in shared database
 */

$sharedConfigPath = '/home/<USER>/.beersty-shared/config/user_shared_database.php';

// Read the current file
$content = file_get_contents($sharedConfigPath);

// Find and replace the COUNT query handling
$oldCountCode = 'return [["count" => $count]];';
$newCountCode = '// Check for column alias in COUNT query (e.g., "COUNT(*) as total")
            $columnName = "count"; // default
            if (preg_match(\'/COUNT\\([^)]*\\)\\s+as\\s+(\\w+)/i\', $selectClause, $aliasMatches)) {
                $columnName = $aliasMatches[1];
            }
            
            return [[$columnName => $count]];';

// Replace the old code with the new code
$updatedContent = str_replace($oldCountCode, $newCountCode, $content);

// Also fix the case where table doesn't exist
$oldEmptyTableCode = 'return [["count" => 0]];';
$newEmptyTableCode = '// Check for column alias in COUNT query (e.g., "COUNT(*) as total")
                $columnName = "count"; // default
                if (preg_match(\'/COUNT\\([^)]*\\)\\s+as\\s+(\\w+)/i\', $selectClause, $aliasMatches)) {
                    $columnName = $aliasMatches[1];
                }
                return [[$columnName => 0]];';

$updatedContent = str_replace($oldEmptyTableCode, $newEmptyTableCode, $updatedContent);

// Write the updated content back to the file
if (file_put_contents($sharedConfigPath, $updatedContent)) {
    echo "<h1>✅ COUNT Query Alias Fixed!</h1>";
    echo "<p>The shared database now properly handles column aliases in COUNT queries.</p>";
    echo "<p><strong>Updated file:</strong> <code>$sharedConfigPath</code></p>";
    echo "<p><strong>Fixed:</strong> COUNT(*) as total now returns 'total' column instead of 'count'</p>";
    echo "<p><a href='/'>🎯 Test Home Page</a></p>";
} else {
    echo "<h1>❌ Update Failed</h1>";
    echo "<p>Failed to update the shared database configuration.</p>";
}
?>
