<?php
/**
 * PHP Built-in Server Router
 * Handles URL routing for development server since .htaccess doesn't work
 */

$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove leading and trailing slashes for consistent matching
$path = trim($path, '/');

// If it's a real file, serve it normally
if ($path && file_exists($path)) {
    return false;
}

// Handle places with slugs (e.g., /places/craft-masters-brewery)
if (preg_match('#^places/([a-zA-Z0-9\-]+)/?$#', $path, $matches)) {
    $slug = $matches[1];

    // Exclude known paths
    if (!in_array($slug, ['search', 'profile'])) {
        $_GET['slug'] = $slug;
        require_once 'places/profile/index.php';
        return true;
    }
}

// Handle other routes
$routes = [
    'places' => 'places/search.php',
    'places/search' => 'places/search.php',
    'account/login' => 'auth/login.php',
    'account/register' => 'auth/register.php',
    'account/logout' => 'auth/logout.php',
    'test-rewrite' => 'test-rewrite.php'
];

// Check for exact matches
if (isset($routes[$path])) {
    $file = $routes[$path];
    if (file_exists($file)) {
        // Handle auth files with proper path context
        if (strpos($file, 'auth/') === 0) {
            $originalDir = getcwd();
            chdir('auth');
            require_once basename($file);
            chdir($originalDir);
        } else {
            require_once $file;
        }
        return true;
    } else {
        error_log("Router: File not found: $file");
        return false;
    }
}

// Handle legacy places URLs with IDs
if (preg_match('#^places/profile/([0-9]+)/?$#', $path, $matches)) {
    $_GET['id'] = $matches[1];
    require_once 'places/profile/index.php';
    return true;
}

if (preg_match('#^places/profile/?$#', $path)) {
    require_once 'places/profile/index.php';
    return true;
}

// If no route matches, serve the file normally or return false
return false;
?>
