<?php
/**
 * Fix Database Connection NOW
 * Comprehensive database connection fix with multiple attempts
 */

echo "<h1>🔧 Fix Database Connection NOW</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>📋 System Check</h2>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>PDO Available:</strong> " . (extension_loaded('pdo') ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>PDO SQLite:</strong> " . (extension_loaded('pdo_sqlite') ? '✅ Yes' : '❌ No') . "</li>";
echo "</ul>";

if (!extension_loaded('pdo_mysql') && !extension_loaded('pdo_sqlite')) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
    echo "<h3>❌ No Database Extensions Available</h3>";
    echo "<p>Neither PDO MySQL nor PDO SQLite is available. Please install PHP database extensions.</p>";
    echo "</div>";
    exit;
}

echo "<h2>🐬 Testing MySQL Configurations</h2>";

$mysqlConfigs = [
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => '', 'name' => 'Default XAMPP/WAMP'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => '', 'name' => 'Localhost IP'],
    ['host' => 'localhost', 'port' => 3307, 'user' => 'root', 'pass' => '', 'name' => 'Alternative Port'],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => 'root', 'name' => 'Root Password'],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'beersty', 'pass' => 'beersty', 'name' => 'Custom User'],
];

$workingConfig = null;

foreach ($mysqlConfigs as $config) {
    echo "<h3>Testing: {$config['name']}</h3>";
    echo "<p>Host: {$config['host']}:{$config['port']}, User: {$config['user']}</p>";
    
    try {
        // Test connection without database first
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $testConn = new PDO($dsn, $config['user'], $config['pass']);
        $testConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p style='color: green;'>✅ MySQL server connection successful!</p>";
        
        // Test if we can create database
        try {
            $testConn->exec("CREATE DATABASE IF NOT EXISTS beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p style='color: green;'>✅ Database creation successful!</p>";
            
            // Test connection to the database
            $dbDsn = "mysql:host={$config['host']};port={$config['port']};dbname=beersty_db;charset=utf8mb4";
            $dbConn = new PDO($dbDsn, $config['user'], $config['pass']);
            $dbConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<p style='color: green;'>✅ Database connection successful!</p>";
            
            $workingConfig = $config;
            break;
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Database creation/connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<hr>";
}

if ($workingConfig) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Found Working MySQL Configuration!</h3>";
    echo "<ul>";
    echo "<li><strong>Configuration:</strong> {$workingConfig['name']}</li>";
    echo "<li><strong>Host:</strong> {$workingConfig['host']}:{$workingConfig['port']}</li>";
    echo "<li><strong>Username:</strong> {$workingConfig['user']}</li>";
    echo "<li><strong>Password:</strong> " . (empty($workingConfig['pass']) ? 'Empty' : '[Set]') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🗄️ Setting Up Database</h2>";
    
    try {
        // Connect to database
        $dsn = "mysql:host={$workingConfig['host']};port={$workingConfig['port']};dbname=beersty_db;charset=utf8mb4";
        $conn = new PDO($dsn, $workingConfig['user'], $workingConfig['pass']);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ Connected to beersty_db database</p>";
        
        // Create tables
        echo "<h3>Creating Tables...</h3>";
        
        // Users table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(32) PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL
            )
        ");
        echo "<p>✅ Users table created</p>";
        
        // Profiles table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS profiles (
                id VARCHAR(32) PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                role VARCHAR(50) DEFAULT 'customer',
                brewery_id VARCHAR(32) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        echo "<p>✅ Profiles table created</p>";
        
        // Breweries table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS breweries (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                address TEXT,
                city VARCHAR(100),
                state VARCHAR(50),
                zip VARCHAR(20),
                phone VARCHAR(50),
                email VARCHAR(255),
                website VARCHAR(255),
                description TEXT,
                brewery_type VARCHAR(50) DEFAULT 'micro',
                verified BOOLEAN DEFAULT 0,
                claimed BOOLEAN DEFAULT 0,
                claimable BOOLEAN DEFAULT 1,
                follower_count INT DEFAULT 0,
                like_count INT DEFAULT 0,
                external_id VARCHAR(100),
                latitude DECIMAL(10, 8),
                longitude DECIMAL(11, 8),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "<p>✅ Breweries table created</p>";
        
        // Create/update admin user
        echo "<h3>Setting Up Admin User...</h3>";
        
        $adminEmail = '<EMAIL>';
        $adminPassword = 'admin123';
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
        
        // Check if admin exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$adminEmail]);
        $existingUser = $stmt->fetch();
        
        if ($existingUser) {
            // Update existing admin
            $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $stmt->execute([$passwordHash, $adminEmail]);
            
            $stmt = $conn->prepare("UPDATE profiles SET role = 'admin' WHERE email = ?");
            $stmt->execute([$adminEmail]);
            
            echo "<p>✅ Admin user updated</p>";
        } else {
            // Create new admin
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $adminEmail, $passwordHash]);
            
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
            $stmt->execute([$userId, $adminEmail]);
            
            echo "<p>✅ Admin user created</p>";
        }
        
        // Update database configuration file
        echo "<h3>Updating Database Configuration...</h3>";
        
        $configContent = "<?php
class Database {
    private \$host = '{$workingConfig['host']}';
    private \$db_name = 'beersty_db';
    private \$username = '{$workingConfig['user']}';
    private \$password = '{$workingConfig['pass']}';
    private \$port = {$workingConfig['port']};
    private \$conn;

    public function __construct() {
        // Load configuration from environment or config file
        if (file_exists(__DIR__ . '/.env')) {
            \$env = parse_ini_file(__DIR__ . '/.env');
            \$this->host = \$env['DB_HOST'] ?? \$this->host;
            \$this->db_name = \$env['DB_NAME'] ?? \$this->db_name;
            \$this->username = \$env['DB_USER'] ?? \$this->username;
            \$this->password = \$env['DB_PASSWORD'] ?? \$this->password;
            \$this->port = \$env['DB_PORT'] ?? \$this->port;
        }
    }

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";port=\" . \$this->port . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password);
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"Database connection failed: \" . \$exception->getMessage());
        }
        
        return \$this->conn;
    }

    public function testConnection() {
        try {
            \$conn = \$this->getConnection();
            return \$conn !== null;
        } catch (Exception \$e) {
            return false;
        }
    }
}
?>";
        
        file_put_contents('config/database.php', $configContent);
        echo "<p>✅ Database configuration file updated</p>";
        
        // Test the new configuration
        echo "<h3>Testing New Configuration...</h3>";
        require_once 'config/database.php';
        $testDb = new Database();
        $testConn = $testDb->getConnection();
        
        if ($testConn) {
            echo "<p style='color: green;'>✅ New configuration works perfectly!</p>";
            
            // Test login functions
            echo "<h3>Testing Login Functions...</h3>";
            require_once 'config/config.php';
            
            $stmt = $testConn->prepare("
                SELECT u.id, u.email, u.password_hash, p.role 
                FROM users u 
                JOIN profiles p ON u.id = p.id 
                WHERE u.email = ?
            ");
            $stmt->execute([$adminEmail]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($adminPassword, $user['password_hash'])) {
                echo "<p style='color: green;'>✅ Admin login test successful!</p>";
            } else {
                echo "<p style='color: red;'>❌ Admin login test failed</p>";
            }
        }
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Database Setup Complete!</h3>";
        echo "<p>Your MySQL database is now properly configured and ready to use.</p>";
        echo "<ul>";
        echo "<li><strong>Database:</strong> beersty_db</li>";
        echo "<li><strong>Configuration:</strong> {$workingConfig['name']}</li>";
        echo "<li><strong>Admin Email:</strong> <EMAIL></li>";
        echo "<li><strong>Admin Password:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
        echo "<h3>❌ Database Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ No Working MySQL Configuration Found</h3>";
    echo "<p>MySQL server is not accessible with any of the tested configurations.</p>";
    echo "<h4>Possible Solutions:</h4>";
    echo "<ul>";
    echo "<li><strong>XAMPP:</strong> Start Apache and MySQL in XAMPP Control Panel</li>";
    echo "<li><strong>WAMP:</strong> Start all services in WAMP</li>";
    echo "<li><strong>MAMP:</strong> Start MySQL service</li>";
    echo "<li><strong>Standalone MySQL:</strong> Start MySQL service</li>";
    echo "<li><strong>Check Firewall:</strong> Ensure MySQL port is not blocked</li>";
    echo "</ul>";
    echo "</div>";
    
    // Offer SQLite as fallback
    if (extension_loaded('pdo_sqlite')) {
        echo "<h2>🔄 SQLite Fallback Option</h2>";
        echo "<p>Since MySQL is not available, I can set up SQLite as a fallback:</p>";
        echo "<a href='?setup_sqlite=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Setup SQLite Database</a>";
    }
}

// Handle SQLite setup if requested
if (isset($_GET['setup_sqlite']) && extension_loaded('pdo_sqlite')) {
    echo "<h2>🗄️ Setting Up SQLite Database</h2>";
    
    try {
        // Create data directory
        if (!is_dir('data')) {
            mkdir('data', 0755, true);
            echo "<p>✅ Created data directory</p>";
        }
        
        // Create SQLite database
        $sqliteDb = 'data/beersty.sqlite';
        $dsn = "sqlite:$sqliteDb";
        $conn = new PDO($dsn);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ SQLite database created</p>";
        
        // Create tables (SQLite version)
        $conn->exec("
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME NULL
            )
        ");
        
        $conn->exec("
            CREATE TABLE IF NOT EXISTS profiles (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                role TEXT DEFAULT 'customer',
                brewery_id TEXT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (id) REFERENCES users(id)
            )
        ");
        
        $conn->exec("
            CREATE TABLE IF NOT EXISTS breweries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                address TEXT,
                city TEXT,
                state TEXT,
                zip TEXT,
                phone TEXT,
                email TEXT,
                website TEXT,
                description TEXT,
                brewery_type TEXT DEFAULT 'micro',
                verified INTEGER DEFAULT 0,
                claimed INTEGER DEFAULT 0,
                claimable INTEGER DEFAULT 1,
                follower_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                external_id TEXT,
                latitude REAL,
                longitude REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        echo "<p>✅ SQLite tables created</p>";
        
        // Create admin user
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT OR REPLACE INTO users (id, email, password_hash) VALUES (?, ?, ?)");
        $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
        
        $stmt = $conn->prepare("INSERT OR REPLACE INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
        $stmt->execute([$userId, '<EMAIL>']);
        
        echo "<p>✅ Admin user created</p>";
        
        // Update database config for SQLite
        $configContent = "<?php
class Database {
    private \$db_name = 'data/beersty.sqlite';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"sqlite:\" . \$this->db_name;
            \$this->conn = new PDO(\$dsn);
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"Database connection failed: \" . \$exception->getMessage());
        }
        
        return \$this->conn;
    }

    public function testConnection() {
        try {
            \$conn = \$this->getConnection();
            return \$conn !== null;
        } catch (Exception \$e) {
            return false;
        }
    }
}
?>";
        
        file_put_contents('config/database.php', $configContent);
        echo "<p>✅ Database configuration updated for SQLite</p>";
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 SQLite Setup Complete!</h3>";
        echo "<p>Your database is now ready to use with SQLite.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;'>";
        echo "<h3>❌ SQLite Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Try Login Now</a></li>";
echo "<li><a href='debug-login-final.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Debug Login</a></li>";
echo "<li><a href='/' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

hr {
    margin: 15px 0;
    border: none;
    border-top: 1px solid #ddd;
}
</style>
