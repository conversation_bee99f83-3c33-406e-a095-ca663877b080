<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize session variables if they don't exist to prevent undefined key warnings
function initializeSession() {
    if (!isset($_SESSION['initialized'])) {
        $_SESSION['initialized'] = true;

        // Set default values for session variables
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['user_id'] = null;
        }
        if (!isset($_SESSION['user_email'])) {
            $_SESSION['user_email'] = null;
        }
        if (!isset($_SESSION['user_role'])) {
            $_SESSION['user_role'] = null;
        }
        if (!isset($_SESSION['brewery_id'])) {
            $_SESSION['brewery_id'] = null;
        }
    }
}

// Call session initialization
initializeSession();

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    foreach ($env as $key => $value) {
        $_ENV[$key] = $value;
    }
}

// Application Configuration
define('APP_NAME', $_ENV['APP_NAME'] ?? 'Beersty');
define('BUSINESS_APP_NAME', $_ENV['BUSINESS_APP_NAME'] ?? 'Beersty - Brewery Management System');
define('APP_URL', $_ENV['APP_URL'] ?? 'http://localhost:8000');
define('UPLOAD_PATH', $_ENV['UPLOAD_PATH'] ?? 'uploads/');
define('MAX_FILE_SIZE', $_ENV['MAX_FILE_SIZE'] ?? 5242880); // 5MB

// Database Configuration
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_NAME', $_ENV['DB_NAME'] ?? 'beersty_db');
define('DB_USER', $_ENV['DB_USER'] ?? 'root');
define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');

// Session Configuration
define('SESSION_LIFETIME', $_ENV['SESSION_LIFETIME'] ?? 7200); // 2 hours

// Set timezone
date_default_timezone_set('America/New_York');

// Error reporting (disable in production)
if ($_ENV['APP_ENV'] ?? 'development' === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Include database connection
require_once __DIR__ . '/database.php';

// Helper functions
function url($path = '') {
    return '/' . ltrim($path, '/');
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']) &&
           isset($_SESSION['user_email']) && !empty($_SESSION['user_email']) &&
           isset($_SESSION['user_role']) && !empty($_SESSION['user_role']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect(url('auth/login.php'));
    }
}

function requireRole($role) {
    requireLogin();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== $role) {
        redirect(url('index.php'));
    }
}

function getCurrentUser() {
    if (isLoggedIn()) {
        return [
            'id' => $_SESSION['user_id'] ?? null,
            'email' => $_SESSION['user_email'] ?? '',
            'role' => $_SESSION['user_role'] ?? 'customer',
            'brewery_id' => $_SESSION['brewery_id'] ?? null
        ];
    }
    return null;
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}

function formatDateTime($datetime) {
    return date('M j, Y g:i A', strtotime($datetime));
}
?>
