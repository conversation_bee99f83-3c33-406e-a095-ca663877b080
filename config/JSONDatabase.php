<?php
/**
 * Simple JSON-based Database
 * A lightweight database implementation using JSON files
 * Compatible with PDO-style methods for easy replacement
 */

class JSONDatabase {
    private $dataDir;
    private $data;
    
    public function __construct() {
        $this->dataDir = __DIR__ . '/../database';
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
        
        $this->loadData();
        $this->initializeDefaultData();
    }
    
    private function loadData() {
        $dataFile = $this->dataDir . '/data.json';
        
        if (file_exists($dataFile)) {
            $json = file_get_contents($dataFile);
            $this->data = json_decode($json, true) ?: [];
        } else {
            $this->data = [
                'users' => [],
                'profiles' => [],
                'breweries' => []
            ];
        }
    }
    
    private function saveData() {
        $dataFile = $this->dataDir . '/data.json';
        file_put_contents($dataFile, json_encode($this->data, JSON_PRETTY_PRINT));
    }
    
    private function initializeDefaultData() {
        // Check if admin user exists
        $adminExists = false;
        foreach ($this->data['users'] as $user) {
            if ($user['email'] === '<EMAIL>') {
                $adminExists = true;
                break;
            }
        }
        
        if (!$adminExists) {
            $adminId = 'admin-user-id';
            $adminEmail = '<EMAIL>';
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            
            // Add admin user
            $this->data['users'][] = [
                'id' => $adminId,
                'email' => $adminEmail,
                'password_hash' => $adminPassword,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'email_verified' => 1,
                'last_login' => null
            ];
            
            // Add admin profile
            $this->data['profiles'][] = [
                'id' => $adminId,
                'email' => $adminEmail,
                'role' => 'admin',
                'first_name' => 'Admin',
                'last_name' => 'User',
                'brewery_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $this->saveData();
            error_log("JSON Database: Created default admin user");
        }
    }
    
    public function prepare($sql) {
        return new JSONStatement($this, $sql);
    }
    
    public function setAttribute($attribute, $value) {
        // Mock PDO method - do nothing
        return true;
    }
    
    public function getAttribute($attribute) {
        // Mock PDO method
        if ($attribute === PDO::ATTR_DRIVER_NAME) {
            return 'json';
        }
        return null;
    }
    
    public function executeQuery($sql, $params = []) {
        // Simple query parser for basic SELECT, INSERT, UPDATE operations
        $sql = trim($sql);
        
        if (preg_match('/^SELECT\s+(.+?)\s+FROM\s+(\w+)(?:\s+(\w+))?\s*(?:JOIN\s+(\w+)\s+(\w+)\s+ON\s+(.+?))?\s*(?:WHERE\s+(.+?))?$/i', $sql, $matches)) {
            return $this->handleSelect($matches, $params);
        } elseif (preg_match('/^INSERT\s+INTO\s+(\w+)\s*\((.+?)\)\s*VALUES\s*\((.+?)\)$/i', $sql, $matches)) {
            return $this->handleInsert($matches, $params);
        } elseif (preg_match('/^UPDATE\s+(\w+)\s+SET\s+(.+?)(?:\s+WHERE\s+(.+?))?$/i', $sql, $matches)) {
            return $this->handleUpdate($matches, $params);
        }
        
        return [];
    }
    
    private function handleSelect($matches, $params) {
        $fields = trim($matches[1]);
        $table = $matches[2];
        $alias = $matches[3] ?? '';
        $joinTable = $matches[4] ?? '';
        $joinAlias = $matches[5] ?? '';
        $joinCondition = $matches[6] ?? '';
        $whereClause = $matches[7] ?? '';
        
        $results = [];
        
        if (!isset($this->data[$table])) {
            return [];
        }
        
        foreach ($this->data[$table] as $row) {
            $result = $row;
            
            // Handle JOIN
            if ($joinTable && isset($this->data[$joinTable])) {
                foreach ($this->data[$joinTable] as $joinRow) {
                    if ($this->evaluateJoinCondition($row, $joinRow, $joinCondition)) {
                        $result = array_merge($result, $joinRow);
                        break;
                    }
                }
            }
            
            // Handle WHERE clause
            if ($whereClause) {
                if (!$this->evaluateWhereClause($result, $whereClause, $params)) {
                    continue;
                }
            }
            
            $results[] = $result;
        }
        
        return $results;
    }
    
    private function handleInsert($matches, $params) {
        $table = $matches[1];
        $fields = array_map('trim', explode(',', $matches[2]));
        $values = $params;
        
        if (!isset($this->data[$table])) {
            $this->data[$table] = [];
        }
        
        $row = [];
        for ($i = 0; $i < count($fields); $i++) {
            $field = trim($fields[$i]);
            $row[$field] = $values[$i] ?? null;
        }
        
        $this->data[$table][] = $row;
        $this->saveData();
        
        return true;
    }
    
    private function handleUpdate($matches, $params) {
        $table = $matches[1];
        $setClause = $matches[2];
        $whereClause = $matches[3] ?? '';
        
        if (!isset($this->data[$table])) {
            return false;
        }
        
        foreach ($this->data[$table] as &$row) {
            if ($whereClause) {
                if (!$this->evaluateWhereClause($row, $whereClause, $params)) {
                    continue;
                }
            }
            
            // Simple SET parsing - just update last_login for now
            if (strpos($setClause, 'last_login') !== false) {
                $row['last_login'] = date('Y-m-d H:i:s');
            }
        }
        
        $this->saveData();
        return true;
    }
    
    private function evaluateWhereClause($row, $whereClause, $params) {
        // Simple WHERE clause evaluation
        if (strpos($whereClause, '?') !== false) {
            // Parameter-based WHERE clause
            if (strpos($whereClause, 'email = ?') !== false) {
                return isset($row['email']) && $row['email'] === $params[0];
            }
            if (strpos($whereClause, 'id = ?') !== false) {
                return isset($row['id']) && $row['id'] === $params[0];
            }
        }
        
        return true;
    }
    
    private function evaluateJoinCondition($row1, $row2, $condition) {
        // Simple JOIN condition evaluation
        if (strpos($condition, 'u.id = p.id') !== false) {
            return $row1['id'] === $row2['id'];
        }
        
        return false;
    }
}

class JSONStatement {
    private $db;
    private $sql;
    private $results;
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        $this->results = $this->db->executeQuery($this->sql, $params);
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}
?>
