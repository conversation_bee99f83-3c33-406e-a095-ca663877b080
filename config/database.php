<?php
require_once __DIR__ . '/JSONDatabase.php';

class Database {
    private $host = 'localhost';
    private $db_name = 'beersty_db';
    private $username = 'beersty_user';
    private $password = 'beersty_pass';
    private $conn;

    public function __construct() {
        // Load configuration from environment or config file
        if (file_exists(__DIR__ . '/.env')) {
            $env = parse_ini_file(__DIR__ . '/.env');
            $this->host = $env['DB_HOST'] ?? $this->host;
            $this->db_name = $env['DB_NAME'] ?? $this->db_name;
            $this->username = $env['DB_USER'] ?? $this->username;
            $this->password = $env['DB_PASSWORD'] ?? $this->password;
        }
    }

    public function getConnection() {
        $this->conn = null;

        try {
            // Try MySQL first
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            error_log("MySQL connection failed: " . $exception->getMessage());

            // Fallback to JSON file database
            try {
                $this->conn = $this->createJSONDatabase();
                error_log("Using JSON file database as fallback");
            } catch(Exception $json_exception) {
                error_log("JSON database also failed: " . $json_exception->getMessage());
                throw new Exception("Database connection failed");
            }
        }

        return $this->conn;
    }

    public function testConnection() {
        try {
            $conn = $this->getConnection();
            return $conn !== null;
        } catch (Exception $e) {
            return false;
        }
    }

    private function createJSONDatabase() {
        // Check if system-wide shared database is available first
        if (file_exists('/opt/beersty-shared/config/shared_database.php')) {
            require_once '/opt/beersty-shared/config/shared_database.php';
            return new SharedDatabase();
        }

        // Check if user-space shared database is available
        $homeDir = $_SERVER['HOME'] ?? '/home/' . get_current_user();
        $userSharedConfig = $homeDir . '/.beersty-shared/config/user_shared_database.php';
        if (file_exists($userSharedConfig)) {
            require_once $userSharedConfig;
            return new UserSharedDatabase();
        }

        // Fallback to local JSON database
        return new JSONDatabase();
    }
}
?>
