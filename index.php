<?php
/**
 * Simplified Homepage - Based on Layout Reference
 * Beersty Platform
 */

require_once 'config/config.php';

$pageTitle = APP_NAME . ' - Discover Amazing Beers & Breweries';
$additionalCSS = ['/assets/css/home-simple.css', '/assets/css/video-hero.css', '/assets/css/location-search.css'];
$additionalJS = ['/assets/js/image-fallback.js', '/assets/js/video-hero.js', '/assets/js/home-search.js'];

// Featured Places Data with SEO-friendly slugs
$featured_places = [
    [
        'id' => 1,
        'slug' => 'craft-masters-brewery',
        'name' => 'Craft Masters Brewery',
        'type' => 'Brewery',
        'address' => '123 Beer Street, Downtown',
        'city' => 'Beer City',
        'state' => 'CA',
        'rating' => 4.8,
        'reviews_count' => 156,
        'image' => '/placeholders/450x300_beer_placeholder1.jpg',
        'featured_beer' => 'Hazy IPA Supreme',
        'price_range' => '$$',
        'features' => ['Outdoor Seating', 'Live Music', 'Food Truck']
    ],
    [
        'id' => 2,
        'slug' => 'the-hoppy-place',
        'name' => 'The Hoppy Place',
        'type' => 'Bar',
        'address' => '456 Craft Avenue, Midtown',
        'city' => 'Beer City',
        'state' => 'CA',
        'rating' => 4.6,
        'reviews_count' => 89,
        'image' => '/placeholders/450x300_beer_placeholder2.jpg',
        'featured_beer' => 'Local IPA Selection',
        'price_range' => '$$$',
        'features' => ['Happy Hour', 'Craft Beer', 'Sports TV']
    ],
    [
        'id' => 3,
        'slug' => 'sunshine-beer-garden',
        'name' => 'Sunshine Beer Garden',
        'type' => 'Beer Garden',
        'address' => '789 Garden Road, Uptown',
        'city' => 'Beer City',
        'state' => 'CA',
        'rating' => 4.7,
        'reviews_count' => 203,
        'image' => '/placeholders/450x300_beer_placeholder3.jpg',
        'featured_beer' => 'Summer Wheat',
        'price_range' => '$$',
        'features' => ['Pet Friendly', 'Outdoor Games', 'Family Friendly']
    ],
    [
        'id' => 4,
        'slug' => 'downtown-taphouse',
        'name' => 'Downtown Taphouse',
        'type' => 'Restaurant',
        'address' => '321 Main Street, Downtown',
        'city' => 'Beer City',
        'state' => 'CA',
        'rating' => 4.5,
        'reviews_count' => 127,
        'image' => '/placeholders/450x300_beer_placeholder1.jpg',
        'featured_beer' => 'Rotating Taps',
        'price_range' => '$$$',
        'features' => ['Full Menu', 'Date Night', 'Business Lunch']
    ]
];

// Get featured places and stats from database
$totalBreweries = 0;

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Get total brewery count for stats
    $stmt = $conn->query("SELECT COUNT(*) as total FROM breweries");
    $result = $stmt->fetch();
    $totalBreweries = $result ? $result['total'] : 0;

    // Get featured places from database (top rated/most liked breweries)
    $stmt = $conn->prepare("
        SELECT id, name, address, city, state, zip, description, brewery_type,
               like_count, follower_count, feature_image, logo
        FROM breweries
        WHERE verified = 1
        ORDER BY like_count DESC, follower_count DESC, created_at DESC
        LIMIT 4
    ");
    $stmt->execute();
    $db_places = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Transform database places to match expected format
    if (!empty($db_places)) {
        $featured_places = [];
        foreach ($db_places as $brewery) {
            $slug = strtolower(str_replace([' ', '&', '.', ','], ['-', 'and', '', ''], $brewery['name']));
            $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
            $slug = preg_replace('/-+/', '-', $slug);
            $slug = trim($slug, '-');

            $featured_places[] = [
                'id' => $brewery['id'],
                'slug' => $slug,
                'name' => $brewery['name'],
                'type' => ucfirst($brewery['brewery_type'] ?? 'Brewery'),
                'address' => $brewery['address'],
                'city' => $brewery['city'],
                'state' => $brewery['state'],
                'rating' => min(5.0, 3.5 + (($brewery['like_count'] ?? 0) / 100)),
                'reviews_count' => ($brewery['like_count'] ?? 0) + ($brewery['follower_count'] ?? 0),
                'image' => $brewery['feature_image'] ?: '/placeholders/450x300_beer_placeholder' . (($brewery['id'] % 4) + 1) . '.jpg',
                'featured_beer' => ['Craft IPA', 'Local Lager', 'Seasonal Ale', 'House Special'][array_rand(['Craft IPA', 'Local Lager', 'Seasonal Ale', 'House Special'])],
                'price_range' => ['$', '$$', '$$$'][array_rand(['$', '$$', '$$$'])],
                'features' => ['Craft Beer', 'Local Brewery', ucfirst($brewery['brewery_type'] ?? 'Brewery')]
            ];
        }
    }

} catch (Exception $e) {
    error_log("Error fetching featured places: " . $e->getMessage());
    $totalBreweries = 1203; // Fallback count
    // Continue with sample data
}

require_once 'includes/header.php';
?>

<!-- Video Hero Banner -->
<section class="video-hero-section position-relative overflow-hidden">
    <!-- Video Background Container -->
    <div class="video-background">
        <video id="heroVideo" autoplay muted loop playsinline class="hero-video">
            <source src="/newvideos/beersty_beachside.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>

        <!-- Video Overlay -->
        <div class="video-overlay position-absolute w-100 h-100"></div>

        <!-- Video Navigation Dots -->
        <div class="video-nav position-absolute">
            <button class="video-dot active" data-video="beachside" data-src="/newvideos/beersty_beachside.mp4" aria-label="Beachside video"></button>
            <button class="video-dot" data-video="tvs" data-src="/newvideos/beersty_tvs.mp4" aria-label="TVs video"></button>
            <button class="video-dot" data-video="pub" data-src="/newvideos/beersty_pub.mp4" aria-label="Pub video"></button>
            <button class="video-dot" data-video="couple" data-src="/newvideos/beersty_couple.mp4" aria-label="Couple video"></button>
        </div>
    </div>

    <!-- Hero Content -->
    <div class="container position-relative hero-content">
        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-10">
                <h1 class="display-4 fw-bold mb-4 hero-title">
                    <?php if (!isLoggedIn()): ?>
                        Discover Amazing Beer Places
                    <?php else: ?>
                        <?php $user = getCurrentUser(); ?>
                        Welcome back, <?php echo htmlspecialchars($user['first_name'] ?? 'Beer Lover'); ?>!
                    <?php endif; ?>
                </h1>
                <p class="lead mb-5 hero-subtitle">
                    Find breweries, bars, and beer gardens. Check-in, review, and share your experiences.
                </p>

                <!-- Main Search Bar -->
                <div class="search-container rounded-3 p-4 shadow-lg mb-5">
                    <form action="/places/search/" method="GET" class="row g-3 align-items-center">
                        <div class="col-md-4 col-lg-4">
                            <div class="input-group">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" name="search" class="form-control border-start-0"
                                       placeholder="Search for breweries, bars..." style="box-shadow: none;">
                            </div>
                        </div>
                        <div class="col-md-5 col-lg-5">
                            <div class="input-group location-input-container">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <input type="text" name="location" class="form-control border-start-0"
                                       placeholder="City, state..."
                                       autocomplete="off"
                                       style="box-shadow: none;"
                                       title="Smart location search with suggestions">
                                <input type="hidden" name="location_lat" id="location_lat">
                                <input type="hidden" name="location_lng" id="location_lng">
                            </div>
                        </div>
                        <div class="col-md-3 col-lg-3">
                            <button type="submit" class="btn btn-search w-100 fw-semibold">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Quick Actions -->
                <?php if (!isLoggedIn()): ?>
                    <div class="d-flex justify-content-center gap-3 mb-4">
                        <a href="/register/" class="btn btn-hero-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Join Now
                        </a>
                        <a href="/login/" class="btn btn-hero-outline btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Featured Places Section -->
<section class="featured-places-section py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="h3 fw-bold mb-3">
                    <i class="fas fa-star text-warning me-2"></i>
                    Featured Places
                </h2>
                <p class="text-muted mb-0">Discover the best breweries, bars, and beer destinations</p>
            </div>
        </div>

        <div class="row g-4">
            <?php foreach ($featured_places as $place): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="featured-place-card card h-100 shadow-sm border-0">
                        <div class="place-image position-relative">
                            <img src="<?php echo htmlspecialchars($place['image']); ?>"
                                 class="card-img-top place-img"
                                 alt="<?php echo htmlspecialchars($place['name']); ?>"
                                 style="height: 200px; object-fit: cover;"
                                 data-type="place"
                                 loading="lazy">
                            <div class="place-badge position-absolute top-0 start-0 m-2">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($place['type']); ?></span>
                            </div>
                            <div class="place-rating position-absolute top-0 end-0 m-2">
                                <span class="badge bg-dark">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    <?php echo $place['rating']; ?>
                                </span>
                            </div>
                        </div>

                        <div class="card-body d-flex flex-column">
                            <div class="card-content-group">
                                <h5 class="card-title">
                                    <a href="places/<?php echo $place['slug']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($place['name']); ?>
                                    </a>
                                </h5>

                                <p class="card-text text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($place['address']); ?>
                                </p>

                                <div class="featured-beer">
                                    <small class="text-primary fw-bold">
                                        <i class="fas fa-beer me-1"></i>
                                        <?php echo htmlspecialchars($place['featured_beer']); ?>
                                    </small>
                                </div>

                                <div class="place-features">
                                    <?php foreach (array_slice($place['features'], 0, 2) as $feature): ?>
                                        <span class="badge bg-light text-dark small"><?php echo htmlspecialchars($feature); ?></span>
                                    <?php endforeach; ?>
                                    <?php if (count($place['features']) > 2): ?>
                                        <span class="badge bg-light text-dark small">+<?php echo count($place['features']) - 2; ?></span>
                                    <?php endif; ?>
                                </div>

                                <div class="place-meta">
                                    <div class="rating-info">
                                        <span class="rating-stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= floor($place['rating'])): ?>
                                                    ⭐
                                                <?php else: ?>
                                                    ☆
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </span>
                                        <small class="text-muted">(<?php echo $place['reviews_count']; ?>)</small>
                                    </div>
                                    <div class="price-range">
                                        <span class="badge bg-success"><?php echo htmlspecialchars($place['price_range']); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="place-actions mt-auto">
                                <div class="row">
                                    <div class="col-9">
                                        <a href="/places/<?php echo $place['slug']; ?>" class="btn btn-sm w-100" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </div>
                                    <div class="col-3">
                                        <?php if (isLoggedIn()): ?>
                                            <button class="btn btn-outline-primary btn-sm" title="Add to Favorites">
                                                <i class="fas fa-heart"></i>
                                            </button>
                                        <?php else: ?>
                                            <a href="/login/" class="btn btn-outline-primary btn-sm" title="Login to Save">
                                                <i class="fas fa-heart"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="/places/search.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-search me-2"></i>
                    Explore All Places
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Main Content Section -->
<div class="main-content py-5">
    <div class="container">
        <div class="row">
            <!-- Left Column - Main Content -->
            <div class="col-lg-8">
                <div class="row g-4">
                    <!-- Discover Breweries -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-map-marker-alt fa-3x text-primary"></i>
                            </div>
                            <h4>Discover Breweries</h4>
                            <p>Find amazing local breweries and hidden gems in your area.</p>
                            <a href="/breweries/discover/" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Explore Now
                            </a>
                        </div>
                    </div>
                    
                    <!-- Rate & Review -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-star fa-3x text-warning"></i>
                            </div>
                            <h4>Rate & Review</h4>
                            <p>Share your beer experiences and help others discover great beers.</p>
                            <a href="/beers/discover/" class="btn btn-warning">
                                <i class="fas fa-beer me-2"></i>Find Beers
                            </a>
                        </div>
                    </div>
                    
                    <!-- Social Features -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-users fa-3x text-success"></i>
                            </div>
                            <h4>Connect & Share</h4>
                            <p>Follow friends and share your beer journey with the community.</p>
                            <?php if (isLoggedIn()): ?>
                                <a href="/social/feed/" class="btn btn-success">
                                    <i class="fas fa-stream me-2"></i>Activity Feed
                                </a>
                            <?php else: ?>
                                <a href="/register/" class="btn btn-success">
                                    <i class="fas fa-user-plus me-2"></i>Join Community
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Check-in Feature -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-check-circle fa-3x text-info"></i>
                            </div>
                            <h4>Check-in Anywhere</h4>
                            <p>Share what you're drinking with photos, ratings, and location.</p>
                            <?php if (isLoggedIn()): ?>
                                <a href="/social/checkin/" class="btn btn-info">
                                    <i class="fas fa-check-circle me-2"></i>Check In Now
                                </a>
                            <?php else: ?>
                                <a href="/login/" class="btn btn-info">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In to Check-in
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column - Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Activity -->
                <div class="sidebar-card mb-4">
                    <h5 class="card-title">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        Recent Activity
                    </h5>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">🍺</div>
                            <div class="activity-text">
                                <strong>Alex M.</strong> checked in at Local Brewery
                                <small class="text-muted d-block">2 minutes ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">⭐</div>
                            <div class="activity-text">
                                <strong>Sarah K.</strong> rated Hazy IPA 5 stars
                                <small class="text-muted d-block">5 minutes ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📸</div>
                            <div class="activity-text">
                                <strong>Mike R.</strong> shared a photo
                                <small class="text-muted d-block">10 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <?php if (isLoggedIn()): ?>
                        <a href="/social/feed/" class="btn btn-outline-primary btn-sm w-100 mt-3">
                            View All Activity
                        </a>
                    <?php endif; ?>
                </div>
                
                <!-- Popular Beers -->
                <div class="sidebar-card">
                    <h5 class="card-title">
                        <i class="fas fa-fire me-2 text-danger"></i>
                        Popular This Week
                    </h5>
                    <div class="popular-list">
                        <div class="popular-item">
                            <div class="popular-rank">1</div>
                            <div class="popular-info">
                                <div class="popular-name">Hazy IPA Supreme</div>
                                <div class="popular-brewery">Craft Masters</div>
                                <div class="popular-rating">⭐⭐⭐⭐⭐ 4.8</div>
                            </div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">2</div>
                            <div class="popular-info">
                                <div class="popular-name">Imperial Stout</div>
                                <div class="popular-brewery">Dark Arts Brewing</div>
                                <div class="popular-rating">⭐⭐⭐⭐ 4.6</div>
                            </div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">3</div>
                            <div class="popular-info">
                                <div class="popular-name">Wheat Wonder</div>
                                <div class="popular-brewery">Sunshine Brewery</div>
                                <div class="popular-rating">⭐⭐⭐⭐ 4.5</div>
                            </div>
                        </div>
                    </div>
                    <a href="/beers/trending/" class="btn btn-outline-danger btn-sm w-100 mt-3">
                        View Trending Beers
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="cta-section bg-primary text-white py-5">
    <div class="container text-center">
        <h2 class="mb-3">Ready to Start Your Beer Journey?</h2>
        <p class="lead mb-4">Join thousands of beer enthusiasts discovering amazing breweries and sharing their experiences.</p>
        
        <?php if (!isLoggedIn()): ?>
            <div class="d-flex justify-content-center gap-3">
                <a href="/register/" class="btn btn-light btn-lg">
                    <i class="fas fa-rocket me-2"></i>Get Started Today
                </a>
                <a href="/breweries/discover/" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-search me-2"></i>Browse Breweries
                </a>
            </div>
        <?php else: ?>
            <a href="/social/checkin/" class="btn btn-light btn-lg">
                <i class="fas fa-check-circle me-2"></i>Make Your First Check-in
            </a>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
