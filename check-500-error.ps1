# CHECK 500 INTERNAL SERVER ERROR
# Diagnose and fix the server error

Write-Host "DIAGNOSING 500 INTERNAL SERVER ERROR" -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Red

# Check Apache error log
Write-Host ""
Write-Host "1. CHECKING APACHE ERROR LOG:" -ForegroundColor Cyan

$errorLog = "C:\xampp\apache\logs\error.log"
if (Test-Path $errorLog) {
    Write-Host "Found error log: $errorLog" -ForegroundColor Green
    Write-Host "Last 10 error entries:" -ForegroundColor Yellow
    
    try {
        $errors = Get-Content $errorLog -Tail 10 -ErrorAction SilentlyContinue
        if ($errors) {
            foreach ($error in $errors) {
                Write-Host "  $error" -ForegroundColor White
            }
        } else {
            Write-Host "  No recent errors in log" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  Could not read error log" -ForegroundColor Red
    }
} else {
    Write-Host "Error log not found at: $errorLog" -ForegroundColor Red
}

# Check PHP error log
Write-Host ""
Write-Host "2. CHECKING PHP ERROR LOG:" -ForegroundColor Cyan

$phpErrorLog = "C:\xampp\php\logs\php_error_log"
if (Test-Path $phpErrorLog) {
    Write-Host "Found PHP error log: $phpErrorLog" -ForegroundColor Green
    Write-Host "Last 5 PHP errors:" -ForegroundColor Yellow
    
    try {
        $phpErrors = Get-Content $phpErrorLog -Tail 5 -ErrorAction SilentlyContinue
        if ($phpErrors) {
            foreach ($error in $phpErrors) {
                Write-Host "  $error" -ForegroundColor White
            }
        } else {
            Write-Host "  No recent PHP errors" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  Could not read PHP error log" -ForegroundColor Red
    }
} else {
    Write-Host "PHP error log not found at: $phpErrorLog" -ForegroundColor Red
}

# Check if PHP is working at all
Write-Host ""
Write-Host "3. TESTING BASIC PHP:" -ForegroundColor Cyan

$basicPhpTest = '<?php echo "PHP is working!"; phpinfo(); ?>'
$testFile = "php-test.php"
$basicPhpTest | Set-Content $testFile -Encoding UTF8

Write-Host "Created basic PHP test: $testFile" -ForegroundColor Green
Write-Host "Test this URL: http://localhost:8000/php-test.php" -ForegroundColor Yellow

# Check Apache configuration
Write-Host ""
Write-Host "4. CHECKING APACHE CONFIGURATION:" -ForegroundColor Cyan

$httpdConf = "C:\xampp\apache\conf\httpd.conf"
if (Test-Path $httpdConf) {
    Write-Host "Checking Apache config..." -ForegroundColor Gray
    
    # Check if PHP module is loaded
    $phpModule = Select-String -Path $httpdConf -Pattern "LoadModule.*php" -ErrorAction SilentlyContinue
    if ($phpModule) {
        Write-Host "PHP module configuration:" -ForegroundColor Yellow
        $phpModule | ForEach-Object { Write-Host "  $($_.Line.Trim())" -ForegroundColor White }
    } else {
        Write-Host "❌ PHP module not found in Apache config!" -ForegroundColor Red
    }
    
    # Check DocumentRoot
    $docRoot = Select-String -Path $httpdConf -Pattern "^DocumentRoot" -ErrorAction SilentlyContinue
    if ($docRoot) {
        Write-Host "Document Root:" -ForegroundColor Yellow
        $docRoot | ForEach-Object { Write-Host "  $($_.Line.Trim())" -ForegroundColor White }
    }
} else {
    Write-Host "Apache config not found" -ForegroundColor Red
}

# Check if project files exist
Write-Host ""
Write-Host "5. CHECKING PROJECT FILES:" -ForegroundColor Cyan

$htdocs = "C:\xampp\htdocs"
$projectPath = Join-Path $htdocs "beersty-lovable"

if (Test-Path $projectPath) {
    Write-Host "✅ Project folder exists: $projectPath" -ForegroundColor Green
    
    # Check key files
    $keyFiles = @("index.php", "admin/user-management.php", "config/config.php")
    foreach ($file in $keyFiles) {
        $filePath = Join-Path $projectPath $file
        if (Test-Path $filePath) {
            Write-Host "  ✅ $file exists" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $file missing" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Project folder not found: $projectPath" -ForegroundColor Red
    Write-Host "Current directory contents:" -ForegroundColor Yellow
    if (Test-Path $htdocs) {
        Get-ChildItem $htdocs | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
    }
}

# Check PHP configuration
Write-Host ""
Write-Host "6. CHECKING PHP CONFIGURATION:" -ForegroundColor Cyan

$phpIni = "C:\xampp\php\php.ini"
if (Test-Path $phpIni) {
    Write-Host "Checking PHP configuration..." -ForegroundColor Gray
    
    # Check error reporting
    $errorReporting = Select-String -Path $phpIni -Pattern "^error_reporting" -ErrorAction SilentlyContinue
    if ($errorReporting) {
        Write-Host "Error reporting:" -ForegroundColor Yellow
        $errorReporting | ForEach-Object { Write-Host "  $($_.Line.Trim())" -ForegroundColor White }
    }
    
    # Check display errors
    $displayErrors = Select-String -Path $phpIni -Pattern "^display_errors" -ErrorAction SilentlyContinue
    if ($displayErrors) {
        Write-Host "Display errors:" -ForegroundColor Yellow
        $displayErrors | ForEach-Object { Write-Host "  $($_.Line.Trim())" -ForegroundColor White }
    }
} else {
    Write-Host "PHP config not found" -ForegroundColor Red
}

# Quick fixes
Write-Host ""
Write-Host "7. QUICK FIXES TO TRY:" -ForegroundColor Red
Write-Host "=====================" -ForegroundColor Red

Write-Host ""
Write-Host "FIX 1: Enable PHP error display" -ForegroundColor Yellow
Write-Host "Add this to your PHP files temporarily:" -ForegroundColor White
Write-Host "<?php ini_set('display_errors', 1); error_reporting(E_ALL); ?>" -ForegroundColor Gray

Write-Host ""
Write-Host "FIX 2: Check file permissions" -ForegroundColor Yellow
Write-Host "Make sure Apache can read your project files" -ForegroundColor White

Write-Host ""
Write-Host "FIX 3: Test basic PHP first" -ForegroundColor Yellow
Write-Host "Go to: http://localhost:8000/php-test.php" -ForegroundColor White

Write-Host ""
Write-Host "FIX 4: Restart Apache" -ForegroundColor Yellow
Write-Host "Stop and start Apache in XAMPP Control Panel" -ForegroundColor White

Write-Host ""
Write-Host "Opening test URLs..." -ForegroundColor Cyan
Start-Process "http://localhost:8000/php-test.php"
Start-Process "http://localhost:8000"

Write-Host ""
Read-Host "Press Enter to continue"
