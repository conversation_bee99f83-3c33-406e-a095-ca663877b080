<?php
/**
 * Simple Database Connection Test
 */

echo "<h1>🔍 Simple Database Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;} .success{background:#d4edda;color:#155724;padding:10px;border-radius:5px;margin:10px 0;} .error{background:#f8d7da;color:#721c24;padding:10px;border-radius:5px;margin:10px 0;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. PHP PDO MySQL Extension Check</h2>";
if (extension_loaded('pdo_mysql')) {
    echo "<div class='success'>✅ PDO MySQL extension is loaded</div>";
} else {
    echo "<div class='error'>❌ PDO MySQL extension is NOT loaded</div>";
    echo "<p>Please install php-mysql extension</p>";
    exit;
}

echo "<h2>2. Basic MySQL Connection Test</h2>";
try {
    echo "<p>Attempting to connect to MySQL...</p>";
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $conn = new PDO($dsn, 'root', '');
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ MySQL connection successful</div>";
    
    // Get MySQL version
    $stmt = $conn->prepare("SELECT VERSION() as version");
    $stmt->execute();
    $version = $stmt->fetch();
    echo "<p><strong>MySQL Version:</strong> " . htmlspecialchars($version['version']) . "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ MySQL connection failed</div>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    
    echo "<h3>🔧 Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li>Check if XAMPP MySQL service is running</li>";
    echo "<li>Open XAMPP Control Panel and start MySQL</li>";
    echo "<li>Check if port 3306 is available</li>";
    echo "<li>Try restarting XAMPP</li>";
    echo "</ol>";
    
    echo "<h3>🔗 Quick Actions:</h3>";
    echo "<p><a href='http://localhost/phpmyadmin' target='_blank' style='background:#007bff;color:white;padding:10px;text-decoration:none;border-radius:3px;'>Open phpMyAdmin</a></p>";
    exit;
}

echo "<h2>3. Database 'beersty_db' Check</h2>";
try {
    $stmt = $conn->prepare("SHOW DATABASES LIKE 'beersty_db'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>✅ Database 'beersty_db' exists</div>";
        
        // Connect to the database
        $conn->exec("USE beersty_db");
        echo "<div class='success'>✅ Connected to beersty_db</div>";
        
    } else {
        echo "<div class='error'>❌ Database 'beersty_db' does not exist</div>";
        echo "<p>Creating database...</p>";
        
        try {
            $conn->exec("CREATE DATABASE beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<div class='success'>✅ Database 'beersty_db' created</div>";
            $conn->exec("USE beersty_db");
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create database: " . htmlspecialchars($e->getMessage()) . "</div>";
            exit;
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database check failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    exit;
}

echo "<h2>4. Test Using Database Class</h2>";
try {
    require_once 'config/config.php';
    $db = new Database();
    $testConn = $db->getConnection();
    echo "<div class='success'>✅ Database class connection successful</div>";
    
    // Test a simple query
    $stmt = $testConn->prepare("SELECT 1 as test");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p><strong>Test Query Result:</strong> " . $result['test'] . "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database class connection failed</div>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>5. Summary</h2>";
echo "<div class='success'>";
echo "<h3>✅ All Tests Passed!</h3>";
echo "<p>Your database connection is working properly.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li><a href='debug-login.php'>Run Login Debug Tool</a></li>";
echo "<li><a href='auth/login.php'>Try Login Page</a></li>";
echo "<li><a href='xampp-setup.php'>Setup Database Tables</a></li>";
echo "</ul>";
echo "</div>";
?>
