<?php
require_once 'config/config.php';

echo "<h1>🍺 Setting Up Menu Management Tables</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>📋 Step 1: Creating Beer Menu Tables</h2>";
    
    // Create beer_styles table
    $beerStylesTable = "
    CREATE TABLE IF NOT EXISTS beer_styles (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(100) NOT NULL UNIQUE,
        category VARCHAR(50) NOT NULL,
        description TEXT NULL,
        characteristics JSON NULL,
        abv_min DECIMAL(4,2) NULL,
        abv_max DECIMAL(4,2) NULL,
        ibu_min INT NULL,
        ibu_max INT NULL,
        srm_min INT NULL,
        srm_max INT NULL,
        parent_style_id VARCHAR(36) NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
    )";
    $conn->exec($beerStylesTable);
    echo "<p>✅ Beer styles table created</p>";
    
    // Create beer_menu table
    $beerMenuTable = "
    CREATE TABLE IF NOT EXISTS beer_menu (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        brewery_id INT NOT NULL,
        beer_style_id VARCHAR(36) NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        abv DECIMAL(4,2) NULL,
        ibu INT NULL,
        srm INT NULL,
        price DECIMAL(8,2) NULL,
        price_half DECIMAL(8,2) NULL,
        tap_number INT NULL,
        hops TEXT NULL,
        malts TEXT NULL,
        yeast VARCHAR(100) NULL,
        thumbnail VARCHAR(255) NULL,
        images JSON NULL,
        featured BOOLEAN DEFAULT FALSE,
        seasonal BOOLEAN DEFAULT FALSE,
        limited_edition BOOLEAN DEFAULT FALSE,
        available BOOLEAN DEFAULT TRUE,
        availability_start DATE NULL,
        availability_end DATE NULL,
        average_rating DECIMAL(3,2) DEFAULT 0.00,
        total_ratings INT DEFAULT 0,
        total_checkins INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        FOREIGN KEY (beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
    )";
    $conn->exec($beerMenuTable);
    echo "<p>✅ Beer menu table created</p>";
    
    echo "<h2>🍽️ Step 2: Creating Food Menu Tables</h2>";
    
    // Create food_categories table
    $foodCategoriesTable = "
    CREATE TABLE IF NOT EXISTS food_categories (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(100) NOT NULL,
        description TEXT,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($foodCategoriesTable);
    echo "<p>✅ Food categories table created</p>";
    
    // Create food_menu table
    $foodMenuTable = "
    CREATE TABLE IF NOT EXISTS food_menu (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        brewery_id INT NOT NULL,
        category_id VARCHAR(36) NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        price DECIMAL(8,2) NULL,
        ingredients TEXT NULL,
        allergens TEXT NULL,
        is_vegetarian BOOLEAN DEFAULT FALSE,
        is_vegan BOOLEAN DEFAULT FALSE,
        is_gluten_free BOOLEAN DEFAULT FALSE,
        image VARCHAR(255) NULL,
        available BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES food_categories(id) ON DELETE SET NULL
    )";
    $conn->exec($foodMenuTable);
    echo "<p>✅ Food menu table created</p>";
    
    echo "<h2>🎯 Step 3: Adding Sample Beer Styles</h2>";
    
    $beerStyles = [
        ['IPA', 'IPA', 'India Pale Ale with hoppy character', 5.0, 7.5, 40, 70, 6, 14],
        ['American IPA', 'IPA', 'American-style India Pale Ale', 5.5, 7.5, 40, 70, 6, 14],
        ['Hazy IPA', 'IPA', 'New England style hazy IPA', 6.0, 8.0, 25, 60, 3, 7],
        ['Lager', 'Lager', 'Clean, crisp lager beer', 4.2, 5.8, 8, 25, 2, 6],
        ['Pilsner', 'Lager', 'Light, hoppy lager', 4.2, 5.8, 25, 45, 2, 6],
        ['Stout', 'Stout', 'Dark, rich stout', 4.0, 12.0, 25, 60, 30, 40],
        ['Imperial Stout', 'Stout', 'Strong, dark stout', 8.0, 12.0, 50, 90, 30, 40],
        ['Wheat Beer', 'Wheat', 'Light, refreshing wheat beer', 4.3, 5.6, 10, 25, 2, 6],
        ['Hefeweizen', 'Wheat', 'German-style wheat beer', 4.3, 5.6, 8, 15, 2, 6],
        ['Porter', 'Porter', 'Dark, malty porter', 4.0, 6.5, 18, 35, 20, 30]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO beer_styles (id, name, category, description, abv_min, abv_max, ibu_min, ibu_max, srm_min, srm_max) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($beerStyles as $style) {
        $stmt->execute($style);
        echo "<p>✅ Added beer style: {$style[0]}</p>";
    }
    
    echo "<h2>🍽️ Step 4: Adding Sample Food Categories</h2>";
    
    $foodCategories = [
        ['Appetizers', 'Starters and small plates', 1],
        ['Entrees', 'Main dishes and hearty meals', 2],
        ['Sandwiches', 'Burgers, sandwiches, and wraps', 3],
        ['Salads', 'Fresh salads and healthy options', 4],
        ['Sides', 'Side dishes and accompaniments', 5],
        ['Desserts', 'Sweet treats and desserts', 6],
        ['Kids Menu', 'Child-friendly options', 7]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO food_categories (id, name, description, sort_order) VALUES (UUID(), ?, ?, ?)");
    
    foreach ($foodCategories as $category) {
        $stmt->execute($category);
        echo "<p>✅ Added food category: {$category[0]}</p>";
    }
    
    echo "<h2>🍺 Step 5: Adding Sample Beer Menu Items</h2>";
    
    // Get the demo brewery ID
    $stmt = $conn->prepare("SELECT id FROM breweries WHERE name = 'Demo Brewery' LIMIT 1");
    $stmt->execute();
    $brewery = $stmt->fetch();
    
    if ($brewery) {
        $breweryId = $brewery['id'];
        
        // Get some beer style IDs
        $stmt = $conn->prepare("SELECT id, name FROM beer_styles LIMIT 10");
        $stmt->execute();
        $styles = $stmt->fetchAll();
        
        $sampleBeers = [
            ['Hoppy IPA', 'A bold and hoppy IPA with citrus notes and a crisp finish', 6.5, 65, 8, 7.00, 1, true],
            ['Smooth Lager', 'A crisp and refreshing lager perfect for any occasion', 4.8, 22, 3, 6.00, 2, true],
            ['Dark Stout', 'Rich and creamy stout with notes of chocolate and coffee', 7.2, 45, 35, 8.00, 3, false],
            ['Summer Wheat', 'Light and refreshing wheat beer with citrus hints', 5.2, 18, 4, 6.50, 4, true],
            ['Imperial Porter', 'Bold porter with roasted malt character', 8.5, 55, 28, 9.00, 5, true],
            ['Hazy Pale Ale', 'Juicy and hazy pale ale with tropical fruit flavors', 5.8, 35, 5, 7.50, 6, true]
        ];
        
        $stmt = $conn->prepare("INSERT INTO beer_menu (id, brewery_id, beer_style_id, name, description, abv, ibu, srm, price, tap_number, available) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sampleBeers as $index => $beer) {
            $styleId = $styles[$index % count($styles)]['id'] ?? null;
            $stmt->execute([
                $breweryId,
                $styleId,
                $beer[0], // name
                $beer[1], // description
                $beer[2], // abv
                $beer[3], // ibu
                $beer[4], // srm
                $beer[5], // price
                $beer[6], // tap_number
                $beer[7]  // available
            ]);
            echo "<p>✅ Added beer: {$beer[0]}</p>";
        }
    } else {
        echo "<p>⚠️ Demo brewery not found, skipping sample beer data</p>";
    }
    
    echo "<h2>🍽️ Step 6: Adding Sample Food Menu Items</h2>";
    
    if ($brewery) {
        // Get food category IDs
        $stmt = $conn->prepare("SELECT id, name FROM food_categories");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        $sampleFood = [
            ['Beer Cheese Dip', 'Creamy cheese dip made with our house beer', 8.99, 'Appetizers'],
            ['Brewery Burger', 'Juicy beef burger with bacon and cheese', 14.99, 'Entrees'],
            ['Fish & Chips', 'Beer-battered fish with crispy fries', 16.99, 'Entrees'],
            ['Caesar Salad', 'Fresh romaine with parmesan and croutons', 11.99, 'Salads'],
            ['Loaded Fries', 'Crispy fries with cheese, bacon, and sour cream', 9.99, 'Sides'],
            ['Chocolate Stout Cake', 'Rich chocolate cake made with our stout', 7.99, 'Desserts']
        ];
        
        $stmt = $conn->prepare("INSERT INTO food_menu (id, brewery_id, category_id, name, description, price) VALUES (UUID(), ?, ?, ?, ?, ?)");
        
        foreach ($sampleFood as $food) {
            $categoryId = null;
            foreach ($categories as $cat) {
                if ($cat['name'] === $food[3]) {
                    $categoryId = $cat['id'];
                    break;
                }
            }
            
            $stmt->execute([
                $breweryId,
                $categoryId,
                $food[0], // name
                $food[1], // description
                $food[2]  // price
            ]);
            echo "<p>✅ Added food item: {$food[0]}</p>";
        }
    }
    
    // Get final counts
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_styles");
    $stmt->execute();
    $styleCount = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu");
    $stmt->execute();
    $beerCount = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM food_categories");
    $stmt->execute();
    $categoryCount = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM food_menu");
    $stmt->execute();
    $foodCount = $stmt->fetch()['count'];
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Menu Management Setup Complete!</h3>";
    echo "<p><strong>Beer Styles:</strong> $styleCount</p>";
    echo "<p><strong>Beer Menu Items:</strong> $beerCount</p>";
    echo "<p><strong>Food Categories:</strong> $categoryCount</p>";
    echo "<p><strong>Food Menu Items:</strong> $foodCount</p>";
    echo "<p><strong>Ready for:</strong> Menu management, digital boards, and customer viewing</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='brewery/menu.php' style='color: #6F4C3E;'>🍺 Access Menu Management</a></li>";
echo "<li><a href='brewery/digital-board.php' style='color: #6F4C3E;'>📺 Digital Board Admin</a></li>";
echo "<li><a href='places/search.php' style='color: #6F4C3E;'>🔍 View Places with Menus</a></li>";
echo "</ul>";
?>
