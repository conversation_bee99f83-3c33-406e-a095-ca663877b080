<?php
// Create Admin User Script for Beersty
echo "=== Beersty Admin User Creation ===" . PHP_EOL;

try {
    // Connect to database
    $pdo = new PDO('mysql:host=localhost;dbname=beersty_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected successfully" . PHP_EOL;
    
    // Check current users
    echo "\n--- Current Users ---" . PHP_EOL;
    $stmt = $pdo->query('SELECT id, email, created_at FROM users LIMIT 10');
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "❌ No users found in database" . PHP_EOL;
    } else {
        echo "Found " . count($users) . " users:" . PHP_EOL;
        foreach ($users as $user) {
            echo "  • " . $user['email'] . " (ID: " . $user['id'] . ")" . PHP_EOL;
        }
    }
    
    // Check if admin user exists
    $adminEmail = '<EMAIL>';
    $stmt = $pdo->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->execute([$adminEmail]);
    $adminExists = $stmt->fetch();
    
    if ($adminExists) {
        echo "\n✅ Admin user already exists: $adminEmail" . PHP_EOL;
    } else {
        echo "\n❌ Admin user not found. Creating admin user..." . PHP_EOL;
        
        // Generate UUID for user ID
        $userId = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
        
        // Create admin user
        $adminPassword = 'admin123'; // Default password
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        
        // Insert into users table
        $stmt = $pdo->prepare('INSERT INTO users (id, email, password_hash, email_verified, created_at) VALUES (?, ?, ?, 1, NOW())');
        $stmt->execute([$userId, $adminEmail, $hashedPassword]);
        
        // Insert into profiles table
        $stmt = $pdo->prepare('INSERT INTO profiles (id, email, role, first_name, last_name, username, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())');
        $stmt->execute([$userId, $adminEmail, 'admin', 'Admin', 'User', 'admin']);
        
        echo "✅ Admin user created successfully!" . PHP_EOL;
        echo "   Email: $adminEmail" . PHP_EOL;
        echo "   Password: $adminPassword" . PHP_EOL;
        echo "   User ID: $userId" . PHP_EOL;
    }
    
    // Verify admin user can be found
    echo "\n--- Verification ---" . PHP_EOL;
    $stmt = $pdo->prepare('SELECT u.id, u.email, p.role, u.created_at FROM users u LEFT JOIN profiles p ON u.id = p.id WHERE u.email = ?');
    $stmt->execute([$adminEmail]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "✅ Admin user verified:" . PHP_EOL;
        echo "   Email: " . $admin['email'] . PHP_EOL;
        echo "   Role: " . $admin['role'] . PHP_EOL;
        echo "   ID: " . $admin['id'] . PHP_EOL;
        echo "   Created: " . $admin['created_at'] . PHP_EOL;
    } else {
        echo "❌ Admin user verification failed" . PHP_EOL;
    }
    
    echo "\n=== Admin User Setup Complete ===" . PHP_EOL;
    echo "You can now login with:" . PHP_EOL;
    echo "Email: <EMAIL>" . PHP_EOL;
    echo "Password: admin123" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
