# Beersty Platform - SEO-Optimized URL Rewriting
# Clean URLs and 301 Redirects for Better SEO

RewriteEngine On
RewriteBase /

# ===== PLACES ROUTES (PRIORITY) =====
# Places routes must come first to avoid conflicts

# Generic places routes
RewriteRule ^places/?$ places/search.php [NC,L]
RewriteRule ^places/search/?$ places/search.php [NC,L]

# Legacy support for old URLs
RewriteRule ^places/profile/([0-9]+)/?$ places/profile/index.php?id=$1 [NC,L]
RewriteRule ^places/profile/([a-zA-Z0-9\-]+)/?$ places/profile/index.php?slug=$1 [NC,L]

# Clean SEO-friendly place URLs (main rule)
RewriteRule ^places/([a-zA-Z0-9\-]+)/?$ places/profile/index.php?slug=$1 [NC,L]

# ===== SEO-FRIENDLY URL ROUTING =====



# Account Management Routes - Use SEO-friendly directory structure
RewriteRule ^account/login/?$ account/login/index.php [NC,L]
RewriteRule ^account/register/?$ account/register/index.php [NC,L]
RewriteRule ^account/logout/?$ account/logout/index.php [NC,L]

# Short URL alternatives
RewriteRule ^login/?$ login/index.php [NC,L]
RewriteRule ^register/?$ register/index.php [NC,L]

# Profile & User Management Routes
RewriteRule ^profile/edit/?$ user/profile.php [NC,L]
RewriteRule ^profile/preferences/?$ user/preferences.php [NC,L]
RewriteRule ^profile/notifications/?$ user/notifications.php [NC,L]
RewriteRule ^profile/messages/?$ user/messages.php [NC,L]
RewriteRule ^profile/photos/?$ user/photos.php [NC,L]
RewriteRule ^profile/lists/?$ user/lists.php [NC,L]
RewriteRule ^profile/badges/?$ user/badges.php [NC,L]
RewriteRule ^profile/statistics/?$ user/statistics.php [NC,L]

# Beer Discovery Routes
RewriteRule ^beers/?$ beers/discover.php [NC,L]
RewriteRule ^beers/discover/?$ beers/discover.php [NC,L]
RewriteRule ^beers/trending/?$ beers/trending.php [NC,L]
RewriteRule ^beers/styles/?$ beers/styles.php [NC,L]
RewriteRule ^beers/rate/?$ beers/rate.php [NC,L]
RewriteRule ^beers/reviews/?$ beers/reviews.php [NC,L]
RewriteRule ^beers/recommendations/?$ discover/recommendations.php [NC,L]

# Individual Beer Pages (dynamic)
RewriteRule ^beers/([a-zA-Z0-9\-]+)/?$ beers/detail.php?slug=$1 [NC,L]

# Brewery Discovery Routes
RewriteRule ^breweries/?$ breweries/listing.php [NC,L]
RewriteRule ^breweries/discover/?$ breweries/listing.php [NC,L]
RewriteRule ^breweries/map/?$ location/brewery-map.php [NC,L]
RewriteRule ^breweries/manage/?$ brewery/profile.php [NC,L]

# Individual Brewery Pages (dynamic)
RewriteRule ^breweries/([a-zA-Z0-9\-]+)/?$ breweries/detail.php?slug=$1 [NC,L]



# Social Features Routes
RewriteRule ^social/?$ social/feed.php [NC,L]
RewriteRule ^social/feed/?$ social/feed.php [NC,L]
RewriteRule ^social/checkin/?$ social/checkin.php [NC,L]
RewriteRule ^social/discover-users/?$ social/discover-users.php [NC,L]

# Search Routes
RewriteRule ^search/?$ search/index.php [NC,L]

# Admin Routes
RewriteRule ^admin/?$ admin/dashboard.php [NC,L]
RewriteRule ^admin/dashboard/?$ admin/dashboard.php [NC,L]
RewriteRule ^admin/breweries/?$ admin/breweries.php [NC,L]
RewriteRule ^admin/analytics/?$ admin/analytics.php [NC,L]
RewriteRule ^admin/import/?$ admin/brewery-import.php [NC,L]

# ===== 301 REDIRECTS FROM OLD URLS =====

# Old auth URLs
RewriteRule ^auth/login\.php$ /account/login/ [R=301,L]
RewriteRule ^auth/register\.php$ /account/register/ [R=301,L]
RewriteRule ^auth/logout\.php$ /account/logout/ [R=301,L]

# Old user URLs
RewriteRule ^user/profile\.php$ /profile/edit/ [R=301,L]
RewriteRule ^user/preferences\.php$ /profile/preferences/ [R=301,L]
RewriteRule ^user/notifications\.php$ /profile/notifications/ [R=301,L]
RewriteRule ^user/messages\.php$ /profile/messages/ [R=301,L]
RewriteRule ^user/photos\.php$ /profile/photos/ [R=301,L]
RewriteRule ^user/lists\.php$ /profile/lists/ [R=301,L]
RewriteRule ^user/badges\.php$ /profile/badges/ [R=301,L]
RewriteRule ^user/statistics\.php$ /profile/statistics/ [R=301,L]

# Old brewery URLs
RewriteRule ^breweries/listing\.php$ /breweries/discover/ [R=301,L]
RewriteRule ^brewery/profile\.php$ /breweries/manage/ [R=301,L]
RewriteRule ^location/brewery-map\.php$ /breweries/map/ [R=301,L]

# Old places URLs - redirect to new slug-based URLs
RewriteCond %{QUERY_STRING} ^id=([0-9]+)$
RewriteRule ^places/profile\.php$ places/redirect.php?id=%1 [L]
RewriteRule ^places/profile/([0-9]+)/?$ places/redirect.php?id=$1 [L]
RewriteRule ^places/search\.php$ /places/search/ [R=301,L]

# Old beer URLs
RewriteRule ^beers/discover\.php$ /beers/discover/ [R=301,L]
RewriteRule ^beers/rate\.php$ /beers/rate/ [R=301,L]
RewriteRule ^discover/recommendations\.php$ /beers/recommendations/ [R=301,L]

# Old social URLs
RewriteRule ^social/feed\.php$ /social/feed/ [R=301,L]
RewriteRule ^social/checkin\.php$ /social/checkin/ [R=301,L]
RewriteRule ^social/discover-users\.php$ /social/discover-users/ [R=301,L]

# Old search URLs
RewriteRule ^search/index\.php$ /search/ [R=301,L]

# Old admin URLs
RewriteRule ^admin/dashboard\.php$ /admin/dashboard/ [R=301,L]
RewriteRule ^admin/breweries\.php$ /admin/breweries/ [R=301,L]
RewriteRule ^admin/analytics\.php$ /admin/analytics/ [R=301,L]
RewriteRule ^admin/brewery-import\.php$ /admin/import/ [R=301,L]

# API Routes (maintain /api/ structure)
RewriteRule ^api/(.*)$ api/$1 [NC,L]

# Fallback for remaining PHP files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^([^/]+)/?$ $1.php [L,QSA]

# Handle directory-based routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/([^/]+)/?$ $1/$2.php [L,QSA]

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self';"
</IfModule>

# Protect sensitive files
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect config directory
<Files "config/*">
    Order Allow,Deny
    Deny from all
</Files>

# Protect database directory
<Files "database/*">
    Order Allow,Deny
    Deny from all
</Files>

# Protect .env files
<Files ".env*">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent execution of PHP files in uploads
<Files "uploads/*.php">
    Order Allow,Deny
    Deny from all
</Files>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Error pages (optional)
ErrorDocument 404 /error/404.php
ErrorDocument 500 /error/500.php

# PHP Configuration (if allowed)
<IfModule mod_php.c>
    # Hide PHP version
    php_flag expose_php Off
    
    # Session security
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_strict_mode 1
    
    # File upload limits
    php_value upload_max_filesize 5M
    php_value post_max_size 10M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    
    # Error reporting (disable in production)
    php_flag display_errors On
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>
