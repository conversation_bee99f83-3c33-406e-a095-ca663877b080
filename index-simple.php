<?php
/**
 * Simplified Homepage - Based on Layout Reference
 * Beersty Platform
 */

$pageTitle = APP_NAME . ' - Discover Amazing Beers & Breweries';
$additionalCSS = ['/assets/css/home-simple.css'];

require_once 'config/config.php';

// Get basic stats
$stats = [
    'total_breweries' => 89,
    'total_beers' => 456,
    'total_users' => 1250,
    'total_checkins' => 3200
];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get actual stats from database
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM breweries WHERE status = 'active'");
    $stmt->execute();
    $stats['total_breweries'] = $stmt->fetchColumn();
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu WHERE available = 1");
    $stmt->execute();
    $stats['total_beers'] = $stmt->fetchColumn();
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $stmt->execute();
    $stats['total_users'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    error_log("Error fetching stats: " . $e->getMessage());
}

require_once 'includes/header.php';
?>

<!-- Hero Banner Section -->
<div class="hero-banner">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    Welcome to Beersty 🍺
                </h1>
                <p class="lead mb-4">
                    <?php if (!isLoggedIn()): ?>
                        Discover amazing breweries, connect with fellow beer enthusiasts, and share your beer journey.
                    <?php else: ?>
                        <?php $user = getCurrentUser(); ?>
                        Welcome back, <?php echo htmlspecialchars($user['first_name'] ?: 'Beer Lover'); ?>! Ready to discover your next favorite beer?
                    <?php endif; ?>
                </p>
                
                <!-- Quick Actions -->
                <div class="d-flex flex-wrap gap-3">
                    <?php if (!isLoggedIn()): ?>
                        <a href="/account/register/" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Join Now
                        </a>
                        <a href="/account/login/" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    <?php else: ?>
                        <a href="/social/checkin/" class="btn btn-primary btn-lg">
                            <i class="fas fa-check-circle me-2"></i>Check In
                        </a>
                        <a href="/social/feed/" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-stream me-2"></i>Activity Feed
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <div class="hero-icon">
                    <i class="fas fa-beer fa-5x text-warning"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats Section -->
<div class="stats-section bg-light py-4">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_breweries']); ?></div>
                    <div class="stat-label">Breweries</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_beers']); ?></div>
                    <div class="stat-label">Beers</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>
                    <div class="stat-label">Members</div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_checkins']); ?></div>
                    <div class="stat-label">Check-ins</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Section -->
<div class="main-content py-5">
    <div class="container">
        <div class="row">
            <!-- Left Column - Main Content -->
            <div class="col-lg-8">
                <div class="row g-4">
                    <!-- Discover Breweries -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-map-marker-alt fa-3x text-primary"></i>
                            </div>
                            <h4>Discover Breweries</h4>
                            <p>Find amazing local breweries and hidden gems in your area.</p>
                            <a href="/breweries/discover/" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Explore Now
                            </a>
                        </div>
                    </div>
                    
                    <!-- Rate & Review -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-star fa-3x text-warning"></i>
                            </div>
                            <h4>Rate & Review</h4>
                            <p>Share your beer experiences and help others discover great beers.</p>
                            <a href="/beers/discover/" class="btn btn-warning">
                                <i class="fas fa-beer me-2"></i>Find Beers
                            </a>
                        </div>
                    </div>
                    
                    <!-- Social Features -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-users fa-3x text-success"></i>
                            </div>
                            <h4>Connect & Share</h4>
                            <p>Follow friends and share your beer journey with the community.</p>
                            <?php if (isLoggedIn()): ?>
                                <a href="/social/feed/" class="btn btn-success">
                                    <i class="fas fa-stream me-2"></i>Activity Feed
                                </a>
                            <?php else: ?>
                                <a href="/account/register/" class="btn btn-success">
                                    <i class="fas fa-user-plus me-2"></i>Join Community
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Check-in Feature -->
                    <div class="col-md-6">
                        <div class="feature-card h-100">
                            <div class="feature-icon">
                                <i class="fas fa-check-circle fa-3x text-info"></i>
                            </div>
                            <h4>Check-in Anywhere</h4>
                            <p>Share what you're drinking with photos, ratings, and location.</p>
                            <?php if (isLoggedIn()): ?>
                                <a href="/social/checkin/" class="btn btn-info">
                                    <i class="fas fa-check-circle me-2"></i>Check In Now
                                </a>
                            <?php else: ?>
                                <a href="/account/login/" class="btn btn-info">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In to Check-in
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column - Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Activity -->
                <div class="sidebar-card mb-4">
                    <h5 class="card-title">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        Recent Activity
                    </h5>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">🍺</div>
                            <div class="activity-text">
                                <strong>Alex M.</strong> checked in at Local Brewery
                                <small class="text-muted d-block">2 minutes ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">⭐</div>
                            <div class="activity-text">
                                <strong>Sarah K.</strong> rated Hazy IPA 5 stars
                                <small class="text-muted d-block">5 minutes ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📸</div>
                            <div class="activity-text">
                                <strong>Mike R.</strong> shared a photo
                                <small class="text-muted d-block">10 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <?php if (isLoggedIn()): ?>
                        <a href="/social/feed/" class="btn btn-outline-primary btn-sm w-100 mt-3">
                            View All Activity
                        </a>
                    <?php endif; ?>
                </div>
                
                <!-- Popular Beers -->
                <div class="sidebar-card">
                    <h5 class="card-title">
                        <i class="fas fa-fire me-2 text-danger"></i>
                        Popular This Week
                    </h5>
                    <div class="popular-list">
                        <div class="popular-item">
                            <div class="popular-rank">1</div>
                            <div class="popular-info">
                                <div class="popular-name">Hazy IPA Supreme</div>
                                <div class="popular-brewery">Craft Masters</div>
                                <div class="popular-rating">⭐⭐⭐⭐⭐ 4.8</div>
                            </div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">2</div>
                            <div class="popular-info">
                                <div class="popular-name">Imperial Stout</div>
                                <div class="popular-brewery">Dark Arts Brewing</div>
                                <div class="popular-rating">⭐⭐⭐⭐ 4.6</div>
                            </div>
                        </div>
                        <div class="popular-item">
                            <div class="popular-rank">3</div>
                            <div class="popular-info">
                                <div class="popular-name">Wheat Wonder</div>
                                <div class="popular-brewery">Sunshine Brewery</div>
                                <div class="popular-rating">⭐⭐⭐⭐ 4.5</div>
                            </div>
                        </div>
                    </div>
                    <a href="/beers/trending/" class="btn btn-outline-danger btn-sm w-100 mt-3">
                        View Trending Beers
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="cta-section bg-primary text-white py-5">
    <div class="container text-center">
        <h2 class="mb-3">Ready to Start Your Beer Journey?</h2>
        <p class="lead mb-4">Join thousands of beer enthusiasts discovering amazing breweries and sharing their experiences.</p>
        
        <?php if (!isLoggedIn()): ?>
            <div class="d-flex justify-content-center gap-3">
                <a href="/account/register/" class="btn btn-light btn-lg">
                    <i class="fas fa-rocket me-2"></i>Get Started Today
                </a>
                <a href="/breweries/discover/" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-search me-2"></i>Browse Breweries
                </a>
            </div>
        <?php else: ?>
            <a href="/social/checkin/" class="btn btn-light btn-lg">
                <i class="fas fa-check-circle me-2"></i>Make Your First Check-in
            </a>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
