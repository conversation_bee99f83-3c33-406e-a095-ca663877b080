# Beersty Cleanup and Setup Script
# Removes any remaining references to old system and sets up clean PHP application

param(
    [string]$XAMPPPath = "C:\xampp"
)

Write-Host "=== Beersty Cleanup and Setup ===" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "⚠ Warning: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "Some operations may fail. Consider running as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# Remove any remaining Node.js/React files
Write-Host "Cleaning up old files..." -ForegroundColor Yellow

$filesToRemove = @(
    "package.json",
    "package-lock.json", 
    "yarn.lock",
    "bun.lockb",
    "tsconfig.json",
    "tsconfig.app.json", 
    "tsconfig.node.json",
    "vite.config.ts",
    "tailwind.config.ts",
    "postcss.config.js",
    "eslint.config.js",
    "components.json"
)

foreach ($file in $filesToRemove) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "✓ Removed $file" -ForegroundColor Green
    }
}

$dirsToRemove = @("src", "node_modules", "dist", ".next", "build")
foreach ($dir in $dirsToRemove) {
    if (Test-Path $dir) {
        Remove-Item $dir -Recurse -Force
        Write-Host "✓ Removed directory $dir" -ForegroundColor Green
    }
}

# Ensure required directories exist
Write-Host "Creating required directories..." -ForegroundColor Yellow

$requiredDirs = @("uploads", "config", "assets/css", "assets/js", "assets/images")
foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory $dir" -ForegroundColor Green
    }
}

# Check XAMPP installation
Write-Host "Checking XAMPP installation..." -ForegroundColor Yellow

if (Test-Path "$XAMPPPath\xampp-control.exe") {
    Write-Host "✓ XAMPP found at $XAMPPPath" -ForegroundColor Green
    
    # Copy files to XAMPP if not already there
    $targetPath = "$XAMPPPath\htdocs\beersty"
    
    if (-not (Test-Path "$targetPath\index.php")) {
        Write-Host "Copying files to XAMPP..." -ForegroundColor Yellow
        
        if (-not (Test-Path $targetPath)) {
            New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
        }
        
        # Copy all PHP files and directories
        $itemsToCopy = @(
            "*.php", "*.html", "*.md", "*.bat", "*.ps1", "*.sql",
            "config", "includes", "assets", "uploads", "database",
            "auth", "admin", "brewery", "breweries", ".htaccess"
        )
        
        foreach ($item in $itemsToCopy) {
            if (Test-Path $item) {
                Copy-Item -Path $item -Destination $targetPath -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
        
        Write-Host "✓ Files copied to $targetPath" -ForegroundColor Green
    } else {
        Write-Host "✓ Files already in XAMPP directory" -ForegroundColor Green
    }
    
} else {
    Write-Host "✗ XAMPP not found at $XAMPPPath" -ForegroundColor Red
    Write-Host "Please install XAMPP first:" -ForegroundColor Yellow
    Write-Host "  Download: https://www.apachefriends.org/download.html" -ForegroundColor White
    Write-Host "  Install to: $XAMPPPath" -ForegroundColor White
    Write-Host ""
    Write-Host "Then run this script again." -ForegroundColor Yellow
    exit 1
}

# Set file permissions
Write-Host "Setting file permissions..." -ForegroundColor Yellow

try {
    $uploadsPath = "$XAMPPPath\htdocs\beersty\uploads"
    if (Test-Path $uploadsPath) {
        icacls $uploadsPath /grant "Everyone:(OI)(CI)F" /T | Out-Null
        Write-Host "✓ Upload directory permissions set" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not set permissions: $_" -ForegroundColor Yellow
}

# Check services
Write-Host "Checking XAMPP services..." -ForegroundColor Yellow

$apacheRunning = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
$mysqlRunning = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue

if ($apacheRunning) {
    Write-Host "✓ Apache is running" -ForegroundColor Green
} else {
    Write-Host "⚠ Apache is not running" -ForegroundColor Yellow
    Write-Host "  Start it in XAMPP Control Panel" -ForegroundColor White
}

if ($mysqlRunning) {
    Write-Host "✓ MySQL is running" -ForegroundColor Green
} else {
    Write-Host "⚠ MySQL is not running" -ForegroundColor Yellow
    Write-Host "  Start it in XAMPP Control Panel" -ForegroundColor White
}

Write-Host ""
Write-Host "=== Cleanup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start Apache and MySQL in XAMPP Control Panel" -ForegroundColor White
Write-Host "2. Run database setup: .\setup-database.ps1" -ForegroundColor White
Write-Host "3. Access application: http://localhost/beersty/" -ForegroundColor White
Write-Host ""
Write-Host "Default Login:" -ForegroundColor Cyan
Write-Host "  Email: <EMAIL>" -ForegroundColor White
Write-Host "  Password: admin123" -ForegroundColor White
Write-Host ""

# Offer to open XAMPP Control Panel
$openXAMPP = Read-Host "Open XAMPP Control Panel now? (y/n)"
if ($openXAMPP -eq "y" -or $openXAMPP -eq "Y") {
    if (Test-Path "$XAMPPPath\xampp-control.exe") {
        Start-Process "$XAMPPPath\xampp-control.exe"
        Write-Host "✓ XAMPP Control Panel opened" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Happy brewing! 🍺" -ForegroundColor Green
