# Import Brewery Data
# PowerShell script to import Michigan brewery data into MySQL

param(
    [string]$ServerUrl = "http://localhost:8000"
)

Write-Host "🍺 Beersty Brewery Data Import" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Set working directory
$ProjectPath = "C:\xkinteractive-github\beersty-lovable"
Set-Location $ProjectPath

Write-Host "📁 Project Directory: $ProjectPath" -ForegroundColor Cyan
Write-Host "🌐 Server URL: $ServerUrl" -ForegroundColor Cyan

# Check if CSV files exist
$michiganCsv = "csv-data\michigan_breweries.csv"
$usCsv = "csv-data\us_breweries.csv"

Write-Host "`n📋 Checking CSV Files..." -ForegroundColor Yellow

if (Test-Path $michiganCsv) {
    $michiganSize = (Get-Item $michiganCsv).Length
    Write-Host "✅ Michigan breweries CSV found ($([math]::Round($michiganSize/1KB, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "❌ Michigan breweries CSV not found: $michiganCsv" -ForegroundColor Red
}

if (Test-Path $usCsv) {
    $usSize = (Get-Item $usCsv).Length
    Write-Host "✅ US breweries CSV found ($([math]::Round($usSize/1KB, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "❌ US breweries CSV not found: $usCsv" -ForegroundColor Red
}

# Test server connection
Write-Host "`n🌐 Testing Server Connection..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $ServerUrl -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Server is running and accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Server responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Cannot connect to server: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please make sure the PHP server is running:" -ForegroundColor Yellow
    Write-Host "   .\start-server.ps1" -ForegroundColor Cyan
    exit 1
}

# Test database connection
Write-Host "`n🗄️ Testing Database Connection..." -ForegroundColor Yellow
try {
    $dbTestUrl = "$ServerUrl/test-database-connection.php"
    $dbResponse = Invoke-WebRequest -Uri $dbTestUrl -UseBasicParsing -TimeoutSec 10
    
    if ($dbResponse.Content -like "*MySQL server connection successful*") {
        Write-Host "✅ MySQL database connection successful" -ForegroundColor Green
    } elseif ($dbResponse.Content -like "*Database connection failed*") {
        Write-Host "❌ Database connection failed" -ForegroundColor Red
        Write-Host "Please run the database setup:" -ForegroundColor Yellow
        Write-Host "   Open: $ServerUrl/fix-mysql-connection.php" -ForegroundColor Cyan
        exit 1
    } else {
        Write-Host "⚠️ Database status unclear" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Cannot test database: $($_.Exception.Message)" -ForegroundColor Red
}

# Import Michigan breweries
if (Test-Path $michiganCsv) {
    Write-Host "`n🍺 Importing Michigan Breweries..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Cyan
    
    try {
        $importUrl = "$ServerUrl/import-michigan-breweries.php"
        $importResponse = Invoke-WebRequest -Uri $importUrl -UseBasicParsing -TimeoutSec 120
        
        if ($importResponse.Content -like "*Import Complete*") {
            Write-Host "✅ Michigan breweries imported successfully!" -ForegroundColor Green
            
            # Extract statistics from response
            if ($importResponse.Content -match "Inserted:\s*(\d+)") {
                $inserted = $matches[1]
                Write-Host "   📊 Breweries inserted: $inserted" -ForegroundColor Cyan
            }
            if ($importResponse.Content -match "Updated:\s*(\d+)") {
                $updated = $matches[1]
                Write-Host "   📊 Breweries updated: $updated" -ForegroundColor Cyan
            }
        } else {
            Write-Host "⚠️ Import completed but may have issues" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ Import failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You can try manually at: $importUrl" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n❌ Cannot import: Michigan CSV file not found" -ForegroundColor Red
}

# Ask about US breweries
if (Test-Path $usCsv) {
    Write-Host "`n🇺🇸 US Breweries Available" -ForegroundColor Yellow
    Write-Host "The US breweries CSV contains 8,104 breweries from all 50 states." -ForegroundColor Cyan
    Write-Host "This import will take significantly longer (5-10 minutes)." -ForegroundColor Cyan
    
    $response = Read-Host "`nDo you want to import US breweries too? (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        Write-Host "`n🇺🇸 Importing US Breweries..." -ForegroundColor Yellow
        Write-Host "This will take several minutes. Please be patient..." -ForegroundColor Cyan
        
        try {
            # Use the CSV import page for US data
            $usImportUrl = "$ServerUrl/admin/csv-import.php"
            Write-Host "Please manually import US data at: $usImportUrl" -ForegroundColor Yellow
            Write-Host "1. Login as admin (<EMAIL> / admin123)" -ForegroundColor Cyan
            Write-Host "2. Click 'Import' button for US Breweries" -ForegroundColor Cyan
        }
        catch {
            Write-Host "❌ US import setup failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n🎉 Import Process Complete!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 Next Steps:" -ForegroundColor Cyan
Write-Host "   🏠 Visit Homepage:    $ServerUrl/" -ForegroundColor White
Write-Host "   🔐 Login as Admin:    $ServerUrl/auth/login.php" -ForegroundColor White
Write-Host "   🍺 Browse Breweries:  $ServerUrl/breweries/listing.php" -ForegroundColor White
Write-Host "   ⚙️  Admin Dashboard:   $ServerUrl/admin/dashboard.php" -ForegroundColor White
Write-Host ""
Write-Host "🔐 Admin Credentials:" -ForegroundColor Cyan
Write-Host "   📧 Email:    <EMAIL>" -ForegroundColor White
Write-Host "   🔑 Password: admin123" -ForegroundColor White
Write-Host ""

# Open browser to homepage
$openBrowser = Read-Host "Open homepage in browser? (Y/n)"
if ($openBrowser -ne "n" -and $openBrowser -ne "N") {
    try {
        Start-Process $ServerUrl
        Write-Host "✅ Browser opened" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Could not open browser automatically" -ForegroundColor Yellow
    }
}
