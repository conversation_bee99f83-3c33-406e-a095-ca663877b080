<?php
// TEST ADMIN BYPASS - Temporary test to bypass authentication

session_start();

// Set admin session temporarily for testing
$_SESSION['user_id'] = 1;
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_role'] = 'admin';
$_SESSION['initialized'] = true;

echo "<h1>Admin Session Set</h1>";
echo "<p>Temporary admin session created for testing.</p>";
echo "<p>Session data:</p>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Test Links:</h2>";
echo "<a href='admin/user-management.php'>User Management</a><br>";
echo "<a href='test-pdo-simple.php'>PDO Test</a><br>";

// Test database connection
echo "<h2>Database Test:</h2>";
try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test users table
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Users table exists</p>";
        
        // Count users
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo "<p>Users in database: <strong>" . $result['count'] . "</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Users table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
a { color: #007cba; text-decoration: none; padding: 5px 10px; background: #f0f0f0; margin: 5px; display: inline-block; }
a:hover { background: #e0e0e0; }
</style>
