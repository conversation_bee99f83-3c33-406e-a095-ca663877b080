@echo off
REM Beersty Development Server Starter
REM Simple batch file to start the Beersty brewery management system

title Beersty Development Server

echo.
echo ===============================================
echo 🍺 Beersty Brewery Management System
echo ===============================================
echo.

REM Change to project directory
cd /d "C:\xkinteractive-github\beersty-lovable"

echo 📁 Project Directory: %CD%
echo 🌐 Server Port: 8000
echo.

REM Check if PHP is available
php -v >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP is not installed or not in PATH
    echo Please install PHP and add it to your PATH
    pause
    exit /b 1
)

echo ✅ PHP is available
echo.

REM Display server information
echo 🚀 Starting PHP Development Server...
echo ✅ Server will run at: http://localhost:8000
echo.
echo 🔗 Quick Links:
echo    🏠 Homepage:     http://localhost:8000/
echo    🔐 Login:        http://localhost:8000/auth/login.php
echo    ⚙️  Admin:        http://localhost:8000/admin/dashboard.php
echo    🍺 Breweries:    http://localhost:8000/breweries/listing.php
echo    📊 CSV Import:   http://localhost:8000/admin/csv-import.php
echo.
echo 🔐 Admin Credentials:
echo    📧 Email:    <EMAIL>
echo    🔑 Password: admin123
echo.
echo 📝 Notes:
echo    • Press Ctrl+C to stop the server
echo    • MySQL database: beersty_db
echo    • 376 Michigan breweries available
echo.

REM Start the PHP server
echo Starting server...
php -S localhost:8000

REM If we get here, the server stopped
echo.
echo Server stopped.
pause
