<?php
/**
 * Test Session Functionality
 * Check if sessions are working properly
 */

echo "<h1>🔧 Session Test</h1>";

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
    echo "<p>✅ Session started</p>";
} else {
    echo "<p>✅ Session already active</p>";
}

echo "<h2>📊 Session Information</h2>";
echo "<ul>";
echo "<li><strong>Session ID:</strong> " . session_id() . "</li>";
echo "<li><strong>Session Status:</strong> " . session_status() . " (1=disabled, 2=active, 3=none)</li>";
echo "<li><strong>Session Name:</strong> " . session_name() . "</li>";
echo "<li><strong>Session Save Path:</strong> " . session_save_path() . "</li>";
echo "</ul>";

echo "<h2>🔐 Current Session Data</h2>";
if (empty($_SESSION)) {
    echo "<p>❌ No session data found</p>";
} else {
    echo "<ul>";
    foreach ($_SESSION as $key => $value) {
        echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>";
    }
    echo "</ul>";
}

echo "<h2>🧪 Test Session Write/Read</h2>";
$_SESSION['test_key'] = 'test_value_' . time();
echo "<p>✅ Set test session variable: test_key = " . $_SESSION['test_key'] . "</p>";

if (isset($_SESSION['test_key'])) {
    echo "<p>✅ Session read successful: " . $_SESSION['test_key'] . "</p>";
} else {
    echo "<p>❌ Session read failed</p>";
}

echo "<h2>🔍 Login Status Check</h2>";

// Include config to test login functions
require_once 'config/config.php';

if (function_exists('isLoggedIn')) {
    if (isLoggedIn()) {
        echo "<p>✅ User is logged in</p>";
        
        if (function_exists('getCurrentUser')) {
            $user = getCurrentUser();
            if ($user) {
                echo "<ul>";
                echo "<li><strong>User ID:</strong> " . htmlspecialchars($user['id']) . "</li>";
                echo "<li><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</li>";
                echo "<li><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</li>";
                echo "</ul>";
            } else {
                echo "<p>❌ getCurrentUser() returned null</p>";
            }
        }
    } else {
        echo "<p>❌ User is not logged in</p>";
        
        echo "<h3>Required Session Variables for Login:</h3>";
        $requiredVars = ['user_id', 'user_email', 'user_role'];
        foreach ($requiredVars as $var) {
            $exists = isset($_SESSION[$var]) && !empty($_SESSION[$var]);
            echo "<li><strong>$var:</strong> " . ($exists ? '✅ Present' : '❌ Missing') . "</li>";
        }
    }
} else {
    echo "<p>❌ isLoggedIn() function not found</p>";
}

echo "<h2>🔧 Manual Login Test</h2>";
echo "<p>Let's manually set session variables and test:</p>";

// Manually set login session
$_SESSION['user_id'] = 'test_user_id';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_role'] = 'admin';

echo "<p>✅ Manually set session variables</p>";

if (isLoggedIn()) {
    echo "<p>✅ isLoggedIn() now returns true!</p>";
} else {
    echo "<p>❌ isLoggedIn() still returns false</p>";
}

// Clear test session
unset($_SESSION['user_id'], $_SESSION['user_email'], $_SESSION['user_role']);
echo "<p>🧹 Cleared test session variables</p>";

echo "<h2>🔗 Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='login-debug-live.php' class='btn btn-primary'>Test Live Login</a></li>";
echo "<li><a href='auth/login.php' class='btn btn-info'>Go to Login Page</a></li>";
echo "<li><a href='check-database-structure.php' class='btn btn-secondary'>Check Database</a></li>";
echo "</ul>";

// Clean up test session variable
unset($_SESSION['test_key']);
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
