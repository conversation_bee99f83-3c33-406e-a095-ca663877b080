<?php
/**
 * Fix MySQL Connection Issues
 * Diagnose and fix MySQL database connection problems
 */

echo "<h1>🐬 Fix MySQL Connection</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>📋 MySQL Extension Check</h2>";
echo "<ul>";
echo "<li><strong>PDO:</strong> " . (extension_loaded('pdo') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ Available' : '❌ Not available') . "</li>";
echo "</ul>";

if (!extension_loaded('pdo_mysql')) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ PDO MySQL Extension Missing</h3>";
    echo "<p>You need to enable the PDO MySQL extension in PHP.</p>";
    echo "<p>Check your php.ini file and uncomment: <code>extension=pdo_mysql</code></p>";
    echo "</div>";
    exit;
}

echo "<h2>🔧 Testing MySQL Connection Parameters</h2>";

$configs = [
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3307, 'user' => 'root', 'pass' => ''], // XAMPP alternative port
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'beersty', 'pass' => 'beersty'],
];

$workingConfig = null;

foreach ($configs as $config) {
    echo "<h3>Testing: {$config['host']}:{$config['port']} with user '{$config['user']}'</h3>";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $conn = new PDO($dsn, $config['user'], $config['pass']);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ Connection successful!</p>";
        $workingConfig = $config;
        break;
        
    } catch (PDOException $e) {
        echo "<p>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

if ($workingConfig) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ Found Working MySQL Configuration!</h3>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> {$workingConfig['host']}</li>";
    echo "<li><strong>Port:</strong> {$workingConfig['port']}</li>";
    echo "<li><strong>Username:</strong> {$workingConfig['user']}</li>";
    echo "<li><strong>Password:</strong> " . (empty($workingConfig['pass']) ? 'Empty' : '[Set]') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🗄️ Setting Up Database</h2>";
    
    try {
        $dsn = "mysql:host={$workingConfig['host']};port={$workingConfig['port']};charset=utf8mb4";
        $conn = new PDO($dsn, $workingConfig['user'], $workingConfig['pass']);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $dbName = 'beersty_db';
        $conn->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✅ Database '$dbName' created/verified</p>";
        
        // Connect to the specific database
        $dsn = "mysql:host={$workingConfig['host']};port={$workingConfig['port']};dbname=$dbName;charset=utf8mb4";
        $dbConn = new PDO($dsn, $workingConfig['user'], $workingConfig['pass']);
        $dbConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ Connected to database '$dbName'</p>";
        
        // Create tables
        echo "<h3>Creating Tables...</h3>";
        
        $tables = [
            'users' => "
                CREATE TABLE IF NOT EXISTS users (
                    id VARCHAR(32) PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL
                )
            ",
            'profiles' => "
                CREATE TABLE IF NOT EXISTS profiles (
                    id VARCHAR(32) PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    role VARCHAR(50) DEFAULT 'customer',
                    brewery_id VARCHAR(32) NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
                )
            ",
            'breweries' => "
                CREATE TABLE IF NOT EXISTS breweries (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    address TEXT,
                    city VARCHAR(100),
                    state VARCHAR(50),
                    zip VARCHAR(20),
                    phone VARCHAR(50),
                    email VARCHAR(255),
                    website VARCHAR(255),
                    description TEXT,
                    brewery_type VARCHAR(50) DEFAULT 'micro',
                    verified BOOLEAN DEFAULT 0,
                    claimed BOOLEAN DEFAULT 0,
                    claimable BOOLEAN DEFAULT 1,
                    follower_count INT DEFAULT 0,
                    like_count INT DEFAULT 0,
                    external_id VARCHAR(100),
                    latitude DECIMAL(10, 8),
                    longitude DECIMAL(11, 8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            "
        ];
        
        foreach ($tables as $tableName => $sql) {
            $dbConn->exec($sql);
            echo "<p>✅ Table '$tableName' created/verified</p>";
        }
        
        // Create admin user
        echo "<h3>Creating Admin User...</h3>";
        
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        // Check if admin already exists
        $stmt = $dbConn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $existingUser = $stmt->fetch();
        
        if ($existingUser) {
            // Update existing admin
            $stmt = $dbConn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $stmt->execute([$passwordHash, '<EMAIL>']);
            
            $stmt = $dbConn->prepare("UPDATE profiles SET role = 'admin' WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            
            echo "<p>✅ Admin user updated</p>";
        } else {
            // Create new admin
            $stmt = $dbConn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
            
            $stmt = $dbConn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
            $stmt->execute([$userId, '<EMAIL>']);
            
            echo "<p>✅ Admin user created</p>";
        }
        
        // Update database configuration
        echo "<h3>Updating Database Configuration...</h3>";
        
        $configContent = "<?php
class Database {
    private \$host = '{$workingConfig['host']}';
    private \$db_name = 'beersty_db';
    private \$username = '{$workingConfig['user']}';
    private \$password = '{$workingConfig['pass']}';
    private \$port = {$workingConfig['port']};
    private \$conn;

    public function __construct() {
        // Load configuration from environment or config file
        if (file_exists(__DIR__ . '/.env')) {
            \$env = parse_ini_file(__DIR__ . '/.env');
            \$this->host = \$env['DB_HOST'] ?? \$this->host;
            \$this->db_name = \$env['DB_NAME'] ?? \$this->db_name;
            \$this->username = \$env['DB_USER'] ?? \$this->username;
            \$this->password = \$env['DB_PASSWORD'] ?? \$this->password;
            \$this->port = \$env['DB_PORT'] ?? \$this->port;
        }
    }

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";port=\" . \$this->port . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password);
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"Database connection failed: \" . \$exception->getMessage());
        }
        
        return \$this->conn;
    }

    public function testConnection() {
        try {
            \$conn = \$this->getConnection();
            return \$conn !== null;
        } catch (Exception \$e) {
            return false;
        }
    }
}
?>";
        
        file_put_contents('config/database.php', $configContent);
        echo "<p>✅ Database configuration updated</p>";
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 MySQL Setup Complete!</h3>";
        echo "<p>Your MySQL database is now properly configured and ready to use.</p>";
        echo "<ul>";
        echo "<li><strong>Database:</strong> beersty_db</li>";
        echo "<li><strong>Host:</strong> {$workingConfig['host']}:{$workingConfig['port']}</li>";
        echo "<li><strong>Admin email:</strong> <EMAIL></li>";
        echo "<li><strong>Admin password:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ Database Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ No Working MySQL Configuration Found</h3>";
    echo "<p>Please ensure MySQL server is running. Common solutions:</p>";
    echo "<ul>";
    echo "<li><strong>XAMPP:</strong> Start MySQL service in XAMPP Control Panel</li>";
    echo "<li><strong>WAMP:</strong> Start MySQL service in WAMP</li>";
    echo "<li><strong>Standalone MySQL:</strong> Start MySQL service</li>";
    echo "<li><strong>Check port:</strong> MySQL might be running on port 3307 instead of 3306</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>Try Login Now</a></li>";
echo "<li><a href='test-database-connection.php' class='btn btn-info'>Test Database Connection</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
