<?php
/**
 * Quick Login Debug Script
 * Check what's preventing login from working
 */

echo "<h1>🔍 Quick Login Debug</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. 📋 PHP Extensions Check</h2>";
echo "<ul>";
echo "<li><strong>PDO:</strong> " . (extension_loaded('pdo') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Not available') . "</li>";
echo "</ul>";

echo "<h2>2. 🗄️ Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connection successful!</p>";
    
    // Check if users table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    $userTableExists = $stmt->fetch();
    
    if ($userTableExists) {
        echo "<p>✅ Users table exists</p>";
        
        // Check if admin user exists
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $adminExists = $stmt->fetch()['count'] > 0;
        
        if ($adminExists) {
            echo "<p>✅ Admin user exists</p>";
            
            // Test password verification
            $stmt = $conn->prepare("SELECT password_hash FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            $user = $stmt->fetch();
            
            if ($user && password_verify('admin123', $user['password_hash'])) {
                echo "<p>✅ Admin password verification works</p>";
            } else {
                echo "<p>❌ Admin password verification failed</p>";
                echo "<p>🔧 <strong>Issue:</strong> Password hash is incorrect</p>";
            }
            
        } else {
            echo "<p>❌ Admin user does not exist</p>";
            echo "<p>🔧 <strong>Issue:</strong> No admin user in database</p>";
        }
        
    } else {
        echo "<p>❌ Users table does not exist</p>";
        echo "<p>🔧 <strong>Issue:</strong> Database tables not created</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>🔧 <strong>Issue:</strong> Database connection problem</p>";
}

echo "<h2>3. 🛠️ Quick Fix Options</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Choose Your Fix:</h3>";
echo "<ul>";
echo "<li><a href='setup-database.php?type=mysql' class='btn btn-primary'>🐬 Fix MySQL Database</a></li>";
echo "<li><a href='setup-database.php?type=sqlite' class='btn btn-success'>📁 Switch to SQLite (Easier)</a></li>";
echo "<li><a href='auth/login.php' class='btn btn-secondary'>🔄 Try Login Again</a></li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. 📝 Manual Test</h2>";
echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
echo "<h4>Test Login Manually:</h4>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label>Email: <input type='email' name='test_email' value='<EMAIL>' style='margin-left: 10px; padding: 5px;'></label>";
echo "</div>";
echo "<div style='margin-bottom: 10px;'>";
echo "<label>Password: <input type='password' name='test_password' value='admin123' style='margin-left: 10px; padding: 5px;'></label>";
echo "</div>";
echo "<button type='submit' name='test_login' style='padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px;'>Test Login</button>";
echo "</form>";

if (isset($_POST['test_login'])) {
    echo "<h3>🧪 Manual Login Test Result:</h3>";
    
    $email = $_POST['test_email'] ?? '';
    $password = $_POST['test_password'] ?? '';
    
    try {
        require_once 'config/database.php';
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.email = ?
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<p>✅ User found in database</p>";
            echo "<p><strong>User ID:</strong> " . htmlspecialchars($user['id']) . "</p>";
            echo "<p><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</p>";
            
            if (password_verify($password, $user['password_hash'])) {
                echo "<p>✅ Password verification successful!</p>";
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<strong>🎉 Login should work!</strong> If it's still not working, there might be a session or redirect issue.";
                echo "</div>";
            } else {
                echo "<p>❌ Password verification failed</p>";
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
                echo "<strong>🔧 Fix needed:</strong> Password hash is incorrect. Run database setup again.";
                echo "</div>";
            }
        } else {
            echo "<p>❌ User not found</p>";
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<strong>🔧 Fix needed:</strong> Admin user doesn't exist. Run database setup.";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-success { background-color: #28a745; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
