# Beersty Development Server Startup Script
# This script starts XAMPP services (Apache, MySQL) and opens the project

Write-Host "Starting Beersty Development Environment..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Yellow

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  Warning: Not running as Administrator. Some operations may fail." -ForegroundColor Red
    Write-Host "   Consider running PowerShell as Administrator for best results." -ForegroundColor Red
    Write-Host ""
}

# Common XAMPP installation paths
$xamppPaths = @(
    "C:\xampp",
    "C:\Program Files\XAMPP",
    "C:\Program Files (x86)\XAMPP",
    "D:\xampp",
    "E:\xampp"
)

# Find XAMPP installation
$xamppPath = $null
foreach ($path in $xamppPaths) {
    if (Test-Path $path) {
        $xamppPath = $path
        break
    }
}

if (-not $xamppPath) {
    Write-Host "❌ XAMPP not found in common locations." -ForegroundColor Red
    Write-Host "   Please install XAMPP or update the script with your XAMPP path." -ForegroundColor Red
    Write-Host "   Download from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found XAMPP at: $xamppPath" -ForegroundColor Green

# XAMPP executable paths
$xamppControl = Join-Path $xamppPath "xampp-control.exe"
$apacheExe = Join-Path $xamppPath "apache\bin\httpd.exe"
$mysqlExe = Join-Path $xamppPath "mysql\bin\mysqld.exe"

# Function to check if a service is running
function Test-ServiceRunning {
    param($ProcessName)
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue) -ne $null
}

# Function to start XAMPP services
function Start-XamppServices {
    Write-Host "🚀 Starting XAMPP services..." -ForegroundColor Cyan
    
    # Start XAMPP Control Panel
    if (Test-Path $xamppControl) {
        Write-Host "   Starting XAMPP Control Panel..." -ForegroundColor White
        Start-Process $xamppControl -WindowStyle Normal
        Start-Sleep 3
    }
    
    # Check if Apache is running
    if (Test-ServiceRunning "httpd") {
        Write-Host "✅ Apache is already running" -ForegroundColor Green
    } else {
        Write-Host "🔄 Starting Apache..." -ForegroundColor Yellow
        # Try to start Apache via XAMPP
        $apacheStart = Join-Path $xamppPath "apache_start.bat"
        if (Test-Path $apacheStart) {
            Start-Process $apacheStart -WindowStyle Hidden
        }
        Start-Sleep 5
        
        if (Test-ServiceRunning "httpd") {
            Write-Host "✅ Apache started successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Apache may not have started. Check XAMPP Control Panel." -ForegroundColor Yellow
        }
    }
    
    # Check if MySQL is running
    if (Test-ServiceRunning "mysqld") {
        Write-Host "✅ MySQL is already running" -ForegroundColor Green
    } else {
        Write-Host "🔄 Starting MySQL..." -ForegroundColor Yellow
        # Try to start MySQL via XAMPP
        $mysqlStart = Join-Path $xamppPath "mysql_start.bat"
        if (Test-Path $mysqlStart) {
            Start-Process $mysqlStart -WindowStyle Hidden
        }
        Start-Sleep 5
        
        if (Test-ServiceRunning "mysqld") {
            Write-Host "✅ MySQL started successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️  MySQL may not have started. Check XAMPP Control Panel." -ForegroundColor Yellow
        }
    }
}

# Function to test web server
function Test-WebServer {
    Write-Host "🌐 Testing web server..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Web server is responding" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ Web server is not responding" -ForegroundColor Red
        return $false
    }
    return $false
}

# Function to test MySQL connection
function Test-MySQLConnection {
    Write-Host "🗄️  Testing MySQL connection..." -ForegroundColor Cyan
    
    $mysqlClient = Join-Path $xamppPath "mysql\bin\mysql.exe"
    if (Test-Path $mysqlClient) {
        try {
            $result = & $mysqlClient -u root -e "SELECT 1;" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ MySQL connection successful" -ForegroundColor Green
                return $true
            } else {
                Write-Host "❌ MySQL connection failed" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "❌ MySQL connection test failed" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "❌ MySQL client not found" -ForegroundColor Red
        return $false
    }
}

# Function to open project URLs
function Open-ProjectUrls {
    Write-Host "🌍 Opening project URLs..." -ForegroundColor Cyan
    
    $urls = @(
        "http://localhost/beersty-lovable",
        "http://localhost/beersty-lovable/admin/user-management.php",
        "http://localhost/phpmyadmin"
    )
    
    foreach ($url in $urls) {
        Write-Host "   Opening: $url" -ForegroundColor White
        Start-Process $url
        Start-Sleep 1
    }
}

# Main execution
try {
    # Start XAMPP services
    Start-XamppServices
    
    # Wait a moment for services to fully start
    Write-Host "⏳ Waiting for services to initialize..." -ForegroundColor Yellow
    Start-Sleep 10
    
    # Test services
    $webOk = Test-WebServer
    $mysqlOk = Test-MySQLConnection
    
    if ($webOk -and $mysqlOk) {
        Write-Host ""
        Write-Host "🎉 All services are running successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Available URLs:" -ForegroundColor Cyan
        Write-Host "   🏠 Main Site: http://localhost/beersty-lovable" -ForegroundColor White
        Write-Host "   👤 User Management: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
        Write-Host "   🗄️  phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
        Write-Host "   🎛️  XAMPP Control: Already opened" -ForegroundColor White
        Write-Host ""
        
        $openUrls = Read-Host "Would you like to open these URLs in your browser? (y/n)"
        if ($openUrls -eq "y" -or $openUrls -eq "Y" -or $openUrls -eq "") {
            Open-ProjectUrls
        }
        
    } else {
        Write-Host ""
        Write-Host "⚠️  Some services may not be running properly." -ForegroundColor Yellow
        Write-Host "   Please check the XAMPP Control Panel for details." -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "🔧 Troubleshooting Tips:" -ForegroundColor Cyan
    Write-Host "   • If Apache won't start, check if port 80 is in use" -ForegroundColor White
    Write-Host "   • If MySQL won't start, check if port 3306 is in use" -ForegroundColor White
    Write-Host "   • Run 'netstat -an | findstr :80' to check port 80" -ForegroundColor White
    Write-Host "   • Run 'netstat -an | findstr :3306' to check port 3306" -ForegroundColor White
    Write-Host ""
    Write-Host "📝 To test ADD USER functionality:" -ForegroundColor Cyan
    Write-Host "   1. Go to: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
    Write-Host "   2. Click 'Add User' button" -ForegroundColor White
    Write-Host "   3. Fill in the form and submit" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ An error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
