#!/bin/bash

# Multi-User Setup Script for Beersty on Kali Linux
# This script sets up a shared database structure for multiple users

set -e

echo "🐧 Beersty Multi-User Setup for Kali Linux"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    print_error "This script should not be run as root. Run as a regular user."
    exit 1
fi

# Get current user and project directory
CURRENT_USER=$(whoami)
PROJECT_DIR=$(pwd)
SHARED_BASE="/opt/beersty-shared"
SHARED_DB_DIR="$SHARED_BASE/database"
SHARED_CONFIG_DIR="$SHARED_BASE/config"
SHARED_UPLOADS_DIR="$SHARED_BASE/uploads"
GROUP_NAME="beersty-users"

echo ""
print_info "Current User: $CURRENT_USER"
print_info "Project Directory: $PROJECT_DIR"
print_info "Shared Base Directory: $SHARED_BASE"

# Function to check if user is in group
user_in_group() {
    groups "$CURRENT_USER" | grep -q "$GROUP_NAME"
}

# Function to setup directories with sudo
setup_shared_directories() {
    echo ""
    echo "🔧 Setting up shared directories..."
    
    # Create shared directories
    sudo mkdir -p "$SHARED_DB_DIR" "$SHARED_CONFIG_DIR" "$SHARED_UPLOADS_DIR"
    print_status "Created shared directories"
    
    # Create group if it doesn't exist
    if ! getent group "$GROUP_NAME" > /dev/null 2>&1; then
        sudo groupadd "$GROUP_NAME"
        print_status "Created group: $GROUP_NAME"
    else
        print_info "Group $GROUP_NAME already exists"
    fi
    
    # Add current user to group
    sudo usermod -a -G "$GROUP_NAME" "$CURRENT_USER"
    print_status "Added $CURRENT_USER to $GROUP_NAME group"
    
    # Set ownership and permissions
    sudo chown -R root:"$GROUP_NAME" "$SHARED_BASE"
    sudo chmod -R 775 "$SHARED_BASE"
    sudo chmod -R g+s "$SHARED_BASE"
    print_status "Set permissions on shared directories"
    
    print_warning "You may need to log out and log back in for group changes to take effect"
}

# Function to create shared database
create_shared_database() {
    echo ""
    echo "🗄️ Creating shared database..."
    
    SHARED_DB_FILE="$SHARED_DB_DIR/beersty_shared.json"
    
    if [[ ! -f "$SHARED_DB_FILE" ]]; then
        # Create initial database structure
        cat > "$SHARED_DB_FILE" << 'EOF'
{
    "users": [
        {
            "id": "admin-shared-id",
            "email": "<EMAIL>",
            "password_hash": "$2y$10$9xp5YQMzoAuGvq8b3n/Y0eV9eEtX6UF8gKgSv5Fb1L/w8E5bIYZqu",
            "created_at": "2025-08-07 00:00:00",
            "updated_at": "2025-08-07 00:00:00",
            "email_verified": 1,
            "last_login": null
        }
    ],
    "profiles": [
        {
            "id": "admin-shared-id",
            "email": "<EMAIL>",
            "role": "admin",
            "first_name": "Shared",
            "last_name": "Admin",
            "brewery_id": null,
            "created_at": "2025-08-07 00:00:00",
            "updated_at": "2025-08-07 00:00:00"
        }
    ],
    "breweries": [],
    "metadata": {
        "created_by": "setup-script",
        "created_at": "2025-08-07 00:00:00",
        "version": "1.0",
        "last_modified": "2025-08-07 00:00:00"
    }
}
EOF
        
        chmod 664 "$SHARED_DB_FILE"
        print_status "Created shared database file"
    else
        print_info "Shared database file already exists"
    fi
}

# Function to link user project to shared database
link_to_shared_database() {
    echo ""
    echo "🔗 Linking project to shared database..."
    
    USER_DB_DIR="$PROJECT_DIR/database"
    
    # Backup existing database if it exists and is not a symlink
    if [[ -d "$USER_DB_DIR" && ! -L "$USER_DB_DIR" ]]; then
        BACKUP_DIR="${USER_DB_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
        mv "$USER_DB_DIR" "$BACKUP_DIR"
        print_status "Backed up existing database to: $BACKUP_DIR"
    fi
    
    # Remove existing symlink if it exists
    if [[ -L "$USER_DB_DIR" ]]; then
        rm "$USER_DB_DIR"
    fi
    
    # Create symlink to shared database
    ln -s "$SHARED_DB_DIR" "$USER_DB_DIR"
    print_status "Created symlink: $USER_DB_DIR -> $SHARED_DB_DIR"
    
    # Also link uploads directory
    USER_UPLOADS_DIR="$PROJECT_DIR/uploads"
    if [[ -d "$USER_UPLOADS_DIR" && ! -L "$USER_UPLOADS_DIR" ]]; then
        BACKUP_UPLOADS="${USER_UPLOADS_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
        mv "$USER_UPLOADS_DIR" "$BACKUP_UPLOADS"
        print_status "Backed up existing uploads to: $BACKUP_UPLOADS"
    fi
    
    if [[ -L "$USER_UPLOADS_DIR" ]]; then
        rm "$USER_UPLOADS_DIR"
    fi
    
    ln -s "$SHARED_UPLOADS_DIR" "$USER_UPLOADS_DIR"
    print_status "Created uploads symlink: $USER_UPLOADS_DIR -> $SHARED_UPLOADS_DIR"
}

# Function to test the setup
test_setup() {
    echo ""
    echo "🧪 Testing setup..."
    
    # Check if symlinks exist and point to correct locations
    if [[ -L "$PROJECT_DIR/database" && -L "$PROJECT_DIR/uploads" ]]; then
        print_status "Symlinks created successfully"
    else
        print_error "Symlinks not created properly"
        return 1
    fi
    
    # Check if shared database file exists and is readable
    if [[ -r "$SHARED_DB_DIR/beersty_shared.json" ]]; then
        print_status "Shared database is accessible"
    else
        print_error "Shared database is not accessible"
        return 1
    fi
    
    # Check group membership (may require re-login)
    if user_in_group; then
        print_status "User is in $GROUP_NAME group"
    else
        print_warning "User not yet in $GROUP_NAME group (may require re-login)"
    fi
}

# Main setup process
main() {
    echo ""
    echo "Starting multi-user setup process..."
    
    # Check if shared directories already exist
    if [[ -d "$SHARED_BASE" ]]; then
        print_info "Shared directories already exist"
        
        # Ask if user wants to link to existing setup
        read -p "Link this project to existing shared database? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            link_to_shared_database
            test_setup
        else
            print_info "Skipping setup"
            exit 0
        fi
    else
        # Full setup required
        print_info "Setting up new shared database structure"
        
        echo ""
        print_warning "This setup requires sudo privileges to:"
        print_warning "- Create directories in /opt/"
        print_warning "- Create a system group"
        print_warning "- Set file permissions"
        echo ""
        
        read -p "Continue with setup? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Setup cancelled"
            exit 0
        fi
        
        setup_shared_directories
        create_shared_database
        link_to_shared_database
        test_setup
    fi
    
    echo ""
    print_status "Multi-user setup complete!"
    echo ""
    echo "📋 Summary:"
    echo "  • Shared database: $SHARED_DB_DIR/beersty_shared.json"
    echo "  • Shared uploads: $SHARED_UPLOADS_DIR"
    echo "  • User group: $GROUP_NAME"
    echo "  • Login credentials: <EMAIL> / admin123"
    echo ""
    print_info "Other users can run this script from their project directories to link to the shared database"
    
    if ! user_in_group; then
        echo ""
        print_warning "IMPORTANT: You may need to log out and log back in for group permissions to take effect"
    fi
}

# Run main function
main "$@"
