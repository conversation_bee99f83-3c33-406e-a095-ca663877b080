<?php
require_once '../config/config.php';

$pageTitle = 'Discover Beers - ' . APP_NAME;
$additionalCSS = ['/assets/css/beer-discovery.css'];

// Get search parameters
$search = sanitizeInput($_GET['search'] ?? '');
$style = sanitizeInput($_GET['style'] ?? '');
$brewery = sanitizeInput($_GET['brewery'] ?? '');
$minAbv = $_GET['min_abv'] ?? '';
$maxAbv = $_GET['max_abv'] ?? '';
$minIbu = $_GET['min_ibu'] ?? '';
$maxIbu = $_GET['max_ibu'] ?? '';
$sortBy = $_GET['sort'] ?? 'name';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// Get beer styles for filter
$beerStyles = [];
$breweries = [];
$beers = [];
$totalBeers = 0;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get beer styles
    $stmt = $conn->prepare("SELECT * FROM beer_styles WHERE is_active = 1 ORDER BY category, name");
    $stmt->execute();
    $beerStyles = $stmt->fetchAll();
    
    // Get breweries
    $stmt = $conn->prepare("SELECT id, name FROM breweries ORDER BY name");
    $stmt->execute();
    $breweries = $stmt->fetchAll();
    
    // Build beer search query
    $whereConditions = ["bm.available = 1"];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(bm.name LIKE ? OR bm.description LIKE ? OR b.name LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($style)) {
        $whereConditions[] = "bm.beer_style_id = ?";
        $params[] = $style;
    }
    
    if (!empty($brewery)) {
        $whereConditions[] = "bm.brewery_id = ?";
        $params[] = $brewery;
    }
    
    if (!empty($minAbv) && is_numeric($minAbv)) {
        $whereConditions[] = "bm.abv >= ?";
        $params[] = $minAbv;
    }
    
    if (!empty($maxAbv) && is_numeric($maxAbv)) {
        $whereConditions[] = "bm.abv <= ?";
        $params[] = $maxAbv;
    }
    
    if (!empty($minIbu) && is_numeric($minIbu)) {
        $whereConditions[] = "bm.ibu >= ?";
        $params[] = $minIbu;
    }
    
    if (!empty($maxIbu) && is_numeric($maxIbu)) {
        $whereConditions[] = "bm.ibu <= ?";
        $params[] = $maxIbu;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get total count
    $countQuery = "
        SELECT COUNT(*) 
        FROM beer_menu bm 
        LEFT JOIN breweries b ON bm.brewery_id = b.id 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE $whereClause
    ";
    $stmt = $conn->prepare($countQuery);
    $stmt->execute($params);
    $totalBeers = $stmt->fetchColumn();
    
    // Get beers with pagination
    $orderBy = match($sortBy) {
        'rating' => 'bm.average_rating DESC, bm.total_ratings DESC',
        'abv' => 'bm.abv DESC',
        'ibu' => 'bm.ibu DESC',
        'newest' => 'bm.created_at DESC',
        default => 'bm.name ASC'
    };
    
    $query = "
        SELECT bm.*, b.name as brewery_name, b.city, b.state, bs.name as style_name, bs.category as style_category
        FROM beer_menu bm 
        LEFT JOIN breweries b ON bm.brewery_id = b.id 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE $whereClause 
        ORDER BY $orderBy 
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $beers = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Beer discovery error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading beer data.';
}

$totalPages = ceil($totalBeers / $limit);

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-search me-3"></i>Discover Beers
            </h1>
            <p class="lead text-muted">
                Explore our collection of craft beers from breweries around the world
            </p>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- Search -->
                <div class="col-md-6 col-lg-4">
                    <label for="search" class="form-label">
                        <i class="fas fa-search me-1"></i>Search Beers
                    </label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Beer name, description...">
                </div>
                
                <!-- Style Filter -->
                <div class="col-md-6 col-lg-4">
                    <label for="style" class="form-label">
                        <i class="fas fa-tags me-1"></i>Beer Style
                    </label>
                    <select class="form-select" id="style" name="style">
                        <option value="">All Styles</option>
                        <?php 
                        $currentCategory = '';
                        foreach ($beerStyles as $beerStyle): 
                            if ($currentCategory !== $beerStyle['category']):
                                if ($currentCategory !== '') echo '</optgroup>';
                                echo '<optgroup label="' . htmlspecialchars($beerStyle['category']) . '">';
                                $currentCategory = $beerStyle['category'];
                            endif;
                        ?>
                            <option value="<?php echo htmlspecialchars($beerStyle['id']); ?>" 
                                    <?php echo $style === $beerStyle['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($beerStyle['name']); ?>
                            </option>
                        <?php endforeach; ?>
                        <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                    </select>
                </div>
                
                <!-- Brewery Filter -->
                <div class="col-md-6 col-lg-4">
                    <label for="brewery" class="form-label">
                        <i class="fas fa-building me-1"></i>Brewery
                    </label>
                    <select class="form-select" id="brewery" name="brewery">
                        <option value="">All Breweries</option>
                        <?php foreach ($breweries as $breweryOption): ?>
                            <option value="<?php echo htmlspecialchars($breweryOption['id']); ?>" 
                                    <?php echo $brewery === $breweryOption['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($breweryOption['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- ABV Range -->
                <div class="col-md-3 col-lg-2">
                    <label for="min_abv" class="form-label">Min ABV %</label>
                    <input type="number" class="form-control" id="min_abv" name="min_abv" 
                           value="<?php echo htmlspecialchars($minAbv); ?>" 
                           min="0" max="20" step="0.1" placeholder="0">
                </div>
                
                <div class="col-md-3 col-lg-2">
                    <label for="max_abv" class="form-label">Max ABV %</label>
                    <input type="number" class="form-control" id="max_abv" name="max_abv" 
                           value="<?php echo htmlspecialchars($maxAbv); ?>" 
                           min="0" max="20" step="0.1" placeholder="20">
                </div>
                
                <!-- IBU Range -->
                <div class="col-md-3 col-lg-2">
                    <label for="min_ibu" class="form-label">Min IBU</label>
                    <input type="number" class="form-control" id="min_ibu" name="min_ibu" 
                           value="<?php echo htmlspecialchars($minIbu); ?>" 
                           min="0" max="120" placeholder="0">
                </div>
                
                <div class="col-md-3 col-lg-2">
                    <label for="max_ibu" class="form-label">Max IBU</label>
                    <input type="number" class="form-control" id="max_ibu" name="max_ibu" 
                           value="<?php echo htmlspecialchars($maxIbu); ?>" 
                           min="0" max="120" placeholder="120">
                </div>
                
                <!-- Sort -->
                <div class="col-md-6 col-lg-3">
                    <label for="sort" class="form-label">
                        <i class="fas fa-sort me-1"></i>Sort By
                    </label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="name" <?php echo $sortBy === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                        <option value="rating" <?php echo $sortBy === 'rating' ? 'selected' : ''; ?>>Highest Rated</option>
                        <option value="abv" <?php echo $sortBy === 'abv' ? 'selected' : ''; ?>>Highest ABV</option>
                        <option value="ibu" <?php echo $sortBy === 'ibu' ? 'selected' : ''; ?>>Most Bitter</option>
                        <option value="newest" <?php echo $sortBy === 'newest' ? 'selected' : ''; ?>>Newest</option>
                    </select>
                </div>
                
                <!-- Buttons -->
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                    <a href="discover.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times me-2"></i>Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <?php echo number_format($totalBeers); ?> beer<?php echo $totalBeers !== 1 ? 's' : ''; ?> found
                </h5>
                <?php if ($totalPages > 1): ?>
                    <span class="text-muted">
                        Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Beer Grid -->
    <?php if (empty($beers)): ?>
        <div class="text-center py-5">
            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No beers found</h4>
            <p class="text-muted">Try adjusting your search criteria</p>
        </div>
    <?php else: ?>
        <div class="row g-4 mb-4">
            <?php foreach ($beers as $beer): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm beer-card">
                        <?php if (!empty($beer['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($beer['thumbnail']); ?>" 
                                 class="card-img-top beer-image" alt="<?php echo htmlspecialchars($beer['name']); ?>">
                        <?php else: ?>
                            <div class="card-img-top beer-image-placeholder d-flex align-items-center justify-content-center">
                                <i class="fas fa-beer fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($beer['name']); ?></h5>
                            
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                    <?php if (!empty($beer['city'])): ?>
                                        • <?php echo htmlspecialchars($beer['city']); ?><?php if (!empty($beer['state'])): ?>, <?php echo htmlspecialchars($beer['state']); ?><?php endif; ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            
                            <?php if (!empty($beer['style_name'])): ?>
                                <div class="mb-2">
                                    <span class="badge bg-primary">
                                        <?php echo htmlspecialchars($beer['style_name']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="beer-stats mb-3">
                                <?php if (!empty($beer['abv'])): ?>
                                    <span class="badge bg-secondary me-1">
                                        <?php echo number_format($beer['abv'], 1); ?>% ABV
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (!empty($beer['ibu'])): ?>
                                    <span class="badge bg-warning text-dark me-1">
                                        <?php echo $beer['ibu']; ?> IBU
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($beer['average_rating'] > 0): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-star me-1"></i>
                                        <?php echo number_format($beer['average_rating'], 1); ?>
                                        (<?php echo $beer['total_ratings']; ?>)
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!empty($beer['description'])): ?>
                                <p class="card-text text-muted small">
                                    <?php echo htmlspecialchars(substr($beer['description'], 0, 100)); ?>
                                    <?php if (strlen($beer['description']) > 100): ?>...<?php endif; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="/beers/detail.php?id=<?php echo $beer['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                                
                                <?php if (isLoggedIn()): ?>
                                    <a href="/beers/rate.php?id=<?php echo $beer['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-star me-1"></i>Rate
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="Beer pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
