<?php
require_once '../config/config.php';

// Get beer ID
$beerId = sanitizeInput($_GET['id'] ?? '');

if (empty($beerId)) {
    $_SESSION['error_message'] = 'Beer not found.';
    redirect('/beers/discover.php');
}

$beer = null;
$brewery = null;
$beerStyle = null;
$ratings = [];
$userRating = null;
$averageRatings = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get beer details
    $stmt = $conn->prepare("
        SELECT bm.*, b.name as brewery_name, b.city, b.state, b.description as brewery_description,
               bs.name as style_name, bs.category as style_category, bs.description as style_description
        FROM beer_menu bm 
        LEFT JOIN breweries b ON bm.brewery_id = b.id 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE bm.id = ?
    ");
    $stmt->execute([$beerId]);
    $beer = $stmt->fetch();
    
    if (!$beer) {
        $_SESSION['error_message'] = 'Beer not found.';
        redirect('/beers/discover.php');
    }
    
    // Get recent ratings
    $stmt = $conn->prepare("
        SELECT br.*, p.first_name, p.last_name, p.username, p.role
        FROM beer_ratings br
        JOIN profiles p ON br.user_id = p.id
        WHERE br.beer_id = ? AND br.is_public = 1
        ORDER BY br.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$beerId]);
    $ratings = $stmt->fetchAll();
    
    // Get user's rating if logged in
    if (isLoggedIn()) {
        $user = getCurrentUser();
        $stmt = $conn->prepare("SELECT * FROM beer_ratings WHERE beer_id = ? AND user_id = ?");
        $stmt->execute([$beerId, $user['id']]);
        $userRating = $stmt->fetch();
    }
    
    // Calculate average ratings by category
    $stmt = $conn->prepare("
        SELECT 
            AVG(overall_rating) as avg_overall,
            AVG(taste_rating) as avg_taste,
            AVG(aroma_rating) as avg_aroma,
            AVG(appearance_rating) as avg_appearance,
            AVG(mouthfeel_rating) as avg_mouthfeel,
            COUNT(*) as total_ratings
        FROM beer_ratings 
        WHERE beer_id = ? AND is_public = 1
    ");
    $stmt->execute([$beerId]);
    $averageRatings = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Beer detail error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading beer details.';
    redirect('/beers/discover.php');
}

$pageTitle = htmlspecialchars($beer['name']) . ' - ' . APP_NAME;
$additionalCSS = ['/assets/css/beer-detail.css'];

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/index.php">Home</a></li>
            <li class="breadcrumb-item"><a href="/beers/discover.php">Discover Beers</a></li>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($beer['name']); ?></li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Beer Information -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row">
                        <!-- Beer Image -->
                        <div class="col-md-4 text-center mb-3">
                            <?php if (!empty($beer['thumbnail'])): ?>
                                <img src="<?php echo htmlspecialchars($beer['thumbnail']); ?>" 
                                     class="img-fluid beer-image" alt="<?php echo htmlspecialchars($beer['name']); ?>">
                            <?php else: ?>
                                <div class="beer-image-placeholder d-flex align-items-center justify-content-center">
                                    <i class="fas fa-beer fa-4x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Beer Details -->
                        <div class="col-md-8">
                            <h1 class="display-6 fw-bold text-primary mb-2">
                                <?php echo htmlspecialchars($beer['name']); ?>
                            </h1>
                            
                            <div class="mb-3">
                                <h5 class="text-muted mb-1">
                                    <i class="fas fa-building me-2"></i>
                                    <a href="/breweries/detail.php?id=<?php echo $beer['brewery_id']; ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                    </a>
                                </h5>
                                <?php if (!empty($beer['city'])): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($beer['city']); ?><?php if (!empty($beer['state'])): ?>, <?php echo htmlspecialchars($beer['state']); ?><?php endif; ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!empty($beer['style_name'])): ?>
                                <div class="mb-3">
                                    <span class="badge bg-primary fs-6">
                                        <?php echo htmlspecialchars($beer['style_name']); ?>
                                    </span>
                                    <?php if (!empty($beer['style_category'])): ?>
                                        <small class="text-muted ms-2">
                                            <?php echo htmlspecialchars($beer['style_category']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Beer Stats -->
                            <div class="beer-stats mb-4">
                                <div class="row g-3">
                                    <?php if (!empty($beer['abv'])): ?>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-card text-center">
                                                <div class="stat-value"><?php echo number_format($beer['abv'], 1); ?>%</div>
                                                <div class="stat-label">ABV</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($beer['ibu'])): ?>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-card text-center">
                                                <div class="stat-value"><?php echo $beer['ibu']; ?></div>
                                                <div class="stat-label">IBU</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($beer['average_rating'] > 0): ?>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-card text-center">
                                                <div class="stat-value">
                                                    <i class="fas fa-star text-warning me-1"></i>
                                                    <?php echo number_format($beer['average_rating'], 1); ?>
                                                </div>
                                                <div class="stat-label"><?php echo $beer['total_ratings']; ?> ratings</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($beer['total_checkins'] > 0): ?>
                                        <div class="col-6 col-md-3">
                                            <div class="stat-card text-center">
                                                <div class="stat-value"><?php echo number_format($beer['total_checkins']); ?></div>
                                                <div class="stat-label">Check-ins</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Description -->
                            <?php if (!empty($beer['description'])): ?>
                                <div class="mb-4">
                                    <h6 class="fw-bold">Description</h6>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($beer['description'])); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Additional Details -->
                            <?php if (!empty($beer['hops']) || !empty($beer['malts']) || !empty($beer['yeast'])): ?>
                                <div class="mb-4">
                                    <h6 class="fw-bold">Brewing Details</h6>
                                    <?php if (!empty($beer['hops'])): ?>
                                        <p class="mb-1"><strong>Hops:</strong> <?php echo htmlspecialchars($beer['hops']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($beer['malts'])): ?>
                                        <p class="mb-1"><strong>Malts:</strong> <?php echo htmlspecialchars($beer['malts']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($beer['yeast'])): ?>
                                        <p class="mb-1"><strong>Yeast:</strong> <?php echo htmlspecialchars($beer['yeast']); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex gap-2 flex-wrap">
                                <?php if (isLoggedIn()): ?>
                                    <?php if ($userRating): ?>
                                        <a href="/beers/rate.php?id=<?php echo $beer['id']; ?>" class="btn btn-outline-primary">
                                            <i class="fas fa-edit me-2"></i>Edit My Rating
                                        </a>
                                    <?php else: ?>
                                        <a href="/beers/rate.php?id=<?php echo $beer['id']; ?>" class="btn btn-primary">
                                            <i class="fas fa-star me-2"></i>Rate This Beer
                                        </a>
                                    <?php endif; ?>
                                    
                                    <button class="btn btn-outline-success" onclick="checkIn()">
                                        <i class="fas fa-map-marker-alt me-2"></i>Check In
                                    </button>
                                <?php else: ?>
                                    <a href="/auth/login.php" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login to Rate
                                    </a>
                                <?php endif; ?>
                                
                                <button class="btn btn-outline-secondary" onclick="shareBeer()">
                                    <i class="fas fa-share me-2"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Rating Breakdown -->
            <?php if ($averageRatings['total_ratings'] > 0): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Rating Breakdown
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if ($averageRatings['avg_overall']): ?>
                            <div class="rating-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Overall</span>
                                    <span class="fw-bold"><?php echo number_format($averageRatings['avg_overall'], 1); ?></span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-primary" style="width: <?php echo ($averageRatings['avg_overall'] / 5) * 100; ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($averageRatings['avg_taste']): ?>
                            <div class="rating-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Taste</span>
                                    <span class="fw-bold"><?php echo number_format($averageRatings['avg_taste'], 1); ?></span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo ($averageRatings['avg_taste'] / 5) * 100; ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($averageRatings['avg_aroma']): ?>
                            <div class="rating-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Aroma</span>
                                    <span class="fw-bold"><?php echo number_format($averageRatings['avg_aroma'], 1); ?></span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-warning" style="width: <?php echo ($averageRatings['avg_aroma'] / 5) * 100; ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($averageRatings['avg_appearance']): ?>
                            <div class="rating-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Appearance</span>
                                    <span class="fw-bold"><?php echo number_format($averageRatings['avg_appearance'], 1); ?></span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-info" style="width: <?php echo ($averageRatings['avg_appearance'] / 5) * 100; ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($averageRatings['avg_mouthfeel']): ?>
                            <div class="rating-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>Mouthfeel</span>
                                    <span class="fw-bold"><?php echo number_format($averageRatings['avg_mouthfeel'], 1); ?></span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-secondary" style="width: <?php echo ($averageRatings['avg_mouthfeel'] / 5) * 100; ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <small class="text-muted">
                            Based on <?php echo number_format($averageRatings['total_ratings']); ?> rating<?php echo $averageRatings['total_ratings'] !== 1 ? 's' : ''; ?>
                        </small>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Style Information -->
            <?php if (!empty($beer['style_description'])): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>About <?php echo htmlspecialchars($beer['style_name']); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-0">
                            <?php echo htmlspecialchars($beer['style_description']); ?>
                        </p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Recent Reviews -->
    <?php if (!empty($ratings)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>Recent Reviews
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($ratings as $rating): ?>
                            <div class="review-item mb-4 pb-3 border-bottom">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <strong>
                                            <?php 
                                            $displayName = trim($rating['first_name'] . ' ' . $rating['last_name']);
                                            if (empty($displayName)) {
                                                $displayName = $rating['username'] ?: 'Beer Enthusiast';
                                            }
                                            echo htmlspecialchars($displayName);
                                            ?>
                                        </strong>
                                        <span class="badge bg-secondary ms-2">
                                            <?php 
                                            $roleLabels = [
                                                'beer_enthusiast' => '🍺 Enthusiast',
                                                'beer_expert' => '🎯 Expert',
                                                'customer' => '👤 Customer'
                                            ];
                                            echo $roleLabels[$rating['role']] ?? ucfirst($rating['role']);
                                            ?>
                                        </span>
                                    </div>
                                    <div class="text-end">
                                        <div class="rating-stars mb-1">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= $rating['overall_rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                        <small class="text-muted"><?php echo formatDateTime($rating['created_at']); ?></small>
                                    </div>
                                </div>
                                
                                <?php if (!empty($rating['review_title'])): ?>
                                    <h6 class="fw-bold"><?php echo htmlspecialchars($rating['review_title']); ?></h6>
                                <?php endif; ?>
                                
                                <?php if (!empty($rating['review_text'])): ?>
                                    <p class="text-muted mb-2"><?php echo nl2br(htmlspecialchars($rating['review_text'])); ?></p>
                                <?php endif; ?>
                                
                                <?php if (!empty($rating['checkin_location'])): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($rating['checkin_location']); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center">
                            <a href="/beers/reviews.php?id=<?php echo $beer['id']; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>View All Reviews
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function checkIn() {
    // TODO: Implement check-in functionality
    alert('Check-in functionality coming soon!');
}

function shareBeer() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($beer['name']); ?>',
            text: 'Check out this beer on Beersty!',
            url: window.location.href
        });
    } else {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
