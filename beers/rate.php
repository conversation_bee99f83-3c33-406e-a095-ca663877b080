<?php
require_once '../config/config.php';
require_once '../includes/BadgeService.php';

// Require login
requireLogin();

// Get beer ID
$beerId = sanitizeInput($_GET['id'] ?? '');

if (empty($beerId)) {
    $_SESSION['error_message'] = 'Beer not found.';
    redirect('/beers/discover.php');
}

$user = getCurrentUser();
$beer = null;
$existingRating = null;
$errors = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get beer details
    $stmt = $conn->prepare("
        SELECT bm.*, b.name as brewery_name, b.city, b.state
        FROM beer_menu bm 
        LEFT JOIN breweries b ON bm.brewery_id = b.id 
        WHERE bm.id = ?
    ");
    $stmt->execute([$beerId]);
    $beer = $stmt->fetch();
    
    if (!$beer) {
        $_SESSION['error_message'] = 'Beer not found.';
        redirect('/beers/discover.php');
    }
    
    // Check for existing rating
    $stmt = $conn->prepare("SELECT * FROM beer_ratings WHERE beer_id = ? AND user_id = ?");
    $stmt->execute([$beerId, $user['id']]);
    $existingRating = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Beer rating page error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading beer details.';
    redirect('/beers/discover.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $overallRating = (float)($_POST['overall_rating'] ?? 0);
    $tasteRating = !empty($_POST['taste_rating']) ? (float)$_POST['taste_rating'] : null;
    $aromaRating = !empty($_POST['aroma_rating']) ? (float)$_POST['aroma_rating'] : null;
    $appearanceRating = !empty($_POST['appearance_rating']) ? (float)$_POST['appearance_rating'] : null;
    $mouthfeelRating = !empty($_POST['mouthfeel_rating']) ? (float)$_POST['mouthfeel_rating'] : null;
    
    $reviewTitle = sanitizeInput($_POST['review_title'] ?? '');
    $reviewText = sanitizeInput($_POST['review_text'] ?? '');
    $checkinLocation = sanitizeInput($_POST['checkin_location'] ?? '');
    $servingStyle = $_POST['serving_style'] ?? null;
    $isPublic = isset($_POST['is_public']) ? 1 : 0;
    
    // Validation
    if ($overallRating < 0.5 || $overallRating > 5.0) {
        $errors[] = 'Overall rating must be between 0.5 and 5.0.';
    }
    
    if ($tasteRating !== null && ($tasteRating < 0.5 || $tasteRating > 5.0)) {
        $errors[] = 'Taste rating must be between 0.5 and 5.0.';
    }
    
    if ($aromaRating !== null && ($aromaRating < 0.5 || $aromaRating > 5.0)) {
        $errors[] = 'Aroma rating must be between 0.5 and 5.0.';
    }
    
    if ($appearanceRating !== null && ($appearanceRating < 0.5 || $appearanceRating > 5.0)) {
        $errors[] = 'Appearance rating must be between 0.5 and 5.0.';
    }
    
    if ($mouthfeelRating !== null && ($mouthfeelRating < 0.5 || $mouthfeelRating > 5.0)) {
        $errors[] = 'Mouthfeel rating must be between 0.5 and 5.0.';
    }
    
    if (empty($errors)) {
        try {
            $conn->beginTransaction();
            
            if ($existingRating) {
                // Update existing rating
                $stmt = $conn->prepare("
                    UPDATE beer_ratings SET 
                        overall_rating = ?, taste_rating = ?, aroma_rating = ?, 
                        appearance_rating = ?, mouthfeel_rating = ?, review_text = ?, 
                        review_title = ?, checkin_location = ?, serving_style = ?, 
                        is_public = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $overallRating, $tasteRating, $aromaRating, $appearanceRating, 
                    $mouthfeelRating, $reviewText, $reviewTitle, $checkinLocation, 
                    $servingStyle, $isPublic, $existingRating['id']
                ]);
                
                $activityType = 'beer_rating';
                $message = 'Rating updated successfully!';
            } else {
                // Insert new rating
                $stmt = $conn->prepare("
                    INSERT INTO beer_ratings 
                    (user_id, beer_id, brewery_id, overall_rating, taste_rating, aroma_rating, 
                     appearance_rating, mouthfeel_rating, review_text, review_title, 
                     checkin_location, serving_style, is_public) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user['id'], $beerId, $beer['brewery_id'], $overallRating, $tasteRating, 
                    $aromaRating, $appearanceRating, $mouthfeelRating, $reviewText, 
                    $reviewTitle, $checkinLocation, $servingStyle, $isPublic
                ]);
                
                $activityType = 'beer_review';
                $message = 'Rating submitted successfully!';
            }
            
            // Update beer statistics
            $stmt = $conn->prepare("
                UPDATE beer_menu SET 
                    average_rating = (
                        SELECT AVG(overall_rating) 
                        FROM beer_ratings 
                        WHERE beer_id = ? AND is_public = 1
                    ),
                    total_ratings = (
                        SELECT COUNT(*) 
                        FROM beer_ratings 
                        WHERE beer_id = ? AND is_public = 1
                    )
                WHERE id = ?
            ");
            $stmt->execute([$beerId, $beerId, $beerId]);
            
            // Log activity
            if ($isPublic) {
                $activityStmt = $conn->prepare("
                    INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata) 
                    VALUES (?, ?, 'beer', ?, ?)
                ");
                $activityStmt->execute([
                    $user['id'], $activityType, $beerId, 
                    json_encode([
                        'beer_name' => $beer['name'],
                        'brewery_name' => $beer['brewery_name'],
                        'rating' => $overallRating
                    ])
                ]);
            }

            // Check for new badges
            $badgeService = new BadgeService($conn);
            $newBadges = $badgeService->checkAndAwardBadges($user['id'], 'beer_rating', [
                'beer_id' => $beerId,
                'brewery_id' => $beer['brewery_id'],
                'rating' => $overallRating,
                'has_review' => !empty($reviewText)
            ]);

            $conn->commit();

            $successMessage = $message;
            if (!empty($newBadges)) {
                $badgeNames = array_column($newBadges, 'name');
                $successMessage .= ' 🏆 New badge' . (count($newBadges) > 1 ? 's' : '') . ' earned: ' . implode(', ', $badgeNames);
            }

            $_SESSION['success_message'] = $successMessage;
            redirect('/beers/detail.php?id=' . $beerId);
            
        } catch (Exception $e) {
            $conn->rollBack();
            error_log("Beer rating submission error: " . $e->getMessage());
            $errors[] = 'An error occurred while saving your rating.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

$pageTitle = 'Rate ' . htmlspecialchars($beer['name']) . ' - ' . APP_NAME;
$additionalCSS = ['/assets/css/beer-rating.css'];

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/index.php">Home</a></li>
            <li class="breadcrumb-item"><a href="/beers/discover.php">Discover Beers</a></li>
            <li class="breadcrumb-item"><a href="/beers/detail.php?id=<?php echo $beer['id']; ?>"><?php echo htmlspecialchars($beer['name']); ?></a></li>
            <li class="breadcrumb-item active">Rate Beer</li>
        </ol>
    </nav>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        <?php echo $existingRating ? 'Edit Your Rating' : 'Rate This Beer'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Beer Info -->
                    <div class="beer-info mb-4 p-3 bg-light rounded">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <?php if (!empty($beer['thumbnail'])): ?>
                                    <img src="<?php echo htmlspecialchars($beer['thumbnail']); ?>" 
                                         class="img-fluid beer-thumb" alt="<?php echo htmlspecialchars($beer['name']); ?>">
                                <?php else: ?>
                                    <div class="beer-thumb-placeholder">
                                        <i class="fas fa-beer fa-2x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-10">
                                <h4 class="mb-1"><?php echo htmlspecialchars($beer['name']); ?></h4>
                                <p class="text-muted mb-1">
                                    <i class="fas fa-building me-1"></i>
                                    <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                    <?php if (!empty($beer['city'])): ?>
                                        • <?php echo htmlspecialchars($beer['city']); ?><?php if (!empty($beer['state'])): ?>, <?php echo htmlspecialchars($beer['state']); ?><?php endif; ?>
                                    <?php endif; ?>
                                </p>
                                <?php if (!empty($beer['abv']) || !empty($beer['ibu'])): ?>
                                    <div class="beer-stats">
                                        <?php if (!empty($beer['abv'])): ?>
                                            <span class="badge bg-secondary me-1"><?php echo number_format($beer['abv'], 1); ?>% ABV</span>
                                        <?php endif; ?>
                                        <?php if (!empty($beer['ibu'])): ?>
                                            <span class="badge bg-warning text-dark"><?php echo $beer['ibu']; ?> IBU</span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating Form -->
                    <form method="POST">
                        <!-- Overall Rating -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-star text-warning me-2"></i>Overall Rating *
                            </label>
                            <div class="rating-input" data-rating="overall_rating">
                                <input type="hidden" name="overall_rating" value="<?php echo $existingRating['overall_rating'] ?? ''; ?>">
                                <div class="stars">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star" data-value="<?php echo $i; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <div class="rating-text">Click to rate</div>
                            </div>
                        </div>
                        
                        <!-- Detailed Ratings -->
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Taste</label>
                                <div class="rating-input" data-rating="taste_rating">
                                    <input type="hidden" name="taste_rating" value="<?php echo $existingRating['taste_rating'] ?? ''; ?>">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star" data-value="<?php echo $i; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="rating-text">Optional</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Aroma</label>
                                <div class="rating-input" data-rating="aroma_rating">
                                    <input type="hidden" name="aroma_rating" value="<?php echo $existingRating['aroma_rating'] ?? ''; ?>">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star" data-value="<?php echo $i; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="rating-text">Optional</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Appearance</label>
                                <div class="rating-input" data-rating="appearance_rating">
                                    <input type="hidden" name="appearance_rating" value="<?php echo $existingRating['appearance_rating'] ?? ''; ?>">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star" data-value="<?php echo $i; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="rating-text">Optional</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Mouthfeel</label>
                                <div class="rating-input" data-rating="mouthfeel_rating">
                                    <input type="hidden" name="mouthfeel_rating" value="<?php echo $existingRating['mouthfeel_rating'] ?? ''; ?>">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star" data-value="<?php echo $i; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="rating-text">Optional</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Review -->
                        <div class="mb-3">
                            <label for="review_title" class="form-label">Review Title</label>
                            <input type="text" class="form-control" id="review_title" name="review_title" 
                                   value="<?php echo htmlspecialchars($existingRating['review_title'] ?? ''); ?>"
                                   placeholder="Give your review a catchy title...">
                        </div>
                        
                        <div class="mb-4">
                            <label for="review_text" class="form-label">Review</label>
                            <textarea class="form-control" id="review_text" name="review_text" rows="4"
                                      placeholder="Share your thoughts about this beer..."><?php echo htmlspecialchars($existingRating['review_text'] ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Check-in Details -->
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="checkin_location" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Location
                                </label>
                                <input type="text" class="form-control" id="checkin_location" name="checkin_location" 
                                       value="<?php echo htmlspecialchars($existingRating['checkin_location'] ?? ''); ?>"
                                       placeholder="Where did you drink this?">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="serving_style" class="form-label">Serving Style</label>
                                <select class="form-select" id="serving_style" name="serving_style">
                                    <option value="">Select serving style...</option>
                                    <option value="draft" <?php echo ($existingRating['serving_style'] ?? '') === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                    <option value="bottle" <?php echo ($existingRating['serving_style'] ?? '') === 'bottle' ? 'selected' : ''; ?>>Bottle</option>
                                    <option value="can" <?php echo ($existingRating['serving_style'] ?? '') === 'can' ? 'selected' : ''; ?>>Can</option>
                                    <option value="growler" <?php echo ($existingRating['serving_style'] ?? '') === 'growler' ? 'selected' : ''; ?>>Growler</option>
                                    <option value="other" <?php echo ($existingRating['serving_style'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Privacy -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" 
                                       <?php echo ($existingRating['is_public'] ?? 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_public">
                                    <i class="fas fa-globe me-1"></i>Make this rating public
                                </label>
                                <div class="form-text">
                                    Public ratings help other beer enthusiasts discover great beers!
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="/beers/detail.php?id=<?php echo $beer['id']; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php echo $existingRating ? 'Update Rating' : 'Submit Rating'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize rating inputs
    const ratingInputs = document.querySelectorAll('.rating-input');
    
    ratingInputs.forEach(ratingInput => {
        const stars = ratingInput.querySelectorAll('.stars i');
        const hiddenInput = ratingInput.querySelector('input[type="hidden"]');
        const ratingText = ratingInput.querySelector('.rating-text');
        
        // Set initial state
        const currentValue = parseFloat(hiddenInput.value) || 0;
        updateStars(stars, currentValue);
        updateText(ratingText, currentValue);
        
        // Add click handlers
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                const value = index + 1;
                hiddenInput.value = value;
                updateStars(stars, value);
                updateText(ratingText, value);
            });
            
            star.addEventListener('mouseenter', () => {
                updateStars(stars, index + 1, true);
            });
        });
        
        ratingInput.addEventListener('mouseleave', () => {
            const currentValue = parseFloat(hiddenInput.value) || 0;
            updateStars(stars, currentValue);
        });
    });
    
    function updateStars(stars, value, isHover = false) {
        stars.forEach((star, index) => {
            if (index < value) {
                star.classList.add('active');
                if (isHover) star.classList.add('hover');
            } else {
                star.classList.remove('active', 'hover');
            }
        });
    }
    
    function updateText(textElement, value) {
        if (value === 0) {
            textElement.textContent = textElement.textContent.includes('Optional') ? 'Optional' : 'Click to rate';
        } else {
            const labels = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
            textElement.textContent = `${value}/5 - ${labels[value]}`;
        }
    }
});
</script>

<?php include '../includes/footer.php'; ?>
