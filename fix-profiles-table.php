<?php
require_once 'config/config.php';

echo "<h2>🔧 Fixing Profiles Table Structure</h2>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h3>📋 Current Profiles Table Structure:</h3>";
    
    // Check current table structure
    $stmt = $conn->query("DESCRIBE profiles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🔨 Adding Missing Columns:</h3>";
    
    // Define required columns for enhanced profile
    $requiredColumns = [
        'first_name' => 'VARCHAR(100) NULL',
        'last_name' => 'VARCHAR(100) NULL',
        'username' => 'VARCHAR(50) UNIQUE NULL',
        'bio' => 'TEXT NULL',
        'avatar' => 'VARCHAR(255) NULL',
        'hero_banner' => 'VARCHAR(255) NULL',
        'location' => 'VARCHAR(255) NULL',
        'hometown' => 'VARCHAR(255) NULL',
        'date_of_birth' => 'DATE NULL',
        'website' => 'VARCHAR(255) NULL',
        'instagram' => 'VARCHAR(100) NULL',
        'twitter' => 'VARCHAR(100) NULL',
        'facebook' => 'VARCHAR(100) NULL',
        'profile_visibility' => "ENUM('public', 'friends', 'private') DEFAULT 'public'",
        'show_location' => 'BOOLEAN DEFAULT TRUE',
        'show_age' => 'BOOLEAN DEFAULT FALSE',
        'allow_messages' => 'BOOLEAN DEFAULT TRUE',
        'total_checkins' => 'INT DEFAULT 0',
        'total_reviews' => 'INT DEFAULT 0',
        'total_photos' => 'INT DEFAULT 0',
        'follower_count' => 'INT DEFAULT 0',
        'following_count' => 'INT DEFAULT 0'
    ];
    
    $addedColumns = 0;
    
    foreach ($requiredColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $sql = "ALTER TABLE profiles ADD COLUMN $columnName $columnDefinition";
                $conn->exec($sql);
                echo "<p>✅ Added column: <strong>$columnName</strong> ($columnDefinition)</p>";
                $addedColumns++;
            } catch (Exception $e) {
                echo "<p>❌ Failed to add column $columnName: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p>⏭️ Column <strong>$columnName</strong> already exists</p>";
        }
    }
    
    if ($addedColumns > 0) {
        echo "<h3>🎉 Successfully added $addedColumns missing columns!</h3>";
    } else {
        echo "<h3>✅ All required columns already exist!</h3>";
    }
    
    echo "<h3>📋 Updated Profiles Table Structure:</h3>";
    
    // Show updated structure
    $stmt = $conn->query("DESCRIBE profiles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Testing Profile Update:</h3>";
    
    // Test if we can now update profiles with avatar
    try {
        $testSql = "UPDATE profiles SET avatar = '/test/path.jpg' WHERE id = 'test' LIMIT 0";
        $conn->prepare($testSql);
        echo "<p>✅ Profile update with avatar field works!</p>";
    } catch (Exception $e) {
        echo "<p>❌ Profile update test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>🔗 Next Steps:</h3>";
echo "<p>1. Go back to <a href='/user/profile.php'>Profile Page</a> and try updating your profile</p>";
echo "<p>2. The avatar upload should now work properly</p>";
echo "<p>3. If you still get errors, check the error logs for more details</p>";
?>
