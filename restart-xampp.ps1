# XAMPP MySQL Restart Script
# This script properly stops and starts XAMPP MySQL

Write-Host "XAMPP MySQL Restart Script" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Step 1: Stop all MySQL processes
Write-Host "Step 1: Stopping MySQL processes..." -ForegroundColor Yellow
try {
    Stop-Process -Name "mysqld" -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
    Write-Host "MySQL processes stopped" -ForegroundColor Green
} catch {
    Write-Host "No MySQL processes to stop" -ForegroundColor Yellow
}

# Step 2: Kill any remaining processes
Write-Host "Step 2: Ensuring all processes are terminated..." -ForegroundColor Yellow
try {
    taskkill /F /IM mysqld.exe 2>$null
    Write-Host "All MySQL processes terminated" -ForegroundColor Green
} catch {
    Write-Host "No additional processes to terminate" -ForegroundColor Yellow
}

# Step 3: Wait for file locks to clear
Write-Host "Step 3: Waiting for file locks to clear..." -ForegroundColor Yellow
Start-Sleep -Seconds 3
Write-Host "✅ File locks cleared" -ForegroundColor Green

# Step 4: Fix permissions (if needed)
Write-Host "Step 4: Checking MySQL data permissions..." -ForegroundColor Yellow
$dataPath = "C:\xampp\mysql\data"
if (Test-Path $dataPath) {
    try {
        icacls $dataPath /grant "Everyone:(OI)(CI)F" /T /Q | Out-Null
        Write-Host "✅ Permissions verified/fixed" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Could not fix permissions" -ForegroundColor Red
    }
} else {
    Write-Host "❌ MySQL data directory not found" -ForegroundColor Red
    exit 1
}

# Step 5: Start MySQL
Write-Host "Step 5: Starting MySQL service..." -ForegroundColor Yellow
$mysqlPath = "C:\xampp\mysql\bin\mysqld.exe"
$configPath = "C:\xampp\mysql\bin\my.ini"

if (Test-Path $mysqlPath) {
    try {
        Start-Process $mysqlPath -ArgumentList "--defaults-file=$configPath" -WindowStyle Hidden
        Write-Host "✅ MySQL service started" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to start MySQL" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ MySQL executable not found" -ForegroundColor Red
    exit 1
}

# Step 6: Wait and test
Write-Host "Step 6: Waiting for MySQL to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Test if MySQL is responding
Write-Host "Step 7: Testing MySQL connection..." -ForegroundColor Yellow
try {
    $testResult = Invoke-WebRequest -Uri "http://localhost/phpmyadmin" -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ MySQL is responding (phpMyAdmin accessible)" -ForegroundColor Green
} catch {
    Write-Host "⚠️ MySQL may still be starting up" -ForegroundColor Yellow
    Write-Host "   Try testing again in a few seconds" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Test database: http://localhost:8000/test-db-simple.php" -ForegroundColor White
Write-Host "2. Try login: http://localhost:8000/auth/login.php" -ForegroundColor White
Write-Host "3. Check phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host ""
Write-Host "XAMPP MySQL restart completed!" -ForegroundColor Green
