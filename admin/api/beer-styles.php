<?php
/**
 * Beer Styles API
 * Handle CRUD operations for beer styles
 */

require_once '../../config/config.php';
requireLogin();
requireRole('admin');

header('Content-Type: application/json');

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            // Add new beer style
            $name = trim($_POST['name'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $abv_min = !empty($_POST['abv_min']) ? (float)$_POST['abv_min'] : null;
            $abv_max = !empty($_POST['abv_max']) ? (float)$_POST['abv_max'] : null;
            $ibu_min = !empty($_POST['ibu_min']) ? (int)$_POST['ibu_min'] : null;
            $ibu_max = !empty($_POST['ibu_max']) ? (int)$_POST['ibu_max'] : null;
            
            // Validation
            if (empty($name) || empty($category)) {
                http_response_code(400);
                echo json_encode(['error' => 'Name and category are required']);
                exit;
            }
            
            // Check if style already exists
            $stmt = $pdo->prepare("SELECT id FROM beer_styles WHERE name = ? AND is_active = 1");
            $stmt->execute([$name]);
            if ($stmt->fetch()) {
                http_response_code(409);
                echo json_encode(['error' => 'Beer style already exists']);
                exit;
            }
            
            // Insert new beer style
            $stmt = $pdo->prepare("
                INSERT INTO beer_styles (id, name, category, description, abv_min, abv_max, ibu_min, ibu_max, is_active, created_at) 
                VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $result = $stmt->execute([$name, $category, $description, $abv_min, $abv_max, $ibu_min, $ibu_max]);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Beer style added successfully',
                    'style' => [
                        'name' => $name,
                        'category' => $category,
                        'description' => $description
                    ]
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to add beer style']);
            }
            break;
            
        case 'GET':
            // Get all beer styles
            $stmt = $pdo->query("
                SELECT bs.id, bs.name, bs.category, bs.description, 
                       bs.abv_min, bs.abv_max, bs.ibu_min, bs.ibu_max,
                       COUNT(bm.id) as beer_count
                FROM beer_styles bs
                LEFT JOIN beer_menu bm ON bs.id = bm.beer_style_id
                WHERE bs.is_active = 1
                GROUP BY bs.id
                ORDER BY bs.category, bs.name
            ");
            
            $styles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode(['styles' => $styles]);
            break;
            
        case 'PUT':
            // Update beer style
            $input = json_decode(file_get_contents('php://input'), true);
            $id = $input['id'] ?? '';
            $name = trim($input['name'] ?? '');
            $category = trim($input['category'] ?? '');
            $description = trim($input['description'] ?? '');
            $abv_min = !empty($input['abv_min']) ? (float)$input['abv_min'] : null;
            $abv_max = !empty($input['abv_max']) ? (float)$input['abv_max'] : null;
            $ibu_min = !empty($input['ibu_min']) ? (int)$input['ibu_min'] : null;
            $ibu_max = !empty($input['ibu_max']) ? (int)$input['ibu_max'] : null;
            
            if (empty($id) || empty($name) || empty($category)) {
                http_response_code(400);
                echo json_encode(['error' => 'ID, name and category are required']);
                exit;
            }
            
            $stmt = $pdo->prepare("
                UPDATE beer_styles 
                SET name = ?, category = ?, description = ?, abv_min = ?, abv_max = ?, ibu_min = ?, ibu_max = ?, updated_at = NOW()
                WHERE id = ? AND is_active = 1
            ");
            
            $result = $stmt->execute([$name, $category, $description, $abv_min, $abv_max, $ibu_min, $ibu_max, $id]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Beer style updated successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update beer style']);
            }
            break;
            
        case 'DELETE':
            // Soft delete beer style
            $input = json_decode(file_get_contents('php://input'), true);
            $id = $input['id'] ?? '';
            
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['error' => 'ID is required']);
                exit;
            }
            
            // Check if style is being used
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM beer_menu WHERE beer_style_id = ?");
            $stmt->execute([$id]);
            $usage_count = $stmt->fetchColumn();
            
            if ($usage_count > 0) {
                http_response_code(409);
                echo json_encode([
                    'error' => "Cannot delete beer style. It is being used by $usage_count beer(s)."
                ]);
                exit;
            }
            
            // Soft delete
            $stmt = $pdo->prepare("UPDATE beer_styles SET is_active = 0, updated_at = NOW() WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Beer style deleted successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to delete beer style']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Beer styles API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
