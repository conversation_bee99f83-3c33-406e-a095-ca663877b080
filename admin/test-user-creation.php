<?php
/**
 * Test User Creation
 * Quick test to verify user management system works
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Test User Creation</title></head><body>";
echo "<h1>🧪 Testing User Creation...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check table structure
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "📋 Available columns: " . implode(', ', $columns) . "<br><br>";
    
    // Test data
    $testUsers = [
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'User',
            'role' => 'user',
            'status' => 'active'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'first_name' => 'Site',
            'last_name' => 'Moderator',
            'role' => 'site_moderator',
            'status' => 'active'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'first_name' => 'Business',
            'last_name' => 'Owner',
            'role' => 'business_owner',
            'status' => 'active'
        ]
    ];
    
    foreach ($testUsers as $i => $userData) {
        echo "🔄 Creating test user " . ($i + 1) . ": {$userData['email']}<br>";
        
        try {
            // Check if user already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$userData['email']]);
            if ($stmt->fetch()) {
                echo "⚠️ User already exists, skipping<br>";
                continue;
            }
            
            // Hash password
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Build INSERT based on available columns
            $insertFields = ['email', 'password'];
            $insertValues = [$userData['email'], $hashedPassword];
            $placeholders = ['?', '?'];
            
            if (in_array('first_name', $columns)) {
                $insertFields[] = 'first_name';
                $insertValues[] = $userData['first_name'];
                $placeholders[] = '?';
            }
            if (in_array('last_name', $columns)) {
                $insertFields[] = 'last_name';
                $insertValues[] = $userData['last_name'];
                $placeholders[] = '?';
            }
            if (in_array('role', $columns)) {
                $insertFields[] = 'role';
                $insertValues[] = $userData['role'];
                $placeholders[] = '?';
            }
            if (in_array('status', $columns)) {
                $insertFields[] = 'status';
                $insertValues[] = $userData['status'];
                $placeholders[] = '?';
            }
            if (in_array('email_verified', $columns)) {
                $insertFields[] = 'email_verified';
                $insertValues[] = 1; // Mark as verified for testing
                $placeholders[] = '?';
            }
            if (in_array('created_at', $columns)) {
                $insertFields[] = 'created_at';
                $placeholders[] = 'NOW()';
            }
            if (in_array('updated_at', $columns)) {
                $insertFields[] = 'updated_at';
                $placeholders[] = 'NOW()';
            }
            
            $sql = "INSERT INTO users (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            echo "📝 SQL: $sql<br>";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($insertValues);
            
            $userId = $conn->lastInsertId();
            echo "✅ User created successfully with ID: $userId<br>";
            
        } catch (Exception $e) {
            echo "❌ Error creating user: " . $e->getMessage() . "<br>";
        }
        
        echo "<br>";
    }
    
    // Show current user count
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetchColumn();
    echo "📊 Total users in database: $count<br>";
    
    // Show sample users
    $stmt = $conn->query("SELECT * FROM users ORDER BY id DESC LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<br>👥 Recent users:<br>";
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, Email: {$user['email']}, Role: " . ($user['role'] ?? 'N/A') . ", Status: " . ($user['status'] ?? 'N/A') . "<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
