<?php
/**
 * Advanced Places CSV Import System
 * Sophisticated import with field mapping, duplicate detection, and data validation
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Places Import - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css', '../assets/css/places-import.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle file upload and analysis
$upload_step = 1;
$csv_data = null;
$headers = [];
$sample_data = [];
$analysis = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'upload_csv':
                $result = handleCSVUpload();
                if ($result['success']) {
                    $upload_step = 2;
                    $csv_data = $result['data'];
                    $headers = $result['headers'];
                    $sample_data = $result['sample'];
                    $analysis = $result['analysis'];
                }
                break;
            case 'import_data':
                $result = handleDataImport();
                break;
        }
    }
}

function handleCSVUpload() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        $_SESSION['error_message'] = 'Please select a valid CSV file';
        return ['success' => false];
    }
    
    $file = $_FILES['csv_file'];
    
    // Validate file type
    $allowed_types = ['text/csv', 'application/csv', 'text/plain'];
    $file_info = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($file_info, $file['tmp_name']);
    finfo_close($file_info);
    
    if (!in_array($mime_type, $allowed_types) && !str_ends_with($file['name'], '.csv')) {
        $_SESSION['error_message'] = 'Please upload a CSV file';
        return ['success' => false];
    }
    
    // Read and parse CSV
    $csv_data = [];
    $headers = [];
    $row_count = 0;
    
    if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
        // Detect delimiter
        $first_line = fgets($handle);
        rewind($handle);
        $delimiter = detectDelimiter($first_line);
        
        while (($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE) {
            if ($row_count === 0) {
                $headers = array_map('trim', $data);
            } else {
                $csv_data[] = array_combine($headers, array_map('trim', $data));
            }
            $row_count++;
            
            // Limit preview to first 1000 rows for performance
            if ($row_count > 1000) break;
        }
        fclose($handle);
    }
    
    if (empty($headers)) {
        $_SESSION['error_message'] = 'Could not read CSV headers';
        return ['success' => false];
    }
    
    // Store CSV data in session for import
    $_SESSION['csv_import_data'] = $csv_data;
    $_SESSION['csv_import_headers'] = $headers;
    
    // Analyze data
    $analysis = analyzeCSVData($csv_data, $headers);
    
    // Get sample data (first 5 rows)
    $sample_data = array_slice($csv_data, 0, 5);
    
    return [
        'success' => true,
        'data' => $csv_data,
        'headers' => $headers,
        'sample' => $sample_data,
        'analysis' => $analysis
    ];
}

function detectDelimiter($line) {
    $delimiters = [',', ';', '\t', '|'];
    $delimiter_counts = [];
    
    foreach ($delimiters as $delimiter) {
        $delimiter_counts[$delimiter] = substr_count($line, $delimiter);
    }
    
    return array_search(max($delimiter_counts), $delimiter_counts) ?: ',';
}

function analyzeCSVData($data, $headers) {
    $analysis = [
        'total_rows' => count($data),
        'columns' => count($headers),
        'field_analysis' => [],
        'potential_duplicates' => [],
        'data_quality' => []
    ];
    
    // Analyze each field
    foreach ($headers as $header) {
        $field_data = array_column($data, $header);
        $non_empty = array_filter($field_data, function($value) {
            return !empty(trim($value));
        });
        
        $analysis['field_analysis'][$header] = [
            'fill_rate' => count($non_empty) / count($data) * 100,
            'unique_values' => count(array_unique($non_empty)),
            'sample_values' => array_slice(array_unique($non_empty), 0, 5),
            'suggested_mapping' => suggestFieldMapping($header, $field_data)
        ];
    }
    
    // Check for potential duplicates
    $analysis['potential_duplicates'] = findPotentialDuplicates($data);
    
    return $analysis;
}

function suggestFieldMapping($header, $data) {
    $header_lower = strtolower($header);
    
    // Define mapping patterns
    $patterns = [
        'name' => ['name', 'business_name', 'brewery_name', 'restaurant_name', 'title'],
        'brewery_type' => ['type', 'brewery_type', 'business_type', 'category'],
        'address_1' => ['address', 'street', 'address_1', 'street_address', 'addr1'],
        'address_2' => ['address_2', 'suite', 'unit', 'apt', 'addr2'],
        'city' => ['city', 'town', 'municipality'],
        'state' => ['state', 'province', 'region', 'state_province'],
        'postal_code' => ['zip', 'postal_code', 'zipcode', 'postcode', 'postal'],
        'country' => ['country', 'nation'],
        'phone' => ['phone', 'telephone', 'tel', 'phone_number'],
        'email' => ['email', 'email_address', 'contact_email'],
        'website_url' => ['website', 'url', 'web', 'homepage', 'site'],
        'latitude' => ['lat', 'latitude', 'y'],
        'longitude' => ['lng', 'lon', 'longitude', 'x'],
        'description' => ['description', 'about', 'bio', 'summary']
    ];
    
    foreach ($patterns as $field => $keywords) {
        foreach ($keywords as $keyword) {
            if (strpos($header_lower, $keyword) !== false) {
                return $field;
            }
        }
    }
    
    // Analyze data patterns for better suggestions
    if (!empty($data)) {
        $sample = array_filter(array_slice($data, 0, 10));
        
        // Check for email pattern
        if (count(array_filter($sample, function($v) { return filter_var($v, FILTER_VALIDATE_EMAIL); })) > 0) {
            return 'email';
        }
        
        // Check for URL pattern
        if (count(array_filter($sample, function($v) { return filter_var($v, FILTER_VALIDATE_URL); })) > 0) {
            return 'website_url';
        }
        
        // Check for phone pattern
        if (count(array_filter($sample, function($v) { return preg_match('/[\d\-\(\)\+\s]{10,}/', $v); })) > 0) {
            return 'phone';
        }
        
        // Check for coordinate pattern
        if (count(array_filter($sample, function($v) { return is_numeric($v) && abs($v) <= 180; })) > 0) {
            if (strpos($header_lower, 'lat') !== false || max($sample) <= 90) {
                return 'latitude';
            } elseif (strpos($header_lower, 'lng') !== false || strpos($header_lower, 'lon') !== false) {
                return 'longitude';
            }
        }
    }
    
    return 'ignore'; // Default to ignore if no pattern matches
}

function findPotentialDuplicates($data) {
    $duplicates = [];
    $seen = [];
    
    foreach ($data as $index => $row) {
        // Create a key based on name and address for duplicate detection
        $key = strtolower(trim($row['name'] ?? '')) . '|' . strtolower(trim($row['address_1'] ?? '')) . '|' . strtolower(trim($row['city'] ?? ''));
        
        if (isset($seen[$key])) {
            $duplicates[] = [
                'original_row' => $seen[$key],
                'duplicate_row' => $index,
                'similarity' => 'exact_match'
            ];
        } else {
            $seen[$key] = $index;
        }
    }
    
    return $duplicates;
}

function handleDataImport() {
    global $pdo;
    
    if (!isset($_SESSION['csv_import_data']) || !isset($_POST['field_mapping'])) {
        $_SESSION['error_message'] = 'Import data not found. Please upload the CSV file again.';
        return ['success' => false];
    }
    
    $csv_data = $_SESSION['csv_import_data'];
    $field_mapping = $_POST['field_mapping'];
    $import_options = $_POST['import_options'] ?? [];
    
    $results = [
        'total_rows' => count($csv_data),
        'imported' => 0,
        'skipped' => 0,
        'errors' => [],
        'duplicates_found' => 0
    ];
    
    try {
        $pdo->beginTransaction();
        
        foreach ($csv_data as $index => $row) {
            $result = importSingleRow($pdo, $row, $field_mapping, $import_options, $index + 1);
            
            if ($result['success']) {
                $results['imported']++;
            } else {
                $results['skipped']++;
                if (!empty($result['error'])) {
                    $results['errors'][] = "Row " . ($index + 1) . ": " . $result['error'];
                }
                if ($result['duplicate']) {
                    $results['duplicates_found']++;
                }
            }
        }
        
        $pdo->commit();
        
        // Clear session data
        unset($_SESSION['csv_import_data']);
        unset($_SESSION['csv_import_headers']);
        
        $_SESSION['success_message'] = "Import completed! Imported: {$results['imported']}, Skipped: {$results['skipped']}, Duplicates: {$results['duplicates_found']}";
        
        if (!empty($results['errors'])) {
            $_SESSION['import_errors'] = $results['errors'];
        }
        
        return ['success' => true, 'results' => $results];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $_SESSION['error_message'] = 'Import failed: ' . $e->getMessage();
        return ['success' => false];
    }
}

function importSingleRow($pdo, $row, $field_mapping, $options, $row_number) {
    // Map CSV data to database fields
    $place_data = [];
    
    foreach ($field_mapping as $csv_field => $db_field) {
        if ($db_field !== 'ignore' && isset($row[$csv_field])) {
            $value = trim($row[$csv_field]);
            
            // Data cleaning and validation
            switch ($db_field) {
                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        return ['success' => false, 'error' => 'Invalid email format', 'duplicate' => false];
                    }
                    break;
                case 'website_url':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                        // Try adding http:// prefix
                        $value = 'http://' . $value;
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $value = ''; // Clear invalid URL
                        }
                    }
                    break;
                case 'phone':
                    // Clean phone number
                    $value = preg_replace('/[^\d\+\-\(\)\s]/', '', $value);
                    break;
                case 'latitude':
                case 'longitude':
                    if (!empty($value) && !is_numeric($value)) {
                        $value = null;
                    }
                    break;
            }
            
            $place_data[$db_field] = $value;
        }
    }
    
    // Validate required fields
    if (empty($place_data['name'])) {
        return ['success' => false, 'error' => 'Name is required', 'duplicate' => false];
    }
    
    // Check for duplicates
    if (isset($options['check_duplicates']) && $options['check_duplicates']) {
        $duplicate_check = checkForDuplicate($pdo, $place_data);
        if ($duplicate_check['found']) {
            if (isset($options['skip_duplicates']) && $options['skip_duplicates']) {
                return ['success' => false, 'error' => 'Duplicate found', 'duplicate' => true];
            } elseif (isset($options['update_duplicates']) && $options['update_duplicates']) {
                return updateExistingPlace($pdo, $duplicate_check['id'], $place_data);
            }
        }
    }
    
    // Insert new place
    return insertNewPlace($pdo, $place_data);
}

function checkForDuplicate($pdo, $place_data) {
    // Check by name and address
    $stmt = $pdo->prepare("
        SELECT id FROM breweries 
        WHERE LOWER(name) = LOWER(?) 
        AND (LOWER(address_1) = LOWER(?) OR LOWER(city) = LOWER(?))
        LIMIT 1
    ");
    
    $stmt->execute([
        $place_data['name'] ?? '',
        $place_data['address_1'] ?? '',
        $place_data['city'] ?? ''
    ]);
    
    $result = $stmt->fetch();
    
    return [
        'found' => !empty($result),
        'id' => $result['id'] ?? null
    ];
}

function updateExistingPlace($pdo, $place_id, $place_data) {
    try {
        $fields = [];
        $values = [];
        
        foreach ($place_data as $field => $value) {
            if (!empty($value)) {
                $fields[] = "$field = ?";
                $values[] = $value;
            }
        }
        
        if (!empty($fields)) {
            $fields[] = "updated_at = NOW()";
            $values[] = $place_id;
            
            $sql = "UPDATE breweries SET " . implode(', ', $fields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
        }
        
        return ['success' => true, 'error' => '', 'duplicate' => false];
        
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage(), 'duplicate' => false];
    }
}

function insertNewPlace($pdo, $place_data) {
    try {
        // Set defaults
        $place_data['id'] = 'UUID()';
        $place_data['created_at'] = 'NOW()';
        $place_data['brewery_type'] = $place_data['brewery_type'] ?? 'brewery';
        $place_data['country'] = $place_data['country'] ?? 'United States';
        
        $fields = array_keys($place_data);
        $placeholders = array_map(function($field) use ($place_data) {
            return in_array($place_data[$field], ['UUID()', 'NOW()']) ? $place_data[$field] : '?';
        }, $fields);
        
        $values = array_filter($place_data, function($value) {
            return !in_array($value, ['UUID()', 'NOW()']);
        });
        
        $sql = "INSERT INTO breweries (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(array_values($values));
        
        return ['success' => true, 'error' => '', 'duplicate' => false];
        
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage(), 'duplicate' => false];
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-upload me-2"></i>Places Import
                    </h1>
                    <p class="text-muted mb-0">Import places from CSV with advanced field mapping and duplicate detection</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo url('admin/download-places-template.php'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                    <a href="<?php echo url('admin/breweries.php'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-map-marker-alt me-2"></i>Manage Places
                    </a>
                    <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['import_errors'])): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Import Errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (array_slice($_SESSION['import_errors'], 0, 10) as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
                <?php if (count($_SESSION['import_errors']) > 10): ?>
                    <li><em>... and <?php echo count($_SESSION['import_errors']) - 10; ?> more errors</em></li>
                <?php endif; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['import_errors']); ?>
    <?php endif; ?>

    <!-- Import Steps -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="import-steps">
                        <div class="step <?php echo $upload_step >= 1 ? 'active' : ''; ?>">
                            <div class="step-number">1</div>
                            <div class="step-title">Upload CSV</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step <?php echo $upload_step >= 2 ? 'active' : ''; ?>">
                            <div class="step-number">2</div>
                            <div class="step-title">Map Fields</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step <?php echo $upload_step >= 3 ? 'active' : ''; ?>">
                            <div class="step-number">3</div>
                            <div class="step-title">Import Data</div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <?php if ($upload_step === 1): ?>
                        <!-- Step 1: Upload CSV -->
                        <div class="upload-section">
                            <h5><i class="fas fa-cloud-upload-alt me-2"></i>Step 1: Upload CSV File</h5>
                            <p class="text-muted">Select a CSV file containing place data. The system will analyze the file and suggest field mappings.</p>

                            <form method="POST" enctype="multipart/form-data" class="upload-form">
                                <input type="hidden" name="action" value="upload_csv">

                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x mb-3"></i>
                                        <h6>Drag & Drop CSV File Here</h6>
                                        <p class="text-muted">or click to browse</p>
                                        <input type="file" name="csv_file" id="csvFile" accept=".csv" required>
                                    </div>
                                </div>

                                <div class="upload-info mt-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle me-2"></i>Supported Formats</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>CSV files (.csv)</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Comma, semicolon, or tab delimited</li>
                                                <li><i class="fas fa-check text-success me-2"></i>UTF-8 encoding recommended</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb me-2"></i>Tips for Best Results</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>Include headers in first row</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Use clear column names</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Ensure data consistency</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-upload me-2"></i>Upload & Analyze CSV
                                    </button>
                                </div>
                            </form>
                        </div>

                    <?php elseif ($upload_step === 2): ?>
                        <!-- Step 2: Field Mapping -->
                        <div class="mapping-section">
                            <h5><i class="fas fa-map me-2"></i>Step 2: Map CSV Fields to Database Fields</h5>
                            <p class="text-muted">Review the detected fields and map them to the correct database columns. The system has made suggestions based on field names and data patterns.</p>

                            <!-- CSV Analysis Summary -->
                            <div class="analysis-summary mb-4">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number"><?php echo number_format($analysis['total_rows']); ?></div>
                                            <div class="stat-label">Total Rows</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number"><?php echo $analysis['columns']; ?></div>
                                            <div class="stat-label">Columns</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number"><?php echo count($analysis['potential_duplicates']); ?></div>
                                            <div class="stat-label">Potential Duplicates</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number"><?php echo count(array_filter($analysis['field_analysis'], function($field) { return $field['suggested_mapping'] !== 'ignore'; })); ?></div>
                                            <div class="stat-label">Mapped Fields</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Field Mapping Form -->
                            <form method="POST" id="mappingForm">
                                <input type="hidden" name="action" value="import_data">

                                <div class="field-mapping-table">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>CSV Column</th>
                                                    <th>Sample Data</th>
                                                    <th>Data Quality</th>
                                                    <th>Map to Database Field</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($headers as $header): ?>
                                                    <?php $field_info = $analysis['field_analysis'][$header]; ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($header); ?></strong>
                                                        </td>
                                                        <td>
                                                            <div class="sample-data">
                                                                <?php foreach (array_slice($field_info['sample_values'], 0, 3) as $sample): ?>
                                                                    <span class="sample-value"><?php echo htmlspecialchars($sample); ?></span>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="quality-info">
                                                                <div class="fill-rate">
                                                                    <small>Fill Rate: <?php echo number_format($field_info['fill_rate'], 1); ?>%</small>
                                                                    <div class="progress" style="height: 4px;">
                                                                        <div class="progress-bar" style="width: <?php echo $field_info['fill_rate']; ?>%"></div>
                                                                    </div>
                                                                </div>
                                                                <small class="text-muted"><?php echo number_format($field_info['unique_values']); ?> unique values</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <select name="field_mapping[<?php echo htmlspecialchars($header); ?>]" class="form-select field-mapping-select">
                                                                <option value="ignore">Ignore this field</option>
                                                                <option value="name" <?php echo $field_info['suggested_mapping'] === 'name' ? 'selected' : ''; ?>>Name *</option>
                                                                <option value="brewery_type" <?php echo $field_info['suggested_mapping'] === 'brewery_type' ? 'selected' : ''; ?>>Type</option>
                                                                <option value="description" <?php echo $field_info['suggested_mapping'] === 'description' ? 'selected' : ''; ?>>Description</option>
                                                                <option value="phone" <?php echo $field_info['suggested_mapping'] === 'phone' ? 'selected' : ''; ?>>Phone</option>
                                                                <option value="email" <?php echo $field_info['suggested_mapping'] === 'email' ? 'selected' : ''; ?>>Email</option>
                                                                <option value="website_url" <?php echo $field_info['suggested_mapping'] === 'website_url' ? 'selected' : ''; ?>>Website URL</option>
                                                                <option value="address_1" <?php echo $field_info['suggested_mapping'] === 'address_1' ? 'selected' : ''; ?>>Address Line 1</option>
                                                                <option value="address_2" <?php echo $field_info['suggested_mapping'] === 'address_2' ? 'selected' : ''; ?>>Address Line 2</option>
                                                                <option value="city" <?php echo $field_info['suggested_mapping'] === 'city' ? 'selected' : ''; ?>>City</option>
                                                                <option value="state" <?php echo $field_info['suggested_mapping'] === 'state' ? 'selected' : ''; ?>>State</option>
                                                                <option value="postal_code" <?php echo $field_info['suggested_mapping'] === 'postal_code' ? 'selected' : ''; ?>>Postal Code</option>
                                                                <option value="country" <?php echo $field_info['suggested_mapping'] === 'country' ? 'selected' : ''; ?>>Country</option>
                                                                <option value="latitude" <?php echo $field_info['suggested_mapping'] === 'latitude' ? 'selected' : ''; ?>>Latitude</option>
                                                                <option value="longitude" <?php echo $field_info['suggested_mapping'] === 'longitude' ? 'selected' : ''; ?>>Longitude</option>
                                                            </select>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Import Options -->
                                <div class="import-options mt-4">
                                    <h6><i class="fas fa-cog me-2"></i>Import Options</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="check_duplicates" name="import_options[check_duplicates]" value="1" checked>
                                                <label class="form-check-label" for="check_duplicates">
                                                    Check for duplicates
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="skip_duplicates" name="import_options[skip_duplicates]" value="1" checked>
                                                <label class="form-check-label" for="skip_duplicates">
                                                    Skip duplicate entries
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="update_duplicates" name="import_options[update_duplicates]" value="1">
                                                <label class="form-check-label" for="update_duplicates">
                                                    Update existing places with new data
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="validate_data" name="import_options[validate_data]" value="1" checked>
                                                <label class="form-check-label" for="validate_data">
                                                    Validate data formats (email, URL, etc.)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sample Data Preview -->
                                <?php if (!empty($sample_data)): ?>
                                    <div class="sample-preview mt-4">
                                        <h6><i class="fas fa-eye me-2"></i>Data Preview (First 5 Rows)</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <?php foreach ($headers as $header): ?>
                                                            <th><?php echo htmlspecialchars($header); ?></th>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($sample_data as $row): ?>
                                                        <tr>
                                                            <?php foreach ($headers as $header): ?>
                                                                <td><?php echo htmlspecialchars(substr($row[$header] ?? '', 0, 50)); ?></td>
                                                            <?php endforeach; ?>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="text-center mt-4">
                                    <button type="button" class="btn btn-secondary me-2" onclick="location.reload()">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Upload
                                    </button>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-download me-2"></i>Import Data
                                    </button>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/places-import.js"></script>

<?php require_once '../includes/footer.php'; ?>
