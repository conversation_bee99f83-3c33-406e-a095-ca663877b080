<?php
/**
 * Brewery Import Guide
 * Instructions for preparing and importing brewery CSV data
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Brewery Import Guide';
$additionalCSS = ['../assets/css/admin.css'];

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-book text-info me-2"></i>
                    Brewery Import Guide
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="brewery-import.php" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>Import Breweries
                        </a>
                    </div>
                    <div class="btn-group">
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>How to Import Michigan Breweries</h5>
                        </div>
                        <div class="card-body">
                            <h6>Step 1: Prepare Your CSV File</h6>
                            <p>Create a CSV file with brewery information. You can use Excel, Google Sheets, or any spreadsheet application.</p>
                            
                            <h6>Step 2: Required Column</h6>
                            <p>Your CSV must include at least one column:</p>
                            <ul>
                                <li><strong>name</strong> - The brewery name (required)</li>
                            </ul>
                            
                            <h6>Step 3: Optional Columns</h6>
                            <p>Include any of these optional columns for more complete data:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul>
                                        <li><strong>address</strong> - Street address</li>
                                        <li><strong>city</strong> - City name</li>
                                        <li><strong>state</strong> - State (e.g., MI)</li>
                                        <li><strong>zip</strong> - ZIP code</li>
                                        <li><strong>phone</strong> - Phone number</li>
                                        <li><strong>website</strong> - Website URL</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul>
                                        <li><strong>email</strong> - Contact email</li>
                                        <li><strong>brewery_type</strong> - Type of brewery</li>
                                        <li><strong>description</strong> - About the brewery</li>
                                        <li><strong>latitude</strong> - GPS latitude</li>
                                        <li><strong>longitude</strong> - GPS longitude</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <h6>Step 4: Column Name Variations</h6>
                            <p>The system automatically recognizes common column name variations:</p>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Standard Field</th>
                                            <th>Recognized Variations</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>name</td>
                                            <td>brewery_name, brewery name, business_name</td>
                                        </tr>
                                        <tr>
                                            <td>address</td>
                                            <td>street, street_address, address_1</td>
                                        </tr>
                                        <tr>
                                            <td>city</td>
                                            <td>town</td>
                                        </tr>
                                        <tr>
                                            <td>state</td>
                                            <td>province, region</td>
                                        </tr>
                                        <tr>
                                            <td>zip</td>
                                            <td>zipcode, zip_code, postal_code, postcode</td>
                                        </tr>
                                        <tr>
                                            <td>phone</td>
                                            <td>telephone, phone_number, tel</td>
                                        </tr>
                                        <tr>
                                            <td>website</td>
                                            <td>url, web, homepage</td>
                                        </tr>
                                        <tr>
                                            <td>brewery_type</td>
                                            <td>type, category</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <h6>Step 5: Brewery Types</h6>
                            <p>If including brewery_type, use one of these values:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul>
                                        <li><strong>micro</strong> - Microbrewery</li>
                                        <li><strong>nano</strong> - Nanobrewery</li>
                                        <li><strong>regional</strong> - Regional brewery</li>
                                        <li><strong>large</strong> - Large brewery</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul>
                                        <li><strong>planning</strong> - In planning</li>
                                        <li><strong>bar</strong> - Brewpub</li>
                                        <li><strong>contract</strong> - Contract brewery</li>
                                        <li><strong>proprietor</strong> - Proprietor</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <h6>Step 6: Data Tips</h6>
                            <ul>
                                <li><strong>Phone numbers:</strong> Any format is accepted (will be cleaned automatically)</li>
                                <li><strong>Websites:</strong> Include http:// or https:// (will be added if missing)</li>
                                <li><strong>Coordinates:</strong> Use decimal degrees format (e.g., 42.3314, -83.0458)</li>
                                <li><strong>Duplicates:</strong> Existing breweries with the same name will be updated</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-download me-2"></i>Sample Template</h5>
                        </div>
                        <div class="card-body">
                            <p>Download a sample CSV template with Michigan brewery examples:</p>
                            <a href="download-template.php" class="btn btn-success w-100 mb-3">
                                <i class="fas fa-download me-2"></i>Download Template
                            </a>
                            
                            <h6>Template Includes:</h6>
                            <ul class="small">
                                <li>Bell's Brewery (Kalamazoo)</li>
                                <li>Founders Brewing (Grand Rapids)</li>
                                <li>Short's Brewing (Bellaire)</li>
                                <li>Brewery Vivant (Grand Rapids)</li>
                                <li>Atwater Brewery (Detroit)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-database me-2"></i>Data Sources</h5>
                        </div>
                        <div class="card-body">
                            <h6>Michigan Brewery Data Sources:</h6>
                            <ul class="small">
                                <li><a href="https://www.brewersassociation.org/" target="_blank">Brewers Association</a></li>
                                <li><a href="https://www.michiganbrewersguild.org/" target="_blank">Michigan Brewers Guild</a></li>
                                <li><a href="https://openbrewerydb.org/" target="_blank">Open Brewery DB</a></li>
                                <li><a href="https://www.beeradvocate.com/" target="_blank">BeerAdvocate</a></li>
                                <li><a href="https://untappd.com/" target="_blank">Untappd</a></li>
                            </ul>
                            
                            <div class="alert alert-info mt-3">
                                <small>
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>Tip:</strong> Many brewery databases offer CSV export options.
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs me-2"></i>Import Process</h5>
                        </div>
                        <div class="card-body">
                            <ol class="small">
                                <li>Upload your CSV file</li>
                                <li>System validates the file format</li>
                                <li>Column mapping is automatically detected</li>
                                <li>Data is cleaned and validated</li>
                                <li>Existing breweries are updated</li>
                                <li>New breweries are inserted</li>
                                <li>Import summary is displayed</li>
                            </ol>
                            
                            <div class="alert alert-warning mt-3">
                                <small>
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    <strong>Note:</strong> Large files may take several minutes to process.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-table me-2"></i>Sample CSV Format</h5>
                        </div>
                        <div class="card-body">
                            <p>Here's an example of what your CSV should look like:</p>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>name</th>
                                            <th>address</th>
                                            <th>city</th>
                                            <th>state</th>
                                            <th>zip</th>
                                            <th>phone</th>
                                            <th>website</th>
                                            <th>brewery_type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Bell's Brewery</td>
                                            <td>355 E Kalamazoo Ave</td>
                                            <td>Kalamazoo</td>
                                            <td>MI</td>
                                            <td>49007</td>
                                            <td>(*************</td>
                                            <td>https://bellsbeer.com</td>
                                            <td>regional</td>
                                        </tr>
                                        <tr>
                                            <td>Founders Brewing</td>
                                            <td>235 Grandville Ave SW</td>
                                            <td>Grand Rapids</td>
                                            <td>MI</td>
                                            <td>49503</td>
                                            <td>(616) 776-1195</td>
                                            <td>https://foundersbrewing.com</td>
                                            <td>regional</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
