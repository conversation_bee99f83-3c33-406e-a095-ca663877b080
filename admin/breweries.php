<?php
/**
 * Places Management
 * Manage all types of places (breweries, restaurants, pubs, etc.) with contact info
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Places Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css', '../assets/css/places-management.css'];
$bodyClass = 'places-management';

// Handle actions
$action = $_GET['action'] ?? 'list';
$placeId = $_GET['id'] ?? null;

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_place') {
        handleUpdatePlace($pdo);
    } elseif ($action === 'create_place') {
        handleCreatePlace($pdo);
    }
}

// Handler functions
function handleUpdatePlace($pdo) {
    $placeId = $_POST['place_id'] ?? null;

    if (!$placeId) {
        $_SESSION['error_message'] = 'Place ID is required';
        return;
    }

    try {
        $stmt = $pdo->prepare("
            UPDATE breweries SET
                name = ?, description = ?, brewery_type = ?, website = ?,
                phone = ?, email = ?, address = ?,
                city = ?, state = ?, zip = ?,
                latitude = ?, longitude = ?, verified = ?, claimed = ?,
                updated_at = NOW()
            WHERE id = ?
        ");

        $stmt->execute([
            $_POST['name'] ?? '',
            $_POST['description'] ?? '',
            $_POST['brewery_type'] ?? 'brewery',
            $_POST['website'] ?? '',
            $_POST['phone'] ?? '',
            $_POST['email'] ?? '',
            $_POST['address'] ?? '',
            $_POST['city'] ?? '',
            $_POST['state'] ?? '',
            $_POST['zip'] ?? '',
            $_POST['latitude'] ?? null,
            $_POST['longitude'] ?? null,
            isset($_POST['verified']) ? 1 : 0,
            isset($_POST['claimed']) ? 1 : 0,
            $placeId
        ]);

        $_SESSION['success_message'] = 'Place updated successfully!';

    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Error updating place: ' . $e->getMessage();
    }
}

function handleCreatePlace($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO breweries (
                name, description, brewery_type, website, phone, email,
                address, city, state, zip,
                latitude, longitude, verified, claimed, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ");

        $stmt->execute([
            $_POST['name'] ?? '',
            $_POST['description'] ?? '',
            $_POST['brewery_type'] ?? 'brewery',
            $_POST['website'] ?? '',
            $_POST['phone'] ?? '',
            $_POST['email'] ?? '',
            $_POST['address'] ?? '',
            $_POST['city'] ?? '',
            $_POST['state'] ?? '',
            $_POST['zip'] ?? '',
            $_POST['latitude'] ?? null,
            $_POST['longitude'] ?? null,
            isset($_POST['verified']) ? 1 : 0,
            isset($_POST['claimed']) ? 1 : 0
        ]);

        $_SESSION['success_message'] = 'Place created successfully!';

    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Error creating place: ' . $e->getMessage();
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$type_filter = $_GET['type'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(10, min(100, intval($_GET['limit'] ?? 20)));
$offset = ($page - 1) * $limit;

// Get places list with filtering
$places = [];
$totalPlaces = 0;

try {
    // Build WHERE clause
    $where_conditions = [];
    $params = [];

    if ($search) {
        $where_conditions[] = "(name LIKE ? OR city LIKE ? OR state LIKE ? OR email LIKE ? OR phone LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }

    if ($type_filter) {
        $where_conditions[] = "brewery_type = ?";
        $params[] = $type_filter;
    }

    if ($status_filter) {
        switch ($status_filter) {
            case 'verified':
                $where_conditions[] = "verified = 1";
                break;
            case 'claimed':
                $where_conditions[] = "claimed = 1 AND verified = 0";
                break;
            case 'unclaimed':
                $where_conditions[] = "claimed = 0";
                break;
        }
    }

    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Get total count
    $count_sql = "SELECT COUNT(*) as count FROM breweries $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $totalPlaces = $stmt->fetch()['count'];

    // Get places for current page
    $stmt = $pdo->prepare("
        SELECT id, name, city, state, brewery_type, claimed, verified, created_at,
               follower_count, phone, email, website, address, description
        FROM breweries
        $where_clause
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ");
    // Bind parameters
    $param_index = 1;
    foreach ($params as $param) {
        $stmt->bindValue($param_index++, $param);
    }

    // Bind LIMIT and OFFSET as integers
    $stmt->bindValue($param_index++, (int)$limit, PDO::PARAM_INT);
    $stmt->bindValue($param_index, (int)$offset, PDO::PARAM_INT);

    $stmt->execute();
    $places = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get statistics
    $stats = [];
    $stmt = $pdo->query("SELECT COUNT(*) FROM breweries");
    $stats['total'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM breweries WHERE verified = 1");
    $stats['verified'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM breweries WHERE claimed = 1");
    $stats['claimed'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(DISTINCT brewery_type) FROM breweries");
    $stats['types'] = $stmt->fetchColumn();

} catch (Exception $e) {
    error_log("Places page error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading places data.';
    $stats = ['total' => 0, 'verified' => 0, 'claimed' => 0, 'types' => 0];
}

$totalPages = ceil($totalPlaces / $limit);

require_once '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Places Management</h1>
                <p class="text-muted mb-0 small">Manage all types of places with contact information</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline-primary modern-btn" onclick="window.location.href='brewery-import.php'">
                    <i class="fas fa-upload"></i>
                    <span class="btn-text">Import Places</span>
                </button>
            </div>
        </div>

        <div class="dashboard-content">
            <!-- Modern Action Bar -->
            <div class="action-bar d-flex justify-content-between align-items-center mb-3">
                <div class="action-bar-left">
                    <span class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Manage brewery and restaurant listings
                    </span>
                </div>
                <div class="action-bar-right">
                    <div class="btn-toolbar" role="toolbar">
                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-primary modern-btn" data-bs-toggle="modal" data-bs-target="#createPlaceModal">
                                <i class="fas fa-plus"></i>
                                <span class="btn-text">Add Place</span>
                            </button>
                        </div>
                        <div class="btn-group me-2" role="group">
                            <a href="import.php" class="btn btn-outline-primary modern-btn">
                                <i class="fas fa-upload"></i>
                                <span class="btn-text">Import</span>
                            </a>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-ghost modern-btn dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="export.php"><i class="fas fa-download me-2"></i>Export Data</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="window.debugModal && debugModal.show(); return false;"><i class="fas fa-bug me-2"></i>Debug Modal Issues</a></li>
                                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Statistics Cards - Ultra Compact -->
    <div class="row mb-2 stats-row g-2">
        <div class="col-3">
            <div class="card text-center stats-card-compact">
                <div class="card-body py-2 px-2">
                    <div class="h6 mb-0 text-primary"><?php echo number_format($stats['total']); ?></div>
                    <small class="text-muted">Total</small>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card text-center stats-card-compact">
                <div class="card-body py-2 px-2">
                    <div class="h6 mb-0 text-success"><?php echo number_format($stats['verified']); ?></div>
                    <small class="text-muted">Verified</small>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card text-center stats-card-compact">
                <div class="card-body py-2 px-2">
                    <div class="h6 mb-0 text-warning"><?php echo number_format($stats['claimed']); ?></div>
                    <small class="text-muted">Claimed</small>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card text-center stats-card-compact">
                <div class="card-body py-2 px-2">
                    <div class="h6 mb-0 text-info"><?php echo number_format($stats['types']); ?></div>
                    <small class="text-muted">Types</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search - Compact -->
    <div class="card mb-2">
        <div class="card-body py-2 px-3 filter-form-compact">
            <form method="GET" class="row g-2 align-items-end">
                <div class="col-md-4">
                    <input type="text" class="form-control form-control-sm" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="🔍 Search places (name, city, email, phone...)">
                </div>

                <div class="col-md-2">
                    <select class="form-select form-select-sm" id="type" name="type">
                        <option value="">📍 All Types</option>
                        <option value="brewery" <?php echo $type_filter === 'brewery' ? 'selected' : ''; ?>>🍺 Brewery</option>
                        <option value="restaurant" <?php echo $type_filter === 'restaurant' ? 'selected' : ''; ?>>🍽️ Restaurant</option>
                        <option value="pub" <?php echo $type_filter === 'pub' ? 'selected' : ''; ?>>🍻 Pub</option>
                        <option value="bar" <?php echo $type_filter === 'bar' ? 'selected' : ''; ?>>🥃 Bar</option>
                        <option value="taproom" <?php echo $type_filter === 'taproom' ? 'selected' : ''; ?>>🚰 Taproom</option>
                        <option value="distillery" <?php echo $type_filter === 'distillery' ? 'selected' : ''; ?>>🥃 Distillery</option>
                        <option value="winery" <?php echo $type_filter === 'winery' ? 'selected' : ''; ?>>🍷 Winery</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <select class="form-select form-select-sm" id="status" name="status">
                        <option value="">⚡ All Status</option>
                        <option value="verified" <?php echo $status_filter === 'verified' ? 'selected' : ''; ?>>✅ Verified</option>
                        <option value="claimed" <?php echo $status_filter === 'claimed' ? 'selected' : ''; ?>>🏢 Claimed</option>
                        <option value="unclaimed" <?php echo $status_filter === 'unclaimed' ? 'selected' : ''; ?>>📋 Unclaimed</option>
                    </select>
                </div>

                <div class="col-md-1">
                    <select class="form-select form-select-sm" id="limit" name="limit">
                        <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                    <a href="breweries.php" class="btn btn-outline-secondary btn-sm ms-1">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Places Table -->
    <div class="card">
        <div class="card-header table-header-actions d-flex justify-content-between align-items-center py-2">
            <h6 class="mb-0"><i class="fas fa-map-marker-alt me-1"></i>Places (<?php echo number_format($totalPlaces); ?>)</h6>
            <small class="text-muted">
                <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $limit, $totalPlaces)); ?> of <?php echo number_format($totalPlaces); ?>
            </small>
        </div>
        <div class="card-body p-0">
            <?php if (!empty($places)): ?>
                <div class="table-responsive">
                    <table class="table table-striped places-table places-table-compact">
                        <thead>
                            <tr>
                                <th>Place Info</th>
                                <th>Contact</th>
                                <th>Location</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($places as $place): ?>
                                <tr>
                                    <td>
                                        <div class="place-info">
                                            <strong class="place-name"><?php echo htmlspecialchars($place['name']); ?></strong>
                                            <?php if ($place['description']): ?>
                                                <div class="place-description">
                                                    <?php echo htmlspecialchars(substr($place['description'], 0, 100)); ?>
                                                    <?php if (strlen($place['description']) > 100): ?>...<?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="place-meta">
                                                <small class="text-muted">
                                                    <i class="fas fa-users me-1"></i><?php echo number_format($place['follower_count']); ?> followers
                                                    <span class="ms-2">
                                                        <i class="fas fa-calendar me-1"></i><?php echo date('M j, Y', strtotime($place['created_at'])); ?>
                                                    </span>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="contact-info">
                                            <?php if ($place['phone']): ?>
                                                <div class="contact-item">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <a href="tel:<?php echo htmlspecialchars($place['phone']); ?>">
                                                        <?php echo htmlspecialchars($place['phone']); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($place['email']): ?>
                                                <div class="contact-item">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    <a href="mailto:<?php echo htmlspecialchars($place['email']); ?>">
                                                        <?php echo htmlspecialchars($place['email']); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($place['website']): ?>
                                                <div class="contact-item">
                                                    <i class="fas fa-globe me-1"></i>
                                                    <a href="<?php echo htmlspecialchars($place['website']); ?>" target="_blank">
                                                        Website
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="location-info">
                                            <?php if ($place['address']): ?>
                                                <div><?php echo htmlspecialchars($place['address']); ?></div>
                                            <?php endif; ?>
                                            <div>
                                                <?php echo htmlspecialchars($place['city']); ?>
                                                <?php if ($place['state']): ?>, <?php echo htmlspecialchars($place['state']); ?><?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo ucfirst(str_replace('_', ' ', $place['brewery_type'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="status-badges">
                                            <?php if ($place['verified']): ?>
                                                <span class="badge bg-success mb-1">Verified</span>
                                            <?php elseif ($place['claimed']): ?>
                                                <span class="badge bg-warning mb-1">Claimed</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary mb-1">Unclaimed</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-table-primary" onclick="editPlace('<?php echo $place['id']; ?>')" title="Edit Place">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-table-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" title="More Actions">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <a class="dropdown-item" href="../place.php?id=<?php echo $place['id']; ?>" target="_blank">
                                                            <i class="fas fa-eye me-2"></i>View Place
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="duplicatePlace('<?php echo $place['id']; ?>')">
                                                            <i class="fas fa-copy me-2"></i>Duplicate
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="#" onclick="deletePlace('<?php echo $place['id']; ?>')">
                                                            <i class="fas fa-trash me-2"></i>Delete
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Places pagination" class="mt-2 px-3 pb-2">
                        <ul class="pagination pagination-sm justify-content-center mb-0">
                            <?php
                            $query_params = $_GET;
                            unset($query_params['page']);
                            $base_url = 'breweries.php?' . http_build_query($query_params);
                            ?>

                            <!-- Previous -->
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                            </li>

                            <!-- Page numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($totalPages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>

                            <!-- Next -->
                            <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No places found</h6>
                    <p class="text-muted">Try adjusting your filters or add some places.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlaceModal">
                        <i class="fas fa-plus me-2"></i>Add New Place
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create Place Modal -->
<div class="modal" id="createPlaceModal" tabindex="-1" data-bs-backdrop="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Place</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="createPlaceForm">
                <input type="hidden" name="action" value="create_place">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="create_name" class="form-label">Name *</label>
                            <input type="text" class="form-control" id="create_name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="create_brewery_type" class="form-label">Type *</label>
                            <select class="form-select" id="create_brewery_type" name="brewery_type" required>
                                <option value="">Select Type...</option>
                                <optgroup label="Breweries & Beer">
                                    <option value="brewery">Brewery</option>
                                    <option value="microbrewery">Microbrewery</option>
                                    <option value="brewpub">Brewpub</option>
                                    <option value="taproom">Taproom</option>
                                    <option value="beer_garden">Beer Garden</option>
                                </optgroup>
                                <optgroup label="Restaurants & Food">
                                    <option value="restaurant">Restaurant</option>
                                    <option value="gastropub">Gastropub</option>
                                    <option value="sports_bar">Sports Bar</option>
                                    <option value="pizza_place">Pizza Place</option>
                                    <option value="food_truck">Food Truck</option>
                                </optgroup>
                                <optgroup label="Bars & Pubs">
                                    <option value="pub">Pub</option>
                                    <option value="bar">Bar</option>
                                    <option value="cocktail_bar">Cocktail Bar</option>
                                    <option value="wine_bar">Wine Bar</option>
                                    <option value="dive_bar">Dive Bar</option>
                                </optgroup>
                                <optgroup label="Other Beverages">
                                    <option value="distillery">Distillery</option>
                                    <option value="winery">Winery</option>
                                    <option value="cidery">Cidery</option>
                                    <option value="meadery">Meadery</option>
                                    <option value="party_store">Party Store</option>
                                </optgroup>
                                <optgroup label="Entertainment">
                                    <option value="nightclub">Nightclub</option>
                                    <option value="lounge">Lounge</option>
                                    <option value="event_venue">Event Venue</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="create_description" class="form-label">Description</label>
                            <textarea class="form-control" id="create_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="col-md-4">
                            <label for="create_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="create_phone" name="phone">
                        </div>
                        <div class="col-md-4">
                            <label for="create_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="create_email" name="email">
                        </div>
                        <div class="col-md-4">
                            <label for="create_website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="create_website" name="website">
                        </div>
                        <div class="col-md-12">
                            <label for="create_address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="create_address" name="address">
                        </div>
                        <div class="col-md-4">
                            <label for="create_city" class="form-label">City</label>
                            <input type="text" class="form-control" id="create_city" name="city">
                        </div>
                        <div class="col-md-4">
                            <label for="create_state" class="form-label">State</label>
                            <input type="text" class="form-control" id="create_state" name="state">
                        </div>
                        <div class="col-md-4">
                            <label for="create_zip" class="form-label">ZIP Code</label>
                            <input type="text" class="form-control" id="create_zip" name="zip">
                        </div>
                        <div class="col-md-6">
                            <label for="create_latitude" class="form-label">Latitude</label>
                            <input type="number" class="form-control" id="create_latitude" name="latitude" step="any">
                        </div>
                        <div class="col-md-6">
                            <label for="create_longitude" class="form-label">Longitude</label>
                            <input type="number" class="form-control" id="create_longitude" name="longitude" step="any">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="create_verified" name="verified" value="1">
                                <label class="form-check-label" for="create_verified">Verified</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="create_claimed" name="claimed" value="1">
                                <label class="form-check-label" for="create_claimed">Claimed</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Create Place</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Place Modal -->
<div class="modal" id="editPlaceModal" tabindex="-1" data-bs-backdrop="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Place</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPlaceForm">
                <input type="hidden" name="action" value="update_place">
                <input type="hidden" name="place_id" id="edit_place_id">
                <div class="modal-body">
                    <!-- Same form fields as create, but with edit_ prefixes -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_name" class="form-label">Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_brewery_type" class="form-label">Type *</label>
                            <select class="form-select" id="edit_brewery_type" name="brewery_type" required>
                                <option value="">Select Type...</option>
                                <optgroup label="Breweries & Beer">
                                    <option value="brewery">Brewery</option>
                                    <option value="microbrewery">Microbrewery</option>
                                    <option value="brewpub">Brewpub</option>
                                    <option value="taproom">Taproom</option>
                                    <option value="beer_garden">Beer Garden</option>
                                </optgroup>
                                <optgroup label="Restaurants & Food">
                                    <option value="restaurant">Restaurant</option>
                                    <option value="gastropub">Gastropub</option>
                                    <option value="sports_bar">Sports Bar</option>
                                    <option value="pizza_place">Pizza Place</option>
                                    <option value="food_truck">Food Truck</option>
                                </optgroup>
                                <optgroup label="Bars & Pubs">
                                    <option value="pub">Pub</option>
                                    <option value="bar">Bar</option>
                                    <option value="cocktail_bar">Cocktail Bar</option>
                                    <option value="wine_bar">Wine Bar</option>
                                    <option value="dive_bar">Dive Bar</option>
                                </optgroup>
                                <optgroup label="Other Beverages">
                                    <option value="distillery">Distillery</option>
                                    <option value="winery">Winery</option>
                                    <option value="cidery">Cidery</option>
                                    <option value="meadery">Meadery</option>
                                    <option value="party_store">Party Store</option>
                                </optgroup>
                                <optgroup label="Entertainment">
                                    <option value="nightclub">Nightclub</option>
                                    <option value="lounge">Lounge</option>
                                    <option value="event_venue">Event Venue</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="edit_phone" name="phone">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="edit_website" name="website">
                        </div>
                        <div class="col-md-12">
                            <label for="edit_address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="edit_address" name="address">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_city" class="form-label">City</label>
                            <input type="text" class="form-control" id="edit_city" name="city">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_state" class="form-label">State</label>
                            <input type="text" class="form-control" id="edit_state" name="state">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_zip" class="form-label">ZIP Code</label>
                            <input type="text" class="form-control" id="edit_zip" name="zip">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_latitude" class="form-label">Latitude</label>
                            <input type="number" class="form-control" id="edit_latitude" name="latitude" step="any">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_longitude" class="form-label">Longitude</label>
                            <input type="number" class="form-control" id="edit_longitude" name="longitude" step="any">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_verified" name="verified" value="1">
                                <label class="form-check-label" for="edit_verified">Verified</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_claimed" name="claimed" value="1">
                                <label class="form-check-label" for="edit_claimed">Claimed</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Place</button>
                </div>
            </form>
        </div>
    </div>
        </div>

<script src="../assets/js/places-management.js"></script>
<script src="../assets/js/debug-modal.js"></script>

<?php
include 'includes/admin-layout-end.php';
require_once '../includes/footer.php';
?>
