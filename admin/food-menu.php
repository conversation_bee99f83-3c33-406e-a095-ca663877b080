<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Food Menu Management - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Sample food menu data (replace with database queries)
$food_categories = [
    ['id' => 1, 'name' => 'Appetizers', 'items_count' => 12],
    ['id' => 2, 'name' => 'Main Courses', 'items_count' => 18],
    ['id' => 3, 'name' => 'Desserts', 'items_count' => 8],
    ['id' => 4, 'name' => 'Sides', 'items_count' => 10]
];

$recent_food_items = [
    ['id' => 1, 'name' => 'Buffalo Wings', 'category' => 'Appetizers', 'price' => 12.99, 'place' => 'Craft Masters Brewery'],
    ['id' => 2, 'name' => 'Beer Battered Fish & Chips', 'category' => 'Main Courses', 'price' => 16.99, 'place' => 'The Hoppy Place'],
    ['id' => 3, 'name' => 'Chocolate Stout Cake', 'category' => 'Desserts', 'price' => 8.99, 'place' => 'Sunshine Beer Garden'],
    ['id' => 4, 'name' => 'Loaded Nachos', 'category' => 'Appetizers', 'price' => 14.99, 'place' => 'Craft Masters Brewery']
];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <div class="header-left">
                <button class="sidebar-toggle-btn" onclick="toggleSidebarCollapse()" title="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <h1><i class="fas fa-utensils me-2"></i>Food Menu Management</h1>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addFoodItemModal">
                    <i class="fas fa-plus me-1"></i>Add Food Item
                </button>
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-upload me-1"></i>Import CSV
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card stat-primary">
                    <div class="stat-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">48</div>
                        <div class="stat-label">Total Food Items</div>
                    </div>
                    <a href="#" class="stat-link">View All <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-success">
                    <div class="stat-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo count($food_categories); ?></div>
                        <div class="stat-label">Food Categories</div>
                    </div>
                    <a href="#" class="stat-link">Manage <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-warning">
                    <div class="stat-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">15</div>
                        <div class="stat-label">Places with Menus</div>
                    </div>
                    <a href="#" class="stat-link">View Places <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-danger">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Pending Reviews</div>
                    </div>
                    <a href="#" class="stat-link">Review <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            
            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Food Categories -->
                <div class="content-card">
                    <div class="card-header">
                        <h5><i class="fas fa-folder me-2"></i>Food Categories</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Items</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($food_categories as $category): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($category['name']); ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $category['items_count']; ?></span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Food Items -->
                <div class="content-card">
                    <div class="card-header">
                        <h5><i class="fas fa-utensils me-2"></i>Recent Food Items</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Place</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_food_items as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['name']); ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($item['category']); ?></span>
                                            </td>
                                            <td>$<?php echo number_format($item['price'], 2); ?></td>
                                            <td>
                                                <small><?php echo htmlspecialchars($item['place']); ?></small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<!-- Add Food Item Modal -->
<div class="modal fade" id="addFoodItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Food Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">Item Name</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <select class="form-select" required>
                            <option value="">Select Category</option>
                            <?php foreach ($food_categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Price</label>
                        <input type="number" class="form-control" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Add Item</button>
            </div>
        </div>
    </div>
</div>

<?php 
include 'includes/admin-layout-end.php';
include '../includes/footer.php'; 
?>
