<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Coupons & Deals Management - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Sample coupons data (replace with database queries)
$coupons = [
    ['id' => 1, 'title' => 'Happy Hour Special', 'discount' => '20%', 'type' => 'percentage', 'place' => 'Craft Masters Brewery', 'status' => 'active', 'expires' => '2024-02-15'],
    ['id' => 2, 'title' => 'Free Appetizer', 'discount' => '$10', 'type' => 'fixed', 'place' => 'The Hoppy Place', 'status' => 'active', 'expires' => '2024-02-20'],
    ['id' => 3, 'title' => 'Buy 2 Get 1 Free', 'discount' => 'BOGO', 'type' => 'special', 'place' => 'Sunshine Beer Garden', 'status' => 'active', 'expires' => '2024-02-25'],
    ['id' => 4, 'title' => 'Weekend Special', 'discount' => '15%', 'type' => 'percentage', 'place' => 'Craft Masters Brewery', 'status' => 'expired', 'expires' => '2024-01-15']
];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-tags me-2"></i>Coupons & Deals Management</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                    <i class="fas fa-plus me-1"></i>Add Coupon
                </button>
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-chart-bar me-1"></i>View Analytics
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card stat-primary">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo count($coupons); ?></div>
                        <div class="stat-label">Total Coupons</div>
                    </div>
                    <a href="#" class="stat-link">View All <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-success">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Active Coupons</div>
                    </div>
                    <a href="#" class="stat-link">Manage <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-warning">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">2</div>
                        <div class="stat-label">Expiring Soon</div>
                    </div>
                    <a href="#" class="stat-link">Review <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-danger">
                    <div class="stat-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1</div>
                        <div class="stat-label">Expired</div>
                    </div>
                    <a href="#" class="stat-link">Clean Up <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            
            <!-- Coupons Table -->
            <div class="content-card">
                <div class="card-header">
                    <h5><i class="fas fa-tags me-2"></i>All Coupons & Deals</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Discount</th>
                                    <th>Type</th>
                                    <th>Place</th>
                                    <th>Status</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($coupons as $coupon): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($coupon['title']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($coupon['discount']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo ucfirst($coupon['type']); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($coupon['place']); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $coupon['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($coupon['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($coupon['expires'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="View Stats">
                                                    <i class="fas fa-chart-bar"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

<!-- Add Coupon Modal -->
<div class="modal fade" id="addCouponModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Coupon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">Coupon Title</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Discount Type</label>
                        <select class="form-select" required>
                            <option value="">Select Type</option>
                            <option value="percentage">Percentage</option>
                            <option value="fixed">Fixed Amount</option>
                            <option value="special">Special Offer</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Discount Value</label>
                        <input type="text" class="form-control" placeholder="e.g., 20% or $10" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Place</label>
                        <select class="form-select" required>
                            <option value="">Select Place</option>
                            <option value="1">Craft Masters Brewery</option>
                            <option value="2">The Hoppy Place</option>
                            <option value="3">Sunshine Beer Garden</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expiration Date</label>
                        <input type="date" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Add Coupon</button>
            </div>
        </div>
    </div>
</div>

<?php 
include 'includes/admin-layout-end.php';
include '../includes/footer.php'; 
?>
