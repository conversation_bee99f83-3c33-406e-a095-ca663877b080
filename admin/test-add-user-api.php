<?php
/**
 * Test the user-api.php create functionality directly
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Simulate the exact data that would be sent from the form
$testData = [
    'action' => 'create',
    'email' => 'testuser' . time() . '@example.com',
    'password' => 'password123',
    'confirm_password' => 'password123',
    'username' => 'testuser' . time(),
    'first_name' => 'Test',
    'last_name' => 'User',
    'role' => 'user',
    'status' => 'active'
];

echo "<h2>Testing User Creation API</h2>";
echo "<h3>Test Data:</h3>";
echo "<pre>";
print_r($testData);
echo "</pre>";

try {
    // Include the user-api functions
    require_once '../config/config.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // Test the createUser function directly
    echo "<h3>Testing createUser function:</h3>";
    
    // Check what columns exist first
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<h4>Available columns:</h4>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Validate required fields
    if (empty($testData['email'])) {
        throw new Exception("Email is required");
    }
    if (empty($testData['password'])) {
        throw new Exception("Password is required");
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$testData['email']]);
    if ($stmt->fetch()) {
        throw new Exception('Email already exists');
    }

    // Hash password
    $hashedPassword = password_hash($testData['password'], PASSWORD_DEFAULT);

    // Build INSERT based on available columns
    $password_field = in_array('password', $columns) ? 'password' : 'password_hash';
    $insertFields = ['email', $password_field];
    $insertValues = [$testData['email'], $hashedPassword];
    $placeholders = ['?', '?'];

    if (in_array('username', $columns) && !empty($testData['username'])) {
        $insertFields[] = 'username';
        $insertValues[] = $testData['username'];
        $placeholders[] = '?';
    }
    if (in_array('first_name', $columns)) {
        $insertFields[] = 'first_name';
        $insertValues[] = $testData['first_name'] ?? '';
        $placeholders[] = '?';
    }
    if (in_array('last_name', $columns)) {
        $insertFields[] = 'last_name';
        $insertValues[] = $testData['last_name'] ?? '';
        $placeholders[] = '?';
    }
    if (in_array('role', $columns)) {
        $insertFields[] = 'role';
        $insertValues[] = $testData['role'] ?? 'user';
        $placeholders[] = '?';
    }
    if (in_array('status', $columns)) {
        $insertFields[] = 'status';
        $insertValues[] = $testData['status'] ?? 'active';
        $placeholders[] = '?';
    }
    if (in_array('email_verified', $columns)) {
        $insertFields[] = 'email_verified';
        $insertValues[] = 0;
        $placeholders[] = '?';
    }
    if (in_array('created_at', $columns)) {
        $insertFields[] = 'created_at';
        $placeholders[] = 'NOW()';
    }
    if (in_array('updated_at', $columns)) {
        $insertFields[] = 'updated_at';
        $placeholders[] = 'NOW()';
    }

    $sql = "INSERT INTO users (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<h4>Generated SQL:</h4>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<h4>Values to insert:</h4>";
    echo "<pre>";
    print_r($insertValues);
    echo "</pre>";
    
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute($insertValues);
    
    if ($result) {
        $userId = $conn->lastInsertId();
        echo "<div class='alert alert-success'>✅ User created successfully! User ID: " . $userId . "</div>";
        
        // Verify the user was created
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h4>Created user data:</h4>";
        echo "<pre>";
        print_r($user);
        echo "</pre>";
    } else {
        echo "<div class='alert alert-danger'>❌ Failed to create user</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<h4>Stack trace:</h4>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Add User API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-3">
        <a href="user-management.php" class="btn btn-secondary">← Back to User Management</a>
    </div>
</body>
</html>
