<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Test Buttons';
$additionalCSS = ['../assets/css/admin.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>🧪 Test Action Buttons</h1>
            
            <div class="card">
                <div class="card-body">
                    <h5>Test Buttons</h5>
                    
                    <button type="button" class="btn btn-primary me-2" onclick="testEditUser(1)">
                        Test Edit User
                    </button>
                    
                    <button type="button" class="btn btn-info me-2" onclick="testViewUser(1)">
                        Test View User
                    </button>
                    
                    <button type="button" class="btn btn-warning me-2" onclick="testSuspendUser(1)">
                        Test Suspend User
                    </button>
                    
                    <button type="button" class="btn btn-success me-2" onclick="testActivateUser(1)">
                        Test Activate User
                    </button>
                    
                    <div id="testResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal -->
<div class="modal" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>This is a test modal to verify Bootstrap is working.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function log(message) {
    const results = document.getElementById('testResults');
    results.innerHTML += '<div class="alert alert-info">' + message + '</div>';
    console.log(message);
}

function testEditUser(userId) {
    log('Testing edit user with ID: ' + userId);
    
    fetch(`user-api.php?action=get&id=${userId}`)
        .then(response => {
            log('Response status: ' + response.status);
            return response.json();
        })
        .then(data => {
            log('Response data: ' + JSON.stringify(data));
            
            if (data.success) {
                log('✅ API call successful');
                
                // Test modal
                try {
                    if (typeof bootstrap !== 'undefined') {
                        log('✅ Bootstrap is available');
                        new bootstrap.Modal(document.getElementById('testModal')).show();
                    } else {
                        log('❌ Bootstrap not available');
                    }
                } catch (e) {
                    log('❌ Modal error: ' + e.message);
                }
            } else {
                log('❌ API error: ' + data.message);
            }
        })
        .catch(error => {
            log('❌ Fetch error: ' + error.message);
        });
}

function testViewUser(userId) {
    log('Testing view user with ID: ' + userId);
    
    fetch(`user-api.php?action=view&id=${userId}`)
        .then(response => response.json())
        .then(data => {
            log('View response: ' + JSON.stringify(data, null, 2));
        })
        .catch(error => {
            log('❌ View error: ' + error.message);
        });
}

function testSuspendUser(userId) {
    log('Testing suspend user with ID: ' + userId);
    
    fetch('user-api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            user_id: userId,
            status: 'suspended'
        })
    })
    .then(response => response.json())
    .then(data => {
        log('Suspend response: ' + JSON.stringify(data));
    })
    .catch(error => {
        log('❌ Suspend error: ' + error.message);
    });
}

function testActivateUser(userId) {
    log('Testing activate user with ID: ' + userId);
    
    fetch('user-api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            user_id: userId,
            status: 'active'
        })
    })
    .then(response => response.json())
    .then(data => {
        log('Activate response: ' + JSON.stringify(data));
    })
    .catch(error => {
        log('❌ Activate error: ' + error.message);
    });
}

// Test Bootstrap availability on page load
document.addEventListener('DOMContentLoaded', function() {
    if (typeof bootstrap !== 'undefined') {
        log('✅ Bootstrap is loaded and available');
    } else {
        log('❌ Bootstrap is not available');
    }
    
    if (typeof $ !== 'undefined') {
        log('✅ jQuery is available');
    } else {
        log('❌ jQuery is not available');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
