<?php
/**
 * Admin Analytics Dashboard
 * Phase 8: Analytics & Business Intelligence
 */

require_once '../config/config.php';
require_once '../includes/AnalyticsService.php';

// Require admin role
requireRole('admin');

$pageTitle = 'Platform Analytics - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get timeframe from request
$timeframe = sanitizeInput($_GET['timeframe'] ?? '1_year');
$validTimeframes = ['1_month', '3_months', '6_months', '1_year', 'all_time'];
if (!in_array($timeframe, $validTimeframes)) {
    $timeframe = '1_year';
}

$platformStats = [];
$userEngagement = [];
$beerTrends = [];
$breweryMetrics = [];
$geographicData = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    $analyticsService = new AnalyticsService($conn);
    
    // Get platform-wide statistics
    $platformStats = $analyticsService->getPlatformStatistics($timeframe);
    $userEngagement = $analyticsService->getUserEngagementMetrics($timeframe);
    $beerTrends = $analyticsService->getBeerTrends($timeframe);
    $breweryMetrics = $analyticsService->getBreweryPerformanceMetrics($timeframe);
    $geographicData = $analyticsService->getGeographicUsagePatterns($timeframe);
    
} catch (Exception $e) {
    error_log("Admin analytics error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading analytics data.';
}

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-chart-line me-2"></i>Platform Analytics</h1>
            <div class="header-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download me-1"></i>Export Report
                </button>
            </div>
        </div>

        <div class="dashboard-content">
        </div>
        <div class="col-lg-4 d-flex align-items-center justify-content-end">
            <div class="d-flex gap-2">
                <!-- Timeframe Selector -->
                <select class="form-select" id="timeframe-selector" onchange="changeTimeframe()">
                    <option value="1_month" <?php echo $timeframe === '1_month' ? 'selected' : ''; ?>>Last Month</option>
                    <option value="3_months" <?php echo $timeframe === '3_months' ? 'selected' : ''; ?>>Last 3 Months</option>
                    <option value="6_months" <?php echo $timeframe === '6_months' ? 'selected' : ''; ?>>Last 6 Months</option>
                    <option value="1_year" <?php echo $timeframe === '1_year' ? 'selected' : ''; ?>>Last Year</option>
                    <option value="all_time" <?php echo $timeframe === 'all_time' ? 'selected' : ''; ?>>All Time</option>
                </select>
                <a href="dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Platform Overview Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h3 class="card-title"><?php echo number_format($platformStats['total_users'] ?? 0); ?></h3>
                    <p class="card-text text-muted">Total Users</p>
                    <small class="text-success">
                        +<?php echo number_format($platformStats['new_users'] ?? 0); ?> this period
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-beer fa-2x"></i>
                    </div>
                    <h3 class="card-title"><?php echo number_format($platformStats['total_checkins'] ?? 0); ?></h3>
                    <p class="card-text text-muted">Total Check-ins</p>
                    <small class="text-success">
                        +<?php echo number_format($platformStats['new_checkins'] ?? 0); ?> this period
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                    <h3 class="card-title"><?php echo number_format($platformStats['total_ratings'] ?? 0); ?></h3>
                    <p class="card-text text-muted">Total Ratings</p>
                    <small class="text-success">
                        +<?php echo number_format($platformStats['new_ratings'] ?? 0); ?> this period
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-industry fa-2x"></i>
                    </div>
                    <h3 class="card-title"><?php echo number_format($platformStats['active_breweries'] ?? 0); ?></h3>
                    <p class="card-text text-muted">Active Breweries</p>
                    <small class="text-success">
                        +<?php echo number_format($platformStats['new_breweries'] ?? 0); ?> this period
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- User Engagement Metrics -->
    <?php if (!empty($userEngagement)): ?>
    <div class="row g-4 mb-5">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>User Engagement Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="engagementChart" width="600" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-percentage me-2"></i>Engagement Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Daily Active Users</span>
                            <strong><?php echo number_format($userEngagement['dau'] ?? 0); ?></strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-primary" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Monthly Active Users</span>
                            <strong><?php echo number_format($userEngagement['mau'] ?? 0); ?></strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-success" style="width: 85%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>User Retention Rate</span>
                            <strong><?php echo number_format($userEngagement['retention_rate'] ?? 0, 1); ?>%</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-warning" style="width: <?php echo $userEngagement['retention_rate'] ?? 0; ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="d-flex justify-content-between">
                            <span>Avg. Session Duration</span>
                            <strong><?php echo number_format($userEngagement['avg_session_duration'] ?? 0); ?>m</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-info" style="width: 60%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Beer Trends -->
    <?php if (!empty($beerTrends)): ?>
    <div class="row g-4 mb-5">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2"></i>Trending Beer Styles
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="beerStylesChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>Top Rated Beers
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Beer</th>
                                    <th>Brewery</th>
                                    <th>Rating</th>
                                    <th>Reviews</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($beerTrends['top_rated'] ?? [], 0, 10) as $beer): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($beer['beer_name']); ?></td>
                                    <td><?php echo htmlspecialchars($beer['brewery_name']); ?></td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <?php echo number_format($beer['avg_rating'], 1); ?>/5
                                        </span>
                                    </td>
                                    <td><?php echo number_format($beer['review_count']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Brewery Performance -->
    <?php if (!empty($breweryMetrics)): ?>
    <div class="row g-4 mb-5">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Brewery Performance
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="breweryChart" width="600" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>Top Breweries
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach (array_slice($breweryMetrics['top_breweries'] ?? [], 0, 5) as $index => $brewery): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <span class="badge bg-primary rounded-pill"><?php echo $index + 1; ?></span>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold"><?php echo htmlspecialchars($brewery['name']); ?></div>
                            <small class="text-muted">
                                <?php echo number_format($brewery['total_checkins']); ?> check-ins • 
                                <?php echo number_format($brewery['avg_rating'], 1); ?>/5 rating
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Geographic Usage -->
    <?php if (!empty($geographicData)): ?>
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-globe me-2"></i>Geographic Usage Patterns
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div id="geographic-map" style="height: 400px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center">
                                    <i class="fas fa-map fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Interactive map coming soon</p>
                                    <small class="text-muted">Geographic visualization will be implemented in future updates</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <h6 class="fw-bold mb-3">Top Locations</h6>
                            <?php foreach (array_slice($geographicData['top_locations'] ?? [], 0, 8) as $location): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong><?php echo htmlspecialchars($location['city']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($location['state']); ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary"><?php echo number_format($location['user_count']); ?></span>
                                    <br><small class="text-muted"><?php echo number_format($location['checkin_count']); ?> check-ins</small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Chart.js for analytics charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Timeframe selector functionality
function changeTimeframe() {
    const timeframe = document.getElementById('timeframe-selector').value;
    const url = new URL(window.location);
    url.searchParams.set('timeframe', timeframe);
    window.location.href = url.toString();
}

// Chart data from PHP
const platformData = {
    userEngagement: <?php echo json_encode($userEngagement); ?>,
    beerTrends: <?php echo json_encode($beerTrends); ?>,
    breweryMetrics: <?php echo json_encode($breweryMetrics); ?>
};

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializePlatformCharts();
});

function initializePlatformCharts() {
    // User engagement chart
    if (platformData.userEngagement && platformData.userEngagement.daily_activity) {
        const engagementCtx = document.getElementById('engagementChart');
        if (engagementCtx) {
            new Chart(engagementCtx, {
                type: 'line',
                data: {
                    labels: platformData.userEngagement.daily_activity.map(d => d.date),
                    datasets: [{
                        label: 'Daily Active Users',
                        data: platformData.userEngagement.daily_activity.map(d => d.active_users),
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'New Registrations',
                        data: platformData.userEngagement.daily_activity.map(d => d.new_registrations),
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }
    
    // Beer styles chart
    if (platformData.beerTrends && platformData.beerTrends.popular_styles) {
        const stylesCtx = document.getElementById('beerStylesChart');
        if (stylesCtx) {
            new Chart(stylesCtx, {
                type: 'doughnut',
                data: {
                    labels: platformData.beerTrends.popular_styles.map(d => d.style_name),
                    datasets: [{
                        data: platformData.beerTrends.popular_styles.map(d => d.checkin_count),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }
    
    // Brewery performance chart
    if (platformData.breweryMetrics && platformData.breweryMetrics.monthly_performance) {
        const breweryCtx = document.getElementById('breweryChart');
        if (breweryCtx) {
            new Chart(breweryCtx, {
                type: 'bar',
                data: {
                    labels: platformData.breweryMetrics.monthly_performance.map(d => d.month),
                    datasets: [{
                        label: 'Total Check-ins',
                        data: platformData.breweryMetrics.monthly_performance.map(d => d.total_checkins),
                        backgroundColor: 'rgba(248, 181, 0, 0.6)',
                        borderColor: 'rgba(248, 181, 0, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }
}
</script>

<style>
.metric-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.metric-item:last-child {
    border-bottom: none;
}

.progress {
    height: 4px;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
}
</style>

        </div>

<?php
include 'includes/admin-layout-end.php';
include '../includes/footer.php';
?>
