<?php
/**
 * Coupon Management System
 * Manage coupons and deals for places
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Coupon Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css', '../assets/css/coupon-management.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get filter parameters
$place_id = $_GET['place_id'] ?? '';
$category_id = $_GET['category_id'] ?? '';
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(10, min(100, intval($_GET['limit'] ?? 20)));
$offset = ($page - 1) * $limit;

// Get places for selection
try {
    $stmt = $pdo->query("SELECT id, name, city, state FROM breweries ORDER BY name LIMIT 200");
    $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $places = [];
}

// Get categories for selection
try {
    $stmt = $pdo->query("SELECT id, name, color FROM coupon_categories WHERE is_active = 1 ORDER BY sort_order, name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
}

// Build coupon query
$where_conditions = [];
$params = [];

if ($place_id) {
    $where_conditions[] = "c.place_id = ?";
    $params[] = $place_id;
}

if ($category_id) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category_id;
}

if ($status !== 'all') {
    switch ($status) {
        case 'active':
            $where_conditions[] = "c.is_active = 1 AND c.start_date <= CURDATE() AND c.end_date >= CURDATE()";
            break;
        case 'inactive':
            $where_conditions[] = "c.is_active = 0";
            break;
        case 'expired':
            $where_conditions[] = "c.end_date < CURDATE()";
            break;
        case 'upcoming':
            $where_conditions[] = "c.start_date > CURDATE()";
            break;
        case 'pending':
            $where_conditions[] = "c.requires_approval = 1 AND c.approved_by IS NULL";
            break;
    }
}

if ($search) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR c.code LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get coupons with pagination
try {
    $sql = "
        SELECT 
            c.*,
            cc.name as category_name,
            cc.color as category_color,
            cc.icon as category_icon,
            b.name as place_name,
            b.city as place_city,
            b.state as place_state,
            u.email as created_by_email,
            a.email as approved_by_email,
            (SELECT COUNT(*) FROM coupon_redemptions WHERE coupon_id = c.id) as redemption_count,
            (SELECT COUNT(*) FROM coupon_views WHERE coupon_id = c.id) as view_count,
            (SELECT COUNT(*) FROM coupon_favorites WHERE coupon_id = c.id) as favorite_count
        FROM coupons c
        LEFT JOIN coupon_categories cc ON c.category_id = cc.id
        LEFT JOIN breweries b ON c.place_id = b.id
        LEFT JOIN users u ON c.created_by = u.id
        LEFT JOIN users a ON c.approved_by = a.id
        $where_clause
        ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $pdo->prepare($sql);

    // Bind parameters
    $param_index = 1;
    foreach ($params as $param) {
        $stmt->bindValue($param_index++, $param);
    }

    // Bind LIMIT and OFFSET as integers
    $stmt->bindValue($param_index++, (int)$limit, PDO::PARAM_INT);
    $stmt->bindValue($param_index, (int)$offset, PDO::PARAM_INT);

    $stmt->execute();
    $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM coupons c LEFT JOIN breweries b ON c.place_id = b.id $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_coupons = $count_stmt->fetchColumn();
    
    $total_pages = ceil($total_coupons / $limit);
    
} catch (PDOException $e) {
    $coupons = [];
    $total_coupons = 0;
    $total_pages = 0;
    $error_message = "Error loading coupons: " . $e->getMessage();
}

// Get statistics
try {
    $stats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
    $stats['total_coupons'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE is_active = 1 AND start_date <= CURDATE() AND end_date >= CURDATE()");
    $stats['active_coupons'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE end_date < CURDATE()");
    $stats['expired_coupons'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_redemptions");
    $stats['total_redemptions'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE requires_approval = 1 AND approved_by IS NULL");
    $stats['pending_approval'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT SUM(discount_amount) FROM coupon_redemptions WHERE status = 'completed'");
    $stats['total_savings'] = $stmt->fetchColumn() ?: 0;
    
} catch (PDOException $e) {
    $stats = [
        'total_coupons' => 0,
        'active_coupons' => 0,
        'expired_coupons' => 0,
        'total_redemptions' => 0,
        'pending_approval' => 0,
        'total_savings' => 0
    ];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-tags me-2"></i>Coupon Management
                    </h1>
                    <p class="text-muted mb-0">Manage deals and promotions for places</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo url('admin/coupon-categories.php'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-folder me-2"></i>Manage Categories
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createCouponModal">
                        <i class="fas fa-plus me-2"></i>Create Coupon
                    </button>
                    <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4 stats-row">
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['total_coupons']); ?></h5>
                    <p class="card-text small">Total Coupons</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['active_coupons']); ?></h5>
                    <p class="card-text small">Active Coupons</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['expired_coupons']); ?></h5>
                    <p class="card-text small">Expired Coupons</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['total_redemptions']); ?></h5>
                    <p class="card-text small">Total Redemptions</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['pending_approval']); ?></h5>
                    <p class="card-text small">Pending Approval</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">$<?php echo number_format($stats['total_savings'], 0); ?></h5>
                    <p class="card-text small">Total Savings</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>Filters & Search</h5>
        </div>
        <div class="card-body filter-form">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search Coupons</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Title, description, code...">
                </div>
                
                <div class="col-md-3">
                    <label for="place_id" class="form-label">Place</label>
                    <select class="form-select" id="place_id" name="place_id">
                        <option value="">All Places</option>
                        <?php foreach ($places as $place): ?>
                            <option value="<?php echo $place['id']; ?>" <?php echo $place_id === $place['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($place['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="category_id" class="form-label">Category</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $category_id === $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        <option value="expired" <?php echo $status === 'expired' ? 'selected' : ''; ?>>Expired</option>
                        <option value="upcoming" <?php echo $status === 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="limit" class="form-label">Per Page</label>
                    <select class="form-select" id="limit" name="limit">
                        <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
                
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Apply Filters
                    </button>
                    <a href="coupon-management.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Coupons Grid -->
    <div class="card">
        <div class="card-header table-header-actions d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-tags me-2"></i>Coupons (<?php echo number_format($total_coupons); ?> total)</h5>
            <div class="text-muted small">
                Showing <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $limit, $total_coupons)); ?>
                of <?php echo number_format($total_coupons); ?> coupons
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($coupons)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No coupons found</h6>
                    <p class="text-muted">Try adjusting your filters or create some coupons.</p>
                </div>
            <?php else: ?>
                <div class="row coupon-grid">
                    <?php foreach ($coupons as $coupon): ?>
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="coupon-card">
                                <!-- Coupon Header -->
                                <div class="coupon-header" style="background: linear-gradient(135deg, <?php echo $coupon['category_color'] ?? '#FFC107'; ?> 0%, <?php echo $coupon['category_color'] ?? '#FFC107'; ?>80 100%);">
                                    <div class="coupon-category">
                                        <i class="<?php echo $coupon['category_icon'] ?? 'fas fa-tag'; ?> me-2"></i>
                                        <?php echo htmlspecialchars($coupon['category_name'] ?? 'General'); ?>
                                    </div>
                                    <div class="coupon-actions">
                                        <button class="btn btn-sm btn-outline-light" onclick="editCoupon('<?php echo $coupon['id']; ?>')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-light" onclick="deleteCoupon('<?php echo $coupon['id']; ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Coupon Content -->
                                <div class="coupon-content">
                                    <div class="coupon-discount">
                                        <?php
                                        switch ($coupon['discount_type']) {
                                            case 'percentage':
                                                echo $coupon['discount_value'] . '% OFF';
                                                break;
                                            case 'fixed_amount':
                                                echo '$' . number_format($coupon['discount_value'], 0) . ' OFF';
                                                break;
                                            case 'buy_x_get_y':
                                                echo 'BOGO';
                                                break;
                                            case 'free_item':
                                                echo 'FREE ITEM';
                                                break;
                                        }
                                        ?>
                                    </div>
                                    
                                    <h6 class="coupon-title"><?php echo htmlspecialchars($coupon['title']); ?></h6>
                                    
                                    <p class="coupon-description">
                                        <?php echo htmlspecialchars(substr($coupon['description'], 0, 100)); ?>
                                        <?php if (strlen($coupon['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                    
                                    <div class="coupon-code">
                                        <strong>Code: <?php echo htmlspecialchars($coupon['code']); ?></strong>
                                    </div>
                                    
                                    <div class="coupon-place">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($coupon['place_name']); ?>
                                    </div>
                                    
                                    <div class="coupon-validity">
                                        <i class="fas fa-calendar me-1"></i>
                                        Valid: <?php echo date('M j', strtotime($coupon['start_date'])); ?> - <?php echo date('M j, Y', strtotime($coupon['end_date'])); ?>
                                    </div>
                                    
                                    <!-- Coupon Stats -->
                                    <div class="coupon-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-eye"></i>
                                            <span><?php echo number_format($coupon['view_count']); ?></span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-heart"></i>
                                            <span><?php echo number_format($coupon['favorite_count']); ?></span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span><?php echo number_format($coupon['redemption_count']); ?></span>
                                        </div>
                                        <?php if ($coupon['usage_limit']): ?>
                                        <div class="stat-item">
                                            <i class="fas fa-users"></i>
                                            <span><?php echo $coupon['redemption_count']; ?>/<?php echo $coupon['usage_limit']; ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Coupon Status -->
                                    <div class="coupon-status">
                                        <?php
                                        $today = date('Y-m-d');
                                        if (!$coupon['is_active']) {
                                            echo '<span class="badge bg-secondary">Inactive</span>';
                                        } elseif ($coupon['end_date'] < $today) {
                                            echo '<span class="badge bg-danger">Expired</span>';
                                        } elseif ($coupon['start_date'] > $today) {
                                            echo '<span class="badge bg-info">Upcoming</span>';
                                        } elseif ($coupon['requires_approval'] && !$coupon['approved_by']) {
                                            echo '<span class="badge bg-warning">Pending Approval</span>';
                                        } else {
                                            echo '<span class="badge bg-success">Active</span>';
                                        }
                                        
                                        if ($coupon['is_featured']) {
                                            echo '<span class="badge bg-primary ms-1">Featured</span>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Coupon pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php
                $query_params = $_GET;
                unset($query_params['page']);
                $base_url = 'coupon-management.php?' . http_build_query($query_params);
                ?>
                
                <!-- Previous -->
                <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                </li>
                
                <!-- Page numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <!-- Next -->
                <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                </li>
            </ul>
        </nav>
    <?php endif; ?>
</div>

<script src="../assets/js/coupon-management.js"></script>

<?php require_once '../includes/footer.php'; ?>
