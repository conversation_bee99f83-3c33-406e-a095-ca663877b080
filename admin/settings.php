<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'System Settings - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-cogs me-2"></i>System Settings</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Settings Tabs -->
            <div class="content-card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#general">General</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#email">Email</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#security">Security</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#api">API</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- General Settings -->
                        <div class="tab-pane fade show active" id="general">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Site Name</label>
                                            <input type="text" class="form-control" value="Beersty">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Site Description</label>
                                            <textarea class="form-control" rows="3">Discover amazing beers and breweries</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Default Language</label>
                                            <select class="form-select">
                                                <option value="en">English</option>
                                                <option value="es">Spanish</option>
                                                <option value="fr">French</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Time Zone</label>
                                            <select class="form-select">
                                                <option value="UTC">UTC</option>
                                                <option value="America/New_York">Eastern Time</option>
                                                <option value="America/Chicago">Central Time</option>
                                                <option value="America/Denver">Mountain Time</option>
                                                <option value="America/Los_Angeles">Pacific Time</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date Format</label>
                                            <select class="form-select">
                                                <option value="Y-m-d">YYYY-MM-DD</option>
                                                <option value="m/d/Y">MM/DD/YYYY</option>
                                                <option value="d/m/Y">DD/MM/YYYY</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="maintenance">
                                                <label class="form-check-label" for="maintenance">
                                                    Maintenance Mode
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Email Settings -->
                        <div class="tab-pane fade" id="email">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Host</label>
                                            <input type="text" class="form-control" placeholder="smtp.gmail.com">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Port</label>
                                            <input type="number" class="form-control" value="587">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Username</label>
                                            <input type="email" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SMTP Password</label>
                                            <input type="password" class="form-control">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">From Email</label>
                                            <input type="email" class="form-control" placeholder="<EMAIL>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">From Name</label>
                                            <input type="text" class="form-control" value="Beersty">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Security Settings -->
                        <div class="tab-pane fade" id="security">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Session Timeout (minutes)</label>
                                            <input type="number" class="form-control" value="60">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Max Login Attempts</label>
                                            <input type="number" class="form-control" value="5">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="twofa" checked>
                                                <label class="form-check-label" for="twofa">
                                                    Enable Two-Factor Authentication
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Password Min Length</label>
                                            <input type="number" class="form-control" value="8">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="ssl" checked>
                                                <label class="form-check-label" for="ssl">
                                                    Force SSL/HTTPS
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="captcha">
                                                <label class="form-check-label" for="captcha">
                                                    Enable CAPTCHA
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <!-- API Settings -->
                        <div class="tab-pane fade" id="api">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">API Rate Limit (requests/hour)</label>
                                            <input type="number" class="form-control" value="1000">
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="api_enabled" checked>
                                                <label class="form-check-label" for="api_enabled">
                                                    Enable API Access
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">API Version</label>
                                            <select class="form-select">
                                                <option value="v1">Version 1.0</option>
                                                <option value="v2">Version 2.0</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="api_logs" checked>
                                                <label class="form-check-label" for="api_logs">
                                                    Enable API Logging
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<?php 
include 'includes/admin-layout-end.php';
include '../includes/footer.php'; 
?>
