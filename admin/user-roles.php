<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'User Roles Management - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-user-shield me-2"></i>User Roles Management</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Add Role
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <div class="content-card">
                <div class="card-header">
                    <h5><i class="fas fa-user-shield me-2"></i>User Roles & Permissions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Role</th>
                                    <th>Description</th>
                                    <th>Users</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-danger">Admin</span></td>
                                    <td>Full system access and control</td>
                                    <td>2</td>
                                    <td><span class="badge bg-success">All Permissions</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">Moderator</span></td>
                                    <td>Content moderation and user management</td>
                                    <td>1</td>
                                    <td><span class="badge bg-warning">Limited</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning">Business Owner</span></td>
                                    <td>Manage own business listings and content</td>
                                    <td>5</td>
                                    <td><span class="badge bg-info">Business Only</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-secondary">User</span></td>
                                    <td>Standard user access</td>
                                    <td>156</td>
                                    <td><span class="badge bg-light text-dark">Basic</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

<?php 
include 'includes/admin-layout-end.php';
include '../includes/footer.php'; 
?>
