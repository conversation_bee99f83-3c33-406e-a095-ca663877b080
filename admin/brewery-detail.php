<?php
require_once '../config/config.php';
requireRole('admin');

$breweryId = $_GET['id'] ?? null;
if (!$breweryId) {
    $_SESSION['error_message'] = 'Brewery ID is required.';
    redirect('breweries.php');
}

$pageTitle = 'Brewery Details - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get brewery data
$brewery = null;
$beerMenu = [];
$foodMenu = [];
$coupons = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get brewery details
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
    if (!$brewery) {
        $_SESSION['error_message'] = 'Brewery not found.';
        redirect('breweries.php');
    }
    
    // Decode social links
    if (isset($brewery['social_links']) && $brewery['social_links']) {
        $brewery['social_links'] = json_decode($brewery['social_links'], true);
    } else {
        $brewery['social_links'] = [];
    }
    
    // Get beer menu
    $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE brewery_id = ? ORDER BY name");
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();
    
    // Get food menu
    $stmt = $conn->prepare("SELECT * FROM food_menu WHERE brewery_id = ? ORDER BY category, name");
    $stmt->execute([$breweryId]);
    $foodMenu = $stmt->fetchAll();
    
    // Get coupons
    $stmt = $conn->prepare("SELECT * FROM brewery_coupons WHERE brewery_id = ? ORDER BY created_at DESC");
    $stmt->execute([$breweryId]);
    $coupons = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Brewery detail error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
    redirect('breweries.php');
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-building me-2"></i><?php echo htmlspecialchars($brewery['name']); ?>
                </h1>
                <div>
                    <a href="breweries.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Breweries
                    </a>
                    <a href="../breweries/listing.php" class="btn btn-primary">
                        <i class="fas fa-eye me-1"></i>View Public Page
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Brewery Info -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Brewery Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> <?php echo htmlspecialchars($brewery['name']); ?></p>
                            <p><strong>Type:</strong> <?php echo ucfirst($brewery['brewery_type']); ?></p>
                            <p><strong>Address:</strong> <?php echo htmlspecialchars($brewery['address'] ?: 'Not provided'); ?></p>
                            <p><strong>City:</strong> <?php echo htmlspecialchars($brewery['city'] ?: 'Not provided'); ?></p>
                            <p><strong>State:</strong> <?php echo htmlspecialchars($brewery['state'] ?: 'Not provided'); ?></p>
                            <p><strong>ZIP:</strong> <?php echo htmlspecialchars($brewery['zip'] ?: 'Not provided'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($brewery['phone'] ?: 'Not provided'); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($brewery['email'] ?: 'Not provided'); ?></p>
                            <p><strong>Website:</strong> 
                                <?php if ($brewery['website']): ?>
                                    <a href="<?php echo htmlspecialchars($brewery['website']); ?>" target="_blank">
                                        <?php echo htmlspecialchars($brewery['website']); ?>
                                    </a>
                                <?php else: ?>
                                    Not provided
                                <?php endif; ?>
                            </p>
                            <p><strong>Created:</strong> <?php echo formatDateTime($brewery['created_at']); ?></p>
                            <p><strong>Updated:</strong> <?php echo formatDateTime($brewery['updated_at']); ?></p>
                        </div>
                    </div>
                    
                    <?php if ($brewery['description']): ?>
                        <div class="mt-3">
                            <strong>Description:</strong>
                            <p class="mt-2"><?php echo nl2br(htmlspecialchars($brewery['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 text-primary"><?php echo number_format($brewery['follower_count']); ?></div>
                            <small class="text-muted">Followers</small>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 text-danger"><?php echo number_format($brewery['like_count']); ?></div>
                            <small class="text-muted">Likes</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-success"><?php echo count($beerMenu); ?></div>
                            <small class="text-muted">Beer Items</small>
                        </div>
                        <div class="col-6">
                            <div class="h4 text-warning"><?php echo count($coupons); ?></div>
                            <small class="text-muted">Coupons</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Claimed:</strong>
                        <span class="badge bg-<?php echo $brewery['claimed'] ? 'success' : 'warning'; ?> ms-2">
                            <?php echo $brewery['claimed'] ? 'Yes' : 'No'; ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Verified:</strong>
                        <span class="badge bg-<?php echo $brewery['verified'] ? 'success' : 'secondary'; ?> ms-2">
                            <?php echo $brewery['verified'] ? 'Yes' : 'Pending'; ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Claimable:</strong>
                        <span class="badge bg-<?php echo $brewery['claimable'] ? 'info' : 'secondary'; ?> ms-2">
                            <?php echo $brewery['claimable'] ? 'Yes' : 'No'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Beer Menu -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-beer me-2"></i>Beer Menu (<?php echo count($beerMenu); ?> items)
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($beerMenu)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>ABV</th>
                                <th>IBU</th>
                                <th>Price</th>
                                <th>Featured</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($beerMenu as $beer): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($beer['name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($beer['type'] ?: 'N/A'); ?></td>
                                    <td><?php echo $beer['abv'] ? $beer['abv'] . '%' : 'N/A'; ?></td>
                                    <td><?php echo $beer['ibu'] ?: 'N/A'; ?></td>
                                    <td><?php echo $beer['price'] ? '$' . number_format($beer['price'], 2) : 'N/A'; ?></td>
                                    <td>
                                        <?php if ($beer['featured']): ?>
                                            <span class="badge bg-warning">Featured</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-muted mb-0">No beer menu items found.</p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Coupons -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-tags me-2"></i>Coupons (<?php echo count($coupons); ?> items)
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($coupons)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Description</th>
                                <th>Discount</th>
                                <th>Expires</th>
                                <th>Status</th>
                                <th>Redemptions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($coupons as $coupon): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($coupon['code']); ?></code></td>
                                    <td><?php echo htmlspecialchars($coupon['description']); ?></td>
                                    <td><?php echo htmlspecialchars($coupon['discount_value']); ?></td>
                                    <td><?php echo formatDate($coupon['expiry_date']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $coupon['is_active'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $coupon['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($coupon['redemption_count']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-muted mb-0">No coupons found.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
