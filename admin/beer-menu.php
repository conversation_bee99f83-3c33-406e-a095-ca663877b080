<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Beer Menu Management - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get beer styles from database
$beer_styles = [];
try {
    $db = new Database();
    $pdo = $db->getConnection();

    // Get beer styles with item counts
    $stmt = $pdo->query("
        SELECT bs.id, bs.name, bs.category,
               COUNT(bm.id) as items_count
        FROM beer_styles bs
        LEFT JOIN beer_menu bm ON bs.id = bm.beer_style_id
        WHERE bs.is_active = 1
        GROUP BY bs.id, bs.name, bs.category
        ORDER BY bs.category, bs.name
    ");
    $beer_styles = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log("Error fetching beer styles: " . $e->getMessage());
    // Fallback to empty array if database error
    $beer_styles = [];
}

$recent_beers = [
    ['id' => 1, 'name' => 'Hoppy IPA', 'style' => 'IPA', 'abv' => 6.5, 'place' => 'Craft Masters Brewery'],
    ['id' => 2, 'name' => 'Dark Chocolate Stout', 'style' => 'Stout', 'abv' => 8.2, 'place' => 'The Hoppy Place'],
    ['id' => 3, 'name' => 'Crisp Pilsner', 'style' => 'Lager', 'abv' => 4.8, 'place' => 'Sunshine Beer Garden'],
    ['id' => 4, 'name' => 'Tropical Sour', 'style' => 'Sour', 'abv' => 5.5, 'place' => 'Craft Masters Brewery']
];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-beer me-2"></i>Beer Menu Management</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addBeerModal">
                    <i class="fas fa-plus me-1"></i>Add Beer
                </button>
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-upload me-1"></i>Import CSV
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card stat-primary">
                    <div class="stat-icon">
                        <i class="fas fa-beer"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">92</div>
                        <div class="stat-label">Total Beers</div>
                    </div>
                    <a href="#" class="stat-link">View All <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-success">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo count($beer_styles); ?></div>
                        <div class="stat-label">Beer Styles</div>
                    </div>
                    <a href="#" class="stat-link">Manage <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-warning">
                    <div class="stat-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">18</div>
                        <div class="stat-label">Breweries</div>
                    </div>
                    <a href="#" class="stat-link">View Breweries <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-danger">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">4.7</div>
                        <div class="stat-label">Avg Rating</div>
                    </div>
                    <a href="#" class="stat-link">View Reviews <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            
            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Beer Styles -->
                <div class="content-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-tags me-2"></i>Beer Styles</h5>
                        <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addStyleModal">
                            <i class="fas fa-plus me-1"></i>Add Style
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Style Name</th>
                                        <th>Category</th>
                                        <th>Beers</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($beer_styles)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-4">
                                                <i class="fas fa-tags fa-2x mb-2"></i><br>
                                                No beer styles found. <a href="#" data-bs-toggle="modal" data-bs-target="#addStyleModal">Add your first style</a>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($beer_styles as $style): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($style['name']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($style['category']); ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $style['items_count']; ?></span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" title="Edit Style">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" title="Delete Style">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Beers -->
                <div class="content-card">
                    <div class="card-header">
                        <h5><i class="fas fa-beer me-2"></i>Recent Beers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Beer</th>
                                        <th>Style</th>
                                        <th>ABV</th>
                                        <th>Brewery</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_beers as $beer): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($beer['name']); ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($beer['style']); ?></span>
                                            </td>
                                            <td><?php echo $beer['abv']; ?>%</td>
                                            <td>
                                                <small><?php echo htmlspecialchars($beer['place']); ?></small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<!-- Add Beer Modal -->
<div class="modal fade" id="addBeerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Beer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">Beer Name</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Style</label>
                        <select class="form-select" required>
                            <option value="">Select Style</option>
                            <?php foreach ($beer_styles as $style): ?>
                                <option value="<?php echo $style['id']; ?>"><?php echo htmlspecialchars($style['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ABV (%)</label>
                        <input type="number" class="form-control" step="0.1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Add Beer</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Beer Style Modal -->
<div class="modal" id="addStyleModal" tabindex="-1" data-bs-backdrop="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Add Beer Style
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addStyleForm" method="POST" action="api/beer-styles.php">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="style_name" class="form-label">Style Name *</label>
                            <input type="text" class="form-control" id="style_name" name="name" required
                                   placeholder="e.g., New England IPA">
                        </div>
                        <div class="col-md-4">
                            <label for="style_category" class="form-label">Category *</label>
                            <select class="form-select" id="style_category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="IPA">IPA</option>
                                <option value="Stout">Stout</option>
                                <option value="Porter">Porter</option>
                                <option value="Lager">Lager</option>
                                <option value="Wheat">Wheat</option>
                                <option value="Pale Ale">Pale Ale</option>
                                <option value="Ale">Ale</option>
                                <option value="Sour">Sour</option>
                                <option value="Specialty">Specialty</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="style_description" class="form-label">Description</label>
                            <textarea class="form-control" id="style_description" name="description" rows="3"
                                      placeholder="Brief description of the beer style characteristics..."></textarea>
                        </div>
                        <div class="col-md-3">
                            <label for="abv_min" class="form-label">Min ABV %</label>
                            <input type="number" class="form-control" id="abv_min" name="abv_min"
                                   step="0.1" min="0" max="20" placeholder="4.0">
                        </div>
                        <div class="col-md-3">
                            <label for="abv_max" class="form-label">Max ABV %</label>
                            <input type="number" class="form-control" id="abv_max" name="abv_max"
                                   step="0.1" min="0" max="20" placeholder="6.0">
                        </div>
                        <div class="col-md-3">
                            <label for="ibu_min" class="form-label">Min IBU</label>
                            <input type="number" class="form-control" id="ibu_min" name="ibu_min"
                                   min="0" max="120" placeholder="20">
                        </div>
                        <div class="col-md-3">
                            <label for="ibu_max" class="form-label">Max IBU</label>
                            <input type="number" class="form-control" id="ibu_max" name="ibu_max"
                                   min="0" max="120" placeholder="60">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>Add Style
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle Add Style Form Submission
document.getElementById('addStyleForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';

    fetch('api/beer-styles.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('Beer style added successfully!', 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addStyleModal'));
            modal.hide();

            // Reset form
            this.reset();

            // Reload page to show new style
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.error || 'Failed to add beer style', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while adding the beer style', 'error');
    })
    .finally(() => {
        // Restore button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php
include 'includes/admin-layout-end.php';
include '../includes/footer.php';
?>
