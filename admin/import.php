<?php
require_once '../config/config.php';
requireRole('admin');

$pageTitle = 'Data Import - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-upload me-2"></i>Data Import
                </h1>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                    <a href="breweries.php" class="btn btn-primary">
                        <i class="fas fa-building me-1"></i>View Breweries
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Import Options -->
    <div class="row g-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-csv me-2"></i>CSV Import
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Import brewery data from a CSV file. The CSV should contain columns for name, address, city, state, etc.</p>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csvFile" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv" required>
                            <div class="form-text">Maximum file size: 5MB</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="hasHeader" name="hasHeader" checked>
                                <label class="form-check-label" for="hasHeader">
                                    File has header row
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" name="importCSV" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Import CSV
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>API Import
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Import brewery data from external APIs like Open Brewery DB.</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="apiSource" class="form-label">API Source</label>
                            <select class="form-select" id="apiSource" name="apiSource">
                                <option value="openbrewerydb">Open Brewery DB</option>
                                <option value="custom" disabled>Custom API (Coming Soon)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">Filter by Location (Optional)</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="e.g., California, San Diego">
                            <div class="form-text">Leave empty to import all available breweries</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="limit" class="form-label">Import Limit</label>
                            <select class="form-select" id="limit" name="limit">
                                <option value="50">50 breweries</option>
                                <option value="100">100 breweries</option>
                                <option value="200">200 breweries</option>
                                <option value="500">500 breweries</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="importAPI" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>Import from API
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sample Data -->
    <div class="row g-4 mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>Sample Data
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Import sample brewery data for testing purposes.</p>
                    
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <i class="fas fa-beer fa-2x text-primary mb-3"></i>
                                    <h6>Craft Breweries</h6>
                                    <p class="small text-muted">10 sample craft breweries with complete profiles</p>
                                    <form method="POST" class="d-inline">
                                        <button type="submit" name="importSample" value="craft" class="btn btn-outline-primary btn-sm">
                                            Import Sample
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <i class="fas fa-building fa-2x text-success mb-3"></i>
                                    <h6>Brewpubs</h6>
                                    <p class="small text-muted">5 sample brewpubs with food menus</p>
                                    <form method="POST" class="d-inline">
                                        <button type="submit" name="importSample" value="brewpub" class="btn btn-outline-success btn-sm">
                                            Import Sample
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <i class="fas fa-industry fa-2x text-warning mb-3"></i>
                                    <h6>Large Breweries</h6>
                                    <p class="small text-muted">3 sample large-scale breweries</p>
                                    <form method="POST" class="d-inline">
                                        <button type="submit" name="importSample" value="large" class="btn btn-outline-warning btn-sm">
                                            Import Sample
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- CSV Format Guide -->
    <div class="row g-4 mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>CSV Format Guide
                    </h5>
                </div>
                <div class="card-body">
                    <p>Your CSV file should include the following columns (header row recommended):</p>
                    
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>Column</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                    <th>Example</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>name</code></td>
                                    <td><span class="badge bg-danger">Yes</span></td>
                                    <td>Brewery name</td>
                                    <td>Stone Brewing</td>
                                </tr>
                                <tr>
                                    <td><code>brewery_type</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>Type of brewery</td>
                                    <td>micro, brewpub, large, etc.</td>
                                </tr>
                                <tr>
                                    <td><code>address</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>Street address</td>
                                    <td>1999 Citracado Pkwy</td>
                                </tr>
                                <tr>
                                    <td><code>city</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>City name</td>
                                    <td>Escondido</td>
                                </tr>
                                <tr>
                                    <td><code>state</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>State or province</td>
                                    <td>California</td>
                                </tr>
                                <tr>
                                    <td><code>zip</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>Postal code</td>
                                    <td>92029</td>
                                </tr>
                                <tr>
                                    <td><code>phone</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>Phone number</td>
                                    <td>(*************</td>
                                </tr>
                                <tr>
                                    <td><code>website</code></td>
                                    <td><span class="badge bg-warning">No</span></td>
                                    <td>Website URL</td>
                                    <td>https://stonebrewing.com</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <a href="#" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download me-1"></i>Download Sample CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
