<?php
/**
 * Direct Import of US Breweries CSV
 * Import the us_breweries.csv file from layouts-for-reference
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Import US Breweries';
$additionalCSS = ['../assets/css/admin.css'];

// Import process
$importResult = null;
$errors = [];

if (isset($_POST['start_import'])) {
    try {
        $csvPath = '../layouts-for-reference/us_breweries.csv';
        
        if (!file_exists($csvPath)) {
            throw new Exception('CSV file not found: ' . $csvPath);
        }
        
        $importResult = importUSBreweries($csvPath);
        
    } catch (Exception $e) {
        $errors[] = 'Import failed: ' . $e->getMessage();
    }
}

function importUSBreweries($csvPath) {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Read CSV file
    $csvData = file_get_contents($csvPath);
    if ($csvData === false) {
        throw new Exception('Failed to read CSV file');
    }
    
    $lines = str_getcsv($csvData, "\n");
    if (count($lines) < 2) {
        throw new Exception('CSV must have at least a header row and one data row');
    }
    
    // Parse header: beersty_id,openbrewerydb_id,name,brewery_type,street,city,state,website_url
    $headers = str_getcsv($lines[0]);
    
    // Initialize stats
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    // Prepare statements
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            id, name, address, city, state, website, brewery_type,
            verified, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, 'active', NOW(), NOW())
    ");

    $updateStmt = $conn->prepare("
        UPDATE breweries SET
            address = ?, city = ?, state = ?, website = ?, brewery_type = ?,
            updated_at = NOW()
        WHERE id = ?
    ");

    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
    
    // Process each row
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (count($row) < 8) {
            $stats['skipped']++;
            $stats['errors'][] = "Row $i: Insufficient columns";
            continue;
        }
        
        // Map CSV columns
        $beerstyId = (int)trim($row[0]);  // Use beersty_id as primary ID
        // Skip openbrewerydb_id (row[1]) - we don't need it
        $name = trim($row[2]);
        $breweryType = trim($row[3]);
        $street = trim($row[4]);
        $city = trim($row[5]);
        $state = trim($row[6]);
        $websiteUrl = trim($row[7]);
        
        // Skip if no name or invalid beersty_id
        if (empty($name) || $beerstyId <= 0) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean data
        $address = $street;
        $website = !empty($websiteUrl) ? $websiteUrl : null;
        
        // Validate brewery type
        $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub', 'closed', 'party_store'];
        if (!in_array(strtolower($breweryType), $validTypes)) {
            $breweryType = 'micro';
        }
        
        try {
            // Check if brewery exists by beersty_id
            $checkStmt->execute([$beerstyId]);
            $exists = $checkStmt->fetch();

            if ($exists) {
                // Update existing brewery
                $updateStmt->execute([
                    $address,
                    $city,
                    $state,
                    $website,
                    $breweryType,
                    $beerstyId
                ]);
                $stats['updated']++;
            } else {
                // Insert new brewery with specific beersty_id
                $insertStmt->execute([
                    $beerstyId,
                    $name,
                    $address,
                    $city,
                    $state,
                    $website,
                    $breweryType
                ]);
                $stats['inserted']++;
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row $i ($name): " . $e->getMessage();
        }
        
        // Progress update every 100 rows
        if ($stats['processed'] % 100 === 0) {
            error_log("Processed {$stats['processed']} rows...");
        }
    }
    
    return $stats;
}

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-database me-2"></i>Import US Breweries Database</h1>
            <div class="header-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-info-circle me-1"></i>Import Guide
                </button>
            </div>
        </div>

        <div class="dashboard-content">
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group">
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Import Errors</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($importResult): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Import Complete!</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li><strong>Total Rows:</strong> <?php echo $importResult['total_rows']; ?></li>
                                <li><strong>Processed:</strong> <?php echo $importResult['processed']; ?></li>
                                <li><strong>Inserted:</strong> <?php echo $importResult['inserted']; ?></li>
                                <li><strong>Updated:</strong> <?php echo $importResult['updated']; ?></li>
                                <li><strong>Skipped:</strong> <?php echo $importResult['skipped']; ?></li>
                            </ul>
                        </div>
                        <?php if (!empty($importResult['errors'])): ?>
                            <div class="col-md-6">
                                <strong>Errors (showing first 10):</strong>
                                <ul class="mb-0">
                                    <?php foreach (array_slice($importResult['errors'], 0, 10) as $error): ?>
                                        <li><small><?php echo htmlspecialchars($error); ?></small></li>
                                    <?php endforeach; ?>
                                    <?php if (count($importResult['errors']) > 10): ?>
                                        <li><em>... and <?php echo count($importResult['errors']) - 10; ?> more errors</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-upload me-2"></i>Import US Breweries Database</h5>
                        </div>
                        <div class="card-body">
                            <p>This will import the comprehensive US breweries database from the reference files.</p>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Import Details:</h6>
                                <ul class="mb-0">
                                    <li><strong>Source:</strong> layouts-for-reference/us_breweries.csv</li>
                                    <li><strong>Records:</strong> ~8,000+ US breweries</li>
                                    <li><strong>Data:</strong> Names, addresses, types, websites</li>
                                    <li><strong>ID System:</strong> Uses beersty_id as primary identifier</li>
                                    <li><strong>Processing:</strong> Automatic deduplication by beersty_id</li>
                                </ul>
                            </div>
                            
                            <?php if (!$importResult): ?>
                                <form method="POST">
                                    <div class="d-grid">
                                        <button type="submit" name="start_import" class="btn btn-primary btn-lg">
                                            <i class="fas fa-database me-2"></i>Start Import Process
                                        </button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <div class="d-grid">
                                    <a href="breweries.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-eye me-2"></i>View Imported Breweries
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar me-2"></i>Database Status</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = new Database();
                                $conn = $db->getConnection();
                                
                                $stmt = $conn->query("SELECT COUNT(*) as total FROM breweries");
                                $currentCount = $stmt->fetchColumn();
                                
                                $stmt = $conn->query("SELECT COUNT(*) as verified FROM breweries WHERE verified = 1");
                                $verifiedCount = $stmt->fetchColumn();
                                
                                echo "<p><strong>Current Breweries:</strong> " . number_format($currentCount) . "</p>";
                                echo "<p><strong>Verified:</strong> " . number_format($verifiedCount) . "</p>";
                                
                            } catch (Exception $e) {
                                echo "<p class='text-muted'>Unable to load stats</p>";
                            }
                            ?>
                            
                            <div class="alert alert-warning">
                                <small>
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    This import may take several minutes to complete.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

<?php
include 'includes/admin-layout-end.php';
include '../includes/footer.php';
?>
