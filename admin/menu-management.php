<?php
/**
 * Admin Menu Management
 * Manage beer and food menus for places (breweries, restaurants, pubs, party stores, production facilities)
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Menu Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get place ID from URL (supports breweries, restaurants, pubs, party stores, production facilities)
$place_id = $_GET['place_id'] ?? null;
$selected_place = null;

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get all places for selection (breweries, restaurants, pubs, party stores, production facilities)
try {
    $stmt = $pdo->query("SELECT id, name, city, state, brewery_type as place_type FROM breweries ORDER BY name");
    $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $places = [];
    $error_message = "Error loading places: " . $e->getMessage();
}

// Get selected place details
if ($place_id) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM breweries WHERE id = ?");
        $stmt->execute([$place_id]);
        $selected_place = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $selected_place = null;
    }
}

// Get beer menu for selected place
$beer_menu = [];
if ($place_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT pb.*, bs.name as style_name
            FROM place_beers pb
            LEFT JOIN beer_styles bs ON pb.beer_style_id = bs.id
            WHERE pb.place_id = ? AND pb.is_active = 1
            ORDER BY pb.name
        ");
        $stmt->execute([$place_id]);
        $beer_menu = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $beer_menu = [];
    }
}

// Get food menu for selected place
$food_menu = [];
if ($place_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT pf.*, fc.name as category_name
            FROM place_food pf
            LEFT JOIN food_categories fc ON pf.food_category_id = fc.id
            WHERE pf.place_id = ? AND pf.is_active = 1
            ORDER BY fc.sort_order, pf.name
        ");
        $stmt->execute([$place_id]);
        $food_menu = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $food_menu = [];
    }
}

// Get beer styles for dropdown
try {
    $stmt = $pdo->query("SELECT id, name, category FROM beer_styles WHERE is_active = 1 ORDER BY category, name");
    $beer_styles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $beer_styles = [];
}

// Get food categories for dropdown
try {
    $stmt = $pdo->query("SELECT id, name FROM food_categories WHERE is_active = 1 ORDER BY sort_order, name");
    $food_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $food_categories = [];
}

require_once '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-utensils me-2"></i>Menu Management</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-1"></i>Add Category
                </button>
            </div>
        </div>

        <div class="dashboard-content">
                    <p class="text-muted mb-0">Manage beer and food menus for places (breweries, restaurants, pubs, party stores)</p>
                </div>
                <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Place Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Select Place
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <select name="place_id" class="form-select" required>
                                <option value="">Choose a place...</option>
                                <?php foreach ($places as $place): ?>
                                    <option value="<?php echo $place['id']; ?>"
                                            <?php echo ($place_id === $place['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($place['name']); ?>
                                        <?php if ($place['place_type']): ?>
                                            <small>(<?php echo ucfirst(str_replace('_', ' ', $place['place_type'])); ?>)</small>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Load Menus
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if ($selected_place): ?>
    <!-- Selected Place Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert place-info-alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-map-marker-alt fa-2x me-3"></i>
                    <div>
                        <h6 class="mb-1">Managing menus for: <strong><?php echo htmlspecialchars($selected_place['name']); ?></strong></h6>
                        <p class="mb-0">
                            <?php echo htmlspecialchars($selected_place['city'] . ', ' . $selected_place['state']); ?>
                            <?php if ($selected_place['brewery_type']): ?>
                                • <?php echo ucfirst(str_replace('_', ' ', $selected_place['brewery_type'])); ?>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Management Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="menuTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="beer-menu-tab" data-bs-toggle="tab" data-bs-target="#beer-menu" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beer Menu (<?php echo count($beer_menu); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="food-menu-tab" data-bs-toggle="tab" data-bs-target="#food-menu" type="button" role="tab">
                        <i class="fas fa-utensils me-2"></i>Food Menu (<?php echo count($food_menu); ?>)
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="menuTabContent">
                <!-- Beer Menu Tab -->
                <div class="tab-pane fade show active" id="beer-menu" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">Beer Menu Items</h5>
                        <div class="d-flex gap-2">
                            <a href="<?php echo url('admin/category-management.php'); ?>" class="btn btn-outline-info">
                                <i class="fas fa-tags me-2"></i>Manage Beer Styles
                            </a>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addBeerModal">
                                <i class="fas fa-plus me-2"></i>Add Beer
                            </button>
                        </div>
                    </div>

                    <!-- Beer Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="beerSearch" class="form-control" placeholder="Search beers by name...">
                        </div>
                        <div class="col-md-3">
                            <select id="beerStyleFilter" class="form-select">
                                <option value="">All Beer Styles</option>
                                <?php foreach ($beer_styles as $style): ?>
                                    <option value="<?php echo htmlspecialchars($style['name']); ?>">
                                        <?php echo htmlspecialchars($style['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="beerCategoryFilter" class="form-select">
                                <option value="">All Categories</option>
                                <?php
                                $categories = array_unique(array_column($beer_styles, 'category'));
                                foreach ($categories as $category):
                                ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>">
                                        <?php echo htmlspecialchars($category); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <?php if (empty($beer_menu)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No beer menu items found</h6>
                            <p class="text-muted">Add your first beer to get started</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Style</th>
                                        <th>ABV</th>
                                        <th>IBU</th>
                                        <th>Price</th>
                                        <th>Available</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($beer_menu as $beer): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($beer['name']); ?></strong>
                                                <?php if ($beer['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($beer['description'], 0, 100)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($beer['style_name'] ?? $beer['type'] ?? 'Unknown'); ?>
                                                </span>
                                            </td>
                                            <td><?php echo $beer['abv'] ? $beer['abv'] . '%' : '-'; ?></td>
                                            <td><?php echo $beer['ibu'] ?? '-'; ?></td>
                                            <td><?php echo $beer['price'] ? '$' . number_format($beer['price'], 2) : '-'; ?></td>
                                            <td>
                                                <span class="badge <?php echo $beer['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $beer['is_active'] ? 'Available' : 'Unavailable'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editBeer('<?php echo $beer['id']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteBeer('<?php echo $beer['id']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Food Menu Tab -->
                <div class="tab-pane fade" id="food-menu" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">Food Menu Items</h5>
                        <div class="d-flex gap-2">
                            <a href="<?php echo url('admin/category-management.php'); ?>" class="btn btn-outline-info">
                                <i class="fas fa-tags me-2"></i>Manage Categories
                            </a>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFoodModal">
                                <i class="fas fa-plus me-2"></i>Add Food Item
                            </button>
                        </div>
                    </div>

                    <!-- Food Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <input type="text" id="foodSearch" class="form-control" placeholder="Search food items by name or description...">
                        </div>
                        <div class="col-md-4">
                            <select id="foodCategoryFilter" class="form-select">
                                <option value="">All Food Categories</option>
                                <?php foreach ($food_categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['name']); ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <?php if (empty($food_menu)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No food menu items found</h6>
                            <p class="text-muted">Add your first food item to get started</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Price</th>
                                        <th>Available</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($food_menu as $food): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($food['name']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <?php echo htmlspecialchars($food['category_name'] ?? 'Uncategorized'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($food['description'] ?? '', 0, 100)); ?>
                                                    <?php echo strlen($food['description'] ?? '') > 100 ? '...' : ''; ?>
                                                </small>
                                            </td>
                                            <td><?php echo $food['price'] ? '$' . number_format($food['price'], 2) : '-'; ?></td>
                                            <td>
                                                <span class="badge <?php echo $food['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $food['is_active'] ? 'Available' : 'Unavailable'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editFood('<?php echo $food['id']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteFood('<?php echo $food['id']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Add Beer Modal -->
<div class="modal fade" id="addBeerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-beer me-2"></i>Add New Beer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addBeerForm">
                <div class="modal-body">
                    <input type="hidden" name="place_id" value="<?php echo htmlspecialchars($place_id ?? ''); ?>">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Beer Name *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Beer Style</label>
                            <select name="beer_style_id" class="form-select">
                                <option value="">Select Style...</option>
                                <?php foreach ($beer_styles as $style): ?>
                                    <option value="<?php echo $style['id']; ?>">
                                        <?php echo htmlspecialchars($style['name'] . ' (' . $style['category'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ABV (%)</label>
                            <input type="number" name="abv" class="form-control" step="0.1" min="0" max="20">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">IBU</label>
                            <input type="number" name="ibu" class="form-control" min="0" max="120">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Price ($)</label>
                            <input type="number" name="price" class="form-control" step="0.01" min="0">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" name="available" class="form-check-input" id="beerAvailable" checked>
                                <label class="form-check-label" for="beerAvailable">Available</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input type="checkbox" name="featured" class="form-check-input" id="beerFeatured">
                                <label class="form-check-label" for="beerFeatured">Featured</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Add Beer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Food Modal -->
<div class="modal fade" id="addFoodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-utensils me-2"></i>Add New Food Item
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addFoodForm">
                <div class="modal-body">
                    <input type="hidden" name="place_id" value="<?php echo htmlspecialchars($place_id ?? ''); ?>">
                    
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label class="form-label">Item Name *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Category</label>
                            <select name="category" class="form-select">
                                <option value="">Select Category...</option>
                                <?php foreach ($food_categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Price ($)</label>
                            <input type="number" name="price" class="form-control" step="0.01" min="0">
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input type="checkbox" name="available" class="form-check-input" id="foodAvailable" checked>
                                <label class="form-check-label" for="foodAvailable">Available</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Add Food Item
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Add Beer Form Handler
document.getElementById('addBeerForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('type', 'beer'); // Specify this is a beer

    fetch('../api/menu-management.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Beer added successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the beer.');
    });
});

// Add Food Form Handler
document.getElementById('addFoodForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('type', 'food');
    
    fetch('../api/menu-management.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Food item added successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the food item.');
    });
});

// Edit and Delete functions
function editBeer(beerId) {
    // TODO: Implement edit functionality
    alert('Edit functionality coming soon!');
}

function deleteBeer(beerId) {
    if (confirm('Are you sure you want to delete this beer?')) {
        fetch('../api/menu-management.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: beerId,
                type: 'beer'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Beer deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the beer.');
        });
    }
}

function editFood(foodId) {
    // TODO: Implement edit functionality
    alert('Edit functionality coming soon!');
}

function deleteFood(foodId) {
    if (confirm('Are you sure you want to delete this food item?')) {
        fetch('../api/menu-management.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: foodId,
                type: 'food'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Food item deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the food item.');
        });
    }
}

// Search and Filter Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Beer search and filter
    const beerSearch = document.getElementById('beerSearch');
    const beerStyleFilter = document.getElementById('beerStyleFilter');
    const beerCategoryFilter = document.getElementById('beerCategoryFilter');

    function filterBeers() {
        const searchTerm = beerSearch ? beerSearch.value.toLowerCase() : '';
        const styleFilter = beerStyleFilter ? beerStyleFilter.value.toLowerCase() : '';
        const categoryFilter = beerCategoryFilter ? beerCategoryFilter.value.toLowerCase() : '';

        const beerRows = document.querySelectorAll('#beer-menu tbody tr');

        beerRows.forEach(row => {
            const name = row.querySelector('td:first-child strong')?.textContent.toLowerCase() || '';
            const description = row.querySelector('td:first-child small')?.textContent.toLowerCase() || '';
            const style = row.querySelector('td:nth-child(2) .badge')?.textContent.toLowerCase() || '';

            const matchesSearch = name.includes(searchTerm) || description.includes(searchTerm);
            const matchesStyle = !styleFilter || style.includes(styleFilter);
            const matchesCategory = !categoryFilter || style.includes(categoryFilter);

            row.style.display = (matchesSearch && matchesStyle && matchesCategory) ? '' : 'none';
        });
    }

    if (beerSearch) beerSearch.addEventListener('input', filterBeers);
    if (beerStyleFilter) beerStyleFilter.addEventListener('change', filterBeers);
    if (beerCategoryFilter) beerCategoryFilter.addEventListener('change', filterBeers);

    // Food search and filter
    const foodSearch = document.getElementById('foodSearch');
    const foodCategoryFilter = document.getElementById('foodCategoryFilter');

    function filterFood() {
        const searchTerm = foodSearch ? foodSearch.value.toLowerCase() : '';
        const categoryFilter = foodCategoryFilter ? foodCategoryFilter.value.toLowerCase() : '';

        const foodRows = document.querySelectorAll('#food-menu tbody tr');

        foodRows.forEach(row => {
            const name = row.querySelector('td:first-child strong')?.textContent.toLowerCase() || '';
            const description = row.querySelector('td:nth-child(3) small')?.textContent.toLowerCase() || '';
            const category = row.querySelector('td:nth-child(2) .badge')?.textContent.toLowerCase() || '';

            const matchesSearch = name.includes(searchTerm) || description.includes(searchTerm);
            const matchesCategory = !categoryFilter || category.includes(categoryFilter);

            row.style.display = (matchesSearch && matchesCategory) ? '' : 'none';
        });
    }

    if (foodSearch) foodSearch.addEventListener('input', filterFood);
    if (foodCategoryFilter) foodCategoryFilter.addEventListener('change', filterFood);
});
</script>

<?php require_once '../includes/footer.php'; ?>
