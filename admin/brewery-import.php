<?php
/**
 * Brewery CSV Import
 * Upload and import breweries from CSV files
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Brewery CSV Import';
$additionalCSS = ['../assets/css/admin.css'];
$additionalJS = ['../assets/js/brewery-import.js'];

// Handle CSV upload
$uploadResult = null;
$errors = [];
$importStats = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        $uploadResult = handleCSVUpload($_FILES['csv_file']);
        if ($uploadResult['success']) {
            $importStats = $uploadResult['stats'];
        } else {
            $errors = $uploadResult['errors'];
        }
    } catch (Exception $e) {
        $errors[] = 'Upload failed: ' . $e->getMessage();
    }
}

function handleCSVUpload($file) {
    global $conn;
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'errors' => ['File upload failed']];
    }
    
    if (!str_ends_with(strtolower($file['name']), '.csv')) {
        return ['success' => false, 'errors' => ['Please upload a CSV file']];
    }
    
    if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
        return ['success' => false, 'errors' => ['File too large. Maximum size is 10MB']];
    }
    
    // Read and parse CSV
    $csvData = file_get_contents($file['tmp_name']);
    if ($csvData === false) {
        return ['success' => false, 'errors' => ['Failed to read CSV file']];
    }
    
    $lines = str_getcsv($csvData, "\n");
    if (count($lines) < 2) {
        return ['success' => false, 'errors' => ['CSV must have at least a header row and one data row']];
    }
    
    // Parse header
    $headers = str_getcsv($lines[0]);
    $headers = array_map('trim', $headers);
    
    // Map CSV columns to database fields
    $fieldMapping = createFieldMapping($headers);
    
    // Validate required fields
    $requiredFields = ['name'];
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (!in_array($field, array_values($fieldMapping))) {
            $missingFields[] = $field;
        }
    }
    
    if (!empty($missingFields)) {
        return [
            'success' => false, 
            'errors' => ['Missing required fields: ' . implode(', ', $missingFields)]
        ];
    }
    
    // Process data rows
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // Prepare insert statement
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            name, address, city, state, zip, phone, website, email,
            brewery_type, description, latitude, longitude,
            verified, claimed, follower_count, like_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    // Prepare update statement
    $updateStmt = $conn->prepare("
        UPDATE breweries SET
            address = ?, city = ?, state = ?, zip = ?, phone = ?, website = ?, email = ?,
            brewery_type = ?, description = ?, latitude = ?, longitude = ?,
            verified = ?, claimed = ?, updated_at = NOW()
        WHERE name = ?
    ");
    
    // Check for existing brewery
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE name = ?");
    
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (empty($row) || count($row) < count($headers)) {
            $stats['skipped']++;
            continue;
        }
        
        // Map row data to brewery fields
        $breweryData = [];
        foreach ($headers as $index => $header) {
            $mappedField = $fieldMapping[$header] ?? null;
            if ($mappedField) {
                $breweryData[$mappedField] = isset($row[$index]) ? trim($row[$index]) : '';
            }
        }
        
        // Skip if no name
        if (empty($breweryData['name'])) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean and validate data
        $breweryData = cleanBreweryData($breweryData);
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$breweryData['name']]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // Update existing brewery
                $updateStmt->execute([
                    $breweryData['address'] ?? '',
                    $breweryData['city'] ?? '',
                    $breweryData['state'] ?? '',
                    $breweryData['zip'] ?? '',
                    $breweryData['phone'] ?? '',
                    $breweryData['website'] ?? '',
                    $breweryData['email'] ?? '',
                    $breweryData['brewery_type'] ?? 'micro',
                    $breweryData['description'] ?? '',
                    $breweryData['latitude'] ?? null,
                    $breweryData['longitude'] ?? null,
                    $breweryData['verified'] ?? 0,
                    $breweryData['claimed'] ?? 0,
                    $breweryData['name']
                ]);
                $stats['updated']++;
            } else {
                // Insert new brewery
                $insertStmt->execute([
                    $breweryData['name'],
                    $breweryData['address'] ?? '',
                    $breweryData['city'] ?? '',
                    $breweryData['state'] ?? '',
                    $breweryData['zip'] ?? '',
                    $breweryData['phone'] ?? '',
                    $breweryData['website'] ?? '',
                    $breweryData['email'] ?? '',
                    $breweryData['brewery_type'] ?? 'micro',
                    $breweryData['description'] ?? '',
                    $breweryData['latitude'] ?? null,
                    $breweryData['longitude'] ?? null,
                    $breweryData['verified'] ?? 0,
                    $breweryData['claimed'] ?? 0,
                    $breweryData['follower_count'] ?? 0,
                    $breweryData['like_count'] ?? 0
                ]);
                $stats['inserted']++;
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row " . ($i + 1) . ": " . $e->getMessage();
        }
    }
    
    return ['success' => true, 'stats' => $stats];
}

function createFieldMapping($headers) {
    $mapping = [];
    
    foreach ($headers as $header) {
        $lower = strtolower(trim($header));
        
        // Map common field variations
        if (in_array($lower, ['name', 'brewery_name', 'brewery name', 'business_name'])) {
            $mapping[$header] = 'name';
        } elseif (in_array($lower, ['address', 'street', 'street_address', 'address_1'])) {
            $mapping[$header] = 'address';
        } elseif (in_array($lower, ['city', 'town'])) {
            $mapping[$header] = 'city';
        } elseif (in_array($lower, ['state', 'province', 'region'])) {
            $mapping[$header] = 'state';
        } elseif (in_array($lower, ['zip', 'zipcode', 'zip_code', 'postal_code', 'postcode'])) {
            $mapping[$header] = 'zip';
        } elseif (in_array($lower, ['phone', 'telephone', 'phone_number', 'tel'])) {
            $mapping[$header] = 'phone';
        } elseif (in_array($lower, ['website', 'url', 'web', 'homepage'])) {
            $mapping[$header] = 'website';
        } elseif (in_array($lower, ['email', 'email_address', 'contact_email'])) {
            $mapping[$header] = 'email';
        } elseif (in_array($lower, ['type', 'brewery_type', 'category'])) {
            $mapping[$header] = 'brewery_type';
        } elseif (in_array($lower, ['description', 'about', 'bio', 'summary'])) {
            $mapping[$header] = 'description';
        } elseif (in_array($lower, ['lat', 'latitude'])) {
            $mapping[$header] = 'latitude';
        } elseif (in_array($lower, ['lng', 'lon', 'longitude'])) {
            $mapping[$header] = 'longitude';
        }
    }
    
    return $mapping;
}

function cleanBreweryData($data) {
    // Clean phone number
    if (isset($data['phone'])) {
        $data['phone'] = preg_replace('/[^0-9+\-\(\)\s]/', '', $data['phone']);
    }
    
    // Clean website URL
    if (isset($data['website']) && !empty($data['website'])) {
        if (!str_starts_with($data['website'], 'http')) {
            $data['website'] = 'https://' . $data['website'];
        }
    }
    
    // Clean email
    if (isset($data['email'])) {
        $data['email'] = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    }
    
    // Validate brewery type
    $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'party_store'];
    if (isset($data['brewery_type']) && !in_array(strtolower($data['brewery_type']), $validTypes)) {
        $data['brewery_type'] = 'micro';
    }
    
    // Convert coordinates to float
    if (isset($data['latitude'])) {
        $data['latitude'] = is_numeric($data['latitude']) ? (float)$data['latitude'] : null;
    }
    if (isset($data['longitude'])) {
        $data['longitude'] = is_numeric($data['longitude']) ? (float)$data['longitude'] : null;
    }
    
    return $data;
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-upload text-primary me-2"></i>
                    Brewery CSV Import
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="brewery-import-guide.php" class="btn btn-info">
                            <i class="fas fa-book me-1"></i>Import Guide
                        </a>
                        <a href="download-template.php" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Download Template
                        </a>
                    </div>
                    <div class="btn-group">
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Upload Errors</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($importStats): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Import Complete!</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li><strong>Total Rows:</strong> <?php echo $importStats['total_rows']; ?></li>
                                <li><strong>Processed:</strong> <?php echo $importStats['processed']; ?></li>
                                <li><strong>Inserted:</strong> <?php echo $importStats['inserted']; ?></li>
                                <li><strong>Updated:</strong> <?php echo $importStats['updated']; ?></li>
                                <li><strong>Skipped:</strong> <?php echo $importStats['skipped']; ?></li>
                            </ul>
                        </div>
                        <?php if (!empty($importStats['errors'])): ?>
                            <div class="col-md-6">
                                <strong>Errors:</strong>
                                <ul class="mb-0">
                                    <?php foreach (array_slice($importStats['errors'], 0, 5) as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                    <?php if (count($importStats['errors']) > 5): ?>
                                        <li><em>... and <?php echo count($importStats['errors']) - 5; ?> more errors</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-csv me-2"></i>Upload CSV File</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="csvUploadForm">
                                <div class="mb-3">
                                    <label for="csv_file" class="form-label">Select CSV File</label>
                                    <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                    <div class="form-text">
                                        Maximum file size: 10MB. Only CSV files are accepted.
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>Upload and Import
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>CSV Format Requirements</h5>
                        </div>
                        <div class="card-body">
                            <h6>Required Columns:</h6>
                            <ul>
                                <li><strong>name</strong> - Brewery name (required)</li>
                            </ul>
                            
                            <h6>Optional Columns:</h6>
                            <ul>
                                <li><strong>address</strong> - Street address</li>
                                <li><strong>city</strong> - City name</li>
                                <li><strong>state</strong> - State/Province</li>
                                <li><strong>zip</strong> - ZIP/Postal code</li>
                                <li><strong>phone</strong> - Phone number</li>
                                <li><strong>website</strong> - Website URL</li>
                                <li><strong>email</strong> - Contact email</li>
                                <li><strong>brewery_type</strong> - Type (micro, nano, regional, etc.)</li>
                                <li><strong>description</strong> - Description</li>
                                <li><strong>latitude</strong> - GPS latitude</li>
                                <li><strong>longitude</strong> - GPS longitude</li>
                            </ul>
                            
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-lightbulb me-1"></i>
                                    The system will automatically map common column name variations.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
