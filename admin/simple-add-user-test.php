<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Add User Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Simple Add User Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>Test 1: Direct Form Submission</h4>
                <form id="simpleForm" class="border p-3">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" value="password123" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" value="password123" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit Direct</button>
                </form>
            </div>
            
            <div class="col-md-6">
                <h4>Test 2: API Call</h4>
                <button id="testApiBtn" class="btn btn-success">Test API Call</button>
                
                <h4 class="mt-3">Test 3: Modal Test</h4>
                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#testModal">
                    Open Test Modal
                </button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>Results:</h4>
                <div id="results" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa;"></div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="modalForm">
                        <div class="mb-3">
                            <label for="modal_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="modal_email" name="email" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label for="modal_password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="modal_password" name="password" value="password123" required>
                        </div>
                        <div class="mb-3">
                            <label for="modal_confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="modal_confirm_password" name="confirm_password" value="password123" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Modal</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            results.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }
        
        // Test 1: Direct form submission
        document.getElementById('simpleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            log('Direct form submitted');
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            log('Form data: ' + JSON.stringify(data));
            
            if (data.password !== data.confirm_password) {
                log('Password mismatch', 'error');
                return;
            }
            
            // Test API call
            fetch('user-api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'create',
                    ...data,
                    role: 'user',
                    status: 'active'
                })
            })
            .then(response => {
                log('Response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('Response: ' + JSON.stringify(data), data.success ? 'success' : 'error');
            })
            .catch(error => {
                log('Error: ' + error.message, 'error');
            });
        });
        
        // Test 2: Direct API call
        document.getElementById('testApiBtn').addEventListener('click', function() {
            log('Testing direct API call');
            
            const testData = {
                action: 'create',
                email: 'api-test-' + Date.now() + '@example.com',
                password: 'password123',
                confirm_password: 'password123',
                role: 'user',
                status: 'active'
            };
            
            log('API test data: ' + JSON.stringify(testData));
            
            fetch('user-api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                log('API response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('API response: ' + JSON.stringify(data), data.success ? 'success' : 'error');
            })
            .catch(error => {
                log('API error: ' + error.message, 'error');
            });
        });
        
        // Test 3: Modal form submission
        document.getElementById('modalForm').addEventListener('submit', function(e) {
            e.preventDefault();
            log('Modal form submitted');
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            log('Modal form data: ' + JSON.stringify(data));
            
            if (data.password !== data.confirm_password) {
                log('Modal password mismatch', 'error');
                return;
            }
            
            // Test API call from modal
            fetch('user-api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'create',
                    ...data,
                    role: 'user',
                    status: 'active'
                })
            })
            .then(response => {
                log('Modal response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('Modal response: ' + JSON.stringify(data), data.success ? 'success' : 'error');
            })
            .catch(error => {
                log('Modal error: ' + error.message, 'error');
            });
        });
        
        log('Test page loaded successfully');
    </script>
</body>
</html>
