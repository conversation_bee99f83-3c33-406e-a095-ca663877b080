<?php
/**
 * Debug Users - Check actual user data
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Debug Users</title></head><body>";
echo "<h1>🔍 Debug User Data...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check table structure
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 Table structure:<br>";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    // Get all users
    $stmt = $conn->query("SELECT * FROM users LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<br>👥 User data:<br>";
    foreach ($users as $i => $user) {
        echo "<br>User " . ($i + 1) . ":<br>";
        foreach ($user as $field => $value) {
            $display_value = $value;
            if (strlen($value) > 100) {
                $display_value = substr($value, 0, 100) . '...';
            }
            echo "  $field: '$display_value'<br>";
        }
    }
    
    // Check role mapping
    $user_roles = [
        'admin' => 'Administrator',
        'site_moderator' => 'Site Moderator',
        'business_owner' => 'Business Owner',
        'business_manager' => 'Business Manager',
        'user' => 'Standard User'
    ];
    
    echo "<br>🔧 Role mapping test:<br>";
    foreach ($users as $user) {
        $role = $user['role'] ?? 'user';
        $mapped_role = $user_roles[$role] ?? $role;
        echo "User {$user['id']}: role='$role' → mapped='$mapped_role'<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to User Management</a>";
echo "</body></html>";
?>
