<?php
/**
 * Direct Import Execution - Run Import Immediately
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Set longer execution time for import
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '256M');

$pageTitle = 'Running Import';
$additionalCSS = ['../assets/css/admin.css'];

echo "<!DOCTYPE html><html><head><title>Import Running</title></head><body>";
echo "<h1>🚀 Starting US Breweries Import...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $csvPath = '../layouts-for-reference/us_breweries.csv';
    
    if (!file_exists($csvPath)) {
        throw new Exception('CSV file not found: ' . $csvPath);
    }
    
    echo "✅ CSV file found: $csvPath<br>";
    
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Read CSV file
    $csvData = file_get_contents($csvPath);
    if ($csvData === false) {
        throw new Exception('Failed to read CSV file');
    }
    
    $lines = str_getcsv($csvData, "\n");
    echo "✅ CSV loaded: " . (count($lines) - 1) . " data rows<br>";
    
    // Parse header
    $headers = str_getcsv($lines[0]);
    echo "✅ Headers: " . implode(', ', $headers) . "<br>";
    
    // Initialize stats
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    echo "🔄 Starting import process...<br><br>";
    
    // Prepare statements
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            id, name, address, city, state, website, brewery_type, 
            verified, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, 'active', NOW(), NOW())
    ");
    
    $updateStmt = $conn->prepare("
        UPDATE breweries SET
            address = ?, city = ?, state = ?, website = ?, brewery_type = ?,
            updated_at = NOW()
        WHERE id = ?
    ");
    
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
    
    echo "✅ SQL statements prepared<br><br>";
    
    // Process each row
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (count($row) < 8) {
            $stats['skipped']++;
            echo "⚠️ Row $i: Insufficient columns<br>";
            continue;
        }
        
        // Map CSV columns
        $beerstyId = (int)trim($row[0]);
        $name = trim($row[2]);
        $breweryType = trim($row[3]);
        $street = trim($row[4]);
        $city = trim($row[5]);
        $state = trim($row[6]);
        $websiteUrl = trim($row[7]);
        
        // Skip if no name or invalid beersty_id
        if (empty($name) || $beerstyId <= 0) {
            $stats['skipped']++;
            echo "⚠️ Row $i: Invalid data (ID: $beerstyId, Name: '$name')<br>";
            continue;
        }
        
        // Clean data
        $address = $street;
        $website = !empty($websiteUrl) ? $websiteUrl : null;
        
        // Validate brewery type
        $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub', 'closed', 'party_store'];
        if (!in_array(strtolower($breweryType), $validTypes)) {
            $breweryType = 'micro';
        }
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$beerstyId]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // Update existing brewery
                $updateStmt->execute([
                    $address,
                    $city,
                    $state,
                    $website,
                    $breweryType,
                    $beerstyId
                ]);
                $stats['updated']++;
                echo "🔄 Updated: $name (ID: $beerstyId)<br>";
            } else {
                // Insert new brewery
                $insertStmt->execute([
                    $beerstyId,
                    $name,
                    $address,
                    $city,
                    $state,
                    $website,
                    $breweryType
                ]);
                $stats['inserted']++;
                echo "✅ Inserted: $name (ID: $beerstyId)<br>";
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row $i ($name): " . $e->getMessage();
            echo "❌ Error Row $i ($name): " . $e->getMessage() . "<br>";
        }
        
        // Progress update every 100 rows
        if ($stats['processed'] % 100 === 0) {
            echo "<br>📊 Progress: {$stats['processed']} rows processed...<br><br>";
            flush();
            ob_flush();
        }
        
        // Full import - no limit
    }
    
    echo "<br><br>🎉 Import Complete!<br>";
    echo "📊 Final Statistics:<br>";
    echo "- Total Rows: {$stats['total_rows']}<br>";
    echo "- Processed: {$stats['processed']}<br>";
    echo "- Inserted: {$stats['inserted']}<br>";
    echo "- Updated: {$stats['updated']}<br>";
    echo "- Skipped: {$stats['skipped']}<br>";
    echo "- Errors: " . count($stats['errors']) . "<br>";
    
    if (!empty($stats['errors'])) {
        echo "<br>❌ Errors:<br>";
        foreach (array_slice($stats['errors'], 0, 10) as $error) {
            echo "- $error<br>";
        }
    }
    
} catch (Exception $e) {
    echo "💥 FATAL ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='breweries.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Breweries</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
