<?php
require_once '../config/config.php';
requireRole('admin');

$pageTitle = 'Admin Dashboard - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get dashboard statistics
$stats = [
    'total_places' => 0,
    'total_users' => 0,
    'total_beer_items' => 0,
    'total_coupons' => 0,
    'recent_registrations' => 0
];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get database type for proper handling
    $driver = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
    $isJsonDb = ($driver === 'json' || $driver === 'user_shared_json');

    // Total places
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM breweries");
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['total_places'] = $result ? ($result['count'] ?? 0) : 0;

    // Total users
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['total_users'] = $result ? ($result['count'] ?? 0) : 0;

    // Total beer menu items
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu");
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['total_beer_items'] = $result ? ($result['count'] ?? 0) : 0;

    // Total active coupons
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM coupons WHERE status = 'active'");
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['total_coupons'] = $result ? ($result['count'] ?? 0) : 0;

    // Recent registrations (last 30 days)
    if ($isJsonDb) {
        // For JSON database, just count all users (simplified)
        $stats['recent_registrations'] = $stats['total_users'];
    } else {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['recent_registrations'] = $result ? ($result['count'] ?? 0) : 0;
    }
    
    // Recent places
    try {
        $stmt = $conn->prepare("
            SELECT id, name, city, state, created_at, claimed, verified
            FROM breweries
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
        $recent_places = $stmt->fetchAll() ?: [];
    } catch (Exception $e) {
        $recent_places = [];
    }

    // Recent users
    try {
        $stmt = $conn->prepare("
            SELECT u.email, p.role, u.created_at
            FROM users u
            JOIN profiles p ON u.id = p.id
            ORDER BY u.created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
        $recent_users = $stmt->fetchAll() ?: [];
    } catch (Exception $e) {
        $recent_users = [];
    }
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading dashboard data.';
}

include '../includes/header.php';
?>

<!-- Admin Dashboard with Sidebar -->
<div class="admin-layout">
    <!-- Sidebar -->
    <nav class="admin-sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-cog me-2"></i>Admin Panel</h4>
            <button class="sidebar-toggle-btn" onclick="handleSidebarToggle()" title="Toggle Sidebar">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <!-- Dashboard -->
            <div class="menu-section">
                <a href="dashboard.php" class="menu-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <!-- Places Management -->
            <div class="menu-section">
                <div class="menu-title">Places Management</div>
                <a href="breweries.php" class="menu-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Manage Places</span>
                </a>
                <a href="import-us-breweries.php" class="menu-item">
                    <i class="fas fa-database"></i>
                    <span>Import US Places</span>
                </a>
                <a href="brewery-import.php" class="menu-item">
                    <i class="fas fa-file-csv"></i>
                    <span>Upload Places CSV</span>
                </a>
            </div>

            <!-- User Management -->
            <div class="menu-section">
                <div class="menu-title">User Management</div>
                <a href="user-management.php" class="menu-item">
                    <i class="fas fa-users"></i>
                    <span>Manage Users</span>
                </a>
                <a href="user-roles.php" class="menu-item">
                    <i class="fas fa-user-shield"></i>
                    <span>User Roles</span>
                </a>
            </div>

            <!-- Content Management -->
            <div class="menu-section">
                <div class="menu-title">Content Management</div>
                <a href="beer-menu.php" class="menu-item">
                    <i class="fas fa-beer"></i>
                    <span>Beer Menus</span>
                </a>
                <a href="food-menu.php" class="menu-item">
                    <i class="fas fa-utensils"></i>
                    <span>Food Menus</span>
                </a>
                <a href="coupons.php" class="menu-item">
                    <i class="fas fa-tags"></i>
                    <span>Coupons & Deals</span>
                </a>
            </div>

            <!-- Data Management -->
            <div class="menu-section">
                <div class="menu-title">Data Management</div>
                <a href="csv-import.php" class="menu-item">
                    <i class="fas fa-upload"></i>
                    <span>CSV Import</span>
                </a>
                <a href="data-export.php" class="menu-item">
                    <i class="fas fa-download"></i>
                    <span>Data Export</span>
                </a>
            </div>

            <!-- Analytics & Reports -->
            <div class="menu-section">
                <div class="menu-title">Analytics & Reports</div>
                <a href="analytics.php" class="menu-item">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </a>
                <a href="reports.php" class="menu-item">
                    <i class="fas fa-file-alt"></i>
                    <span>Reports</span>
                </a>
            </div>

            <!-- System -->
            <div class="menu-section">
                <div class="menu-title">System</div>
                <a href="settings.php" class="menu-item">
                    <i class="fas fa-cogs"></i>
                    <span>Settings</span>
                </a>
                <a href="logs.php" class="menu-item">
                    <i class="fas fa-list-alt"></i>
                    <span>System Logs</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="main-header">
            <div class="header-left">
                <h1><i class="fas fa-tachometer-alt me-2"></i>Dashboard Overview</h1>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card stat-primary">
                <div class="stat-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($stats['total_places']); ?></div>
                    <div class="stat-label">Total Places</div>
                </div>
                <a href="breweries.php" class="stat-link">View Details <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="stat-card stat-success">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>
                    <div class="stat-label">Total Users</div>
                </div>
                <a href="user-management.php" class="stat-link">View Details <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="stat-card stat-warning">
                <div class="stat-icon">
                    <i class="fas fa-beer"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($stats['total_beer_items']); ?></div>
                    <div class="stat-label">Beer Menu Items</div>
                </div>
                <a href="beer-menu.php" class="stat-link">View Details <i class="fas fa-arrow-right"></i></a>
            </div>

            <div class="stat-card stat-danger">
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo number_format($stats['total_coupons']); ?></div>
                    <div class="stat-label">Active Coupons</div>
                </div>
                <a href="coupons.php" class="stat-link">View Details <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="dashboard-content">
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <h5><i class="fas fa-map-marker-alt me-2"></i>Recent Places</h5>
                    </div>
                <div class="card-body">
                    <?php if (!empty($recent_places)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Added</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_places as $place): ?>
                                        <tr>
                                            <td>
                                                <a href="brewery-detail.php?id=<?php echo $place['id']; ?>">
                                                    <?php echo htmlspecialchars($place['name']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($place['city'] . ', ' . $place['state']); ?>
                                            </td>
                                            <td>
                                                <?php if ($place['verified']): ?>
                                                    <span class="badge bg-success">Verified</span>
                                                <?php elseif ($place['claimed']): ?>
                                                    <span class="badge bg-warning">Claimed</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Unclaimed</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo formatDate($place['created_at']); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-0">No places found.</p>
                    <?php endif; ?>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>Recent Users</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_users)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Registered</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_users as $user): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'brewery' ? 'warning' : 'info'); ?>">
                                                        <?php echo ucfirst($user['role']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($user['created_at']); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted mb-0">No users found.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>


<script>
// Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar state
    const sidebar = document.querySelector('.admin-sidebar');
    const mainContent = document.querySelector('.admin-main');
    const toggleBtn = document.querySelector('.sidebar-toggle-btn');

    // Check for saved sidebar state
    const sidebarCollapsed = localStorage.getItem('admin-sidebar-collapsed') === 'true';
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
    }

    // Add tooltips to menu items for collapsed state
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        const span = item.querySelector('span');
        if (span) {
            item.setAttribute('data-tooltip', span.textContent.trim());
        }
    });
});

// Smart toggle function that handles both desktop and mobile
function handleSidebarToggle() {
    if (window.innerWidth <= 768) {
        toggleMobileSidebar();
    } else {
        toggleSidebarCollapse();
    }
}

// Desktop sidebar collapse/expand
function toggleSidebarCollapse() {
    const sidebar = document.querySelector('.admin-sidebar');
    const isCollapsed = sidebar.classList.contains('collapsed');

    if (isCollapsed) {
        sidebar.classList.remove('collapsed');
        localStorage.setItem('admin-sidebar-collapsed', 'false');
    } else {
        sidebar.classList.add('collapsed');
        localStorage.setItem('admin-sidebar-collapsed', 'true');
    }

    // Trigger resize event for any charts or responsive elements
    setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
    }, 300);
}

// Mobile sidebar toggle
function toggleMobileSidebar() {
    const sidebar = document.querySelector('.admin-sidebar');
    const isOpen = sidebar.classList.contains('show');

    if (isOpen) {
        sidebar.classList.remove('show');
        removeMobileBackdrop();
    } else {
        sidebar.classList.add('show');
        addMobileBackdrop();
    }
}

// Close mobile sidebar when clicking outside
document.addEventListener('click', function(e) {
    const sidebar = document.querySelector('.admin-sidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle-btn');

    if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
        if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
            sidebar.classList.remove('show');
        }
    }
});

// Add mobile backdrop
function addMobileBackdrop() {
    if (window.innerWidth <= 768) {
        const sidebar = document.querySelector('.admin-sidebar');
        const existingBackdrop = document.querySelector('.mobile-backdrop');

        if (sidebar.classList.contains('show') && !existingBackdrop) {
            const backdrop = document.createElement('div');
            backdrop.className = 'mobile-backdrop';
            backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            document.body.appendChild(backdrop);

            // Fade in backdrop
            setTimeout(() => {
                backdrop.style.opacity = '1';
            }, 10);

            // Close sidebar when backdrop is clicked
            backdrop.addEventListener('click', function() {
                sidebar.classList.remove('show');
                removeMobileBackdrop();
            });
        } else if (!sidebar.classList.contains('show') && existingBackdrop) {
            removeMobileBackdrop();
        }
    }
}

function removeMobileBackdrop() {
    const backdrop = document.querySelector('.mobile-backdrop');
    if (backdrop) {
        backdrop.style.opacity = '0';
        setTimeout(() => {
            backdrop.remove();
        }, 300);
    }
}

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.admin-sidebar');
    if (window.innerWidth > 768) {
        sidebar.classList.remove('show');
        removeMobileBackdrop();
    }
});

// Refresh button functionality
document.querySelector('.header-actions .btn')?.addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
    this.disabled = true;

    setTimeout(() => {
        window.location.reload();
    }, 1000);
});

// Add smooth scrolling to sidebar
const sidebarElement = document.querySelector('.admin-sidebar');
if (sidebarElement) {
    sidebarElement.style.scrollBehavior = 'smooth';
}

// Enhanced menu item interactions
document.querySelectorAll('.menu-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        if (document.querySelector('.admin-sidebar').classList.contains('collapsed')) {
            this.style.transform = 'scale(1.05)';
        }
    });

    item.addEventListener('mouseleave', function() {
        this.style.transform = '';
    });
});
</script>

<?php include '../includes/footer.php'; ?>
