    </main>
</div>

<!-- Modern Admin Functionality -->
<script>
// Modern Admin Panel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeModernAdmin();
});

function initializeModernAdmin() {
    // Initialize sidebar state
    initializeSidebarState();

    // Initialize search functionality
    initializeSearch();

    // Initialize collapsible sections
    initializeCollapsibleSections();

    // Initialize mobile toggle
    initializeMobileToggle();

    // Initialize smooth scrolling
    initializeSmoothScrolling();

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

// Initialize Sidebar State
function initializeSidebarState() {
    const sidebar = document.querySelector('.admin-sidebar');
    const isCollapsed = localStorage.getItem('admin-sidebar-collapsed') === 'true';

    if (isCollapsed) {
        sidebar.classList.add('collapsed');
    }
}

// Modern Search Functionality
function initializeSearch() {
    const searchInput = document.getElementById('adminSearch');
    const searchResults = document.getElementById('searchResults');

    if (!searchInput) return;

    const menuItems = document.querySelectorAll('.menu-item');
    const searchData = Array.from(menuItems).map(item => ({
        text: item.textContent.trim(),
        href: item.href,
        icon: item.querySelector('i').className
    }));

    searchInput.addEventListener('input', function(e) {
        const query = e.target.value.toLowerCase().trim();

        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        const results = searchData.filter(item =>
            item.text.toLowerCase().includes(query)
        ).slice(0, 5);

        if (results.length > 0) {
            searchResults.innerHTML = results.map(item =>
                `<a href="${item.href}" class="search-result-item">
                    <i class="${item.icon}"></i>
                    <span>${item.text}</span>
                </a>`
            ).join('');
            searchResults.style.display = 'block';
        } else {
            searchResults.innerHTML = '<div class="no-results">No results found</div>';
            searchResults.style.display = 'block';
        }
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
}

// Collapsible Sections
function toggleSection(sectionId) {
    const section = document.querySelector(`[data-section="${sectionId}"]`);
    if (section) {
        section.classList.toggle('collapsed');

        // Save state to localStorage
        const collapsed = section.classList.contains('collapsed');
        localStorage.setItem(`admin-section-${sectionId}`, collapsed);
    }
}

function initializeCollapsibleSections() {
    // Restore saved states
    document.querySelectorAll('[data-section]').forEach(section => {
        const sectionId = section.dataset.section;
        const isCollapsed = localStorage.getItem(`admin-section-${sectionId}`) === 'true';

        if (isCollapsed) {
            section.classList.add('collapsed');
        }
    });
}

// Mobile Toggle
function toggleSidebar() {
    document.querySelector('.admin-sidebar').classList.toggle('show');
}

function initializeMobileToggle() {
    if (window.innerWidth <= 768) {
        const header = document.querySelector('.main-header');
        if (header && !header.querySelector('.mobile-toggle')) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'btn btn-outline-light btn-sm me-2 d-md-none mobile-toggle';
            toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
            toggleBtn.onclick = toggleSidebar;
            header.insertBefore(toggleBtn, header.firstChild);
        }
    }
}

// Smooth Scrolling
function initializeSmoothScrolling() {
    const sidebar = document.querySelector('.admin-sidebar');
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }
}

// WordPress-Style Sidebar Toggle
function toggleSidebarCollapse() {
    const sidebar = document.querySelector('.admin-sidebar');
    const isCollapsed = sidebar.classList.contains('collapsed');

    if (isCollapsed) {
        sidebar.classList.remove('collapsed');
        localStorage.setItem('admin-sidebar-collapsed', 'false');
    } else {
        sidebar.classList.add('collapsed');
        localStorage.setItem('admin-sidebar-collapsed', 'true');
    }

    // Add smooth animation class
    sidebar.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
}

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('adminSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Ctrl/Cmd + B for sidebar toggle
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebarCollapse();
        }

        // Escape to close search
        if (e.key === 'Escape') {
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                searchResults.style.display = 'none';
            }
        }
    });
}
</script>
