<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

function isActiveMenu($page, $current) {
    return $page === $current ? 'active' : '';
}

function isActiveSection($pages, $current) {
    return in_array($current, $pages) ? 'active' : '';
}

// Get dynamic counts for sidebar badges
$sidebar_counts = [
    'places' => 0,
    'users' => 0,
    'beer_items' => 0,
    'food_items' => 0,
    'coupons' => 0,
    'user_roles' => 0
];

try {
    require_once '../config/config.php';
    $db = new Database();
    $conn = $db->getConnection();

    // Count places
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM breweries");
    $stmt->execute();
    $sidebar_counts['places'] = $stmt->fetch()['count'];

    // Count users
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $sidebar_counts['users'] = $stmt->fetch()['count'];

    // Count beer menu items
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu");
    $stmt->execute();
    $sidebar_counts['beer_items'] = $stmt->fetch()['count'];

    // Count food menu items (if table exists)
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM food_menu");
        $stmt->execute();
        $sidebar_counts['food_items'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $sidebar_counts['food_items'] = 0;
    }

    // Count coupons (if table exists)
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM coupons");
        $stmt->execute();
        $sidebar_counts['coupons'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $sidebar_counts['coupons'] = 0;
    }

    // Count distinct user roles
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT role) as count FROM users");
    $stmt->execute();
    $sidebar_counts['user_roles'] = $stmt->fetch()['count'];

} catch (Exception $e) {
    // If database connection fails, keep default values
    error_log("Sidebar counts error: " . $e->getMessage());
}
?>

<!-- Admin Dashboard with Sidebar -->
<div class="admin-layout">
    <!-- Sidebar -->
    <nav class="admin-sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-cog me-2"></i>Admin Panel</h4>
            <button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Modern Search Bar -->
        <div class="sidebar-search">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search admin..." id="adminSearch">
                <div class="search-results" id="searchResults"></div>
            </div>
        </div>

        <div class="sidebar-menu">
            <!-- Dashboard -->
            <div class="menu-section">
                <a href="dashboard.php" class="menu-item <?php echo isActiveMenu('dashboard.php', $current_page); ?>" data-tooltip="Dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            
            <!-- Places Management -->
            <div class="menu-section" data-section="places">
                <div class="menu-title collapsible" onclick="toggleSection('places')">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>Places Management</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="places-items">
                    <a href="breweries.php" class="menu-item <?php echo isActiveMenu('breweries.php', $current_page); ?>" data-tooltip="Manage Places">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Manage Places</span>
                        <span class="item-badge"><?php echo number_format($sidebar_counts['places']); ?></span>
                    </a>
                    <a href="import-us-breweries.php" class="menu-item <?php echo isActiveMenu('import-us-breweries.php', $current_page); ?>" data-tooltip="Import US Places">
                        <i class="fas fa-database"></i>
                        <span>Import US Places</span>
                        <span class="item-badge new">New</span>
                    </a>
                    <a href="brewery-import.php" class="menu-item <?php echo isActiveMenu('brewery-import.php', $current_page); ?>" data-tooltip="Upload Places CSV">
                        <i class="fas fa-file-csv"></i>
                        <span>Upload Places CSV</span>
                    </a>
                </div>
            </div>
            
            <!-- User Management -->
            <div class="menu-section" data-section="users">
                <div class="menu-title collapsible" onclick="toggleSection('users')">
                    <i class="fas fa-users me-2"></i>
                    <span>User Management</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="users-items">
                    <a href="user-management.php" class="menu-item <?php echo isActiveMenu('user-management.php', $current_page); ?>" data-tooltip="Manage Users">
                        <i class="fas fa-users"></i>
                        <span>Manage Users</span>
                        <span class="item-badge"><?php echo number_format($sidebar_counts['users']); ?></span>
                    </a>
                    <a href="users.php" class="menu-item <?php echo isActiveMenu('users.php', $current_page); ?>" data-tooltip="User List">
                        <i class="fas fa-user-list"></i>
                        <span>User List</span>
                    </a>
                    <a href="user-roles.php" class="menu-item <?php echo isActiveMenu('user-roles.php', $current_page); ?>" data-tooltip="User Roles">
                        <i class="fas fa-user-shield"></i>
                        <span>User Roles</span>
                        <span class="item-badge warning"><?php echo number_format($sidebar_counts['user_roles']); ?></span>
                    </a>
                </div>
            </div>
            
            <!-- Content Management -->
            <div class="menu-section" data-section="content">
                <div class="menu-title collapsible" onclick="toggleSection('content')">
                    <i class="fas fa-clipboard-list me-2"></i>
                    <span>Content Management</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="content-items">
                    <a href="beer-menu.php" class="menu-item <?php echo isActiveMenu('beer-menu.php', $current_page); ?>" data-tooltip="Beer Menus">
                        <i class="fas fa-beer"></i>
                        <span>Beer Menus</span>
                        <span class="item-badge"><?php echo number_format($sidebar_counts['beer_items']); ?></span>
                    </a>
                    <a href="food-menu.php" class="menu-item <?php echo isActiveMenu('food-menu.php', $current_page); ?>" data-tooltip="Food Menus">
                        <i class="fas fa-utensils"></i>
                        <span>Food Menus</span>
                        <span class="item-badge"><?php echo number_format($sidebar_counts['food_items']); ?></span>
                    </a>
                    <a href="menu-management.php" class="menu-item <?php echo isActiveMenu('menu-management.php', $current_page); ?>" data-tooltip="Menu Management">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Menu Management</span>
                    </a>
                    <a href="coupons.php" class="menu-item <?php echo isActiveMenu('coupons.php', $current_page); ?>" data-tooltip="Coupons & Deals">
                        <i class="fas fa-tags"></i>
                        <span>Coupons & Deals</span>
                        <span class="item-badge warning"><?php echo number_format($sidebar_counts['coupons']); ?></span>
                    </a>
                    <a href="category-management.php" class="menu-item <?php echo isActiveMenu('category-management.php', $current_page); ?>" data-tooltip="Categories">
                        <i class="fas fa-folder"></i>
                        <span>Categories</span>
                    </a>
                </div>
            </div>
            
            <!-- Data Management -->
            <div class="menu-section" data-section="data">
                <div class="menu-title collapsible" onclick="toggleSection('data')">
                    <i class="fas fa-database me-2"></i>
                    <span>Data Management</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="data-items">
                    <a href="csv-import.php" class="menu-item <?php echo isActiveMenu('csv-import.php', $current_page); ?>" data-tooltip="CSV Import">
                        <i class="fas fa-upload"></i>
                        <span>CSV Import</span>
                    </a>
                    <a href="csv-menu-management.php" class="menu-item <?php echo isActiveMenu('csv-menu-management.php', $current_page); ?>" data-tooltip="CSV Menu Management">
                        <i class="fas fa-file-csv"></i>
                        <span>CSV Menu Management</span>
                    </a>
                    <a href="data-export.php" class="menu-item <?php echo isActiveMenu('data-export.php', $current_page); ?>" data-tooltip="Data Export">
                        <i class="fas fa-download"></i>
                        <span>Data Export</span>
                    </a>
                </div>
            </div>

            <!-- Media Management -->
            <div class="menu-section" data-section="media">
                <div class="menu-title collapsible" onclick="toggleSection('media')">
                    <i class="fas fa-images me-2"></i>
                    <span>Media Management</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="media-items">
                    <a href="photo-management.php" class="menu-item <?php echo isActiveMenu('photo-management.php', $current_page); ?>" data-tooltip="Photo Management">
                        <i class="fas fa-images"></i>
                        <span>Photo Management</span>
                    </a>
                    <a href="album-management.php" class="menu-item <?php echo isActiveMenu('album-management.php', $current_page); ?>" data-tooltip="Album Management">
                        <i class="fas fa-folder-open"></i>
                        <span>Album Management</span>
                    </a>
                </div>
            </div>
            
            <!-- Analytics & Reports -->
            <div class="menu-section" data-section="analytics">
                <div class="menu-title collapsible" onclick="toggleSection('analytics')">
                    <i class="fas fa-chart-line me-2"></i>
                    <span>Analytics & Reports</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="analytics-items">
                    <a href="analytics.php" class="menu-item <?php echo isActiveMenu('analytics.php', $current_page); ?>" data-tooltip="Analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="reports.php" class="menu-item <?php echo isActiveMenu('reports.php', $current_page); ?>" data-tooltip="Reports">
                        <i class="fas fa-file-alt"></i>
                        <span>Reports</span>
                    </a>
                </div>
            </div>

            <!-- System -->
            <div class="menu-section" data-section="system">
                <div class="menu-title collapsible" onclick="toggleSection('system')">
                    <i class="fas fa-cogs me-2"></i>
                    <span>System</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items" id="system-items">
                    <a href="settings.php" class="menu-item <?php echo isActiveMenu('settings.php', $current_page); ?>" data-tooltip="Settings">
                        <i class="fas fa-cogs"></i>
                        <span>Settings</span>
                    </a>
                    <a href="logs.php" class="menu-item <?php echo isActiveMenu('logs.php', $current_page); ?>" data-tooltip="System Logs">
                        <i class="fas fa-list-alt"></i>
                        <span>System Logs</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="admin-main">
