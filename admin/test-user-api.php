<?php
/**
 * Test User API
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Test User API</title></head><body>";
echo "<h1>🧪 Testing User API...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Get first user
    $stmt = $conn->query("SELECT * FROM users LIMIT 1");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        $userId = $user['id'];
        echo "📋 Testing with user ID: $userId<br>";
        echo "User data: " . json_encode($user) . "<br><br>";
        
        // Test GET request
        echo "🔄 Testing GET request...<br>";
        $url = "user-api.php?action=get&id=$userId";
        echo "URL: $url<br>";
        
        // Simulate the API call
        $_GET['action'] = 'get';
        $_GET['id'] = $userId;
        
        ob_start();
        include 'user-api.php';
        $response = ob_get_clean();
        
        echo "Response: $response<br><br>";
        
        // Test view request
        echo "🔄 Testing VIEW request...<br>";
        $_GET['action'] = 'view';
        $_GET['id'] = $userId;
        
        ob_start();
        include 'user-api.php';
        $response = ob_get_clean();
        
        echo "Response: $response<br>";
        
    } else {
        echo "❌ No users found in database<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to User Management</a>";
echo "</body></html>";
?>
