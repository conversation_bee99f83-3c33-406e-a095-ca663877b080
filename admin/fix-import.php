<?php
/**
 * Fix Import - Check table structure and fix import
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Fix Import';
$additionalCSS = ['../assets/css/admin.css'];

echo "<!DOCTYPE html><html><head><title>Fix Import</title></head><body>";
echo "<h1>🔧 Fixing Import Issues...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check table structure
    $stmt = $conn->query("DESCRIBE breweries");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 Current table structure:<br>";
    $columnNames = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    echo "<br>🔧 Fixing import script...<br>";
    
    // Check if status column exists
    $hasStatus = in_array('status', $columnNames);
    $hasVerified = in_array('verified', $columnNames);
    
    echo "Status column exists: " . ($hasStatus ? "✅ Yes" : "❌ No") . "<br>";
    echo "Verified column exists: " . ($hasVerified ? "✅ Yes" : "❌ No") . "<br>";
    
    // Create the correct insert statement based on available columns
    $insertFields = ['id', 'name'];
    $insertPlaceholders = ['?', '?'];
    $insertValues = [];
    
    if (in_array('address', $columnNames)) {
        $insertFields[] = 'address';
        $insertPlaceholders[] = '?';
    }
    if (in_array('city', $columnNames)) {
        $insertFields[] = 'city';
        $insertPlaceholders[] = '?';
    }
    if (in_array('state', $columnNames)) {
        $insertFields[] = 'state';
        $insertPlaceholders[] = '?';
    }
    if (in_array('website', $columnNames)) {
        $insertFields[] = 'website';
        $insertPlaceholders[] = '?';
    }
    if (in_array('brewery_type', $columnNames)) {
        $insertFields[] = 'brewery_type';
        $insertPlaceholders[] = '?';
    }
    if (in_array('verified', $columnNames)) {
        $insertFields[] = 'verified';
        $insertPlaceholders[] = '1';
    }
    if (in_array('status', $columnNames)) {
        $insertFields[] = 'status';
        $insertPlaceholders[] = "'active'";
    }
    if (in_array('created_at', $columnNames)) {
        $insertFields[] = 'created_at';
        $insertPlaceholders[] = 'NOW()';
    }
    if (in_array('updated_at', $columnNames)) {
        $insertFields[] = 'updated_at';
        $insertPlaceholders[] = 'NOW()';
    }
    
    $insertSQL = "INSERT INTO breweries (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $insertPlaceholders) . ")";
    echo "<br>📝 Insert SQL: $insertSQL<br>";
    
    // Create update statement
    $updateFields = [];
    if (in_array('address', $columnNames)) $updateFields[] = 'address = ?';
    if (in_array('city', $columnNames)) $updateFields[] = 'city = ?';
    if (in_array('state', $columnNames)) $updateFields[] = 'state = ?';
    if (in_array('website', $columnNames)) $updateFields[] = 'website = ?';
    if (in_array('brewery_type', $columnNames)) $updateFields[] = 'brewery_type = ?';
    if (in_array('updated_at', $columnNames)) $updateFields[] = 'updated_at = NOW()';
    
    $updateSQL = "UPDATE breweries SET " . implode(', ', $updateFields) . " WHERE id = ?";
    echo "📝 Update SQL: $updateSQL<br>";
    
    echo "<br>🚀 Starting corrected import...<br><br>";
    
    // Now run the import with correct SQL
    $csvPath = '../layouts-for-reference/us_breweries.csv';
    $csvData = file_get_contents($csvPath);
    $lines = str_getcsv($csvData, "\n");
    
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    // Prepare statements with correct SQL
    $insertStmt = $conn->prepare($insertSQL);
    $updateStmt = $conn->prepare($updateSQL);
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
    
    // Process each row
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (count($row) < 8) {
            $stats['skipped']++;
            continue;
        }
        
        // Map CSV columns
        $beerstyId = (int)trim($row[0]);
        $name = trim($row[2]);
        $breweryType = trim($row[3]);
        $street = trim($row[4]);
        $city = trim($row[5]);
        $state = trim($row[6]);
        $websiteUrl = trim($row[7]);
        
        // Skip if no name or invalid beersty_id
        if (empty($name) || $beerstyId <= 0) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean data
        $address = $street;
        $website = !empty($websiteUrl) ? $websiteUrl : null;
        
        // Validate brewery type
        $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub', 'closed', 'party_store'];
        if (!in_array(strtolower($breweryType), $validTypes)) {
            $breweryType = 'micro';
        }
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$beerstyId]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // Update existing brewery
                $updateParams = [];
                if (in_array('address', $columnNames)) $updateParams[] = $address;
                if (in_array('city', $columnNames)) $updateParams[] = $city;
                if (in_array('state', $columnNames)) $updateParams[] = $state;
                if (in_array('website', $columnNames)) $updateParams[] = $website;
                if (in_array('brewery_type', $columnNames)) $updateParams[] = $breweryType;
                $updateParams[] = $beerstyId; // WHERE clause
                
                $updateStmt->execute($updateParams);
                $stats['updated']++;
                echo "🔄 Updated: $name (ID: $beerstyId)<br>";
            } else {
                // Insert new brewery
                $insertParams = [$beerstyId, $name];
                if (in_array('address', $columnNames)) $insertParams[] = $address;
                if (in_array('city', $columnNames)) $insertParams[] = $city;
                if (in_array('state', $columnNames)) $insertParams[] = $state;
                if (in_array('website', $columnNames)) $insertParams[] = $website;
                if (in_array('brewery_type', $columnNames)) $insertParams[] = $breweryType;
                
                $insertStmt->execute($insertParams);
                $stats['inserted']++;
                echo "✅ Inserted: $name (ID: $beerstyId)<br>";
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row $i ($name): " . $e->getMessage();
            echo "❌ Error Row $i ($name): " . $e->getMessage() . "<br>";
        }
        
        // Progress update every 100 rows
        if ($stats['processed'] % 100 === 0) {
            echo "<br>📊 Progress: {$stats['processed']} rows processed...<br><br>";
            flush();
            ob_flush();
        }
        
        // Limit for testing
        if ($stats['processed'] >= 100) {
            echo "<br>🛑 Stopping at 100 rows for testing...<br>";
            break;
        }
    }
    
    echo "<br><br>🎉 Import Complete!<br>";
    echo "📊 Final Statistics:<br>";
    echo "- Total Rows: {$stats['total_rows']}<br>";
    echo "- Processed: {$stats['processed']}<br>";
    echo "- Inserted: {$stats['inserted']}<br>";
    echo "- Updated: {$stats['updated']}<br>";
    echo "- Skipped: {$stats['skipped']}<br>";
    echo "- Errors: " . count($stats['errors']) . "<br>";
    
} catch (Exception $e) {
    echo "💥 FATAL ERROR: " . $e->getMessage() . "<br>";
}

echo "</div>";
echo "<br><a href='breweries.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Breweries</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
