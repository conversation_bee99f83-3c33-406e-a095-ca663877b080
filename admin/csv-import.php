<?php
/**
 * CSV Data Import Script
 * Import Michigan and US brewery data from CSV files
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'CSV Data Import';
$additionalCSS = ['../assets/css/admin.css'];

// Handle import request
$importResult = null;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_file'])) {
    try {
        $importResult = importCSVFile($_POST['import_file']);
    } catch (Exception $e) {
        $errors[] = 'Import failed: ' . $e->getMessage();
    }
}

function importCSVFile($filename) {
    global $conn;
    
    $csvPath = '../csv-data/' . $filename;
    
    if (!file_exists($csvPath)) {
        throw new Exception("CSV file not found: $filename");
    }
    
    // Read CSV file
    $csvData = file_get_contents($csvPath);
    if ($csvData === false) {
        throw new Exception("Failed to read CSV file: $filename");
    }
    
    $lines = str_getcsv($csvData, "\n");
    if (count($lines) < 2) {
        throw new Exception("CSV file must have at least a header row and one data row");
    }
    
    // Parse header
    $headers = str_getcsv($lines[0]);
    
    // Expected columns: beersty_id, openbrewerydb_id, name, brewery_type, street, city, state, website_url
    $expectedColumns = ['beersty_id', 'openbrewerydb_id', 'name', 'brewery_type', 'street', 'city', 'state', 'website_url'];
    
    // Validate headers
    foreach ($expectedColumns as $col) {
        if (!in_array($col, $headers)) {
            throw new Exception("Missing required column: $col");
        }
    }
    
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // Prepare statements
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            name, address, city, state, brewery_type, website, 
            verified, claimed, follower_count, like_count,
            external_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 1, 0, 0, 0, ?, NOW(), NOW())
    ");
    
    $updateStmt = $conn->prepare("
        UPDATE breweries SET
            address = ?, city = ?, state = ?, brewery_type = ?, website = ?,
            external_id = ?, updated_at = NOW()
        WHERE name = ?
    ");
    
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE name = ? OR external_id = ?");
    
    // Process data rows
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (empty($row) || count($row) < count($headers)) {
            $stats['skipped']++;
            continue;
        }
        
        // Map row data
        $data = array_combine($headers, $row);
        
        // Skip if no name
        if (empty(trim($data['name']))) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean and prepare data
        $breweryName = trim($data['name']);
        $address = trim($data['street'] ?? '');
        $city = trim($data['city'] ?? '');
        $state = trim($data['state'] ?? '');
        $breweryType = cleanBreweryType(trim($data['brewery_type'] ?? ''));
        $website = cleanWebsite(trim($data['website_url'] ?? ''));
        $externalId = trim($data['openbrewerydb_id'] ?? '');
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$breweryName, $externalId]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // Update existing brewery
                $updateStmt->execute([
                    $address, $city, $state, $breweryType, $website, $externalId, $breweryName
                ]);
                $stats['updated']++;
            } else {
                // Insert new brewery
                $insertStmt->execute([
                    $breweryName, $address, $city, $state, $breweryType, $website, $externalId
                ]);
                $stats['inserted']++;
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row " . ($i + 1) . " ($breweryName): " . $e->getMessage();
        }
    }
    
    return ['success' => true, 'stats' => $stats];
}

function cleanBreweryType($type) {
    $type = strtolower(trim($type));
    $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub', 'party_store'];

    // Map common variations
    $typeMap = [
        'brewpub' => 'brewpub',
        'micro' => 'micro',
        'nano' => 'nano',
        'regional' => 'regional',
        'large' => 'large',
        'planning' => 'planning',
        'bar' => 'bar',
        'contract' => 'contract',
        'proprietor' => 'proprietor',
        'party_store' => 'party_store',
        'closed' => 'planning' // Map closed to planning
    ];

    return $typeMap[$type] ?? 'micro';
}

function cleanWebsite($url) {
    if (empty($url)) return '';
    
    // Add protocol if missing
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'https://' . $url;
    }
    
    return filter_var($url, FILTER_VALIDATE_URL) ? $url : '';
}

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-upload me-2"></i>CSV Data Import</h1>
            <div class="header-actions">
                <a href="download-template.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download me-1"></i>Download Template
                </a>
            </div>
        </div>

        <div class="dashboard-content">
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Import Errors</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($importResult): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Import Complete!</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li><strong>Total Rows:</strong> <?php echo $importResult['stats']['total_rows']; ?></li>
                                <li><strong>Processed:</strong> <?php echo $importResult['stats']['processed']; ?></li>
                                <li><strong>Inserted:</strong> <?php echo $importResult['stats']['inserted']; ?></li>
                                <li><strong>Updated:</strong> <?php echo $importResult['stats']['updated']; ?></li>
                                <li><strong>Skipped:</strong> <?php echo $importResult['stats']['skipped']; ?></li>
                            </ul>
                        </div>
                        <?php if (!empty($importResult['stats']['errors'])): ?>
                            <div class="col-md-6">
                                <strong>Errors:</strong>
                                <ul class="mb-0">
                                    <?php foreach (array_slice($importResult['stats']['errors'], 0, 5) as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                    <?php if (count($importResult['stats']['errors']) > 5): ?>
                                        <li><em>... and <?php echo count($importResult['stats']['errors']) - 5; ?> more errors</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-csv me-2"></i>Available CSV Files</h5>
                        </div>
                        <div class="card-body">
                            <p>Select a CSV file to import brewery data:</p>
                            
                            <?php
                            $csvFiles = [
                                'michigan_breweries.csv' => [
                                    'name' => 'Michigan Breweries',
                                    'description' => 'Michigan-specific brewery data (376 breweries)',
                                    'icon' => 'fas fa-map-marker-alt',
                                    'color' => 'primary'
                                ],
                                'us_breweries.csv' => [
                                    'name' => 'US Breweries',
                                    'description' => 'Complete US brewery database (8,104 breweries)',
                                    'icon' => 'fas fa-flag-usa',
                                    'color' => 'success'
                                ]
                            ];
                            
                            foreach ($csvFiles as $filename => $info):
                                $filePath = '../csv-data/' . $filename;
                                $fileExists = file_exists($filePath);
                                $fileSize = $fileExists ? formatFileSize(filesize($filePath)) : 'N/A';
                            ?>
                                <div class="card mb-3 <?php echo $fileExists ? '' : 'border-danger'; ?>">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h6 class="card-title">
                                                    <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?> me-2"></i>
                                                    <?php echo $info['name']; ?>
                                                </h6>
                                                <p class="card-text text-muted mb-1"><?php echo $info['description']; ?></p>
                                                <small class="text-muted">
                                                    <strong>File:</strong> <?php echo $filename; ?> 
                                                    <strong>Size:</strong> <?php echo $fileSize; ?>
                                                    <?php if ($fileExists): ?>
                                                        <span class="badge bg-success ms-2">Available</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger ms-2">Missing</span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <?php if ($fileExists): ?>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="import_file" value="<?php echo $filename; ?>">
                                                        <button type="submit" class="btn btn-<?php echo $info['color']; ?>" 
                                                                onclick="return confirm('Are you sure you want to import <?php echo $info['name']; ?>? This may take several minutes.')">
                                                            <i class="fas fa-download me-1"></i>Import
                                                        </button>
                                                    </form>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary" disabled>
                                                        <i class="fas fa-exclamation-triangle me-1"></i>File Missing
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Import Information</h5>
                        </div>
                        <div class="card-body">
                            <h6>CSV File Format:</h6>
                            <ul class="small">
                                <li><strong>beersty_id</strong> - Internal ID</li>
                                <li><strong>openbrewerydb_id</strong> - External reference</li>
                                <li><strong>name</strong> - Brewery name (required)</li>
                                <li><strong>brewery_type</strong> - Type of brewery</li>
                                <li><strong>street</strong> - Street address</li>
                                <li><strong>city</strong> - City name</li>
                                <li><strong>state</strong> - State/Province</li>
                                <li><strong>website_url</strong> - Website URL</li>
                            </ul>
                            
                            <h6 class="mt-3">Import Process:</h6>
                            <ol class="small">
                                <li>Validates CSV format and columns</li>
                                <li>Cleans and normalizes data</li>
                                <li>Checks for existing breweries</li>
                                <li>Updates existing or inserts new</li>
                                <li>Provides detailed import statistics</li>
                            </ol>
                            
                            <div class="alert alert-warning mt-3">
                                <small>
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    <strong>Note:</strong> Large imports may take several minutes. 
                                    Existing breweries will be updated based on name matching.
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar me-2"></i>Current Database</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = new Database();
                                $conn = $db->getConnection();
                                
                                $stmt = $conn->query("SELECT COUNT(*) as total FROM breweries");
                                $total = $stmt->fetch()['total'];
                                
                                $stmt = $conn->query("SELECT COUNT(*) as verified FROM breweries WHERE verified = 1");
                                $verified = $stmt->fetch()['verified'];
                                
                                $stmt = $conn->query("SELECT COUNT(*) as claimed FROM breweries WHERE claimed = 1");
                                $claimed = $stmt->fetch()['claimed'];
                                
                                $stmt = $conn->query("SELECT COUNT(DISTINCT state) as states FROM breweries");
                                $states = $stmt->fetch()['states'];
                            ?>
                                <ul class="mb-0">
                                    <li><strong>Total Breweries:</strong> <?php echo number_format($total); ?></li>
                                    <li><strong>Verified:</strong> <?php echo number_format($verified); ?></li>
                                    <li><strong>Claimed:</strong> <?php echo number_format($claimed); ?></li>
                                    <li><strong>States/Regions:</strong> <?php echo $states; ?></li>
                                </ul>
                            <?php
                            } catch (Exception $e) {
                                echo "<p class='text-danger'>Error loading database stats</p>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

        </div>

<?php
include 'includes/admin-layout-end.php';
include '../includes/footer.php';
?>
