<?php
/**
 * Fix Admin User ID
 * Ensure admin user has proper numeric ID for multiple admin support
 */

require_once '../config/config.php';
requireLogin();

echo "<!DOCTYPE html><html><head><title>Fix Admin ID</title></head><body>";
echo "<h1>🔧 Fixing Admin User ID...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current admin user
    $stmt = $conn->query("SELECT * FROM users WHERE email = '<EMAIL>'");
    $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin_user) {
        echo "📋 Current admin user:<br>";
        echo "ID: '{$admin_user['id']}'<br>";
        echo "Email: '{$admin_user['email']}'<br>";
        echo "Role: '{$admin_user['role']}'<br>";
        echo "Status: '{$admin_user['status']}'<br><br>";
        
        // Check if ID is numeric
        if (is_numeric($admin_user['id'])) {
            echo "✅ Admin user already has numeric ID: {$admin_user['id']}<br>";
        } else {
            echo "❌ Admin user has non-numeric ID: '{$admin_user['id']}'<br>";
            echo "🔄 Creating new admin user with proper numeric ID...<br>";
            
            // Create new admin user with proper ID
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("
                INSERT INTO users (
                    email, password_hash, role, status, first_name, last_name,
                    email_verified, created_at, updated_at
                ) VALUES (
                    '<EMAIL>', ?, 'admin', 'active', 'Admin', 'User',
                    1, NOW(), NOW()
                )
            ");
            
            try {
                $stmt->execute([$password]);
                $new_admin_id = $conn->lastInsertId();
                echo "✅ Created new admin user with ID: $new_admin_id<br>";
                
                // Delete old admin user with string ID
                $stmt = $conn->prepare("DELETE FROM users WHERE id = ? AND email = '<EMAIL>'");
                $stmt->execute([$admin_user['id']]);
                echo "✅ Removed old admin user with string ID<br>";
                
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    echo "⚠️ Admin user already exists, updating existing record...<br>";
                    
                    // Update existing admin user
                    $stmt = $conn->prepare("
                        UPDATE users SET 
                            password_hash = ?, role = 'admin', status = 'active',
                            first_name = 'Admin', last_name = 'User', email_verified = 1,
                            updated_at = NOW()
                        WHERE email = '<EMAIL>' AND id != ?
                    ");
                    $stmt->execute([$password, $admin_user['id']]);
                    
                    // Delete old string ID user
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$admin_user['id']]);
                    echo "✅ Updated existing admin and removed duplicate<br>";
                } else {
                    throw $e;
                }
            }
        }
    } else {
        echo "❌ No admin user found, creating new one...<br>";
        
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO users (
                email, password_hash, role, status, first_name, last_name,
                email_verified, created_at, updated_at
            ) VALUES (
                '<EMAIL>', ?, 'admin', 'active', 'Admin', 'User',
                1, NOW(), NOW()
            )
        ");
        $stmt->execute([$password]);
        
        $admin_id = $conn->lastInsertId();
        echo "✅ Created admin user with ID: $admin_id<br>";
    }
    
    // Show all admin users
    echo "<br>👥 All admin users:<br>";
    $stmt = $conn->query("SELECT id, email, role, status, first_name, last_name FROM users WHERE role = 'admin'");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($admins as $admin) {
        echo "- ID: {$admin['id']}, Email: {$admin['email']}, Name: {$admin['first_name']} {$admin['last_name']}, Status: {$admin['status']}<br>";
    }
    
    // Create additional admin users for testing multiple admins
    echo "<br>🔄 Creating additional admin users for testing...<br>";
    
    $additional_admins = [
        ['email' => '<EMAIL>', 'first_name' => 'Admin', 'last_name' => 'Two'],
        ['email' => '<EMAIL>', 'first_name' => 'Super', 'last_name' => 'Admin']
    ];
    
    foreach ($additional_admins as $admin_data) {
        // Check if admin already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$admin_data['email']]);
        
        if (!$stmt->fetch()) {
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("
                INSERT INTO users (
                    email, password_hash, role, status, first_name, last_name,
                    email_verified, created_at, updated_at
                ) VALUES (
                    ?, ?, 'admin', 'active', ?, ?,
                    1, NOW(), NOW()
                )
            ");
            $stmt->execute([
                $admin_data['email'], 
                $password, 
                $admin_data['first_name'], 
                $admin_data['last_name']
            ]);
            
            $new_id = $conn->lastInsertId();
            echo "✅ Created admin: {$admin_data['email']} (ID: $new_id)<br>";
        } else {
            echo "⚠️ Admin already exists: {$admin_data['email']}<br>";
        }
    }
    
    // Final check - show all users with their IDs
    echo "<br>📊 All users in system:<br>";
    $stmt = $conn->query("SELECT id, email, role, status FROM users ORDER BY id");
    $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($all_users as $user) {
        $id_type = is_numeric($user['id']) ? 'numeric' : 'string';
        echo "- ID: {$user['id']} ($id_type), Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}<br>";
    }
    
    echo "<br>🎉 Admin ID fix complete!<br>";
    echo "📝 All admin users now have proper numeric IDs<br>";
    echo "🔐 Default password for all admin accounts: admin123<br>";
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
