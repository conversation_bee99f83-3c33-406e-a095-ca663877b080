<?php
/**
 * Fix User Roles - Update role values to match expected format
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Fix Roles</title></head><body>";
echo "<h1>🔧 Fixing User Roles...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current roles
    $stmt = $conn->query("SELECT DISTINCT role FROM users WHERE role IS NOT NULL");
    $current_roles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Current roles in database: " . implode(', ', $current_roles) . "<br>";
    
    // Expected roles
    $expected_roles = ['admin', 'site_moderator', 'business_owner', 'business_manager', 'user'];
    echo "📋 Expected roles: " . implode(', ', $expected_roles) . "<br><br>";
    
    // Check if we need to fix any roles
    $role_mapping = [
        'Administrator' => 'admin',
        'Site Moderator' => 'site_moderator',
        'Business Owner' => 'business_owner',
        'Business Manager' => 'business_manager',
        'Standard User' => 'user',
        'User' => 'user'
    ];
    
    $fixed_count = 0;
    
    foreach ($role_mapping as $old_role => $new_role) {
        if (in_array($old_role, $current_roles)) {
            echo "🔄 Fixing role '$old_role' → '$new_role'<br>";
            
            $stmt = $conn->prepare("UPDATE users SET role = ? WHERE role = ?");
            $stmt->execute([$new_role, $old_role]);
            
            $affected = $stmt->rowCount();
            echo "✅ Updated $affected users<br>";
            $fixed_count += $affected;
        }
    }
    
    // Set default role for users with NULL role
    $stmt = $conn->prepare("UPDATE users SET role = 'user' WHERE role IS NULL OR role = ''");
    $stmt->execute();
    $null_fixed = $stmt->rowCount();
    
    if ($null_fixed > 0) {
        echo "🔄 Set default role for $null_fixed users with NULL/empty role<br>";
        $fixed_count += $null_fixed;
    }
    
    // Check final state
    $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $final_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<br>📊 Final role distribution:<br>";
    foreach ($final_roles as $role_data) {
        echo "- {$role_data['role']}: {$role_data['count']} users<br>";
    }
    
    echo "<br>🎉 Fixed $fixed_count user roles<br>";
    
    // Also check and fix status values
    echo "<br>🔄 Checking status values...<br>";
    
    $stmt = $conn->query("SELECT DISTINCT status FROM users WHERE status IS NOT NULL");
    $current_statuses = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Current statuses: " . implode(', ', $current_statuses) . "<br>";
    
    $status_mapping = [
        'Active' => 'active',
        'Inactive' => 'inactive',
        'Suspended' => 'suspended',
        'Pending' => 'pending'
    ];
    
    $status_fixed = 0;
    
    foreach ($status_mapping as $old_status => $new_status) {
        if (in_array($old_status, $current_statuses)) {
            echo "🔄 Fixing status '$old_status' → '$new_status'<br>";
            
            $stmt = $conn->prepare("UPDATE users SET status = ? WHERE status = ?");
            $stmt->execute([$new_status, $old_status]);
            
            $affected = $stmt->rowCount();
            echo "✅ Updated $affected users<br>";
            $status_fixed += $affected;
        }
    }
    
    // Set default status for users with NULL status
    $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE status IS NULL OR status = ''");
    $stmt->execute();
    $null_status_fixed = $stmt->rowCount();
    
    if ($null_status_fixed > 0) {
        echo "🔄 Set default status for $null_status_fixed users with NULL/empty status<br>";
        $status_fixed += $null_status_fixed;
    }
    
    echo "<br>🎉 Fixed $status_fixed user statuses<br>";
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to User Management</a>";
echo "</body></html>";
?>
