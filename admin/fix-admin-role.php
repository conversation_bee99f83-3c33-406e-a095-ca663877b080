<?php
/**
 * Fix Admin Role - Direct database update
 */

require_once '../config/config.php';
requireLogin();

echo "<!DOCTYPE html><html><head><title>Fix Admin Role</title></head><body>";
echo "<h1>🔧 Fixing Admin Role...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current user data
    $stmt = $conn->query("SELECT * FROM users WHERE email = '<EMAIL>'");
    $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin_user) {
        echo "📋 Current admin user data:<br>";
        foreach ($admin_user as $field => $value) {
            echo "  $field: '$value'<br>";
        }
        echo "<br>";
        
        // Update admin role directly
        echo "🔄 Updating admin role to 'admin'...<br>";
        $stmt = $conn->prepare("UPDATE users SET role = 'admin' WHERE email = '<EMAIL>'");
        $stmt->execute();
        
        echo "✅ Updated admin role<br>";
        
        // Verify the update
        $stmt = $conn->query("SELECT * FROM users WHERE email = '<EMAIL>'");
        $updated_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<br>📋 Updated admin user data:<br>";
        foreach ($updated_user as $field => $value) {
            echo "  $field: '$value'<br>";
        }
        
        // Test role mapping
        $user_roles = [
            'admin' => 'Administrator',
            'site_moderator' => 'Site Moderator',
            'business_owner' => 'Business Owner',
            'business_manager' => 'Business Manager',
            'user' => 'Standard User'
        ];
        
        $role = $updated_user['role'] ?? 'user';
        $mapped_role = $user_roles[$role] ?? $role;
        
        echo "<br>🔧 Role mapping test:<br>";
        echo "Database role: '$role'<br>";
        echo "Mapped display: '$mapped_role'<br>";
        
        if ($mapped_role === 'Administrator') {
            echo "✅ Role mapping is working correctly!<br>";
        } else {
            echo "❌ Role mapping failed<br>";
        }
        
    } else {
        echo "❌ Admin user not found<br>";
        
        // Create admin user
        echo "🔄 Creating admin user...<br>";
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO users (email, password, role, status, email_verified, created_at, updated_at) 
            VALUES ('<EMAIL>', ?, 'admin', 'active', 1, NOW(), NOW())
        ");
        $stmt->execute([$password]);
        
        echo "✅ Created admin user<br>";
        echo "📧 Email: <EMAIL><br>";
        echo "🔑 Password: admin123<br>";
    }
    
    // Check all users with their roles
    echo "<br>👥 All users and their roles:<br>";
    $stmt = $conn->query("SELECT id, email, role, status FROM users");
    $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($all_users as $user) {
        $role = $user['role'] ?? 'user';
        $mapped_role = $user_roles[$role] ?? $role;
        echo "ID: {$user['id']}, Email: {$user['email']}, Role: '$role' → '$mapped_role', Status: {$user['status']}<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to User Management</a>";
echo "</body></html>";
?>
