<?php
/**
 * Check all requirements for ADD USER functionality
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<h2>ADD USER Requirements Check</h2>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<div class='alert alert-success'>✅ Database connection successful</div>";
    
    // Check users table structure
    echo "<h3>1. Users Table Structure</h3>";
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Check required columns
    echo "<h3>2. Required Columns Check</h3>";
    $required_columns = ['email', 'password', 'role', 'status'];
    $available_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $req_col) {
        if (in_array($req_col, $available_columns)) {
            echo "<div class='alert alert-success'>✅ Column '$req_col' exists</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ Column '$req_col' missing</div>";
        }
    }
    
    // Check password field specifically
    echo "<h3>3. Password Field Check</h3>";
    $password_field = in_array('password', $available_columns) ? 'password' : 
                     (in_array('password_hash', $available_columns) ? 'password_hash' : 'none');
    echo "<div class='alert alert-info'>Password field detected: <strong>$password_field</strong></div>";
    
    // Test a simple insert
    echo "<h3>4. Test Insert (Dry Run)</h3>";
    $test_email = 'test_' . time() . '@example.com';
    $test_password = password_hash('test123', PASSWORD_DEFAULT);
    
    // Build test insert
    $insertFields = ['email', $password_field];
    $insertValues = [$test_email, $test_password];
    $placeholders = ['?', '?'];
    
    if (in_array('username', $available_columns)) {
        $insertFields[] = 'username';
        $insertValues[] = 'testuser_' . time();
        $placeholders[] = '?';
    }
    if (in_array('role', $available_columns)) {
        $insertFields[] = 'role';
        $insertValues[] = 'user';
        $placeholders[] = '?';
    }
    if (in_array('status', $available_columns)) {
        $insertFields[] = 'status';
        $insertValues[] = 'active';
        $placeholders[] = '?';
    }
    if (in_array('created_at', $available_columns)) {
        $insertFields[] = 'created_at';
        $placeholders[] = 'NOW()';
    }
    
    $sql = "INSERT INTO users (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    echo "<div class='alert alert-info'>";
    echo "<strong>Test SQL:</strong><br>";
    echo "<code>" . htmlspecialchars($sql) . "</code><br><br>";
    echo "<strong>Test Values:</strong><br>";
    echo "<pre>" . print_r($insertValues, true) . "</pre>";
    echo "</div>";
    
    // Check if we can prepare the statement
    try {
        $stmt = $conn->prepare($sql);
        echo "<div class='alert alert-success'>✅ SQL statement prepared successfully</div>";
        
        // Don't actually execute, just test preparation
        echo "<div class='alert alert-warning'>⚠️ SQL not executed (dry run only)</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ SQL preparation failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Check user-api.php file
    echo "<h3>5. API File Check</h3>";
    if (file_exists('user-api.php')) {
        echo "<div class='alert alert-success'>✅ user-api.php exists</div>";
        if (is_readable('user-api.php')) {
            echo "<div class='alert alert-success'>✅ user-api.php is readable</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ user-api.php is not readable</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ user-api.php not found</div>";
    }
    
    // Check session and permissions
    echo "<h3>6. Session and Permissions Check</h3>";
    echo "<div class='alert alert-info'>";
    echo "<strong>Current User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
    echo "<strong>Current User Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "<br>";
    echo "<strong>Is Admin:</strong> " . (($_SESSION['role'] ?? '') === 'admin' ? 'Yes' : 'No') . "<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>ADD USER Requirements Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-3">
        <a href="user-management.php" class="btn btn-secondary">← Back to User Management</a>
        <a href="test-add-user-modal.php" class="btn btn-primary">Test Add User Modal</a>
        <a href="test-add-user-api.php" class="btn btn-info">Test API Directly</a>
    </div>
</body>
</html>
