<?php
/**
 * Album Management System
 * Manage photo albums for users and places
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Album Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css', '../assets/css/album-management.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_album') {
        handleCreateAlbum($pdo);
    } elseif ($action === 'update_album') {
        handleUpdateAlbum($pdo);
    } elseif ($action === 'delete_album') {
        handleDeleteAlbum($pdo);
    }
}

// Handler functions
function handleCreateAlbum($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO photo_albums (id, owner_type, owner_id, name, description, is_public, sort_order, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $_POST['owner_type'] ?? 'user',
            $_POST['owner_id'] ?? '',
            $_POST['name'] ?? '',
            $_POST['description'] ?? '',
            isset($_POST['is_public']) ? 1 : 0,
            $_POST['sort_order'] ?? 0
        ]);
        
        $_SESSION['success_message'] = 'Album created successfully!';
        
    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Error creating album: ' . $e->getMessage();
    }
}

function handleUpdateAlbum($pdo) {
    $album_id = $_POST['album_id'] ?? null;
    
    if (!$album_id) {
        $_SESSION['error_message'] = 'Album ID is required';
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            UPDATE photo_albums SET 
                name = ?, description = ?, is_public = ?, sort_order = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            $_POST['name'] ?? '',
            $_POST['description'] ?? '',
            isset($_POST['is_public']) ? 1 : 0,
            $_POST['sort_order'] ?? 0,
            $album_id
        ]);
        
        $_SESSION['success_message'] = 'Album updated successfully!';
        
    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Error updating album: ' . $e->getMessage();
    }
}

function handleDeleteAlbum($pdo) {
    $album_id = $_POST['album_id'] ?? null;
    
    if (!$album_id) {
        $_SESSION['error_message'] = 'Album ID is required';
        return;
    }
    
    try {
        // First, move photos to no album (set album_id to NULL)
        $stmt = $pdo->prepare("UPDATE photos SET album_id = NULL WHERE album_id = ?");
        $stmt->execute([$album_id]);
        
        // Then delete the album
        $stmt = $pdo->prepare("DELETE FROM photo_albums WHERE id = ?");
        $stmt->execute([$album_id]);
        
        $_SESSION['success_message'] = 'Album deleted successfully! Photos have been moved to no album.';
        
    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'Error deleting album: ' . $e->getMessage();
    }
}

// Get filter parameters
$owner_type = $_GET['owner_type'] ?? 'all';
$owner_id = $_GET['owner_id'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(10, min(100, intval($_GET['limit'] ?? 20)));
$offset = ($page - 1) * $limit;

// Get owners for selection
try {
    $users = [];
    $places = [];
    
    if ($owner_type === 'all' || $owner_type === 'user') {
        $stmt = $pdo->query("SELECT id, email, username FROM users ORDER BY email LIMIT 100");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    if ($owner_type === 'all' || $owner_type === 'place') {
        $stmt = $pdo->query("SELECT id, name, city, state FROM breweries ORDER BY name LIMIT 100");
        $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $users = [];
    $places = [];
}

// Build album query
$where_conditions = [];
$params = [];

if ($owner_type !== 'all') {
    $where_conditions[] = "pa.owner_type = ?";
    $params[] = $owner_type;
}

if ($owner_id) {
    $where_conditions[] = "pa.owner_id = ?";
    $params[] = $owner_id;
}

if ($search) {
    $where_conditions[] = "(pa.name LIKE ? OR pa.description LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get albums with pagination
try {
    $sql = "
        SELECT 
            pa.*,
            CASE 
                WHEN pa.owner_type = 'user' THEN (SELECT email FROM users WHERE id = pa.owner_id)
                WHEN pa.owner_type = 'place' THEN (SELECT name FROM breweries WHERE id = pa.owner_id)
            END as owner_name,
            (SELECT COUNT(*) FROM photos WHERE album_id = pa.id) as photo_count,
            (SELECT filename FROM photos WHERE album_id = pa.id ORDER BY sort_order, created_at LIMIT 1) as cover_photo
        FROM photo_albums pa
        $where_clause
        ORDER BY pa.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $pdo->prepare($sql);

    // Bind parameters
    $param_index = 1;
    foreach ($params as $param) {
        $stmt->bindValue($param_index++, $param);
    }

    // Bind LIMIT and OFFSET as integers
    $stmt->bindValue($param_index++, (int)$limit, PDO::PARAM_INT);
    $stmt->bindValue($param_index, (int)$offset, PDO::PARAM_INT);

    $stmt->execute();
    $albums = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM photo_albums pa $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_albums = $count_stmt->fetchColumn();
    
    $total_pages = ceil($total_albums / $limit);
    
} catch (PDOException $e) {
    $albums = [];
    $total_albums = 0;
    $total_pages = 0;
    $error_message = "Error loading albums: " . $e->getMessage();
}

// Get statistics
try {
    $stats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums");
    $stats['total_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'user'");
    $stats['user_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'place'");
    $stats['place_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums WHERE is_public = 1");
    $stats['public_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos WHERE album_id IS NOT NULL");
    $stats['photos_in_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos WHERE album_id IS NULL");
    $stats['photos_no_album'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $stats = [
        'total_albums' => 0,
        'user_albums' => 0,
        'place_albums' => 0,
        'public_albums' => 0,
        'photos_in_albums' => 0,
        'photos_no_album' => 0
    ];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-folder me-2"></i>Album Management
                    </h1>
                    <p class="text-muted mb-0">Manage photo albums for users and places</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo url('admin/photo-management.php'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-images me-2"></i>Manage Photos
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createAlbumModal">
                        <i class="fas fa-plus me-2"></i>Create Album
                    </button>
                    <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4 stats-row">
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['total_albums']); ?></h5>
                    <p class="card-text small">Total Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['user_albums']); ?></h5>
                    <p class="card-text small">User Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['place_albums']); ?></h5>
                    <p class="card-text small">Place Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['public_albums']); ?></h5>
                    <p class="card-text small">Public Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['photos_in_albums']); ?></h5>
                    <p class="card-text small">Photos in Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['photos_no_album']); ?></h5>
                    <p class="card-text small">Unorganized Photos</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>Filters & Search</h5>
        </div>
        <div class="card-body filter-form">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Albums</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Album name, description...">
                </div>
                
                <div class="col-md-2">
                    <label for="owner_type" class="form-label">Owner Type</label>
                    <select class="form-select" id="owner_type" name="owner_type" onchange="updateOwnerOptions()">
                        <option value="all" <?php echo $owner_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                        <option value="user" <?php echo $owner_type === 'user' ? 'selected' : ''; ?>>Users</option>
                        <option value="place" <?php echo $owner_type === 'place' ? 'selected' : ''; ?>>Places</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="owner_id" class="form-label">Owner</label>
                    <select class="form-select" id="owner_id" name="owner_id">
                        <option value="">All Owners</option>
                        <?php if ($owner_type === 'all' || $owner_type === 'user'): ?>
                            <optgroup label="Users">
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $owner_id === $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['email']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>
                        <?php if ($owner_type === 'all' || $owner_type === 'place'): ?>
                            <optgroup label="Places">
                                <?php foreach ($places as $place): ?>
                                    <option value="<?php echo $place['id']; ?>" <?php echo $owner_id === $place['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($place['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="limit" class="form-label">Per Page</label>
                    <select class="form-select" id="limit" name="limit">
                        <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="col-12">
                    <a href="album-management.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Albums Grid -->
    <div class="card">
        <div class="card-header table-header-actions d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-folder me-2"></i>Albums (<?php echo number_format($total_albums); ?> total)</h5>
            <div class="text-muted small">
                Showing <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $limit, $total_albums)); ?>
                of <?php echo number_format($total_albums); ?> albums
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($albums)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No albums found</h6>
                    <p class="text-muted">Try adjusting your filters or create some albums.</p>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createAlbumModal">
                        <i class="fas fa-plus me-2"></i>Create Album
                    </button>
                </div>
            <?php else: ?>
                <div class="row album-grid">
                    <?php foreach ($albums as $album): ?>
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="album-card">
                                <!-- Album Cover -->
                                <div class="album-cover">
                                    <?php if ($album['cover_photo']): ?>
                                        <img src="../uploads/photos/<?php echo htmlspecialchars($album['cover_photo']); ?>"
                                             alt="<?php echo htmlspecialchars($album['name']); ?>" class="img-fluid">
                                    <?php else: ?>
                                        <div class="album-placeholder">
                                            <i class="fas fa-folder"></i>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Album Actions -->
                                    <div class="album-actions">
                                        <button class="btn btn-sm btn-outline-light" onclick="editAlbum('<?php echo $album['id']; ?>')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-light" onclick="deleteAlbum('<?php echo $album['id']; ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Photo Count Badge -->
                                    <div class="photo-count-badge">
                                        <span class="badge bg-dark">
                                            <i class="fas fa-images me-1"></i><?php echo $album['photo_count']; ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- Album Info -->
                                <div class="album-info">
                                    <h6 class="album-title"><?php echo htmlspecialchars($album['name']); ?></h6>

                                    <?php if ($album['description']): ?>
                                        <p class="album-description">
                                            <?php echo htmlspecialchars(substr($album['description'], 0, 100)); ?>
                                            <?php if (strlen($album['description']) > 100): ?>...<?php endif; ?>
                                        </p>
                                    <?php endif; ?>

                                    <div class="album-meta">
                                        <div class="album-owner">
                                            <i class="fas fa-<?php echo $album['owner_type'] === 'user' ? 'user' : 'building'; ?> me-1"></i>
                                            <small><?php echo ucfirst($album['owner_type']); ?>: <?php echo htmlspecialchars($album['owner_name']); ?></small>
                                        </div>

                                        <div class="album-stats">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i><?php echo date('M j, Y', strtotime($album['created_at'])); ?>
                                                <span class="ms-2">
                                                    <i class="fas fa-sort-numeric-up me-1"></i>Order: <?php echo $album['sort_order']; ?>
                                                </span>
                                            </small>
                                        </div>

                                        <div class="album-badges">
                                            <?php if ($album['is_public']): ?>
                                                <span class="badge bg-success">Public</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Private</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Album pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php
                $query_params = $_GET;
                unset($query_params['page']);
                $base_url = 'album-management.php?' . http_build_query($query_params);
                ?>

                <!-- Previous -->
                <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                </li>

                <!-- Page numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>

                <!-- Next -->
                <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                </li>
            </ul>
        </nav>
    <?php endif; ?>
</div>

<!-- Create Album Modal -->
<div class="modal fade" id="createAlbumModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Album</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="createAlbumForm">
                <input type="hidden" name="action" value="create_album">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="create_name" class="form-label">Album Name *</label>
                            <input type="text" class="form-control" id="create_name" name="name" required>
                        </div>

                        <div class="col-12">
                            <label for="create_description" class="form-label">Description</label>
                            <textarea class="form-control" id="create_description" name="description" rows="3"></textarea>
                        </div>

                        <div class="col-md-6">
                            <label for="create_owner_type" class="form-label">Owner Type *</label>
                            <select class="form-select" id="create_owner_type" name="owner_type" required onchange="updateCreateOwnerOptions()">
                                <option value="">Select Type</option>
                                <option value="user">User</option>
                                <option value="place">Place</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="create_owner_id" class="form-label">Owner *</label>
                            <select class="form-select" id="create_owner_id" name="owner_id" required>
                                <option value="">Select Owner</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="create_sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="create_sort_order" name="sort_order" value="0" min="0">
                        </div>

                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="create_is_public" name="is_public" value="1" checked>
                                <label class="form-check-label" for="create_is_public">Public Album</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Create Album</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Album Modal -->
<div class="modal fade" id="editAlbumModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Album</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editAlbumForm">
                <input type="hidden" name="action" value="update_album">
                <input type="hidden" name="album_id" id="edit_album_id">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="edit_name" class="form-label">Album Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>

                        <div class="col-12">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>

                        <div class="col-md-6">
                            <label for="edit_sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="edit_sort_order" name="sort_order" value="0" min="0">
                        </div>

                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="edit_is_public" name="is_public" value="1">
                                <label class="form-check-label" for="edit_is_public">Public Album</label>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="album-owner-info">
                                <label class="form-label">Owner</label>
                                <div class="owner-display">
                                    <span id="edit_owner_display" class="text-muted"></span>
                                </div>
                                <small class="text-muted">Owner cannot be changed after creation</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Album</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Album Modal -->
<div class="modal fade" id="deleteAlbumModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Album</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="deleteAlbumForm">
                <input type="hidden" name="action" value="delete_album">
                <input type="hidden" name="album_id" id="delete_album_id">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> This action cannot be undone.
                    </div>

                    <p>Are you sure you want to delete the album "<strong id="delete_album_name"></strong>"?</p>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Photos in this album will be moved to "No Album" and will not be deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Album</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../assets/js/album-management.js"></script>

<?php require_once '../includes/footer.php'; ?>
