<?php
/**
 * Debug User Query
 * Check what the user management query is actually returning
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Debug User Query</title></head><body>";
echo "<h1>🔍 Debug User Management Query...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Simulate the user management query
    $page = 1;
    $limit = 25;
    $search = '';
    $role_filter = '';
    $status_filter = '';
    $sort = 'created_at';
    $order = 'DESC';
    $offset = ($page - 1) * $limit;
    
    // Check what columns exist
    $stmt = $conn->query("DESCRIBE users");
    $available_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "📋 Available columns: " . implode(', ', $available_columns) . "<br><br>";
    
    // Build SELECT based on available columns (same as user-management.php)
    $select_fields = ['id'];
    if (in_array('username', $available_columns)) $select_fields[] = 'username';
    if (in_array('email', $available_columns)) $select_fields[] = 'email';
    if (in_array('first_name', $available_columns)) $select_fields[] = 'first_name';
    if (in_array('last_name', $available_columns)) $select_fields[] = 'last_name';
    if (in_array('role', $available_columns)) $select_fields[] = 'role';
    if (in_array('status', $available_columns)) $select_fields[] = 'status';
    if (in_array('created_at', $available_columns)) $select_fields[] = 'created_at';
    if (in_array('updated_at', $available_columns)) $select_fields[] = 'updated_at';
    if (in_array('last_login', $available_columns)) $select_fields[] = 'last_login';
    if (in_array('email_verified', $available_columns)) $select_fields[] = 'email_verified';
    if (in_array('phone', $available_columns)) $select_fields[] = 'phone';
    if (in_array('city', $available_columns)) $select_fields[] = 'city';
    if (in_array('state', $available_columns)) $select_fields[] = 'state';
    if (in_array('country', $available_columns)) $select_fields[] = 'country';
    
    echo "📊 SELECT fields: " . implode(', ', $select_fields) . "<br><br>";
    
    // Build the query
    $sql = "
        SELECT " . implode(', ', $select_fields) . "
        FROM users 
        ORDER BY $sort $order 
        LIMIT $limit OFFSET $offset
    ";
    
    echo "📝 Query: $sql<br><br>";
    
    // Execute the query
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Query returned " . count($users) . " users<br><br>";
    
    if (empty($users)) {
        echo "❌ No users returned by query!<br>";
        
        // Try a simple query
        echo "🔄 Trying simple query...<br>";
        $stmt = $conn->query("SELECT * FROM users");
        $simple_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📊 Simple query returned " . count($simple_users) . " users<br>";
        
        if (!empty($simple_users)) {
            echo "✅ Users exist, but complex query failed<br>";
            echo "🔍 Sample user data:<br>";
            foreach (array_slice($simple_users, 0, 3) as $user) {
                echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}<br>";
            }
        }
    } else {
        echo "✅ Users found!<br>";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}<br>";
        }
    }
    
    // Test count query
    echo "<br>🔄 Testing count query...<br>";
    $count_sql = "SELECT COUNT(*) FROM users";
    $count_stmt = $conn->query($count_sql);
    $total_users = $count_stmt->fetchColumn();
    echo "📊 Total users count: $total_users<br>";
    
    // Test role statistics
    echo "<br>🔄 Testing role statistics...<br>";
    if (in_array('role', $available_columns)) {
        $role_stats_sql = "SELECT role, COUNT(*) as count FROM users GROUP BY role";
        $role_stats_stmt = $conn->query($role_stats_sql);
        $role_stats = $role_stats_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        echo "📊 Role statistics:<br>";
        foreach ($role_stats as $role => $count) {
            echo "- $role: $count users<br>";
        }
    } else {
        echo "❌ No role column available<br>";
    }
    
    // Check if there's a WHERE clause issue
    echo "<br>🔄 Testing with different sort options...<br>";
    
    $test_sorts = ['id', 'email'];
    foreach ($test_sorts as $test_sort) {
        if (in_array($test_sort, $available_columns)) {
            $test_sql = "SELECT id, email, role FROM users ORDER BY $test_sort LIMIT 5";
            $test_stmt = $conn->query($test_sql);
            $test_users = $test_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "📊 Sort by $test_sort: " . count($test_users) . " users<br>";
        }
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "</body></html>";
?>
