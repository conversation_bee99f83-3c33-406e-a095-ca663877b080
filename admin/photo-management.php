<?php
/**
 * Photo Management System
 * Manage photos and albums for users and places
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Photo Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css', '../assets/css/photo-management.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get filter parameters
$owner_type = $_GET['owner_type'] ?? 'all';
$owner_id = $_GET['owner_id'] ?? '';
$album_id = $_GET['album_id'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(10, min(100, intval($_GET['limit'] ?? 20)));
$offset = ($page - 1) * $limit;

// Get owners for selection
try {
    $users = [];
    $places = [];
    
    if ($owner_type === 'all' || $owner_type === 'user') {
        $stmt = $pdo->query("SELECT id, email, username FROM users ORDER BY email LIMIT 100");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    if ($owner_type === 'all' || $owner_type === 'place') {
        $stmt = $pdo->query("SELECT id, name, city, state FROM breweries ORDER BY name LIMIT 100");
        $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $users = [];
    $places = [];
}

// Get albums for filter
$albums = [];
if ($owner_id) {
    try {
        $stmt = $pdo->prepare("SELECT id, name FROM photo_albums WHERE owner_id = ? ORDER BY sort_order, name");
        $stmt->execute([$owner_id]);
        $albums = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $albums = [];
    }
}

// Build photo query
$where_conditions = [];
$params = [];

if ($owner_type !== 'all') {
    $where_conditions[] = "p.owner_type = ?";
    $params[] = $owner_type;
}

if ($owner_id) {
    $where_conditions[] = "p.owner_id = ?";
    $params[] = $owner_id;
}

if ($album_id) {
    $where_conditions[] = "p.album_id = ?";
    $params[] = $album_id;
}

if ($search) {
    $where_conditions[] = "(p.title LIKE ? OR p.description LIKE ? OR p.original_filename LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get photos with pagination
try {
    $sql = "
        SELECT 
            p.*,
            pa.name as album_name,
            u.email as uploaded_by_email,
            CASE 
                WHEN p.owner_type = 'user' THEN (SELECT email FROM users WHERE id = p.owner_id)
                WHEN p.owner_type = 'place' THEN (SELECT name FROM breweries WHERE id = p.owner_id)
            END as owner_name
        FROM photos p
        LEFT JOIN photo_albums pa ON p.album_id = pa.id
        LEFT JOIN users u ON p.uploaded_by = u.id
        $where_clause
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $pdo->prepare($sql);

    // Bind parameters
    $param_index = 1;
    foreach ($params as $param) {
        $stmt->bindValue($param_index++, $param);
    }

    // Bind LIMIT and OFFSET as integers
    $stmt->bindValue($param_index++, (int)$limit, PDO::PARAM_INT);
    $stmt->bindValue($param_index, (int)$offset, PDO::PARAM_INT);

    $stmt->execute();
    $photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM photos p $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_photos = $count_stmt->fetchColumn();
    
    $total_pages = ceil($total_photos / $limit);
    
} catch (PDOException $e) {
    $photos = [];
    $total_photos = 0;
    $total_pages = 0;
    $error_message = "Error loading photos: " . $e->getMessage();
}

// Get statistics
try {
    $stats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos");
    $stats['total_photos'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums");
    $stats['total_albums'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos WHERE owner_type = 'user'");
    $stats['user_photos'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos WHERE owner_type = 'place'");
    $stats['place_photos'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos WHERE is_profile_photo = 1");
    $stats['profile_photos'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $stats = [
        'total_photos' => 0,
        'total_albums' => 0,
        'user_photos' => 0,
        'place_photos' => 0,
        'profile_photos' => 0
    ];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-images me-2"></i>Photo Management
                    </h1>
                    <p class="text-muted mb-0">Manage photos and albums for users and places</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo url('admin/album-management.php'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-folder me-2"></i>Manage Albums
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadPhotoModal">
                        <i class="fas fa-upload me-2"></i>Upload Photos
                    </button>
                    <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4 stats-row">
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['total_photos']); ?></h5>
                    <p class="card-text small">Total Photos</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['total_albums']); ?></h5>
                    <p class="card-text small">Total Albums</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['user_photos']); ?></h5>
                    <p class="card-text small">User Photos</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['place_photos']); ?></h5>
                    <p class="card-text small">Place Photos</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo number_format($stats['profile_photos']); ?></h5>
                    <p class="card-text small">Profile Photos</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo $total_photos > 0 ? number_format(($stats['total_photos'] / 1024), 1) . 'K' : '0'; ?></h5>
                    <p class="card-text small">Est. Storage</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>Filters & Search</h5>
        </div>
        <div class="card-body filter-form">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search Photos</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Title, description, filename...">
                </div>
                
                <div class="col-md-2">
                    <label for="owner_type" class="form-label">Owner Type</label>
                    <select class="form-select" id="owner_type" name="owner_type" onchange="updateOwnerOptions()">
                        <option value="all" <?php echo $owner_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                        <option value="user" <?php echo $owner_type === 'user' ? 'selected' : ''; ?>>Users</option>
                        <option value="place" <?php echo $owner_type === 'place' ? 'selected' : ''; ?>>Places</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="owner_id" class="form-label">Owner</label>
                    <select class="form-select" id="owner_id" name="owner_id" onchange="updateAlbumOptions()">
                        <option value="">All Owners</option>
                        <?php if ($owner_type === 'all' || $owner_type === 'user'): ?>
                            <optgroup label="Users">
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $owner_id === $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['email']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>
                        <?php if ($owner_type === 'all' || $owner_type === 'place'): ?>
                            <optgroup label="Places">
                                <?php foreach ($places as $place): ?>
                                    <option value="<?php echo $place['id']; ?>" <?php echo $owner_id === $place['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($place['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="album_id" class="form-label">Album</label>
                    <select class="form-select" id="album_id" name="album_id">
                        <option value="">All Albums</option>
                        <?php foreach ($albums as $album): ?>
                            <option value="<?php echo $album['id']; ?>" <?php echo $album_id === $album['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($album['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="limit" class="form-label">Per Page</label>
                    <select class="form-select" id="limit" name="limit">
                        <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                        <option value="20" <?php echo $limit == 20 ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
                
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Apply Filters
                    </button>
                    <a href="photo-management.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Photos Grid -->
    <div class="card">
        <div class="card-header table-header-actions d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-images me-2"></i>Photos (<?php echo number_format($total_photos); ?> total)</h5>
            <div class="text-muted small">
                Showing <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $limit, $total_photos)); ?>
                of <?php echo number_format($total_photos); ?> photos
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($photos)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No photos found</h6>
                    <p class="text-muted">Try adjusting your filters or upload some photos.</p>
                </div>
            <?php else: ?>
                <div class="row photo-grid">
                    <?php foreach ($photos as $photo): ?>
                        <div class="col-md-3 col-sm-4 col-6 mb-4">
                            <div class="photo-card">
                                <div class="photo-thumbnail">
                                    <img src="../uploads/photos/<?php echo htmlspecialchars($photo['filename']); ?>" 
                                         alt="<?php echo htmlspecialchars($photo['alt_text'] ?? $photo['title'] ?? 'Photo'); ?>"
                                         class="img-fluid"
                                         onclick="viewPhoto('<?php echo $photo['id']; ?>')">
                                    
                                    <!-- Photo badges -->
                                    <div class="photo-badges">
                                        <?php if ($photo['is_profile_photo']): ?>
                                            <span class="badge bg-primary">Profile</span>
                                        <?php endif; ?>
                                        <?php if ($photo['is_cover_photo']): ?>
                                            <span class="badge bg-info">Cover</span>
                                        <?php endif; ?>
                                        <?php if (!$photo['is_public']): ?>
                                            <span class="badge bg-warning">Private</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Photo actions -->
                                    <div class="photo-actions">
                                        <button class="btn btn-sm btn-outline-light" onclick="editPhoto('<?php echo $photo['id']; ?>')" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-light" onclick="deletePhoto('<?php echo $photo['id']; ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="photo-info">
                                    <h6 class="photo-title"><?php echo htmlspecialchars($photo['title'] ?? $photo['original_filename']); ?></h6>
                                    <p class="photo-meta">
                                        <small class="text-muted">
                                            <?php echo ucfirst($photo['owner_type']); ?>: <?php echo htmlspecialchars($photo['owner_name']); ?>
                                            <?php if ($photo['album_name']): ?>
                                                <br>Album: <?php echo htmlspecialchars($photo['album_name']); ?>
                                            <?php endif; ?>
                                            <br><?php echo date('M j, Y', strtotime($photo['created_at'])); ?>
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <nav aria-label="Photo pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php
                $query_params = $_GET;
                unset($query_params['page']);
                $base_url = 'photo-management.php?' . http_build_query($query_params);
                ?>
                
                <!-- Previous -->
                <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                </li>
                
                <!-- Page numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <!-- Next -->
                <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                    <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                </li>
            </ul>
        </nav>
    <?php endif; ?>
</div>

<script src="../assets/js/photo-management.js"></script>

<?php require_once '../includes/footer.php'; ?>
