<?php
/**
 * Test Import - Import first 10 breweries as a test
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Test Import';
$additionalCSS = ['../assets/css/admin.css'];

$testResult = null;
$errors = [];

if (isset($_POST['test_import'])) {
    try {
        $csvPath = '../layouts-for-reference/us_breweries.csv';
        
        if (!file_exists($csvPath)) {
            throw new Exception('CSV file not found: ' . $csvPath);
        }
        
        $testResult = testImportBreweries($csvPath);
        
    } catch (Exception $e) {
        $errors[] = 'Test import failed: ' . $e->getMessage();
    }
}

function testImportBreweries($csvPath) {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Read CSV file
    $csvData = file_get_contents($csvPath);
    if ($csvData === false) {
        throw new Exception('Failed to read CSV file');
    }
    
    $lines = str_getcsv($csvData, "\n");
    if (count($lines) < 2) {
        throw new Exception('CSV must have at least a header row and one data row');
    }
    
    // Parse header
    $headers = str_getcsv($lines[0]);
    
    // Initialize stats
    $stats = [
        'total_rows' => min(10, count($lines) - 1), // Test with first 10 rows
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => [],
        'sample_data' => []
    ];
    
    // Prepare statements
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            id, name, address, city, state, website, brewery_type, 
            verified, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, 'active', NOW(), NOW())
    ");
    
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
    
    // Process first 10 rows only
    for ($i = 1; $i <= min(10, count($lines) - 1); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (count($row) < 8) {
            $stats['skipped']++;
            $stats['errors'][] = "Row $i: Insufficient columns";
            continue;
        }
        
        // Map CSV columns
        $beerstyId = (int)trim($row[0]);
        $name = trim($row[2]);
        $breweryType = trim($row[3]);
        $street = trim($row[4]);
        $city = trim($row[5]);
        $state = trim($row[6]);
        $websiteUrl = trim($row[7]);
        
        // Skip if no name or invalid beersty_id
        if (empty($name) || $beerstyId <= 0) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean data
        $address = $street;
        $website = !empty($websiteUrl) ? $websiteUrl : null;
        
        // Validate brewery type
        $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub', 'closed', 'party_store'];
        if (!in_array(strtolower($breweryType), $validTypes)) {
            $breweryType = 'micro';
        }
        
        // Store sample data
        $stats['sample_data'][] = [
            'id' => $beerstyId,
            'name' => $name,
            'type' => $breweryType,
            'city' => $city,
            'state' => $state
        ];
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$beerstyId]);
            $exists = $checkStmt->fetch();
            
            if (!$exists) {
                // Insert new brewery
                $insertStmt->execute([
                    $beerstyId,
                    $name,
                    $address,
                    $city,
                    $state,
                    $website,
                    $breweryType
                ]);
                $stats['inserted']++;
            } else {
                $stats['updated']++;
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row $i ($name): " . $e->getMessage();
        }
    }
    
    return $stats;
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-flask text-primary me-2"></i>
                    Test Import (First 10 Breweries)
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group">
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Test Errors</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($testResult): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Test Import Complete!</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li><strong>Total Rows:</strong> <?php echo $testResult['total_rows']; ?></li>
                                <li><strong>Processed:</strong> <?php echo $testResult['processed']; ?></li>
                                <li><strong>Inserted:</strong> <?php echo $testResult['inserted']; ?></li>
                                <li><strong>Updated:</strong> <?php echo $testResult['updated']; ?></li>
                                <li><strong>Skipped:</strong> <?php echo $testResult['skipped']; ?></li>
                            </ul>
                        </div>
                        <?php if (!empty($testResult['errors'])): ?>
                            <div class="col-md-6">
                                <strong>Errors:</strong>
                                <ul class="mb-0">
                                    <?php foreach ($testResult['errors'] as $error): ?>
                                        <li><small><?php echo htmlspecialchars($error); ?></small></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (!empty($testResult['sample_data'])): ?>
                        <div class="mt-3">
                            <h6>Sample Imported Data:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>City</th>
                                            <th>State</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($testResult['sample_data'], 0, 5) as $brewery): ?>
                                            <tr>
                                                <td><?php echo $brewery['id']; ?></td>
                                                <td><?php echo htmlspecialchars($brewery['name']); ?></td>
                                                <td><?php echo htmlspecialchars($brewery['type']); ?></td>
                                                <td><?php echo htmlspecialchars($brewery['city']); ?></td>
                                                <td><?php echo htmlspecialchars($brewery['state']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-flask me-2"></i>Test Import Process</h5>
                        </div>
                        <div class="card-body">
                            <p>This will import the first 10 breweries as a test to verify the import process works correctly.</p>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Test Details:</h6>
                                <ul class="mb-0">
                                    <li><strong>Records:</strong> First 10 breweries only</li>
                                    <li><strong>Purpose:</strong> Verify import functionality</li>
                                    <li><strong>Safe:</strong> Limited scope for testing</li>
                                </ul>
                            </div>
                            
                            <?php if (!$testResult): ?>
                                <form method="POST">
                                    <div class="d-grid">
                                        <button type="submit" name="test_import" class="btn btn-success btn-lg">
                                            <i class="fas fa-flask me-2"></i>Run Test Import
                                        </button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <div class="d-grid gap-2">
                                    <a href="breweries.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-eye me-2"></i>View Imported Breweries
                                    </a>
                                    <a href="import-us-breweries.php" class="btn btn-warning btn-lg">
                                        <i class="fas fa-database me-2"></i>Run Full Import
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Why Test First?</h5>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li><strong>Verify Setup:</strong> Ensure database is ready</li>
                                <li><strong>Check Data:</strong> Validate CSV format</li>
                                <li><strong>Test Logic:</strong> Confirm import process</li>
                                <li><strong>Safe Testing:</strong> Limited to 10 records</li>
                            </ul>
                            
                            <div class="alert alert-warning">
                                <small>
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Run this test before the full import to catch any issues early.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
