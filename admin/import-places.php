<?php
/**
 * Places Import Interface
 * Web interface for the sophisticated CSV import system
 */

require_once '../config/config.php';
require_once 'places-csv-import.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Import Places - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$step = 1;
$analysis_result = null;
$import_result = null;
$error_message = null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'upload_analyze':
                    $step = 2;
                    $analysis_result = handleCSVUpload();
                    break;
                case 'execute_import':
                    $step = 3;
                    $import_result = handleImportExecution();
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        $step = 1;
    }
}

function handleCSVUpload() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Please select a valid CSV file');
    }

    $file = $_FILES['csv_file'];

    // Validate file
    if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
        throw new Exception('File size too large. Maximum 10MB allowed.');
    }

    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($file_extension !== 'csv') {
        throw new Exception('Please upload a CSV file');
    }

    // Process with importer
    global $pdo;
    $importer = new PlacesCSVImporter($pdo);
    $result = $importer->processCSVFile($file['tmp_name']);

    // Enhanced analysis
    $validation_results = $importer->validateData();
    $preview = $importer->generateImportPreview();

    // Data enhancement
    $enhanced_count = $importer->enhanceData();

    // Store importer in session for next step
    $_SESSION['csv_importer_data'] = serialize($importer);
    $_SESSION['csv_validation_results'] = $validation_results;
    $_SESSION['csv_preview'] = $preview;
    $_SESSION['csv_enhanced_count'] = $enhanced_count;

    return array_merge($result, [
        'validation' => $validation_results,
        'preview' => $preview,
        'enhanced_count' => $enhanced_count
    ]);
}

function handleImportExecution() {
    if (!isset($_SESSION['csv_importer_data'])) {
        throw new Exception('Import session expired. Please upload the CSV file again.');
    }

    // Restore importer from session
    $importer = unserialize($_SESSION['csv_importer_data']);

    // Set field mapping
    $field_mapping = $_POST['field_mapping'] ?? [];
    $importer->setFieldMapping($field_mapping);

    // Set import options
    $import_options = [
        'check_duplicates' => isset($_POST['check_duplicates']),
        'skip_duplicates' => isset($_POST['skip_duplicates']),
        'update_duplicates' => isset($_POST['update_duplicates']),
        'validate_data' => isset($_POST['validate_data'])
    ];
    $importer->setImportOptions($import_options);

    // Execute batch import with progress tracking
    $batch_size = intval($_POST['batch_size'] ?? 100);
    $result = $importer->executeBatchImport($batch_size);

    // Generate detailed report
    $report = $importer->generateImportReport();

    // Store report in session for viewing
    $_SESSION['import_report'] = $report;

    // Clear import session data
    unset($_SESSION['csv_importer_data']);
    unset($_SESSION['csv_validation_results']);
    unset($_SESSION['csv_preview']);
    unset($_SESSION['csv_enhanced_count']);

    return array_merge($result, ['report' => $report]);
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-upload me-2"></i>Import Places
                    </h1>
                    <p class="text-muted mb-0">Import places from CSV with field mapping and duplicate detection</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="download-places-template.php" class="btn btn-outline-info">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                    <a href="breweries.php" class="btn btn-outline-secondary">
                        <i class="fas fa-map-marker-alt me-2"></i>Manage Places
                    </a>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Step 1: Upload CSV -->
    <?php if ($step === 1): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cloud-upload-alt me-2"></i>Step 1: Upload CSV File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="upload_analyze">
                    
                    <div class="mb-4">
                        <label for="csv_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">Maximum file size: 10MB. Only CSV files are accepted.</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Supported Fields</h6>
                            <ul class="list-unstyled">
                                <li><strong>Required:</strong> name</li>
                                <li><strong>Optional:</strong> brewery_type, description, phone, email</li>
                                <li><strong>Address:</strong> address_1, address_2, city, state, postal_code, country</li>
                                <li><strong>Location:</strong> latitude, longitude</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb me-2"></i>Tips</h6>
                            <ul class="list-unstyled">
                                <li>• Include headers in the first row</li>
                                <li>• Use clear, descriptive column names</li>
                                <li>• Ensure data consistency</li>
                                <li>• Download our template for best results</li>
                            </ul>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload & Analyze
                    </button>
                </form>
            </div>
        </div>
    
    <!-- Step 2: Field Mapping -->
    <?php elseif ($step === 2 && $analysis_result): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-map me-2"></i>Step 2: Review Analysis & Map Fields</h5>
            </div>
            <div class="card-body">
                <!-- Enhanced Analysis Summary -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo number_format($analysis_result['total_rows']); ?></h4>
                            <small>Total Rows</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo $analysis_result['columns']; ?></h4>
                            <small>Columns</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo count($analysis_result['analysis']['potential_duplicates']); ?></h4>
                            <small>Duplicates</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo number_format($analysis_result['analysis']['data_quality_score'], 1); ?>%</h4>
                            <small>Data Quality</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo $analysis_result['validation']['valid_rows']; ?></h4>
                            <small>Valid Rows</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="mb-1"><?php echo $analysis_result['enhanced_count']; ?></h4>
                            <small>Enhanced</small>
                        </div>
                    </div>
                </div>

                <!-- Validation Results -->
                <?php if (!empty($analysis_result['validation']['errors']) || !empty($analysis_result['validation']['warnings'])): ?>
                    <div class="alert alert-warning mb-4">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Data Validation Results</h6>

                        <?php if (!empty($analysis_result['validation']['errors'])): ?>
                            <div class="mb-2">
                                <strong>Errors (<?php echo count($analysis_result['validation']['errors']); ?>):</strong>
                                <ul class="mb-0">
                                    <?php foreach (array_slice($analysis_result['validation']['errors'], 0, 5) as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                    <?php if (count($analysis_result['validation']['errors']) > 5): ?>
                                        <li><em>... and <?php echo count($analysis_result['validation']['errors']) - 5; ?> more errors</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($analysis_result['validation']['warnings'])): ?>
                            <div>
                                <strong>Warnings (<?php echo count($analysis_result['validation']['warnings']); ?>):</strong>
                                <ul class="mb-0">
                                    <?php foreach (array_slice($analysis_result['validation']['warnings'], 0, 3) as $warning): ?>
                                        <li><?php echo htmlspecialchars($warning); ?></li>
                                    <?php endforeach; ?>
                                    <?php if (count($analysis_result['validation']['warnings']) > 3): ?>
                                        <li><em>... and <?php echo count($analysis_result['validation']['warnings']) - 3; ?> more warnings</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Import Preview Recommendations -->
                <?php if (!empty($analysis_result['preview']['summary']['recommendations'])): ?>
                    <div class="alert alert-info mb-4">
                        <h6><i class="fas fa-lightbulb me-2"></i>Import Recommendations</h6>
                        <ul class="mb-0">
                            <?php foreach ($analysis_result['preview']['summary']['recommendations'] as $recommendation): ?>
                                <li><?php echo htmlspecialchars($recommendation); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <div class="mt-2">
                            <small><strong>Estimated Import Time:</strong> <?php echo $analysis_result['preview']['summary']['estimated_import_time']; ?></small>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Data Enhancement Opportunities -->
                <?php if (!empty($analysis_result['preview']['data_enhancement_opportunities'])): ?>
                    <div class="alert alert-success mb-4">
                        <h6><i class="fas fa-magic me-2"></i>Data Enhancement Opportunities</h6>
                        <ul class="mb-0">
                            <?php foreach ($analysis_result['preview']['data_enhancement_opportunities'] as $opportunity): ?>
                                <li><?php echo htmlspecialchars($opportunity['description']); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST">
                    <input type="hidden" name="action" value="execute_import">
                    
                    <!-- Field Mapping -->
                    <h6>Field Mapping</h6>
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>CSV Column</th>
                                    <th>Sample Data</th>
                                    <th>Fill Rate</th>
                                    <th>Map to Database Field</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($analysis_result['headers'] as $header): ?>
                                    <?php $field_info = $analysis_result['analysis']['field_analysis'][$header]; ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($header); ?></strong></td>
                                        <td>
                                            <?php foreach (array_slice($field_info['sample_values'], 0, 2) as $sample): ?>
                                                <span class="badge bg-secondary me-1"><?php echo htmlspecialchars(substr($sample, 0, 30)); ?></span>
                                            <?php endforeach; ?>
                                        </td>
                                        <td><?php echo number_format($field_info['fill_rate'], 1); ?>%</td>
                                        <td>
                                            <select name="field_mapping[<?php echo htmlspecialchars($header); ?>]" class="form-select">
                                                <option value="ignore">Ignore this field</option>
                                                <option value="name" <?php echo $field_info['suggested_mapping'] === 'name' ? 'selected' : ''; ?>>Name *</option>
                                                <option value="brewery_type" <?php echo $field_info['suggested_mapping'] === 'brewery_type' ? 'selected' : ''; ?>>Type</option>
                                                <option value="description" <?php echo $field_info['suggested_mapping'] === 'description' ? 'selected' : ''; ?>>Description</option>
                                                <option value="phone" <?php echo $field_info['suggested_mapping'] === 'phone' ? 'selected' : ''; ?>>Phone</option>
                                                <option value="email" <?php echo $field_info['suggested_mapping'] === 'email' ? 'selected' : ''; ?>>Email</option>
                                                <option value="website_url" <?php echo $field_info['suggested_mapping'] === 'website_url' ? 'selected' : ''; ?>>Website</option>
                                                <option value="address_1" <?php echo $field_info['suggested_mapping'] === 'address_1' ? 'selected' : ''; ?>>Address 1</option>
                                                <option value="address_2" <?php echo $field_info['suggested_mapping'] === 'address_2' ? 'selected' : ''; ?>>Address 2</option>
                                                <option value="city" <?php echo $field_info['suggested_mapping'] === 'city' ? 'selected' : ''; ?>>City</option>
                                                <option value="state" <?php echo $field_info['suggested_mapping'] === 'state' ? 'selected' : ''; ?>>State</option>
                                                <option value="postal_code" <?php echo $field_info['suggested_mapping'] === 'postal_code' ? 'selected' : ''; ?>>Postal Code</option>
                                                <option value="country" <?php echo $field_info['suggested_mapping'] === 'country' ? 'selected' : ''; ?>>Country</option>
                                                <option value="latitude" <?php echo $field_info['suggested_mapping'] === 'latitude' ? 'selected' : ''; ?>>Latitude</option>
                                                <option value="longitude" <?php echo $field_info['suggested_mapping'] === 'longitude' ? 'selected' : ''; ?>>Longitude</option>
                                            </select>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Enhanced Import Options -->
                    <h6>Import Options</h6>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <h6 class="small text-muted">Duplicate Handling</h6>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check_duplicates" name="check_duplicates" checked>
                                <label class="form-check-label" for="check_duplicates">Check for duplicates</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="skip_duplicates" name="skip_duplicates" checked>
                                <label class="form-check-label" for="skip_duplicates">Skip duplicate entries</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="update_duplicates" name="update_duplicates">
                                <label class="form-check-label" for="update_duplicates">Update existing places</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small text-muted">Data Processing</h6>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="validate_data" name="validate_data" checked>
                                <label class="form-check-label" for="validate_data">Validate data formats</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="enhance_data" name="enhance_data" checked>
                                <label class="form-check-label" for="enhance_data">Enhance data quality</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="geocode_addresses" name="geocode_addresses">
                                <label class="form-check-label" for="geocode_addresses">Geocode missing coordinates</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small text-muted">Performance</h6>
                            <div class="mb-2">
                                <label for="batch_size" class="form-label small">Batch Size</label>
                                <select class="form-select form-select-sm" id="batch_size" name="batch_size">
                                    <option value="50">50 records (slower, safer)</option>
                                    <option value="100" selected>100 records (recommended)</option>
                                    <option value="200">200 records (faster)</option>
                                    <option value="500">500 records (fastest)</option>
                                </select>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="detailed_logging" name="detailed_logging">
                                <label class="form-check-label" for="detailed_logging">Detailed logging</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="location.reload()">
                            <i class="fas fa-arrow-left me-2"></i>Back
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    
    <!-- Step 3: Import Results -->
    <?php elseif ($step === 3 && $import_result): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-check-circle me-2"></i>Step 3: Import Results</h5>
            </div>
            <div class="card-body">
                <?php if ($import_result['success']): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>Import completed successfully!
                        <?php if (isset($import_result['report']['executive_summary'])): ?>
                            <div class="mt-2">
                                <strong>Success Rate:</strong> <?php echo $import_result['report']['executive_summary']['success_rate_percentage']; ?>% |
                                <strong>Processing Time:</strong> <?php echo number_format($import_result['report']['executive_summary']['processing_time'], 2); ?>s |
                                <strong>Data Quality:</strong> <?php echo $import_result['report']['executive_summary']['data_quality_score']; ?>%
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Enhanced Results Display -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1 text-success"><?php echo $import_result['results']['imported']; ?></h4>
                                <small>Imported</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1 text-info"><?php echo $import_result['results']['updated']; ?></h4>
                                <small>Updated</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1 text-warning"><?php echo $import_result['results']['skipped']; ?></h4>
                                <small>Skipped</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1 text-secondary"><?php echo $import_result['results']['duplicates_found']; ?></h4>
                                <small>Duplicates</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1 text-danger"><?php echo $import_result['results']['validation_errors']; ?></h4>
                                <small>Validation Errors</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="mb-1"><?php echo $import_result['results']['total_rows']; ?></h4>
                                <small>Total Rows</small>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Report -->
                    <?php if (isset($import_result['report'])): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">Processing Time</small>
                                                <div class="fw-bold"><?php echo number_format($import_result['report']['performance_metrics']['processing_time_seconds'], 2); ?>s</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Records/Second</small>
                                                <div class="fw-bold"><?php echo $import_result['report']['performance_metrics']['records_per_second']; ?></div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Peak Memory</small>
                                                <div class="fw-bold"><?php echo $import_result['report']['performance_metrics']['memory_usage']['peak_memory_mb']; ?>MB</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Batches Processed</small>
                                                <div class="fw-bold"><?php echo $import_result['report']['performance_metrics']['batch_processing']['completed_batches']; ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Error Analysis</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($import_result['report']['detailed_statistics']['error_analysis']['common_error_types'])): ?>
                                            <?php foreach ($import_result['report']['detailed_statistics']['error_analysis']['common_error_types'] as $error_type => $count): ?>
                                                <div class="d-flex justify-content-between">
                                                    <span class="small"><?php echo ucwords(str_replace('_', ' ', $error_type)); ?></span>
                                                    <span class="badge bg-secondary"><?php echo $count; ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="text-center text-muted">
                                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                                <div>No errors detected</div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recommendations -->
                        <?php if (!empty($import_result['report']['recommendations'])): ?>
                            <div class="alert alert-info mt-4">
                                <h6><i class="fas fa-lightbulb me-2"></i>Post-Import Recommendations</h6>
                                <ul class="mb-0">
                                    <?php foreach ($import_result['report']['recommendations'] as $recommendation): ?>
                                        <li><?php echo htmlspecialchars($recommendation); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php if (!empty($import_result['results']['errors'])): ?>
                        <div class="mt-4">
                            <h6>Errors Encountered:</h6>
                            <div class="alert alert-warning">
                                <ul class="mb-0">
                                    <?php foreach (array_slice($import_result['results']['errors'], 0, 10) as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                    <?php if (count($import_result['results']['errors']) > 10): ?>
                                        <li><em>... and <?php echo count($import_result['results']['errors']) - 10; ?> more errors</em></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Import failed!
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="import-places.php" class="btn btn-primary me-2">
                        <i class="fas fa-upload me-2"></i>Import Another File
                    </a>
                    <a href="breweries.php" class="btn btn-outline-secondary">
                        <i class="fas fa-map-marker-alt me-2"></i>View Places
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
