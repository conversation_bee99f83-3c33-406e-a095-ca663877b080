<?php
/**
 * Download Places CSV Template
 * Generate a CSV template with proper headers and sample data
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Define CSV headers and sample data
$headers = [
    'name',
    'brewery_type',
    'description',
    'phone',
    'email',
    'website_url',
    'address_1',
    'address_2',
    'city',
    'state',
    'postal_code',
    'country',
    'latitude',
    'longitude'
];

$sample_data = [
    [
        'name' => 'Sample Brewery',
        'brewery_type' => 'brewery',
        'description' => 'A great local brewery with craft beers',
        'phone' => '(*************',
        'email' => '<EMAIL>',
        'website_url' => 'https://www.samplebrewery.com',
        'address_1' => '123 Main Street',
        'address_2' => 'Suite 100',
        'city' => 'Anytown',
        'state' => 'CA',
        'postal_code' => '12345',
        'country' => 'United States',
        'latitude' => '37.7749',
        'longitude' => '-122.4194'
    ],
    [
        'name' => 'Example Restaurant',
        'brewery_type' => 'restaurant',
        'description' => 'Family restaurant with great food and beer selection',
        'phone' => '************',
        'email' => '<EMAIL>',
        'website_url' => 'www.examplerestaurant.com',
        'address_1' => '456 Oak Avenue',
        'address_2' => '',
        'city' => 'Somewhere',
        'state' => 'NY',
        'postal_code' => '67890',
        'country' => 'United States',
        'latitude' => '40.7128',
        'longitude' => '-74.0060'
    ],
    [
        'name' => 'Demo Pub',
        'brewery_type' => 'pub',
        'description' => 'Traditional pub with live music and local beers',
        'phone' => '******-456-7890',
        'email' => '<EMAIL>',
        'website_url' => 'http://demopub.com',
        'address_1' => '789 Elm Street',
        'address_2' => 'Unit 5',
        'city' => 'Elsewhere',
        'state' => 'TX',
        'postal_code' => '54321',
        'country' => 'United States',
        'latitude' => '29.7604',
        'longitude' => '-95.3698'
    ]
];

// Get template type
$template_type = $_GET['type'] ?? 'full';

// Set appropriate headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="places_import_template_' . date('Y-m-d') . '.csv"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Excel compatibility)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write headers
fputcsv($output, $headers);

// Write sample data based on template type
switch ($template_type) {
    case 'empty':
        // Just headers, no sample data
        break;
        
    case 'minimal':
        // Only required fields sample
        $minimal_sample = [
            [
                'name' => 'Your Place Name Here',
                'brewery_type' => 'brewery',
                'description' => '',
                'phone' => '',
                'email' => '',
                'website_url' => '',
                'address_1' => 'Your Address Here',
                'address_2' => '',
                'city' => 'Your City',
                'state' => 'Your State',
                'postal_code' => '',
                'country' => 'United States',
                'latitude' => '',
                'longitude' => ''
            ]
        ];
        foreach ($minimal_sample as $row) {
            fputcsv($output, $row);
        }
        break;
        
    case 'full':
    default:
        // Full sample data
        foreach ($sample_data as $row) {
            fputcsv($output, $row);
        }
        break;
}

// Add comments/instructions as additional rows (will be ignored during import)
if ($template_type !== 'empty') {
    fputcsv($output, []);
    fputcsv($output, ['# INSTRUCTIONS - Delete these rows before importing']);
    fputcsv($output, ['# Required fields: name']);
    fputcsv($output, ['# Supported place types: brewery, restaurant, pub, bar, taproom, distillery, winery']);
    fputcsv($output, ['# Phone formats: (*************, ************, +1-************']);
    fputcsv($output, ['# Website URLs: Include http:// or https://']);
    fputcsv($output, ['# Coordinates: Use decimal degrees (e.g., 37.7749, -122.4194)']);
    fputcsv($output, ['# Country: Use full country name (default: United States)']);
    fputcsv($output, []);
    fputcsv($output, ['# Field Descriptions:']);
    fputcsv($output, ['# name: Business/place name (REQUIRED)']);
    fputcsv($output, ['# brewery_type: Type of establishment']);
    fputcsv($output, ['# description: Brief description of the place']);
    fputcsv($output, ['# phone: Contact phone number']);
    fputcsv($output, ['# email: Contact email address']);
    fputcsv($output, ['# website_url: Website URL']);
    fputcsv($output, ['# address_1: Street address']);
    fputcsv($output, ['# address_2: Suite, unit, or apartment number']);
    fputcsv($output, ['# city: City name']);
    fputcsv($output, ['# state: State or province']);
    fputcsv($output, ['# postal_code: ZIP or postal code']);
    fputcsv($output, ['# country: Country name']);
    fputcsv($output, ['# latitude: Latitude coordinate']);
    fputcsv($output, ['# longitude: Longitude coordinate']);
}

fclose($output);
exit;
?>
