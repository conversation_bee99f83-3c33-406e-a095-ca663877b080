<?php
/**
 * CSV Menu Import/Export Management
 * Advanced CSV operations for beer and food menus
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'CSV Menu Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get places for selection
try {
    $stmt = $pdo->query("SELECT id, name, city, state, brewery_type as place_type FROM breweries ORDER BY name");
    $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $places = [];
}

// Get beer styles and food categories for reference
try {
    $stmt = $pdo->query("SELECT id, name, category FROM beer_styles WHERE is_active = 1 ORDER BY category, name");
    $beer_styles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->query("SELECT id, name FROM food_categories WHERE is_active = 1 ORDER BY sort_order, name");
    $food_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $beer_styles = [];
    $food_categories = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-file-csv me-2"></i>CSV Menu Management
                    </h1>
                    <p class="text-muted mb-0">Import and export beer and food menus via CSV files</p>
                </div>
                <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- CSV Operations Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="csvTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab">
                        <i class="fas fa-upload me-2"></i>Import CSV
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab">
                        <i class="fas fa-download me-2"></i>Export CSV
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                        <i class="fas fa-file-alt me-2"></i>CSV Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                        <i class="fas fa-history me-2"></i>Import History
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="csvTabContent">
                <!-- Import Tab -->
                <div class="tab-pane fade show active" id="import" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-upload me-2"></i>Import Menu Data
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form id="csvImportForm" enctype="multipart/form-data">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Select Place *</label>
                                                <select name="place_id" class="form-select" required>
                                                    <option value="">Choose a place...</option>
                                                    <?php foreach ($places as $place): ?>
                                                        <option value="<?php echo $place['id']; ?>">
                                                            <?php echo htmlspecialchars($place['name']); ?>
                                                            <?php if ($place['place_type']): ?>
                                                                (<?php echo ucfirst(str_replace('_', ' ', $place['place_type'])); ?>)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label class="form-label">Menu Type *</label>
                                                <select name="menu_type" class="form-select" required>
                                                    <option value="">Select menu type...</option>
                                                    <option value="beer">Beer Menu</option>
                                                    <option value="food">Food Menu</option>
                                                </select>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label class="form-label">CSV File *</label>
                                                <input type="file" name="csv_file" class="form-control" accept=".csv" required>
                                                <small class="text-muted">Maximum file size: 5MB. Only CSV files are allowed.</small>
                                            </div>
                                            
                                            <div class="col-12">
                                                <div class="form-check">
                                                    <input type="checkbox" name="replace_existing" class="form-check-input" id="replaceExisting">
                                                    <label class="form-check-label" for="replaceExisting">
                                                        Replace existing menu items (clear current menu first)
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <div class="form-check">
                                                    <input type="checkbox" name="validate_only" class="form-check-input" id="validateOnly">
                                                    <label class="form-check-label" for="validateOnly">
                                                        Validate only (don't import, just check for errors)
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-4">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-upload me-2"></i>Import CSV
                                            </button>
                                            <button type="button" class="btn btn-outline-info ms-2" onclick="showImportPreview()">
                                                <i class="fas fa-eye me-2"></i>Preview Data
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Import Guidelines
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6>Beer Menu CSV Format:</h6>
                                    <ul class="small">
                                        <li>name (required)</li>
                                        <li>beer_style_name</li>
                                        <li>description</li>
                                        <li>abv (decimal)</li>
                                        <li>ibu (integer)</li>
                                        <li>price (decimal)</li>
                                        <li>availability (year_round, seasonal, limited, one_off)</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">Food Menu CSV Format:</h6>
                                    <ul class="small">
                                        <li>name (required)</li>
                                        <li>food_category_name</li>
                                        <li>description</li>
                                        <li>price (decimal)</li>
                                        <li>ingredients</li>
                                        <li>allergens</li>
                                        <li>is_vegetarian (1/0)</li>
                                        <li>is_vegan (1/0)</li>
                                        <li>is_gluten_free (1/0)</li>
                                    </ul>
                                    
                                    <div class="alert alert-info mt-3">
                                        <small>
                                            <i class="fas fa-lightbulb me-1"></i>
                                            Download templates from the Templates tab for proper formatting.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Tab -->
                <div class="tab-pane fade" id="export" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-download me-2"></i>Export Menu Data
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form id="csvExportForm">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label class="form-label">Select Place</label>
                                                <select name="export_place_id" class="form-select">
                                                    <option value="">All places</option>
                                                    <?php foreach ($places as $place): ?>
                                                        <option value="<?php echo $place['id']; ?>">
                                                            <?php echo htmlspecialchars($place['name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label class="form-label">Menu Type *</label>
                                                <select name="export_menu_type" class="form-select" required>
                                                    <option value="">Select menu type...</option>
                                                    <option value="beer">Beer Menu</option>
                                                    <option value="food">Food Menu</option>
                                                    <option value="both">Both Menus (separate files)</option>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label class="form-label">Include Inactive Items</label>
                                                <select name="include_inactive" class="form-select">
                                                    <option value="0">Active items only</option>
                                                    <option value="1">Include inactive items</option>
                                                </select>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label class="form-label">Date Format</label>
                                                <select name="date_format" class="form-select">
                                                    <option value="Y-m-d">YYYY-MM-DD</option>
                                                    <option value="m/d/Y">MM/DD/YYYY</option>
                                                    <option value="d/m/Y">DD/MM/YYYY</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-4">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-download me-2"></i>Export CSV
                                            </button>
                                            <button type="button" class="btn btn-outline-info ms-2" onclick="previewExport()">
                                                <i class="fas fa-eye me-2"></i>Preview Export
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>Export Statistics
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="exportStats">
                                        <p class="text-muted">Select options to see export statistics</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Templates Tab -->
                <div class="tab-pane fade" id="templates" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-beer me-2"></i>Beer Menu Template
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>Download a properly formatted CSV template for beer menu imports.</p>
                                    
                                    <div class="table-responsive mb-3">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Column</th>
                                                    <th>Required</th>
                                                    <th>Example</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>name</td><td>Yes</td><td>House IPA</td></tr>
                                                <tr><td>beer_style_name</td><td>No</td><td>American IPA</td></tr>
                                                <tr><td>description</td><td>No</td><td>Hoppy and citrusy</td></tr>
                                                <tr><td>abv</td><td>No</td><td>6.5</td></tr>
                                                <tr><td>ibu</td><td>No</td><td>65</td></tr>
                                                <tr><td>price</td><td>No</td><td>7.99</td></tr>
                                                <tr><td>availability</td><td>No</td><td>year_round</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <a href="../api/csv-templates.php?type=beer" class="btn btn-success">
                                        <i class="fas fa-download me-2"></i>Download Beer Template
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-utensils me-2"></i>Food Menu Template
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>Download a properly formatted CSV template for food menu imports.</p>
                                    
                                    <div class="table-responsive mb-3">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Column</th>
                                                    <th>Required</th>
                                                    <th>Example</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>name</td><td>Yes</td><td>Margherita Pizza</td></tr>
                                                <tr><td>food_category_name</td><td>No</td><td>Pizza</td></tr>
                                                <tr><td>description</td><td>No</td><td>Fresh mozzarella and basil</td></tr>
                                                <tr><td>price</td><td>No</td><td>14.99</td></tr>
                                                <tr><td>ingredients</td><td>No</td><td>Tomato, mozzarella, basil</td></tr>
                                                <tr><td>allergens</td><td>No</td><td>Gluten, Dairy</td></tr>
                                                <tr><td>is_vegetarian</td><td>No</td><td>1</td></tr>
                                                <tr><td>is_vegan</td><td>No</td><td>0</td></tr>
                                                <tr><td>is_gluten_free</td><td>No</td><td>0</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <a href="../api/csv-templates.php?type=food" class="btn btn-success">
                                        <i class="fas fa-download me-2"></i>Download Food Template
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tags me-2"></i>Available Categories
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Beer Styles:</h6>
                                            <div class="small">
                                                <?php foreach ($beer_styles as $style): ?>
                                                    <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars($style['name']); ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Food Categories:</h6>
                                            <div class="small">
                                                <?php foreach ($food_categories as $category): ?>
                                                    <span class="badge bg-warning text-dark me-1 mb-1"><?php echo htmlspecialchars($category['name']); ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Tab -->
                <div class="tab-pane fade" id="history" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>Import History
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="importHistory">
                                <p class="text-muted">Loading import history...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Results Modal -->
<div class="modal fade" id="importResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="importResultsContent">
                <!-- Results will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/csv-menu-management.js"></script>

<?php require_once '../includes/footer.php'; ?>
