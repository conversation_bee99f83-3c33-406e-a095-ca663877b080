<?php
/**
 * User Management System
 * Complete user administration with role-based access control
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'User Management';
$additionalCSS = ['../assets/css/admin.css'];

// Pagination and filtering parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 25;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$role_filter = isset($_GET['role']) ? trim($_GET['role']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$order = isset($_GET['order']) ? $_GET['order'] : 'DESC';

$offset = ($page - 1) * $limit;

// We'll validate sort columns after checking available columns

$order = strtoupper($order) === 'ASC' ? 'ASC' : 'DESC';

// User roles configuration
$user_roles = [
    'admin' => 'Administrator',
    'site_moderator' => 'Site Moderator',
    'business_owner' => 'Business Owner',
    'business_manager' => 'Business Manager',
    'user' => 'Standard User'
];

$user_statuses = [
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',
    'pending' => 'Pending Verification'
];

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Check what columns exist in users table
    $stmt = $conn->query("DESCRIBE users");
    $available_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Validate sort column based on available columns
    $valid_sorts = array_intersect(['id', 'username', 'email', 'role', 'status', 'created_at', 'last_login'], $available_columns);
    if (!in_array($sort, $valid_sorts)) {
        $sort = in_array('created_at', $available_columns) ? 'created_at' : 'id';
    }

    // Build WHERE clause for filtering based on available columns
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $search_fields = [];
        if (in_array('username', $available_columns)) $search_fields[] = "username LIKE ?";
        if (in_array('email', $available_columns)) $search_fields[] = "email LIKE ?";
        if (in_array('first_name', $available_columns)) $search_fields[] = "first_name LIKE ?";
        if (in_array('last_name', $available_columns)) $search_fields[] = "last_name LIKE ?";

        if (!empty($search_fields)) {
            $where_conditions[] = "(" . implode(" OR ", $search_fields) . ")";
            $search_param = "%$search%";
            $params = array_merge($params, array_fill(0, count($search_fields), $search_param));
        }
    }
    
    if (!empty($role_filter) && in_array('role', $available_columns)) {
        $where_conditions[] = "role = ?";
        $params[] = $role_filter;
    }

    if (!empty($status_filter) && in_array('status', $available_columns)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count for pagination
    $count_sql = "SELECT COUNT(*) FROM users $where_clause";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->execute($params);
    $total_users = $count_stmt->fetchColumn();
    
    $total_pages = ceil($total_users / $limit);
    
    // Build SELECT based on available columns
    $select_fields = ['id'];
    if (in_array('username', $available_columns)) $select_fields[] = 'username';
    if (in_array('email', $available_columns)) $select_fields[] = 'email';
    if (in_array('first_name', $available_columns)) $select_fields[] = 'first_name';
    if (in_array('last_name', $available_columns)) $select_fields[] = 'last_name';
    if (in_array('role', $available_columns)) $select_fields[] = 'role';
    if (in_array('status', $available_columns)) $select_fields[] = 'status';
    if (in_array('created_at', $available_columns)) $select_fields[] = 'created_at';
    if (in_array('updated_at', $available_columns)) $select_fields[] = 'updated_at';
    if (in_array('last_login', $available_columns)) $select_fields[] = 'last_login';
    if (in_array('email_verified', $available_columns)) $select_fields[] = 'email_verified';
    if (in_array('phone', $available_columns)) $select_fields[] = 'phone';
    if (in_array('city', $available_columns)) $select_fields[] = 'city';
    if (in_array('state', $available_columns)) $select_fields[] = 'state';
    if (in_array('country', $available_columns)) $select_fields[] = 'country';

    // Get users with pagination
    $sql = "
        SELECT " . implode(', ', $select_fields) . "
        FROM users
        $where_clause
        ORDER BY $sort $order
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get role statistics (only if role column exists)
    $role_stats = [];
    if (in_array('role', $available_columns)) {
        $role_stats_sql = "SELECT role, COUNT(*) as count FROM users GROUP BY role";
        $role_stats_stmt = $conn->query($role_stats_sql);
        $role_stats = $role_stats_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }

    // Get status statistics (only if status column exists)
    $status_stats = [];
    if (in_array('status', $available_columns)) {
        $status_stats_sql = "SELECT status, COUNT(*) as count FROM users GROUP BY status";
        $status_stats_stmt = $conn->query($status_stats_sql);
        $status_stats = $status_stats_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }
    
} catch (Exception $e) {
    $error = "Error loading users: " . $e->getMessage();
}

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-users me-2"></i>User Management</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-1"></i>Add User
                </button>
            </div>
        </div>

        <div class="dashboard-content">
            <!-- Page Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-users text-primary me-2"></i>
                    User Management
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i>Add User
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportUsers()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4 stats-row">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo number_format($total_users); ?></h5>
                            <p class="card-text small">Total Users</p>
                        </div>
                    </div>
                </div>
                <?php foreach ($user_roles as $role_key => $role_name): ?>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo number_format($role_stats[$role_key] ?? 0); ?></h5>
                                <p class="card-text small"><?php echo $role_name; ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Filters and Search -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-filter me-2"></i>Filters & Search</h5>
                </div>
                <div class="card-body filter-form">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search Users</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Username, email, name...">
                        </div>
                        <div class="col-md-2">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">All Roles</option>
                                <?php foreach ($user_roles as $role_key => $role_name): ?>
                                    <option value="<?php echo $role_key; ?>" <?php echo $role_filter === $role_key ? 'selected' : ''; ?>>
                                        <?php echo $role_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <?php foreach ($user_statuses as $status_key => $status_name): ?>
                                    <option value="<?php echo $status_key; ?>" <?php echo $status_filter === $status_key ? 'selected' : ''; ?>>
                                        <?php echo $status_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="limit" class="form-label">Per Page</label>
                            <select class="form-select" id="limit" name="limit">
                                <option value="10" <?php echo $limit == 10 ? 'selected' : ''; ?>>10</option>
                                <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>25</option>
                                <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                                <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                                <option value="250" <?php echo $limit == 250 ? 'selected' : ''; ?>>250</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="sort" class="form-label">Sort By</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Date Created</option>
                                <option value="username" <?php echo $sort === 'username' ? 'selected' : ''; ?>>Username</option>
                                <option value="email" <?php echo $sort === 'email' ? 'selected' : ''; ?>>Email</option>
                                <option value="role" <?php echo $sort === 'role' ? 'selected' : ''; ?>>Role</option>
                                <option value="status" <?php echo $sort === 'status' ? 'selected' : ''; ?>>Status</option>
                                <option value="last_login" <?php echo $sort === 'last_login' ? 'selected' : ''; ?>>Last Login</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label for="order" class="form-label">Order</label>
                            <select class="form-select" id="order" name="order">
                                <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>↓ Desc</option>
                                <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>↑ Asc</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Apply Filters
                            </button>
                            <a href="user-management.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header table-header-actions d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i>Users (<?php echo number_format($total_users); ?> total)</h5>
                    <div class="text-muted small">
                        Showing <?php echo number_format($offset + 1); ?>-<?php echo number_format(min($offset + $limit, $total_users)); ?>
                        of <?php echo number_format($total_users); ?> users
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0 user-management-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="8" class="no-users-state">
                                            <i class="fas fa-users fa-3x mb-3"></i>
                                            <p>No users found matching your criteria.</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td class="id-column" title="<?php echo $user['id']; ?>">
                                                <?php echo substr($user['id'], 0, 8) . '...'; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                    <div>
                                                        <div class="user-display-name">
                                                            <?php
                                                            $display_name = $user['username'] ?? $user['email'] ?? 'User #' . $user['id'];
                                                            echo htmlspecialchars($display_name);
                                                            ?>
                                                        </div>
                                                        <?php if (!empty($user['first_name']) || !empty($user['last_name'])): ?>
                                                            <div class="user-real-name">
                                                                <?php echo htmlspecialchars(trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''))); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="email-column">
                                                <?php echo htmlspecialchars($user['email'] ?? 'No email'); ?>
                                                <?php if (isset($user['email_verified']) && $user['email_verified']): ?>
                                                    <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                                                <?php elseif (isset($user['email_verified'])): ?>
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Unverified"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php
                                                    $role = $user['role'] ?? 'user';
                                                    echo htmlspecialchars($user_roles[$role] ?? $role);
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $status = $user['status'] ?? 'active';
                                                $status_colors = [
                                                    'active' => 'success',
                                                    'inactive' => 'secondary',
                                                    'suspended' => 'danger',
                                                    'pending' => 'warning'
                                                ];
                                                $color = $status_colors[$status] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $color; ?>">
                                                    <?php echo $user_statuses[$status] ?? $status; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (isset($user['created_at'])): ?>
                                                    <div class="date-display"><?php echo date('M j, Y', strtotime($user['created_at'])); ?></div>
                                                <?php else: ?>
                                                    <div class="date-display text-muted">Unknown</div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($user['last_login']) && $user['last_login']): ?>
                                                    <div class="date-display"><?php echo date('M j, Y', strtotime($user['last_login'])); ?></div>
                                                <?php else: ?>
                                                    <div class="date-display text-muted">Never</div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editUser('<?php echo htmlspecialchars($user['id']); ?>')" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-info"
                                                            onclick="viewUser('<?php echo htmlspecialchars($user['id']); ?>')" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php
                                                    $user_status = $user['status'] ?? 'active';
                                                    if ($user_status === 'active'):
                                                    ?>
                                                        <button type="button" class="btn btn-outline-warning"
                                                                onclick="suspendUser('<?php echo htmlspecialchars($user['id']); ?>')" title="Suspend">
                                                            <i class="fas fa-pause"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-outline-success"
                                                                onclick="activateUser('<?php echo htmlspecialchars($user['id']); ?>')" title="Activate">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="deleteUser('<?php echo htmlspecialchars($user['id']); ?>')" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="User pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php
                        $query_params = $_GET;
                        unset($query_params['page']);
                        $base_url = 'user-management.php?' . http_build_query($query_params);
                        ?>
                        
                        <!-- Previous -->
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page - 1; ?>">Previous</a>
                        </li>
                        
                        <!-- Page numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        if ($start_page > 1): ?>
                            <li class="page-item"><a class="page-link" href="<?php echo $base_url; ?>&page=1">1</a></li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            <?php endif; ?>
                            <li class="page-item"><a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $total_pages; ?>"><?php echo $total_pages; ?></a></li>
                        <?php endif; ?>
                        
                        <!-- Next -->
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo $base_url; ?>&page=<?php echo $page + 1; ?>">Next</a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="Optional - will use email if empty">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <?php foreach ($user_roles as $role_key => $role_name): ?>
                                        <option value="<?php echo $role_key; ?>"><?php echo $role_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-select" id="status" name="status" required>
                                    <?php foreach ($user_statuses as $status_key => $status_name): ?>
                                        <option value="<?php echo $status_key; ?>" <?php echo $status_key === 'active' ? 'selected' : ''; ?>>
                                            <?php echo $status_name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                <input type="hidden" id="edit_user_id" name="user_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="edit_username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="edit_first_name" name="first_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="edit_last_name" name="last_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_role" class="form-label">Role *</label>
                                <select class="form-select" id="edit_role" name="role" required>
                                    <?php foreach ($user_roles as $role_key => $role_name): ?>
                                        <option value="<?php echo $role_key; ?>"><?php echo $role_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status *</label>
                                <select class="form-select" id="edit_status" name="status" required>
                                    <?php foreach ($user_statuses as $status_key => $status_name): ?>
                                        <option value="<?php echo $status_key; ?>"><?php echo $status_name; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_email_verified" name="email_verified">
                                <label class="form-check-label" for="edit_email_verified">
                                    Email Verified
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View User Modal -->
<div class="modal" id="viewUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-eye me-2"></i>User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Debug: Test if script is loaded
console.log('User management script loaded');

// User Management JavaScript Functions
function editUser(userId) {
    console.log('Edit user clicked:', userId);

    // Fetch user data and populate edit modal
    fetch(`user-api.php?action=get&id=${userId}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                const user = data.user;
                document.getElementById('edit_user_id').value = user.id;
                document.getElementById('edit_username').value = user.username || user.email || '';
                document.getElementById('edit_email').value = user.email || '';
                document.getElementById('edit_first_name').value = user.first_name || '';
                document.getElementById('edit_last_name').value = user.last_name || '';
                document.getElementById('edit_role').value = user.role || 'user';
                document.getElementById('edit_status').value = user.status || 'active';
                document.getElementById('edit_email_verified').checked = user.email_verified == 1;

                // Try different ways to show modal
                try {
                    if (typeof bootstrap !== 'undefined') {
                        new bootstrap.Modal(document.getElementById('editUserModal')).show();
                    } else if (typeof $ !== 'undefined') {
                        $('#editUserModal').modal('show');
                    } else {
                        alert('Bootstrap not loaded properly');
                    }
                } catch (e) {
                    console.error('Modal error:', e);
                    alert('Error showing modal: ' + e.message);
                }
            } else {
                alert('Error loading user data: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading user data: ' + error.message);
        });
}

function viewUser(userId) {
    console.log('View user clicked:', userId);

    // Fetch user data and show in view modal
    fetch(`user-api.php?action=view&id=${userId}`)
        .then(response => response.json())
        .then(data => {
            console.log('View response:', data);
            if (data.success) {
                document.getElementById('userDetailsContent').innerHTML = data.html;

                // Try different ways to show modal
                try {
                    if (typeof bootstrap !== 'undefined') {
                        new bootstrap.Modal(document.getElementById('viewUserModal')).show();
                    } else if (typeof $ !== 'undefined') {
                        $('#viewUserModal').modal('show');
                    } else {
                        alert('Bootstrap not loaded properly');
                    }
                } catch (e) {
                    console.error('Modal error:', e);
                    alert('Error showing modal: ' + e.message);
                }
            } else {
                alert('Error loading user details: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading user details: ' + error.message);
        });
}

function suspendUser(userId) {
    if (confirm('Are you sure you want to suspend this user?')) {
        updateUserStatus(userId, 'suspended');
    }
}

function activateUser(userId) {
    if (confirm('Are you sure you want to activate this user?')) {
        updateUserStatus(userId, 'active');
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch('user-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete',
                user_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting user: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting user');
        });
    }
}

function updateUserStatus(userId, status) {
    fetch('user-api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update_status',
            user_id: userId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating user status: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating user status');
    });
}

function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.location.href = 'user-management.php?' + params.toString();
}

// Form submissions - Wait for DOM to be ready and ensure Bootstrap is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up form listeners');

    // Wait a bit more to ensure Bootstrap modals are fully initialized
    setTimeout(function() {
        setupFormListeners();
    }, 100);
});

function setupFormListeners() {
    console.log('Setting up form listeners');

    // Add User Form
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        console.log('Add user form found, attaching listener');

        // Remove any existing listeners to prevent duplicates
        addUserForm.removeEventListener('submit', handleAddUserSubmit);
        addUserForm.addEventListener('submit', handleAddUserSubmit);
    } else {
        console.error('Add user form not found!');
        // Try again after a short delay
        setTimeout(function() {
            const retryForm = document.getElementById('addUserForm');
            if (retryForm) {
                console.log('Add user form found on retry, attaching listener');
                retryForm.removeEventListener('submit', handleAddUserSubmit);
                retryForm.addEventListener('submit', handleAddUserSubmit);
            }
        }, 500);
    }
}

function handleAddUserSubmit(e) {
    e.preventDefault();
    console.log('Add user form submitted');

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    console.log('Form data:', data);

    // Validate passwords
    if (data.password !== data.confirm_password) {
        alert('Passwords do not match');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        return;
    }

    // Validate required fields
    if (!data.email || !data.password || !data.role || !data.status) {
        alert('Please fill in all required fields');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        return;
    }

    console.log('Sending request to user-api.php');
    fetch('user-api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create',
            ...data
        })
    })
    .then(response => {
        console.log('Response received:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert('User created successfully!');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            if (modal) {
                modal.hide();
            }
            // Reset form
            document.getElementById('addUserForm').reset();
            // Reload page to show new user
            location.reload();
        } else {
            alert('Error creating user: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating user: ' + error.message);
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

function setupEditUserForm() {
    // Edit User Form
    const editUserForm = document.getElementById('editUserForm');
    if (editUserForm) {
        console.log('Edit user form found, attaching listener');

        // Remove any existing listeners to prevent duplicates
        editUserForm.removeEventListener('submit', handleEditUserSubmit);
        editUserForm.addEventListener('submit', handleEditUserSubmit);
    } else {
        console.error('Edit user form not found!');
        // Try again after a short delay
        setTimeout(function() {
            const retryForm = document.getElementById('editUserForm');
            if (retryForm) {
                console.log('Edit user form found on retry, attaching listener');
                retryForm.removeEventListener('submit', handleEditUserSubmit);
                retryForm.addEventListener('submit', handleEditUserSubmit);
            }
        }, 500);
    }
}

function handleEditUserSubmit(e) {
    e.preventDefault();
    console.log('Edit user form submitted');

    // Disable submit button to prevent double submission
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    console.log('Edit form data:', data);

    // Validate required fields
    if (!data.email || !data.username || !data.role || !data.status) {
        alert('Please fill in all required fields');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        return;
    }

    fetch('user-api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'update',
            ...data
        })
    })
    .then(response => {
        console.log('Edit response received:', response.status, response.statusText);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Edit response data:', data);
        if (data.success) {
            alert('User updated successfully!');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            if (modal) {
                modal.hide();
            }
            // Reload page to show updated user
            location.reload();
        } else {
            alert('Error updating user: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating user: ' + error.message);
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Setup edit user form
    setupEditUserForm();

    // Ensure dark mode manager is available
    if (window.darkModeManager) {
        window.darkModeManager.updateDropdownButtons();
    }
});
</script>

        </div>

<?php
include 'includes/admin-layout-end.php';
include '../includes/footer.php';
?>
