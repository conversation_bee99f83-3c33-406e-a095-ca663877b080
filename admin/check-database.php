<?php
/**
 * Database Check and Setup
 * Check if breweries table exists and create if needed
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Database Check';
$additionalCSS = ['../assets/css/admin.css'];

$results = [];
$errors = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if breweries table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'breweries'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        $results[] = "✅ Breweries table exists";
        
        // Check table structure
        $stmt = $conn->query("DESCRIBE breweries");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results[] = "✅ Table has " . count($columns) . " columns";
        
        // Check current count
        $stmt = $conn->query("SELECT COUNT(*) as count FROM breweries");
        $count = $stmt->fetchColumn();
        $results[] = "📊 Current brewery count: " . $count;
        
        // Show table structure
        $results[] = "📋 Table structure:";
        foreach ($columns as $column) {
            $results[] = "   - " . $column['Field'] . " (" . $column['Type'] . ")";
        }
        
    } else {
        $errors[] = "❌ Breweries table does not exist";
        
        // Try to create the table
        $createSQL = "
        CREATE TABLE IF NOT EXISTS breweries (
            id INT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            state VARCHAR(50),
            zip VARCHAR(20),
            phone VARCHAR(50),
            website VARCHAR(255),
            email VARCHAR(255),
            brewery_type VARCHAR(50) DEFAULT 'micro',
            description TEXT,
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            verified TINYINT(1) DEFAULT 0,
            claimed TINYINT(1) DEFAULT 0,
            status VARCHAR(20) DEFAULT 'active',
            featured TINYINT(1) DEFAULT 0,
            follower_count INT DEFAULT 0,
            like_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_city (city),
            INDEX idx_state (state),
            INDEX idx_type (brewery_type),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($createSQL);
        $results[] = "✅ Created breweries table";
    }
    
    // Check CSV file
    $csvPath = '../layouts-for-reference/us_breweries.csv';
    if (file_exists($csvPath)) {
        $results[] = "✅ CSV file found: " . $csvPath;
        $lines = file($csvPath);
        $results[] = "📊 CSV has " . (count($lines) - 1) . " data rows";
    } else {
        $errors[] = "❌ CSV file not found: " . $csvPath;
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-database text-primary me-2"></i>
                    Database Check
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group">
                        <a href="dashboard.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Issues Found</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($results)): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Database Status</h5>
                    <ul class="mb-0">
                        <?php foreach ($results as $result): ?>
                            <li><?php echo htmlspecialchars($result); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="import-us-breweries.php" class="btn btn-warning">
                                    <i class="fas fa-database me-2"></i>Import US Breweries
                                </a>
                                <a href="breweries.php" class="btn btn-primary">
                                    <i class="fas fa-list me-2"></i>View Breweries
                                </a>
                                <a href="dashboard.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-question-circle me-2"></i>Next Steps</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($errors)): ?>
                                <p>✅ Database is ready for import!</p>
                                <p>Click "Import US Breweries" to populate the database with ~8,100 brewery records.</p>
                            <?php else: ?>
                                <p>❌ Please resolve the issues above before importing.</p>
                                <p>The system will attempt to create missing tables automatically.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
