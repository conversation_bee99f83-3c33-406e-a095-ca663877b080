<?php
/**
 * Add Missing User Columns
 * Add role, status, and other missing columns to users table
 */

require_once '../config/config.php';
requireLogin();

echo "<!DOCTYPE html><html><head><title>Add User Columns</title></head><body>";
echo "<h1>🔧 Adding Missing User Columns...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current table structure
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 Current table structure:<br>";
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    echo "<br>";
    
    // Define columns we need
    $required_columns = [
        'role' => "ENUM('admin', 'site_moderator', 'business_owner', 'business_manager', 'user') DEFAULT 'user'",
        'status' => "ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'active'",
        'username' => "VARCHAR(50) UNIQUE",
        'first_name' => "VARCHAR(100)",
        'last_name' => "VARCHAR(100)",
        'phone' => "VARCHAR(20)",
        'city' => "VARCHAR(100)",
        'state' => "VARCHAR(50)",
        'country' => "VARCHAR(100)"
    ];
    
    $added_columns = 0;
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            echo "🔄 Adding column '$column_name'...<br>";
            
            try {
                $sql = "ALTER TABLE users ADD COLUMN $column_name $column_definition";
                echo "SQL: $sql<br>";
                $conn->exec($sql);
                echo "✅ Added column '$column_name'<br>";
                $added_columns++;
            } catch (Exception $e) {
                echo "❌ Error adding column '$column_name': " . $e->getMessage() . "<br>";
            }
        } else {
            echo "✅ Column '$column_name' already exists<br>";
        }
    }
    
    echo "<br>📊 Added $added_columns new columns<br>";
    
    // Now update the admin user
    if (in_array('role', $existing_columns) || $added_columns > 0) {
        echo "<br>🔄 Updating admin user role...<br>";
        
        $stmt = $conn->prepare("UPDATE users SET role = 'admin', status = 'active' WHERE email = '<EMAIL>'");
        $stmt->execute();
        
        echo "✅ Updated admin user role to 'admin'<br>";
        
        // Verify the update
        $stmt = $conn->query("SELECT * FROM users WHERE email = '<EMAIL>'");
        $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin_user) {
            echo "<br>📋 Updated admin user data:<br>";
            foreach ($admin_user as $field => $value) {
                if (strlen($value) > 100) {
                    $value = substr($value, 0, 100) . '...';
                }
                echo "  $field: '$value'<br>";
            }
            
            // Test role mapping
            $user_roles = [
                'admin' => 'Administrator',
                'site_moderator' => 'Site Moderator',
                'business_owner' => 'Business Owner',
                'business_manager' => 'Business Manager',
                'user' => 'Standard User'
            ];
            
            $role = $admin_user['role'] ?? 'user';
            $mapped_role = $user_roles[$role] ?? $role;
            
            echo "<br>🔧 Role mapping test:<br>";
            echo "Database role: '$role'<br>";
            echo "Mapped display: '$mapped_role'<br>";
            
            if ($mapped_role === 'Administrator') {
                echo "✅ Role mapping is working correctly!<br>";
            } else {
                echo "❌ Role mapping failed<br>";
            }
        }
    }
    
    // Show final table structure
    echo "<br>📋 Final table structure:<br>";
    $stmt = $conn->query("DESCRIBE users");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($final_columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) " . 
             ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
             ($column['Default'] ? " DEFAULT {$column['Default']}" : '') . "<br>";
    }
    
    // Create a few test users with different roles
    echo "<br>🔄 Creating test users with different roles...<br>";
    
    $test_users = [
        ['email' => '<EMAIL>', 'role' => 'site_moderator', 'name' => 'Site Moderator'],
        ['email' => '<EMAIL>', 'role' => 'business_owner', 'name' => 'Business Owner'],
        ['email' => '<EMAIL>', 'role' => 'business_manager', 'name' => 'Business Manager'],
        ['email' => '<EMAIL>', 'role' => 'user', 'name' => 'Standard User']
    ];
    
    foreach ($test_users as $test_user) {
        // Check if user already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$test_user['email']]);
        
        if (!$stmt->fetch()) {
            $password = password_hash('password123', PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("
                INSERT INTO users (email, password_hash, role, status, first_name, email_verified, created_at, updated_at) 
                VALUES (?, ?, ?, 'active', ?, 1, NOW(), NOW())
            ");
            $stmt->execute([$test_user['email'], $password, $test_user['role'], $test_user['name']]);
            
            echo "✅ Created test user: {$test_user['email']} ({$test_user['role']})<br>";
        } else {
            echo "⚠️ Test user already exists: {$test_user['email']}<br>";
        }
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
