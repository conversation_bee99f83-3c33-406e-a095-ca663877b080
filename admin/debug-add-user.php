<?php
/**
 * Debug ADD USER functionality
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Test if we can receive POST data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>Raw Input:</h3>";
    echo "<pre>";
    $input = file_get_contents('php://input');
    echo htmlspecialchars($input);
    echo "</pre>";
    
    echo "<h3>JSON Decoded:</h3>";
    echo "<pre>";
    $decoded = json_decode($input, true);
    print_r($decoded);
    echo "</pre>";
    
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Add User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Debug Add User Form</h2>
        
        <form id="testForm">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" value="password123" required>
            </div>
            <div class="mb-3">
                <label for="confirm_password" class="form-label">Confirm Password</label>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" value="password123" required>
            </div>
            <div class="mb-3">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role" required>
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status" required>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Test Submit</button>
        </form>
        
        <div id="result" class="mt-3"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            console.log('Form submitted');
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            console.log('Form data:', data);
            
            // Test 1: Send to this same file
            fetch('debug-add-user.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'create',
                    ...data
                })
            })
            .then(response => response.text())
            .then(data => {
                document.getElementById('result').innerHTML = '<h4>Response:</h4><pre>' + data + '</pre>';
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>
