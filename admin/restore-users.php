<?php
/**
 * Restore Missing Users
 * Check current users and restore admin + default users
 */

require_once '../config/config.php';
requireLogin();

echo "<!DOCTYPE html><html><head><title>Restore Users</title></head><body>";
echo "<h1>🔍 Checking and Restoring Users...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current users
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetchColumn();
    
    echo "📊 Current user count: $user_count<br>";
    
    if ($user_count > 0) {
        echo "<br>👥 Current users:<br>";
        $stmt = $conn->query("SELECT * FROM users ORDER BY id");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}<br>";
        }
    } else {
        echo "❌ No users found in database!<br>";
    }
    
    echo "<br>🔄 Restoring default users...<br>";
    
    // Default users to create/restore
    $default_users = [
        [
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'role' => 'admin',
            'status' => 'active',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'site_moderator',
            'status' => 'active',
            'first_name' => 'Site',
            'last_name' => 'Moderator',
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'business_owner',
            'status' => 'active',
            'first_name' => 'Business',
            'last_name' => 'Owner',
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'business_manager',
            'status' => 'active',
            'first_name' => 'Business',
            'last_name' => 'Manager',
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user',
            'status' => 'active',
            'first_name' => 'Standard',
            'last_name' => 'User',
            'email_verified' => 1
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'user',
            'status' => 'active',
            'first_name' => 'Test',
            'last_name' => 'User',
            'email_verified' => 0
        ]
    ];
    
    $created_count = 0;
    $updated_count = 0;
    
    foreach ($default_users as $user_data) {
        // Check if user already exists
        $stmt = $conn->prepare("SELECT id, role, status FROM users WHERE email = ?");
        $stmt->execute([$user_data['email']]);
        $existing_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $password_hash = password_hash($user_data['password'], PASSWORD_DEFAULT);
        
        if ($existing_user) {
            // Update existing user
            echo "🔄 Updating existing user: {$user_data['email']}<br>";
            
            $stmt = $conn->prepare("
                UPDATE users SET 
                    password_hash = ?, role = ?, status = ?, 
                    first_name = ?, last_name = ?, email_verified = ?,
                    updated_at = NOW()
                WHERE email = ?
            ");
            $stmt->execute([
                $password_hash,
                $user_data['role'],
                $user_data['status'],
                $user_data['first_name'],
                $user_data['last_name'],
                $user_data['email_verified'],
                $user_data['email']
            ]);
            
            echo "✅ Updated: {$user_data['email']} (ID: {$existing_user['id']})<br>";
            $updated_count++;
            
        } else {
            // Create new user
            echo "➕ Creating new user: {$user_data['email']}<br>";
            
            $stmt = $conn->prepare("
                INSERT INTO users (
                    email, password_hash, role, status, first_name, last_name,
                    email_verified, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                $user_data['email'],
                $password_hash,
                $user_data['role'],
                $user_data['status'],
                $user_data['first_name'],
                $user_data['last_name'],
                $user_data['email_verified']
            ]);
            
            $new_id = $conn->lastInsertId();
            echo "✅ Created: {$user_data['email']} (ID: $new_id)<br>";
            $created_count++;
        }
    }
    
    echo "<br>📊 Summary:<br>";
    echo "- Created: $created_count new users<br>";
    echo "- Updated: $updated_count existing users<br>";
    
    // Show final user list
    echo "<br>👥 Final user list:<br>";
    $stmt = $conn->query("SELECT id, email, role, status, first_name, last_name FROM users ORDER BY id");
    $final_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($final_users as $user) {
        $name = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
        echo "- ID: {$user['id']}, Email: {$user['email']}, Name: '$name', Role: {$user['role']}, Status: {$user['status']}<br>";
    }
    
    // Show role distribution
    echo "<br>📈 Role distribution:<br>";
    $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role ORDER BY count DESC");
    $role_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($role_stats as $stat) {
        echo "- {$stat['role']}: {$stat['count']} users<br>";
    }
    
    // Show login credentials
    echo "<br>🔐 Login credentials for testing:<br>";
    echo "Admin: <EMAIL> / admin123<br>";
    echo "Moderator: <EMAIL> / password123<br>";
    echo "Business Owner: <EMAIL> / password123<br>";
    echo "Business Manager: <EMAIL> / password123<br>";
    echo "Standard User: <EMAIL> / password123<br>";
    echo "Test User: <EMAIL> / password123<br>";
    
    echo "<br>🎉 User restoration complete!<br>";
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
