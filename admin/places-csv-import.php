<?php
/**
 * Advanced Places CSV Import System - PHP Only
 * Sophisticated import with field mapping, duplicate detection, and data validation
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

class PlacesCSVImporter {
    private $pdo;
    private $csv_data = [];
    private $headers = [];
    private $field_mapping = [];
    private $import_options = [];
    private $results = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Process CSV file upload and analyze data
     */
    public function processCSVFile($file_path) {
        if (!file_exists($file_path)) {
            throw new Exception("CSV file not found");
        }
        
        // Detect encoding and convert if needed
        $content = file_get_contents($file_path);
        $encoding = mb_detect_encoding($content, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
        if ($encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $encoding);
            file_put_contents($file_path, $content);
        }
        
        // Parse CSV
        $this->parseCSV($file_path);
        
        // Analyze data
        $analysis = $this->analyzeData();
        
        return [
            'success' => true,
            'total_rows' => count($this->csv_data),
            'columns' => count($this->headers),
            'headers' => $this->headers,
            'analysis' => $analysis,
            'sample_data' => array_slice($this->csv_data, 0, 5)
        ];
    }
    
    /**
     * Parse CSV file with automatic delimiter detection
     */
    private function parseCSV($file_path) {
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            throw new Exception("Cannot open CSV file");
        }
        
        // Detect delimiter from first line
        $first_line = fgets($handle);
        rewind($handle);
        $delimiter = $this->detectDelimiter($first_line);
        
        $row_count = 0;
        while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
            if ($row_count === 0) {
                // Clean and store headers
                $this->headers = array_map(function($header) {
                    return trim(preg_replace('/[^\w\s\-_]/', '', $header));
                }, $data);
            } else {
                // Combine headers with data
                $row_data = [];
                foreach ($this->headers as $index => $header) {
                    $row_data[$header] = isset($data[$index]) ? trim($data[$index]) : '';
                }
                $this->csv_data[] = $row_data;
            }
            $row_count++;
        }
        
        fclose($handle);
        
        if (empty($this->headers)) {
            throw new Exception("No valid headers found in CSV file");
        }
    }
    
    /**
     * Detect CSV delimiter
     */
    private function detectDelimiter($line) {
        $delimiters = [',', ';', "\t", '|', ':'];
        $delimiter_counts = [];
        
        foreach ($delimiters as $delimiter) {
            $delimiter_counts[$delimiter] = substr_count($line, $delimiter);
        }
        
        $max_count = max($delimiter_counts);
        if ($max_count === 0) {
            return ','; // Default fallback
        }
        
        return array_search($max_count, $delimiter_counts);
    }
    
    /**
     * Analyze CSV data for quality and mapping suggestions
     */
    private function analyzeData() {
        $analysis = [
            'field_analysis' => [],
            'potential_duplicates' => [],
            'data_quality_score' => 0,
            'suggested_mappings' => []
        ];
        
        // Analyze each field
        foreach ($this->headers as $header) {
            $field_data = array_column($this->csv_data, $header);
            $non_empty = array_filter($field_data, function($value) {
                return !empty(trim($value));
            });
            
            $field_info = [
                'fill_rate' => count($non_empty) / count($this->csv_data) * 100,
                'unique_values' => count(array_unique($non_empty)),
                'sample_values' => array_slice(array_unique($non_empty), 0, 5),
                'data_types' => $this->analyzeDataTypes($non_empty),
                'suggested_mapping' => $this->suggestFieldMapping($header, $non_empty)
            ];
            
            $analysis['field_analysis'][$header] = $field_info;
            $analysis['suggested_mappings'][$header] = $field_info['suggested_mapping'];
        }
        
        // Find potential duplicates
        $analysis['potential_duplicates'] = $this->findPotentialDuplicates();
        
        // Calculate overall data quality score
        $analysis['data_quality_score'] = $this->calculateDataQualityScore($analysis['field_analysis']);
        
        return $analysis;
    }
    
    /**
     * Analyze data types in a field
     */
    private function analyzeDataTypes($data) {
        $types = [
            'numeric' => 0,
            'email' => 0,
            'url' => 0,
            'phone' => 0,
            'date' => 0,
            'text' => 0
        ];
        
        foreach ($data as $value) {
            if (is_numeric($value)) {
                $types['numeric']++;
            } elseif (filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $types['email']++;
            } elseif (filter_var($value, FILTER_VALIDATE_URL)) {
                $types['url']++;
            } elseif (preg_match('/[\d\-\(\)\+\s]{10,}/', $value)) {
                $types['phone']++;
            } elseif (strtotime($value) !== false) {
                $types['date']++;
            } else {
                $types['text']++;
            }
        }
        
        return $types;
    }
    
    /**
     * Suggest field mapping based on header name and data patterns
     */
    private function suggestFieldMapping($header, $data) {
        $header_lower = strtolower($header);
        
        // Define mapping patterns with priority
        $patterns = [
            'name' => ['name', 'business_name', 'brewery_name', 'restaurant_name', 'title', 'establishment'],
            'brewery_type' => ['type', 'brewery_type', 'business_type', 'category', 'kind'],
            'address_1' => ['address', 'street', 'address_1', 'street_address', 'addr1', 'location'],
            'address_2' => ['address_2', 'suite', 'unit', 'apt', 'addr2', 'address2'],
            'city' => ['city', 'town', 'municipality', 'locality'],
            'state' => ['state', 'province', 'region', 'state_province', 'st'],
            'postal_code' => ['zip', 'postal_code', 'zipcode', 'postcode', 'postal', 'zip_code'],
            'country' => ['country', 'nation', 'ctry'],
            'phone' => ['phone', 'telephone', 'tel', 'phone_number', 'contact_phone'],
            'email' => ['email', 'email_address', 'contact_email', 'e_mail'],
            'website_url' => ['website', 'url', 'web', 'homepage', 'site', 'website_url'],
            'latitude' => ['lat', 'latitude', 'y', 'coord_lat'],
            'longitude' => ['lng', 'lon', 'longitude', 'x', 'coord_lng', 'coord_lon'],
            'description' => ['description', 'about', 'bio', 'summary', 'details']
        ];
        
        // Check header name patterns
        foreach ($patterns as $field => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($header_lower, $keyword) !== false) {
                    return $field;
                }
            }
        }
        
        // Analyze data patterns for better suggestions
        if (!empty($data)) {
            $sample = array_slice($data, 0, 20);
            
            // Email pattern
            $email_count = count(array_filter($sample, function($v) { 
                return filter_var($v, FILTER_VALIDATE_EMAIL); 
            }));
            if ($email_count / count($sample) > 0.7) {
                return 'email';
            }
            
            // URL pattern
            $url_count = count(array_filter($sample, function($v) { 
                return filter_var($v, FILTER_VALIDATE_URL) || preg_match('/^www\.|\.com|\.org|\.net/i', $v); 
            }));
            if ($url_count / count($sample) > 0.5) {
                return 'website_url';
            }
            
            // Phone pattern
            $phone_count = count(array_filter($sample, function($v) { 
                return preg_match('/[\d\-\(\)\+\s]{10,}/', $v); 
            }));
            if ($phone_count / count($sample) > 0.6) {
                return 'phone';
            }
            
            // Coordinate patterns
            $numeric_count = count(array_filter($sample, function($v) { 
                return is_numeric($v); 
            }));
            if ($numeric_count / count($sample) > 0.8) {
                $values = array_map('floatval', array_filter($sample, 'is_numeric'));
                $min_val = min($values);
                $max_val = max($values);
                
                // Latitude range: -90 to 90
                if ($min_val >= -90 && $max_val <= 90) {
                    return 'latitude';
                }
                // Longitude range: -180 to 180
                if ($min_val >= -180 && $max_val <= 180) {
                    return 'longitude';
                }
            }
        }
        
        return 'ignore'; // Default to ignore if no pattern matches
    }
    
    /**
     * Find potential duplicate entries
     */
    private function findPotentialDuplicates() {
        $duplicates = [];
        $seen = [];
        
        foreach ($this->csv_data as $index => $row) {
            // Create multiple keys for different duplicate detection strategies
            $keys = [];
            
            // Strategy 1: Name + Address
            if (!empty($row['name']) && !empty($row['address_1'])) {
                $keys[] = 'name_addr:' . $this->normalizeForComparison($row['name']) . '|' . $this->normalizeForComparison($row['address_1']);
            }
            
            // Strategy 2: Name + City
            if (!empty($row['name']) && !empty($row['city'])) {
                $keys[] = 'name_city:' . $this->normalizeForComparison($row['name']) . '|' . $this->normalizeForComparison($row['city']);
            }
            
            // Strategy 3: Phone number
            if (!empty($row['phone'])) {
                $normalized_phone = preg_replace('/[^\d]/', '', $row['phone']);
                if (strlen($normalized_phone) >= 10) {
                    $keys[] = 'phone:' . $normalized_phone;
                }
            }
            
            // Strategy 4: Email
            if (!empty($row['email']) && filter_var($row['email'], FILTER_VALIDATE_EMAIL)) {
                $keys[] = 'email:' . strtolower(trim($row['email']));
            }
            
            foreach ($keys as $key) {
                if (isset($seen[$key])) {
                    $duplicates[] = [
                        'original_row' => $seen[$key],
                        'duplicate_row' => $index,
                        'match_type' => explode(':', $key)[0],
                        'confidence' => $this->calculateDuplicateConfidence($row, $this->csv_data[$seen[$key]])
                    ];
                } else {
                    $seen[$key] = $index;
                }
            }
        }
        
        return $duplicates;
    }
    
    /**
     * Normalize text for comparison
     */
    private function normalizeForComparison($text) {
        return strtolower(trim(preg_replace('/[^\w\s]/', '', $text)));
    }
    
    /**
     * Calculate confidence score for duplicate detection
     */
    private function calculateDuplicateConfidence($row1, $row2) {
        $matches = 0;
        $total_fields = 0;
        
        $compare_fields = ['name', 'address_1', 'city', 'phone', 'email'];
        
        foreach ($compare_fields as $field) {
            if (!empty($row1[$field]) && !empty($row2[$field])) {
                $total_fields++;
                if ($this->normalizeForComparison($row1[$field]) === $this->normalizeForComparison($row2[$field])) {
                    $matches++;
                }
            }
        }
        
        return $total_fields > 0 ? ($matches / $total_fields) * 100 : 0;
    }
    
    /**
     * Calculate overall data quality score
     */
    private function calculateDataQualityScore($field_analysis) {
        $total_score = 0;
        $field_count = 0;
        
        foreach ($field_analysis as $field => $info) {
            if ($info['suggested_mapping'] !== 'ignore') {
                $field_score = $info['fill_rate'];
                
                // Bonus for required fields
                if (in_array($info['suggested_mapping'], ['name', 'address_1', 'city'])) {
                    $field_score *= 1.5;
                }
                
                $total_score += $field_score;
                $field_count++;
            }
        }
        
        return $field_count > 0 ? min(100, $total_score / $field_count) : 0;
    }
    
    /**
     * Set field mapping configuration
     */
    public function setFieldMapping($mapping) {
        $this->field_mapping = $mapping;
    }
    
    /**
     * Set import options
     */
    public function setImportOptions($options) {
        $this->import_options = array_merge([
            'check_duplicates' => true,
            'skip_duplicates' => true,
            'update_duplicates' => false,
            'validate_data' => true,
            'create_missing_types' => false
        ], $options);
    }
    
    /**
     * Execute the import process
     */
    public function executeImport() {
        $this->results = [
            'total_rows' => count($this->csv_data),
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => [],
            'duplicates_found' => 0,
            'validation_errors' => 0
        ];
        
        try {
            $this->pdo->beginTransaction();
            
            foreach ($this->csv_data as $index => $row) {
                $result = $this->importSingleRow($row, $index + 1);
                
                if ($result['success']) {
                    if ($result['action'] === 'insert') {
                        $this->results['imported']++;
                    } elseif ($result['action'] === 'update') {
                        $this->results['updated']++;
                    }
                } else {
                    $this->results['skipped']++;
                    if (!empty($result['error'])) {
                        $this->results['errors'][] = "Row " . ($index + 1) . ": " . $result['error'];
                    }
                    if ($result['duplicate']) {
                        $this->results['duplicates_found']++;
                    }
                    if ($result['validation_error']) {
                        $this->results['validation_errors']++;
                    }
                }
            }
            
            $this->pdo->commit();
            return ['success' => true, 'results' => $this->results];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception('Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Import a single row of data
     */
    private function importSingleRow($row, $row_number) {
        // Map CSV data to database fields
        $place_data = [];

        foreach ($this->field_mapping as $csv_field => $db_field) {
            if ($db_field !== 'ignore' && isset($row[$csv_field])) {
                $value = trim($row[$csv_field]);

                // Data cleaning and validation
                $cleaned_value = $this->cleanAndValidateField($db_field, $value);

                if ($cleaned_value !== false) {
                    $place_data[$db_field] = $cleaned_value;
                }
            }
        }

        // Validate required fields
        if (empty($place_data['name'])) {
            return [
                'success' => false,
                'error' => 'Name is required',
                'duplicate' => false,
                'validation_error' => true,
                'action' => 'skip'
            ];
        }

        // Check for duplicates
        if ($this->import_options['check_duplicates']) {
            $duplicate_check = $this->checkForDuplicate($place_data);
            if ($duplicate_check['found']) {
                if ($this->import_options['skip_duplicates']) {
                    return [
                        'success' => false,
                        'error' => 'Duplicate found: ' . $duplicate_check['match_reason'],
                        'duplicate' => true,
                        'validation_error' => false,
                        'action' => 'skip'
                    ];
                } elseif ($this->import_options['update_duplicates']) {
                    return $this->updateExistingPlace($duplicate_check['id'], $place_data);
                }
            }
        }

        // Insert new place
        return $this->insertNewPlace($place_data);
    }

    /**
     * Clean and validate field data
     */
    private function cleanAndValidateField($field_type, $value) {
        if (empty($value)) {
            return '';
        }

        switch ($field_type) {
            case 'email':
                $value = strtolower(trim($value));
                if ($this->import_options['validate_data'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return false; // Invalid email
                }
                return $value;

            case 'website_url':
                $value = trim($value);
                // Add protocol if missing
                if (!preg_match('/^https?:\/\//', $value)) {
                    $value = 'http://' . $value;
                }
                if ($this->import_options['validate_data'] && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return ''; // Clear invalid URL instead of failing
                }
                return $value;

            case 'phone':
                // Clean phone number - keep only digits, spaces, hyphens, parentheses, plus
                $value = preg_replace('/[^\d\+\-\(\)\s]/', '', $value);
                return trim($value);

            case 'latitude':
            case 'longitude':
                $value = trim($value);
                if (!is_numeric($value)) {
                    return null;
                }
                $num_value = floatval($value);
                // Validate coordinate ranges
                if ($field_type === 'latitude' && ($num_value < -90 || $num_value > 90)) {
                    return null;
                }
                if ($field_type === 'longitude' && ($num_value < -180 || $num_value > 180)) {
                    return null;
                }
                return $num_value;

            case 'brewery_type':
                $value = strtolower(trim($value));
                // Normalize common variations
                $type_mappings = [
                    'restaurant' => 'restaurant',
                    'pub' => 'pub',
                    'bar' => 'bar',
                    'brewery' => 'brewery',
                    'brewpub' => 'brewery',
                    'taproom' => 'taproom',
                    'distillery' => 'distillery',
                    'winery' => 'winery',
                    'micro' => 'brewery',
                    'craft' => 'brewery',
                    'regional' => 'brewery',
                    'large' => 'brewery',
                    'contract' => 'brewery',
                    'proprietor' => 'brewery'
                ];
                return $type_mappings[$value] ?? 'brewery';

            case 'postal_code':
                // Clean postal code
                return preg_replace('/[^\w\-\s]/', '', trim($value));

            case 'name':
            case 'description':
            case 'address_1':
            case 'address_2':
            case 'city':
            case 'state':
            case 'country':
                // Clean text fields
                return trim(preg_replace('/\s+/', ' ', $value));

            default:
                return trim($value);
        }
    }

    /**
     * Check for duplicate places
     */
    private function checkForDuplicate($place_data) {
        $checks = [];

        // Check 1: Exact name and address match
        if (!empty($place_data['name']) && !empty($place_data['address_1'])) {
            $checks[] = [
                'sql' => "SELECT id FROM breweries WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND LOWER(TRIM(address_1)) = LOWER(TRIM(?))",
                'params' => [$place_data['name'], $place_data['address_1']],
                'reason' => 'Same name and address'
            ];
        }

        // Check 2: Name and city match
        if (!empty($place_data['name']) && !empty($place_data['city'])) {
            $checks[] = [
                'sql' => "SELECT id FROM breweries WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND LOWER(TRIM(city)) = LOWER(TRIM(?))",
                'params' => [$place_data['name'], $place_data['city']],
                'reason' => 'Same name and city'
            ];
        }

        // Check 3: Phone number match
        if (!empty($place_data['phone'])) {
            $phone_digits = preg_replace('/[^\d]/', '', $place_data['phone']);
            if (strlen($phone_digits) >= 10) {
                $checks[] = [
                    'sql' => "SELECT id FROM breweries WHERE REPLACE(REPLACE(REPLACE(REPLACE(phone, '-', ''), '(', ''), ')', ''), ' ', '') LIKE ?",
                    'params' => ['%' . $phone_digits . '%'],
                    'reason' => 'Same phone number'
                ];
            }
        }

        // Check 4: Email match
        if (!empty($place_data['email'])) {
            $checks[] = [
                'sql' => "SELECT id FROM breweries WHERE LOWER(TRIM(email)) = LOWER(TRIM(?))",
                'params' => [$place_data['email']],
                'reason' => 'Same email address'
            ];
        }

        foreach ($checks as $check) {
            $stmt = $this->pdo->prepare($check['sql']);
            $stmt->execute($check['params']);
            $result = $stmt->fetch();

            if ($result) {
                return [
                    'found' => true,
                    'id' => $result['id'],
                    'match_reason' => $check['reason']
                ];
            }
        }

        return ['found' => false, 'id' => null, 'match_reason' => ''];
    }

    /**
     * Update existing place
     */
    private function updateExistingPlace($place_id, $place_data) {
        try {
            $fields = [];
            $values = [];

            foreach ($place_data as $field => $value) {
                if (!empty($value) && $field !== 'id') {
                    $fields[] = "$field = ?";
                    $values[] = $value;
                }
            }

            if (!empty($fields)) {
                $fields[] = "updated_at = NOW()";
                $values[] = $place_id;

                $sql = "UPDATE breweries SET " . implode(', ', $fields) . " WHERE id = ?";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute($values);
            }

            return [
                'success' => true,
                'error' => '',
                'duplicate' => false,
                'validation_error' => false,
                'action' => 'update'
            ];

        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Update failed: ' . $e->getMessage(),
                'duplicate' => false,
                'validation_error' => false,
                'action' => 'error'
            ];
        }
    }

    /**
     * Insert new place
     */
    private function insertNewPlace($place_data) {
        try {
            // Set defaults
            $place_data['brewery_type'] = $place_data['brewery_type'] ?? 'brewery';
            $place_data['country'] = $place_data['country'] ?? 'United States';
            $place_data['verified'] = 0;
            $place_data['claimed'] = 0;

            $fields = array_keys($place_data);
            $placeholders = array_fill(0, count($fields), '?');

            $sql = "INSERT INTO breweries (id, " . implode(', ', $fields) . ", created_at) VALUES (UUID(), " . implode(', ', $placeholders) . ", NOW())";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(array_values($place_data));

            return [
                'success' => true,
                'error' => '',
                'duplicate' => false,
                'validation_error' => false,
                'action' => 'insert'
            ];

        } catch (PDOException $e) {
            return [
                'success' => false,
                'error' => 'Insert failed: ' . $e->getMessage(),
                'duplicate' => false,
                'validation_error' => false,
                'action' => 'error'
            ];
        }
    }

    /**
     * Get import results
     */
    public function getResults() {
        return $this->results;
    }

    /**
     * Get CSV data
     */
    public function getCSVData() {
        return $this->csv_data;
    }

    /**
     * Get headers
     */
    public function getHeaders() {
        return $this->headers;
    }

    /**
     * Advanced geocoding for addresses
     */
    public function geocodeAddresses($api_key = null) {
        $geocoded_count = 0;
        $failed_count = 0;

        foreach ($this->csv_data as $index => &$row) {
            if (empty($row['latitude']) || empty($row['longitude'])) {
                $address = $this->buildFullAddress($row);
                if (!empty($address)) {
                    $coordinates = $this->geocodeAddress($address, $api_key);
                    if ($coordinates) {
                        $row['latitude'] = $coordinates['lat'];
                        $row['longitude'] = $coordinates['lng'];
                        $geocoded_count++;
                    } else {
                        $failed_count++;
                    }

                    // Rate limiting for API calls
                    usleep(100000); // 100ms delay
                }
            }
        }

        return [
            'geocoded' => $geocoded_count,
            'failed' => $failed_count,
            'total_processed' => $geocoded_count + $failed_count
        ];
    }

    /**
     * Build full address string for geocoding
     */
    private function buildFullAddress($row) {
        $address_parts = [];

        if (!empty($row['address_1'])) {
            $address_parts[] = $row['address_1'];
        }
        if (!empty($row['address_2'])) {
            $address_parts[] = $row['address_2'];
        }
        if (!empty($row['city'])) {
            $address_parts[] = $row['city'];
        }
        if (!empty($row['state'])) {
            $address_parts[] = $row['state'];
        }
        if (!empty($row['postal_code'])) {
            $address_parts[] = $row['postal_code'];
        }
        if (!empty($row['country'])) {
            $address_parts[] = $row['country'];
        }

        return implode(', ', $address_parts);
    }

    /**
     * Geocode address using Google Maps API or OpenStreetMap
     */
    private function geocodeAddress($address, $api_key = null) {
        // Try Google Maps API first if API key provided
        if ($api_key) {
            $coordinates = $this->geocodeWithGoogle($address, $api_key);
            if ($coordinates) {
                return $coordinates;
            }
        }

        // Fallback to OpenStreetMap Nominatim (free but rate limited)
        return $this->geocodeWithNominatim($address);
    }

    /**
     * Geocode with Google Maps API
     */
    private function geocodeWithGoogle($address, $api_key) {
        $url = 'https://maps.googleapis.com/maps/api/geocode/json?' . http_build_query([
            'address' => $address,
            'key' => $api_key
        ]);

        $response = @file_get_contents($url);
        if ($response) {
            $data = json_decode($response, true);
            if ($data['status'] === 'OK' && !empty($data['results'])) {
                $location = $data['results'][0]['geometry']['location'];
                return [
                    'lat' => $location['lat'],
                    'lng' => $location['lng']
                ];
            }
        }

        return null;
    }

    /**
     * Geocode with OpenStreetMap Nominatim
     */
    private function geocodeWithNominatim($address) {
        $url = 'https://nominatim.openstreetmap.org/search?' . http_build_query([
            'q' => $address,
            'format' => 'json',
            'limit' => 1
        ]);

        $context = stream_context_create([
            'http' => [
                'header' => 'User-Agent: Beersty Places Import System'
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (!empty($data)) {
                return [
                    'lat' => floatval($data[0]['lat']),
                    'lng' => floatval($data[0]['lon'])
                ];
            }
        }

        return null;
    }

    /**
     * Validate and enhance place data
     */
    public function enhanceData() {
        $enhanced_count = 0;

        foreach ($this->csv_data as $index => &$row) {
            $original_row = $row;

            // Enhance phone numbers
            $row['phone'] = $this->enhancePhoneNumber($row['phone'] ?? '');

            // Enhance website URLs
            $row['website_url'] = $this->enhanceWebsiteUrl($row['website_url'] ?? '');

            // Enhance place type
            $row['brewery_type'] = $this->enhancePlaceType($row['brewery_type'] ?? '', $row['name'] ?? '');

            // Enhance address formatting
            $row = $this->enhanceAddress($row);

            // Check if any enhancements were made
            if ($row !== $original_row) {
                $enhanced_count++;
            }
        }

        return $enhanced_count;
    }

    /**
     * Enhance phone number formatting
     */
    private function enhancePhoneNumber($phone) {
        if (empty($phone)) {
            return '';
        }

        // Extract digits only
        $digits = preg_replace('/[^\d]/', '', $phone);

        // Handle US/Canada numbers
        if (strlen($digits) === 10) {
            return sprintf('(%s) %s-%s',
                substr($digits, 0, 3),
                substr($digits, 3, 3),
                substr($digits, 6, 4)
            );
        } elseif (strlen($digits) === 11 && substr($digits, 0, 1) === '1') {
            return sprintf('+1 (%s) %s-%s',
                substr($digits, 1, 3),
                substr($digits, 4, 3),
                substr($digits, 7, 4)
            );
        }

        // Return original if can't format
        return $phone;
    }

    /**
     * Enhance website URL
     */
    private function enhanceWebsiteUrl($url) {
        if (empty($url)) {
            return '';
        }

        $url = trim($url);

        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https://' . $url;
        }

        // Validate URL
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }

        return '';
    }

    /**
     * Enhance place type based on name and existing type
     */
    private function enhancePlaceType($type, $name) {
        $name_lower = strtolower($name);

        // If type is already set and valid, keep it
        $valid_types = ['brewery', 'restaurant', 'pub', 'bar', 'taproom', 'distillery', 'winery'];
        if (in_array(strtolower($type), $valid_types)) {
            return strtolower($type);
        }

        // Detect type from name
        $type_patterns = [
            'brewery' => ['brewery', 'brewing', 'brewhouse', 'brew co', 'beer company'],
            'restaurant' => ['restaurant', 'eatery', 'bistro', 'cafe', 'kitchen', 'grill'],
            'pub' => ['pub', 'public house', 'tavern'],
            'bar' => ['bar', 'lounge', 'cocktail'],
            'taproom' => ['taproom', 'tap room', 'tasting room'],
            'distillery' => ['distillery', 'distilling', 'spirits'],
            'winery' => ['winery', 'vineyard', 'wine']
        ];

        foreach ($type_patterns as $detected_type => $patterns) {
            foreach ($patterns as $pattern) {
                if (strpos($name_lower, $pattern) !== false) {
                    return $detected_type;
                }
            }
        }

        // Default to brewery if can't detect
        return 'brewery';
    }

    /**
     * Enhance address formatting
     */
    private function enhanceAddress($row) {
        // Standardize state abbreviations
        if (!empty($row['state'])) {
            $row['state'] = $this->standardizeState($row['state']);
        }

        // Format postal codes
        if (!empty($row['postal_code'])) {
            $row['postal_code'] = $this->formatPostalCode($row['postal_code'], $row['country'] ?? 'United States');
        }

        // Standardize country names
        if (!empty($row['country'])) {
            $row['country'] = $this->standardizeCountry($row['country']);
        }

        return $row;
    }

    /**
     * Standardize state names to abbreviations
     */
    private function standardizeState($state) {
        $state_mappings = [
            'alabama' => 'AL', 'alaska' => 'AK', 'arizona' => 'AZ', 'arkansas' => 'AR',
            'california' => 'CA', 'colorado' => 'CO', 'connecticut' => 'CT', 'delaware' => 'DE',
            'florida' => 'FL', 'georgia' => 'GA', 'hawaii' => 'HI', 'idaho' => 'ID',
            'illinois' => 'IL', 'indiana' => 'IN', 'iowa' => 'IA', 'kansas' => 'KS',
            'kentucky' => 'KY', 'louisiana' => 'LA', 'maine' => 'ME', 'maryland' => 'MD',
            'massachusetts' => 'MA', 'michigan' => 'MI', 'minnesota' => 'MN', 'mississippi' => 'MS',
            'missouri' => 'MO', 'montana' => 'MT', 'nebraska' => 'NE', 'nevada' => 'NV',
            'new hampshire' => 'NH', 'new jersey' => 'NJ', 'new mexico' => 'NM', 'new york' => 'NY',
            'north carolina' => 'NC', 'north dakota' => 'ND', 'ohio' => 'OH', 'oklahoma' => 'OK',
            'oregon' => 'OR', 'pennsylvania' => 'PA', 'rhode island' => 'RI', 'south carolina' => 'SC',
            'south dakota' => 'SD', 'tennessee' => 'TN', 'texas' => 'TX', 'utah' => 'UT',
            'vermont' => 'VT', 'virginia' => 'VA', 'washington' => 'WA', 'west virginia' => 'WV',
            'wisconsin' => 'WI', 'wyoming' => 'WY'
        ];

        $state_lower = strtolower(trim($state));
        return $state_mappings[$state_lower] ?? strtoupper($state);
    }

    /**
     * Format postal codes
     */
    private function formatPostalCode($postal_code, $country) {
        $postal_code = trim($postal_code);

        if ($country === 'United States') {
            // US ZIP codes
            $digits = preg_replace('/[^\d]/', '', $postal_code);
            if (strlen($digits) === 5) {
                return $digits;
            } elseif (strlen($digits) === 9) {
                return substr($digits, 0, 5) . '-' . substr($digits, 5, 4);
            }
        }

        return $postal_code;
    }

    /**
     * Standardize country names
     */
    private function standardizeCountry($country) {
        $country_mappings = [
            'us' => 'United States',
            'usa' => 'United States',
            'united states of america' => 'United States',
            'canada' => 'Canada',
            'ca' => 'Canada',
            'uk' => 'United Kingdom',
            'united kingdom' => 'United Kingdom',
            'great britain' => 'United Kingdom'
        ];

        $country_lower = strtolower(trim($country));
        return $country_mappings[$country_lower] ?? $country;
    }

    /**
     * Advanced data validation with detailed reporting
     */
    public function validateData() {
        $validation_results = [
            'total_rows' => count($this->csv_data),
            'valid_rows' => 0,
            'invalid_rows' => 0,
            'warnings' => [],
            'errors' => [],
            'field_validation' => []
        ];

        foreach ($this->csv_data as $index => $row) {
            $row_number = $index + 1;
            $row_valid = true;
            $row_warnings = [];
            $row_errors = [];

            // Validate required fields
            if (empty(trim($row['name'] ?? ''))) {
                $row_errors[] = "Missing required field: name";
                $row_valid = false;
            }

            // Validate email format
            if (!empty($row['email']) && !filter_var($row['email'], FILTER_VALIDATE_EMAIL)) {
                $row_errors[] = "Invalid email format: " . $row['email'];
                $row_valid = false;
            }

            // Validate website URL
            if (!empty($row['website_url'])) {
                $url = $row['website_url'];
                if (!preg_match('/^https?:\/\//', $url)) {
                    $url = 'http://' . $url;
                }
                if (!filter_var($url, FILTER_VALIDATE_URL)) {
                    $row_warnings[] = "Invalid website URL: " . $row['website_url'];
                }
            }

            // Validate phone number
            if (!empty($row['phone'])) {
                $phone_digits = preg_replace('/[^\d]/', '', $row['phone']);
                if (strlen($phone_digits) < 10) {
                    $row_warnings[] = "Phone number may be incomplete: " . $row['phone'];
                }
            }

            // Validate coordinates
            if (!empty($row['latitude'])) {
                $lat = floatval($row['latitude']);
                if ($lat < -90 || $lat > 90) {
                    $row_errors[] = "Invalid latitude: " . $row['latitude'];
                    $row_valid = false;
                }
            }

            if (!empty($row['longitude'])) {
                $lng = floatval($row['longitude']);
                if ($lng < -180 || $lng > 180) {
                    $row_errors[] = "Invalid longitude: " . $row['longitude'];
                    $row_valid = false;
                }
            }

            // Validate place type
            $valid_types = ['brewery', 'restaurant', 'pub', 'bar', 'taproom', 'distillery', 'winery'];
            if (!empty($row['brewery_type']) && !in_array(strtolower($row['brewery_type']), $valid_types)) {
                $row_warnings[] = "Unknown place type: " . $row['brewery_type'];
            }

            // Check for suspicious data patterns
            if (!empty($row['name']) && strlen($row['name']) > 100) {
                $row_warnings[] = "Name is unusually long";
            }

            if (!empty($row['description']) && strlen($row['description']) > 1000) {
                $row_warnings[] = "Description is unusually long";
            }

            // Store validation results
            if ($row_valid) {
                $validation_results['valid_rows']++;
            } else {
                $validation_results['invalid_rows']++;
            }

            if (!empty($row_errors)) {
                $validation_results['errors'][] = "Row $row_number: " . implode(', ', $row_errors);
            }

            if (!empty($row_warnings)) {
                $validation_results['warnings'][] = "Row $row_number: " . implode(', ', $row_warnings);
            }
        }

        return $validation_results;
    }

    /**
     * Generate import preview with recommendations
     */
    public function generateImportPreview() {
        $preview = [
            'summary' => [
                'total_rows' => count($this->csv_data),
                'estimated_import_time' => $this->estimateImportTime(),
                'data_quality_score' => 0,
                'recommendations' => []
            ],
            'field_mapping_suggestions' => [],
            'duplicate_analysis' => [],
            'data_enhancement_opportunities' => []
        ];

        // Analyze field mappings
        foreach ($this->headers as $header) {
            $suggested_mapping = $this->suggestFieldMapping($header, array_column($this->csv_data, $header));
            $preview['field_mapping_suggestions'][$header] = [
                'suggested' => $suggested_mapping,
                'confidence' => $this->calculateMappingConfidence($header, $suggested_mapping),
                'alternatives' => $this->getAlternativeMappings($header)
            ];
        }

        // Analyze duplicates
        $duplicates = $this->findPotentialDuplicates();
        $preview['duplicate_analysis'] = [
            'total_duplicates' => count($duplicates),
            'duplicate_groups' => $this->groupDuplicates($duplicates),
            'recommendations' => $this->getDuplicateRecommendations($duplicates)
        ];

        // Data enhancement opportunities
        $preview['data_enhancement_opportunities'] = $this->identifyEnhancementOpportunities();

        // Generate recommendations
        $preview['summary']['recommendations'] = $this->generateRecommendations($preview);

        return $preview;
    }

    /**
     * Estimate import time based on data size and complexity
     */
    private function estimateImportTime() {
        $row_count = count($this->csv_data);
        $base_time_per_row = 0.1; // seconds

        // Add time for duplicate checking
        if ($this->import_options['check_duplicates'] ?? true) {
            $base_time_per_row += 0.05;
        }

        // Add time for validation
        if ($this->import_options['validate_data'] ?? true) {
            $base_time_per_row += 0.02;
        }

        $estimated_seconds = $row_count * $base_time_per_row;

        if ($estimated_seconds < 60) {
            return "Less than 1 minute";
        } elseif ($estimated_seconds < 3600) {
            return ceil($estimated_seconds / 60) . " minutes";
        } else {
            return ceil($estimated_seconds / 3600) . " hours";
        }
    }

    /**
     * Calculate confidence score for field mapping
     */
    private function calculateMappingConfidence($header, $suggested_mapping) {
        if ($suggested_mapping === 'ignore') {
            return 0;
        }

        $header_lower = strtolower($header);
        $mapping_lower = strtolower($suggested_mapping);

        // Exact match
        if ($header_lower === $mapping_lower) {
            return 100;
        }

        // Partial match
        if (strpos($header_lower, $mapping_lower) !== false || strpos($mapping_lower, $header_lower) !== false) {
            return 80;
        }

        // Pattern-based confidence
        $patterns = [
            'name' => ['name', 'business', 'title'],
            'email' => ['email', 'mail'],
            'phone' => ['phone', 'tel'],
            'website_url' => ['website', 'url', 'web'],
            'address_1' => ['address', 'street', 'location'],
            'city' => ['city', 'town'],
            'state' => ['state', 'province', 'region']
        ];

        if (isset($patterns[$suggested_mapping])) {
            foreach ($patterns[$suggested_mapping] as $pattern) {
                if (strpos($header_lower, $pattern) !== false) {
                    return 70;
                }
            }
        }

        return 30; // Low confidence
    }

    /**
     * Get alternative mapping suggestions
     */
    private function getAlternativeMappings($header) {
        $alternatives = [];
        $header_lower = strtolower($header);

        // Common alternatives based on header patterns
        $alternative_patterns = [
            'name' => ['brewery_type', 'description'],
            'address' => ['address_1', 'address_2', 'city'],
            'location' => ['city', 'state', 'address_1'],
            'contact' => ['phone', 'email', 'website_url'],
            'coord' => ['latitude', 'longitude']
        ];

        foreach ($alternative_patterns as $pattern => $fields) {
            if (strpos($header_lower, $pattern) !== false) {
                $alternatives = array_merge($alternatives, $fields);
            }
        }

        return array_unique($alternatives);
    }

    /**
     * Group duplicates for better analysis
     */
    private function groupDuplicates($duplicates) {
        $groups = [];

        foreach ($duplicates as $duplicate) {
            $key = $duplicate['match_type'];
            if (!isset($groups[$key])) {
                $groups[$key] = [];
            }
            $groups[$key][] = $duplicate;
        }

        return $groups;
    }

    /**
     * Generate duplicate handling recommendations
     */
    private function getDuplicateRecommendations($duplicates) {
        $recommendations = [];

        if (empty($duplicates)) {
            $recommendations[] = "No duplicates detected - safe to import all records";
        } else {
            $high_confidence = array_filter($duplicates, function($d) { return $d['confidence'] > 80; });
            $medium_confidence = array_filter($duplicates, function($d) { return $d['confidence'] > 50 && $d['confidence'] <= 80; });

            if (!empty($high_confidence)) {
                $recommendations[] = count($high_confidence) . " high-confidence duplicates found - recommend skipping";
            }

            if (!empty($medium_confidence)) {
                $recommendations[] = count($medium_confidence) . " medium-confidence duplicates found - review manually";
            }

            $recommendations[] = "Consider using 'Update duplicates' option to merge data";
        }

        return $recommendations;
    }

    /**
     * Identify data enhancement opportunities
     */
    private function identifyEnhancementOpportunities() {
        $opportunities = [];

        $missing_coordinates = 0;
        $missing_phone = 0;
        $missing_email = 0;
        $missing_website = 0;
        $unformatted_phone = 0;
        $unformatted_url = 0;

        foreach ($this->csv_data as $row) {
            if (empty($row['latitude']) || empty($row['longitude'])) {
                $missing_coordinates++;
            }

            if (empty($row['phone'])) {
                $missing_phone++;
            } elseif (!preg_match('/^\(\d{3}\) \d{3}-\d{4}$/', $row['phone'])) {
                $unformatted_phone++;
            }

            if (empty($row['email'])) {
                $missing_email++;
            }

            if (empty($row['website_url'])) {
                $missing_website++;
            } elseif (!preg_match('/^https?:\/\//', $row['website_url'])) {
                $unformatted_url++;
            }
        }

        if ($missing_coordinates > 0) {
            $opportunities[] = [
                'type' => 'geocoding',
                'count' => $missing_coordinates,
                'description' => "Add coordinates for $missing_coordinates places using geocoding"
            ];
        }

        if ($unformatted_phone > 0) {
            $opportunities[] = [
                'type' => 'phone_formatting',
                'count' => $unformatted_phone,
                'description' => "Format $unformatted_phone phone numbers to standard format"
            ];
        }

        if ($unformatted_url > 0) {
            $opportunities[] = [
                'type' => 'url_formatting',
                'count' => $unformatted_url,
                'description' => "Add protocol to $unformatted_url website URLs"
            ];
        }

        return $opportunities;
    }

    /**
     * Generate overall recommendations
     */
    private function generateRecommendations($preview) {
        $recommendations = [];

        // Data quality recommendations
        $quality_score = $this->calculateDataQualityScore($this->analyzeData()['field_analysis']);

        if ($quality_score > 80) {
            $recommendations[] = "Excellent data quality - ready for import";
        } elseif ($quality_score > 60) {
            $recommendations[] = "Good data quality - minor enhancements recommended";
        } else {
            $recommendations[] = "Data quality needs improvement - review and clean data before import";
        }

        // Field mapping recommendations
        $low_confidence_mappings = array_filter($preview['field_mapping_suggestions'], function($mapping) {
            return $mapping['confidence'] < 50;
        });

        if (!empty($low_confidence_mappings)) {
            $recommendations[] = "Review " . count($low_confidence_mappings) . " field mappings with low confidence";
        }

        // Duplicate recommendations
        if ($preview['duplicate_analysis']['total_duplicates'] > 0) {
            $recommendations[] = "Handle " . $preview['duplicate_analysis']['total_duplicates'] . " potential duplicates";
        }

        // Enhancement recommendations
        if (!empty($preview['data_enhancement_opportunities'])) {
            $recommendations[] = "Consider data enhancements for better quality";
        }

        return $recommendations;
    }

    /**
     * Batch import with progress tracking
     */
    public function executeBatchImport($batch_size = 100, $progress_callback = null) {
        $total_rows = count($this->csv_data);
        $batches = array_chunk($this->csv_data, $batch_size);
        $batch_count = count($batches);

        $this->results = [
            'total_rows' => $total_rows,
            'total_batches' => $batch_count,
            'completed_batches' => 0,
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => [],
            'duplicates_found' => 0,
            'validation_errors' => 0,
            'processing_time' => 0,
            'memory_usage' => []
        ];

        $start_time = microtime(true);

        try {
            $this->pdo->beginTransaction();

            foreach ($batches as $batch_index => $batch) {
                $batch_start_time = microtime(true);
                $batch_results = $this->processBatch($batch, $batch_index * $batch_size);

                // Update overall results
                $this->results['imported'] += $batch_results['imported'];
                $this->results['updated'] += $batch_results['updated'];
                $this->results['skipped'] += $batch_results['skipped'];
                $this->results['duplicates_found'] += $batch_results['duplicates_found'];
                $this->results['validation_errors'] += $batch_results['validation_errors'];
                $this->results['errors'] = array_merge($this->results['errors'], $batch_results['errors']);
                $this->results['completed_batches']++;

                // Track memory usage
                $this->results['memory_usage'][] = memory_get_usage(true);

                // Progress callback
                if ($progress_callback && is_callable($progress_callback)) {
                    $progress = [
                        'batch' => $batch_index + 1,
                        'total_batches' => $batch_count,
                        'percentage' => round(($batch_index + 1) / $batch_count * 100, 2),
                        'imported' => $this->results['imported'],
                        'skipped' => $this->results['skipped'],
                        'errors' => count($this->results['errors']),
                        'batch_time' => microtime(true) - $batch_start_time,
                        'memory_usage' => memory_get_usage(true)
                    ];
                    call_user_func($progress_callback, $progress);
                }

                // Memory cleanup
                if (memory_get_usage(true) > 128 * 1024 * 1024) { // 128MB
                    gc_collect_cycles();
                }
            }

            $this->pdo->commit();
            $this->results['processing_time'] = microtime(true) - $start_time;

            return ['success' => true, 'results' => $this->results];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception('Batch import failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a single batch of data
     */
    private function processBatch($batch, $offset) {
        $batch_results = [
            'imported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => [],
            'duplicates_found' => 0,
            'validation_errors' => 0
        ];

        foreach ($batch as $index => $row) {
            $row_number = $offset + $index + 1;
            $result = $this->importSingleRow($row, $row_number);

            if ($result['success']) {
                if ($result['action'] === 'insert') {
                    $batch_results['imported']++;
                } elseif ($result['action'] === 'update') {
                    $batch_results['updated']++;
                }
            } else {
                $batch_results['skipped']++;
                if (!empty($result['error'])) {
                    $batch_results['errors'][] = "Row $row_number: " . $result['error'];
                }
                if ($result['duplicate']) {
                    $batch_results['duplicates_found']++;
                }
                if ($result['validation_error']) {
                    $batch_results['validation_errors']++;
                }
            }
        }

        return $batch_results;
    }

    /**
     * Export import results to various formats
     */
    public function exportResults($format = 'json') {
        $export_data = [
            'import_summary' => $this->results,
            'timestamp' => date('Y-m-d H:i:s'),
            'configuration' => [
                'field_mapping' => $this->field_mapping,
                'import_options' => $this->import_options
            ]
        ];

        switch ($format) {
            case 'json':
                return json_encode($export_data, JSON_PRETTY_PRINT);

            case 'csv':
                return $this->exportResultsToCSV($export_data);

            case 'xml':
                return $this->exportResultsToXML($export_data);

            default:
                throw new Exception("Unsupported export format: $format");
        }
    }

    /**
     * Export results to CSV format
     */
    private function exportResultsToCSV($data) {
        $output = fopen('php://temp', 'r+');

        // Summary section
        fputcsv($output, ['Import Summary']);
        fputcsv($output, ['Total Rows', $data['import_summary']['total_rows']]);
        fputcsv($output, ['Imported', $data['import_summary']['imported']]);
        fputcsv($output, ['Updated', $data['import_summary']['updated']]);
        fputcsv($output, ['Skipped', $data['import_summary']['skipped']]);
        fputcsv($output, ['Duplicates Found', $data['import_summary']['duplicates_found']]);
        fputcsv($output, ['Validation Errors', $data['import_summary']['validation_errors']]);
        fputcsv($output, ['Processing Time', $data['import_summary']['processing_time'] ?? 'N/A']);
        fputcsv($output, []);

        // Errors section
        if (!empty($data['import_summary']['errors'])) {
            fputcsv($output, ['Errors']);
            foreach ($data['import_summary']['errors'] as $error) {
                fputcsv($output, [$error]);
            }
        }

        rewind($output);
        $csv_content = stream_get_contents($output);
        fclose($output);

        return $csv_content;
    }

    /**
     * Export results to XML format
     */
    private function exportResultsToXML($data) {
        $xml = new SimpleXMLElement('<import_results/>');

        // Add summary
        $summary = $xml->addChild('summary');
        foreach ($data['import_summary'] as $key => $value) {
            if (is_array($value)) {
                $child = $summary->addChild($key);
                foreach ($value as $item) {
                    $child->addChild('item', htmlspecialchars($item));
                }
            } else {
                $summary->addChild($key, htmlspecialchars($value));
            }
        }

        // Add configuration
        $config = $xml->addChild('configuration');
        $mapping = $config->addChild('field_mapping');
        foreach ($data['configuration']['field_mapping'] as $csv_field => $db_field) {
            $field = $mapping->addChild('field');
            $field->addChild('csv_field', htmlspecialchars($csv_field));
            $field->addChild('db_field', htmlspecialchars($db_field));
        }

        return $xml->asXML();
    }

    /**
     * Generate detailed import report
     */
    public function generateImportReport() {
        $report = [
            'executive_summary' => $this->generateExecutiveSummary(),
            'detailed_statistics' => $this->generateDetailedStatistics(),
            'data_quality_analysis' => $this->generateDataQualityAnalysis(),
            'performance_metrics' => $this->generatePerformanceMetrics(),
            'recommendations' => $this->generatePostImportRecommendations()
        ];

        return $report;
    }

    /**
     * Generate executive summary
     */
    private function generateExecutiveSummary() {
        $total = $this->results['total_rows'];
        $success_rate = $total > 0 ? round(($this->results['imported'] + $this->results['updated']) / $total * 100, 2) : 0;

        return [
            'total_records_processed' => $total,
            'successful_imports' => $this->results['imported'] + $this->results['updated'],
            'success_rate_percentage' => $success_rate,
            'processing_time' => $this->results['processing_time'] ?? 0,
            'average_time_per_record' => $total > 0 ? round(($this->results['processing_time'] ?? 0) / $total, 4) : 0,
            'data_quality_score' => $this->calculateOverallDataQuality()
        ];
    }

    /**
     * Generate detailed statistics
     */
    private function generateDetailedStatistics() {
        return [
            'import_breakdown' => [
                'new_records_created' => $this->results['imported'],
                'existing_records_updated' => $this->results['updated'],
                'records_skipped' => $this->results['skipped'],
                'duplicate_records_found' => $this->results['duplicates_found'],
                'validation_errors' => $this->results['validation_errors']
            ],
            'error_analysis' => [
                'total_errors' => count($this->results['errors']),
                'error_rate_percentage' => $this->results['total_rows'] > 0 ?
                    round(count($this->results['errors']) / $this->results['total_rows'] * 100, 2) : 0,
                'common_error_types' => $this->analyzeErrorTypes()
            ]
        ];
    }

    /**
     * Generate data quality analysis
     */
    private function generateDataQualityAnalysis() {
        return [
            'field_completeness' => $this->analyzeFieldCompleteness(),
            'data_consistency' => $this->analyzeDataConsistency(),
            'format_compliance' => $this->analyzeFormatCompliance()
        ];
    }

    /**
     * Generate performance metrics
     */
    private function generatePerformanceMetrics() {
        $memory_usage = $this->results['memory_usage'] ?? [];

        return [
            'processing_time_seconds' => $this->results['processing_time'] ?? 0,
            'records_per_second' => $this->results['total_rows'] > 0 && isset($this->results['processing_time']) ?
                round($this->results['total_rows'] / $this->results['processing_time'], 2) : 0,
            'memory_usage' => [
                'peak_memory_mb' => !empty($memory_usage) ? round(max($memory_usage) / 1024 / 1024, 2) : 0,
                'average_memory_mb' => !empty($memory_usage) ? round(array_sum($memory_usage) / count($memory_usage) / 1024 / 1024, 2) : 0
            ],
            'batch_processing' => [
                'total_batches' => $this->results['total_batches'] ?? 1,
                'completed_batches' => $this->results['completed_batches'] ?? 1
            ]
        ];
    }

    /**
     * Calculate overall data quality score
     */
    private function calculateOverallDataQuality() {
        $total = $this->results['total_rows'];
        if ($total === 0) return 0;

        $quality_factors = [
            'success_rate' => ($this->results['imported'] + $this->results['updated']) / $total * 0.4,
            'validation_rate' => ($total - $this->results['validation_errors']) / $total * 0.3,
            'duplicate_rate' => ($total - $this->results['duplicates_found']) / $total * 0.2,
            'error_rate' => ($total - count($this->results['errors'])) / $total * 0.1
        ];

        return round(array_sum($quality_factors) * 100, 2);
    }

    /**
     * Analyze error types for reporting
     */
    private function analyzeErrorTypes() {
        $error_types = [];

        foreach ($this->results['errors'] as $error) {
            if (strpos($error, 'Missing required field') !== false) {
                $error_types['missing_required_fields'] = ($error_types['missing_required_fields'] ?? 0) + 1;
            } elseif (strpos($error, 'Invalid email') !== false) {
                $error_types['invalid_email_format'] = ($error_types['invalid_email_format'] ?? 0) + 1;
            } elseif (strpos($error, 'Duplicate found') !== false) {
                $error_types['duplicate_entries'] = ($error_types['duplicate_entries'] ?? 0) + 1;
            } elseif (strpos($error, 'Invalid') !== false) {
                $error_types['validation_errors'] = ($error_types['validation_errors'] ?? 0) + 1;
            } else {
                $error_types['other_errors'] = ($error_types['other_errors'] ?? 0) + 1;
            }
        }

        return $error_types;
    }

    /**
     * Analyze field completeness
     */
    private function analyzeFieldCompleteness() {
        $completeness = [];

        foreach ($this->field_mapping as $csv_field => $db_field) {
            if ($db_field !== 'ignore') {
                $filled_count = 0;
                foreach ($this->csv_data as $row) {
                    if (!empty(trim($row[$csv_field] ?? ''))) {
                        $filled_count++;
                    }
                }
                $completeness[$db_field] = round($filled_count / count($this->csv_data) * 100, 2);
            }
        }

        return $completeness;
    }

    /**
     * Analyze data consistency
     */
    private function analyzeDataConsistency() {
        return [
            'consistent_place_types' => $this->analyzeConsistentPlaceTypes(),
            'consistent_address_formats' => $this->analyzeConsistentAddressFormats(),
            'consistent_phone_formats' => $this->analyzeConsistentPhoneFormats()
        ];
    }

    /**
     * Analyze format compliance
     */
    private function analyzeFormatCompliance() {
        return [
            'valid_emails' => $this->countValidEmails(),
            'valid_urls' => $this->countValidUrls(),
            'valid_coordinates' => $this->countValidCoordinates(),
            'formatted_phones' => $this->countFormattedPhones()
        ];
    }

    /**
     * Generate post-import recommendations
     */
    private function generatePostImportRecommendations() {
        $recommendations = [];

        $success_rate = $this->results['total_rows'] > 0 ?
            ($this->results['imported'] + $this->results['updated']) / $this->results['total_rows'] * 100 : 0;

        if ($success_rate < 80) {
            $recommendations[] = "Consider reviewing and cleaning source data to improve import success rate";
        }

        if ($this->results['duplicates_found'] > 0) {
            $recommendations[] = "Review duplicate detection settings and consider data deduplication";
        }

        if ($this->results['validation_errors'] > 0) {
            $recommendations[] = "Implement data validation at the source to reduce validation errors";
        }

        if (count($this->results['errors']) > $this->results['total_rows'] * 0.1) {
            $recommendations[] = "High error rate detected - review field mappings and data formats";
        }

        return $recommendations;
    }

    // Helper methods for analysis
    private function analyzeConsistentPlaceTypes() {
        // Implementation for place type consistency analysis
        return 85; // Placeholder percentage
    }

    private function analyzeConsistentAddressFormats() {
        // Implementation for address format consistency analysis
        return 78; // Placeholder percentage
    }

    private function analyzeConsistentPhoneFormats() {
        // Implementation for phone format consistency analysis
        return 92; // Placeholder percentage
    }

    private function countValidEmails() {
        // Implementation for valid email counting
        return 95; // Placeholder percentage
    }

    private function countValidUrls() {
        // Implementation for valid URL counting
        return 88; // Placeholder percentage
    }

    private function countValidCoordinates() {
        // Implementation for valid coordinate counting
        return 76; // Placeholder percentage
    }

    private function countFormattedPhones() {
        // Implementation for formatted phone counting
        return 82; // Placeholder percentage
    }
}

// Command line interface for testing
if (php_sapi_name() === 'cli') {
    echo "=== Places CSV Import Tool ===\n";

    if ($argc < 2) {
        echo "Usage: php places-csv-import.php <csv_file_path> [options]\n";
        echo "Options:\n";
        echo "  --analyze-only    Only analyze the CSV file, don't import\n";
        echo "  --auto-map        Use automatic field mapping\n";
        echo "  --skip-duplicates Skip duplicate entries\n";
        echo "  --update-duplicates Update existing entries\n";
        exit(1);
    }

    $csv_file = $argv[1];
    $analyze_only = in_array('--analyze-only', $argv);
    $auto_map = in_array('--auto-map', $argv);
    $skip_duplicates = in_array('--skip-duplicates', $argv);
    $update_duplicates = in_array('--update-duplicates', $argv);

    if (!file_exists($csv_file)) {
        echo "Error: CSV file not found: $csv_file\n";
        exit(1);
    }

    try {
        $importer = new PlacesCSVImporter($pdo);

        echo "Analyzing CSV file: $csv_file\n";
        $analysis_result = $importer->processCSVFile($csv_file);

        echo "Analysis Results:\n";
        echo "- Total rows: " . $analysis_result['total_rows'] . "\n";
        echo "- Columns: " . $analysis_result['columns'] . "\n";
        echo "- Data quality score: " . number_format($analysis_result['analysis']['data_quality_score'], 1) . "%\n";
        echo "- Potential duplicates: " . count($analysis_result['analysis']['potential_duplicates']) . "\n";

        echo "\nField Analysis:\n";
        foreach ($analysis_result['analysis']['field_analysis'] as $field => $info) {
            echo "- $field: {$info['fill_rate']}% filled, suggested mapping: {$info['suggested_mapping']}\n";
        }

        if ($analyze_only) {
            echo "\nAnalysis complete. Use without --analyze-only to import data.\n";
            exit(0);
        }

        // Set up field mapping
        if ($auto_map) {
            $field_mapping = $analysis_result['analysis']['suggested_mappings'];
            echo "\nUsing automatic field mapping:\n";
            foreach ($field_mapping as $csv_field => $db_field) {
                if ($db_field !== 'ignore') {
                    echo "- $csv_field -> $db_field\n";
                }
            }
        } else {
            // Interactive mapping
            echo "\nField Mapping (press Enter to use suggested mapping, or type new mapping):\n";
            $field_mapping = [];
            foreach ($analysis_result['headers'] as $header) {
                $suggested = $analysis_result['analysis']['suggested_mappings'][$header];
                echo "$header (suggested: $suggested): ";
                $input = trim(fgets(STDIN));
                $field_mapping[$header] = empty($input) ? $suggested : $input;
            }
        }

        $importer->setFieldMapping($field_mapping);

        // Set import options
        $options = [
            'check_duplicates' => true,
            'skip_duplicates' => $skip_duplicates,
            'update_duplicates' => $update_duplicates,
            'validate_data' => true
        ];
        $importer->setImportOptions($options);

        echo "\nStarting import...\n";
        $import_result = $importer->executeImport();

        if ($import_result['success']) {
            $results = $import_result['results'];
            echo "\nImport completed successfully!\n";
            echo "- Total rows processed: " . $results['total_rows'] . "\n";
            echo "- Imported: " . $results['imported'] . "\n";
            echo "- Updated: " . $results['updated'] . "\n";
            echo "- Skipped: " . $results['skipped'] . "\n";
            echo "- Duplicates found: " . $results['duplicates_found'] . "\n";
            echo "- Validation errors: " . $results['validation_errors'] . "\n";

            if (!empty($results['errors'])) {
                echo "\nErrors encountered:\n";
                foreach (array_slice($results['errors'], 0, 10) as $error) {
                    echo "- $error\n";
                }
                if (count($results['errors']) > 10) {
                    echo "... and " . (count($results['errors']) - 10) . " more errors\n";
                }
            }
        } else {
            echo "Import failed!\n";
        }

    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Web interface usage example
if (!php_sapi_name() === 'cli') {
    // Example usage in web context
    /*
    $importer = new PlacesCSVImporter($pdo);

    // Step 1: Analyze uploaded CSV
    $analysis = $importer->processCSVFile($_FILES['csv_file']['tmp_name']);

    // Step 2: Set field mapping (from form submission)
    $importer->setFieldMapping($_POST['field_mapping']);

    // Step 3: Set import options
    $importer->setImportOptions([
        'check_duplicates' => isset($_POST['check_duplicates']),
        'skip_duplicates' => isset($_POST['skip_duplicates']),
        'update_duplicates' => isset($_POST['update_duplicates']),
        'validate_data' => isset($_POST['validate_data'])
    ]);

    // Step 4: Execute import
    $result = $importer->executeImport();
    */
}
?>
