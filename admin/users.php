<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'User List - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Sample users data (replace with database queries)
$users = [
    ['id' => 1, 'email' => '<EMAIL>', 'role' => 'admin', 'status' => 'active', 'created_at' => '2024-01-15', 'last_login' => '2024-01-20'],
    ['id' => 2, 'email' => '<EMAIL>', 'role' => 'brewery', 'status' => 'active', 'created_at' => '2024-01-16', 'last_login' => '2024-01-19'],
    ['id' => 3, 'email' => '<EMAIL>', 'role' => 'user', 'status' => 'active', 'created_at' => '2024-01-17', 'last_login' => '2024-01-18'],
    ['id' => 4, 'email' => '<EMAIL>', 'role' => 'moderator', 'status' => 'active', 'created_at' => '2024-01-18', 'last_login' => '2024-01-20']
];

include '../includes/header.php';
include 'includes/admin-sidebar.php';
?>

        <div class="main-header">
            <h1><i class="fas fa-user-list me-2"></i>User List</h1>
            <div class="header-actions">
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-1"></i>Add User
                </button>
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-download me-1"></i>Export Users
                </button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card stat-primary">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo count($users); ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <a href="#" class="stat-link">View All <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-success">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">4</div>
                        <div class="stat-label">Active Users</div>
                    </div>
                    <a href="#" class="stat-link">Manage <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-warning">
                    <div class="stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">2</div>
                        <div class="stat-label">Admin Users</div>
                    </div>
                    <a href="#" class="stat-link">View Admins <i class="fas fa-arrow-right"></i></a>
                </div>
                
                <div class="stat-card stat-danger">
                    <div class="stat-icon">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Suspended Users</div>
                    </div>
                    <a href="#" class="stat-link">Review <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="content-card">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>All Users</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'brewery' ? 'warning' : ($user['role'] === 'moderator' ? 'info' : 'secondary')); ?>">
                                                <?php echo ucfirst($user['role']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($user['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <small><?php echo date('M j, Y', strtotime($user['last_login'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" title="Suspend">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Role</label>
                        <select class="form-select" required>
                            <option value="">Select Role</option>
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="brewery">Business Owner</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" required>
                            <option value="active">Active</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Add User</button>
            </div>
        </div>
    </div>
</div>

<?php 
include 'includes/admin-layout-end.php';
include '../includes/footer.php'; 
?>
