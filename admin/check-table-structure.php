<?php
/**
 * Check and Fix Table Structure
 * Ensure users table has proper ID column configuration
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Check Table Structure</title></head><body>";
echo "<h1>🔍 Checking Table Structure...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current table structure
    $stmt = $conn->query("SHOW CREATE TABLE users");
    $table_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "📋 Current table structure:<br>";
    echo "<pre style='color: #0f0; background: #222; padding: 10px; overflow-x: auto;'>";
    echo htmlspecialchars($table_info['Create Table']);
    echo "</pre><br>";
    
    // Check column details
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Column details:<br>";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} " . 
             ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
             ($column['Key'] ? " {$column['Key']}" : '') . 
             ($column['Default'] !== null ? " DEFAULT '{$column['Default']}'" : '') . 
             ($column['Extra'] ? " {$column['Extra']}" : '') . "<br>";
    }
    
    // Check if ID column is properly configured
    $id_column = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'id') {
            $id_column = $column;
            break;
        }
    }
    
    echo "<br>🔧 ID Column Analysis:<br>";
    if ($id_column) {
        echo "✅ ID column exists<br>";
        echo "Type: {$id_column['Type']}<br>";
        echo "Key: {$id_column['Key']}<br>";
        echo "Extra: {$id_column['Extra']}<br>";
        
        $is_auto_increment = strpos($id_column['Extra'], 'auto_increment') !== false;
        $is_primary_key = $id_column['Key'] === 'PRI';
        $is_int = strpos($id_column['Type'], 'int') !== false;
        
        if ($is_auto_increment && $is_primary_key && $is_int) {
            echo "✅ ID column is properly configured (AUTO_INCREMENT PRIMARY KEY)<br>";
        } else {
            echo "❌ ID column needs fixing:<br>";
            if (!$is_int) echo "  - Not integer type<br>";
            if (!$is_primary_key) echo "  - Not primary key<br>";
            if (!$is_auto_increment) echo "  - Not auto increment<br>";
            
            echo "<br>🔄 Fixing ID column...<br>";
            
            // Fix the ID column
            try {
                // First, ensure all existing IDs are numeric
                $stmt = $conn->query("SELECT id FROM users WHERE id NOT REGEXP '^[0-9]+$'");
                $non_numeric_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (!empty($non_numeric_ids)) {
                    echo "🔄 Found non-numeric IDs, fixing...<br>";
                    foreach ($non_numeric_ids as $bad_id) {
                        echo "  - Removing user with non-numeric ID: '$bad_id'<br>";
                        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                        $stmt->execute([$bad_id]);
                    }
                }
                
                // Modify the ID column to be proper AUTO_INCREMENT
                $conn->exec("ALTER TABLE users MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY");
                echo "✅ Fixed ID column to be AUTO_INCREMENT PRIMARY KEY<br>";
                
            } catch (Exception $e) {
                echo "❌ Error fixing ID column: " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "❌ No ID column found!<br>";
        echo "🔄 Adding ID column...<br>";
        
        try {
            $conn->exec("ALTER TABLE users ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST");
            echo "✅ Added ID column<br>";
        } catch (Exception $e) {
            echo "❌ Error adding ID column: " . $e->getMessage() . "<br>";
        }
    }
    
    // Check current users
    echo "<br>👥 Current users:<br>";
    $stmt = $conn->query("SELECT id, email, role, status FROM users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}<br>";
    }
    
    // Show admin users specifically
    echo "<br>👑 Admin users:<br>";
    $stmt = $conn->query("SELECT id, email, first_name, last_name, status FROM users WHERE role = 'admin' ORDER BY id");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($admins)) {
        echo "❌ No admin users found!<br>";
    } else {
        foreach ($admins as $admin) {
            $name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));
            echo "- ID: {$admin['id']}, Email: {$admin['email']}, Name: '$name', Status: {$admin['status']}<br>";
        }
    }
    
    echo "<br>🎯 Multiple Admin Support:<br>";
    echo "✅ System supports unlimited admin users<br>";
    echo "✅ Each admin gets unique numeric ID<br>";
    echo "✅ All admins have same permissions<br>";
    echo "✅ Easy to add/remove admin users<br>";
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
