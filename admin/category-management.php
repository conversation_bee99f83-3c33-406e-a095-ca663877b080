<?php
/**
 * Category Management
 * Manage beer styles and food categories for places
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Category Management - Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_beer_style') {
        $name = $_POST['name'] ?? '';
        $category = $_POST['category'] ?? '';
        $description = $_POST['description'] ?? '';
        $abv_min = $_POST['abv_min'] ?? null;
        $abv_max = $_POST['abv_max'] ?? null;
        $ibu_min = $_POST['ibu_min'] ?? null;
        $ibu_max = $_POST['ibu_max'] ?? null;
        
        if ($name && $category) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO beer_styles (id, name, category, description, abv_min, abv_max, ibu_min, ibu_max, is_active, created_at) 
                    VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$name, $category, $description, $abv_min, $abv_max, $ibu_min, $ibu_max]);
                $success_message = "Beer style added successfully!";
            } catch (PDOException $e) {
                $error_message = "Error adding beer style: " . $e->getMessage();
            }
        }
    }
    
    if ($action === 'add_food_category') {
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $sort_order = $_POST['sort_order'] ?? 0;
        
        if ($name) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO food_categories (id, name, description, sort_order, is_active, created_at) 
                    VALUES (UUID(), ?, ?, ?, 1, NOW())
                ");
                $stmt->execute([$name, $description, $sort_order]);
                $success_message = "Food category added successfully!";
            } catch (PDOException $e) {
                $error_message = "Error adding food category: " . $e->getMessage();
            }
        }
    }
}

// Get beer styles
try {
    $stmt = $pdo->query("SELECT * FROM beer_styles ORDER BY category, name");
    $beer_styles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $beer_styles = [];
}

// Get food categories
try {
    $stmt = $pdo->query("SELECT * FROM food_categories ORDER BY sort_order, name");
    $food_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $food_categories = [];
}

// Group beer styles by category
$beer_categories = [];
foreach ($beer_styles as $style) {
    $beer_categories[$style['category']][] = $style;
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-tags me-2"></i>Category Management
                    </h1>
                    <p class="text-muted mb-0">Manage beer styles and food categories for all places</p>
                </div>
                <a href="<?php echo url('admin/dashboard.php'); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Category Management Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="categoryTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="beer-styles-tab" data-bs-toggle="tab" data-bs-target="#beer-styles" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beer Styles (<?php echo count($beer_styles); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="food-categories-tab" data-bs-toggle="tab" data-bs-target="#food-categories" type="button" role="tab">
                        <i class="fas fa-utensils me-2"></i>Food Categories (<?php echo count($food_categories); ?>)
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="categoryTabContent">
                <!-- Beer Styles Tab -->
                <div class="tab-pane fade show active" id="beer-styles" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-beer me-2"></i>Beer Styles
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <!-- Search Beer Styles -->
                                    <div class="mb-3">
                                        <input type="text" id="beerStyleSearch" class="form-control" placeholder="Search beer styles...">
                                    </div>
                                    
                                    <?php if (empty($beer_categories)): ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">No beer styles found</h6>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($beer_categories as $category => $styles): ?>
                                            <div class="beer-category-group mb-4">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <?php echo htmlspecialchars($category); ?> (<?php echo count($styles); ?>)
                                                </h6>
                                                <div class="row">
                                                    <?php foreach ($styles as $style): ?>
                                                        <div class="col-md-6 mb-2 beer-style-item" data-name="<?php echo strtolower($style['name']); ?>" data-category="<?php echo strtolower($category); ?>">
                                                            <div class="card card-sm">
                                                                <div class="card-body p-3">
                                                                    <div class="d-flex justify-content-between align-items-start">
                                                                        <div>
                                                                            <h6 class="mb-1"><?php echo htmlspecialchars($style['name']); ?></h6>
                                                                            <?php if ($style['description']): ?>
                                                                                <small class="text-muted"><?php echo htmlspecialchars(substr($style['description'], 0, 80)); ?>...</small>
                                                                            <?php endif; ?>
                                                                            <?php if ($style['abv_min'] || $style['abv_max']): ?>
                                                                                <br><small class="text-info">
                                                                                    ABV: <?php echo $style['abv_min'] ?? '?'; ?>-<?php echo $style['abv_max'] ?? '?'; ?>%
                                                                                </small>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                        <div class="btn-group btn-group-sm">
                                                                            <button class="btn btn-outline-primary btn-sm" onclick="editBeerStyle('<?php echo $style['id']; ?>')">
                                                                                <i class="fas fa-edit"></i>
                                                                            </button>
                                                                            <button class="btn btn-outline-danger btn-sm" onclick="deleteBeerStyle('<?php echo $style['id']; ?>')">
                                                                                <i class="fas fa-trash"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus me-2"></i>Add Beer Style
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="add_beer_style">
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Style Name *</label>
                                            <input type="text" name="name" class="form-control" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Category *</label>
                                            <input type="text" name="category" class="form-control" list="beerCategories" required>
                                            <datalist id="beerCategories">
                                                <?php foreach (array_keys($beer_categories) as $cat): ?>
                                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                                <?php endforeach; ?>
                                                <option value="IPA">
                                                <option value="Stout">
                                                <option value="Lager">
                                                <option value="Wheat">
                                                <option value="Pale Ale">
                                                <option value="Porter">
                                                <option value="Sour">
                                                <option value="Farmhouse">
                                            </datalist>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea name="description" class="form-control" rows="3"></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-6">
                                                <label class="form-label">ABV Min</label>
                                                <input type="number" name="abv_min" class="form-control" step="0.1" min="0" max="20">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">ABV Max</label>
                                                <input type="number" name="abv_max" class="form-control" step="0.1" min="0" max="20">
                                            </div>
                                        </div>
                                        
                                        <div class="row mt-3">
                                            <div class="col-6">
                                                <label class="form-label">IBU Min</label>
                                                <input type="number" name="ibu_min" class="form-control" min="0" max="120">
                                            </div>
                                            <div class="col-6">
                                                <label class="form-label">IBU Max</label>
                                                <input type="number" name="ibu_max" class="form-control" min="0" max="120">
                                            </div>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success w-100 mt-3">
                                            <i class="fas fa-save me-2"></i>Add Beer Style
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Food Categories Tab -->
                <div class="tab-pane fade" id="food-categories" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-utensils me-2"></i>Food Categories
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <!-- Search Food Categories -->
                                    <div class="mb-3">
                                        <input type="text" id="foodCategorySearch" class="form-control" placeholder="Search food categories...">
                                    </div>
                                    
                                    <?php if (empty($food_categories)): ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">No food categories found</h6>
                                        </div>
                                    <?php else: ?>
                                        <div class="row">
                                            <?php foreach ($food_categories as $category): ?>
                                                <div class="col-md-6 mb-3 food-category-item" data-name="<?php echo strtolower($category['name']); ?>">
                                                    <div class="card">
                                                        <div class="card-body">
                                                            <div class="d-flex justify-content-between align-items-start">
                                                                <div>
                                                                    <h6 class="mb-1"><?php echo htmlspecialchars($category['name']); ?></h6>
                                                                    <?php if ($category['description']): ?>
                                                                        <small class="text-muted"><?php echo htmlspecialchars($category['description']); ?></small>
                                                                    <?php endif; ?>
                                                                    <br><small class="text-info">Sort Order: <?php echo $category['sort_order']; ?></small>
                                                                </div>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button class="btn btn-outline-primary btn-sm" onclick="editFoodCategory('<?php echo $category['id']; ?>')">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteFoodCategory('<?php echo $category['id']; ?>')">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus me-2"></i>Add Food Category
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="add_food_category">
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Category Name *</label>
                                            <input type="text" name="name" class="form-control" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea name="description" class="form-control" rows="3"></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">Sort Order</label>
                                            <input type="number" name="sort_order" class="form-control" value="<?php echo count($food_categories) + 1; ?>">
                                            <small class="text-muted">Lower numbers appear first</small>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-save me-2"></i>Add Category
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Search functionality for beer styles
document.getElementById('beerStyleSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const items = document.querySelectorAll('.beer-style-item');
    const groups = document.querySelectorAll('.beer-category-group');
    
    items.forEach(item => {
        const name = item.dataset.name;
        const category = item.dataset.category;
        const matches = name.includes(searchTerm) || category.includes(searchTerm);
        item.style.display = matches ? 'block' : 'none';
    });
    
    // Hide empty category groups
    groups.forEach(group => {
        const visibleItems = group.querySelectorAll('.beer-style-item[style="display: block"], .beer-style-item:not([style*="display: none"])');
        group.style.display = visibleItems.length > 0 ? 'block' : 'none';
    });
});

// Search functionality for food categories
document.getElementById('foodCategorySearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const items = document.querySelectorAll('.food-category-item');
    
    items.forEach(item => {
        const name = item.dataset.name;
        const matches = name.includes(searchTerm);
        item.style.display = matches ? 'block' : 'none';
    });
});

// Edit and delete functions (placeholder)
function editBeerStyle(id) {
    alert('Edit beer style functionality coming soon!');
}

function deleteBeerStyle(id) {
    if (confirm('Are you sure you want to delete this beer style?')) {
        // TODO: Implement delete functionality
        alert('Delete functionality coming soon!');
    }
}

function editFoodCategory(id) {
    alert('Edit food category functionality coming soon!');
}

function deleteFoodCategory(id) {
    if (confirm('Are you sure you want to delete this food category?')) {
        // TODO: Implement delete functionality
        alert('Delete functionality coming soon!');
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
