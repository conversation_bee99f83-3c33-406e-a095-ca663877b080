<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Simple User Test';
$additionalCSS = ['../assets/css/admin.css'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get users
    $stmt = $conn->query("SELECT * FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1>🧪 Simple User Test</h1>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-body">
                    <h5>Test Users</h5>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Role (Raw)</th>
                                <th>Role (Mapped)</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $user_roles = [
                                'admin' => 'Administrator',
                                'site_moderator' => 'Site Moderator',
                                'business_owner' => 'Business Owner',
                                'business_manager' => 'Business Manager',
                                'user' => 'Standard User'
                            ];
                            
                            foreach ($users as $user): 
                                $role = $user['role'] ?? 'user';
                                $mapped_role = $user_roles[$role] ?? $role;
                            ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['email'] ?? ''); ?></td>
                                    <td><code><?php echo htmlspecialchars($role); ?></code></td>
                                    <td><span class="badge bg-info"><?php echo htmlspecialchars($mapped_role); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo htmlspecialchars($user['status'] ?? 'active'); ?></span></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="testEdit(<?php echo $user['id']; ?>)">
                                            Edit
                                        </button>
                                        <button type="button" class="btn btn-sm btn-info" onclick="testView(<?php echo $user['id']; ?>)">
                                            View
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    
                    <div id="testOutput" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Simple Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <p>Loading...</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function log(message) {
    const output = document.getElementById('testOutput');
    output.innerHTML += '<div class="alert alert-info small">' + message + '</div>';
    console.log(message);
}

function testEdit(userId) {
    log('🔄 Testing edit for user ID: ' + userId);
    
    fetch('user-api.php?action=get&id=' + userId)
        .then(response => {
            log('📡 Response status: ' + response.status);
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            log('📄 Response data: ' + JSON.stringify(data, null, 2));
            
            if (data.success) {
                log('✅ API call successful');
                document.getElementById('modalContent').innerHTML = '<pre>' + JSON.stringify(data.user, null, 2) + '</pre>';
                
                // Test modal
                if (typeof bootstrap !== 'undefined') {
                    log('✅ Bootstrap available, showing modal');
                    const modal = new bootstrap.Modal(document.getElementById('testModal'));
                    modal.show();
                } else {
                    log('❌ Bootstrap not available');
                    alert('Data loaded: ' + JSON.stringify(data.user));
                }
            } else {
                log('❌ API error: ' + data.message);
            }
        })
        .catch(error => {
            log('❌ Fetch error: ' + error.message);
        });
}

function testView(userId) {
    log('🔄 Testing view for user ID: ' + userId);
    
    fetch('user-api.php?action=view&id=' + userId)
        .then(response => response.json())
        .then(data => {
            log('📄 View response: ' + JSON.stringify(data, null, 2));
            
            if (data.success) {
                document.getElementById('modalContent').innerHTML = data.html;
                
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(document.getElementById('testModal'));
                    modal.show();
                } else {
                    alert('View data loaded');
                }
            } else {
                log('❌ View error: ' + data.message);
            }
        })
        .catch(error => {
            log('❌ View fetch error: ' + error.message);
        });
}

// Test on page load
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Page loaded');
    
    if (typeof bootstrap !== 'undefined') {
        log('✅ Bootstrap is available (version: ' + bootstrap.Tooltip.VERSION + ')');
    } else {
        log('❌ Bootstrap is NOT available');
    }
    
    if (typeof $ !== 'undefined') {
        log('✅ jQuery is available');
    } else {
        log('❌ jQuery is NOT available');
    }
    
    // Test fetch API
    if (typeof fetch !== 'undefined') {
        log('✅ Fetch API is available');
    } else {
        log('❌ Fetch API is NOT available');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
