<?php
/**
 * User Management API
 * Handles AJAX requests for user management operations
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

header('Content-Type: application/json');

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetRequest($conn);
    } elseif ($method === 'POST') {
        handlePostRequest($conn);
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function handleGetRequest($conn) {
    $action = $_GET['action'] ?? '';
    $userId = $_GET['id'] ?? '';
    
    switch ($action) {
        case 'get':
            getUserData($conn, $userId);
            break;
        case 'view':
            getUserDetails($conn, $userId);
            break;
        default:
            throw new Exception('Invalid action');
    }
}

function handlePostRequest($conn) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    // Debug logging
    error_log("User API POST request - Action: " . $action);
    error_log("User API POST request - Input: " . print_r($input, true));

    switch ($action) {
        case 'create':
            createUser($conn, $input);
            break;
        case 'update':
            updateUser($conn, $input);
            break;
        case 'update_status':
            updateUserStatus($conn, $input);
            break;
        case 'delete':
            deleteUser($conn, $input);
            break;
        default:
            throw new Exception('Invalid action: ' . $action);
    }
}

function getUserData($conn, $userId) {
    // First check what columns exist
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Build SELECT based on available columns
    $selectFields = ['id'];
    if (in_array('username', $columns)) $selectFields[] = 'username';
    if (in_array('email', $columns)) $selectFields[] = 'email';
    if (in_array('first_name', $columns)) $selectFields[] = 'first_name';
    if (in_array('last_name', $columns)) $selectFields[] = 'last_name';
    if (in_array('role', $columns)) $selectFields[] = 'role';
    if (in_array('status', $columns)) $selectFields[] = 'status';
    if (in_array('email_verified', $columns)) $selectFields[] = 'email_verified';
    if (in_array('phone', $columns)) $selectFields[] = 'phone';
    if (in_array('city', $columns)) $selectFields[] = 'city';
    if (in_array('state', $columns)) $selectFields[] = 'state';
    if (in_array('country', $columns)) $selectFields[] = 'country';

    $sql = "SELECT " . implode(', ', $selectFields) . " FROM users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        throw new Exception('User not found');
    }

    // Add default values for missing fields
    if (!isset($user['username'])) $user['username'] = $user['email'] ?? '';
    if (!isset($user['role'])) $user['role'] = 'user';
    if (!isset($user['status'])) $user['status'] = 'active';
    if (!isset($user['email_verified'])) $user['email_verified'] = 0;

    echo json_encode([
        'success' => true,
        'user' => $user
    ]);
}

function getUserDetails($conn, $userId) {
    // First check what tables exist
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Check what columns exist in users table
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Build SELECT based on available columns
    $selectFields = ['u.*'];
    $joins = [];
    $counts = [];

    // Only add joins for tables that exist
    if (in_array('business_claims', $tables)) {
        $joins[] = "LEFT JOIN business_claims bc ON u.id = bc.user_id AND bc.status = 'approved'";
        $counts[] = "COUNT(DISTINCT bc.id) as business_count";
    } else {
        $counts[] = "0 as business_count";
    }

    if (in_array('reviews', $tables)) {
        $joins[] = "LEFT JOIN reviews r ON u.id = r.user_id";
        $counts[] = "COUNT(DISTINCT r.id) as review_count";
    } else {
        $counts[] = "0 as review_count";
    }

    if (in_array('checkins', $tables)) {
        $joins[] = "LEFT JOIN checkins ci ON u.id = ci.user_id";
        $counts[] = "COUNT(DISTINCT ci.id) as checkin_count";
    } else {
        $counts[] = "0 as checkin_count";
    }

    $sql = "SELECT " . implode(', ', array_merge($selectFields, $counts)) . "
            FROM users u
            " . implode(' ', $joins) . "
            WHERE u.id = ?
            GROUP BY u.id";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // User roles
    $user_roles = [
        'admin' => 'Administrator',
        'site_moderator' => 'Site Moderator',
        'business_owner' => 'Business Owner',
        'business_manager' => 'Business Manager',
        'user' => 'Standard User'
    ];
    
    $user_statuses = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'pending' => 'Pending Verification'
    ];
    
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6>Basic Information</h6>
            <table class="table table-sm">
                <tr><td><strong>ID:</strong></td><td>' . htmlspecialchars($user['id']) . '</td></tr>
                <tr><td><strong>Username:</strong></td><td>' . htmlspecialchars($user['username'] ?? $user['email'] ?? 'N/A') . '</td></tr>
                <tr><td><strong>Email:</strong></td><td>' . htmlspecialchars($user['email'] ?? 'N/A') . '</td></tr>
                <tr><td><strong>Name:</strong></td><td>' . htmlspecialchars(trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''))) . '</td></tr>
                <tr><td><strong>Role:</strong></td><td><span class="badge bg-info">' . ($user_roles[$user['role'] ?? 'user'] ?? ($user['role'] ?? 'user')) . '</span></td></tr>
                <tr><td><strong>Status:</strong></td><td><span class="badge bg-' . getStatusColor($user['status'] ?? 'active') . '">' . ($user_statuses[$user['status'] ?? 'active'] ?? ($user['status'] ?? 'active')) . '</span></td></tr>
                <tr><td><strong>Email Verified:</strong></td><td>' . (($user['email_verified'] ?? 0) ? '<i class="fas fa-check text-success"></i> Yes' : '<i class="fas fa-times text-danger"></i> No') . '</td></tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6>Activity Statistics</h6>
            <table class="table table-sm">
                <tr><td><strong>Businesses Claimed:</strong></td><td>' . ($user['business_count'] ?? 0) . '</td></tr>
                <tr><td><strong>Reviews Written:</strong></td><td>' . ($user['review_count'] ?? 0) . '</td></tr>
                <tr><td><strong>Check-ins:</strong></td><td>' . ($user['checkin_count'] ?? 0) . '</td></tr>
                <tr><td><strong>Member Since:</strong></td><td>' . (isset($user['created_at']) ? date('M j, Y', strtotime($user['created_at'])) : 'Unknown') . '</td></tr>
                <tr><td><strong>Last Login:</strong></td><td>' . (isset($user['last_login']) && $user['last_login'] ? date('M j, Y g:i A', strtotime($user['last_login'])) : 'Never') . '</td></tr>
                <tr><td><strong>Last Updated:</strong></td><td>' . (isset($user['updated_at']) ? date('M j, Y g:i A', strtotime($user['updated_at'])) : 'Unknown') . '</td></tr>
            </table>
        </div>
    </div>';
    
    if (!empty($user['phone'] ?? '') || !empty($user['city'] ?? '') || !empty($user['state'] ?? '') || !empty($user['country'] ?? '')) {
        $html .= '
        <div class="row mt-3">
            <div class="col-12">
                <h6>Contact Information</h6>
                <table class="table table-sm">
                    ' . (!empty($user['phone'] ?? '') ? '<tr><td><strong>Phone:</strong></td><td>' . htmlspecialchars($user['phone']) . '</td></tr>' : '') . '
                    ' . (!empty($user['city'] ?? '') ? '<tr><td><strong>City:</strong></td><td>' . htmlspecialchars($user['city']) . '</td></tr>' : '') . '
                    ' . (!empty($user['state'] ?? '') ? '<tr><td><strong>State:</strong></td><td>' . htmlspecialchars($user['state']) . '</td></tr>' : '') . '
                    ' . (!empty($user['country'] ?? '') ? '<tr><td><strong>Country:</strong></td><td>' . htmlspecialchars($user['country']) . '</td></tr>' : '') . '
                </table>
            </div>
        </div>';
    }
    
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
}

function createUser($conn, $data) {
    // Debug logging
    error_log("createUser called with data: " . print_r($data, true));

    // Check what columns exist
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    error_log("Available columns: " . print_r($columns, true));

    // Validate required fields based on available columns
    if (empty($data['email'])) {
        error_log("createUser error: Email is required");
        throw new Exception("Email is required");
    }
    if (empty($data['password'])) {
        error_log("createUser error: Password is required");
        throw new Exception("Password is required");
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$data['email']]);
    if ($stmt->fetch()) {
        throw new Exception('Email already exists');
    }

    // Hash password
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

    // Build INSERT based on available columns
    $password_field = in_array('password', $columns) ? 'password' : 'password_hash';
    $insertFields = ['email', $password_field];
    $insertValues = [$data['email'], $hashedPassword];
    $placeholders = ['?', '?'];

    if (in_array('username', $columns) && !empty($data['username'])) {
        $insertFields[] = 'username';
        $insertValues[] = $data['username'];
        $placeholders[] = '?';
    }
    if (in_array('first_name', $columns)) {
        $insertFields[] = 'first_name';
        $insertValues[] = $data['first_name'] ?? '';
        $placeholders[] = '?';
    }
    if (in_array('last_name', $columns)) {
        $insertFields[] = 'last_name';
        $insertValues[] = $data['last_name'] ?? '';
        $placeholders[] = '?';
    }
    if (in_array('role', $columns)) {
        $insertFields[] = 'role';
        $insertValues[] = $data['role'] ?? 'user';
        $placeholders[] = '?';
    }
    if (in_array('status', $columns)) {
        $insertFields[] = 'status';
        $insertValues[] = $data['status'] ?? 'active';
        $placeholders[] = '?';
    }
    if (in_array('email_verified', $columns)) {
        $insertFields[] = 'email_verified';
        $insertValues[] = 0;
        $placeholders[] = '?';
    }
    if (in_array('created_at', $columns)) {
        $insertFields[] = 'created_at';
        $placeholders[] = 'NOW()';
    }
    if (in_array('updated_at', $columns)) {
        $insertFields[] = 'updated_at';
        $placeholders[] = 'NOW()';
    }

    $sql = "INSERT INTO users (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $placeholders) . ")";
    error_log("createUser SQL: " . $sql);
    error_log("createUser values: " . print_r($insertValues, true));

    $stmt = $conn->prepare($sql);
    $result = $stmt->execute($insertValues);

    if ($result) {
        $userId = $conn->lastInsertId();
        error_log("createUser success: User created with ID " . $userId);
        echo json_encode([
            'success' => true,
            'message' => 'User created successfully',
            'user_id' => $userId
        ]);
    } else {
        error_log("createUser error: Failed to execute SQL");
        throw new Exception('Failed to create user');
    }
}

function updateUser($conn, $data) {
    $userId = $data['user_id'] ?? '';
    if (empty($userId)) {
        throw new Exception('User ID is required');
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    if (!$stmt->fetch()) {
        throw new Exception('User not found');
    }
    
    // Check if username or email already exists for other users
    $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
    $stmt->execute([$data['username'], $data['email'], $userId]);
    if ($stmt->fetch()) {
        throw new Exception('Username or email already exists');
    }
    
    // Update user
    $stmt = $conn->prepare("
        UPDATE users SET
            username = ?, email = ?, first_name = ?, last_name = ?,
            role = ?, status = ?, email_verified = ?, updated_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([
        $data['username'],
        $data['email'],
        $data['first_name'] ?? '',
        $data['last_name'] ?? '',
        $data['role'],
        $data['status'],
        isset($data['email_verified']) ? 1 : 0,
        $userId
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'User updated successfully'
    ]);
}

function updateUserStatus($conn, $data) {
    $userId = $data['user_id'] ?? '';
    $status = $data['status'] ?? '';
    
    if (empty($userId) || empty($status)) {
        throw new Exception('User ID and status are required');
    }
    
    $stmt = $conn->prepare("UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$status, $userId]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('User not found');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'User status updated successfully'
    ]);
}

function deleteUser($conn, $data) {
    $userId = $data['user_id'] ?? '';
    if (empty($userId)) {
        throw new Exception('User ID is required');
    }
    
    // Don't allow deleting the current user
    if ($userId == $_SESSION['user_id']) {
        throw new Exception('Cannot delete your own account');
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    if (!$stmt->fetch()) {
        throw new Exception('User not found');
    }
    
    // Delete user (consider soft delete in production)
    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'User deleted successfully'
    ]);
}

function getStatusColor($status) {
    $colors = [
        'active' => 'success',
        'inactive' => 'secondary',
        'suspended' => 'danger',
        'pending' => 'warning'
    ];
    return $colors[$status] ?? 'secondary';
}
?>
