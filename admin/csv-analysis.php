<?php
/**
 * CSV Data Analysis
 * Analyze the CSV files before importing
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<h1>📊 CSV Data Analysis</h1>";

function analyzeCSV($filename) {
    $csvPath = '../csv-data/' . $filename;
    
    if (!file_exists($csvPath)) {
        return ['error' => "File not found: $filename"];
    }
    
    $csvData = file_get_contents($csvPath);
    $lines = str_getcsv($csvData, "\n");
    
    if (count($lines) < 2) {
        return ['error' => "Invalid CSV file"];
    }
    
    $headers = str_getcsv($lines[0]);
    $dataRows = array_slice($lines, 1);
    
    $analysis = [
        'filename' => $filename,
        'total_rows' => count($dataRows),
        'headers' => $headers,
        'sample_data' => [],
        'states' => [],
        'brewery_types' => [],
        'websites' => 0,
        'empty_names' => 0
    ];
    
    // Analyze first 10 rows and collect statistics
    for ($i = 0; $i < min(10, count($dataRows)); $i++) {
        $row = str_getcsv($dataRows[$i]);
        if (count($row) >= count($headers)) {
            $analysis['sample_data'][] = array_combine($headers, $row);
        }
    }
    
    // Analyze all data for statistics
    foreach ($dataRows as $line) {
        $row = str_getcsv($line);
        if (count($row) >= count($headers)) {
            $data = array_combine($headers, $row);
            
            // Count states
            if (!empty($data['state'])) {
                $state = trim($data['state']);
                $analysis['states'][$state] = ($analysis['states'][$state] ?? 0) + 1;
            }
            
            // Count brewery types
            if (!empty($data['brewery_type'])) {
                $type = trim($data['brewery_type']);
                $analysis['brewery_types'][$type] = ($analysis['brewery_types'][$type] ?? 0) + 1;
            }
            
            // Count websites
            if (!empty(trim($data['website_url'] ?? ''))) {
                $analysis['websites']++;
            }
            
            // Count empty names
            if (empty(trim($data['name'] ?? ''))) {
                $analysis['empty_names']++;
            }
        }
    }
    
    return $analysis;
}

try {
    echo "<h2>🍺 Michigan Breweries Analysis</h2>";
    $michiganAnalysis = analyzeCSV('michigan_breweries.csv');
    
    if (isset($michiganAnalysis['error'])) {
        echo "<p class='error'>❌ " . $michiganAnalysis['error'] . "</p>";
    } else {
        echo "<div class='analysis-card'>";
        echo "<h3>📋 Overview</h3>";
        echo "<ul>";
        echo "<li><strong>Total Breweries:</strong> " . number_format($michiganAnalysis['total_rows']) . "</li>";
        echo "<li><strong>Columns:</strong> " . implode(', ', $michiganAnalysis['headers']) . "</li>";
        echo "<li><strong>Breweries with Websites:</strong> " . number_format($michiganAnalysis['websites']) . "</li>";
        echo "<li><strong>Empty Names:</strong> " . $michiganAnalysis['empty_names'] . "</li>";
        echo "</ul>";
        
        echo "<h3>🏭 Brewery Types</h3>";
        echo "<ul>";
        arsort($michiganAnalysis['brewery_types']);
        foreach ($michiganAnalysis['brewery_types'] as $type => $count) {
            echo "<li><strong>$type:</strong> " . number_format($count) . "</li>";
        }
        echo "</ul>";
        
        echo "<h3>📍 States</h3>";
        echo "<ul>";
        arsort($michiganAnalysis['states']);
        foreach ($michiganAnalysis['states'] as $state => $count) {
            echo "<li><strong>$state:</strong> " . number_format($count) . "</li>";
        }
        echo "</ul>";
        
        echo "<h3>📄 Sample Data (First 5 Breweries)</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; border-collapse: collapse;'>";
        echo "<thead><tr>";
        foreach ($michiganAnalysis['headers'] as $header) {
            echo "<th style='background: #f0f0f0;'>$header</th>";
        }
        echo "</tr></thead><tbody>";
        
        foreach (array_slice($michiganAnalysis['sample_data'], 0, 5) as $row) {
            echo "<tr>";
            foreach ($michiganAnalysis['headers'] as $header) {
                $value = $row[$header] ?? '';
                echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    }
    
    echo "<hr>";
    
    echo "<h2>🇺🇸 US Breweries Analysis</h2>";
    $usAnalysis = analyzeCSV('us_breweries.csv');
    
    if (isset($usAnalysis['error'])) {
        echo "<p class='error'>❌ " . $usAnalysis['error'] . "</p>";
    } else {
        echo "<div class='analysis-card'>";
        echo "<h3>📋 Overview</h3>";
        echo "<ul>";
        echo "<li><strong>Total Breweries:</strong> " . number_format($usAnalysis['total_rows']) . "</li>";
        echo "<li><strong>Columns:</strong> " . implode(', ', $usAnalysis['headers']) . "</li>";
        echo "<li><strong>Breweries with Websites:</strong> " . number_format($usAnalysis['websites']) . "</li>";
        echo "<li><strong>Empty Names:</strong> " . $usAnalysis['empty_names'] . "</li>";
        echo "</ul>";
        
        echo "<h3>🏭 Brewery Types (Top 10)</h3>";
        echo "<ul>";
        arsort($usAnalysis['brewery_types']);
        $topTypes = array_slice($usAnalysis['brewery_types'], 0, 10, true);
        foreach ($topTypes as $type => $count) {
            echo "<li><strong>$type:</strong> " . number_format($count) . "</li>";
        }
        echo "</ul>";
        
        echo "<h3>📍 States (Top 15)</h3>";
        echo "<ul>";
        arsort($usAnalysis['states']);
        $topStates = array_slice($usAnalysis['states'], 0, 15, true);
        foreach ($topStates as $state => $count) {
            echo "<li><strong>$state:</strong> " . number_format($count) . "</li>";
        }
        echo "</ul>";
        
        echo "<h3>📄 Sample Data (First 3 Breweries)</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; border-collapse: collapse;'>";
        echo "<thead><tr>";
        foreach ($usAnalysis['headers'] as $header) {
            echo "<th style='background: #f0f0f0;'>$header</th>";
        }
        echo "</tr></thead><tbody>";
        
        foreach (array_slice($usAnalysis['sample_data'], 0, 3) as $row) {
            echo "<tr>";
            foreach ($usAnalysis['headers'] as $header) {
                $value = $row[$header] ?? '';
                echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Analysis failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h2>🚀 Ready to Import</h2>";
echo "<p>Both CSV files are ready for import. Choose which dataset to import:</p>";
echo "<ul>";
echo "<li><a href='csv-import.php' class='btn btn-primary'>Go to CSV Import Page</a></li>";
echo "<li><a href='dashboard.php' class='btn btn-secondary'>Back to Dashboard</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.analysis-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border-left: 4px solid #007bff;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }

table {
    font-size: 12px;
    margin: 10px 0;
}

th {
    background-color: #f0f0f0 !important;
    font-weight: bold;
    text-align: left;
}

td, th {
    padding: 8px;
    border: 1px solid #ddd;
}

.error {
    color: #dc3545;
    background: #f8d7da;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #f5c6cb;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
