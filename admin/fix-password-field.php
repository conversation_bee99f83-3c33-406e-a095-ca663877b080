<?php
/**
 * Fix Password Field
 * Ensure password field is correctly named in database
 */

require_once '../config/config.php';
requireLogin();

echo "<!DOCTYPE html><html><head><title>Fix Password Field</title></head><body>";
echo "<h1>🔧 Checking Password Field...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check current table structure
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📋 Current columns:<br>";
    $password_fields = [];
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
        if (strpos($column['Field'], 'password') !== false) {
            $password_fields[] = $column['Field'];
        }
    }
    
    echo "<br>🔐 Password-related fields: " . implode(', ', $password_fields) . "<br>";
    
    // Check if we need to rename password_hash to password
    if (in_array('password_hash', $password_fields) && !in_array('password', $password_fields)) {
        echo "<br>🔄 Renaming password_hash to password for consistency...<br>";
        
        try {
            $conn->exec("ALTER TABLE users CHANGE password_hash password VARCHAR(255) NOT NULL");
            echo "✅ Renamed password_hash to password<br>";
        } catch (Exception $e) {
            echo "❌ Error renaming field: " . $e->getMessage() . "<br>";
        }
    } elseif (in_array('password', $password_fields)) {
        echo "✅ Password field already exists<br>";
    } else {
        echo "❌ No password field found!<br>";
        echo "🔄 Adding password field...<br>";
        
        try {
            $conn->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL");
            echo "✅ Added password field<br>";
        } catch (Exception $e) {
            echo "❌ Error adding password field: " . $e->getMessage() . "<br>";
        }
    }
    
    // Update user-api.php to use correct field name
    echo "<br>🔄 Checking user-api.php field usage...<br>";
    
    // Show final table structure
    echo "<br>📋 Final table structure:<br>";
    $stmt = $conn->query("DESCRIBE users");
    $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($final_columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    // Test user creation with correct field
    echo "<br>🧪 Testing user creation...<br>";
    
    $test_email = '<EMAIL>';
    
    // Delete if exists
    $stmt = $conn->prepare("DELETE FROM users WHERE email = ?");
    $stmt->execute([$test_email]);
    
    // Create test user
    $password = password_hash('test123', PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("
        INSERT INTO users (email, password, role, status, first_name, last_name, email_verified) 
        VALUES (?, ?, 'user', 'active', 'Test', 'Creation', 1)
    ");
    $stmt->execute([$test_email, $password]);
    
    $test_id = $conn->lastInsertId();
    echo "✅ Created test user with ID: $test_id<br>";
    
    // Verify the user
    $stmt = $conn->prepare("SELECT id, email, role, status FROM users WHERE id = ?");
    $stmt->execute([$test_id]);
    $test_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($test_user) {
        echo "✅ Test user verified: {$test_user['email']} (Role: {$test_user['role']})<br>";
        
        // Clean up test user
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$test_id]);
        echo "✅ Cleaned up test user<br>";
    } else {
        echo "❌ Test user not found after creation<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "</body></html>";
?>
