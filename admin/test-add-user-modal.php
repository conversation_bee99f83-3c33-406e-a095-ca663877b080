<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

// Define user roles and statuses for the form
$user_roles = [
    'admin' => 'Administrator',
    'site_moderator' => 'Site Moderator',
    'business_owner' => 'Business Owner',
    'business_manager' => 'Business Manager',
    'user' => 'User'
];

$user_statuses = [
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',
    'pending' => 'Pending'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add User Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Add User Modal</h2>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i>Add User
        </button>
        
        <div id="console-log" class="mt-3">
            <h4>Console Log:</h4>
            <div id="log-output" style="background: #f8f9fa; padding: 10px; border: 1px solid #ddd; height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addUserForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username"
                                           placeholder="Optional - will use email if empty">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <?php foreach ($user_roles as $role_key => $role_name): ?>
                                            <option value="<?php echo $role_key; ?>"><?php echo $role_name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <?php foreach ($user_statuses as $status_key => $status_name): ?>
                                            <option value="<?php echo $status_key; ?>" <?php echo $status_key === 'active' ? 'selected' : ''; ?>>
                                                <?php echo $status_name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password *</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Custom console.log to display on page
        const originalLog = console.log;
        const originalError = console.error;
        const logOutput = document.getElementById('log-output');
        
        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : 'black';
            logOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        // Form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up form listeners');
            
            const addUserForm = document.getElementById('addUserForm');
            if (addUserForm) {
                console.log('Add user form found, attaching listener');
                addUserForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log('Add user form submitted');

                    const formData = new FormData(this);
                    const data = Object.fromEntries(formData);
                    console.log('Form data:', JSON.stringify(data));

                    if (data.password !== data.confirm_password) {
                        console.error('Passwords do not match');
                        alert('Passwords do not match');
                        return;
                    }

                    console.log('Sending request to user-api.php');
                    fetch('user-api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'create',
                            ...data
                        })
                    })
                    .then(response => {
                        console.log('Response received:', response.status, response.statusText);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', JSON.stringify(data));
                        if (data.success) {
                            alert('User created successfully!');
                            location.reload();
                        } else {
                            alert('Error creating user: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error.message);
                        alert('Error creating user: ' + error.message);
                    });
                });
            } else {
                console.error('Add user form not found!');
            }
        });
    </script>
</body>
</html>
