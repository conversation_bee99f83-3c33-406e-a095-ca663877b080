<?php
/**
 * Check User Table Structure
 * Analyze existing user table and fix user management system
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

echo "<!DOCTYPE html><html><head><title>Check User Table</title></head><body>";
echo "<h1>🔍 Checking User Table Structure...</h1>";
echo "<div style='font-family: monospace; background: #000; color: #0f0; padding: 20px;'>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check if users table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "✅ Users table exists<br><br>";
        
        // Check table structure
        $stmt = $conn->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📋 Current table structure:<br>";
        $columnNames = [];
        foreach ($columns as $column) {
            $columnNames[] = $column['Field'];
            echo "- {$column['Field']} ({$column['Type']}) " . 
                 ($column['Null'] === 'NO' ? 'NOT NULL' : 'NULL') . 
                 ($column['Key'] ? " {$column['Key']}" : '') . 
                 ($column['Default'] ? " DEFAULT {$column['Default']}" : '') . "<br>";
        }
        
        echo "<br>📊 Available columns: " . implode(', ', $columnNames) . "<br>";
        
        // Check current user count
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetchColumn();
        echo "👥 Current user count: $count<br>";
        
        // Show sample data
        if ($count > 0) {
            $stmt = $conn->query("SELECT * FROM users LIMIT 3");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<br>📄 Sample user data:<br>";
            foreach ($users as $i => $user) {
                echo "User " . ($i + 1) . ":<br>";
                foreach ($user as $field => $value) {
                    echo "  $field: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "<br>";
                }
                echo "<br>";
            }
        }
        
        // Suggest fixes based on actual structure
        echo "<br>🔧 Suggested fixes:<br>";
        
        if (!in_array('username', $columnNames)) {
            echo "❌ Missing 'username' column<br>";
            if (in_array('email', $columnNames)) {
                echo "✅ Can use 'email' as primary identifier<br>";
            }
        }
        
        if (!in_array('role', $columnNames)) {
            echo "❌ Missing 'role' column<br>";
            echo "💡 Need to add role column or use existing field<br>";
        }
        
        if (!in_array('status', $columnNames)) {
            echo "❌ Missing 'status' column<br>";
            echo "💡 Need to add status column or use existing field<br>";
        }
        
        // Check for alternative fields
        $possibleUserFields = ['name', 'full_name', 'display_name', 'first_name', 'last_name'];
        $foundUserFields = array_intersect($possibleUserFields, $columnNames);
        if (!empty($foundUserFields)) {
            echo "✅ Found name fields: " . implode(', ', $foundUserFields) . "<br>";
        }
        
        $possibleRoleFields = ['user_type', 'account_type', 'level', 'permissions'];
        $foundRoleFields = array_intersect($possibleRoleFields, $columnNames);
        if (!empty($foundRoleFields)) {
            echo "✅ Found possible role fields: " . implode(', ', $foundRoleFields) . "<br>";
        }
        
        $possibleStatusFields = ['active', 'enabled', 'verified', 'is_active'];
        $foundStatusFields = array_intersect($possibleStatusFields, $columnNames);
        if (!empty($foundStatusFields)) {
            echo "✅ Found possible status fields: " . implode(', ', $foundStatusFields) . "<br>";
        }
        
    } else {
        echo "❌ Users table does not exist<br>";
        echo "💡 Need to create users table first<br>";
        
        // Suggest creating table
        echo "<br>🔧 Creating users table...<br>";
        
        $createSQL = "
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            role ENUM('admin', 'site_moderator', 'business_owner', 'business_manager', 'user') DEFAULT 'user',
            status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'active',
            email_verified TINYINT(1) DEFAULT 0,
            phone VARCHAR(20),
            city VARCHAR(100),
            state VARCHAR(50),
            country VARCHAR(100),
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($createSQL);
        echo "✅ Users table created successfully<br>";
        
        // Create default admin user
        $adminEmail = '<EMAIL>';
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO users (email, password, first_name, last_name, role, status, email_verified) 
            VALUES (?, ?, 'Admin', 'User', 'admin', 'active', 1)
        ");
        $stmt->execute([$adminEmail, $adminPassword]);
        
        echo "✅ Default admin user created<br>";
        echo "📧 Email: $adminEmail<br>";
        echo "🔑 Password: admin123<br>";
    }
    
} catch (Exception $e) {
    echo "💥 ERROR: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br>";
    echo nl2br($e->getTraceAsString());
}

echo "</div>";
echo "<br><a href='user-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to User Management</a>";
echo "<br><br><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</body></html>";
?>
