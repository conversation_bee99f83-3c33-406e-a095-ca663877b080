<?php
/**
 * PHP Information Page
 * Use this to verify PHP configuration in XAMPP
 * Access via: http://localhost/beersty/phpinfo.php
 * 
 * SECURITY NOTE: Remove this file in production!
 */

// Only allow access in development environment
if (!isset($_ENV['APP_ENV']) || $_ENV['APP_ENV'] !== 'development') {
    // Check if we're likely in a development environment
    $isDevelopment = (
        $_SERVER['SERVER_NAME'] === 'localhost' || 
        $_SERVER['SERVER_NAME'] === '127.0.0.1' ||
        strpos($_SERVER['SERVER_NAME'], '.local') !== false
    );
    
    if (!$isDevelopment) {
        http_response_code(404);
        exit('Not found');
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP Info - Beersty</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .info-section { margin-bottom: 2rem; }
        .status-good { color: #198754; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 0.375rem; }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-info-circle me-2"></i>PHP Configuration Info</h1>
                    <a href="/beersty/" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to App
                    </a>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Security Notice:</strong> This page should only be accessible in development. 
                    Remove this file before deploying to production!
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="card info-section">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-server me-2"></i>Server Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>PHP Version:</strong></td>
                                <td class="<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'status-good' : 'status-error'; ?>">
                                    <?php echo PHP_VERSION; ?>
                                    <?php if (version_compare(PHP_VERSION, '8.0.0', '>=')): ?>
                                        <i class="fas fa-check-circle ms-1"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle ms-1"></i>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Server Software:</strong></td>
                                <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Document Root:</strong></td>
                                <td><code><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Current Directory:</strong></td>
                                <td><code><?php echo __DIR__; ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Server Name:</strong></td>
                                <td><?php echo $_SERVER['SERVER_NAME'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Server Port:</strong></td>
                                <td><?php echo $_SERVER['SERVER_PORT'] ?? 'Unknown'; ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="card info-section">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-database me-2"></i>Database Extensions</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>PDO:</strong></td>
                                <td class="<?php echo extension_loaded('pdo') ? 'status-good' : 'status-error'; ?>">
                                    <?php echo extension_loaded('pdo') ? 'Enabled' : 'Disabled'; ?>
                                    <i class="fas fa-<?php echo extension_loaded('pdo') ? 'check' : 'times'; ?>-circle ms-1"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PDO MySQL:</strong></td>
                                <td class="<?php echo extension_loaded('pdo_mysql') ? 'status-good' : 'status-error'; ?>">
                                    <?php echo extension_loaded('pdo_mysql') ? 'Enabled' : 'Disabled'; ?>
                                    <i class="fas fa-<?php echo extension_loaded('pdo_mysql') ? 'check' : 'times'; ?>-circle ms-1"></i>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>MySQLi:</strong></td>
                                <td class="<?php echo extension_loaded('mysqli') ? 'status-good' : 'status-warning'; ?>">
                                    <?php echo extension_loaded('mysqli') ? 'Enabled' : 'Disabled'; ?>
                                    <i class="fas fa-<?php echo extension_loaded('mysqli') ? 'check' : 'exclamation'; ?>-circle ms-1"></i>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card info-section">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>PHP Configuration</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Memory Limit:</strong></td>
                                <td><?php echo ini_get('memory_limit'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Max Execution Time:</strong></td>
                                <td><?php echo ini_get('max_execution_time'); ?>s</td>
                            </tr>
                            <tr>
                                <td><strong>Upload Max Filesize:</strong></td>
                                <td><?php echo ini_get('upload_max_filesize'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Post Max Size:</strong></td>
                                <td><?php echo ini_get('post_max_size'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Display Errors:</strong></td>
                                <td class="<?php echo ini_get('display_errors') ? 'status-warning' : 'status-good'; ?>">
                                    <?php echo ini_get('display_errors') ? 'On' : 'Off'; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Error Reporting:</strong></td>
                                <td><?php echo error_reporting(); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="card info-section">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-puzzle-piece me-2"></i>Required Extensions</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $required_extensions = [
                            'pdo' => 'PDO (Database)',
                            'pdo_mysql' => 'PDO MySQL Driver',
                            'session' => 'Session Support',
                            'json' => 'JSON Support',
                            'mbstring' => 'Multibyte String',
                            'openssl' => 'OpenSSL',
                            'fileinfo' => 'File Info',
                            'gd' => 'GD (Image Processing)'
                        ];
                        ?>
                        <table class="table table-sm">
                            <?php foreach ($required_extensions as $ext => $name): ?>
                                <tr>
                                    <td><strong><?php echo $name; ?>:</strong></td>
                                    <td class="<?php echo extension_loaded($ext) ? 'status-good' : 'status-error'; ?>">
                                        <?php echo extension_loaded($ext) ? 'Enabled' : 'Disabled'; ?>
                                        <i class="fas fa-<?php echo extension_loaded($ext) ? 'check' : 'times'; ?>-circle ms-1"></i>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-folder me-2"></i>File Permissions Check</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $directories_to_check = [
                            'config' => 'Configuration Directory',
                            'uploads' => 'Uploads Directory',
                            'assets' => 'Assets Directory'
                        ];
                        ?>
                        <table class="table table-sm">
                            <?php foreach ($directories_to_check as $dir => $name): ?>
                                <?php
                                $path = __DIR__ . '/' . $dir;
                                $exists = is_dir($path);
                                $writable = $exists ? is_writable($path) : false;
                                ?>
                                <tr>
                                    <td><strong><?php echo $name; ?>:</strong></td>
                                    <td>
                                        <?php if ($exists): ?>
                                            <span class="status-good">
                                                <i class="fas fa-check-circle me-1"></i>Exists
                                            </span>
                                            <?php if ($writable): ?>
                                                <span class="status-good ms-2">
                                                    <i class="fas fa-edit me-1"></i>Writable
                                                </span>
                                            <?php else: ?>
                                                <span class="status-warning ms-2">
                                                    <i class="fas fa-lock me-1"></i>Read-only
                                                </span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="status-error">
                                                <i class="fas fa-times-circle me-1"></i>Missing
                                            </span>
                                        <?php endif; ?>
                                        <code class="ms-2"><?php echo $path; ?></code>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Full PHP Info</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Complete PHP configuration details:</p>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <?php phpinfo(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
