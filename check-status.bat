@echo off
echo CHECKING STATUS AFTER RESTART
echo ==============================

echo.
echo 1. Checking running processes:
echo Apache processes:
tasklist | findstr httpd
echo MySQL processes:
tasklist | findstr mysqld

echo.
echo 2. Checking port usage:
echo Port 80:
netstat -an | findstr ":80"
echo Port 3306:
netstat -an | findstr ":3306"

echo.
echo 3. Testing localhost...
echo Opening test page...
start "" "http://localhost"
timeout /t 3 /nobreak >nul

echo Opening project...
start "" "http://localhost/beersty-lovable"
timeout /t 2 /nobreak >nul

echo Opening PDO test...
start "" "http://localhost/beersty-lovable/test-pdo-simple.php"

echo.
echo If pages opened successfully, everything is working!
echo If not, check XAMPP Control Panel.
echo.
pause
