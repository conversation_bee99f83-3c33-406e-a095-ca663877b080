# BEERSTY DEVELOPMENT ENVIRONMENT STARTER
# The definitive script to start your development environment every time
# Version 1.0 - Tested and reliable

param(
    [switch]$Force,
    [switch]$SkipPDO,
    [switch]$Port8080
)

Write-Host "🍺 BEERSTY DEVELOPMENT ENVIRONMENT STARTER" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Yellow
Write-Host "The reliable way to start your dev environment" -ForegroundColor Green
Write-Host ""

# Configuration
$ProjectName = "beersty-lovable"
$DatabaseName = "beersty_db"

# Step 1: Find XAMPP Installation
Write-Host "STEP 1: Locating XAMPP..." -ForegroundColor Cyan
$XamppPaths = @(
    "C:\xampp",
    "C:\Program Files\XAMPP", 
    "C:\Program Files (x86)\XAMPP",
    "D:\xampp",
    "E:\xampp"
)

$XamppPath = $null
foreach ($path in $XamppPaths) {
    if (Test-Path $path) {
        $XamppPath = $path
        Write-Host "✅ Found XAMPP at: $XamppPath" -ForegroundColor Green
        break
    }
}

if (-not $XamppPath) {
    Write-Host "❌ XAMPP not found!" -ForegroundColor Red
    Write-Host "Please install XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Clean Stop All Processes
Write-Host ""
Write-Host "STEP 2: Stopping existing processes..." -ForegroundColor Cyan

$ProcessesToStop = @("httpd", "mysqld", "apache", "mysql", "xampp-control")
foreach ($process in $ProcessesToStop) {
    $running = Get-Process -Name $process -ErrorAction SilentlyContinue
    if ($running) {
        Write-Host "Stopping $process..." -ForegroundColor Yellow
        $running | Stop-Process -Force
    }
}

# Stop IIS if running
try {
    Stop-Service -Name "W3SVC" -Force -ErrorAction SilentlyContinue
    Write-Host "Stopped IIS" -ForegroundColor Yellow
} catch {
    # IIS not running, that's fine
}

Start-Sleep 3
Write-Host "✅ All processes stopped" -ForegroundColor Green

# Step 3: Configure PDO (unless skipped)
if (-not $SkipPDO) {
    Write-Host ""
    Write-Host "STEP 3: Configuring PDO MySQL..." -ForegroundColor Cyan
    
    $PhpIniPath = Join-Path $XamppPath "php\php.ini"
    if (Test-Path $PhpIniPath) {
        # Create backup
        $BackupPath = $PhpIniPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
        Copy-Item $PhpIniPath $BackupPath -ErrorAction SilentlyContinue
        
        # Read and modify php.ini
        $Content = Get-Content $PhpIniPath
        $Modified = $false
        
        for ($i = 0; $i -lt $Content.Length; $i++) {
            $line = $Content[$i]
            
            # Enable PDO extensions
            if ($line -match "^;\s*extension=pdo_mysql") {
                $Content[$i] = "extension=pdo_mysql"
                Write-Host "✅ Enabled pdo_mysql" -ForegroundColor Green
                $Modified = $true
            }
            elseif ($line -match "^;\s*extension=mysqli") {
                $Content[$i] = "extension=mysqli"
                Write-Host "✅ Enabled mysqli" -ForegroundColor Green
                $Modified = $true
            }
        }
        
        if ($Modified) {
            $Content | Set-Content $PhpIniPath -Encoding UTF8
            Write-Host "✅ PDO configuration updated" -ForegroundColor Green
        } else {
            Write-Host "✅ PDO already configured" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️ php.ini not found at: $PhpIniPath" -ForegroundColor Yellow
    }
}

# Step 4: Configure Apache Port (if requested)
if ($Port8080) {
    Write-Host ""
    Write-Host "STEP 4: Configuring Apache for port 8080..." -ForegroundColor Cyan
    
    $HttpdConf = Join-Path $XamppPath "apache\conf\httpd.conf"
    if (Test-Path $HttpdConf) {
        $BackupConf = $HttpdConf + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
        Copy-Item $HttpdConf $BackupConf -ErrorAction SilentlyContinue
        
        $Content = Get-Content $HttpdConf
        for ($i = 0; $i -lt $Content.Length; $i++) {
            if ($Content[$i] -match "^Listen 80$") {
                $Content[$i] = "Listen 8080"
                Write-Host "✅ Changed Apache to port 8080" -ForegroundColor Green
            }
        }
        $Content | Set-Content $HttpdConf -Encoding UTF8
    }
}

# Step 5: Start XAMPP Control Panel
Write-Host ""
Write-Host "STEP 5: Starting XAMPP Control Panel..." -ForegroundColor Cyan

$XamppControl = Join-Path $XamppPath "xampp-control.exe"
if (Test-Path $XamppControl) {
    Start-Process $XamppControl -WindowStyle Normal
    Start-Sleep 3
    Write-Host "✅ XAMPP Control Panel started" -ForegroundColor Green
} else {
    Write-Host "❌ XAMPP Control Panel not found" -ForegroundColor Red
    exit 1
}

# Step 6: Wait for manual service start
Write-Host ""
Write-Host "STEP 6: Start Services Manually" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Yellow
Write-Host "In the XAMPP Control Panel that just opened:" -ForegroundColor White
Write-Host "1. Click 'Start' next to Apache" -ForegroundColor White
Write-Host "2. Click 'Start' next to MySQL" -ForegroundColor White
Write-Host "3. Wait for both to show 'Running' status" -ForegroundColor White
Write-Host ""
Write-Host "If Apache shows an error:" -ForegroundColor Yellow
Write-Host "- Click 'Logs' to see the error message" -ForegroundColor White
Write-Host "- Common issue: Port 80 in use (try -Port8080 parameter)" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter AFTER both Apache and MySQL are running"

# Step 7: Test Services
Write-Host ""
Write-Host "STEP 7: Testing services..." -ForegroundColor Cyan

$BaseUrl = if ($Port8080) { "http://localhost:8080" } else { "http://localhost" }

# Test web server
Write-Host "Testing web server..." -ForegroundColor Yellow
try {
    $Response = Invoke-WebRequest -Uri $BaseUrl -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ Web server responding (Status: $($Response.StatusCode))" -ForegroundColor Green
    $WebServerOK = $true
} catch {
    Write-Host "❌ Web server not responding" -ForegroundColor Red
    Write-Host "Check XAMPP Control Panel - is Apache running?" -ForegroundColor Yellow
    $WebServerOK = $false
}

# Test MySQL
Write-Host "Testing MySQL..." -ForegroundColor Yellow
$MysqlClient = Join-Path $XamppPath "mysql\bin\mysql.exe"
if (Test-Path $MysqlClient) {
    try {
        $Result = & $MysqlClient -u root -e "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ MySQL connection successful" -ForegroundColor Green
            $MySQLOK = $true
        } else {
            Write-Host "❌ MySQL connection failed" -ForegroundColor Red
            $MySQLOK = $false
        }
    } catch {
        Write-Host "❌ MySQL test failed" -ForegroundColor Red
        $MySQLOK = $false
    }
} else {
    Write-Host "⚠️ MySQL client not found" -ForegroundColor Yellow
    $MySQLOK = $false
}

# Test project
if ($WebServerOK) {
    Write-Host "Testing project..." -ForegroundColor Yellow
    $ProjectUrl = "$BaseUrl/$ProjectName"
    try {
        $Response = Invoke-WebRequest -Uri $ProjectUrl -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ Project accessible at $ProjectUrl" -ForegroundColor Green
        $ProjectOK = $true
    } catch {
        Write-Host "⚠️ Project not accessible at $ProjectUrl" -ForegroundColor Yellow
        Write-Host "Make sure project files are in htdocs/$ProjectName" -ForegroundColor Yellow
        $ProjectOK = $false
    }
}

# Step 8: Open URLs
if ($WebServerOK) {
    Write-Host ""
    Write-Host "STEP 8: Opening development URLs..." -ForegroundColor Cyan
    
    $URLs = @(
        $BaseUrl,
        "$BaseUrl/$ProjectName",
        "$BaseUrl/$ProjectName/test-pdo-simple.php",
        "$BaseUrl/phpmyadmin"
    )
    
    if ($ProjectOK) {
        $URLs += "$BaseUrl/$ProjectName/admin/user-management.php"
    }
    
    foreach ($url in $URLs) {
        Write-Host "Opening: $url" -ForegroundColor White
        Start-Process $url
        Start-Sleep 1
    }
}

# Step 9: Summary
Write-Host ""
Write-Host "🎉 BEERSTY DEVELOPMENT ENVIRONMENT READY!" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

if ($WebServerOK -and $MySQLOK) {
    Write-Host "✅ All services are running successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Your URLs:" -ForegroundColor Cyan
    Write-Host "  Main Site: $BaseUrl/$ProjectName" -ForegroundColor White
    Write-Host "  User Management: $BaseUrl/$ProjectName/admin/user-management.php" -ForegroundColor White
    Write-Host "  PDO Test: $BaseUrl/$ProjectName/test-pdo-simple.php" -ForegroundColor White
    Write-Host "  phpMyAdmin: $BaseUrl/phpmyadmin" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 To test ADD USER functionality:" -ForegroundColor Yellow
    Write-Host "1. Go to User Management page" -ForegroundColor White
    Write-Host "2. Click 'Add User' button" -ForegroundColor White
    Write-Host "3. Fill in the form and submit" -ForegroundColor White
} else {
    Write-Host "⚠️ Some services may not be running properly" -ForegroundColor Yellow
    Write-Host "Check the XAMPP Control Panel for any error messages" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "💡 Usage for next time:" -ForegroundColor Cyan
Write-Host "  Normal start: .\beersty-dev-start.ps1" -ForegroundColor White
Write-Host "  Use port 8080: .\beersty-dev-start.ps1 -Port8080" -ForegroundColor White
Write-Host "  Skip PDO config: .\beersty-dev-start.ps1 -SkipPDO" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
