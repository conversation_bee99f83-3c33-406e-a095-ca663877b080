@echo off
echo Copying Beersty files to XAMPP...

REM Check if XAMPP exists
if not exist "C:\xampp\htdocs" (
    echo ERROR: XAMPP htdocs directory not found!
    echo Please make sure XAMPP is installed at C:\xampp\
    pause
    exit /b 1
)

REM Create beersty directory
if not exist "C:\xampp\htdocs\beersty" (
    mkdir "C:\xampp\htdocs\beersty"
    echo Created directory: C:\xampp\htdocs\beersty
)

REM Copy all files
echo Copying files...
copy "*.php" "C:\xampp\htdocs\beersty\" /Y
copy "*.html" "C:\xampp\htdocs\beersty\" /Y
copy ".htaccess" "C:\xampp\htdocs\beersty\" /Y

REM Copy directories
xcopy "config" "C:\xampp\htdocs\beersty\config\" /E /I /Y
xcopy "includes" "C:\xampp\htdocs\beersty\includes\" /E /I /Y
xcopy "assets" "C:\xampp\htdocs\beersty\assets\" /E /I /Y
xcopy "auth" "C:\xampp\htdocs\beersty\auth\" /E /I /Y
xcopy "admin" "C:\xampp\htdocs\beersty\admin\" /E /I /Y
xcopy "brewery" "C:\xampp\htdocs\beersty\brewery\" /E /I /Y
xcopy "breweries" "C:\xampp\htdocs\beersty\breweries\" /E /I /Y
xcopy "database" "C:\xampp\htdocs\beersty\database\" /E /I /Y
xcopy "uploads" "C:\xampp\htdocs\beersty\uploads\" /E /I /Y

echo.
echo Files copied successfully!
echo.
echo You can now access the application at:
echo http://localhost/beersty/
echo.
echo Test pages:
echo http://localhost/beersty/simple-test.php
echo http://localhost/beersty/debug.php
echo.
pause
