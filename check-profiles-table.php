<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=beersty_db', 'root', '');
    
    echo "=== Profiles Table Structure ===" . PHP_EOL;
    $stmt = $pdo->query('DESCRIBE profiles');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . PHP_EOL;
    }
    
    echo "\n=== Current Profiles ===" . PHP_EOL;
    $stmt = $pdo->query('SELECT * FROM profiles LIMIT 5');
    $profiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($profiles)) {
        echo "No profiles found" . PHP_EOL;
    } else {
        foreach ($profiles as $profile) {
            echo "ID: " . $profile['id'] . " | Email: " . $profile['email'] . " | Role: " . $profile['role'] . PHP_EOL;
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
