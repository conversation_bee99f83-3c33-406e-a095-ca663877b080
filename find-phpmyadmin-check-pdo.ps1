# FIND PHPMYADMIN AND VERIFY PDO MYSQL

Write-Host "FINDING PHPMYADMIN AND CHECKING PDO MYSQL" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Yellow

# 1. Find phpMyAdmin
Write-Host ""
Write-Host "1. SEARCHING FOR PHPMYADMIN:" -ForegroundColor Cyan

$phpMyAdminPaths = @(
    "C:\xampp\phpMyAdmin",
    "C:\xampp\phpmyadmin", 
    "C:\xampp\htdocs\phpMyAdmin",
    "C:\xampp\htdocs\phpmyadmin"
)

$foundPhpMyAdmin = $null
foreach ($path in $phpMyAdminPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Found phpMyAdmin at: $path" -ForegroundColor Green
        $foundPhpMyAdmin = $path
        break
    } else {
        Write-Host "❌ Not found: $path" -ForegroundColor Red
    }
}

if ($foundPhpMyAdmin) {
    # Get the folder name for URL
    $folderName = Split-Path $foundPhpMyAdmin -Leaf
    $phpMyAdminUrl = "http://localhost:8000/$folderName"
    Write-Host "phpMyAdmin URL should be: $phpMyAdminUrl" -ForegroundColor Yellow
} else {
    Write-Host "❌ phpMyAdmin not found in any common location!" -ForegroundColor Red
}

# 2. Check PDO MySQL extension
Write-Host ""
Write-Host "2. CHECKING PDO MYSQL EXTENSION:" -ForegroundColor Cyan

# Check php.ini
$phpIni = "C:\xampp\php\php.ini"
if (Test-Path $phpIni) {
    Write-Host "Checking php.ini: $phpIni" -ForegroundColor Gray
    
    $content = Get-Content $phpIni
    $pdoMysqlEnabled = $false
    $mysqliEnabled = $false
    
    foreach ($line in $content) {
        if ($line -match "^extension=pdo_mysql") {
            Write-Host "✅ PDO MySQL: ENABLED" -ForegroundColor Green
            $pdoMysqlEnabled = $true
        }
        if ($line -match "^extension=mysqli") {
            Write-Host "✅ MySQLi: ENABLED" -ForegroundColor Green
            $mysqliEnabled = $true
        }
    }
    
    if (-not $pdoMysqlEnabled) {
        Write-Host "❌ PDO MySQL: NOT ENABLED" -ForegroundColor Red
        Write-Host "Enabling PDO MySQL..." -ForegroundColor Yellow
        
        # Enable PDO MySQL
        for ($i = 0; $i -lt $content.Length; $i++) {
            if ($content[$i] -match "^;\s*extension=pdo_mysql") {
                $content[$i] = "extension=pdo_mysql"
                Write-Host "✅ Enabled pdo_mysql" -ForegroundColor Green
            }
            if ($content[$i] -match "^;\s*extension=mysqli") {
                $content[$i] = "extension=mysqli"
                Write-Host "✅ Enabled mysqli" -ForegroundColor Green
            }
        }
        
        # Save changes
        $content | Set-Content $phpIni -Encoding UTF8
        Write-Host "✅ php.ini updated - Apache restart required" -ForegroundColor Green
        
        # Restart Apache
        Write-Host "Restarting Apache..." -ForegroundColor Cyan
        Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep 3
        Start-Process "C:\xampp\apache\bin\httpd.exe" -WindowStyle Hidden
        Start-Sleep 5
        Write-Host "✅ Apache restarted" -ForegroundColor Green
    }
} else {
    Write-Host "❌ php.ini not found!" -ForegroundColor Red
}

# 3. Create PDO test file
Write-Host ""
Write-Host "3. CREATING PDO TEST FILE:" -ForegroundColor Cyan

$pdoTestContent = @'
<?php
echo "<h1>PDO MySQL Extension Test</h1>";

echo "<h2>Extension Status:</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";
echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";
echo "<p>MySQLi: " . (extension_loaded('mysqli') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";

echo "<h2>PHP Version:</h2>";
echo "<p>" . phpversion() . "</p>";

echo "<h2>Configuration File:</h2>";
echo "<p>" . php_ini_loaded_file() . "</p>";

echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color:green'>✅ PDO MySQL connection successful</p>";
    
    // Test database
    try {
        $pdo->exec("USE beersty_db");
        echo "<p style='color:green'>✅ Connected to beersty_db database</p>";
        
        // Test users table
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color:green'>✅ Users table exists</p>";
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            echo "<p>Users in database: <strong>" . $result['count'] . "</strong></p>";
        } else {
            echo "<p style='color:orange'>⚠️ Users table does not exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color:orange'>⚠️ Database 'beersty_db' not found: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color:red'>❌ PDO MySQL connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>All Loaded Extensions:</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<div style='max-height:200px; overflow-y:auto; border:1px solid #ccc; padding:10px;'>";
foreach ($extensions as $ext) {
    $color = (strpos($ext, 'pdo') !== false || strpos($ext, 'mysql') !== false) ? 'color:green; font-weight:bold;' : '';
    echo "<div style='$color'>$ext</div>";
}
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
'@

$pdoTestFile = "pdo-mysql-test.php"
$pdoTestContent | Set-Content $pdoTestFile -Encoding UTF8
Write-Host "✅ Created PDO test file: $pdoTestFile" -ForegroundColor Green

# 4. Test URLs
Write-Host ""
Write-Host "4. TESTING URLS:" -ForegroundColor Cyan

# Test PDO
$pdoTestUrl = "http://localhost:8000/beersty/$pdoTestFile"
Write-Host "Testing PDO: $pdoTestUrl" -ForegroundColor Gray
try {
    $response = Invoke-WebRequest -Uri $pdoTestUrl -TimeoutSec 5 -UseBasicParsing
    Write-Host "✅ PDO test accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ PDO test failed" -ForegroundColor Red
}

# Test phpMyAdmin
if ($foundPhpMyAdmin) {
    Write-Host "Testing phpMyAdmin: $phpMyAdminUrl" -ForegroundColor Gray
    try {
        $response = Invoke-WebRequest -Uri $phpMyAdminUrl -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ phpMyAdmin accessible" -ForegroundColor Green
    } catch {
        Write-Host "❌ phpMyAdmin failed" -ForegroundColor Red
    }
}

# 5. Open URLs
Write-Host ""
Write-Host "5. OPENING URLS:" -ForegroundColor Cyan

Write-Host "Opening PDO test..." -ForegroundColor Gray
Start-Process $pdoTestUrl

if ($foundPhpMyAdmin) {
    Write-Host "Opening phpMyAdmin..." -ForegroundColor Gray
    Start-Process $phpMyAdminUrl
}

Write-Host "Opening admin bypass..." -ForegroundColor Gray
Start-Process "http://localhost:8000/beersty/test-admin-bypass.php"

# 6. Summary
Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Green
Write-Host "========" -ForegroundColor Green

Write-Host ""
Write-Host "YOUR URLS:" -ForegroundColor Yellow
Write-Host "  PDO Test: $pdoTestUrl" -ForegroundColor White
if ($foundPhpMyAdmin) {
    Write-Host "  phpMyAdmin: $phpMyAdminUrl" -ForegroundColor White
}
Write-Host "  Admin Bypass: http://localhost:8000/beersty/test-admin-bypass.php" -ForegroundColor White
Write-Host "  User Management: http://localhost:8000/beersty/admin/user-management.php" -ForegroundColor White

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Check PDO test page - should show all green checkmarks" -ForegroundColor White
Write-Host "2. Use phpMyAdmin to manage database" -ForegroundColor White
Write-Host "3. Test ADD USER functionality" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to continue"
