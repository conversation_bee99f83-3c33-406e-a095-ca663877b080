<?php
require_once 'config/config.php';

echo "<h1>🔧 Fixing Brewery Demo Account</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>📋 Step 1: Checking Current State</h2>";
    
    // Check if brewery demo user exists
    $stmt = $conn->prepare("SELECT u.id, u.email, p.role, p.brewery_id FROM users u JOIN profiles p ON u.id = p.id WHERE u.email = '<EMAIL>'");
    $stmt->execute();
    $breweryUser = $stmt->fetch();
    
    if ($breweryUser) {
        echo "<p>✅ Found brewery demo user: {$breweryUser['email']}</p>";
        echo "<p>📊 Current brewery_id: " . ($breweryUser['brewery_id'] ?: 'NULL') . "</p>";
        echo "<p>📊 Role: {$breweryUser['role']}</p>";
    } else {
        echo "<p>❌ Brewery demo user not found!</p>";
        exit;
    }
    
    // Check if Demo Brewery exists
    $stmt = $conn->prepare("SELECT id, name FROM breweries WHERE name = 'Demo Brewery'");
    $stmt->execute();
    $demoBrewery = $stmt->fetch();
    
    if ($demoBrewery) {
        echo "<p>✅ Found Demo Brewery: {$demoBrewery['name']} (ID: {$demoBrewery['id']})</p>";
    } else {
        echo "<p>❌ Demo Brewery not found! Creating it...</p>";
        
        // Create Demo Brewery
        $stmt = $conn->prepare("INSERT INTO breweries (name, city, state, brewery_type, description, claimed, verified) VALUES (?, ?, ?, ?, ?, 1, 1)");
        $stmt->execute([
            'Demo Brewery',
            'Demo City',
            'CA',
            'micro',
            'A demo brewery for testing the Beersty platform features.'
        ]);
        
        $demoBreweryId = $conn->lastInsertId();
        echo "<p>✅ Created Demo Brewery with ID: $demoBreweryId</p>";
        
        // Get the created brewery
        $stmt = $conn->prepare("SELECT id, name FROM breweries WHERE id = ?");
        $stmt->execute([$demoBreweryId]);
        $demoBrewery = $stmt->fetch();
    }
    
    echo "<h2>🔗 Step 2: Linking Brewery Demo User to Demo Brewery</h2>";
    
    // Update the brewery_id for the demo user
    $stmt = $conn->prepare("UPDATE profiles SET brewery_id = ? WHERE id = ?");
    $stmt->execute([$demoBrewery['id'], $breweryUser['id']]);
    
    echo "<p>✅ Updated brewery_<NAME_EMAIL> to {$demoBrewery['id']}</p>";
    
    echo "<h2>🧪 Step 3: Verifying the Fix</h2>";
    
    // Verify the update
    $stmt = $conn->prepare("
        SELECT u.id, u.email, p.role, p.brewery_id, b.name as brewery_name 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        LEFT JOIN breweries b ON p.brewery_id = b.id 
        WHERE u.email = '<EMAIL>'
    ");
    $stmt->execute();
    $verifyUser = $stmt->fetch();
    
    if ($verifyUser && $verifyUser['brewery_id']) {
        echo "<p>✅ Verification successful!</p>";
        echo "<p>📊 User: {$verifyUser['email']}</p>";
        echo "<p>📊 Role: {$verifyUser['role']}</p>";
        echo "<p>📊 Brewery ID: {$verifyUser['brewery_id']}</p>";
        echo "<p>📊 Brewery Name: {$verifyUser['brewery_name']}</p>";
    } else {
        echo "<p>❌ Verification failed!</p>";
    }
    
    echo "<h2>🍺 Step 4: Ensuring Demo Brewery Has Sample Data</h2>";
    
    // Check if demo brewery has beer menu items
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu WHERE brewery_id = ?");
    $stmt->execute([$demoBrewery['id']]);
    $beerCount = $stmt->fetch()['count'];
    
    echo "<p>📊 Current beer menu items: $beerCount</p>";
    
    if ($beerCount == 0) {
        echo "<p>➕ Adding sample beer menu items...</p>";
        
        // Get some beer styles
        $stmt = $conn->prepare("SELECT id, name FROM beer_styles LIMIT 6");
        $stmt->execute();
        $styles = $stmt->fetchAll();
        
        if (!empty($styles)) {
            $sampleBeers = [
                ['Hoppy IPA', 'A bold and hoppy IPA with citrus notes and a crisp finish', 6.5, 65, 8, 7.00, 1, true],
                ['Smooth Lager', 'A crisp and refreshing lager perfect for any occasion', 4.8, 22, 3, 6.00, 2, true],
                ['Dark Stout', 'Rich and creamy stout with notes of chocolate and coffee', 7.2, 45, 35, 8.00, 3, false],
                ['Summer Wheat', 'Light and refreshing wheat beer with citrus hints', 5.2, 18, 4, 6.50, 4, true],
                ['Imperial Porter', 'Bold porter with roasted malt character', 8.5, 55, 28, 9.00, 5, true],
                ['Hazy Pale Ale', 'Juicy and hazy pale ale with tropical fruit flavors', 5.8, 35, 5, 7.50, 6, true]
            ];
            
            $stmt = $conn->prepare("INSERT INTO beer_menu (id, brewery_id, beer_style_id, name, description, abv, ibu, srm, price, tap_number, available) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            foreach ($sampleBeers as $index => $beer) {
                $styleId = $styles[$index % count($styles)]['id'] ?? null;
                $stmt->execute([
                    $demoBrewery['id'],
                    $styleId,
                    $beer[0], // name
                    $beer[1], // description
                    $beer[2], // abv
                    $beer[3], // ibu
                    $beer[4], // srm
                    $beer[5], // price
                    $beer[6], // tap_number
                    $beer[7]  // available
                ]);
                echo "<p>✅ Added beer: {$beer[0]}</p>";
            }
        }
    }
    
    // Check if demo brewery has food menu items
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM food_menu WHERE brewery_id = ?");
    $stmt->execute([$demoBrewery['id']]);
    $foodCount = $stmt->fetch()['count'];
    
    echo "<p>📊 Current food menu items: $foodCount</p>";
    
    if ($foodCount == 0) {
        echo "<p>➕ Adding sample food menu items...</p>";
        
        // Get food categories
        $stmt = $conn->prepare("SELECT id, name FROM food_categories");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        if (!empty($categories)) {
            $sampleFood = [
                ['Beer Cheese Dip', 'Creamy cheese dip made with our house beer', 8.99, 'Appetizers'],
                ['Brewery Burger', 'Juicy beef burger with bacon and cheese', 14.99, 'Entrees'],
                ['Fish & Chips', 'Beer-battered fish with crispy fries', 16.99, 'Entrees'],
                ['Caesar Salad', 'Fresh romaine with parmesan and croutons', 11.99, 'Salads'],
                ['Loaded Fries', 'Crispy fries with cheese, bacon, and sour cream', 9.99, 'Sides'],
                ['Chocolate Stout Cake', 'Rich chocolate cake made with our stout', 7.99, 'Desserts']
            ];
            
            $stmt = $conn->prepare("INSERT INTO food_menu (id, brewery_id, category_id, name, description, price) VALUES (UUID(), ?, ?, ?, ?, ?)");
            
            foreach ($sampleFood as $food) {
                $categoryId = null;
                foreach ($categories as $cat) {
                    if ($cat['name'] === $food[3]) {
                        $categoryId = $cat['id'];
                        break;
                    }
                }
                
                $stmt->execute([
                    $demoBrewery['id'],
                    $categoryId,
                    $food[0], // name
                    $food[1], // description
                    $food[2]  // price
                ]);
                echo "<p>✅ Added food item: {$food[0]}</p>";
            }
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Brewery Demo Account Fixed!</h3>";
    echo "<p><strong>Account:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> brewery123</p>";
    echo "<p><strong>Brewery:</strong> {$demoBrewery['name']} (ID: {$demoBrewery['id']})</p>";
    echo "<p><strong>Features Available:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Edit Brewery Profile</li>";
    echo "<li>✅ Manage Menu (Beer & Food)</li>";
    echo "<li>✅ Digital Board Admin</li>";
    echo "<li>✅ Menu Preview</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Fix Failed</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='color: #6F4C3E;'>🔐 Login as Brewery Demo</a></li>";
echo "<li><a href='brewery/profile.php' style='color: #6F4C3E;'>👤 Edit Brewery Profile</a></li>";
echo "<li><a href='brewery/menu.php' style='color: #6F4C3E;'>🍺 Manage Menu</a></li>";
echo "<li><a href='brewery/digital-board.php' style='color: #6F4C3E;'>📺 Digital Board Admin</a></li>";
echo "</ul>";
?>
