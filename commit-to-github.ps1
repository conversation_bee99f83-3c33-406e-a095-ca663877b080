# Commit Beersty Changes to GitHub
# PowerShell script to commit all changes and push to GitHub

param(
    [string]$CommitMessage = "Fix MySQL database connection and complete brewery management system"
)

Write-Host "🚀 Committing Beersty Changes to GitHub" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set working directory
$ProjectPath = "C:\xkinteractive-github\beersty-lovable"
Set-Location $ProjectPath

Write-Host "📁 Project Directory: $ProjectPath" -ForegroundColor Cyan
Write-Host "💬 Commit Message: $CommitMessage" -ForegroundColor Cyan

# Check if Git is available
try {
    $gitVersion = git --version 2>$null
    if ($gitVersion) {
        Write-Host "✅ Git is available: $gitVersion" -ForegroundColor Green
    } else {
        throw "Git not found"
    }
}
catch {
    Write-Host "❌ Git is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Git and add it to your PATH" -ForegroundColor Yellow
    exit 1
}

# Check Git status
Write-Host "`n📊 Checking Git Status..." -ForegroundColor Yellow
try {
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Host "✅ Found changes to commit:" -ForegroundColor Green
        git status --short
    } else {
        Write-Host "⚠️ No changes detected" -ForegroundColor Yellow
        $response = Read-Host "Continue anyway? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "❌ Aborted" -ForegroundColor Red
            exit 0
        }
    }
}
catch {
    Write-Host "❌ Error checking Git status: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check current branch
Write-Host "`n🌿 Checking Current Branch..." -ForegroundColor Yellow
try {
    $currentBranch = git branch --show-current
    Write-Host "✅ Current branch: $currentBranch" -ForegroundColor Green
    
    if ($currentBranch -ne "main") {
        Write-Host "⚠️ Not on main branch" -ForegroundColor Yellow
        $response = Read-Host "Switch to main branch? (Y/n)"
        if ($response -ne "n" -and $response -ne "N") {
            git checkout main
            Write-Host "✅ Switched to main branch" -ForegroundColor Green
        }
    }
}
catch {
    Write-Host "❌ Error checking branch: $($_.Exception.Message)" -ForegroundColor Red
}

# Add all changes
Write-Host "`n📦 Adding Changes..." -ForegroundColor Yellow
try {
    git add .
    Write-Host "✅ All changes added to staging" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error adding changes: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Show what will be committed
Write-Host "`n📋 Files to be committed:" -ForegroundColor Yellow
try {
    git diff --cached --name-status
}
catch {
    Write-Host "⚠️ Could not show staged changes" -ForegroundColor Yellow
}

# Commit changes
Write-Host "`n💾 Committing Changes..." -ForegroundColor Yellow
try {
    git commit -m "$CommitMessage"
    Write-Host "✅ Changes committed successfully" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error committing changes: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check remote
Write-Host "`n🌐 Checking Remote Repository..." -ForegroundColor Yellow
try {
    $remoteUrl = git remote get-url origin
    Write-Host "✅ Remote URL: $remoteUrl" -ForegroundColor Green
}
catch {
    Write-Host "❌ No remote repository configured" -ForegroundColor Red
    Write-Host "Please configure a remote repository first" -ForegroundColor Yellow
    exit 1
}

# Push to GitHub
Write-Host "`n🚀 Pushing to GitHub..." -ForegroundColor Yellow
$pushConfirm = Read-Host "Push changes to GitHub? (Y/n)"
if ($pushConfirm -ne "n" -and $pushConfirm -ne "N") {
    try {
        git push origin main
        Write-Host "✅ Changes pushed to GitHub successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Error pushing to GitHub: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You may need to authenticate or check your internet connection" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "⚠️ Push skipped" -ForegroundColor Yellow
}

# Show final status
Write-Host "`n📊 Final Status:" -ForegroundColor Yellow
try {
    git log --oneline -5
    Write-Host ""
    git status
}
catch {
    Write-Host "⚠️ Could not show final status" -ForegroundColor Yellow
}

Write-Host "`n🎉 Git Operations Complete!" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Summary of Changes Committed:" -ForegroundColor Cyan
Write-Host "   🔧 Fixed MySQL database connection issues" -ForegroundColor White
Write-Host "   🗄️ Updated database configuration and setup" -ForegroundColor White
Write-Host "   🔐 Fixed login system and authentication" -ForegroundColor White
Write-Host "   🍺 Added Michigan brewery data import (376 breweries)" -ForegroundColor White
Write-Host "   📊 Created CSV import system for brewery data" -ForegroundColor White
Write-Host "   ⚙️ Added PowerShell management scripts" -ForegroundColor White
Write-Host "   🐛 Fixed URL path issues and navigation" -ForegroundColor White
Write-Host "   📱 Completed social features and user management" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Repository Links:" -ForegroundColor Cyan
Write-Host "   📂 GitHub: $remoteUrl" -ForegroundColor White
Write-Host "   🏠 Local: $ProjectPath" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "   • Start server: .\start-server.ps1" -ForegroundColor White
Write-Host "   • Login as admin: <EMAIL> / admin123" -ForegroundColor White
Write-Host "   • Browse breweries: http://localhost:8000/breweries/listing.php" -ForegroundColor White
Write-Host ""
