# FIX PHP CONFIGURATION ERRORS
# Based on Apache error log analysis

Write-Host "FIXING PHP CONFIGURATION ERRORS" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red

# 1. Fix php.ini - Remove duplicate and fix PDO loading
Write-Host ""
Write-Host "1. FIXING PHP.INI CONFIGURATION:" -ForegroundColor Cyan

$phpIni = "C:\xampp\php\php.ini"
if (Test-Path $phpIni) {
    # Create backup
    $backup = $phpIni + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
    Copy-Item $phpIni $backup
    Write-Host "Backup created: $backup" -ForegroundColor Gray
    
    $content = Get-Content $phpIni
    $newContent = @()
    $mysqliFound = $false
    $pdoFound = $false
    $pdoMysqlFound = $false
    
    foreach ($line in $content) {
        # Skip duplicate mysqli entries
        if ($line -match "^extension=mysqli" -and $mysqliFound) {
            Write-Host "Removed duplicate mysqli entry" -ForegroundColor Yellow
            continue
        }
        
        # Fix PDO loading - PDO must be loaded before pdo_mysql
        if ($line -match "^extension=pdo_mysql" -and -not $pdoFound) {
            # Add PDO first, then pdo_mysql
            $newContent += "extension=pdo"
            $newContent += "extension=pdo_mysql"
            Write-Host "Fixed PDO loading order" -ForegroundColor Green
            $pdoFound = $true
            $pdoMysqlFound = $true
            continue
        }
        
        # Skip standalone pdo entries since we handle them above
        if ($line -match "^extension=pdo$") {
            continue
        }
        
        # Track what we've found
        if ($line -match "^extension=mysqli") {
            $mysqliFound = $true
        }
        
        $newContent += $line
    }
    
    # Save fixed configuration
    $newContent | Set-Content $phpIni -Encoding UTF8
    Write-Host "✅ php.ini configuration fixed" -ForegroundColor Green
} else {
    Write-Host "❌ php.ini not found" -ForegroundColor Red
}

# 2. Check PHP extensions directory
Write-Host ""
Write-Host "2. CHECKING PHP EXTENSIONS:" -ForegroundColor Cyan

$extDir = "C:\xampp\php\ext"
if (Test-Path $extDir) {
    Write-Host "Extensions directory: $extDir" -ForegroundColor Green
    
    # Check for required DLLs
    $requiredDlls = @("php_pdo.dll", "php_pdo_mysql.dll", "php_mysqli.dll")
    foreach ($dll in $requiredDlls) {
        $dllPath = Join-Path $extDir $dll
        if (Test-Path $dllPath) {
            Write-Host "✅ Found: $dll" -ForegroundColor Green
        } else {
            Write-Host "❌ Missing: $dll" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Extensions directory not found" -ForegroundColor Red
}

# 3. Fix database schema - Add missing password_hash column
Write-Host ""
Write-Host "3. FIXING DATABASE SCHEMA:" -ForegroundColor Cyan

$mysqlClient = "C:\xampp\mysql\bin\mysql.exe"
if (Test-Path $mysqlClient) {
    Write-Host "Adding missing password_hash column..." -ForegroundColor Yellow
    
    # SQL to add password_hash column
    $sql = @"
USE beersty_db;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255) AFTER password;
UPDATE users SET password_hash = password WHERE password_hash IS NULL;
"@
    
    $sqlFile = "fix_schema.sql"
    $sql | Set-Content $sqlFile -Encoding UTF8
    
    try {
        $result = & $mysqlClient -u root -e "source $sqlFile" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database schema updated" -ForegroundColor Green
        } else {
            Write-Host "❌ Database update failed: $result" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Database update error: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ MySQL client not found" -ForegroundColor Red
}

# 4. Restart Apache to apply changes
Write-Host ""
Write-Host "4. RESTARTING APACHE:" -ForegroundColor Cyan

Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 3

Start-Process "C:\xampp\apache\bin\httpd.exe" -WindowStyle Hidden
Start-Sleep 5

$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host "✅ Apache restarted successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Apache failed to restart" -ForegroundColor Red
}

# 5. Test PHP and PDO
Write-Host ""
Write-Host "5. TESTING PHP AND PDO:" -ForegroundColor Cyan

# Create comprehensive test
$testContent = @'
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Configuration Test</h1>";

echo "<h2>PHP Version:</h2>";
echo "<p>" . phpversion() . "</p>";

echo "<h2>Extension Status:</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";
echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";
echo "<p>MySQLi: " . (extension_loaded('mysqli') ? '<span style="color:green">✅ LOADED</span>' : '<span style="color:red">❌ NOT LOADED</span>') . "</p>";

echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color:green'>✅ PDO MySQL connection successful</p>";
    
    // Test beersty_db
    $pdo->exec("USE beersty_db");
    echo "<p style='color:green'>✅ Connected to beersty_db</p>";
    
    // Check users table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    echo "<h3>Users Table Structure:</h3>";
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $col) {
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
'@

$testFile = "comprehensive-test.php"
$testContent | Set-Content $testFile -Encoding UTF8
Write-Host "Created comprehensive test: $testFile" -ForegroundColor Green

# Test the file
$testUrl = "http://localhost/beersty/$testFile"
try {
    Start-Sleep 2
    $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 10 -UseBasicParsing
    if ($response.Content -match "PHP Configuration Test") {
        Write-Host "✅ PHP is working!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ PHP may not be processing correctly" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. Open test URLs
Write-Host ""
Write-Host "6. OPENING TEST URLS:" -ForegroundColor Cyan

$urls = @(
    $testUrl,
    "http://localhost/beersty",
    "http://localhost/beersty/admin/user-management.php",
    "http://localhost/phpmyadmin"
)

foreach ($url in $urls) {
    Write-Host "Opening: $url" -ForegroundColor White
    Start-Process $url
    Start-Sleep 1
}

Write-Host ""
Write-Host "PHP CONFIGURATION FIX COMPLETE!" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host ""
Write-Host "Check the opened pages:" -ForegroundColor Yellow
Write-Host "1. Comprehensive test should show all green checkmarks" -ForegroundColor White
Write-Host "2. User management should work without 500 errors" -ForegroundColor White
Write-Host "3. phpMyAdmin should be accessible" -ForegroundColor White

Read-Host "Press Enter to continue"
