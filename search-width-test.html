<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Box Width Test - Beersty</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/home-simple.css" rel="stylesheet">
    <link href="/assets/css/location-search.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #4a2c17 100%);
            color: #fff;
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .screen-size-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            z-index: 9999;
        }
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .width-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator" id="screen-size">
        Screen: <span id="screen-width"></span>px
    </div>
    
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="display-4 mb-3">
                <i class="fas fa-expand-arrows-alt me-3"></i>
                Search Box Width Test
            </h1>
            <p class="lead">Testing responsive search box widths across different screen sizes</p>
        </div>

        <div class="test-section">
            <h2 class="h3 mb-3">
                <i class="fas fa-desktop me-2"></i>
                Responsive Breakpoints
            </h2>
            <div class="row">
                <div class="col-md-3">
                    <h5>Mobile</h5>
                    <p>&lt; 768px</p>
                    <small>Standard width</small>
                </div>
                <div class="col-md-3">
                    <h5>Tablet</h5>
                    <p>768px - 991px</p>
                    <small>350px min-width</small>
                </div>
                <div class="col-md-3">
                    <h5>Desktop</h5>
                    <p>992px - 1199px</p>
                    <small>400px min-width</small>
                </div>
                <div class="col-md-3">
                    <h5>Large Desktop</h5>
                    <p>≥ 1200px</p>
                    <small>450px min-width</small>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="h3 mb-3">
                <i class="fas fa-search me-2"></i>
                Live Search Test
            </h2>
            
            <div class="search-container">
                <form class="search-form">
                    <div class="row g-2">
                        <div class="col-md-4 col-lg-4">
                            <div class="input-group">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" name="search" class="form-control border-start-0"
                                       placeholder="Search for breweries, bars..." style="box-shadow: none;">
                            </div>
                        </div>
                        <div class="col-md-5 col-lg-5">
                            <div class="input-group location-input-container">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <input type="text" name="location" class="form-control border-start-0"
                                       placeholder="City, state..." 
                                       autocomplete="off"
                                       style="box-shadow: none;"
                                       title="Smart location search with suggestions">
                                <input type="hidden" name="location_lat" id="location_lat">
                                <input type="hidden" name="location_lng" id="location_lng">
                            </div>
                        </div>
                        <div class="col-md-3 col-lg-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="width-info" id="width-info">
                    <div>Location input width: <span id="location-width"></span>px</div>
                    <div>Dropdown width: <span id="dropdown-width"></span>px</div>
                    <div>Container width: <span id="container-width"></span>px</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="h3 mb-3">
                <i class="fas fa-list me-2"></i>
                Test Instructions
            </h2>
            <ol>
                <li><strong>Resize your browser window</strong> to test different breakpoints</li>
                <li><strong>Type in the location field</strong> (try "taylor", "plymouth", "spring")</li>
                <li><strong>Check the dropdown width</strong> matches the input field</li>
                <li><strong>Verify text readability</strong> in the suggestions</li>
                <li><strong>Test on mobile</strong> by using browser dev tools</li>
            </ol>
        </div>

        <div class="test-section">
            <h2 class="h3 mb-3">
                <i class="fas fa-check-circle me-2"></i>
                Expected Behavior
            </h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>Desktop (≥992px)</h5>
                    <ul>
                        <li>Location input: 400px+ width</li>
                        <li>Larger padding and font size</li>
                        <li>Spacious dropdown suggestions</li>
                        <li>Better icon sizing</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Mobile (&lt;768px)</h5>
                    <ul>
                        <li>Full-width responsive layout</li>
                        <li>Touch-friendly sizing</li>
                        <li>Compact but readable</li>
                        <li>Stacked form elements</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
    <script>
        // Screen size indicator
        function updateScreenSize() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            // Update width info
            const locationInput = document.querySelector('input[name="location"]');
            const dropdown = document.querySelector('.location-suggestions');
            const container = document.querySelector('.search-container');
            
            if (locationInput) {
                document.getElementById('location-width').textContent = locationInput.offsetWidth;
            }
            if (dropdown) {
                document.getElementById('dropdown-width').textContent = dropdown.offsetWidth || 'Hidden';
            }
            if (container) {
                document.getElementById('container-width').textContent = container.offsetWidth;
            }
        }
        
        window.addEventListener('resize', updateScreenSize);
        
        document.addEventListener('DOMContentLoaded', function() {
            updateScreenSize();
            
            // Initialize smart location search
            const locationInput = document.querySelector('input[name="location"]');
            if (typeof Beersty !== 'undefined' && Beersty.components && locationInput) {
                Beersty.components.setupLocationAutocomplete(locationInput);
            }
            
            // Update width info when dropdown appears
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        updateScreenSize();
                    }
                });
            });
            
            const dropdown = document.querySelector('.location-suggestions');
            if (dropdown) {
                observer.observe(dropdown, { attributes: true });
            }
            
            // Update on input focus
            locationInput?.addEventListener('focus', () => {
                setTimeout(updateScreenSize, 100);
            });
        });
    </script>
</body>
</html>
