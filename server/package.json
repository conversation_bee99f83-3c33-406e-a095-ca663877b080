{"name": "beersty-backend", "version": "1.0.0", "description": "Backend API for Beersty Brewery Management System", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "node src/scripts/migrate.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["brewery", "management", "api", "mysql"], "author": "Beersty Team", "license": "MIT"}