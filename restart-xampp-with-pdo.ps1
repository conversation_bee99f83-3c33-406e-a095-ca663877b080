# Beersty XAMPP Restart Script with PDO Extension Check
# This script restarts XAMPP services, verifies PDO extension, and starts development server

Write-Host "=== Beersty XAMPP Restart with PDO Check ===" -ForegroundColor Green

# Function to check if a process is running
function Test-ProcessRunning {
    param([string]$ProcessName)
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue) -ne $null
}

# Function to wait for process to stop
function Wait-ProcessStop {
    param([string]$ProcessName, [int]$TimeoutSeconds = 30)
    $timeout = (Get-Date).AddSeconds($TimeoutSeconds)
    while ((Test-ProcessRunning $ProcessName) -and (Get-Date) -lt $timeout) {
        Start-Sleep -Seconds 1
        Write-Host "." -NoNewline
    }
    Write-Host ""
}

# Step 1: Stop existing services
Write-Host "`nStep 1: Stopping existing XAMPP services..." -ForegroundColor Yellow

if (Test-ProcessRunning "httpd") {
    Write-Host "Stopping Apache (httpd)..." -ForegroundColor Red
    Stop-Process -Name "httpd" -Force -ErrorAction SilentlyContinue
    Wait-ProcessStop "httpd"
}

if (Test-ProcessRunning "mysqld") {
    Write-Host "Stopping MySQL (mysqld)..." -ForegroundColor Red
    Stop-Process -Name "mysqld" -Force -ErrorAction SilentlyContinue
    Wait-ProcessStop "mysqld"
}

if (Test-ProcessRunning "xampp-control") {
    Write-Host "Stopping XAMPP Control Panel..." -ForegroundColor Red
    Stop-Process -Name "xampp-control" -Force -ErrorAction SilentlyContinue
    Wait-ProcessStop "xampp-control"
}

Write-Host "All services stopped." -ForegroundColor Green

# Step 2: Check PHP and PDO extension
Write-Host "`nStep 2: Checking PHP and PDO extension..." -ForegroundColor Yellow

# Ensure XAMPP PHP is in PATH
if (Test-Path "C:\xampp\php\php.exe") {
    $env:PATH = "C:\xampp\php;$env:PATH"
    Write-Host "XAMPP PHP added to PATH" -ForegroundColor Green
} else {
    Write-Host "ERROR: XAMPP PHP not found at C:\xampp\php\php.exe" -ForegroundColor Red
    exit 1
}

# Check PHP version
try {
    $phpVersion = php -r "echo phpversion();" 2>$null
    Write-Host "PHP Version: $phpVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Cannot execute PHP!" -ForegroundColor Red
    exit 1
}

# Check PDO extensions
Write-Host "Checking PDO extensions..." -ForegroundColor Yellow
$pdoAvailable = php -r "echo extension_loaded('pdo') ? 'Yes' : 'No';" 2>$null
$pdoMysqlAvailable = php -r "echo extension_loaded('pdo_mysql') ? 'Yes' : 'No';" 2>$null

Write-Host "PDO Extension: $pdoAvailable" -ForegroundColor $(if ($pdoAvailable -eq "Yes") { "Green" } else { "Red" })
Write-Host "PDO MySQL Extension: $pdoMysqlAvailable" -ForegroundColor $(if ($pdoMysqlAvailable -eq "Yes") { "Green" } else { "Red" })

if ($pdoAvailable -ne "Yes" -or $pdoMysqlAvailable -ne "Yes") {
    Write-Host "`nERROR: PDO extensions not properly loaded!" -ForegroundColor Red
    
    # Check php.ini
    $phpIniPath = php -r "echo php_ini_loaded_file();" 2>$null
    Write-Host "PHP.ini location: $phpIniPath" -ForegroundColor Cyan
    
    Write-Host "`nPlease ensure these lines are uncommented in php.ini:" -ForegroundColor Yellow
    Write-Host "extension=pdo" -ForegroundColor Green
    Write-Host "extension=pdo_mysql" -ForegroundColor Green
    
    Write-Host "`nWould you like to continue anyway? (y/n): " -ForegroundColor Yellow -NoNewline
    $continue = Read-Host
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "Exiting. Please fix PDO extensions first." -ForegroundColor Red
        exit 1
    }
}

# Step 3: Start XAMPP Control Panel
Write-Host "`nStep 3: Starting XAMPP Control Panel..." -ForegroundColor Yellow
if (Test-Path "C:\xampp\xampp-control.exe") {
    Start-Process "C:\xampp\xampp-control.exe" -WindowStyle Normal
    Start-Sleep -Seconds 3
    Write-Host "XAMPP Control Panel started." -ForegroundColor Green
} else {
    Write-Host "ERROR: XAMPP Control Panel not found!" -ForegroundColor Red
    exit 1
}

# Step 4: Instructions for user
Write-Host "`nStep 4: Please start Apache and MySQL in XAMPP Control Panel" -ForegroundColor Cyan
Write-Host "1. Click 'Start' next to Apache" -ForegroundColor White
Write-Host "2. Click 'Start' next to MySQL" -ForegroundColor White
Write-Host "3. Wait for both to show 'Running' with green background" -ForegroundColor White
Write-Host "`nPress any key when both services are running..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 5: Verify services
Write-Host "`nStep 5: Verifying services..." -ForegroundColor Yellow
$apacheRunning = Test-ProcessRunning "httpd"
$mysqlRunning = Test-ProcessRunning "mysqld"

Write-Host "Apache: $(if ($apacheRunning) { 'Running ✓' } else { 'Not Running ✗' })" -ForegroundColor $(if ($apacheRunning) { "Green" } else { "Red" })
Write-Host "MySQL: $(if ($mysqlRunning) { 'Running ✓' } else { 'Not Running ✗' })" -ForegroundColor $(if ($mysqlRunning) { 'Green' } else { 'Red' })

# Step 6: Test database connection
Write-Host "`nStep 6: Testing database connection..." -ForegroundColor Yellow
if ($mysqlRunning) {
    try {
        $testResult = php -r "try { `$pdo = new PDO('mysql:host=localhost;dbname=beersty_db', 'root', ''); echo 'SUCCESS'; } catch (PDOException `$e) { echo 'FAILED: ' . `$e->getMessage(); }" 2>$null

        if ($testResult -like "*SUCCESS*") {
            Write-Host "Database connection: SUCCESS ✓" -ForegroundColor Green
        } else {
            Write-Host "Database connection: $testResult" -ForegroundColor Yellow
            Write-Host "Note: Database may need to be created first." -ForegroundColor Cyan
        }
    } catch {
        Write-Host "Could not test database connection." -ForegroundColor Yellow
    }
}

# Step 7: Start development server
Write-Host "`nStep 7: Ready to start development server!" -ForegroundColor Green
Write-Host "URLs available:" -ForegroundColor Cyan
Write-Host "• Development server: http://localhost:8000" -ForegroundColor White
Write-Host "• phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host "• XAMPP Dashboard: http://localhost" -ForegroundColor White

Write-Host "`nStarting PHP development server on port 8000..." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Red

Start-Sleep -Seconds 2
Write-Host "`n=== Development Server Starting ===" -ForegroundColor Green
php -S localhost:8000
