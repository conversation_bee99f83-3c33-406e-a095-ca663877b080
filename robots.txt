# Beersty Platform - Robots.txt
# SEO-Optimized Crawler Instructions

User-agent: *

# Allow crawling of main content
Allow: /
Allow: /beers/
Allow: /breweries/
Allow: /location/
Allow: /learn/
Allow: /search/
Allow: /blog/

# Allow public profiles
Allow: /profile/@*

# Block private/admin areas
Disallow: /admin/
Disallow: /api/
Disallow: /config/
Disallow: /database/
Disallow: /includes/
Disallow: /uploads/private/

# Block user account pages
Disallow: /account/
Disallow: /profile/edit/
Disallow: /profile/preferences/
Disallow: /profile/notifications/
Disallow: /profile/messages/
Disallow: /profile/photos/
Disallow: /profile/lists/
Disallow: /profile/badges/
Disallow: /profile/statistics/

# Block social interaction pages
Disallow: /social/checkin/
Disallow: /social/discover-users/

# Block business management
Disallow: /business/
Disallow: /breweries/manage/
Disallow: /breweries/claim/

# Block development/debug files
Disallow: /*debug*
Disallow: /*test*
Disallow: /*.php$
Disallow: /*.sql$
Disallow: /*.log$
Disallow: /*.md$

# Block query parameters that don't add value
Disallow: /*?*utm_*
Disallow: /*?*session*
Disallow: /*?*debug*
Disallow: /*?*test*

# Block duplicate content
Disallow: /*?sort=*
Disallow: /*?page=*
Disallow: /*?filter=*

# Allow specific crawlers for social media
User-agent: facebookexternalhit
Allow: /
Allow: /beers/
Allow: /breweries/
Allow: /profile/@*

User-agent: Twitterbot
Allow: /
Allow: /beers/
Allow: /breweries/
Allow: /profile/@*

User-agent: LinkedInBot
Allow: /
Allow: /beers/
Allow: /breweries/
Allow: /profile/@*

# Crawl delay for respectful crawling
Crawl-delay: 1

# Sitemap location
Sitemap: https://beersty.com/sitemap.php
Sitemap: https://beersty.com/sitemap.xml

# Host directive (update with actual domain)
# Host: https://beersty.com
