<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'Welcome to Beersty - ' . APP_NAME;
$additionalCSS = ['/assets/css/profile.css', '/assets/css/onboarding.css'];

$user = getCurrentUser();

// Check if user has already completed onboarding
$profile = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT * FROM profiles WHERE id = ?");
    $stmt->execute([$user['id']]);
    $profile = $stmt->fetch();
    
    // If user has already filled basic info, redirect to preferences
    if (!empty($profile['first_name']) && !empty($profile['username'])) {
        redirect('/user/preferences.php?onboarding=1');
    }
} catch (Exception $e) {
    error_log("Error checking onboarding status: " . $e->getMessage());
}

$errors = [];
$currentStep = $_GET['step'] ?? 1;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $step = (int)($_POST['step'] ?? 1);
    
    if ($step === 1) {
        // Basic profile information
        $firstName = sanitizeInput($_POST['first_name'] ?? '');
        $lastName = sanitizeInput($_POST['last_name'] ?? '');
        $username = sanitizeInput($_POST['username'] ?? '');
        $bio = sanitizeInput($_POST['bio'] ?? '');
        $location = sanitizeInput($_POST['location'] ?? '');
        
        // Validation
        if (empty($firstName)) {
            $errors[] = 'First name is required.';
        }
        
        if (empty($username)) {
            $errors[] = 'Username is required.';
        } elseif (strlen($username) < 3 || strlen($username) > 50) {
            $errors[] = 'Username must be between 3 and 50 characters.';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'Username can only contain letters, numbers, and underscores.';
        }
        
        if (empty($errors)) {
            try {
                // Check if username is taken
                $stmt = $conn->prepare("SELECT id FROM profiles WHERE username = ? AND id != ?");
                $stmt->execute([$username, $user['id']]);
                if ($stmt->fetch()) {
                    $errors[] = 'Username is already taken.';
                } else {
                    // Update profile
                    $stmt = $conn->prepare("
                        UPDATE profiles SET 
                            first_name = ?, last_name = ?, username = ?, bio = ?, location = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$firstName, $lastName, $username, $bio, $location, $user['id']]);
                    
                    // Log activity
                    $activityStmt = $conn->prepare("
                        INSERT INTO user_activities (user_id, activity_type, metadata) 
                        VALUES (?, 'joined', ?)
                    ");
                    $activityStmt->execute([$user['id'], json_encode(['onboarding_completed' => true])]);
                    
                    // Redirect to preferences
                    redirect('/user/preferences.php?onboarding=1');
                }
            } catch (Exception $e) {
                error_log("Onboarding error: " . $e->getMessage());
                $errors[] = 'An error occurred. Please try again.';
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="onboarding-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Progress Bar -->
                <div class="progress mb-4" style="height: 8px;">
                    <div class="progress-bar" role="progressbar" style="width: 33%"></div>
                </div>

                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold">
                        🍺 Welcome to Beersty!
                    </h1>
                    <p class="lead" style="color: #F5F5DC;">
                        Let's set up your profile to get the most out of your beer journey
                    </p>
                    <div class="badge fs-6 mb-3" style="background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%); color: #3B2A2A;">
                        Step 1 of 3: Basic Information
                    </div>
                </div>
                
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <input type="hidden" name="step" value="1">
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="first_name" class="form-label fw-bold">
                                        <i class="fas fa-user me-2" style="color: #FFC107;"></i>First Name *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($_POST['first_name'] ?? $profile['first_name'] ?? ''); ?>"
                                           required placeholder="Your first name">
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="last_name" class="form-label fw-bold">
                                        <i class="fas fa-user me-2" style="color: #FFC107;"></i>Last Name
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($_POST['last_name'] ?? $profile['last_name'] ?? ''); ?>"
                                           placeholder="Your last name">
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="username" class="form-label fw-bold">
                                    <i class="fas fa-at me-2" style="color: #FFC107;"></i>Username *
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text">@</span>
                                    <input type="text" class="form-control" id="username" name="username"
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? $profile['username'] ?? ''); ?>"
                                           required placeholder="Choose a unique username">
                                </div>
                                <div class="form-text">
                                    This will be your unique identifier on Beersty. 3-50 characters, letters, numbers, and underscores only.
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="bio" class="form-label fw-bold">
                                    <i class="fas fa-quote-left me-2" style="color: #FFC107;"></i>Tell us about yourself
                                </label>
                                <textarea class="form-control form-control-lg" id="bio" name="bio" rows="3"
                                          placeholder="Share your beer journey, favorite styles, or what brought you to Beersty..."><?php echo htmlspecialchars($_POST['bio'] ?? $profile['bio'] ?? ''); ?></textarea>
                                <div class="form-text">
                                    This helps other beer enthusiasts connect with you!
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="location" class="form-label fw-bold">
                                    <i class="fas fa-map-marker-alt me-2" style="color: #FFC107;"></i>Location
                                </label>
                                <input type="text" class="form-control form-control-lg" id="location" name="location"
                                       value="<?php echo htmlspecialchars($_POST['location'] ?? $profile['location'] ?? ''); ?>"
                                       placeholder="City, State/Country">
                                <div class="form-text">
                                    Help us recommend nearby breweries and connect you with local beer enthusiasts.
                                </div>
                            </div>
                            
                            <!-- Role Information -->
                            <div class="alert alert-info">
                                <h6 class="fw-bold mb-2">
                                    <i class="fas fa-info-circle me-2"></i>Your Account Type
                                </h6>
                                <div class="d-flex align-items-center">
                                    <span class="badge me-3 fs-6" style="background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%); color: #3B2A2A;">
                                        <?php 
                                        $roleLabels = [
                                            'beer_enthusiast' => '🍺 Beer Enthusiast',
                                            'beer_expert' => '🎯 Beer Expert/Critic',
                                            'customer' => '👤 Customer',
                                            'brewery' => '🏭 Brewery Owner',
                                            'admin' => '⚡ Admin'
                                        ];
                                        echo $roleLabels[$user['role']] ?? ucfirst($user['role']);
                                        ?>
                                    </span>
                                    <small class="text-muted">
                                        <?php
                                        switch ($user['role']) {
                                            case 'beer_enthusiast':
                                                echo 'Perfect for discovering, rating, and sharing beer experiences!';
                                                break;
                                            case 'beer_expert':
                                                echo 'Share your expertise with detailed reviews and insights.';
                                                break;
                                            case 'customer':
                                                echo 'Explore breweries and enjoy the beer community.';
                                                break;
                                            default:
                                                echo 'Welcome to the beer community!';
                                        }
                                        ?>
                                    </small>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="/index.php" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>Skip for Now
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    Next: Beer Preferences
                                    <i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- What's Next -->
                <div class="text-center mt-4">
                    <h6 class="mb-3" style="color: #F5F5DC;">What's coming next:</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div style="color: #FFC107;">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <div class="small">Set Beer Preferences</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="color: #D69A6B;">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <div class="small">Follow Breweries</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="color: #6F4C3E;">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <div class="small">Start Rating Beers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
