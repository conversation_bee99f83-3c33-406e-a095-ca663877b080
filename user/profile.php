<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'My Profile - ' . APP_NAME;
$additionalCSS = ['/assets/css/social-dark.css'];
$additionalCSS = ['/assets/css/profile.css'];

$user = getCurrentUser();
$errors = [];
$success = false;

// Function to safely get user role
function getUserRole($profile, $profileUser) {
    return $profile['role'] ?? $profileUser['profile_role'] ?? $profileUser['role'] ?? 'customer';
}

// Check if we're viewing another user's profile
$viewingUserId = $_GET['id'] ?? $user['id'];
$isOwnProfile = ($viewingUserId === $user['id']);

// Check if we're in edit mode (only allowed for own profile)
$editMode = $isOwnProfile && isset($_GET['edit']) && $_GET['edit'] === 'true';

// Handle profile update (only for own profile)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $editMode && $isOwnProfile) {
    // Debug: Log form submission
    error_log("Profile form submitted by user: " . $user['id']);
    error_log("POST data: " . print_r($_POST, true));
    error_log("FILES data: " . print_r($_FILES, true));

    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    $username = sanitizeInput($_POST['username'] ?? '');
    $bio = sanitizeInput($_POST['bio'] ?? '');
    $location = sanitizeInput($_POST['location'] ?? '');
    $hometown = sanitizeInput($_POST['hometown'] ?? '');
    $dateOfBirth = $_POST['date_of_birth'] ?? '';
    $website = sanitizeInput($_POST['website'] ?? '');
    $instagram = sanitizeInput($_POST['instagram'] ?? '');
    $twitter = sanitizeInput($_POST['twitter'] ?? '');
    $facebook = sanitizeInput($_POST['facebook'] ?? '');
    
    // Privacy settings
    $profileVisibility = $_POST['profile_visibility'] ?? 'public';
    $showLocation = isset($_POST['show_location']) ? 1 : 0;
    $showAge = isset($_POST['show_age']) ? 1 : 0;
    $allowMessages = isset($_POST['allow_messages']) ? 1 : 0;

    // Handle avatar upload
    $avatarPath = null;
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/avatars/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileInfo = pathinfo($_FILES['avatar']['name']);
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
        $fileExtension = strtolower($fileInfo['extension']);

        if (!in_array($fileExtension, $allowedTypes)) {
            $errors[] = 'Avatar must be a JPG, PNG, or GIF image.';
        } elseif ($_FILES['avatar']['size'] > 5 * 1024 * 1024) { // 5MB limit
            $errors[] = 'Avatar file size must be less than 5MB.';
        } else {
            // Generate unique filename
            $fileName = $user['id'] . '_' . time() . '.' . $fileExtension;
            $uploadPath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadPath)) {
                $avatarPath = '/uploads/avatars/' . $fileName;

                // Delete old avatar if exists
                if (!empty($profile['avatar']) && file_exists('../' . ltrim($profile['avatar'], '/'))) {
                    unlink('../' . ltrim($profile['avatar'], '/'));
                }
            } else {
                $errors[] = 'Failed to upload avatar. Please try again.';
            }
        }
    }

    // Handle hero banner upload
    $heroBannerPath = null;
    if (isset($_FILES['hero_banner']) && $_FILES['hero_banner']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/hero-banners/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileInfo = pathinfo($_FILES['hero_banner']['name']);
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
        $fileExtension = strtolower($fileInfo['extension']);

        if (!in_array($fileExtension, $allowedTypes)) {
            $errors[] = 'Hero banner must be a JPG, PNG, or GIF image.';
        } elseif ($_FILES['hero_banner']['size'] > 10 * 1024 * 1024) { // 10MB limit for hero banners
            $errors[] = 'Hero banner file size must be less than 10MB.';
        } else {
            // Generate unique filename
            $fileName = $user['id'] . '_hero_' . time() . '.' . $fileExtension;
            $uploadPath = $uploadDir . $fileName;

            if (move_uploaded_file($_FILES['hero_banner']['tmp_name'], $uploadPath)) {
                $heroBannerPath = '/uploads/hero-banners/' . $fileName;

                // Delete old hero banner if exists
                if (!empty($profile['hero_banner']) && file_exists('../' . ltrim($profile['hero_banner'], '/'))) {
                    unlink('../' . ltrim($profile['hero_banner'], '/'));
                }
            } else {
                $errors[] = 'Failed to upload hero banner.';
            }
        }
    }
    
    // Validation
    if (!empty($username)) {
        if (strlen($username) < 3 || strlen($username) > 50) {
            $errors[] = 'Username must be between 3 and 50 characters.';
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'Username can only contain letters, numbers, and underscores.';
        }
    }
    
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Please enter a valid website URL.';
    }
    
    if (!empty($dateOfBirth)) {
        $birthDate = DateTime::createFromFormat('Y-m-d', $dateOfBirth);
        if (!$birthDate || $birthDate > new DateTime()) {
            $errors[] = 'Please enter a valid birth date.';
        }
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // Check if username is already taken (if provided)
            if (!empty($username)) {
                $stmt = $conn->prepare("SELECT id FROM profiles WHERE username = ? AND id != ?");
                $stmt->execute([$username, $user['id']]);
                if ($stmt->fetch()) {
                    $errors[] = 'Username is already taken.';
                }
            }
            
            if (empty($errors)) {
                // Prepare SQL based on what's being updated
                $updateFields = [
                    'first_name = ?', 'last_name = ?', 'username = ?', 'bio = ?',
                    'location = ?', 'hometown = ?', 'date_of_birth = ?', 'website = ?',
                    'instagram = ?', 'twitter = ?', 'facebook = ?',
                    'profile_visibility = ?', 'show_location = ?', 'show_age = ?', 'allow_messages = ?'
                ];

                $params = [
                    $firstName, $lastName, $username ?: null, $bio,
                    $location, $hometown, $dateOfBirth ?: null, $website,
                    $instagram, $twitter, $facebook,
                    $profileVisibility, $showLocation, $showAge, $allowMessages
                ];

                if ($avatarPath !== null) {
                    $updateFields[] = 'avatar = ?';
                    $params[] = $avatarPath;
                }

                if ($heroBannerPath !== null) {
                    $updateFields[] = 'hero_banner = ?';
                    $params[] = $heroBannerPath;
                }

                $updateFields[] = 'updated_at = NOW()';
                $params[] = $user['id'];

                $sql = "UPDATE profiles SET " . implode(', ', $updateFields) . " WHERE id = ?";
                $stmt = $conn->prepare($sql);
                
                $stmt->execute($params);
                
                // Log activity (optional - don't fail if table doesn't exist)
                try {
                    $activityStmt = $conn->prepare("
                        INSERT INTO user_activities (user_id, activity_type, metadata, ip_address, user_agent)
                        VALUES (?, 'profile_update', ?, ?, ?)
                    ");
                    $metadata = json_encode(['updated_fields' => array_keys($_POST)]);
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

                    $activityStmt->execute([$user['id'], $metadata, $ipAddress, $userAgent]);
                } catch (Exception $activityError) {
                    // Log the error but don't fail the profile update
                    error_log("Activity logging failed: " . $activityError->getMessage());
                }

                $_SESSION['success_message'] = 'Profile updated successfully!';
                $success = true;
            }
            
        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            error_log("Profile update SQL error details: " . print_r($e, true));
            $errors[] = 'An error occurred while updating your profile: ' . $e->getMessage();
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get profile data for the user being viewed
$profile = null;
$profileUser = null;
try {
    $db = new Database();
    $conn = $db->getConnection();

    // Get profile data
    $stmt = $conn->prepare("SELECT * FROM profiles WHERE id = ?");
    $stmt->execute([$viewingUserId]);
    $profile = $stmt->fetch();

    if (!$profile) {
        // If profile not found, redirect to 404 or show error
        $_SESSION['error_message'] = 'User profile not found.';
        header('Location: /social/discover-users.php');
        exit;
    }

    // Get user data for the profile being viewed
    $stmt = $conn->prepare("SELECT u.*, p.role as profile_role FROM users u LEFT JOIN profiles p ON u.id = p.id WHERE u.id = ?");
    $stmt->execute([$viewingUserId]);
    $profileUser = $stmt->fetch();

    if (!$profileUser) {
        $_SESSION['error_message'] = 'User not found.';
        header('Location: /social/discover-users.php');
        exit;
    }



    // Update page title based on whose profile we're viewing
    if (!$isOwnProfile) {
        $displayName = trim($profile['first_name'] . ' ' . $profile['last_name']);
        if (empty($displayName)) {
            $displayName = $profile['username'] ?: 'User Profile';
        }
        $pageTitle = htmlspecialchars($displayName) . ' - ' . APP_NAME;
    }

} catch (Exception $e) {
    error_log("Error fetching profile: " . $e->getMessage());
    $errors[] = 'Error loading profile data.';
}

include '../includes/header.php';
?>

<!-- Mobile-Friendly Responsive CSS -->
<style>
/* Mobile-First Responsive Design */
.profile-hero {
    height: 300px !important;
}

@media (min-width: 768px) {
    .profile-hero {
        height: 400px !important;
    }
}

/* Mobile Hero Adjustments */
@media (max-width: 767px) {
    .profile-hero {
        height: 250px !important;
    }

    .profile-info {
        padding-bottom: 1rem !important;
    }

    .profile-picture-hero {
        margin-right: 1rem !important;
    }

    .profile-picture-hero img,
    .avatar-placeholder-hero {
        width: 100px !important;
        height: 100px !important;
    }

    .profile-header-info h1 {
        font-size: 1.5rem !important;
    }

    .profile-header-info p {
        font-size: 1rem !important;
    }

    .profile-badges .badge {
        font-size: 0.75rem !important;
        margin-bottom: 0.25rem;
        display: inline-block;
    }

    .profile-stats {
        flex-wrap: wrap;
        gap: 1rem !important;
    }

    .stat-item {
        min-width: 60px;
    }

    .stat-number {
        font-size: 1.25rem !important;
    }
}

/* Mobile Navigation Tabs */
@media (max-width: 767px) {
    .profile-nav-container .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .profile-nav-container .nav-tabs::-webkit-scrollbar {
        display: none;
    }

    .profile-nav-container .nav-link {
        white-space: nowrap;
        min-width: 100px;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
    }

    .profile-nav-container .badge {
        font-size: 0.7rem;
    }
}

/* Mobile Content Layout */
@media (max-width: 991px) {
    .profile-content-mobile {
        margin-top: 1rem;
    }

    .about-section-mobile {
        order: 1;
        margin-bottom: 1.5rem;
    }

    .activity-section-mobile {
        order: 2;
    }

    .quick-actions-mobile {
        order: 3;
        margin-top: 1rem;
    }
}

/* Mobile Cards */
@media (max-width: 767px) {
    .card {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-header h5,
    .card-header h6 {
        font-size: 1rem;
        margin-bottom: 0;
    }
}

/* Mobile Buttons */
@media (max-width: 767px) {
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .profile-action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .profile-action-buttons .btn {
        width: 100%;
    }
}

/* Mobile Edit Mode */
@media (max-width: 767px) {
    .edit-profile-sidebar {
        order: 2;
        margin-top: 1.5rem;
    }

    .edit-profile-content {
        order: 1;
    }

    .current-avatar,
    .current-hero-banner {
        text-align: center;
        margin-bottom: 1rem;
    }

    .form-row-mobile {
        flex-direction: column;
    }

    .form-row-mobile .col-md-6,
    .form-row-mobile .col-md-4,
    .form-row-mobile .col-md-3 {
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* Touch-Friendly Interactions */
@media (max-width: 767px) {
    .nav-link,
    .btn,
    .badge {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .nav-link {
        padding: 0.75rem 1rem;
    }

    .social-links .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* Mobile Typography */
@media (max-width: 767px) {
    .display-5 {
        font-size: 1.75rem;
    }

    .lead {
        font-size: 1rem;
    }

    h1, h2, h3, h4, h5, h6 {
        line-height: 1.3;
    }

    .small {
        font-size: 0.8rem;
    }
}

/* Mobile Spacing */
@media (max-width: 767px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .gap-4 {
        gap: 1rem !important;
    }
}
</style>

<?php if (!$editMode): ?>
<!-- Public Profile View - Full Width Hero -->
<!-- Profile Hero Section -->
<section class="profile-hero position-relative">
    <?php
    $heroStyle = "height: 400px; background-size: cover; background-position: center;";
    if (!empty($profile['hero_banner'])) {
        $heroStyle .= " background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('" . htmlspecialchars($profile['hero_banner']) . "');";
    } else {
        $heroStyle .= " background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 50%, #D69A6B 100%);";
    }
    ?>
    <div class="hero-image" style="<?php echo $heroStyle; ?>">
        <div class="hero-overlay position-absolute w-100 h-100" style="background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)); z-index: 1;"></div>

        <div class="container h-100">
            <div class="row h-100 align-items-end">
                <div class="col-lg-8">
                    <div class="profile-info text-white pb-4" style="position: relative; z-index: 2;">
                        <div class="d-flex align-items-end mb-4 flex-column flex-md-row">
                            <!-- Large Profile Picture -->
                            <div class="profile-picture-hero me-4 mb-3 mb-md-0 align-self-center">
                                <?php if (!empty($profile['avatar'])): ?>
                                    <img src="<?php echo htmlspecialchars($profile['avatar']); ?>"
                                         alt="Profile Picture" class="rounded-circle border border-4 border-white"
                                         width="150" height="150" style="object-fit: cover; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
                                <?php else: ?>
                                    <div class="avatar-placeholder-hero rounded-circle border border-4 border-white d-flex align-items-center justify-content-center"
                                         style="width: 150px; height: 150px; background: #6F4C3E; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
                                        <i class="fas fa-user fa-4x text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="profile-header-info text-center text-md-start">
                                <h1 class="display-5 fw-bold mb-2 text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                                    <?php if (!empty($profile['first_name']) || !empty($profile['last_name'])): ?>
                                        <?php echo htmlspecialchars(trim($profile['first_name'] . ' ' . $profile['last_name'])); ?>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($profileUser['email']); ?>
                                    <?php endif; ?>
                                </h1>

                                <?php if (!empty($profile['username'])): ?>
                                    <p class="fs-4 mb-3 text-white-50">@<?php echo htmlspecialchars($profile['username']); ?></p>
                                <?php endif; ?>

                                <div class="profile-badges mb-3">
                                    <span class="badge bg-primary fs-6 me-2">
                                        <?php
                                        $roleLabels = [
                                            'beer_enthusiast' => '🍺 Beer Enthusiast',
                                            'beer_expert' => '🎯 Beer Expert',
                                            'customer' => '👤 Customer',
                                            'brewery' => '🏭 Brewery Owner',
                                            'admin' => '⚡ Admin'
                                        ];
                                        $userRole = getUserRole($profile, $profileUser);
                                        echo $roleLabels[$userRole] ?? ucfirst($userRole);
                                        ?>
                                    </span>
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-calendar me-1"></i>
                                        Member since <?php echo date('M Y', strtotime($profile['created_at'])); ?>
                                    </span>
                                </div>

                                <?php if (!empty($profile['bio'])): ?>
                                    <p class="fs-6 mb-3 text-white" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8); max-width: 600px;">
                                        <?php echo nl2br(htmlspecialchars($profile['bio'])); ?>
                                    </p>
                                <?php endif; ?>

                                <?php if (!empty($profile['location']) && $profile['show_location']): ?>
                                    <p class="fs-6 mb-3 text-white-50">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        <?php echo htmlspecialchars($profile['location']); ?>
                                    </p>
                                <?php endif; ?>

                                <div class="profile-stats d-flex gap-4 mb-3">
                                    <div class="stat-item text-center">
                                        <div class="stat-number fs-3 fw-bold text-warning"><?php echo number_format($profile['total_checkins'] ?? 0); ?></div>
                                        <div class="stat-label small text-white-50">Check-ins</div>
                                    </div>
                                    <div class="stat-item text-center">
                                        <div class="stat-number fs-3 fw-bold text-warning"><?php echo number_format($profile['total_reviews'] ?? 0); ?></div>
                                        <div class="stat-label small text-white-50">Reviews</div>
                                    </div>
                                    <div class="stat-item text-center">
                                        <div class="stat-number fs-3 fw-bold text-warning"><?php echo number_format($profile['follower_count'] ?? 0); ?></div>
                                        <div class="stat-label small text-white-50">Followers</div>
                                    </div>
                                    <div class="stat-item text-center">
                                        <div class="stat-number fs-3 fw-bold text-warning"><?php echo number_format($profile['following_count'] ?? 0); ?></div>
                                        <div class="stat-label small text-white-50">Following</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end text-center text-md-end">
                    <div style="position: relative; z-index: 2; margin-bottom: 3rem;">
                        <?php if ($isOwnProfile): ?>
                            <a href="/user/profile.php?edit=true" class="btn btn-primary btn-lg">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </a>
                        <?php else: ?>
                            <!-- Follow/Message buttons for other users -->
                            <div class="d-flex gap-2 profile-action-buttons justify-content-center justify-content-md-end">
                                <button class="btn btn-primary btn-lg follow-btn"
                                        data-user-id="<?php echo $viewingUserId; ?>"
                                        data-action="follow">
                                    <i class="fas fa-user-plus me-2"></i>Follow
                                </button>
                                <button class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-envelope me-2"></i>Message
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Profile Navigation -->
<div class="profile-nav-container" style="background: #000000; border-bottom: 2px solid #D69A6B;">
    <div class="container">
        <ul class="nav nav-tabs nav-fill border-0" id="profileTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active fw-bold text-white border-0" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" style="background: transparent; border-bottom: 3px solid #FFC107 !important;">
                    <i class="fas fa-user me-2"></i>Overview
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link fw-bold text-white border-0" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab" style="background: transparent;">
                    <i class="fas fa-clock me-2"></i>Activity <span class="badge bg-secondary ms-1"><?php echo $profile['total_checkins'] ?? 0; ?></span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link fw-bold text-white border-0" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" style="background: transparent;">
                    <i class="fas fa-star me-2"></i>Reviews <span class="badge bg-secondary ms-1"><?php echo $profile['total_reviews'] ?? 0; ?></span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link fw-bold text-white border-0" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab" style="background: transparent;">
                    <i class="fas fa-camera me-2"></i>Photos <span class="badge bg-secondary ms-1"><?php echo $profile['total_photos'] ?? 0; ?></span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link fw-bold text-white border-0" id="badges-tab" data-bs-toggle="tab" data-bs-target="#badges" type="button" role="tab" style="background: transparent;">
                    <i class="fas fa-trophy me-2"></i>Badges
                </button>
            </li>
        </ul>
    </div>
</div>

<!-- Tab Content -->
<div class="container py-4">
    <div class="tab-content" id="profileTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <div class="col-lg-8 profile-content-mobile">
                    <!-- Recent Activity -->
                    <div class="card shadow-sm activity-section-mobile">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2" style="color: #FFC107;"></i>Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-4">
                                <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No recent activity</h6>
                                <p class="text-muted small">Start exploring beers and checking in to see your activity here!</p>
                                <a href="/social/checkin.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Check In a Beer
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- About Section -->
                    <div class="card shadow-sm mb-4 about-section-mobile">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2" style="color: #FFC107;"></i>About
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($profile['bio'])): ?>
                                <p class="mb-3"><?php echo nl2br(htmlspecialchars($profile['bio'])); ?></p>
                            <?php else: ?>
                                <p class="text-muted mb-3">No bio provided yet.</p>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-12">
                                    <?php if (!empty($profile['location']) && $profile['show_location']): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                            <strong>Lives in:</strong> <?php echo htmlspecialchars($profile['location']); ?>
                                        </p>
                                    <?php endif; ?>

                                    <?php if (!empty($profile['hometown'])): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-home me-2 text-success"></i>
                                            <strong>From:</strong> <?php echo htmlspecialchars($profile['hometown']); ?>
                                        </p>
                                    <?php endif; ?>

                                    <?php if (!empty($profile['website'])): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-globe me-2 text-info"></i>
                                            <strong>Website:</strong>
                                            <a href="<?php echo htmlspecialchars($profile['website']); ?>" target="_blank" class="text-decoration-none">
                                                <?php echo htmlspecialchars($profile['website']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>

                                    <p class="mb-2">
                                        <i class="fas fa-calendar me-2 text-warning"></i>
                                        <strong>Joined:</strong> <?php echo date('F j, Y', strtotime($profile['created_at'])); ?>
                                    </p>
                                </div>
                            </div>

                            <!-- Social Links -->
                            <?php if (!empty($profile['instagram']) || !empty($profile['twitter']) || !empty($profile['facebook'])): ?>
                                <hr>
                                <div class="social-links">
                                    <h6 class="fw-bold mb-3">Connect</h6>
                                    <?php if (!empty($profile['instagram'])): ?>
                                        <a href="https://instagram.com/<?php echo htmlspecialchars($profile['instagram']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fab fa-instagram me-1"></i>Instagram
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($profile['twitter'])): ?>
                                        <a href="https://twitter.com/<?php echo htmlspecialchars($profile['twitter']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fab fa-twitter me-1"></i>Twitter
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($profile['facebook'])): ?>
                                        <a href="https://facebook.com/<?php echo htmlspecialchars($profile['facebook']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                            <i class="fab fa-facebook me-1"></i>Facebook
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card shadow-sm quick-actions-mobile">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2" style="color: #FFC107;"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/social/checkin.php" class="btn btn-primary">
                                    <i class="fas fa-map-marker-alt me-2"></i>Check In
                                </a>
                                <a href="/beers/discover.php" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-2"></i>Discover Beers
                                </a>
                                <a href="/places/search.php" class="btn btn-outline-primary">
                                    <i class="fas fa-building me-2"></i>Find Places
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other tabs with placeholder content -->
        <div class="tab-pane fade" id="activity" role="tabpanel">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-clock fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Activity Yet</h5>
                        <p class="text-muted">Your check-ins, reviews, and other activities will appear here.</p>
                        <a href="/social/checkin.php" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Start with a Check-in
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="reviews" role="tabpanel">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-star fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Reviews Yet</h5>
                        <p class="text-muted">Your beer reviews and ratings will appear here.</p>
                        <a href="/beers/discover.php" class="btn btn-primary">
                            <i class="fas fa-star me-1"></i>Write Your First Review
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="photos" role="tabpanel">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-camera fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Photos Yet</h5>
                        <p class="text-muted">Photos from your check-ins and reviews will appear here.</p>
                        <a href="/social/checkin.php" class="btn btn-primary">
                            <i class="fas fa-camera me-1"></i>Add Photos with Check-in
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" id="badges" role="tabpanel">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Badges Earned Yet</h5>
                        <p class="text-muted">Complete activities to earn badges and show off your achievements!</p>
                        <a href="/social/checkin.php" class="btn btn-primary">
                            <i class="fas fa-trophy me-1"></i>Start Earning Badges
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- Edit Mode - Container Layout -->
<div class="container py-4">
    <div class="row">
        <!-- Profile Sidebar -->
        <div class="col-lg-4 mb-4 edit-profile-sidebar">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="profile-avatar mb-3">
                        <?php if (!empty($profile['avatar'])): ?>
                            <img src="<?php echo htmlspecialchars($profile['avatar']); ?>" 
                                 alt="Profile Picture" class="rounded-circle" width="120" height="120">
                        <?php else: ?>
                            <div class="avatar-placeholder rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                 style="width: 120px; height: 120px; background: #f8f9fa; border: 2px dashed #dee2e6;">
                                <i class="fas fa-user fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <h4 class="mb-1">
                        <?php if (!empty($profile['first_name']) || !empty($profile['last_name'])): ?>
                            <?php echo htmlspecialchars(trim($profile['first_name'] . ' ' . $profile['last_name'])); ?>
                        <?php else: ?>
                            <?php echo htmlspecialchars($profileUser['email']); ?>
                        <?php endif; ?>
                    </h4>
                    
                    <?php if (!empty($profile['username'])): ?>
                        <p class="text-muted mb-2">@<?php echo htmlspecialchars($profile['username']); ?></p>
                    <?php endif; ?>
                    
                    <span class="badge bg-primary mb-3">
                        <?php 
                        $roleLabels = [
                            'beer_enthusiast' => '🍺 Beer Enthusiast',
                            'beer_expert' => '🎯 Beer Expert',
                            'customer' => '👤 Customer',
                            'brewery' => '🏭 Brewery Owner',
                            'admin' => '⚡ Admin'
                        ];
                        $userRole = getUserRole($profile, $profileUser);
                        echo $roleLabels[$userRole] ?? ucfirst($userRole);
                        ?>
                    </span>
                    
                    <?php if (!empty($profile['bio'])): ?>
                        <p class="text-muted small"><?php echo nl2br(htmlspecialchars($profile['bio'])); ?></p>
                    <?php endif; ?>
                    
                    <?php if (!empty($profile['location']) && $profile['show_location']): ?>
                        <p class="text-muted small">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            <?php echo htmlspecialchars($profile['location']); ?>
                        </p>
                    <?php endif; ?>
                    
                    <!-- Stats -->
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <div class="fw-bold"><?php echo number_format($profile['total_checkins'] ?? 0); ?></div>
                            <small class="text-muted">Check-ins</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold"><?php echo number_format($profile['total_reviews'] ?? 0); ?></div>
                            <small class="text-muted">Reviews</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold"><?php echo number_format($profile['follower_count'] ?? 0); ?></div>
                            <small class="text-muted">Followers</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Links (Phase 7) -->
            <div class="card shadow-sm mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-link me-2"></i>Quick Links
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($isOwnProfile && !$editMode): ?>
                            <a href="/user/profile.php?edit=true" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </a>
                            <hr>
                        <?php endif; ?>

                        <a href="/beersty/user/preferences.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-heart me-2"></i>Beer Preferences
                        </a>
                        <a href="/beersty/user/notification-preferences.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-bell me-2"></i>Notification Settings
                        </a>
                        <a href="/beersty/user/messages.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-envelope me-2"></i>Messages
                        </a>
                        <a href="/beersty/user/notifications.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-bell me-2"></i>View Notifications
                        </a>
                        <a href="/beersty/user/lists.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>My Beer Lists
                        </a>
                        <a href="/beersty/user/badges.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-trophy me-2"></i>My Badges
                        </a>
                        <a href="/beersty/user/statistics.php" class="btn btn-outline-dark btn-sm">
                            <i class="fas fa-chart-bar me-2"></i>Statistics
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="col-lg-8 edit-profile-content">
            <?php if ($editMode && $isOwnProfile): ?>
                <!-- Edit Profile Form -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </h5>
                            <a href="/user/profile.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <!-- Basic Information -->
                        <h6 class="fw-bold mb-3">Basic Information</h6>

                        <!-- Hero Banner Upload -->
                        <div class="mb-4">
                            <label for="hero_banner" class="form-label">
                                <i class="fas fa-image me-2" style="color: #FFC107;"></i>Hero Banner
                            </label>
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="current-hero-banner text-center">
                                        <?php if (!empty($profile['hero_banner'])): ?>
                                            <img src="<?php echo htmlspecialchars($profile['hero_banner']); ?>"
                                                 alt="Current Hero Banner" class="rounded mb-2" width="200" height="100"
                                                 style="object-fit: cover; border: 2px solid #FFC107;">
                                        <?php else: ?>
                                            <div class="hero-banner-placeholder rounded mx-auto d-flex align-items-center justify-content-center mb-2"
                                                 style="width: 200px; height: 100px; background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%); border: 2px dashed #D69A6B;">
                                                <i class="fas fa-image fa-2x" style="color: #F5F5DC;"></i>
                                            </div>
                                        <?php endif; ?>
                                        <small class="text-muted d-block">Current</small>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <input type="file" class="form-control" id="hero_banner" name="hero_banner"
                                           accept="image/jpeg,image/jpg,image/png,image/gif">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Upload JPG, PNG, or GIF. Maximum size: 10MB. Recommended: 1200x400px banner image.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Avatar Upload -->
                        <div class="mb-4">
                            <label for="avatar" class="form-label">
                                <i class="fas fa-camera me-2" style="color: #FFC107;"></i>Profile Picture
                            </label>
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <div class="current-avatar text-center">
                                        <?php if (!empty($profile['avatar'])): ?>
                                            <img src="<?php echo htmlspecialchars($profile['avatar']); ?>"
                                                 alt="Current Avatar" class="rounded-circle mb-2" width="80" height="80"
                                                 style="object-fit: cover; border: 2px solid #FFC107;">
                                        <?php else: ?>
                                            <div class="avatar-placeholder rounded-circle mx-auto d-flex align-items-center justify-content-center mb-2"
                                                 style="width: 80px; height: 80px;">
                                                <i class="fas fa-user fa-2x"></i>
                                            </div>
                                        <?php endif; ?>
                                        <small class="text-muted d-block">Current</small>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <input type="file" class="form-control" id="avatar" name="avatar"
                                           accept="image/jpeg,image/jpg,image/png,image/gif">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Upload JPG, PNG, or GIF. Maximum size: 5MB. Recommended: 400x400px square image.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-row-mobile">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       value="<?php echo htmlspecialchars($profile['first_name'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       value="<?php echo htmlspecialchars($profile['last_name'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <div class="input-group">
                                <span class="input-group-text">@</span>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($profile['username'] ?? ''); ?>"
                                       placeholder="Choose a unique username">
                            </div>
                            <div class="form-text">3-50 characters, letters, numbers, and underscores only.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control" id="bio" name="bio" rows="3" 
                                      placeholder="Tell us about yourself and your beer journey..."><?php echo htmlspecialchars($profile['bio'] ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Location Information -->
                        <h6 class="fw-bold mb-3 mt-4">Location</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">Current Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="<?php echo htmlspecialchars($profile['location'] ?? ''); ?>"
                                       placeholder="City, State/Country">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="hometown" class="form-label">Hometown</label>
                                <input type="text" class="form-control" id="hometown" name="hometown" 
                                       value="<?php echo htmlspecialchars($profile['hometown'] ?? ''); ?>"
                                       placeholder="Where you're from">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="date_of_birth" class="form-label">Date of Birth</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                   value="<?php echo htmlspecialchars($profile['date_of_birth'] ?? ''); ?>">
                            <div class="form-text">Used for age verification and birthday celebrations.</div>
                        </div>
                        
                        <!-- Social Links -->
                        <h6 class="fw-bold mb-3 mt-4">Social Links</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="website" class="form-label">
                                    <i class="fas fa-globe me-1"></i>Website
                                </label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="<?php echo htmlspecialchars($profile['website'] ?? ''); ?>"
                                       placeholder="https://yourwebsite.com">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="instagram" class="form-label">
                                    <i class="fab fa-instagram me-1"></i>Instagram
                                </label>
                                <input type="text" class="form-control" id="instagram" name="instagram" 
                                       value="<?php echo htmlspecialchars($profile['instagram'] ?? ''); ?>"
                                       placeholder="@username">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="twitter" class="form-label">
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                </label>
                                <input type="text" class="form-control" id="twitter" name="twitter" 
                                       value="<?php echo htmlspecialchars($profile['twitter'] ?? ''); ?>"
                                       placeholder="@username">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="facebook" class="form-label">
                                    <i class="fab fa-facebook me-1"></i>Facebook
                                </label>
                                <input type="text" class="form-control" id="facebook" name="facebook" 
                                       value="<?php echo htmlspecialchars($profile['facebook'] ?? ''); ?>"
                                       placeholder="Profile URL or username">
                            </div>
                        </div>
                        
                        <!-- Privacy Settings -->
                        <h6 class="fw-bold mb-3 mt-4">Privacy Settings</h6>
                        <div class="mb-3">
                            <label for="profile_visibility" class="form-label">Profile Visibility</label>
                            <select class="form-select" id="profile_visibility" name="profile_visibility">
                                <option value="public" <?php echo ($profile['profile_visibility'] ?? 'public') === 'public' ? 'selected' : ''; ?>>
                                    Public - Anyone can see your profile
                                </option>
                                <option value="friends" <?php echo ($profile['profile_visibility'] ?? '') === 'friends' ? 'selected' : ''; ?>>
                                    Friends Only - Only people you follow can see your profile
                                </option>
                                <option value="private" <?php echo ($profile['profile_visibility'] ?? '') === 'private' ? 'selected' : ''; ?>>
                                    Private - Only you can see your profile
                                </option>
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show_location" name="show_location" 
                                           <?php echo ($profile['show_location'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="show_location">
                                        Show Location
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show_age" name="show_age" 
                                           <?php echo ($profile['show_age'] ?? 0) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="show_age">
                                        Show Age
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_messages" name="allow_messages" 
                                           <?php echo ($profile['allow_messages'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="allow_messages">
                                        Allow Messages
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="/index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Home
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <?php else: ?>
                <!-- Public Profile View - Facebook Style -->
                <div class="profile-hero-section">
                    <!-- Cover Photo / Hero Section -->
                    <div class="profile-cover position-relative" style="height: 350px; background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 50%, #D69A6B 100%); border-radius: 12px; overflow: hidden;">
                        <div class="cover-overlay position-absolute w-100 h-100" style="background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)); z-index: 1;"></div>

                        <!-- Profile Info Overlay -->
                        <div class="profile-info-overlay position-absolute bottom-0 start-0 w-100 text-white p-4" style="z-index: 2;">
                            <div class="row align-items-end">
                                <div class="col-auto">
                                    <!-- Large Profile Picture -->
                                    <div class="profile-picture-large position-relative">
                                        <?php if (!empty($profile['avatar'])): ?>
                                            <img src="<?php echo htmlspecialchars($profile['avatar']); ?>"
                                                 alt="Profile Picture" class="rounded-circle border border-4 border-white"
                                                 width="150" height="150" style="object-fit: cover;">
                                        <?php else: ?>
                                            <div class="avatar-placeholder-large rounded-circle border border-4 border-white d-flex align-items-center justify-content-center"
                                                 style="width: 150px; height: 150px; background: #6F4C3E;">
                                                <i class="fas fa-user fa-4x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="profile-header-info ms-3">
                                        <h1 class="display-6 fw-bold mb-2 text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                                            <?php if (!empty($profile['first_name']) || !empty($profile['last_name'])): ?>
                                                <?php echo htmlspecialchars(trim($profile['first_name'] . ' ' . $profile['last_name'])); ?>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            <?php endif; ?>
                                        </h1>

                                        <?php if (!empty($profile['username'])): ?>
                                            <p class="fs-5 mb-2 text-white-50">@<?php echo htmlspecialchars($profile['username']); ?></p>
                                        <?php endif; ?>

                                        <div class="profile-badges mb-3">
                                            <span class="badge bg-primary fs-6 me-2">
                                                <?php
                                                $roleLabels = [
                                                    'beer_enthusiast' => '🍺 Beer Enthusiast',
                                                    'beer_expert' => '🎯 Beer Expert',
                                                    'customer' => '👤 Customer',
                                                    'brewery' => '🏭 Brewery Owner',
                                                    'admin' => '⚡ Admin'
                                                ];
                                                echo $roleLabels[$user['role']] ?? ucfirst($user['role']);
                                                ?>
                                            </span>
                                            <span class="badge bg-success fs-6">
                                                <i class="fas fa-calendar me-1"></i>
                                                Member since <?php echo date('M Y', strtotime($profile['created_at'])); ?>
                                            </span>
                                        </div>

                                        <div class="profile-stats d-flex gap-4 mb-3">
                                            <div class="stat-item text-center">
                                                <div class="stat-number fs-4 fw-bold text-warning"><?php echo $profile['total_checkins'] ?? 0; ?></div>
                                                <div class="stat-label small text-white-50">Check-ins</div>
                                            </div>
                                            <div class="stat-item text-center">
                                                <div class="stat-number fs-4 fw-bold text-warning"><?php echo $profile['total_reviews'] ?? 0; ?></div>
                                                <div class="stat-label small text-white-50">Reviews</div>
                                            </div>
                                            <div class="stat-item text-center">
                                                <div class="stat-number fs-4 fw-bold text-warning"><?php echo $profile['follower_count'] ?? 0; ?></div>
                                                <div class="stat-label small text-white-50">Followers</div>
                                            </div>
                                            <div class="stat-item text-center">
                                                <div class="stat-number fs-4 fw-bold text-warning"><?php echo $profile['following_count'] ?? 0; ?></div>
                                                <div class="stat-label small text-white-50">Following</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <a href="/user/profile.php?edit=true" class="btn btn-primary btn-lg">
                                        <i class="fas fa-edit me-2"></i>Edit Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Navigation Tabs -->
                    <div class="profile-nav mt-3">
                        <ul class="nav nav-tabs nav-fill" id="profileTabs" role="tablist" style="border-bottom: 2px solid #D69A6B;">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active fw-bold" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                    <i class="fas fa-user me-2"></i>Overview
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                                    <i class="fas fa-clock me-2"></i>Activity <span class="badge bg-secondary ms-1"><?php echo $profile['total_checkins'] ?? 0; ?></span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                                    <i class="fas fa-star me-2"></i>Reviews <span class="badge bg-secondary ms-1"><?php echo $profile['total_reviews'] ?? 0; ?></span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                                    <i class="fas fa-camera me-2"></i>Photos <span class="badge bg-secondary ms-1"><?php echo $profile['total_photos'] ?? 0; ?></span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link fw-bold" id="badges-tab" data-bs-toggle="tab" data-bs-target="#badges" type="button" role="tab">
                                    <i class="fas fa-trophy me-2"></i>Badges
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content mt-4" id="profileTabContent">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <!-- About Section -->
                                    <div class="card shadow-sm mb-4">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                <i class="fas fa-info-circle me-2" style="color: #FFC107;"></i>About
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <?php if (!empty($profile['bio'])): ?>
                                                <p class="mb-3"><?php echo nl2br(htmlspecialchars($profile['bio'])); ?></p>
                                            <?php else: ?>
                                                <p class="text-muted mb-3">No bio provided yet.</p>
                                            <?php endif; ?>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <?php if (!empty($profile['location']) && $profile['show_location']): ?>
                                                        <p class="mb-2">
                                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                                            <strong>Lives in:</strong> <?php echo htmlspecialchars($profile['location']); ?>
                                                        </p>
                                                    <?php endif; ?>

                                                    <?php if (!empty($profile['hometown'])): ?>
                                                        <p class="mb-2">
                                                            <i class="fas fa-home me-2 text-success"></i>
                                                            <strong>From:</strong> <?php echo htmlspecialchars($profile['hometown']); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-6">
                                                    <?php if (!empty($profile['website'])): ?>
                                                        <p class="mb-2">
                                                            <i class="fas fa-globe me-2 text-info"></i>
                                                            <strong>Website:</strong>
                                                            <a href="<?php echo htmlspecialchars($profile['website']); ?>" target="_blank" class="text-decoration-none">
                                                                <?php echo htmlspecialchars($profile['website']); ?>
                                                            </a>
                                                        </p>
                                                    <?php endif; ?>

                                                    <p class="mb-2">
                                                        <i class="fas fa-calendar me-2 text-warning"></i>
                                                        <strong>Joined:</strong> <?php echo date('F j, Y', strtotime($profile['created_at'])); ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Social Links -->
                                            <?php if (!empty($profile['instagram']) || !empty($profile['twitter']) || !empty($profile['facebook'])): ?>
                                                <hr>
                                                <div class="social-links">
                                                    <h6 class="fw-bold mb-3">Connect</h6>
                                                    <?php if (!empty($profile['instagram'])): ?>
                                                        <a href="https://instagram.com/<?php echo htmlspecialchars($profile['instagram']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                                            <i class="fab fa-instagram me-1"></i>Instagram
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (!empty($profile['twitter'])): ?>
                                                        <a href="https://twitter.com/<?php echo htmlspecialchars($profile['twitter']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                                            <i class="fab fa-twitter me-1"></i>Twitter
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (!empty($profile['facebook'])): ?>
                                                        <a href="https://facebook.com/<?php echo htmlspecialchars($profile['facebook']); ?>" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                                            <i class="fab fa-facebook me-1"></i>Facebook
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Recent Activity -->
                                    <div class="card shadow-sm">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                <i class="fas fa-clock me-2" style="color: #FFC107;"></i>Recent Activity
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="text-center py-4">
                                                <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                                                <h6 class="text-muted">No recent activity</h6>
                                                <p class="text-muted small">Start exploring beers and checking in to see your activity here!</p>
                                                <a href="/social/checkin.php" class="btn btn-primary">
                                                    <i class="fas fa-plus me-1"></i>Check In a Beer
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4">
                                    <!-- Quick Stats -->
                                    <div class="card shadow-sm mb-4">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-chart-bar me-2" style="color: #FFC107;"></i>Quick Stats
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6 mb-3">
                                                    <div class="stat-box p-3 rounded" style="background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);">
                                                        <div class="stat-number fs-4 fw-bold text-dark"><?php echo $profile['total_checkins'] ?? 0; ?></div>
                                                        <div class="stat-label small text-dark">Check-ins</div>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="stat-box p-3 rounded" style="background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%);">
                                                        <div class="stat-number fs-4 fw-bold text-white"><?php echo $profile['total_reviews'] ?? 0; ?></div>
                                                        <div class="stat-label small text-white">Reviews</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="stat-box p-3 rounded" style="background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);">
                                                        <div class="stat-number fs-4 fw-bold text-white"><?php echo $profile['follower_count'] ?? 0; ?></div>
                                                        <div class="stat-label small text-white">Followers</div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="stat-box p-3 rounded" style="background: linear-gradient(135deg, #3B2A2A 0%, #000000 100%);">
                                                        <div class="stat-number fs-4 fw-bold text-white"><?php echo $profile['following_count'] ?? 0; ?></div>
                                                        <div class="stat-label small text-white">Following</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Quick Actions -->
                                    <div class="card shadow-sm">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-bolt me-2" style="color: #FFC107;"></i>Quick Actions
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <a href="/social/checkin.php" class="btn btn-primary">
                                                    <i class="fas fa-map-marker-alt me-2"></i>Check In
                                                </a>
                                                <a href="/beers/discover.php" class="btn btn-outline-primary">
                                                    <i class="fas fa-search me-2"></i>Discover Beers
                                                </a>
                                                <a href="/places/search.php" class="btn btn-outline-primary">
                                                    <i class="fas fa-building me-2"></i>Find Places
                                                </a>
                                                <a href="/user/profile.php?edit=true" class="btn btn-outline-secondary">
                                                    <i class="fas fa-edit me-2"></i>Edit Profile
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Tab -->
                        <div class="tab-pane fade" id="activity" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="text-center py-5">
                                        <i class="fas fa-clock fa-4x text-muted mb-4"></i>
                                        <h5 class="text-muted">No Activity Yet</h5>
                                        <p class="text-muted">Your check-ins, reviews, and other activities will appear here.</p>
                                        <a href="/social/checkin.php" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>Start with a Check-in
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reviews Tab -->
                        <div class="tab-pane fade" id="reviews" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="text-center py-5">
                                        <i class="fas fa-star fa-4x text-muted mb-4"></i>
                                        <h5 class="text-muted">No Reviews Yet</h5>
                                        <p class="text-muted">Your beer reviews and ratings will appear here.</p>
                                        <a href="/beers/discover.php" class="btn btn-primary">
                                            <i class="fas fa-star me-1"></i>Write Your First Review
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Photos Tab -->
                        <div class="tab-pane fade" id="photos" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="text-center py-5">
                                        <i class="fas fa-camera fa-4x text-muted mb-4"></i>
                                        <h5 class="text-muted">No Photos Yet</h5>
                                        <p class="text-muted">Photos from your check-ins and reviews will appear here.</p>
                                        <a href="/social/checkin.php" class="btn btn-primary">
                                            <i class="fas fa-camera me-1"></i>Add Photos with Check-in
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Badges Tab -->
                        <div class="tab-pane fade" id="badges" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <div class="text-center py-5">
                                        <i class="fas fa-trophy fa-4x text-muted mb-4"></i>
                                        <h5 class="text-muted">No Badges Earned Yet</h5>
                                        <p class="text-muted">Complete activities to earn badges and show off your achievements!</p>
                                        <a href="/social/checkin.php" class="btn btn-primary">
                                            <i class="fas fa-trophy me-1"></i>Start Earning Badges
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if ($editMode): ?>
<script>
// Avatar preview functionality
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a JPG, PNG, or GIF image.');
            this.value = '';
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            this.value = '';
            return;
        }

        // Create preview
        const reader = new FileReader();
        reader.onload = function(e) {
            const currentAvatar = document.querySelector('.current-avatar');
            const existingImg = currentAvatar.querySelector('img');
            const placeholder = currentAvatar.querySelector('.avatar-placeholder');

            if (existingImg) {
                existingImg.src = e.target.result;
            } else if (placeholder) {
                // Replace placeholder with image
                placeholder.outerHTML = `
                    <img src="${e.target.result}" alt="Avatar Preview"
                         class="rounded-circle mb-2" width="80" height="80"
                         style="object-fit: cover; border: 2px solid #FFC107;">
                `;
            }

            // Update the "Current" text to "Preview"
            const currentText = currentAvatar.querySelector('small');
            if (currentText) {
                currentText.textContent = 'Preview';
                currentText.style.color = '#FFC107';
            }
        };
        reader.readAsDataURL(file);
    }
});

// Hero banner preview functionality
document.getElementById('hero_banner').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a JPG, PNG, or GIF image.');
            this.value = '';
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB.');
            this.value = '';
            return;
        }

        // Create preview
        const reader = new FileReader();
        reader.onload = function(e) {
            const currentHeroBanner = document.querySelector('.current-hero-banner');
            const existingImg = currentHeroBanner.querySelector('img');
            const placeholder = currentHeroBanner.querySelector('.hero-banner-placeholder');

            if (existingImg) {
                existingImg.src = e.target.result;
            } else if (placeholder) {
                // Replace placeholder with image
                placeholder.outerHTML = `
                    <img src="${e.target.result}" alt="Hero Banner Preview"
                         class="rounded mb-2" width="200" height="100"
                         style="object-fit: cover; border: 2px solid #FFC107;">
                `;
            }

            // Update the "Current" text to "Preview"
            const currentText = currentHeroBanner.querySelector('small');
            if (currentText) {
                currentText.textContent = 'Preview';
                currentText.style.color = '#FFC107';
            }
        };
        reader.readAsDataURL(file);
    }
});
</script>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>
