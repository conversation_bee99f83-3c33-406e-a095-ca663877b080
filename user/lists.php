<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'My Beer Lists - ' . APP_NAME;
$additionalCSS = ['/assets/css/lists.css'];

$user = getCurrentUser();
$userId = $user['id'];

$userLists = [];
$defaultLists = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get user's lists
    $stmt = $conn->prepare("
        SELECT ul.*, 
               COUNT(uli.id) as item_count,
               (SELECT COUNT(*) FROM user_list_items uli2 WHERE uli2.list_id = ul.id) as beer_count
        FROM user_lists ul
        LEFT JOIN user_list_items uli ON ul.id = uli.list_id
        WHERE ul.user_id = ?
        GROUP BY ul.id
        ORDER BY ul.is_default DESC, ul.created_at DESC
    ");
    $stmt->execute([$userId]);
    $allLists = $stmt->fetchAll();
    
    // Separate default and custom lists
    foreach ($allLists as $list) {
        if ($list['is_default']) {
            $defaultLists[] = $list;
        } else {
            $userLists[] = $list;
        }
    }
    
    // Create default lists if they don't exist
    $defaultListTypes = [
        'want_to_try' => 'Want to Try',
        'favorites' => 'Favorites',
        'tried' => 'Tried'
    ];
    
    $existingDefaults = array_column($defaultLists, 'list_type');
    
    foreach ($defaultListTypes as $type => $name) {
        if (!in_array($type, $existingDefaults)) {
            $stmt = $conn->prepare("
                INSERT INTO user_lists (user_id, name, list_type, is_default, is_public)
                VALUES (?, ?, ?, 1, 0)
            ");
            $stmt->execute([$userId, $name, $type]);
            
            // Refresh the list
            $stmt = $conn->prepare("
                SELECT ul.*, 
                       COUNT(uli.id) as item_count
                FROM user_lists ul
                LEFT JOIN user_list_items uli ON ul.id = uli.list_id
                WHERE ul.user_id = ? AND ul.list_type = ?
                GROUP BY ul.id
            ");
            $stmt->execute([$userId, $type]);
            $newList = $stmt->fetch();
            if ($newList) {
                $defaultLists[] = $newList;
            }
        }
    }
    
} catch (Exception $e) {
    error_log("User lists error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading your lists.';
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 fw-bold text-primary mb-2">
                        <i class="fas fa-list me-3"></i>My Beer Lists
                    </h1>
                    <p class="lead text-muted">
                        Organize and track your beer discoveries
                    </p>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createListModal">
                    <i class="fas fa-plus me-2"></i>Create New List
                </button>
            </div>
        </div>
    </div>
    
    <!-- Default Lists -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title mb-4">
                <i class="fas fa-star me-2 text-warning"></i>Default Lists
            </h3>
            <div class="row g-4">
                <?php foreach ($defaultLists as $list): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card list-card default-list">
                            <div class="card-body">
                                <div class="list-header">
                                    <div class="list-icon">
                                        <?php
                                        $icons = [
                                            'want_to_try' => 'fas fa-heart',
                                            'favorites' => 'fas fa-star',
                                            'tried' => 'fas fa-check-circle'
                                        ];
                                        $colors = [
                                            'want_to_try' => 'text-danger',
                                            'favorites' => 'text-warning',
                                            'tried' => 'text-success'
                                        ];
                                        ?>
                                        <i class="<?php echo $icons[$list['list_type']] ?? 'fas fa-list'; ?> fa-2x <?php echo $colors[$list['list_type']] ?? 'text-primary'; ?>"></i>
                                    </div>
                                    <div class="list-info">
                                        <h5 class="list-name"><?php echo htmlspecialchars($list['name']); ?></h5>
                                        <p class="list-count text-muted">
                                            <?php echo number_format($list['item_count'] ?? 0); ?> beers
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="list-description">
                                    <?php
                                    $descriptions = [
                                        'want_to_try' => 'Beers you want to try in the future',
                                        'favorites' => 'Your all-time favorite beers',
                                        'tried' => 'Beers you\'ve already experienced'
                                    ];
                                    ?>
                                    <p class="text-muted">
                                        <?php echo $descriptions[$list['list_type']] ?? 'Custom beer list'; ?>
                                    </p>
                                </div>
                                
                                <div class="list-actions">
                                    <a href="/user/list-detail.php?id=<?php echo $list['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View List
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="shareList('<?php echo $list['id']; ?>')">
                                        <i class="fas fa-share me-1"></i>Share
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- Custom Lists -->
    <div class="row">
        <div class="col-12">
            <h3 class="section-title mb-4">
                <i class="fas fa-layer-group me-2 text-info"></i>Custom Lists
                <span class="badge bg-info ms-2"><?php echo count($userLists); ?></span>
            </h3>
            
            <?php if (empty($userLists)): ?>
                <div class="empty-state">
                    <i class="fas fa-list fa-4x text-muted mb-4"></i>
                    <h5>No custom lists yet</h5>
                    <p class="text-muted mb-4">
                        Create custom lists to organize your beers by style, brewery, or any theme you like!
                    </p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createListModal">
                        <i class="fas fa-plus me-2"></i>Create Your First List
                    </button>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($userLists as $list): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card list-card custom-list">
                                <div class="card-body">
                                    <div class="list-header">
                                        <div class="list-icon">
                                            <i class="fas fa-list fa-2x text-info"></i>
                                        </div>
                                        <div class="list-info">
                                            <h5 class="list-name"><?php echo htmlspecialchars($list['name']); ?></h5>
                                            <p class="list-count text-muted">
                                                <?php echo number_format($list['item_count'] ?? 0); ?> beers
                                            </p>
                                        </div>
                                        <div class="list-menu">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="/user/list-detail.php?id=<?php echo $list['id']; ?>">
                                                        <i class="fas fa-eye me-2"></i>View List
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editList('<?php echo $list['id']; ?>')">
                                                        <i class="fas fa-edit me-2"></i>Edit List
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="shareList('<?php echo $list['id']; ?>')">
                                                        <i class="fas fa-share me-2"></i>Share List
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteList('<?php echo $list['id']; ?>')">
                                                        <i class="fas fa-trash me-2"></i>Delete List
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($list['description'])): ?>
                                        <div class="list-description">
                                            <p class="text-muted">
                                                <?php echo htmlspecialchars($list['description']); ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="list-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Created <?php echo formatDateTime($list['created_at']); ?>
                                        </small>
                                        <?php if ($list['is_public']): ?>
                                            <span class="badge bg-success ms-2">Public</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary ms-2">Private</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="list-actions mt-3">
                                        <a href="/user/list-detail.php?id=<?php echo $list['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View List
                                        </a>
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="editList('<?php echo $list['id']; ?>')">
                                            <i class="fas fa-edit me-1"></i>Edit
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create List Modal -->
<div class="modal fade" id="createListModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Beer List</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createListForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="listName" class="form-label">List Name *</label>
                        <input type="text" class="form-control" id="listName" name="name" 
                               placeholder="e.g., Summer Favorites, IPAs to Try" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="listDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="listDescription" name="description" 
                                  rows="3" placeholder="Describe what this list is for..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isPublic" name="is_public">
                            <label class="form-check-label" for="isPublic">
                                Make this list public
                            </label>
                            <div class="form-text">
                                Public lists can be viewed by other users
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create List
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Create list form submission
    document.getElementById('createListForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('/api/create-list.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                location.reload();
            } else {
                alert(result.message || 'Failed to create list');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to create list');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });
});

function editList(listId) {
    // TODO: Implement edit list functionality
    alert('Edit functionality coming soon!');
}

function shareList(listId) {
    const url = `${window.location.origin}/user/list-detail.php?id=${listId}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'Check out my beer list',
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            alert('List URL copied to clipboard!');
        }).catch(() => {
            prompt('Copy this URL to share your list:', url);
        });
    }
}

function deleteList(listId) {
    if (confirm('Are you sure you want to delete this list? This action cannot be undone.')) {
        // TODO: Implement delete functionality
        alert('Delete functionality coming soon!');
    }
}
</script>

<?php include '../includes/footer.php'; ?>
