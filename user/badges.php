<?php
require_once '../config/config.php';
require_once '../includes/BadgeService.php';

// Require login
requireLogin();

$pageTitle = 'My Badges - ' . APP_NAME;
$additionalCSS = ['/assets/css/badges.css'];

$user = getCurrentUser();
$userId = $user['id'];

$earnedBadges = [];
$availableBadges = [];
$badgeCategories = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Initialize badge service
    $badgeService = new BadgeService($conn);
    
    // Get user's earned badges
    $earnedBadges = $badgeService->getUserBadges($userId);
    
    // Get all available badges
    $stmt = $conn->prepare("
        SELECT b.*, 
               CASE WHEN ub.user_id IS NOT NULL THEN 1 ELSE 0 END as is_earned,
               ub.earned_at
        FROM badges b
        LEFT JOIN user_badges ub ON b.id = ub.badge_id AND ub.user_id = ?
        WHERE b.is_active = 1
        ORDER BY is_earned DESC, b.category, b.criteria_value ASC
    ");
    $stmt->execute([$userId]);
    $allBadges = $stmt->fetchAll();
    
    // Group badges by category
    foreach ($allBadges as $badge) {
        $badgeCategories[$badge['category']][] = $badge;
    }
    
    // Get user statistics for progress tracking
    $stmt = $conn->prepare("SELECT * FROM user_statistics WHERE user_id = ?");
    $stmt->execute([$userId]);
    $userStats = $stmt->fetch() ?: [];
    
} catch (Exception $e) {
    error_log("Badges page error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading badges.';
}

// Category information
$categoryInfo = [
    'explorer' => [
        'name' => 'Explorer',
        'icon' => 'fas fa-compass',
        'color' => 'primary',
        'description' => 'Badges for discovering new beers and breweries'
    ],
    'connoisseur' => [
        'name' => 'Connoisseur',
        'icon' => 'fas fa-wine-glass',
        'color' => 'warning',
        'description' => 'Badges for beer expertise and tasting skills'
    ],
    'social' => [
        'name' => 'Social',
        'icon' => 'fas fa-users',
        'color' => 'info',
        'description' => 'Badges for community engagement and social activity'
    ],
    'seasonal' => [
        'name' => 'Seasonal',
        'icon' => 'fas fa-calendar',
        'color' => 'success',
        'description' => 'Badges for seasonal and time-based achievements'
    ],
    'location' => [
        'name' => 'Location',
        'icon' => 'fas fa-map-marker-alt',
        'color' => 'danger',
        'description' => 'Badges for geographic exploration'
    ],
    'special' => [
        'name' => 'Special',
        'icon' => 'fas fa-star',
        'color' => 'secondary',
        'description' => 'Unique and rare achievement badges'
    ]
];

function getBadgeProgress($badge, $userStats) {
    $criteriaType = $badge['criteria_type'];
    $criteriaValue = $badge['criteria_value'];
    
    switch ($criteriaType) {
        case 'checkin_count':
            $current = $userStats['total_checkins'] ?? 0;
            break;
        case 'beer_count':
            $current = $userStats['unique_beers_tried'] ?? 0;
            break;
        case 'brewery_count':
            $current = $userStats['unique_breweries_visited'] ?? 0;
            break;
        case 'style_count':
            $current = $userStats['unique_styles_tried'] ?? 0;
            break;
        case 'rating_count':
            $current = $userStats['total_ratings'] ?? 0;
            break;
        case 'follower_count':
            $current = $userStats['total_followers'] ?? 0;
            break;
        default:
            return null;
    }
    
    $percentage = $criteriaValue > 0 ? min(100, ($current / $criteriaValue) * 100) : 0;
    return [
        'current' => $current,
        'target' => $criteriaValue,
        'percentage' => $percentage
    ];
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-trophy me-3"></i>My Badges
            </h1>
            <p class="lead text-muted">
                Track your achievements and unlock new badges
            </p>
        </div>
    </div>
    
    <!-- Badge Summary -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card badge-summary">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-number text-primary"><?php echo count($earnedBadges); ?></div>
                                <div class="stat-label">Badges Earned</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-number text-success">
                                    <?php echo array_sum(array_column($earnedBadges, 'points_value')); ?>
                                </div>
                                <div class="stat-label">Total Points</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-number text-warning">
                                    <?php 
                                    $totalBadges = array_sum(array_map('count', $badgeCategories));
                                    echo $totalBadges - count($earnedBadges);
                                    ?>
                                </div>
                                <div class="stat-label">Badges Available</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-number text-info">
                                    <?php 
                                    $completionRate = $totalBadges > 0 ? round((count($earnedBadges) / $totalBadges) * 100) : 0;
                                    echo $completionRate;
                                    ?>%
                                </div>
                                <div class="stat-label">Completion Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Badge Categories -->
    <?php foreach ($badgeCategories as $category => $badges): ?>
        <?php $categoryData = $categoryInfo[$category] ?? []; ?>
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-<?php echo $categoryData['color'] ?? 'primary'; ?> text-white">
                        <h5 class="mb-0">
                            <i class="<?php echo $categoryData['icon'] ?? 'fas fa-medal'; ?> me-2"></i>
                            <?php echo $categoryData['name'] ?? ucfirst($category); ?> Badges
                        </h5>
                        <small class="opacity-75">
                            <?php echo $categoryData['description'] ?? ''; ?>
                        </small>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <?php foreach ($badges as $badge): ?>
                                <div class="col-lg-4 col-md-6">
                                    <div class="badge-card <?php echo $badge['is_earned'] ? 'earned' : 'locked'; ?>">
                                        <div class="badge-header">
                                            <div class="badge-icon">
                                                <span class="badge-emoji"><?php echo $badge['icon']; ?></span>
                                            </div>
                                            <div class="badge-rarity">
                                                <span class="badge bg-<?php echo $badge['rarity']; ?>">
                                                    <?php echo ucfirst($badge['rarity']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="badge-content">
                                            <h6 class="badge-name"><?php echo htmlspecialchars($badge['name']); ?></h6>
                                            <p class="badge-description">
                                                <?php echo htmlspecialchars($badge['description']); ?>
                                            </p>
                                            
                                            <?php if ($badge['is_earned']): ?>
                                                <div class="badge-earned">
                                                    <i class="fas fa-check-circle text-success me-1"></i>
                                                    Earned <?php echo date('M j, Y', strtotime($badge['earned_at'])); ?>
                                                </div>
                                                <div class="badge-points">
                                                    <span class="badge bg-success">+<?php echo $badge['points_value']; ?> points</span>
                                                </div>
                                            <?php else: ?>
                                                <?php $progress = getBadgeProgress($badge, $userStats); ?>
                                                <?php if ($progress): ?>
                                                    <div class="badge-progress">
                                                        <div class="progress mb-2">
                                                            <div class="progress-bar" style="width: <?php echo $progress['percentage']; ?>%"></div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php echo number_format($progress['current']); ?> / <?php echo number_format($progress['target']); ?>
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="badge-points">
                                                    <span class="badge bg-secondary"><?php echo $badge['points_value']; ?> points</span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-rocket me-2"></i>Earn More Badges
                    </h5>
                    <p class="text-muted mb-4">
                        Keep exploring, rating, and connecting to unlock more achievements!
                    </p>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="/social/checkin.php" class="btn btn-primary w-100">
                                <i class="fas fa-map-marker-alt d-block mb-2"></i>
                                Check In Beer
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/beers/discover.php" class="btn btn-success w-100">
                                <i class="fas fa-search d-block mb-2"></i>
                                Discover Beers
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/social/discover-users.php" class="btn btn-info w-100">
                                <i class="fas fa-users d-block mb-2"></i>
                                Find Friends
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/user/statistics.php" class="btn btn-warning w-100">
                                <i class="fas fa-chart-bar d-block mb-2"></i>
                                View Statistics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
