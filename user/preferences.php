<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'Beer Preferences - ' . APP_NAME;
$additionalCSS = ['/assets/css/profile.css'];

$user = getCurrentUser();
$errors = [];
$success = false;
$isOnboarding = isset($_GET['onboarding']) && $_GET['onboarding'] == '1';

// Beer styles data
$beerStyles = [
    'IPA' => ['India Pale Ale', 'American IPA', 'Double IPA', 'Session IPA', 'New England IPA'],
    'Stout' => ['Imperial Stout', 'Milk Stout', 'Dry Stout', 'Coffee Stout', 'Chocolate Stout'],
    'Lager' => ['<PERSON><PERSON><PERSON>', 'Helles', 'Märzen', 'Vienna Lager', 'American Lager'],
    'Wheat' => ['Hefeweizen', 'American Wheat', 'Witbier', 'Wheat IPA'],
    'Sour' => ['Berliner Weisse', 'Gose', 'Lambic', 'American Sour', 'Kettle Sour'],
    'Porter' => ['English Porter', 'American Porter', 'Baltic Porter', 'Smoked Porter'],
    'Pale Ale' => ['American Pale Ale', 'English Pale Ale', 'Session Pale Ale'],
    'Belgian' => ['Belgian Dubbel', 'Belgian Tripel', 'Belgian Quad', 'Saison'],
    'Other' => ['Barleywine', 'Mead', 'Cider', 'Fruit Beer', 'Spiced Beer']
];

// Handle preferences update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $favoriteStyles = $_POST['favorite_styles'] ?? [];
    $avoidStyles = $_POST['avoid_styles'] ?? [];
    $preferredAbvMin = $_POST['preferred_abv_min'] ?? null;
    $preferredAbvMax = $_POST['preferred_abv_max'] ?? null;
    $preferredIbuMin = $_POST['preferred_ibu_min'] ?? null;
    $preferredIbuMax = $_POST['preferred_ibu_max'] ?? null;
    $dietaryRestrictions = $_POST['dietary_restrictions'] ?? [];
    
    // Validation
    if (!empty($preferredAbvMin) && (!is_numeric($preferredAbvMin) || $preferredAbvMin < 0 || $preferredAbvMin > 20)) {
        $errors[] = 'Minimum ABV must be between 0 and 20.';
    }
    
    if (!empty($preferredAbvMax) && (!is_numeric($preferredAbvMax) || $preferredAbvMax < 0 || $preferredAbvMax > 20)) {
        $errors[] = 'Maximum ABV must be between 0 and 20.';
    }
    
    if (!empty($preferredAbvMin) && !empty($preferredAbvMax) && $preferredAbvMin > $preferredAbvMax) {
        $errors[] = 'Minimum ABV cannot be greater than maximum ABV.';
    }
    
    if (!empty($preferredIbuMin) && (!is_numeric($preferredIbuMin) || $preferredIbuMin < 0 || $preferredIbuMin > 120)) {
        $errors[] = 'Minimum IBU must be between 0 and 120.';
    }
    
    if (!empty($preferredIbuMax) && (!is_numeric($preferredIbuMax) || $preferredIbuMax < 0 || $preferredIbuMax > 120)) {
        $errors[] = 'Maximum IBU must be between 0 and 120.';
    }
    
    if (!empty($preferredIbuMin) && !empty($preferredIbuMax) && $preferredIbuMin > $preferredIbuMax) {
        $errors[] = 'Minimum IBU cannot be greater than maximum IBU.';
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // Check if preferences exist
            $stmt = $conn->prepare("SELECT id FROM user_beer_preferences WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $existingPrefs = $stmt->fetch();
            
            if ($existingPrefs) {
                // Update existing preferences
                $stmt = $conn->prepare("
                    UPDATE user_beer_preferences SET 
                        favorite_styles = ?, avoid_styles = ?, 
                        preferred_abv_min = ?, preferred_abv_max = ?,
                        preferred_ibu_min = ?, preferred_ibu_max = ?,
                        dietary_restrictions = ?, updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([
                    json_encode($favoriteStyles), json_encode($avoidStyles),
                    $preferredAbvMin ?: null, $preferredAbvMax ?: null,
                    $preferredIbuMin ?: null, $preferredIbuMax ?: null,
                    json_encode($dietaryRestrictions), $user['id']
                ]);
            } else {
                // Insert new preferences
                $stmt = $conn->prepare("
                    INSERT INTO user_beer_preferences 
                    (user_id, favorite_styles, avoid_styles, preferred_abv_min, preferred_abv_max,
                     preferred_ibu_min, preferred_ibu_max, dietary_restrictions) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user['id'], json_encode($favoriteStyles), json_encode($avoidStyles),
                    $preferredAbvMin ?: null, $preferredAbvMax ?: null,
                    $preferredIbuMin ?: null, $preferredIbuMax ?: null,
                    json_encode($dietaryRestrictions)
                ]);
            }
            
            if ($isOnboarding) {
                $_SESSION['success_message'] = 'Welcome to Beersty! Your profile is now complete.';
                redirect('/index.php?welcome=1');
            } else {
                $_SESSION['success_message'] = 'Beer preferences updated successfully!';
                $success = true;
            }
            
        } catch (Exception $e) {
            error_log("Preferences update error: " . $e->getMessage());
            $errors[] = 'An error occurred while updating your preferences.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get current preferences
$preferences = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT * FROM user_beer_preferences WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $preferences = $stmt->fetch();
    
    if ($preferences) {
        $preferences['favorite_styles'] = json_decode($preferences['favorite_styles'] ?? '[]', true);
        $preferences['avoid_styles'] = json_decode($preferences['avoid_styles'] ?? '[]', true);
        $preferences['dietary_restrictions'] = json_decode($preferences['dietary_restrictions'] ?? '[]', true);
    }
} catch (Exception $e) {
    error_log("Error fetching preferences: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-heart me-2"></i>Beer Preferences
                        <?php if ($isOnboarding): ?>
                            <span class="badge bg-primary ms-2">Step 2 of 3</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($isOnboarding): ?>
                        <div class="alert alert-info mb-4">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-magic me-2"></i>Almost there!
                            </h6>
                            <p class="mb-0">
                                Set your beer preferences to get personalized recommendations and discover new favorites.
                                You can always change these later in your profile settings.
                            </p>
                        </div>
                    <?php endif; ?>

                    <p class="text-muted mb-4">
                        Help us personalize your beer discovery experience by telling us about your preferences.
                    </p>
                    
                    <form method="POST">
                        <!-- Favorite Beer Styles -->
                        <h6 class="fw-bold mb-3">Favorite Beer Styles</h6>
                        <p class="text-muted small mb-3">Select the beer styles you enjoy most:</p>
                        
                        <?php foreach ($beerStyles as $category => $styles): ?>
                            <div class="mb-3">
                                <h6 class="text-primary mb-2"><?php echo htmlspecialchars($category); ?></h6>
                                <div class="row">
                                    <?php foreach ($styles as $style): ?>
                                        <div class="col-md-6 col-lg-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="favorite_styles[]" value="<?php echo htmlspecialchars($style); ?>"
                                                       id="fav_<?php echo str_replace(' ', '_', strtolower($style)); ?>"
                                                       <?php echo in_array($style, $preferences['favorite_styles'] ?? []) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="fav_<?php echo str_replace(' ', '_', strtolower($style)); ?>">
                                                    <?php echo htmlspecialchars($style); ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <!-- Styles to Avoid -->
                        <h6 class="fw-bold mb-3 mt-4">Styles to Avoid</h6>
                        <p class="text-muted small mb-3">Select beer styles you prefer not to see in recommendations:</p>
                        
                        <?php foreach ($beerStyles as $category => $styles): ?>
                            <div class="mb-3">
                                <h6 class="text-danger mb-2"><?php echo htmlspecialchars($category); ?></h6>
                                <div class="row">
                                    <?php foreach ($styles as $style): ?>
                                        <div class="col-md-6 col-lg-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="avoid_styles[]" value="<?php echo htmlspecialchars($style); ?>"
                                                       id="avoid_<?php echo str_replace(' ', '_', strtolower($style)); ?>"
                                                       <?php echo in_array($style, $preferences['avoid_styles'] ?? []) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="avoid_<?php echo str_replace(' ', '_', strtolower($style)); ?>">
                                                    <?php echo htmlspecialchars($style); ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <!-- ABV Preferences -->
                        <h6 class="fw-bold mb-3 mt-4">Alcohol Content (ABV) Preferences</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="preferred_abv_min" class="form-label">Minimum ABV (%)</label>
                                <input type="number" class="form-control" id="preferred_abv_min" name="preferred_abv_min" 
                                       min="0" max="20" step="0.1" 
                                       value="<?php echo htmlspecialchars($preferences['preferred_abv_min'] ?? ''); ?>"
                                       placeholder="e.g., 3.5">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="preferred_abv_max" class="form-label">Maximum ABV (%)</label>
                                <input type="number" class="form-control" id="preferred_abv_max" name="preferred_abv_max" 
                                       min="0" max="20" step="0.1" 
                                       value="<?php echo htmlspecialchars($preferences['preferred_abv_max'] ?? ''); ?>"
                                       placeholder="e.g., 8.0">
                            </div>
                        </div>
                        
                        <!-- IBU Preferences -->
                        <h6 class="fw-bold mb-3 mt-4">Bitterness (IBU) Preferences</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="preferred_ibu_min" class="form-label">Minimum IBU</label>
                                <input type="number" class="form-control" id="preferred_ibu_min" name="preferred_ibu_min" 
                                       min="0" max="120" 
                                       value="<?php echo htmlspecialchars($preferences['preferred_ibu_min'] ?? ''); ?>"
                                       placeholder="e.g., 10">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="preferred_ibu_max" class="form-label">Maximum IBU</label>
                                <input type="number" class="form-control" id="preferred_ibu_max" name="preferred_ibu_max" 
                                       min="0" max="120" 
                                       value="<?php echo htmlspecialchars($preferences['preferred_ibu_max'] ?? ''); ?>"
                                       placeholder="e.g., 60">
                            </div>
                        </div>
                        
                        <!-- Dietary Restrictions -->
                        <h6 class="fw-bold mb-3 mt-4">Dietary Restrictions</h6>
                        <div class="row">
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dietary_restrictions[]" 
                                           value="gluten_free" id="gluten_free"
                                           <?php echo in_array('gluten_free', $preferences['dietary_restrictions'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="gluten_free">
                                        Gluten-Free
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dietary_restrictions[]" 
                                           value="vegan" id="vegan"
                                           <?php echo in_array('vegan', $preferences['dietary_restrictions'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="vegan">
                                        Vegan
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dietary_restrictions[]" 
                                           value="low_alcohol" id="low_alcohol"
                                           <?php echo in_array('low_alcohol', $preferences['dietary_restrictions'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="low_alcohol">
                                        Low Alcohol Only
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dietary_restrictions[]" 
                                           value="no_lactose" id="no_lactose"
                                           <?php echo in_array('no_lactose', $preferences['dietary_restrictions'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="no_lactose">
                                        Lactose-Free
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dietary_restrictions[]" 
                                           value="organic_only" id="organic_only"
                                           <?php echo in_array('organic_only', $preferences['dietary_restrictions'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="organic_only">
                                        Organic Only
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <?php if ($isOnboarding): ?>
                                <a href="/user/onboarding.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    Complete Setup
                                    <i class="fas fa-check ms-2"></i>
                                </button>
                            <?php else: ?>
                                <a href="/user/profile.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Preferences
                                </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
