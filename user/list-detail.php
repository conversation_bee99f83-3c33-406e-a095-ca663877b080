<?php
require_once '../config/config.php';

// Get list ID
$listId = sanitizeInput($_GET['id'] ?? '');

if (empty($listId)) {
    header('Location: /user/lists.php');
    exit;
}

$list = null;
$listItems = [];
$canEdit = false;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get list details
    $stmt = $conn->prepare("
        SELECT ul.*, p.first_name, p.last_name, p.username, p.avatar
        FROM user_lists ul
        LEFT JOIN profiles p ON ul.user_id = p.id
        WHERE ul.id = ?
    ");
    $stmt->execute([$listId]);
    $list = $stmt->fetch();
    
    if (!$list) {
        header('Location: /user/lists.php');
        exit;
    }
    
    // Check if user can edit this list
    if (isLoggedIn()) {
        $currentUser = getCurrentUser();
        $canEdit = ($currentUser['id'] === $list['user_id']);
    }
    
    // Check if list is public or user owns it
    if (!$list['is_public'] && !$canEdit) {
        header('Location: /user/lists.php');
        exit;
    }
    
    // Get list items
    $stmt = $conn->prepare("
        SELECT 
            uli.*,
            bm.name as beer_name,
            bm.thumbnail,
            bm.abv,
            bm.ibu,
            bm.average_rating,
            bm.total_ratings,
            b.name as brewery_name,
            bs.name as style_name
        FROM user_list_items uli
        JOIN beer_menu bm ON uli.beer_id = bm.id
        LEFT JOIN breweries b ON bm.brewery_id = b.id
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
        WHERE uli.list_id = ?
        ORDER BY uli.priority DESC, uli.added_at DESC
    ");
    $stmt->execute([$listId]);
    $listItems = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("List detail error: " . $e->getMessage());
    header('Location: /user/lists.php');
    exit;
}

$pageTitle = htmlspecialchars($list['name']) . ' - Beer List - ' . APP_NAME;
$additionalCSS = ['/assets/css/lists.css', '/assets/css/beer-cards.css'];

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/user/lists.php">My Lists</a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($list['name']); ?></li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-start">
                <div class="list-header-info">
                    <h1 class="display-5 fw-bold text-primary mb-2">
                        <?php
                        $icons = [
                            'want_to_try' => 'fas fa-heart text-danger',
                            'favorites' => 'fas fa-star text-warning',
                            'tried' => 'fas fa-check-circle text-success',
                            'custom' => 'fas fa-list text-info'
                        ];
                        $icon = $icons[$list['list_type']] ?? 'fas fa-list text-info';
                        ?>
                        <i class="<?php echo $icon; ?> me-3"></i>
                        <?php echo htmlspecialchars($list['name']); ?>
                    </h1>
                    
                    <?php if (!empty($list['description'])): ?>
                        <p class="lead text-muted mb-3">
                            <?php echo htmlspecialchars($list['description']); ?>
                        </p>
                    <?php endif; ?>
                    
                    <div class="list-meta">
                        <span class="badge bg-primary me-2">
                            <?php echo count($listItems); ?> beers
                        </span>
                        
                        <?php if ($list['is_public']): ?>
                            <span class="badge bg-success me-2">Public</span>
                        <?php else: ?>
                            <span class="badge bg-secondary me-2">Private</span>
                        <?php endif; ?>
                        
                        <span class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            by <?php echo htmlspecialchars($list['first_name'] . ' ' . $list['last_name']); ?>
                        </span>
                        
                        <span class="text-muted ms-3">
                            <i class="fas fa-calendar me-1"></i>
                            Created <?php echo formatDateTime($list['created_at']); ?>
                        </span>
                    </div>
                </div>
                
                <div class="list-actions">
                    <?php if ($canEdit): ?>
                        <button class="btn btn-outline-primary me-2" onclick="addBeerToList()">
                            <i class="fas fa-plus me-1"></i>Add Beer
                        </button>
                        <button class="btn btn-outline-secondary me-2" onclick="editList()">
                            <i class="fas fa-edit me-1"></i>Edit List
                        </button>
                    <?php endif; ?>
                    
                    <button class="btn btn-outline-info" onclick="shareList()">
                        <i class="fas fa-share me-1"></i>Share
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- List Items -->
    <div class="row">
        <div class="col-12">
            <?php if (empty($listItems)): ?>
                <div class="empty-state">
                    <i class="fas fa-beer fa-4x text-muted mb-4"></i>
                    <h5>No beers in this list yet</h5>
                    <?php if ($canEdit): ?>
                        <p class="text-muted mb-4">
                            Start building your list by adding some beers!
                        </p>
                        <button class="btn btn-primary" onclick="addBeerToList()">
                            <i class="fas fa-plus me-2"></i>Add Your First Beer
                        </button>
                    <?php else: ?>
                        <p class="text-muted">
                            This list is empty. Check back later!
                        </p>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($listItems as $item): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card beer-card">
                                <div class="card-body">
                                    <div class="d-flex">
                                        <div class="beer-image me-3">
                                            <?php if ($item['thumbnail']): ?>
                                                <img src="<?php echo htmlspecialchars($item['thumbnail']); ?>" 
                                                     alt="<?php echo htmlspecialchars($item['beer_name']); ?>" 
                                                     class="beer-thumb">
                                            <?php else: ?>
                                                <div class="beer-thumb-placeholder">
                                                    <i class="fas fa-beer"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="beer-info flex-grow-1">
                                            <h6 class="beer-name">
                                                <a href="/beers/detail.php?id=<?php echo $item['beer_id']; ?>" 
                                                   class="text-decoration-none">
                                                    <?php echo htmlspecialchars($item['beer_name']); ?>
                                                </a>
                                            </h6>
                                            
                                            <p class="brewery-name text-muted mb-2">
                                                <?php echo htmlspecialchars($item['brewery_name']); ?>
                                            </p>
                                            
                                            <?php if ($item['style_name']): ?>
                                                <p class="beer-style text-muted mb-2">
                                                    <i class="fas fa-tag me-1"></i>
                                                    <?php echo htmlspecialchars($item['style_name']); ?>
                                                </p>
                                            <?php endif; ?>
                                            
                                            <div class="beer-stats">
                                                <?php if ($item['abv']): ?>
                                                    <span class="badge bg-info me-1">
                                                        <?php echo number_format($item['abv'], 1); ?>% ABV
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <?php if ($item['ibu']): ?>
                                                    <span class="badge bg-warning me-1">
                                                        <?php echo $item['ibu']; ?> IBU
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <?php if ($item['average_rating']): ?>
                                                    <span class="badge bg-success">
                                                        ⭐ <?php echo number_format($item['average_rating'], 1); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <?php if (!empty($item['notes'])): ?>
                                                <div class="list-notes mt-2">
                                                    <small class="text-muted">
                                                        <i class="fas fa-sticky-note me-1"></i>
                                                        <?php echo htmlspecialchars($item['notes']); ?>
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="item-meta mt-2">
                                                <small class="text-muted">
                                                    Added <?php echo formatDateTime($item['added_at']); ?>
                                                </small>
                                                
                                                <?php if ($canEdit): ?>
                                                    <div class="item-actions mt-2">
                                                        <button class="btn btn-sm btn-outline-secondary me-1" 
                                                                onclick="editListItem('<?php echo $item['id']; ?>')">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" 
                                                                onclick="removeFromList('<?php echo $item['id']; ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function addBeerToList() {
    // TODO: Implement add beer functionality
    alert('Add beer functionality coming soon!');
}

function editList() {
    // TODO: Implement edit list functionality
    alert('Edit list functionality coming soon!');
}

function shareList() {
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: '<?php echo addslashes($list['name']); ?> - Beer List',
            text: 'Check out this beer list on Beersty!',
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            alert('List URL copied to clipboard!');
        }).catch(() => {
            prompt('Copy this URL to share the list:', url);
        });
    }
}

function editListItem(itemId) {
    // TODO: Implement edit item functionality
    alert('Edit item functionality coming soon!');
}

function removeFromList(itemId) {
    if (confirm('Remove this beer from the list?')) {
        // TODO: Implement remove functionality
        alert('Remove functionality coming soon!');
    }
}
</script>

<?php include '../includes/footer.php'; ?>
