<?php
/**
 * User Notification Preferences Page
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';
require_once '../includes/NotificationService.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/auth/login.php');
}

$user = getCurrentUser();
$pageTitle = 'Notification Preferences';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        // Prepare notification preferences data
        $preferences = [
            'email_new_follower' => isset($_POST['email_new_follower']),
            'email_friend_checkin' => isset($_POST['email_friend_checkin']),
            'email_beer_release' => isset($_POST['email_beer_release']),
            'email_brewery_event' => isset($_POST['email_brewery_event']),
            'email_achievement_unlocked' => isset($_POST['email_achievement_unlocked']),
            'email_message_received' => isset($_POST['email_message_received']),
            'email_rating_liked' => isset($_POST['email_rating_liked']),
            'email_comment_received' => isset($_POST['email_comment_received']),
            
            'push_new_follower' => isset($_POST['push_new_follower']),
            'push_friend_checkin' => isset($_POST['push_friend_checkin']),
            'push_beer_release' => isset($_POST['push_beer_release']),
            'push_brewery_event' => isset($_POST['push_brewery_event']),
            'push_achievement_unlocked' => isset($_POST['push_achievement_unlocked']),
            'push_message_received' => isset($_POST['push_message_received']),
            'push_rating_liked' => isset($_POST['push_rating_liked']),
            'push_comment_received' => isset($_POST['push_comment_received']),
            
            'digest_frequency' => sanitizeInput($_POST['digest_frequency'] ?? 'weekly'),
            'quiet_hours_start' => sanitizeInput($_POST['quiet_hours_start'] ?? '22:00:00'),
            'quiet_hours_end' => sanitizeInput($_POST['quiet_hours_end'] ?? '08:00:00')
        ];
        
        // Validate digest frequency
        if (!in_array($preferences['digest_frequency'], ['none', 'daily', 'weekly'])) {
            $errors[] = 'Invalid digest frequency selected.';
        }
        
        // Validate time format
        if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $preferences['quiet_hours_start'])) {
            $errors[] = 'Invalid quiet hours start time format.';
        }
        
        if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $preferences['quiet_hours_end'])) {
            $errors[] = 'Invalid quiet hours end time format.';
        }
        
        if (empty($errors)) {
            // Check if preferences exist
            $stmt = $conn->prepare("SELECT id FROM notification_preferences WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Update existing preferences
                $updateFields = [];
                $updateValues = [];
                
                foreach ($preferences as $field => $value) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $value;
                }
                
                $updateValues[] = $user['id'];
                
                $sql = "UPDATE notification_preferences SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE user_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute($updateValues);
            } else {
                // Create new preferences
                $notificationService = new NotificationService($conn);
                $notificationService->createDefaultNotificationPreferences($user['id']);
                
                // Update with user's choices
                $updateFields = [];
                $updateValues = [];
                
                foreach ($preferences as $field => $value) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $value;
                }
                
                $updateValues[] = $user['id'];
                
                $sql = "UPDATE notification_preferences SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE user_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute($updateValues);
            }
            
            $_SESSION['success_message'] = 'Notification preferences updated successfully!';
            $success = true;
        }
        
    } catch (Exception $e) {
        error_log("Notification preferences update error: " . $e->getMessage());
        $errors[] = 'An error occurred while updating your preferences.';
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get current preferences
$currentPreferences = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    $notificationService = new NotificationService($conn);
    $currentPreferences = $notificationService->getUserNotificationPreferences($user['id']);
} catch (Exception $e) {
    error_log("Error fetching notification preferences: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Notification Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Customize how and when you receive notifications from Beersty.
                    </p>
                    
                    <form method="POST">
                        <!-- Email Notifications -->
                        <div class="mb-5">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-envelope me-2 text-primary"></i>Email Notifications
                            </h6>
                            <p class="text-muted small mb-3">Choose which activities trigger email notifications:</p>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_new_follower" 
                                               id="email_new_follower" <?php echo ($currentPreferences['email_new_follower'] ?? true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_new_follower">
                                            <strong>New Followers</strong><br>
                                            <small class="text-muted">When someone follows you</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_friend_checkin" 
                                               id="email_friend_checkin" <?php echo ($currentPreferences['email_friend_checkin'] ?? true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_friend_checkin">
                                            <strong>Friend Check-ins</strong><br>
                                            <small class="text-muted">When friends check in to beers</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_achievement_unlocked" 
                                               id="email_achievement_unlocked" <?php echo ($currentPreferences['email_achievement_unlocked'] ?? true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_achievement_unlocked">
                                            <strong>Achievements</strong><br>
                                            <small class="text-muted">When you earn new badges</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_message_received" 
                                               id="email_message_received" <?php echo ($currentPreferences['email_message_received'] ?? true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_message_received">
                                            <strong>Messages</strong><br>
                                            <small class="text-muted">When you receive direct messages</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_rating_liked" 
                                               id="email_rating_liked" <?php echo ($currentPreferences['email_rating_liked'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_rating_liked">
                                            <strong>Rating Likes</strong><br>
                                            <small class="text-muted">When someone likes your rating</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_comment_received" 
                                               id="email_comment_received" <?php echo ($currentPreferences['email_comment_received'] ?? true) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_comment_received">
                                            <strong>Comments</strong><br>
                                            <small class="text-muted">When someone comments on your activity</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_beer_release" 
                                               id="email_beer_release" <?php echo ($currentPreferences['email_beer_release'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_beer_release">
                                            <strong>New Beer Releases</strong><br>
                                            <small class="text-muted">When breweries release new beers</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="email_brewery_event" 
                                               id="email_brewery_event" <?php echo ($currentPreferences['email_brewery_event'] ?? false) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_brewery_event">
                                            <strong>Brewery Events</strong><br>
                                            <small class="text-muted">When breweries announce events</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Push Notifications -->
                        <div class="mb-5">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-mobile-alt me-2 text-success"></i>Push Notifications
                                <span class="badge bg-secondary ms-2">Coming Soon</span>
                            </h6>
                            <p class="text-muted small mb-3">Real-time notifications (will be available in a future update):</p>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="push_achievement_unlocked" 
                                               id="push_achievement_unlocked" <?php echo ($currentPreferences['push_achievement_unlocked'] ?? true) ? 'checked' : ''; ?> disabled>
                                        <label class="form-check-label text-muted" for="push_achievement_unlocked">
                                            <strong>Achievements</strong><br>
                                            <small>When you earn new badges</small>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="push_message_received" 
                                               id="push_message_received" <?php echo ($currentPreferences['push_message_received'] ?? true) ? 'checked' : ''; ?> disabled>
                                        <label class="form-check-label text-muted" for="push_message_received">
                                            <strong>Messages</strong><br>
                                            <small>When you receive direct messages</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- General Settings -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-cog me-2 text-warning"></i>General Settings
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="digest_frequency" class="form-label">Email Digest</label>
                                    <select class="form-select" name="digest_frequency" id="digest_frequency">
                                        <option value="none" <?php echo ($currentPreferences['digest_frequency'] ?? 'weekly') === 'none' ? 'selected' : ''; ?>>None</option>
                                        <option value="daily" <?php echo ($currentPreferences['digest_frequency'] ?? 'weekly') === 'daily' ? 'selected' : ''; ?>>Daily</option>
                                        <option value="weekly" <?php echo ($currentPreferences['digest_frequency'] ?? 'weekly') === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                    </select>
                                    <small class="text-muted">Summary of activity from your network</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="quiet_hours_start" class="form-label">Quiet Hours Start</label>
                                    <input type="time" class="form-control" name="quiet_hours_start" id="quiet_hours_start" 
                                           value="<?php echo substr($currentPreferences['quiet_hours_start'] ?? '22:00:00', 0, 5); ?>">
                                    <small class="text-muted">No notifications during quiet hours</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="quiet_hours_end" class="form-label">Quiet Hours End</label>
                                    <input type="time" class="form-control" name="quiet_hours_end" id="quiet_hours_end" 
                                           value="<?php echo substr($currentPreferences['quiet_hours_end'] ?? '08:00:00', 0, 5); ?>">
                                    <small class="text-muted">Resume notifications after this time</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="/beersty/user/profile.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Profile
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-check-input:checked {
    background-color: #f8b500;
    border-color: #f8b500;
}

.form-check-input:focus {
    border-color: #f8b500;
    box-shadow: 0 0 0 0.25rem rgba(248, 181, 0, 0.25);
}

.form-check-label strong {
    color: #333;
}

.badge {
    font-size: 0.7rem;
}
</style>

<?php include '../includes/footer.php'; ?>
