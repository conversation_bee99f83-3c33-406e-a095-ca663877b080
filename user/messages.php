<?php
/**
 * User Messages Page
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/auth/login.php');
}

$user = getCurrentUser();
$pageTitle = 'Messages';

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row h-100">
        <!-- Conversations Sidebar -->
        <div class="col-md-4 col-lg-3">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">💬 Messages</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#newMessageModal">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="conversations-list" class="list-group list-group-flush">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="col-md-8 col-lg-9">
            <div class="card h-100">
                <div id="chat-header" class="card-header d-none">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <img id="chat-avatar" src="" alt="" class="rounded-circle" width="40" height="40">
                        </div>
                        <div class="flex-grow-1">
                            <h6 id="chat-title" class="mb-0"></h6>
                            <small id="chat-subtitle" class="text-muted"></small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle me-2"></i>View Profile</a></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-ban me-2"></i>Block User</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="card-body d-flex flex-column" style="height: 500px;">
                    <!-- Welcome Message -->
                    <div id="welcome-message" class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Select a conversation</h5>
                            <p class="text-muted">Choose a conversation from the sidebar to start messaging</p>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div id="messages-container" class="flex-grow-1 overflow-auto d-none" style="max-height: 400px;">
                        <!-- Messages will be loaded here -->
                    </div>

                    <!-- Message Input -->
                    <div id="message-input-container" class="mt-3 d-none">
                        <form id="message-form" class="d-flex">
                            <input type="text" id="message-input" class="form-control me-2" 
                                   placeholder="Type your message..." maxlength="2000" required>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Message Modal -->
<div class="modal fade" id="newMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">New Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="new-message-form">
                    <div class="mb-3">
                        <label for="recipient-search" class="form-label">To:</label>
                        <input type="text" id="recipient-search" class="form-control" 
                               placeholder="Search for users..." autocomplete="off">
                        <div id="recipient-suggestions" class="list-group mt-2" style="display: none;"></div>
                        <input type="hidden" id="selected-recipient-id">
                    </div>
                    <div class="mb-3">
                        <label for="initial-message" class="form-label">Message:</label>
                        <textarea id="initial-message" class="form-control" rows="3" 
                                  placeholder="Type your message..." maxlength="2000" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendNewMessage()">Send Message</button>
            </div>
        </div>
    </div>
</div>

<style>
.conversation-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.conversation-item:hover {
    background-color: #f8f9fa;
}

.conversation-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #1976d2;
}

.message-bubble {
    max-width: 70%;
    margin-bottom: 1rem;
}

.message-bubble.sent {
    margin-left: auto;
}

.message-bubble.sent .message-content {
    background-color: #1976d2;
    color: white;
}

.message-bubble.received .message-content {
    background-color: #f1f3f4;
    color: #333;
}

.message-content {
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.unread-indicator {
    width: 8px;
    height: 8px;
    background-color: #f8b500;
    border-radius: 50%;
}

#messages-container {
    scroll-behavior: smooth;
}

.recipient-suggestion {
    cursor: pointer;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
}

.recipient-suggestion:hover {
    background-color: #f8f9fa;
}

.recipient-suggestion.selected {
    background-color: #e3f2fd;
}
</style>

<script>
let currentConversationId = null;
let conversations = [];
let messages = [];
let messagePollingInterval = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadConversations();
    setupMessageForm();
    setupRecipientSearch();
});

// Load conversations
async function loadConversations() {
    try {
        const response = await fetch('/beersty/api/messages.php?action=conversations');
        const data = await response.json();
        
        if (data.success) {
            conversations = data.conversations;
            displayConversations(conversations);
        } else {
            showError('Failed to load conversations');
        }
    } catch (error) {
        console.error('Error loading conversations:', error);
        showError('Failed to load conversations');
    }
}

// Display conversations
function displayConversations(conversations) {
    const container = document.getElementById('conversations-list');
    
    if (conversations.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">No conversations yet</p>
                <small class="text-muted">Start a new conversation!</small>
            </div>
        `;
        return;
    }
    
    container.innerHTML = conversations.map(conversation => {
        const unreadBadge = conversation.unread_count > 0 ? 
            `<span class="badge bg-danger rounded-pill">${conversation.unread_count}</span>` : '';
        
        return `
            <div class="list-group-item conversation-item" data-id="${conversation.id}" 
                 onclick="selectConversation('${conversation.id}')">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" 
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${escapeHtml(conversation.other_participants || 'Group Chat')}</h6>
                        <p class="mb-0 text-muted small">${escapeHtml(conversation.last_message || 'No messages yet')}</p>
                    </div>
                    <div class="text-end">
                        ${unreadBadge}
                        ${conversation.unread_count > 0 ? '<div class="unread-indicator mt-1"></div>' : ''}
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Select conversation
async function selectConversation(conversationId) {
    // Update active conversation
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-id="${conversationId}"]`).classList.add('active');
    
    currentConversationId = conversationId;
    
    // Show chat interface
    document.getElementById('welcome-message').classList.add('d-none');
    document.getElementById('chat-header').classList.remove('d-none');
    document.getElementById('messages-container').classList.remove('d-none');
    document.getElementById('message-input-container').classList.remove('d-none');
    
    // Load messages
    await loadMessages(conversationId);
    
    // Start polling for new messages
    startMessagePolling();
    
    // Update conversation header
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
        document.getElementById('chat-title').textContent = conversation.other_participants || 'Group Chat';
        document.getElementById('chat-subtitle').textContent = `${conversation.participant_count} participants`;
    }
}

// Load messages
async function loadMessages(conversationId) {
    try {
        const response = await fetch(`/beersty/api/messages.php?action=messages&conversation_id=${conversationId}`);
        const data = await response.json();
        
        if (data.success) {
            messages = data.messages;
            displayMessages(messages);
        } else {
            showError('Failed to load messages');
        }
    } catch (error) {
        console.error('Error loading messages:', error);
        showError('Failed to load messages');
    }
}

// Display messages
function displayMessages(messages) {
    const container = document.getElementById('messages-container');
    const currentUserId = '<?php echo $user['id']; ?>';
    
    container.innerHTML = messages.map(message => {
        const isSent = message.sender_id === currentUserId;
        const senderName = message.first_name + ' ' + message.last_name;
        const messageTime = formatMessageTime(message.created_at);
        
        return `
            <div class="message-bubble ${isSent ? 'sent' : 'received'}">
                <div class="message-content">
                    ${!isSent ? `<div class="fw-bold mb-1">${escapeHtml(senderName)}</div>` : ''}
                    <div>${escapeHtml(message.content)}</div>
                </div>
                <div class="message-time ${isSent ? 'text-end' : ''}">${messageTime}</div>
            </div>
        `;
    }).join('');
    
    // Scroll to bottom
    container.scrollTop = container.scrollHeight;
}

// Setup message form
function setupMessageForm() {
    const form = document.getElementById('message-form');
    const input = document.getElementById('message-input');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const content = input.value.trim();
        if (!content || !currentConversationId) return;
        
        try {
            const response = await fetch('/beersty/api/messages.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'send_message',
                    conversation_id: currentConversationId,
                    content: content
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                input.value = '';
                loadMessages(currentConversationId);
                loadConversations(); // Refresh conversation list
            } else {
                showError('Failed to send message');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            showError('Failed to send message');
        }
    });
}

// Setup recipient search
function setupRecipientSearch() {
    const searchInput = document.getElementById('recipient-search');
    const suggestionsContainer = document.getElementById('recipient-suggestions');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            suggestionsContainer.style.display = 'none';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            searchUsers(query);
        }, 300);
    });
}

// Search users
async function searchUsers(query) {
    try {
        const response = await fetch(`/beersty/api/global-search.php?q=${encodeURIComponent(query)}&type=users&limit=5`);
        const data = await response.json();
        
        if (data.success && data.results.users) {
            displayUserSuggestions(data.results.users);
        }
    } catch (error) {
        console.error('Error searching users:', error);
    }
}

// Display user suggestions
function displayUserSuggestions(users) {
    const container = document.getElementById('recipient-suggestions');
    
    if (users.length === 0) {
        container.style.display = 'none';
        return;
    }
    
    container.innerHTML = users.map(user => `
        <div class="recipient-suggestion" onclick="selectRecipient('${user.id}', '${escapeHtml(user.first_name + ' ' + user.last_name)}')">
            <div class="d-flex align-items-center">
                <div class="me-2">
                    <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" 
                         style="width: 30px; height: 30px;">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div>
                    <div class="fw-bold">${escapeHtml(user.first_name + ' ' + user.last_name)}</div>
                    <small class="text-muted">${escapeHtml(user.email)}</small>
                </div>
            </div>
        </div>
    `).join('');
    
    container.style.display = 'block';
}

// Select recipient
function selectRecipient(userId, userName) {
    document.getElementById('recipient-search').value = userName;
    document.getElementById('selected-recipient-id').value = userId;
    document.getElementById('recipient-suggestions').style.display = 'none';
}

// Send new message
async function sendNewMessage() {
    const recipientId = document.getElementById('selected-recipient-id').value;
    const message = document.getElementById('initial-message').value.trim();
    
    if (!recipientId || !message) {
        showError('Please select a recipient and enter a message');
        return;
    }
    
    try {
        const response = await fetch('/beersty/api/messages.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'start_direct_message',
                target_user_id: recipientId,
                message: message
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('newMessageModal'));
            modal.hide();
            
            // Clear form
            document.getElementById('new-message-form').reset();
            document.getElementById('selected-recipient-id').value = '';
            
            // Refresh conversations and select new one
            await loadConversations();
            selectConversation(data.conversation_id);
            
            Beersty.utils.showToast('Message sent successfully', 'success');
        } else {
            showError(data.message || 'Failed to send message');
        }
    } catch (error) {
        console.error('Error sending new message:', error);
        showError('Failed to send message');
    }
}

// Start message polling
function startMessagePolling() {
    stopMessagePolling();
    
    messagePollingInterval = setInterval(() => {
        if (currentConversationId) {
            loadMessages(currentConversationId);
        }
    }, 5000); // Poll every 5 seconds
}

// Stop message polling
function stopMessagePolling() {
    if (messagePollingInterval) {
        clearInterval(messagePollingInterval);
        messagePollingInterval = null;
    }
}

// Utility functions
function formatMessageTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
        return date.toLocaleDateString();
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showError(message) {
    Beersty.utils.showToast(message, 'danger');
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    stopMessagePolling();
});
</script>

<?php include '../includes/footer.php'; ?>
