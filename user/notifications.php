<?php
/**
 * User Notifications Page
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('/auth/login.php');
}

$user = getCurrentUser();
$pageTitle = 'Notifications';

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">🔔 Notifications</h1>
                    <p class="text-muted mb-0">Stay updated with your beer community</p>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-primary me-2" onclick="markAllAsRead()">
                        <i class="fas fa-check-double"></i> Mark All Read
                    </button>
                    <a href="/beersty/user/preferences.php" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
            </div>

            <!-- Notification Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="btn-group" role="group" aria-label="Notification filters">
                                <button type="button" class="btn btn-outline-primary active" data-filter="all">
                                    All
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-filter="unread">
                                    Unread <span id="unread-count-badge" class="badge bg-danger ms-1">0</span>
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-filter="social">
                                    Social
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-filter="achievements">
                                    Achievements
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshNotifications()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div id="notifications-container">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading notifications...</p>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-4" id="load-more-container" style="display: none;">
                <button type="button" class="btn btn-outline-primary" onclick="loadMoreNotifications()">
                    <i class="fas fa-plus"></i> Load More
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.notification-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-item.unread {
    background-color: #f8f9fa;
    border-left-color: #f8b500;
}

.notification-item:hover {
    background-color: #f1f3f4;
    transform: translateX(2px);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.notification-icon.social {
    background-color: #e3f2fd;
    color: #1976d2;
}

.notification-icon.achievement {
    background-color: #fff3e0;
    color: #f57c00;
}

.notification-icon.message {
    background-color: #e8f5e8;
    color: #388e3c;
}

.notification-icon.system {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.notification-time {
    font-size: 0.875rem;
    color: #6c757d;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}
</style>

<script>
let currentFilter = 'all';
let currentOffset = 0;
let isLoading = false;
let hasMore = true;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    setupFilterButtons();
});

// Setup filter buttons
function setupFilterButtons() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update filter and reload
            currentFilter = this.dataset.filter;
            currentOffset = 0;
            hasMore = true;
            loadNotifications();
        });
    });
}

// Load notifications
async function loadNotifications() {
    if (isLoading) return;
    
    isLoading = true;
    
    try {
        let url = '/beersty/api/notifications.php?action=list&limit=20&offset=' + currentOffset;
        
        if (currentFilter === 'unread') {
            url = '/beersty/api/notifications.php?action=unread';
        }
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
            if (currentOffset === 0) {
                displayNotifications(data.notifications);
            } else {
                appendNotifications(data.notifications);
            }
            
            // Update unread count
            updateUnreadCount();
            
            // Show/hide load more button
            if (data.notifications.length < 20) {
                hasMore = false;
                document.getElementById('load-more-container').style.display = 'none';
            } else {
                document.getElementById('load-more-container').style.display = 'block';
            }
        } else {
            showError('Failed to load notifications');
        }
    } catch (error) {
        console.error('Error loading notifications:', error);
        showError('Failed to load notifications');
    } finally {
        isLoading = false;
    }
}

// Display notifications
function displayNotifications(notifications) {
    const container = document.getElementById('notifications-container');
    
    if (notifications.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No notifications</h5>
                <p class="text-muted">You're all caught up!</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
}

// Append notifications for pagination
function appendNotifications(notifications) {
    const container = document.getElementById('notifications-container');
    const newHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
    container.insertAdjacentHTML('beforeend', newHTML);
}

// Create notification HTML
function createNotificationHTML(notification) {
    const isUnread = !notification.is_read;
    const timeAgo = formatTimeAgo(notification.created_at);
    const iconClass = getNotificationIcon(notification.type);
    const iconBg = getNotificationIconBg(notification.type);
    
    return `
        <div class="card mb-3 notification-item ${isUnread ? 'unread' : ''}" data-id="${notification.id}">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="notification-icon ${iconBg}">
                            <i class="${iconClass}"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h6 class="mb-1 fw-bold">${escapeHtml(notification.title)}</h6>
                        <p class="mb-1">${escapeHtml(notification.message)}</p>
                        <small class="notification-time">${timeAgo}</small>
                    </div>
                    <div class="col-auto notification-actions">
                        ${isUnread ? `
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" 
                                    onclick="markAsRead('${notification.id}')">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                ${isUnread ? `
                                    <li><a class="dropdown-item" href="#" onclick="markAsRead('${notification.id}')">
                                        <i class="fas fa-check me-2"></i>Mark as read
                                    </a></li>
                                ` : ''}
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification('${notification.id}')">
                                    <i class="fas fa-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Get notification icon
function getNotificationIcon(type) {
    const icons = {
        'new_follower': 'fas fa-user-plus',
        'friend_checkin': 'fas fa-map-marker-alt',
        'beer_release': 'fas fa-beer',
        'brewery_event': 'fas fa-calendar-alt',
        'achievement_unlocked': 'fas fa-trophy',
        'message_received': 'fas fa-envelope',
        'rating_liked': 'fas fa-heart',
        'comment_received': 'fas fa-comment',
        'badge_earned': 'fas fa-medal'
    };
    return icons[type] || 'fas fa-bell';
}

// Get notification icon background class
function getNotificationIconBg(type) {
    if (['new_follower', 'friend_checkin', 'rating_liked', 'comment_received'].includes(type)) {
        return 'social';
    }
    if (['achievement_unlocked', 'badge_earned'].includes(type)) {
        return 'achievement';
    }
    if (['message_received'].includes(type)) {
        return 'message';
    }
    return 'system';
}

// Mark notification as read
async function markAsRead(notificationId) {
    try {
        const response = await fetch('/beersty/api/notifications.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'mark_read',
                notification_id: notificationId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update UI
            const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.remove('unread');
                notificationElement.querySelector('.notification-actions').innerHTML = 
                    notificationElement.querySelector('.notification-actions').innerHTML.replace(/Mark as read.*?<\/a><\/li>/, '');
            }
            
            updateUnreadCount();
            Beersty.utils.showToast('Notification marked as read', 'success');
        } else {
            showError('Failed to mark notification as read');
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
        showError('Failed to mark notification as read');
    }
}

// Mark all notifications as read
async function markAllAsRead() {
    try {
        const response = await fetch('/beersty/api/notifications.php', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'mark_all_read'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update UI
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
            });
            
            updateUnreadCount();
            Beersty.utils.showToast('All notifications marked as read', 'success');
        } else {
            showError('Failed to mark all notifications as read');
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        showError('Failed to mark all notifications as read');
    }
}

// Update unread count
async function updateUnreadCount() {
    try {
        const response = await fetch('/beersty/api/notifications.php?action=count');
        const data = await response.json();
        
        if (data.success) {
            const badge = document.getElementById('unread-count-badge');
            if (badge) {
                badge.textContent = data.unread_count;
                badge.style.display = data.unread_count > 0 ? 'inline' : 'none';
            }
        }
    } catch (error) {
        console.error('Error updating unread count:', error);
    }
}

// Load more notifications
function loadMoreNotifications() {
    if (!hasMore || isLoading) return;
    
    currentOffset += 20;
    loadNotifications();
}

// Refresh notifications
function refreshNotifications() {
    currentOffset = 0;
    hasMore = true;
    loadNotifications();
}

// Utility functions
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
    if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + ' days ago';
    
    return date.toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showError(message) {
    Beersty.utils.showToast(message, 'danger');
}
</script>

<?php include '../includes/footer.php'; ?>
