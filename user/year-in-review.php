<?php
/**
 * User Year in Review
 * Phase 8: Analytics & Business Intelligence
 */

require_once '../config/config.php';
require_once '../includes/AnalyticsService.php';
require_once '../includes/BadgeService.php';

// Require login
requireLogin();

$pageTitle = 'Year in Review - ' . APP_NAME;
$additionalCSS = ['/assets/css/statistics.css'];

$user = getCurrentUser();
$userId = $user['id'];

// Get year from request, default to current year
$year = (int)($_GET['year'] ?? date('Y'));
$currentYear = (int)date('Y');

// Validate year
if ($year < 2020 || $year > $currentYear) {
    $year = $currentYear;
}

$yearStats = [];
$yearHighlights = [];
$yearBadges = [];
$yearJourney = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $analyticsService = new AnalyticsService($conn);
    $badgeService = new BadgeService($conn);
    
    // Get year-specific analytics
    $yearStats = $analyticsService->getYearInReview($userId, $year);
    $yearHighlights = $analyticsService->getYearHighlights($userId, $year);
    $yearBadges = $badgeService->getYearBadges($userId, $year);
    $yearJourney = $analyticsService->getYearJourney($userId, $year);
    
} catch (Exception $e) {
    error_log("Year in review error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading year in review data.';
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-3">
                <i class="fas fa-calendar-alt me-3"></i><?php echo $year; ?> Year in Review
            </h1>
            <p class="lead text-muted mb-4">
                Your beer journey through <?php echo $year; ?>
            </p>
            
            <!-- Year Selector -->
            <div class="d-flex justify-content-center mb-4">
                <select class="form-select w-auto" id="year-selector" onchange="changeYear()">
                    <?php for ($y = $currentYear; $y >= 2020; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo $y === $year ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
        </div>
    </div>

    <?php if (empty($yearStats) || ($yearStats['total_checkins'] ?? 0) === 0): ?>
    <!-- No Data Message -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center py-5">
                <div class="card-body">
                    <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                    <h3 class="text-muted">No Beer Journey in <?php echo $year; ?></h3>
                    <p class="text-muted mb-4">
                        You didn't log any beer activities in <?php echo $year; ?>. 
                        <?php if ($year === $currentYear): ?>
                            Start your beer journey today!
                        <?php endif; ?>
                    </p>
                    <?php if ($year === $currentYear): ?>
                    <a href="/beersty/social/checkin.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Check In Your First Beer
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>

    <!-- Year Overview Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card text-center year-card">
                <div class="card-body">
                    <div class="year-icon mb-3">
                        <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                    </div>
                    <h2 class="year-number"><?php echo number_format($yearStats['total_checkins'] ?? 0); ?></h2>
                    <p class="year-label">Check-ins</p>
                    <small class="text-muted">
                        <?php echo number_format(($yearStats['total_checkins'] ?? 0) / 12, 1); ?> per month
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center year-card">
                <div class="card-body">
                    <div class="year-icon mb-3">
                        <i class="fas fa-beer fa-2x text-warning"></i>
                    </div>
                    <h2 class="year-number"><?php echo number_format($yearStats['unique_beers'] ?? 0); ?></h2>
                    <p class="year-label">Unique Beers</p>
                    <small class="text-muted">
                        <?php echo number_format(($yearStats['unique_beers'] ?? 0) / 52, 1); ?> per week
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center year-card">
                <div class="card-body">
                    <div class="year-icon mb-3">
                        <i class="fas fa-industry fa-2x text-success"></i>
                    </div>
                    <h2 class="year-number"><?php echo number_format($yearStats['unique_breweries'] ?? 0); ?></h2>
                    <p class="year-label">Breweries</p>
                    <small class="text-muted">
                        Explored <?php echo number_format($yearStats['unique_breweries'] ?? 0); ?> different breweries
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card text-center year-card">
                <div class="card-body">
                    <div class="year-icon mb-3">
                        <i class="fas fa-trophy fa-2x text-danger"></i>
                    </div>
                    <h2 class="year-number"><?php echo number_format(count($yearBadges)); ?></h2>
                    <p class="year-label">Badges Earned</p>
                    <small class="text-muted">
                        New achievements unlocked
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Year Highlights -->
    <?php if (!empty($yearHighlights)): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i><?php echo $year; ?> Highlights
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Favorite Beer -->
                        <?php if (!empty($yearHighlights['favorite_beer'])): ?>
                        <div class="col-lg-4 mb-4">
                            <div class="highlight-item">
                                <div class="highlight-icon">
                                    <i class="fas fa-heart text-danger"></i>
                                </div>
                                <div class="highlight-content">
                                    <h6 class="highlight-title">Favorite Beer</h6>
                                    <div class="highlight-value">
                                        <?php echo htmlspecialchars($yearHighlights['favorite_beer']['name']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($yearHighlights['favorite_beer']['brewery']); ?> • 
                                        <?php echo number_format($yearHighlights['favorite_beer']['rating'], 1); ?>/5
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Most Active Month -->
                        <?php if (!empty($yearHighlights['most_active_month'])): ?>
                        <div class="col-lg-4 mb-4">
                            <div class="highlight-item">
                                <div class="highlight-icon">
                                    <i class="fas fa-calendar text-primary"></i>
                                </div>
                                <div class="highlight-content">
                                    <h6 class="highlight-title">Most Active Month</h6>
                                    <div class="highlight-value">
                                        <?php echo date('F', mktime(0, 0, 0, $yearHighlights['most_active_month']['month'], 1)); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo number_format($yearHighlights['most_active_month']['checkins']); ?> check-ins
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Favorite Style -->
                        <?php if (!empty($yearHighlights['favorite_style'])): ?>
                        <div class="col-lg-4 mb-4">
                            <div class="highlight-item">
                                <div class="highlight-icon">
                                    <i class="fas fa-palette text-warning"></i>
                                </div>
                                <div class="highlight-content">
                                    <h6 class="highlight-title">Favorite Style</h6>
                                    <div class="highlight-value">
                                        <?php echo htmlspecialchars($yearHighlights['favorite_style']['name']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo number_format($yearHighlights['favorite_style']['count']); ?> beers tried
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Monthly Journey -->
    <?php if (!empty($yearJourney)): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-route me-2"></i>Your <?php echo $year; ?> Beer Journey
                    </h5>
                </div>
                <div class="card-body">
                    <div class="journey-timeline">
                        <?php foreach ($yearJourney as $month => $data): ?>
                        <div class="journey-month">
                            <div class="journey-month-header">
                                <h6 class="fw-bold"><?php echo date('F', mktime(0, 0, 0, $month, 1)); ?></h6>
                                <span class="badge bg-primary"><?php echo $data['checkins']; ?> check-ins</span>
                            </div>
                            <div class="journey-month-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Unique Beers:</small>
                                        <strong><?php echo $data['unique_beers']; ?></strong>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Avg Rating:</small>
                                        <strong><?php echo number_format($data['avg_rating'], 1); ?>/5</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Year Badges -->
    <?php if (!empty($yearBadges)): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>Badges Earned in <?php echo $year; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($yearBadges as $badge): ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="badge-card">
                                <div class="badge-icon-large">
                                    <?php echo $badge['icon']; ?>
                                </div>
                                <div class="badge-info">
                                    <h6 class="badge-name"><?php echo htmlspecialchars($badge['name']); ?></h6>
                                    <p class="badge-description"><?php echo htmlspecialchars($badge['description']); ?></p>
                                    <small class="text-muted">
                                        Earned <?php echo date('M j', strtotime($badge['earned_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Share Your Year -->
    <div class="row">
        <div class="col-12">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-share me-2"></i>Share Your <?php echo $year; ?> Beer Journey
                    </h5>
                    <p class="card-text text-muted">
                        Let your friends know about your amazing beer discoveries in <?php echo $year; ?>!
                    </p>
                    <div class="d-flex justify-content-center gap-2">
                        <button class="btn btn-primary" onclick="shareYearInReview()">
                            <i class="fas fa-share-alt me-2"></i>Share
                        </button>
                        <a href="statistics.php" class="btn btn-outline-secondary">
                            <i class="fas fa-chart-line me-2"></i>View Full Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>
</div>

<script>
function changeYear() {
    const year = document.getElementById('year-selector').value;
    const url = new URL(window.location);
    url.searchParams.set('year', year);
    window.location.href = url.toString();
}

function shareYearInReview() {
    const year = <?php echo $year; ?>;
    const checkins = <?php echo $yearStats['total_checkins'] ?? 0; ?>;
    const uniqueBeers = <?php echo $yearStats['unique_beers'] ?? 0; ?>;
    const breweries = <?php echo $yearStats['unique_breweries'] ?? 0; ?>;
    
    const shareText = `🍺 My ${year} Beer Journey on Beersty:\n` +
                     `✅ ${checkins} check-ins\n` +
                     `🍻 ${uniqueBeers} unique beers\n` +
                     `🏭 ${breweries} breweries explored\n\n` +
                     `Join me on Beersty! #BeerJourney #Beersty`;
    
    if (navigator.share) {
        navigator.share({
            title: `My ${year} Beer Journey`,
            text: shareText,
            url: window.location.href
        });
    } else {
        // Fallback to copying to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            Beersty.utils.showToast('Year in review copied to clipboard!', 'success');
        });
    }
}
</script>

<style>
.year-card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.year-card:hover {
    transform: translateY(-5px);
}

.year-icon {
    padding: 20px;
    border-radius: 50%;
    background: rgba(248, 181, 0, 0.1);
}

.year-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #f8b500;
    margin-bottom: 0;
}

.year-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.highlight-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.highlight-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.highlight-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #212529;
}

.journey-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.journey-month {
    padding: 15px;
    border-radius: 8px;
    background: #f8f9fa;
    border-left: 4px solid #f8b500;
}

.journey-month-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.badge-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease;
}

.badge-card:hover {
    transform: translateY(-2px);
}

.badge-icon-large {
    font-size: 3rem;
    margin-bottom: 15px;
}

.badge-name {
    font-weight: bold;
    color: #495057;
    margin-bottom: 10px;
}

.badge-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .year-number {
        font-size: 2rem;
    }
    
    .highlight-item {
        flex-direction: column;
        text-align: center;
    }
    
    .highlight-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
