<?php
/**
 * User Dashboard
 * Shows user's saved places, deals, social stats, and activity
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ' . url('auth/login.php'));
    exit;
}

$user = getCurrentUser();
$pageTitle = 'My Dashboard - ' . APP_NAME;

// Get user statistics
try {
    // Get saved places count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM saved_places WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $savedPlacesCount = $stmt->fetchColumn();
    
    // Get saved deals count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM saved_deals WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $savedDealsCount = $stmt->fetchColumn();
    
    // Get following places count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM place_follows WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $followingPlacesCount = $stmt->fetchColumn();
    
    // Get followers count (people following user)
    $stmt = $conn->prepare("SELECT COUNT(*) FROM user_follows WHERE following_id = ?");
    $stmt->execute([$user['id']]);
    $followersCount = $stmt->fetchColumn();
    
    // Get following users count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM user_follows WHERE follower_id = ?");
    $stmt->execute([$user['id']]);
    $followingUsersCount = $stmt->fetchColumn();
    
    // Get recent saved places
    $stmt = $conn->prepare("
        SELECT p.*, sp.saved_at 
        FROM saved_places sp 
        JOIN places p ON sp.place_id = p.id 
        WHERE sp.user_id = ? 
        ORDER BY sp.saved_at DESC 
        LIMIT 6
    ");
    $stmt->execute([$user['id']]);
    $recentSavedPlaces = $stmt->fetchAll();
    
    // Get recent saved deals
    $stmt = $conn->prepare("
        SELECT d.*, p.name as place_name, sd.saved_at 
        FROM saved_deals sd 
        JOIN deals d ON sd.deal_id = d.id 
        JOIN places p ON d.place_id = p.id 
        WHERE sd.user_id = ? AND d.status = 'active' AND d.valid_until >= CURDATE()
        ORDER BY sd.saved_at DESC 
        LIMIT 6
    ");
    $stmt->execute([$user['id']]);
    $recentSavedDeals = $stmt->fetchAll();
    
    // Get recent activity
    $stmt = $conn->prepare("
        SELECT * FROM user_activities 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user['id']]);
    $recentActivity = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $savedPlacesCount = $savedDealsCount = $followingPlacesCount = 0;
    $followersCount = $followingUsersCount = 0;
    $recentSavedPlaces = $recentSavedDeals = $recentActivity = [];
}

require_once '../includes/header.php';
?>

<!-- Dashboard Header -->
<section class="dashboard-header bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-2">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                    Welcome back, <?php echo htmlspecialchars($user['first_name'] ?: 'Beer Lover'); ?>!
                </h1>
                <p class="text-muted mb-0">Here's what's happening in your beer world</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="quick-actions">
                    <a href="/social/checkin/" class="btn btn-primary me-2">
                        <i class="fas fa-check-circle me-1"></i>Check In
                    </a>
                    <a href="/places/search.php" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>Find Places
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="dashboard-content py-4">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Stats Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-md-4">
                        <div class="stat-card card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="stat-icon mb-3">
                                    <i class="fas fa-bookmark fa-2x text-primary"></i>
                                </div>
                                <h4 class="stat-number text-primary"><?php echo number_format($savedPlacesCount); ?></h4>
                                <p class="stat-label text-muted mb-0">Saved Places</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="stat-icon mb-3">
                                    <i class="fas fa-tags fa-2x text-success"></i>
                                </div>
                                <h4 class="stat-number text-success"><?php echo number_format($savedDealsCount); ?></h4>
                                <p class="stat-label text-muted mb-0">Saved Deals</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="stat-icon mb-3">
                                    <i class="fas fa-heart fa-2x text-danger"></i>
                                </div>
                                <h4 class="stat-number text-danger"><?php echo number_format($followingPlacesCount); ?></h4>
                                <p class="stat-label text-muted mb-0">Following</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Saved Places -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-bookmark me-2"></i>Recently Saved Places
                        </h5>
                        <a href="/user/saved-places.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentSavedPlaces)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No saved places yet</p>
                                <a href="/places/search.php" class="btn btn-primary">Discover Places</a>
                            </div>
                        <?php else: ?>
                            <div class="row g-3">
                                <?php foreach ($recentSavedPlaces as $place): ?>
                                    <div class="col-md-6">
                                        <div class="place-item d-flex align-items-center p-3 border rounded">
                                            <div class="place-image me-3">
                                                <img src="https://via.placeholder.com/60x60?text=<?php echo urlencode(substr($place['name'], 0, 1)); ?>" 
                                                     class="rounded" width="60" height="60" alt="<?php echo htmlspecialchars($place['name']); ?>">
                                            </div>
                                            <div class="place-info flex-grow-1">
                                                <h6 class="mb-1">
                                                    <a href="/places/profile.php?id=<?php echo $place['id']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($place['name']); ?>
                                                    </a>
                                                </h6>
                                                <p class="text-muted small mb-1"><?php echo htmlspecialchars($place['type']); ?></p>
                                                <small class="text-muted">Saved <?php echo formatDateTime($place['saved_at']); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Saved Deals -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>Active Saved Deals
                        </h5>
                        <a href="/user/saved-deals.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentSavedDeals)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No saved deals yet</p>
                                <a href="/places/search.php" class="btn btn-primary">Find Deals</a>
                            </div>
                        <?php else: ?>
                            <div class="row g-3">
                                <?php foreach ($recentSavedDeals as $deal): ?>
                                    <div class="col-md-6">
                                        <div class="deal-item border rounded p-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($deal['title']); ?></h6>
                                                <span class="badge bg-success">Active</span>
                                            </div>
                                            <p class="text-muted small mb-2"><?php echo htmlspecialchars($deal['place_name']); ?></p>
                                            <p class="mb-2"><?php echo htmlspecialchars($deal['description']); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    Expires: <?php echo date('M j, Y', strtotime($deal['valid_until'])); ?>
                                                </small>
                                                <code class="bg-light px-2 py-1 rounded"><?php echo $deal['qr_code']; ?></code>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Social Stats -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>Social Stats
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="social-stat">
                                    <h4 class="text-primary mb-1"><?php echo number_format($followersCount); ?></h4>
                                    <small class="text-muted">Followers</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="social-stat">
                                    <h4 class="text-success mb-1"><?php echo number_format($followingUsersCount); ?></h4>
                                    <small class="text-muted">Following</small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <a href="/social/discover-users.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-friends me-2"></i>Find Friends
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Recent Activity
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentActivity)): ?>
                            <p class="text-muted text-center">No recent activity</p>
                        <?php else: ?>
                            <div class="activity-list">
                                <?php foreach (array_slice($recentActivity, 0, 5) as $activity): ?>
                                    <div class="activity-item d-flex align-items-center mb-3">
                                        <div class="activity-icon me-3">
                                            <?php
                                            $iconClass = 'fas fa-circle';
                                            $iconColor = 'text-muted';
                                            
                                            switch ($activity['activity_type']) {
                                                case 'place_follow':
                                                    $iconClass = 'fas fa-heart';
                                                    $iconColor = 'text-danger';
                                                    break;
                                                case 'place_save':
                                                    $iconClass = 'fas fa-bookmark';
                                                    $iconColor = 'text-primary';
                                                    break;
                                                case 'deal_save':
                                                    $iconClass = 'fas fa-tags';
                                                    $iconColor = 'text-success';
                                                    break;
                                                case 'user_follow':
                                                    $iconClass = 'fas fa-user-plus';
                                                    $iconColor = 'text-info';
                                                    break;
                                            }
                                            ?>
                                            <i class="<?php echo $iconClass; ?> <?php echo $iconColor; ?>"></i>
                                        </div>
                                        <div class="activity-content flex-grow-1">
                                            <p class="mb-1 small">
                                                <?php
                                                $metadata = json_decode($activity['metadata'], true);
                                                switch ($activity['activity_type']) {
                                                    case 'place_follow':
                                                        echo 'Followed ' . htmlspecialchars($metadata['place_name'] ?? 'a place');
                                                        break;
                                                    case 'place_save':
                                                        echo 'Saved ' . htmlspecialchars($metadata['place_name'] ?? 'a place');
                                                        break;
                                                    case 'deal_save':
                                                        echo 'Saved deal at ' . htmlspecialchars($metadata['place_name'] ?? 'a place');
                                                        break;
                                                    case 'user_follow':
                                                        echo 'Followed ' . htmlspecialchars($metadata['target_user_name'] ?? 'a user');
                                                        break;
                                                    default:
                                                        echo ucfirst(str_replace('_', ' ', $activity['activity_type']));
                                                }
                                                ?>
                                            </p>
                                            <small class="text-muted"><?php echo formatDateTime($activity['created_at']); ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center">
                                <a href="/social/activity-feed.php" class="btn btn-sm btn-outline-primary">View All Activity</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>
