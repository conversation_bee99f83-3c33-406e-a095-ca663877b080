<?php
require_once '../config/config.php';
require_once '../includes/PhotoManager.php';
require_once '../components/photo-gallery.php';

// Require login
requireLogin();

$pageTitle = 'My Photos - ' . APP_NAME;
$additionalCSS = ['/assets/css/lists.css', '/assets/css/beersty-layouts.css'];
$additionalJS = ['/assets/js/photo-gallery.js'];

$user = getCurrentUser();
$userId = $user['id'];

$photoType = sanitizeInput($_GET['type'] ?? 'all');
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 24;
$offset = ($page - 1) * $limit;

try {
    $photoManager = new PhotoManager();
    $db = new Database();
    $conn = $db->getConnection();
    
    // Build query based on type filter
    $whereClause = "WHERE p.user_id = ?";
    $params = [$userId];
    
    if ($photoType !== 'all') {
        $whereClause .= " AND p.type = ?";
        $params[] = $photoType;
    }
    
    // Get photos with pagination
    $stmt = $conn->prepare("
        SELECT p.*, 
               bm.name as beer_name,
               b.name as brewery_name,
               COUNT(*) OVER() as total_count
        FROM photos p
        LEFT JOIN beer_menu bm ON p.type = 'beer' AND p.target_id = bm.id
        LEFT JOIN breweries b ON p.type = 'brewery' AND p.target_id = b.id
        {$whereClause}
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
    ");
    
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $photos = $stmt->fetchAll();
    
    $totalPhotos = $photos[0]['total_count'] ?? 0;
    $totalPages = ceil($totalPhotos / $limit);
    
    // Get photo statistics
    $stmt = $conn->prepare("
        SELECT 
            p.type,
            COUNT(*) as count
        FROM photos p
        WHERE p.user_id = ?
        GROUP BY p.type
        ORDER BY count DESC
    ");
    $stmt->execute([$userId]);
    $photoStats = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("User photos error: " . $e->getMessage());
    $photos = [];
    $totalPhotos = 0;
    $totalPages = 0;
    $photoStats = [];
}

include '../includes/header.php';
?>

<!-- Apply brewery theme background -->
<style>
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}
</style>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 fw-bold mb-2" style="color: #F5F5DC;">
                        <i class="fas fa-images me-3" style="color: #FFC107;"></i>My Photos
                    </h1>
                    <p class="lead" style="color: #D69A6B;">
                        Manage your beer photos and memories
                    </p>
                </div>
                <button class="btn" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;" onclick="openPhotoUpload('general', '')">
                    <i class="fas fa-plus me-2"></i>Upload Photos
                </button>
            </div>
        </div>
    </div>
    
    <!-- Photo Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
                <div class="card-body">
                    <h6 class="card-title" style="color: #F5F5DC;">
                        <i class="fas fa-chart-bar me-2" style="color: #FFC107;"></i>Photo Statistics
                    </h6>
                    <div class="row g-3">
                        <div class="col-md-2">
                            <div class="stat-item text-center">
                                <div class="stat-number" style="color: #FFC107; font-weight: bold;"><?php echo number_format($totalPhotos); ?></div>
                                <div class="stat-label" style="color: #D69A6B;">Total Photos</div>
                            </div>
                        </div>
                        <?php foreach ($photoStats as $stat): ?>
                            <div class="col-md-2">
                                <div class="stat-item text-center">
                                    <div class="stat-number" style="color: #FFC107; font-weight: bold;"><?php echo number_format($stat['count']); ?></div>
                                    <div class="stat-label" style="color: #D69A6B;"><?php echo ucfirst($stat['type']); ?> Photos</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link <?php echo $photoType === 'all' ? 'active' : ''; ?>"
                       href="?type=all"
                       style="<?php echo $photoType === 'all' ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border: 1px solid #D69A6B;'; ?>">
                        <i class="fas fa-images me-1"></i>All Photos
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $photoType === 'checkin' ? 'active' : ''; ?>"
                       href="?type=checkin"
                       style="<?php echo $photoType === 'checkin' ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border: 1px solid #D69A6B;'; ?>">
                        <i class="fas fa-map-marker-alt me-1"></i>Check-ins
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $photoType === 'beer' ? 'active' : ''; ?>"
                       href="?type=beer"
                       style="<?php echo $photoType === 'beer' ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border: 1px solid #D69A6B;'; ?>">
                        <i class="fas fa-beer me-1"></i>Beers
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $photoType === 'brewery' ? 'active' : ''; ?>"
                       href="?type=brewery"
                       style="<?php echo $photoType === 'brewery' ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border: 1px solid #D69A6B;'; ?>">
                        <i class="fas fa-industry me-1"></i>Breweries
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $photoType === 'general' ? 'active' : ''; ?>"
                       href="?type=general"
                       style="<?php echo $photoType === 'general' ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border: 1px solid #D69A6B;'; ?>">
                        <i class="fas fa-camera me-1"></i>General
                    </a>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- Photo Grid -->
    <div class="row">
        <div class="col-12">
            <?php if (empty($photos)): ?>
                <div class="empty-state text-center py-5">
                    <i class="fas fa-camera fa-4x mb-4" style="color: #D69A6B;"></i>
                    <h5 style="color: #F5F5DC;">No photos yet</h5>
                    <p class="mb-4" style="color: #D69A6B;">
                        Start sharing your beer experiences with photos!
                    </p>
                    <button class="btn" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;" onclick="openPhotoUpload('general', '')">
                        <i class="fas fa-camera me-2"></i>Upload Your First Photo
                    </button>
                </div>
            <?php else: ?>
                <div class="photo-gallery" id="user-photos-gallery">
                    <div class="row g-3 photo-grid" data-columns="4">
                        <?php foreach ($photos as $index => $photo): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="photo-item" data-photo-id="<?php echo $photo['id']; ?>">
                                    <div class="photo-card" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
                                        <div class="photo-image-container">
                                            <img src="<?php echo htmlspecialchars($photo['thumbnail_path'] ? '/' . $photo['thumbnail_path'] : '/' . $photo['file_path']); ?>"
                                                 alt="<?php echo htmlspecialchars($photo['alt_text'] ?: $photo['title'] ?: 'Photo'); ?>"
                                                 class="photo-image"
                                                 onclick="openLightbox('user-photos-gallery', <?php echo $index; ?>)"
                                                 loading="lazy"
                                                 style="border-radius: 8px;">

                                            <!-- Photo Overlay -->
                                            <div class="photo-overlay">
                                                <div class="photo-actions">
                                                    <button class="btn btn-sm" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;" onclick="openLightbox('user-photos-gallery', <?php echo $index; ?>)">
                                                        <i class="fas fa-expand"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="deletePhoto('<?php echo $photo['id']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>

                                                <?php if ($photo['like_count'] > 0): ?>
                                                    <div class="photo-likes" style="color: #FFC107;">
                                                        <i class="fas fa-heart" style="color: #FFC107;"></i>
                                                        <?php echo number_format($photo['like_count']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="photo-info" style="padding: 0.75rem;">
                                            <?php if ($photo['title']): ?>
                                                <h6 class="photo-title" style="color: #F5F5DC;"><?php echo htmlspecialchars($photo['title']); ?></h6>
                                            <?php endif; ?>

                                            <div class="photo-meta">
                                                <small style="color: #D69A6B;">
                                                    <i class="fas fa-tag me-1" style="color: #FFC107;"></i>
                                                    <?php echo ucfirst($photo['type']); ?>
                                                </small>

                                                <?php if ($photo['beer_name']): ?>
                                                    <small style="color: #D69A6B;" class="ms-2">
                                                        <i class="fas fa-beer me-1" style="color: #FFC107;"></i>
                                                        <?php echo htmlspecialchars($photo['beer_name']); ?>
                                                    </small>
                                                <?php elseif ($photo['brewery_name']): ?>
                                                    <small style="color: #D69A6B;" class="ms-2">
                                                        <i class="fas fa-industry me-1" style="color: #FFC107;"></i>
                                                        <?php echo htmlspecialchars($photo['brewery_name']); ?>
                                                    </small>
                                                <?php endif; ?>
                                                
                                                <small class="text-muted d-block mt-1">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo formatDateTime($photo['created_at']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Photo pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?type=<?php echo $photoType; ?>&page=<?php echo $page - 1; ?>"
                                       style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?type=<?php echo $photoType; ?>&page=<?php echo $i; ?>"
                                       style="<?php echo $i === $page ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;'; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?type=<?php echo $photoType; ?>&page=<?php echo $page + 1; ?>"
                                       style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Brewery-themed hover effects for photos page */
.nav-pills .nav-link:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.photo-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 12px;
    overflow: hidden;
}

.photo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.3) !important;
    border-color: #FFC107 !important;
}

.photo-image {
    transition: transform 0.3s ease;
}

.photo-card:hover .photo-image {
    transform: scale(1.05);
}

.photo-overlay {
    background: linear-gradient(to top, rgba(59, 42, 42, 0.8) 0%, transparent 100%);
}

.btn:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: rgba(59, 42, 42, 0.1);
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 193, 7, 0.1);
}

.photo-actions .btn {
    margin: 0 2px;
    backdrop-filter: blur(10px);
}

.empty-state {
    background: rgba(111, 76, 62, 0.1);
    border-radius: 12px;
    border: 2px dashed #D69A6B;
}
</style>

<!-- Lightbox Modal -->
<div class="modal" id="lightbox-user-photos-gallery" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content bg-dark">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="lightbox-title"></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div class="lightbox-container">
                    <img id="lightbox-image" src="" alt="" class="img-fluid w-100">
                    
                    <!-- Navigation -->
                    <button class="lightbox-nav lightbox-prev" onclick="navigateLightbox('user-photos-gallery', -1)">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="lightbox-nav lightbox-next" onclick="navigateLightbox('user-photos-gallery', 1)">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    
                    <!-- Photo Info Overlay -->
                    <div class="lightbox-info">
                        <div id="lightbox-description"></div>
                        <div id="lightbox-meta" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Store photo data for lightbox
window.galleryData = window.galleryData || {};
window.galleryData['user-photos-gallery'] = <?php echo json_encode($photos); ?>;

// Delete photo function
async function deletePhoto(photoId) {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch('/api/delete-photo.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ photo_id: photoId })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Remove photo from page
            const photoElement = document.querySelector(`[data-photo-id="${photoId}"]`);
            if (photoElement) {
                photoElement.closest('.col-lg-3').remove();
            }
            
            // Show success message
            alert('Photo deleted successfully');
        } else {
            alert(result.message || 'Failed to delete photo');
        }
        
    } catch (error) {
        console.error('Delete error:', error);
        alert('Failed to delete photo');
    }
}
</script>

<?php include '../includes/footer.php'; ?>
