<?php
require_once '../config/config.php';
require_once '../includes/BadgeService.php';
require_once '../includes/AnalyticsService.php';

// Require login
requireLogin();

$pageTitle = 'My Analytics - ' . APP_NAME;
$additionalCSS = ['/assets/css/statistics.css'];

$user = getCurrentUser();
$userId = $user['id'];

$userStats = [];
$badges = [];
$recentActivity = [];
$analytics = [];

// Get timeframe from request
$timeframe = sanitizeInput($_GET['timeframe'] ?? '1_year');
$validTimeframes = ['1_month', '3_months', '6_months', '1_year', 'all_time'];
if (!in_array($timeframe, $validTimeframes)) {
    $timeframe = '1_year';
}

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Initialize services
    $badgeService = new BadgeService($conn);
    $analyticsService = new AnalyticsService($conn);

    // Calculate user statistics
    $badgeService->calculateUserStatistics($userId);

    // Get user statistics
    $stmt = $conn->prepare("SELECT * FROM user_statistics WHERE user_id = ?");
    $stmt->execute([$userId]);
    $userStats = $stmt->fetch() ?: [];

    // Get comprehensive analytics
    $analytics = $analyticsService->getUserAnalytics($userId, $timeframe);
    
    // Get user's badges
    $badges = $badgeService->getUserBadges($userId);
    
    // Get recent activity
    $stmt = $conn->prepare("
        SELECT ua.*, 
               CASE 
                   WHEN ua.activity_type = 'badge_earned' THEN b.name
                   ELSE NULL 
               END as badge_name
        FROM user_activities ua
        LEFT JOIN badges b ON ua.target_id = b.id AND ua.target_type = 'badge'
        WHERE ua.user_id = ? AND ua.is_public = 1
        ORDER BY ua.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$userId]);
    $recentActivity = $stmt->fetchAll();
    
    // Get favorite beer style
    $favoriteStyle = null;
    if (!empty($userStats['favorite_beer_style_id'])) {
        $stmt = $conn->prepare("SELECT name FROM beer_styles WHERE id = ?");
        $stmt->execute([$userStats['favorite_beer_style_id']]);
        $favoriteStyle = $stmt->fetchColumn();
    }
    
    // Calculate additional stats
    $stmt = $conn->prepare("
        SELECT 
            COUNT(DISTINCT DATE(created_at)) as active_days,
            COUNT(CASE WHEN DAYOFWEEK(created_at) IN (1,7) THEN 1 END) as weekend_checkins,
            COUNT(CASE WHEN HOUR(created_at) BETWEEN 17 AND 23 THEN 1 END) as evening_checkins
        FROM beer_checkins 
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $additionalStats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Statistics page error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading statistics.';
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-chart-line me-3"></i>My Analytics Dashboard
            </h1>
            <p class="lead text-muted">
                Advanced insights into your beer journey and drinking patterns
            </p>
        </div>
        <div class="col-lg-4 d-flex align-items-center justify-content-end">
            <div class="d-flex gap-2">
                <!-- Timeframe Selector -->
                <select class="form-select" id="timeframe-selector" onchange="changeTimeframe()">
                    <option value="1_month" <?php echo $timeframe === '1_month' ? 'selected' : ''; ?>>Last Month</option>
                    <option value="3_months" <?php echo $timeframe === '3_months' ? 'selected' : ''; ?>>Last 3 Months</option>
                    <option value="6_months" <?php echo $timeframe === '6_months' ? 'selected' : ''; ?>>Last 6 Months</option>
                    <option value="1_year" <?php echo $timeframe === '1_year' ? 'selected' : ''; ?>>Last Year</option>
                    <option value="all_time" <?php echo $timeframe === 'all_time' ? 'selected' : ''; ?>>All Time</option>
                </select>
                <a href="year-in-review.php" class="btn btn-warning me-2">
                    <i class="fas fa-calendar-alt me-2"></i>Year in Review
                </a>
                <a href="profile.php" class="btn btn-outline-primary">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3 col-sm-6">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($userStats['total_checkins'] ?? 0); ?></h3>
                    <p class="stat-label">Total Check-ins</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-beer fa-2x text-warning"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($userStats['unique_beers_tried'] ?? 0); ?></h3>
                    <p class="stat-label">Unique Beers</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-industry fa-2x text-success"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($userStats['unique_breweries_visited'] ?? 0); ?></h3>
                    <p class="stat-label">Breweries Visited</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-trophy fa-2x text-danger"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($userStats['total_badges_earned'] ?? 0); ?></h3>
                    <p class="stat-label">Badges Earned</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Statistics -->
    <div class="row g-4 mb-5">
        <!-- Rating Statistics -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Rating Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo number_format($userStats['total_ratings'] ?? 0); ?></div>
                                <div class="stat-desc">Total Ratings</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php echo number_format($userStats['average_rating_given'] ?? 0, 1); ?>/5
                                </div>
                                <div class="stat-desc">Average Rating</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-success">
                                    <?php echo number_format($userStats['highest_rating_given'] ?? 0, 1); ?>
                                </div>
                                <div class="stat-desc">Highest Rating</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value text-warning">
                                    <?php echo number_format($userStats['lowest_rating_given'] ?? 5, 1); ?>
                                </div>
                                <div class="stat-desc">Lowest Rating</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Social Statistics -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Social Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo number_format($userStats['total_followers'] ?? 0); ?></div>
                                <div class="stat-desc">Followers</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo number_format($userStats['total_following'] ?? 0); ?></div>
                                <div class="stat-desc">Following</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo number_format($userStats['total_points_earned'] ?? 0); ?></div>
                                <div class="stat-desc">Points Earned</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo number_format($additionalStats['active_days'] ?? 0); ?></div>
                                <div class="stat-desc">Active Days</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Beer Journey -->
    <div class="row g-4 mb-5">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-route me-2"></i>Beer Journey
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="journey-stat">
                                <div class="journey-icon">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                </div>
                                <div class="journey-info">
                                    <div class="journey-label">First Check-in</div>
                                    <div class="journey-value">
                                        <?php 
                                        if (!empty($userStats['first_checkin_date'])) {
                                            echo date('M j, Y', strtotime($userStats['first_checkin_date']));
                                        } else {
                                            echo 'No check-ins yet';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="journey-stat">
                                <div class="journey-icon">
                                    <i class="fas fa-clock text-success"></i>
                                </div>
                                <div class="journey-info">
                                    <div class="journey-label">Last Check-in</div>
                                    <div class="journey-value">
                                        <?php 
                                        if (!empty($userStats['last_checkin_date'])) {
                                            echo date('M j, Y', strtotime($userStats['last_checkin_date']));
                                        } else {
                                            echo 'No check-ins yet';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="journey-stat">
                                <div class="journey-icon">
                                    <i class="fas fa-palette text-warning"></i>
                                </div>
                                <div class="journey-info">
                                    <div class="journey-label">Styles Explored</div>
                                    <div class="journey-value">
                                        <?php echo number_format($userStats['unique_styles_tried'] ?? 0); ?> categories
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="journey-stat">
                                <div class="journey-icon">
                                    <i class="fas fa-heart text-danger"></i>
                                </div>
                                <div class="journey-info">
                                    <div class="journey-label">Favorite Style</div>
                                    <div class="journey-value">
                                        <?php echo $favoriteStyle ?: 'Not determined yet'; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Achievements -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>Recent Badges
                    </h5>
                    <a href="/user/badges.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($badges)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-medal fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No badges earned yet</p>
                            <small class="text-muted">Start checking in beers to earn your first badge!</small>
                        </div>
                    <?php else: ?>
                        <div class="badges-list">
                            <?php foreach (array_slice($badges, 0, 5) as $badge): ?>
                                <div class="badge-item d-flex align-items-center mb-3">
                                    <div class="badge-icon me-3">
                                        <span class="badge-emoji"><?php echo $badge['icon']; ?></span>
                                    </div>
                                    <div class="badge-info flex-grow-1">
                                        <div class="badge-name fw-bold"><?php echo htmlspecialchars($badge['name']); ?></div>
                                        <div class="badge-desc small text-muted">
                                            <?php echo htmlspecialchars($badge['description']); ?>
                                        </div>
                                        <div class="badge-earned small text-success">
                                            Earned <?php echo formatDateTime($badge['earned_at']); ?>
                                        </div>
                                    </div>
                                    <div class="badge-points">
                                        <span class="badge bg-primary"><?php echo $badge['points_value']; ?>pts</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics (Phase 8) -->
    <?php if (!empty($analytics)): ?>

    <!-- Drinking Patterns -->
    <?php if (!empty($analytics['drinking_patterns'])): ?>
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Drinking Patterns
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Hourly Patterns -->
                        <div class="col-lg-6 mb-4">
                            <h6 class="fw-bold mb-3">Peak Hours</h6>
                            <div class="chart-container">
                                <canvas id="hourlyChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Daily Patterns -->
                        <div class="col-lg-6 mb-4">
                            <h6 class="fw-bold mb-3">Weekly Patterns</h6>
                            <div class="chart-container">
                                <canvas id="dailyChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Seasonal Insights -->
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">Seasonal Preferences</h6>
                            <div class="row">
                                <?php foreach ($analytics['drinking_patterns']['seasonal'] as $season): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title"><?php echo $season['season']; ?></h5>
                                            <p class="card-text">
                                                <strong><?php echo number_format($season['checkin_count']); ?></strong> check-ins<br>
                                                <small class="text-muted">Avg rating: <?php echo number_format($season['avg_rating'], 1); ?>/5</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Beer Preferences Evolution -->
    <?php if (!empty($analytics['beer_preferences'])): ?>
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Beer Preference Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- ABV Preferences -->
                        <div class="col-lg-4 mb-4">
                            <h6 class="fw-bold mb-3">Alcohol Strength Preferences</h6>
                            <div class="chart-container">
                                <canvas id="abvChart" width="300" height="300"></canvas>
                            </div>
                        </div>

                        <!-- IBU Preferences -->
                        <div class="col-lg-4 mb-4">
                            <h6 class="fw-bold mb-3">Bitterness Preferences</h6>
                            <div class="chart-container">
                                <canvas id="ibuChart" width="300" height="300"></canvas>
                            </div>
                        </div>

                        <!-- Style Evolution -->
                        <div class="col-lg-4 mb-4">
                            <h6 class="fw-bold mb-3">Top Beer Styles</h6>
                            <div class="style-list">
                                <?php
                                $styleGroups = [];
                                foreach ($analytics['beer_preferences']['style_evolution'] as $style) {
                                    if (!isset($styleGroups[$style['style_name']])) {
                                        $styleGroups[$style['style_name']] = [
                                            'total_checkins' => 0,
                                            'avg_rating' => 0,
                                            'ratings' => []
                                        ];
                                    }
                                    $styleGroups[$style['style_name']]['total_checkins'] += $style['checkin_count'];
                                    $styleGroups[$style['style_name']]['ratings'][] = $style['avg_rating'];
                                }

                                // Calculate averages and sort
                                foreach ($styleGroups as $styleName => &$data) {
                                    $data['avg_rating'] = array_sum($data['ratings']) / count($data['ratings']);
                                }
                                arsort($styleGroups);

                                $topStyles = array_slice($styleGroups, 0, 5, true);
                                foreach ($topStyles as $styleName => $data): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong><?php echo htmlspecialchars($styleName); ?></strong><br>
                                        <small class="text-muted"><?php echo $data['total_checkins']; ?> check-ins</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary"><?php echo number_format($data['avg_rating'], 1); ?>/5</span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Social Analytics -->
    <?php if (!empty($analytics['social_analytics'])): ?>
    <div class="row g-4 mb-5">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Social Activity Trends
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="socialChart" width="600" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Influence Score
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="influence-score mb-3">
                        <h2 class="display-4 text-primary fw-bold">
                            <?php echo number_format($analytics['social_analytics']['influence_score']); ?>
                        </h2>
                        <p class="text-muted">Community Influence</p>
                    </div>

                    <div class="influence-breakdown">
                        <div class="row text-center">
                            <div class="col-6 mb-2">
                                <div class="small text-muted">Followers</div>
                                <div class="fw-bold"><?php echo number_format($analytics['social_analytics']['influence_data']['followers']); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <div class="small text-muted">Likes Received</div>
                                <div class="fw-bold"><?php echo number_format($analytics['social_analytics']['influence_data']['likes_received']); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <div class="small text-muted">Ratings</div>
                                <div class="fw-bold"><?php echo number_format($analytics['social_analytics']['influence_data']['total_ratings']); ?></div>
                            </div>
                            <div class="col-6 mb-2">
                                <div class="small text-muted">Check-ins</div>
                                <div class="fw-bold"><?php echo number_format($analytics['social_analytics']['influence_data']['total_checkins']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Rating Behavior Analysis -->
    <?php if (!empty($analytics['rating_behavior'])): ?>
    <div class="row g-4 mb-5">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star-half-alt me-2"></i>Rating Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="ratingDistributionChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Rating Trends
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="ratingTrendsChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-rocket me-2"></i>Continue Your Journey
                    </h5>
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <a href="/social/checkin.php" class="btn btn-primary w-100">
                                <i class="fas fa-map-marker-alt d-block mb-2"></i>
                                Check In Beer
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/beers/discover.php" class="btn btn-success w-100">
                                <i class="fas fa-search d-block mb-2"></i>
                                Discover Beers
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/social/discover-users.php" class="btn btn-info w-100">
                                <i class="fas fa-users d-block mb-2"></i>
                                Find Friends
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/user/badges.php" class="btn btn-warning w-100">
                                <i class="fas fa-trophy d-block mb-2"></i>
                                View Badges
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for analytics charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Timeframe selector functionality
function changeTimeframe() {
    const timeframe = document.getElementById('timeframe-selector').value;
    const url = new URL(window.location);
    url.searchParams.set('timeframe', timeframe);
    window.location.href = url.toString();
}

// Chart data from PHP
const analyticsData = <?php echo json_encode($analytics); ?>;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (analyticsData && Object.keys(analyticsData).length > 0) {
        initializeCharts();
    }
});

function initializeCharts() {
    // Hourly patterns chart
    if (analyticsData.drinking_patterns && analyticsData.drinking_patterns.hourly) {
        const hourlyCtx = document.getElementById('hourlyChart');
        if (hourlyCtx) {
            new Chart(hourlyCtx, {
                type: 'bar',
                data: {
                    labels: analyticsData.drinking_patterns.hourly.map(d => d.hour + ':00'),
                    datasets: [{
                        label: 'Check-ins',
                        data: analyticsData.drinking_patterns.hourly.map(d => d.checkin_count),
                        backgroundColor: 'rgba(248, 181, 0, 0.6)',
                        borderColor: 'rgba(248, 181, 0, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    // Daily patterns chart
    if (analyticsData.drinking_patterns && analyticsData.drinking_patterns.daily) {
        const dailyCtx = document.getElementById('dailyChart');
        if (dailyCtx) {
            new Chart(dailyCtx, {
                type: 'doughnut',
                data: {
                    labels: analyticsData.drinking_patterns.daily.map(d => d.day_name),
                    datasets: [{
                        data: analyticsData.drinking_patterns.daily.map(d => d.checkin_count),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // ABV preferences chart
    if (analyticsData.beer_preferences && analyticsData.beer_preferences.abv_preferences) {
        const abvCtx = document.getElementById('abvChart');
        if (abvCtx) {
            new Chart(abvCtx, {
                type: 'pie',
                data: {
                    labels: analyticsData.beer_preferences.abv_preferences.map(d => d.abv_range),
                    datasets: [{
                        data: analyticsData.beer_preferences.abv_preferences.map(d => d.checkin_count),
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // IBU preferences chart
    if (analyticsData.beer_preferences && analyticsData.beer_preferences.ibu_preferences) {
        const ibuCtx = document.getElementById('ibuChart');
        if (ibuCtx) {
            new Chart(ibuCtx, {
                type: 'pie',
                data: {
                    labels: analyticsData.beer_preferences.ibu_preferences.map(d => d.ibu_range),
                    datasets: [{
                        data: analyticsData.beer_preferences.ibu_preferences.map(d => d.checkin_count),
                        backgroundColor: ['#FF9F40', '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // Social activity chart
    if (analyticsData.social_analytics && analyticsData.social_analytics.follower_growth) {
        const socialCtx = document.getElementById('socialChart');
        if (socialCtx) {
            new Chart(socialCtx, {
                type: 'line',
                data: {
                    labels: analyticsData.social_analytics.follower_growth.map(d => d.month),
                    datasets: [{
                        label: 'New Followers',
                        data: analyticsData.social_analytics.follower_growth.map(d => d.new_followers),
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    // Rating distribution chart
    if (analyticsData.rating_behavior && analyticsData.rating_behavior.distribution) {
        const ratingDistCtx = document.getElementById('ratingDistributionChart');
        if (ratingDistCtx) {
            new Chart(ratingDistCtx, {
                type: 'bar',
                data: {
                    labels: analyticsData.rating_behavior.distribution.map(d => d.overall_rating + ' stars'),
                    datasets: [{
                        label: 'Number of Ratings',
                        data: analyticsData.rating_behavior.distribution.map(d => d.rating_count),
                        backgroundColor: 'rgba(255, 159, 64, 0.6)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    // Rating trends chart
    if (analyticsData.rating_behavior && analyticsData.rating_behavior.trends) {
        const ratingTrendsCtx = document.getElementById('ratingTrendsChart');
        if (ratingTrendsCtx) {
            new Chart(ratingTrendsCtx, {
                type: 'line',
                data: {
                    labels: analyticsData.rating_behavior.trends.map(d => d.month),
                    datasets: [{
                        label: 'Average Rating',
                        data: analyticsData.rating_behavior.trends.map(d => d.avg_rating),
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: 'Number of Ratings',
                        data: analyticsData.rating_behavior.trends.map(d => d.rating_count),
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            min: 0,
                            max: 5
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    }
}
</script>

<style>
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

.influence-score {
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    color: white;
    margin-bottom: 20px;
}

.influence-score h2 {
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.style-list {
    max-height: 300px;
    overflow-y: auto;
}

.analytics-card {
    transition: transform 0.2s ease-in-out;
}

.analytics-card:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }

    .influence-score h2 {
        font-size: 2rem;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
