# USE XAMPP DEFAULT CONFIGURATION
# Reset to XAMPP's default working configuration

Write-Host "USING XAMPP DEFAULT CONFIGURATION" -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow

# Stop Apache
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 2

# Use XAMPP's default httpd.conf
$defaultConf = "C:\xampp\apache\conf\httpd.conf"
$originalConf = "C:\xampp\apache\conf\original\httpd.conf"

# Check if original exists
if (Test-Path $originalConf) {
    Write-Host "Restoring original XAMPP configuration..." -ForegroundColor Cyan
    Copy-Item $originalConf $defaultConf -Force
    Write-Host "Original configuration restored" -ForegroundColor Green
} else {
    Write-Host "Using current configuration and just changing port..." -ForegroundColor Cyan
    
    # Just change the port to 8000 in existing config
    $content = Get-Content $defaultConf
    for ($i = 0; $i -lt $content.Length; $i++) {
        if ($content[$i] -match "^Listen\s+\d+") {
            $content[$i] = "Listen 8000"
            Write-Host "Set port to 8000" -ForegroundColor Green
            break
        }
    }
    $content | Set-Content $defaultConf -Encoding UTF8
}

# Start Apache with default XAMPP configuration
Write-Host "Starting Apache with XAMPP defaults..." -ForegroundColor Cyan
Start-Process "C:\xampp\apache\bin\httpd.exe" -WindowStyle Hidden
Start-Sleep 5

# Test if Apache started
$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host "✅ Apache started successfully" -ForegroundColor Green
    
    # Test basic connectivity
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ Port 8000 responding" -ForegroundColor Green
        
        # Test if PHP works with a simple file
        $simplePhp = '<?php echo "Hello from PHP!"; ?>'
        $simplePhp | Set-Content "simple-test.php" -Encoding UTF8
        
        try {
            $phpResponse = Invoke-WebRequest -Uri "http://localhost:8000/simple-test.php" -TimeoutSec 5 -UseBasicParsing
            if ($phpResponse.Content -match "Hello from PHP") {
                Write-Host "✅ PHP is working!" -ForegroundColor Green
            } else {
                Write-Host "⚠️ PHP not processing - showing raw code" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ PHP test failed" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ Port 8000 not responding" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Apache failed to start" -ForegroundColor Red
}

# Open test URLs
Write-Host "Opening test URLs..." -ForegroundColor Cyan
Start-Process "http://localhost:8000"
Start-Process "http://localhost:8000/beersty"
Start-Process "http://localhost:8000/simple-test.php"

Write-Host ""
Write-Host "XAMPP DEFAULT SETUP COMPLETE" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host ""
Write-Host "If PHP still doesn't work, we need to:" -ForegroundColor Yellow
Write-Host "1. Use XAMPP Control Panel to start Apache" -ForegroundColor White
Write-Host "2. Check XAMPP's built-in PHP configuration" -ForegroundColor White
Write-Host "3. Use standard port 80 instead of 8000" -ForegroundColor White

Read-Host "Press Enter to continue"
