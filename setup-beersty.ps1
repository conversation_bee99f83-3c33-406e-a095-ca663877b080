# Beersty Setup Script
# PowerShell script to setup and manage the Beersty brewery management system

param(
    [string]$Action = "start",
    [string]$Port = "8000"
)

Write-Host "🍺 Beersty Setup & Management Script" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

function Test-PHPInstallation {
    Write-Host "`n📋 Checking PHP Installation..." -ForegroundColor Yellow
    
    try {
        $phpVersion = php -v 2>$null
        if ($phpVersion) {
            Write-Host "✅ PHP is installed" -ForegroundColor Green
            $phpVersion[0] | Write-Host -ForegroundColor Cyan
            return $true
        }
    }
    catch {
        Write-Host "❌ PHP is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install PHP and add it to your PATH" -ForegroundColor Yellow
        return $false
    }
}

function Test-MySQLService {
    Write-Host "`n🐬 Checking MySQL Service..." -ForegroundColor Yellow
    
    # Check for MySQL service
    $mysqlService = Get-Service -Name "MySQL*" -ErrorAction SilentlyContinue
    if ($mysqlService) {
        Write-Host "✅ MySQL service found: $($mysqlService.Name)" -ForegroundColor Green
        Write-Host "Status: $($mysqlService.Status)" -ForegroundColor Cyan
        
        if ($mysqlService.Status -eq "Running") {
            return $true
        } else {
            Write-Host "⚠️ MySQL service is not running. Attempting to start..." -ForegroundColor Yellow
            try {
                Start-Service $mysqlService.Name
                Write-Host "✅ MySQL service started" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "❌ Failed to start MySQL service: $($_.Exception.Message)" -ForegroundColor Red
                return $false
            }
        }
    }
    
    # Check for XAMPP MySQL
    $xamppPath = "C:\xampp\mysql\bin\mysqld.exe"
    if (Test-Path $xamppPath) {
        Write-Host "✅ XAMPP MySQL found" -ForegroundColor Green
        
        # Check if XAMPP MySQL is running
        $mysqlProcess = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
        if ($mysqlProcess) {
            Write-Host "✅ XAMPP MySQL is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ XAMPP MySQL is not running" -ForegroundColor Yellow
            Write-Host "Please start MySQL in XAMPP Control Panel" -ForegroundColor Yellow
            return $false
        }
    }
    
    Write-Host "❌ No MySQL installation found" -ForegroundColor Red
    Write-Host "Please install MySQL or XAMPP" -ForegroundColor Yellow
    return $false
}

function Start-PHPServer {
    param([string]$ServerPort = "8000")
    
    Write-Host "`n🚀 Starting PHP Development Server..." -ForegroundColor Yellow
    Write-Host "Port: $ServerPort" -ForegroundColor Cyan
    Write-Host "Document Root: $(Get-Location)" -ForegroundColor Cyan
    
    # Check if port is already in use
    $portInUse = Get-NetTCPConnection -LocalPort $ServerPort -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Host "⚠️ Port $ServerPort is already in use" -ForegroundColor Yellow
        $processes = Get-Process -Id $portInUse.OwningProcess -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Process using port: $($processes.ProcessName) (PID: $($processes.Id))" -ForegroundColor Cyan
        }
        
        $response = Read-Host "Do you want to kill the process and continue? (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            Stop-Process -Id $portInUse.OwningProcess -Force
            Write-Host "✅ Process terminated" -ForegroundColor Green
        } else {
            Write-Host "❌ Cannot start server on port $ServerPort" -ForegroundColor Red
            return $false
        }
    }
    
    try {
        Write-Host "✅ Starting PHP server on http://localhost:$ServerPort" -ForegroundColor Green
        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
        Write-Host "`n🔗 Quick Links:" -ForegroundColor Cyan
        Write-Host "   Homepage: http://localhost:$ServerPort/" -ForegroundColor White
        Write-Host "   Login: http://localhost:$ServerPort/auth/login.php" -ForegroundColor White
        Write-Host "   Admin: http://localhost:$ServerPort/admin/dashboard.php" -ForegroundColor White
        Write-Host "`n🔐 Admin Credentials:" -ForegroundColor Cyan
        Write-Host "   Email: <EMAIL>" -ForegroundColor White
        Write-Host "   Password: admin123" -ForegroundColor White
        Write-Host ""
        
        # Start PHP server
        php -S localhost:$ServerPort
        return $true
    }
    catch {
        Write-Host "❌ Failed to start PHP server: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Setup-Database {
    Write-Host "`n🗄️ Setting up MySQL Database..." -ForegroundColor Yellow
    
    # Test MySQL connection
    try {
        $testConnection = php -r "
            try {
                `$pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
                echo 'SUCCESS';
            } catch (Exception `$e) {
                echo 'FAILED: ' . `$e->getMessage();
            }
        "
        
        if ($testConnection -like "SUCCESS*") {
            Write-Host "✅ MySQL connection successful" -ForegroundColor Green
            
            # Run database setup
            Write-Host "🔧 Running database setup..." -ForegroundColor Yellow
            $setupResult = Invoke-WebRequest -Uri "http://localhost:$Port/fix-mysql-connection.php" -UseBasicParsing -ErrorAction SilentlyContinue
            
            if ($setupResult.StatusCode -eq 200) {
                Write-Host "✅ Database setup completed" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Database setup may have issues. Check manually." -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ MySQL connection failed: $testConnection" -ForegroundColor Red
            Write-Host "Please ensure MySQL is running and accessible" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ Database setup failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Import-BreweryData {
    Write-Host "`n🍺 Importing Michigan Brewery Data..." -ForegroundColor Yellow
    
    try {
        $importResult = Invoke-WebRequest -Uri "http://localhost:$Port/import-michigan-breweries.php" -UseBasicParsing -ErrorAction SilentlyContinue
        
        if ($importResult.StatusCode -eq 200) {
            Write-Host "✅ Brewery data import completed" -ForegroundColor Green
            Write-Host "376 Michigan breweries imported" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️ Brewery data import may have issues" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ Brewery data import failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You can import manually at: http://localhost:$Port/import-michigan-breweries.php" -ForegroundColor Yellow
    }
}

function Show-Status {
    Write-Host "`n📊 Beersty System Status" -ForegroundColor Green
    Write-Host "========================" -ForegroundColor Green
    
    # Check PHP
    if (Test-PHPInstallation) {
        Write-Host "✅ PHP: Ready" -ForegroundColor Green
    } else {
        Write-Host "❌ PHP: Not available" -ForegroundColor Red
    }
    
    # Check MySQL
    if (Test-MySQLService) {
        Write-Host "✅ MySQL: Running" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL: Not running" -ForegroundColor Red
    }
    
    # Check if server is running
    $serverRunning = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($serverRunning) {
        Write-Host "✅ PHP Server: Running on port $Port" -ForegroundColor Green
    } else {
        Write-Host "❌ PHP Server: Not running" -ForegroundColor Red
    }
    
    # Check database connection
    try {
        $dbTest = php -r "
            require_once 'config/config.php';
            try {
                `$db = new Database();
                `$conn = `$db->getConnection();
                echo 'SUCCESS';
            } catch (Exception `$e) {
                echo 'FAILED';
            }
        " 2>$null
        
        if ($dbTest -eq "SUCCESS") {
            Write-Host "✅ Database: Connected" -ForegroundColor Green
        } else {
            Write-Host "❌ Database: Connection failed" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Database: Cannot test" -ForegroundColor Red
    }
}

function Show-Help {
    Write-Host "`n📖 Beersty PowerShell Commands" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\setup-beersty.ps1 -Action <action> [-Port <port>]" -ForegroundColor White
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  start       Start the PHP development server (default)" -ForegroundColor White
    Write-Host "  setup       Setup database and import brewery data" -ForegroundColor White
    Write-Host "  status      Show system status" -ForegroundColor White
    Write-Host "  import      Import Michigan brewery data" -ForegroundColor White
    Write-Host "  help        Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\setup-beersty.ps1                    # Start server on port 8000" -ForegroundColor White
    Write-Host "  .\setup-beersty.ps1 -Action setup      # Setup database" -ForegroundColor White
    Write-Host "  .\setup-beersty.ps1 -Action start -Port 8080  # Start on port 8080" -ForegroundColor White
    Write-Host "  .\setup-beersty.ps1 -Action status     # Check system status" -ForegroundColor White
}

# Main script logic
switch ($Action.ToLower()) {
    "start" {
        if (-not (Test-PHPInstallation)) { exit 1 }
        if (-not (Test-MySQLService)) { 
            Write-Host "⚠️ MySQL is not running, but continuing with server start..." -ForegroundColor Yellow
        }
        Start-PHPServer -ServerPort $Port
    }
    
    "setup" {
        if (-not (Test-PHPInstallation)) { exit 1 }
        if (-not (Test-MySQLService)) { exit 1 }
        Setup-Database
        Import-BreweryData
        Write-Host "`n🎉 Setup completed! Run '.\setup-beersty.ps1 -Action start' to start the server" -ForegroundColor Green
    }
    
    "status" {
        Show-Status
    }
    
    "import" {
        Import-BreweryData
    }
    
    "help" {
        Show-Help
    }
    
    default {
        Write-Host "❌ Unknown action: $Action" -ForegroundColor Red
        Show-Help
        exit 1
    }
}
