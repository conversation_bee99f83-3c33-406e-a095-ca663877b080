<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Test - Beersty Search</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/home-simple.css" rel="stylesheet">
    <link href="/assets/css/location-search.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #4a2c17 100%);
            color: #fff;
            min-height: 100vh;
            padding: 1rem;
        }
        .mobile-container {
            max-width: 500px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.8rem;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="device-info" id="device-info">
        <div>Width: <span id="screen-width"></span>px</div>
        <div>Device: <span id="device-type"></span></div>
    </div>
    
    <div class="mobile-container">
        <div class="text-center mb-4">
            <h1 class="h2 mb-3">
                <i class="fas fa-mobile-alt me-2"></i>
                Mobile Test
            </h1>
            <p class="lead">Testing clean, mobile-friendly search layout</p>
        </div>

        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="fas fa-search me-2"></i>
                Search Form
            </h2>
            
            <div class="search-container">
                <form class="search-form">
                    <div class="row g-2">
                        <div class="col-md-4 col-lg-3">
                            <div class="input-group">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" name="search" class="form-control border-start-0"
                                       placeholder="Search breweries..." style="box-shadow: none;">
                            </div>
                        </div>
                        <div class="col-md-5 col-lg-6">
                            <div class="input-group location-input-container">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <input type="text" name="location" class="form-control border-start-0"
                                       placeholder="City, state..."
                                       autocomplete="off"
                                       style="box-shadow: none;"
                                       title="Smart location search">
                                <input type="hidden" name="location_lat" id="location_lat">
                                <input type="hidden" name="location_lng" id="location_lng">
                            </div>
                        </div>
                        <div class="col-md-3 col-lg-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="fas fa-list me-2"></i>
                Test Instructions
            </h2>
            <ol>
                <li><strong>Type in location field</strong> (try "taylor", "plymouth")</li>
                <li><strong>Check dropdown visibility</strong> and text readability</li>
                <li><strong>Test touch interactions</strong> on suggestions</li>
                <li><strong>Verify responsive layout</strong> on different screen sizes</li>
                <li><strong>Check form stacking</strong> on very small screens</li>
            </ol>
        </div>

        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="fas fa-check-circle me-2"></i>
                Expected Results
            </h2>
            <ul>
                <li><strong>Clean layout</strong> - Not too wide, well-balanced</li>
                <li><strong>Touch-friendly</strong> - Easy to tap and type</li>
                <li><strong>Readable text</strong> - Black text on white dropdown</li>
                <li><strong>Responsive</strong> - Adapts to screen size</li>
                <li><strong>No overflow</strong> - No horizontal scrolling</li>
            </ul>
        </div>

        <div class="test-section">
            <h2 class="h4 mb-3">
                <i class="fas fa-info-circle me-2"></i>
                Device Breakpoints
            </h2>
            <div class="row">
                <div class="col-6">
                    <small>
                        <strong>Mobile:</strong> &lt; 768px<br>
                        <strong>Tablet:</strong> 768px - 991px
                    </small>
                </div>
                <div class="col-6">
                    <small>
                        <strong>Desktop:</strong> 992px+<br>
                        <strong>Current:</strong> <span id="current-breakpoint"></span>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
    <script>
        function updateDeviceInfo() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            let deviceType = 'Desktop';
            let breakpoint = 'lg';
            
            if (width < 576) {
                deviceType = 'Mobile (XS)';
                breakpoint = 'xs';
            } else if (width < 768) {
                deviceType = 'Mobile (SM)';
                breakpoint = 'sm';
            } else if (width < 992) {
                deviceType = 'Tablet (MD)';
                breakpoint = 'md';
            } else if (width < 1200) {
                deviceType = 'Desktop (LG)';
                breakpoint = 'lg';
            } else {
                deviceType = 'Large Desktop (XL)';
                breakpoint = 'xl';
            }
            
            document.getElementById('device-type').textContent = deviceType;
            document.getElementById('current-breakpoint').textContent = breakpoint;
        }
        
        window.addEventListener('resize', updateDeviceInfo);
        
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            
            // Initialize smart location search
            const locationInput = document.querySelector('input[name="location"]');
            if (typeof Beersty !== 'undefined' && Beersty.components && locationInput) {
                Beersty.components.setupLocationAutocomplete(locationInput);
            }
        });
    </script>
</body>
</html>
