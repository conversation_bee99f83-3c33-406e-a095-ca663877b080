<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Beersty</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #f8b500;
            --secondary-color: #6c757d;
            --dark-color: #212529;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .offline-container {
            text-align: center;
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .offline-title {
            color: var(--dark-color);
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            color: var(--secondary-color);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: #e6a200;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(248, 181, 0, 0.3);
            color: white;
        }
        
        .offline-features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .feature-icon {
            color: var(--primary-color);
            margin-right: 1rem;
            width: 20px;
        }
        
        .connection-status {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .status-online {
            background: #28a745;
        }
        
        @media (max-width: 576px) {
            .offline-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <i class="fas fa-wifi" id="connection-icon"></i>
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - you can still browse some content that's been saved on your device.
        </p>
        
        <a href="javascript:void(0)" class="retry-btn" onclick="checkConnection()">
            <i class="fas fa-sync-alt me-2"></i>Try Again
        </a>
        
        <div class="offline-features">
            <h5 class="mb-3">What you can do offline:</h5>
            
            <div class="feature-item">
                <i class="fas fa-eye feature-icon"></i>
                <span>View previously loaded beer information</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-star feature-icon"></i>
                <span>Rate beers (will sync when online)</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-map-marker-alt feature-icon"></i>
                <span>Save check-ins for later</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-user feature-icon"></i>
                <span>View your profile and statistics</span>
            </div>
        </div>
        
        <div class="connection-status">
            <div class="d-flex align-items-center justify-content-center">
                <span class="status-indicator status-offline" id="status-indicator"></span>
                <span id="status-text">Connection Status: Offline</span>
            </div>
            <small class="text-muted d-block mt-1">
                We'll automatically reconnect when your internet is back.
            </small>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const connectionIcon = document.getElementById('connection-icon');
            
            if (navigator.onLine) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = 'Connection Status: Online';
                connectionIcon.className = 'fas fa-wifi';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = '/beersty/';
                }, 1000);
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Connection Status: Offline';
                connectionIcon.className = 'fas fa-wifi';
            }
        }
        
        // Manual connection check
        function checkConnection() {
            const retryBtn = document.querySelector('.retry-btn');
            const icon = retryBtn.querySelector('i');
            
            // Show loading state
            icon.className = 'fas fa-spinner fa-spin me-2';
            retryBtn.style.pointerEvents = 'none';
            
            setTimeout(() => {
                if (navigator.onLine) {
                    window.location.href = '/beersty/';
                } else {
                    // Reset button
                    icon.className = 'fas fa-sync-alt me-2';
                    retryBtn.style.pointerEvents = 'auto';
                    
                    // Show feedback
                    const originalText = retryBtn.innerHTML;
                    retryBtn.innerHTML = '<i class="fas fa-times me-2"></i>Still Offline';
                    retryBtn.style.background = '#dc3545';
                    
                    setTimeout(() => {
                        retryBtn.innerHTML = originalText;
                        retryBtn.style.background = '#f8b500';
                    }, 2000);
                }
            }, 1000);
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);
        
        // Register service worker if available
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/beersty/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    </script>
</body>
</html>
