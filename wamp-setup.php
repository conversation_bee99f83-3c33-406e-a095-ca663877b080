<?php
/**
 * WAMP Database Setup
 * Creates database and users via web interface
 */

echo "<h1>🔧 WAMP Database Setup</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Step 1: Testing MySQL Connection</h2>";

try {
    // Test basic MySQL connection without database
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $conn = new PDO($dsn, 'root', '');
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ MySQL server connection successful</p>";
    
    // Get MySQL version
    $stmt = $conn->prepare("SELECT VERSION() as version");
    $stmt->execute();
    $version = $stmt->fetch();
    echo "<p>🐬 MySQL Version: " . htmlspecialchars($version['version']) . "</p>";
    
    echo "<h2>📊 Step 2: Creating Database</h2>";
    
    // Create database
    $conn->exec("CREATE DATABASE IF NOT EXISTS beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ Database 'beersty_db' created</p>";
    
    // Switch to the new database
    $conn->exec("USE beersty_db");
    echo "<p>✅ Connected to beersty_db</p>";
    
    echo "<h2>🏗️ Step 3: Creating Tables</h2>";
    
    // Create users table
    $usersTable = "
    CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL
    )";
    $conn->exec($usersTable);
    echo "<p>✅ Users table created</p>";
    
    // Create profiles table
    $profilesTable = "
    CREATE TABLE IF NOT EXISTS profiles (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        role VARCHAR(50) DEFAULT 'customer',
        brewery_id VARCHAR(36) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $conn->exec($profilesTable);
    echo "<p>✅ Profiles table created</p>";
    
    echo "<h2>👥 Step 4: Creating Users</h2>";
    
    // Create users
    $users = [
        ['id' => 'admin-001', 'email' => '<EMAIL>', 'password' => 'admin123', 'role' => 'admin'],
        ['id' => 'brewery-001', 'email' => '<EMAIL>', 'password' => 'brewery123', 'role' => 'brewery'],
        ['id' => 'customer-001', 'email' => '<EMAIL>', 'password' => 'customer123', 'role' => 'customer'],
        ['id' => 'user-001', 'email' => '<EMAIL>', 'password' => 'user123', 'role' => 'customer']
    ];
    
    foreach ($users as $userData) {
        // Check if user exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$userData['email']]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p>⚠️ User {$userData['email']} already exists, updating...</p>";
            
            // Update password
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $stmt->execute([$passwordHash, $userData['email']]);
            
            // Update profile
            $stmt = $conn->prepare("UPDATE profiles SET role = ? WHERE email = ?");
            $stmt->execute([$userData['role'], $userData['email']]);
            
        } else {
            echo "<p>➕ Creating user {$userData['email']}...</p>";
            
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Insert user
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userData['id'], $userData['email'], $passwordHash]);
            
            // Insert profile
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)");
            $stmt->execute([$userData['id'], $userData['email'], $userData['role']]);
            
            echo "<p>✅ User {$userData['email']} created</p>";
        }
    }
    
    echo "<h2>🔐 Step 5: Testing Authentication</h2>";
    
    // Test login for admin user
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user && password_verify('admin123', $user['password_hash'])) {
        echo "<p>✅ Admin login test PASSED</p>";
    } else {
        echo "<p>❌ Admin login test FAILED</p>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 WAMP Database Setup Complete!</h3>";
    echo "<p><strong>Database:</strong> beersty_db</p>";
    echo "<p><strong>Users Created:</strong></p>";
    echo "<ul>";
    foreach ($users as $user) {
        echo "<li><strong>{$user['role']}:</strong> {$user['email']} / {$user['password']}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Troubleshooting:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure WAMP is running (green icon in system tray)</li>";
    echo "<li>Check that MySQL service is started in WAMP</li>";
    echo "<li>Try accessing phpMyAdmin: <a href='http://localhost/phpmyadmin'>http://localhost/phpmyadmin</a></li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='color: #007bff;'>Try Login Now</a></li>";
echo "<li><a href='test-database-connection.php' style='color: #007bff;'>Test Database Connection</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' style='color: #007bff;'>Open phpMyAdmin</a></li>";
echo "<li><a href='/' style='color: #007bff;'>Back to Homepage</a></li>";
echo "</ul>";
?>
