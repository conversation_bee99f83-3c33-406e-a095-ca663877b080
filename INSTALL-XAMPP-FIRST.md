# 🚨 XAMPP Installation Required

The "site can't be reached" error indicates that XAMPP is not installed or not running properly.

## 📥 Step 1: Download and Install XAMPP

1. **Download XAMPP** from the official website:
   - Go to: https://www.apachefriends.org/download.html
   - Download the **Windows version** (latest PHP 8.x)
   - File size: ~150MB

2. **Install XAMPP**:
   - Run the downloaded installer **as Administrator**
   - Install to: `C:\xampp` (default location)
   - Select components:
     - ✅ Apache
     - ✅ MySQL
     - ✅ PHP
     - ✅ phpMyAdmin
     - ❌ Mercury (not needed)
     - ❌ Tomcat (not needed)

## 🚀 Step 2: Start XAMPP Services

1. **Open XAMPP Control Panel**:
   - Go to: `C:\xampp\xampp-control.exe`
   - **Run as Administrator**

2. **Start Services**:
   - Click **"Start"** next to **Apache**
   - Click **"Start"** next to **MySQL**
   - Both should show **green** status

3. **Verify Installation**:
   - Open browser and go to: http://localhost/
   - You should see the XAMPP dashboard

## 📁 Step 3: Copy Project Files

Once XAMPP is running:

1. **Copy the Beersty project** to:
   ```
   C:\xampp\htdocs\beersty\
   ```

2. **Set permissions** (run as Administrator):
   ```powershell
   icacls "C:\xampp\htdocs\beersty\uploads" /grant "Everyone:(OI)(CI)F"
   ```

## 🗄️ Step 4: Set Up Database

1. **Open phpMyAdmin**: http://localhost/phpmyadmin/
2. **Create database**: Click "New" → Name: `beersty_db`
3. **Import schema**: 
   - Click "Import" tab
   - Choose file: `database/schema.sql`
   - Click "Go"

## 🎯 Step 5: Access Application

- **Application**: http://localhost/beersty/
- **Login**: <EMAIL> / admin123

## 🔧 Troubleshooting

### Port Conflicts
If Apache won't start:
- **Skype**: Disable "Use port 80 and 443" in Skype settings
- **IIS**: Disable Internet Information Services
- **Other web servers**: Stop them first

### Firewall Issues
- Allow Apache and MySQL through Windows Firewall
- Run XAMPP Control Panel as Administrator

### Alternative: Use Different Ports
In XAMPP Control Panel:
- Click "Config" next to Apache
- Change port from 80 to 8080
- Access via: http://localhost:8080/beersty/

## 📞 Quick Help

**Common XAMPP locations:**
- Control Panel: `C:\xampp\xampp-control.exe`
- Apache config: `C:\xampp\apache\conf\httpd.conf`
- PHP config: `C:\xampp\php\php.ini`
- Web root: `C:\xampp\htdocs\`

**Test URLs after installation:**
- XAMPP Dashboard: http://localhost/
- phpMyAdmin: http://localhost/phpmyadmin/
- Beersty App: http://localhost/beersty/

---

**Once XAMPP is installed and running, the Beersty application will work perfectly! 🍺**
