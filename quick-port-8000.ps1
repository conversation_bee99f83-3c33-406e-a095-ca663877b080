# QUICK PORT 8000 SETUP
# Restore the working port 8000 configuration

Write-Host "RESTORING PORT 8000 SETUP" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

# Find XAMPP
$XamppPath = "C:\xampp"
if (-not (Test-Path $XamppPath)) {
    Write-Host "XAMPP not found at C:\xampp" -ForegroundColor Red
    exit 1
}

Write-Host "Using XAMPP at: $XamppPath" -ForegroundColor Green

# Stop Apache
Write-Host "Stopping Apache..." -ForegroundColor Cyan
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 3

# Configure Apache for port 8000
Write-Host "Configuring Apache for port 8000..." -ForegroundColor Cyan
$HttpdConf = Join-Path $XamppPath "apache\conf\httpd.conf"

if (Test-Path $HttpdConf) {
    $Content = Get-Content $HttpdConf
    
    for ($i = 0; $i -lt $Content.Length; $i++) {
        if ($Content[$i] -match "^Listen\s+\d+") {
            $Content[$i] = "Listen 8000"
            Write-Host "Changed Listen to port 8000" -ForegroundColor Green
        }
        if ($Content[$i] -match "^#?ServerName\s+localhost") {
            $Content[$i] = "ServerName localhost:8000"
            Write-Host "Changed ServerName to localhost:8000" -ForegroundColor Green
        }
    }
    
    $Content | Set-Content $HttpdConf -Encoding UTF8
    Write-Host "Apache configured for port 8000" -ForegroundColor Green
}

# Start Apache
Write-Host "Starting Apache..." -ForegroundColor Cyan
$ApacheExe = Join-Path $XamppPath "apache\bin\httpd.exe"
Start-Process $ApacheExe -WindowStyle Hidden
Start-Sleep 5

# Test port 8000
Write-Host "Testing port 8000..." -ForegroundColor Cyan
try {
    $Response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -UseBasicParsing
    Write-Host "SUCCESS: Port 8000 is working!" -ForegroundColor Green
    
    # Open URLs
    Write-Host "Opening URLs..." -ForegroundColor Cyan
    Start-Process "http://localhost:8000/beersty-lovable"
    Start-Process "http://localhost:8000/beersty-lovable/admin/user-management.php"
    
} catch {
    Write-Host "Port 8000 test failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "PORT 8000 SETUP COMPLETE!" -ForegroundColor Green
Write-Host "Your URLs:" -ForegroundColor Yellow
Write-Host "  http://localhost:8000/beersty-lovable" -ForegroundColor White
Write-Host "  http://localhost:8000/beersty-lovable/admin/user-management.php" -ForegroundColor White

Read-Host "Press Enter to continue"
