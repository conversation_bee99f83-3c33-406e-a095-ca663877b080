# 🍺 Beersty Development Environment Scripts

## The Right Way to Start Your Development Environment Every Time

After rebooting and creating the proper scripts, here's your definitive guide to starting the Beersty development environment.

## 📁 Available Scripts

### Main Scripts
- **`beersty-dev-start.ps1`** - The definitive startup script (PowerShell)
- **`start-xampp-beersty.bat`** - Simple batch wrapper for the PowerShell script
- **`beersty-status.ps1`** - Quick status checker

### Legacy/Backup Scripts
- **`nuclear-restart.ps1`** - Emergency full restart (kills all processes)
- **`fresh-restart.ps1`** - Clean restart with PDO setup
- **`enable-pdo.ps1`** - PDO configuration only

## 🚀 How to Start Your Environment

### Method 1: Double-click (Easiest)
```
Double-click: start-xampp-beersty.bat
```

### Method 2: PowerShell (Recommended)
```powershell
# Basic start
.\beersty-dev-start.ps1

# Use port 8080 if port 80 is blocked
.\beersty-dev-start.ps1 -Port8080

# Skip PDO configuration (if already set up)
.\beersty-dev-start.ps1 -SkipPDO
```

### Method 3: Command Prompt
```cmd
powershell -ExecutionPolicy Bypass -File "beersty-dev-start.ps1"
```

## 📋 What the Script Does

1. **🔍 Finds XAMPP** - Automatically locates your XAMPP installation
2. **🛑 Stops Processes** - Cleanly stops any existing Apache/MySQL processes
3. **⚙️ Configures PDO** - Enables PDO MySQL extensions in php.ini
4. **🚀 Starts XAMPP** - Opens XAMPP Control Panel
5. **⏳ Waits for You** - Prompts you to start Apache and MySQL manually
6. **🧪 Tests Services** - Verifies everything is working
7. **🌐 Opens URLs** - Automatically opens your development URLs

## 🎯 Expected Results

After running the script successfully:

✅ **XAMPP Control Panel open**  
✅ **Apache: Running (green)**  
✅ **MySQL: Running (green)**  
✅ **URLs automatically opened:**
- http://localhost/beersty-lovable
- http://localhost/beersty-lovable/admin/user-management.php
- http://localhost/beersty-lovable/test-pdo-simple.php
- http://localhost/phpmyadmin

## 🔧 Quick Status Check

To check if everything is running:
```powershell
.\beersty-status.ps1
```

## 🧪 Testing ADD USER Functionality

1. Go to: http://localhost/beersty-lovable/admin/user-management.php
2. Click "Add User" button
3. Fill in the form:
   - Email: <EMAIL>
   - Password: password123
   - Confirm Password: password123
   - Role: User
   - Status: Active
4. Click "Create User"
5. Should see success message and page reload

## 🚨 Troubleshooting

### If Apache Won't Start
- **Port 80 in use**: Run with `-Port8080` parameter
- **Permission issues**: Run PowerShell as Administrator
- **IIS conflict**: The script automatically stops IIS

### If MySQL Won't Start
- **Port 3306 in use**: Check for other MySQL installations
- **Permission issues**: Run as Administrator

### If PDO Doesn't Work
- Check: http://localhost/beersty-lovable/test-pdo-simple.php
- Should show all green checkmarks
- If red errors, run: `.\enable-pdo.ps1`

### Nuclear Option
If nothing works:
```powershell
.\nuclear-restart.ps1
```
This kills ALL processes and starts completely fresh.

## 💾 Save These Scripts

These scripts are designed to be your permanent solution. Save them in your project folder and use them every time you need to start development.

## 📝 Notes

- **Tested after reboot** - These scripts work on a clean system
- **Handles conflicts** - Automatically stops IIS and other conflicting services
- **Configures PDO** - Ensures database connections work
- **Reusable** - Use the same scripts every time
- **Reliable** - Built to work consistently

## 🎉 Success Indicators

When everything is working correctly:
- XAMPP Control Panel shows Apache and MySQL as "Running" (green)
- http://localhost/beersty-lovable loads the homepage
- PDO test page shows all green checkmarks
- ADD USER functionality works in admin panel

---

**Remember**: Use `beersty-dev-start.ps1` as your go-to script for starting the development environment every time!
