# SIMPLE PORT CONFLICT TEST

Write-Host "PORT CONFLICT TEST" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

# Check key ports
Write-Host ""
Write-Host "1. CHECKING PORT USAGE:" -ForegroundColor Cyan

Write-Host "Port 80 (HTTP):" -ForegroundColor Yellow
$port80 = netstat -an | findstr ":80 "
if ($port80) {
    Write-Host "  IN USE:" -ForegroundColor Red
    $port80 | ForEach-Object { Write-Host "    $_" -ForegroundColor White }
} else {
    Write-Host "  FREE" -ForegroundColor Green
}

Write-Host ""
Write-Host "Port 8080 (HTTP Alt):" -ForegroundColor Yellow
$port8080 = netstat -an | findstr ":8080 "
if ($port8080) {
    Write-Host "  IN USE:" -ForegroundColor Red
    $port8080 | ForEach-Object { Write-Host "    $_" -ForegroundColor White }
} else {
    Write-Host "  FREE" -ForegroundColor Green
}

Write-Host ""
Write-Host "Port 3306 (MySQL):" -ForegroundColor Yellow
$port3306 = netstat -an | findstr ":3306 "
if ($port3306) {
    Write-Host "  IN USE:" -ForegroundColor Red
    $port3306 | ForEach-Object { Write-Host "    $_" -ForegroundColor White }
} else {
    Write-Host "  FREE" -ForegroundColor Green
}

# Test connectivity
Write-Host ""
Write-Host "2. TESTING CONNECTIVITY:" -ForegroundColor Cyan

$testUrls = @("http://localhost", "http://localhost:8080", "http://127.0.0.1")
$working = @()

foreach ($url in $testUrls) {
    Write-Host ""
    Write-Host "Testing $url..." -ForegroundColor Gray
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 3 -UseBasicParsing
        Write-Host "  SUCCESS: $url works!" -ForegroundColor Green
        $working += $url
    } catch {
        Write-Host "  FAILED: $url" -ForegroundColor Red
    }
}

# Check processes
Write-Host ""
Write-Host "3. CHECKING PROCESSES:" -ForegroundColor Cyan

$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host "Apache: RUNNING" -ForegroundColor Green
} else {
    Write-Host "Apache: NOT RUNNING" -ForegroundColor Red
}

$mysql = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysql) {
    Write-Host "MySQL: RUNNING" -ForegroundColor Green
} else {
    Write-Host "MySQL: NOT RUNNING" -ForegroundColor Red
}

# Check IIS
Write-Host ""
Write-Host "4. CHECKING IIS:" -ForegroundColor Cyan
try {
    $iis = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
    if ($iis -and $iis.Status -eq "Running") {
        Write-Host "IIS: RUNNING (CONFLICTS WITH APACHE!)" -ForegroundColor Red
    } else {
        Write-Host "IIS: Not running" -ForegroundColor Green
    }
} catch {
    Write-Host "IIS: Not installed" -ForegroundColor Green
}

# Summary
Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Yellow
Write-Host "========" -ForegroundColor Yellow

if ($working.Count -gt 0) {
    Write-Host ""
    Write-Host "WORKING URLS:" -ForegroundColor Green
    foreach ($url in $working) {
        Write-Host "  $url" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Opening working URLs..." -ForegroundColor Cyan
    Start-Process $working[0]
    Start-Process "$($working[0])/beersty-lovable"
    Start-Process "$($working[0])/beersty-lovable/admin/user-management.php"
    
} else {
    Write-Host ""
    Write-Host "NO WORKING URLS FOUND!" -ForegroundColor Red
    Write-Host "Apache may not be running or port conflicts exist." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
