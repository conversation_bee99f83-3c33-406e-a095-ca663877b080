<?php
/**
 * Phase 8 Setup Script
 * Analytics & Business Intelligence
 */

require_once 'config/config.php';

echo "<h1>📊 Phase 8 Setup: Analytics & Business Intelligence</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Setting up Phase 8 analytics infrastructure...</h2>";
    
    // Read and execute the Phase 8 SQL file
    $sqlFile = 'database/phase8_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt) && !preg_match('/^\/\*/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    $createdTables = [];
    $createdIndexes = [];
    $createdProcedures = [];
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $conn->exec($statement);
                $successCount++;
                
                // Track what was created
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    $createdTables[] = $tableName;
                    echo "<p>✅ Created table: <strong>$tableName</strong></p>";
                } elseif (strpos($statement, 'CREATE INDEX') !== false) {
                    preg_match('/CREATE INDEX.*?(\w+)/', $statement, $matches);
                    $indexName = $matches[1] ?? 'unknown';
                    $createdIndexes[] = $indexName;
                    echo "<p>✅ Created index: <strong>$indexName</strong></p>";
                } elseif (strpos($statement, 'CREATE PROCEDURE') !== false) {
                    preg_match('/CREATE PROCEDURE.*?(\w+)/', $statement, $matches);
                    $procedureName = $matches[1] ?? 'unknown';
                    $createdProcedures[] = $procedureName;
                    echo "<p>✅ Created procedure: <strong>$procedureName</strong></p>";
                } elseif (strpos($statement, 'CREATE EVENT') !== false) {
                    preg_match('/CREATE EVENT.*?(\w+)/', $statement, $matches);
                    $eventName = $matches[1] ?? 'unknown';
                    echo "<p>✅ Created event: <strong>$eventName</strong></p>";
                } elseif (strpos($statement, 'CREATE OR REPLACE VIEW') !== false) {
                    preg_match('/CREATE OR REPLACE VIEW.*?(\w+)/', $statement, $matches);
                    $viewName = $matches[1] ?? 'unknown';
                    echo "<p>✅ Created view: <strong>$viewName</strong></p>";
                }
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<p>❌ Error executing statement: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>📊 Setup Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p><strong>✅ Successful operations:</strong> $successCount</p>";
    echo "<p><strong>❌ Failed operations:</strong> $errorCount</p>";
    echo "<p><strong>📋 Tables created:</strong> " . count($createdTables) . "</p>";
    echo "<p><strong>🔍 Indexes created:</strong> " . count($createdIndexes) . "</p>";
    echo "<p><strong>⚙️ Procedures created:</strong> " . count($createdProcedures) . "</p>";
    echo "</div>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Phase 8 Setup Complete!</h3>";
        echo "<p>Analytics & Business Intelligence infrastructure has been successfully set up. You now have access to:</p>";
        echo "<ul>";
        echo "<li><strong>Enhanced User Analytics:</strong> Advanced personal drinking pattern analysis</li>";
        echo "<li><strong>Year in Review:</strong> Annual summary reports with highlights</li>";
        echo "<li><strong>Platform Analytics:</strong> Admin dashboard with business intelligence</li>";
        echo "<li><strong>Performance Tracking:</strong> Optimized database queries with analytics indexes</li>";
        echo "<li><strong>Automated Reporting:</strong> Daily and monthly analytics summaries</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🚀 New Features Available</h2>";
        echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>👤 User Features:</h4>";
        echo "<ul>";
        echo "<li><strong>Enhanced Statistics Dashboard:</strong> Advanced analytics with charts and insights</li>";
        echo "<li><strong>Year in Review:</strong> Personalized annual beer journey summaries</li>";
        echo "<li><strong>Drinking Pattern Analysis:</strong> Hourly, daily, and seasonal insights</li>";
        echo "<li><strong>Beer Preference Evolution:</strong> Track how your tastes change over time</li>";
        echo "<li><strong>Social Analytics:</strong> Influence score and engagement metrics</li>";
        echo "</ul>";
        
        echo "<h4>👨‍💼 Admin Features:</h4>";
        echo "<ul>";
        echo "<li><strong>Platform Analytics Dashboard:</strong> Comprehensive business intelligence</li>";
        echo "<li><strong>User Engagement Metrics:</strong> DAU/MAU, retention, and activity trends</li>";
        echo "<li><strong>Beer & Brewery Trends:</strong> Popular styles and top-performing content</li>";
        echo "<li><strong>Geographic Insights:</strong> Usage patterns by location</li>";
        echo "<li><strong>Automated Reporting:</strong> Daily and monthly summary generation</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔧 Performance Enhancements</h2>";
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<ul>";
        echo "<li><strong>Analytics Indexes:</strong> Optimized database queries for faster reporting</li>";
        echo "<li><strong>Summary Tables:</strong> Pre-calculated daily and monthly metrics</li>";
        echo "<li><strong>Automated Calculations:</strong> Background processes for analytics updates</li>";
        echo "<li><strong>Efficient Views:</strong> Optimized queries for common analytics operations</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔗 Quick Links</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p><strong>User Features:</strong></p>";
        echo "<p><a href='/beersty/user/statistics.php' class='btn btn-primary' style='margin-right: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Enhanced Analytics</a>";
        echo "<a href='/beersty/user/year-in-review.php' class='btn btn-warning' style='margin-right: 10px; padding: 10px 20px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px;'>Year in Review</a></p>";
        
        echo "<p><strong>Admin Features:</strong></p>";
        echo "<p><a href='/beersty/admin/analytics.php' class='btn btn-info' style='margin-right: 10px; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px;'>Platform Analytics</a>";
        echo "<a href='/beersty/admin/dashboard.php' class='btn btn-secondary' style='padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>Admin Dashboard</a></p>";
        echo "</div>";
        
        // Test analytics functionality
        echo "<h2>🧪 Testing Analytics Features</h2>";
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        
        try {
            // Test AnalyticsService
            require_once 'includes/AnalyticsService.php';
            $analyticsService = new AnalyticsService($conn);
            
            // Get a test user
            $stmt = $conn->query("SELECT id FROM users LIMIT 1");
            $testUser = $stmt->fetch();
            
            if ($testUser) {
                $userId = $testUser['id'];
                
                // Test user analytics
                $userAnalytics = $analyticsService->getUserAnalytics($userId, '1_year');
                echo "<p>✅ User analytics service working</p>";
                
                // Test platform analytics
                $platformStats = $analyticsService->getPlatformStatistics('1_year');
                echo "<p>✅ Platform analytics service working</p>";
                
                // Test year in review
                $yearReview = $analyticsService->getYearInReview($userId, date('Y'));
                echo "<p>✅ Year in review service working</p>";
                
            } else {
                echo "<p>⚠️ No test user found - create a user account to test analytics features</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Analytics testing error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Setup Completed with Errors</h3>";
        echo "<p>Some operations failed. Please check the error messages above and try running the setup again.</p>";
        echo "<p>Common issues:</p>";
        echo "<ul>";
        echo "<li>MySQL version compatibility (requires MySQL 5.7+ for events and procedures)</li>";
        echo "<li>Database permissions for creating procedures and events</li>";
        echo "<li>Event scheduler not enabled (run: SET GLOBAL event_scheduler = ON;)</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='test-phase8.php'>Test Phase 8 Features →</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: black;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
