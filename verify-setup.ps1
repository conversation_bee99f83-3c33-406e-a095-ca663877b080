# Verify Setup Script - Quick check after restart

Write-Host "Verifying <PERSON>AM<PERSON> and PDO Setup..." -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Yellow

# Check processes
Write-Host ""
Write-Host "1. Checking running processes:" -ForegroundColor Cyan

$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apache) {
    Write-Host "   Apache: RUNNING" -ForegroundColor Green
} else {
    Write-Host "   Apache: NOT RUNNING" -ForegroundColor Red
}

$mysql = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysql) {
    Write-Host "   MySQL: RUNNING" -ForegroundColor Green
} else {
    Write-Host "   MySQL: NOT RUNNING" -ForegroundColor Red
}

# Test web server
Write-Host ""
Write-Host "2. Testing web server:" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -UseBasicParsing
    Write-Host "   Web Server: RESPONDING (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "   Web Server: NOT RESPONDING" -ForegroundColor Red
}

# Test project
Write-Host ""
Write-Host "3. Testing project:" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/beersty-lovable" -TimeoutSec 5 -UseBasicParsing
    Write-Host "   Project: ACCESSIBLE" -ForegroundColor Green
} catch {
    Write-Host "   Project: NOT ACCESSIBLE" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. Opening test pages..." -ForegroundColor Cyan
Start-Process "http://localhost/beersty-lovable/test-pdo-simple.php"
Start-Process "http://localhost/beersty-lovable/check-pdo.php"

Write-Host ""
Write-Host "Check the opened pages:" -ForegroundColor Yellow
Write-Host "- PDO Simple Test should show all GREEN checkmarks" -ForegroundColor White
Write-Host "- If you see RED errors, PDO is not properly enabled" -ForegroundColor White
Write-Host ""
Write-Host "If PDO is working, test ADD USER at:" -ForegroundColor Yellow
Write-Host "http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
