# BEERSTY STATUS CHECKER
# Quick status check for your development environment

Write-Host "🍺 BEERSTY STATUS CHECK" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

# Check processes
Write-Host ""
Write-Host "📊 Service Status:" -ForegroundColor Cyan

$Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($Apache) {
    Write-Host "  ✅ Apache: RUNNING" -ForegroundColor Green
} else {
    Write-Host "  ❌ Apache: NOT RUNNING" -ForegroundColor Red
}

$MySQL = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($MySQL) {
    Write-Host "  ✅ MySQL: RUNNING" -ForegroundColor Green
} else {
    Write-Host "  ❌ MySQL: NOT RUNNING" -ForegroundColor Red
}

# Test URLs
Write-Host ""
Write-Host "🌐 URL Tests:" -ForegroundColor Cyan

$URLs = @(
    @{Name="Localhost"; URL="http://localhost"},
    @{Name="Localhost:8080"; URL="http://localhost:8080"},
    @{Name="Project"; URL="http://localhost/beersty-lovable"},
    @{Name="PDO Test"; URL="http://localhost/beersty-lovable/test-pdo-simple.php"},
    @{Name="User Management"; URL="http://localhost/beersty-lovable/admin/user-management.php"}
)

foreach ($test in $URLs) {
    try {
        $Response = Invoke-WebRequest -Uri $test.URL -TimeoutSec 3 -UseBasicParsing
        Write-Host "  ✅ $($test.Name): ACCESSIBLE" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ $($test.Name): NOT ACCESSIBLE" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🔧 Quick Actions:" -ForegroundColor Cyan
Write-Host "  Start Environment: .\beersty-dev-start.ps1" -ForegroundColor White
Write-Host "  Open User Management: Start-Process 'http://localhost/beersty-lovable/admin/user-management.php'" -ForegroundColor White
Write-Host "  Open PDO Test: Start-Process 'http://localhost/beersty-lovable/test-pdo-simple.php'" -ForegroundColor White
