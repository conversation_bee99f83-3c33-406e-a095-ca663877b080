@echo off
echo ========================================
echo MANUAL XAMPP STARTUP - STEP BY STEP
echo ========================================
echo.

echo STEP 1: Finding XAMPP...
if exist "C:\xampp\xampp-control.exe" (
    echo Found XAMPP at C:\xampp
    set XAMPP_PATH=C:\xampp
    goto :start_xampp
)

if exist "C:\Program Files\XAMPP\xampp-control.exe" (
    echo Found XAMPP at C:\Program Files\XAMPP
    set XAMPP_PATH=C:\Program Files\XAMPP
    goto :start_xampp
)

echo ERROR: XAMPP not found!
echo.
echo You need to install XAMPP:
echo 1. Go to: https://www.apachefriends.org/download.html
echo 2. Download XAMPP for Windows
echo 3. Install it
echo 4. Run this script again
echo.
pause
exit /b 1

:start_xampp
echo.
echo STEP 2: Starting XAMPP Control Panel...
start "" "%XAMPP_PATH%\xampp-control.exe"
echo.
echo XAMPP Control Panel should be opening...
echo.
echo ========================================
echo WHAT TO DO IN XAMPP CONTROL PANEL:
echo ========================================
echo.
echo 1. Look for a row labeled "Apache"
echo 2. Click the "Start" button next to Apache
echo 3. Look for a row labeled "MySQL" 
echo 4. Click the "Start" button next to MySQL
echo.
echo EXPECTED RESULTS:
echo - Apache should turn GREEN and say "Running"
echo - MySQL should turn GREEN and say "Running"
echo.
echo IF APACHE WON'T START:
echo - Click "Logs" next to Apache to see the error
echo - Common issue: Port 80 already in use
echo - Solution: Close other web servers or use port 8080
echo.
echo IF YOU GET PERMISSION ERRORS:
echo - Close XAMPP Control Panel
echo - Right-click on xampp-control.exe
echo - Select "Run as administrator"
echo.

pause

echo.
echo STEP 3: Testing if it worked...
echo.

echo Testing localhost...
curl -s http://localhost >nul 2>&1
if %errorlevel% == 0 (
    echo SUCCESS: localhost is working!
    start "" "http://localhost"
    start "" "http://localhost/beersty-lovable"
) else (
    echo localhost not working, trying port 8080...
    curl -s http://localhost:8080 >nul 2>&1
    if %errorlevel% == 0 (
        echo SUCCESS: localhost:8080 is working!
        start "" "http://localhost:8080"
        start "" "http://localhost:8080/beersty-lovable"
    ) else (
        echo FAILED: Neither localhost nor localhost:8080 is working
        echo.
        echo TROUBLESHOOTING:
        echo 1. Make sure Apache is green/running in XAMPP Control Panel
        echo 2. Try running XAMPP as Administrator
        echo 3. Check XAMPP logs for errors
        echo 4. Restart your computer and try again
    )
)

echo.
echo ========================================
echo NEXT STEPS:
echo ========================================
echo.
echo If localhost is working:
echo 1. Go to: http://localhost/beersty-lovable/admin/user-management.php
echo 2. Test the ADD USER functionality
echo.
echo If localhost:8080 is working:
echo 1. Go to: http://localhost:8080/beersty-lovable/admin/user-management.php
echo 2. Test the ADD USER functionality
echo.
pause
