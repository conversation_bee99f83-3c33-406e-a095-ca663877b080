<?php
/**
 * Multi-User Database Setup for Kali Linux
 * Creates a shared database structure that multiple users can access
 */

echo "<h1>🐧 Multi-User Database Setup for Kali Linux</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
    .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_GET['action'] ?? 'show_options';

function getCurrentUser() {
    return posix_getpwuid(posix_geteuid())['name'] ?? 'unknown';
}

function getSystemUsers() {
    $users = [];
    $passwd = file('/etc/passwd');
    foreach ($passwd as $line) {
        $parts = explode(':', $line);
        if (isset($parts[2]) && $parts[2] >= 1000 && $parts[2] < 65534) { // Regular users
            $users[] = $parts[0];
        }
    }
    return $users;
}

if ($action === 'setup_shared') {
    echo "<h2>🔧 Setting up Shared Database Structure</h2>";
    
    $currentUser = getCurrentUser();
    echo "<p><strong>Current User:</strong> $currentUser</p>";
    
    try {
        // 1. Create shared database directory
        $sharedDbPath = '/opt/beersty-shared/database';
        $sharedConfigPath = '/opt/beersty-shared/config';
        $sharedUploadsPath = '/opt/beersty-shared/uploads';
        
        echo "<h3>📁 Creating Shared Directories</h3>";
        
        // Create directories with proper permissions
        $directories = [
            $sharedDbPath => 'Database storage',
            $sharedConfigPath => 'Configuration files', 
            $sharedUploadsPath => 'File uploads'
        ];
        
        foreach ($directories as $dir => $description) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0775, true)) {
                    echo "<p>✅ Created $description directory: <code>$dir</code></p>";
                } else {
                    throw new Exception("Failed to create directory: $dir");
                }
            } else {
                echo "<p>ℹ️ Directory already exists: <code>$dir</code></p>";
            }
        }
        
        // 2. Set up group permissions
        echo "<h3>👥 Setting up Group Permissions</h3>";
        
        $groupName = 'beersty-users';
        
        // Check if group exists, create if not
        $groupExists = !empty(shell_exec("getent group $groupName 2>/dev/null"));
        
        if (!$groupExists) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Group Setup Required</h4>";
            echo "<p>Run these commands as root to set up the group:</p>";
            echo "<div class='code'>";
            echo "sudo groupadd $groupName<br>";
            echo "sudo usermod -a -G $groupName $currentUser<br>";
            
            $systemUsers = getSystemUsers();
            foreach ($systemUsers as $user) {
                if ($user !== $currentUser) {
                    echo "sudo usermod -a -G $groupName $user<br>";
                }
            }
            
            echo "sudo chgrp -R $groupName /opt/beersty-shared<br>";
            echo "sudo chmod -R 775 /opt/beersty-shared<br>";
            echo "sudo chmod -R g+s /opt/beersty-shared<br>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<p>✅ Group '$groupName' already exists</p>";
        }
        
        // 3. Create shared database configuration
        echo "<h3>🗄️ Creating Shared Database Configuration</h3>";
        
        $sharedDbFile = $sharedDbPath . '/beersty_shared.json';
        $configFile = $sharedConfigPath . '/shared_database.php';
        
        // Create shared database file
        if (!file_exists($sharedDbFile)) {
            $initialData = [
                'users' => [
                    [
                        'id' => 'admin-shared-id',
                        'email' => '<EMAIL>',
                        'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'email_verified' => 1,
                        'last_login' => null
                    ]
                ],
                'profiles' => [
                    [
                        'id' => 'admin-shared-id',
                        'email' => '<EMAIL>',
                        'role' => 'admin',
                        'first_name' => 'Shared',
                        'last_name' => 'Admin',
                        'brewery_id' => null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ],
                'breweries' => [],
                'metadata' => [
                    'created_by' => $currentUser,
                    'created_at' => date('Y-m-d H:i:s'),
                    'version' => '1.0',
                    'last_modified' => date('Y-m-d H:i:s')
                ]
            ];
            
            file_put_contents($sharedDbFile, json_encode($initialData, JSON_PRETTY_PRINT));
            chmod($sharedDbFile, 0664);
            echo "<p>✅ Created shared database file: <code>$sharedDbFile</code></p>";
        } else {
            echo "<p>ℹ️ Shared database file already exists</p>";
        }
        
        // Create shared database configuration class
        $sharedConfigContent = '<?php
/**
 * Shared Database Configuration for Multi-User Setup
 */

class SharedDatabase {
    private $dataFile;
    private $lockFile;
    private $data;
    
    public function __construct() {
        $this->dataFile = "/opt/beersty-shared/database/beersty_shared.json";
        $this->lockFile = "/opt/beersty-shared/database/beersty_shared.lock";
        
        if (!file_exists($this->dataFile)) {
            throw new Exception("Shared database file not found. Run setup first.");
        }
        
        $this->loadData();
    }
    
    private function loadData() {
        // Use file locking for concurrent access
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_SH)) {
            $json = file_get_contents($this->dataFile);
            $this->data = json_decode($json, true) ?: [];
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    private function saveData() {
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_EX)) {
            $this->data["metadata"]["last_modified"] = date("Y-m-d H:i:s");
            $this->data["metadata"]["modified_by"] = posix_getpwuid(posix_geteuid())["name"] ?? "unknown";
            
            file_put_contents($this->dataFile, json_encode($this->data, JSON_PRETTY_PRINT));
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    public function prepare($sql) {
        return new SharedStatement($this, $sql);
    }
    
    public function setAttribute($attribute, $value) {
        return true;
    }
    
    public function getAttribute($attribute) {
        if ($attribute === PDO::ATTR_DRIVER_NAME) {
            return "shared_json";
        }
        return null;
    }
    
    public function executeQuery($sql, $params = []) {
        $this->loadData(); // Reload for latest data
        
        // Simple query parser (same as JSONDatabase but with locking)
        $sql = trim($sql);
        
        if (preg_match("/^SELECT\s+(.+?)\s+FROM\s+(\w+)(?:\s+(\w+))?\s*(?:JOIN\s+(\w+)\s+(\w+)\s+ON\s+(.+?))?\s*(?:WHERE\s+(.+?))?$/i", $sql, $matches)) {
            return $this->handleSelect($matches, $params);
        } elseif (preg_match("/^INSERT\s+INTO\s+(\w+)\s*\((.+?)\)\s*VALUES\s*\((.+?)\)$/i", $sql, $matches)) {
            return $this->handleInsert($matches, $params);
        } elseif (preg_match("/^UPDATE\s+(\w+)\s+SET\s+(.+?)(?:\s+WHERE\s+(.+?))?$/i", $sql, $matches)) {
            return $this->handleUpdate($matches, $params);
        }
        
        return [];
    }
    
    private function handleSelect($matches, $params) {
        $table = $matches[2];
        $whereClause = $matches[7] ?? "";
        
        $results = [];
        
        if (!isset($this->data[$table])) {
            return [];
        }
        
        foreach ($this->data[$table] as $row) {
            $result = $row;
            
            // Handle JOIN (simplified)
            if (isset($matches[4]) && $matches[4]) {
                $joinTable = $matches[4];
                if (isset($this->data[$joinTable])) {
                    foreach ($this->data[$joinTable] as $joinRow) {
                        if (isset($row["id"]) && isset($joinRow["id"]) && $row["id"] === $joinRow["id"]) {
                            $result = array_merge($result, $joinRow);
                            break;
                        }
                    }
                }
            }
            
            // Handle WHERE clause
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($result["email"]) || $result["email"] !== $params[0]) {
                    continue;
                }
            }
            
            $results[] = $result;
        }
        
        return $results;
    }
    
    private function handleInsert($matches, $params) {
        $table = $matches[1];
        $fields = array_map("trim", explode(",", $matches[2]));
        
        if (!isset($this->data[$table])) {
            $this->data[$table] = [];
        }
        
        $row = [];
        for ($i = 0; $i < count($fields); $i++) {
            $field = trim($fields[$i]);
            $row[$field] = $params[$i] ?? null;
        }
        
        $this->data[$table][] = $row;
        $this->saveData();
        
        return true;
    }
    
    private function handleUpdate($matches, $params) {
        $table = $matches[1];
        $whereClause = $matches[3] ?? "";
        
        if (!isset($this->data[$table])) {
            return false;
        }
        
        foreach ($this->data[$table] as &$row) {
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($row["email"]) || $row["email"] !== $params[0]) {
                    continue;
                }
            }
            
            // Simple SET parsing
            if (strpos($matches[2], "last_login") !== false) {
                $row["last_login"] = date("Y-m-d H:i:s");
            }
        }
        
        $this->saveData();
        return true;
    }
}

class SharedStatement {
    private $db;
    private $sql;
    private $results;
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        $this->results = $this->db->executeQuery($this->sql, $params);
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}
?>';
        
        file_put_contents($configFile, $sharedConfigContent);
        chmod($configFile, 0664);
        echo "<p>✅ Created shared database configuration: <code>$configFile</code></p>";
        
        // 4. Create symlinks in user directories
        echo "<h3>🔗 Creating Symlinks</h3>";
        
        $currentProjectPath = __DIR__;
        $userDbPath = $currentProjectPath . '/database';
        $userConfigPath = $currentProjectPath . '/config';
        
        // Backup existing database if it exists
        if (is_dir($userDbPath) && !is_link($userDbPath)) {
            $backupPath = $userDbPath . '_backup_' . date('Y-m-d_H-i-s');
            rename($userDbPath, $backupPath);
            echo "<p>📦 Backed up existing database to: <code>$backupPath</code></p>";
        }
        
        // Create symlink to shared database
        if (!is_link($userDbPath)) {
            if (symlink($sharedDbPath, $userDbPath)) {
                echo "<p>✅ Created database symlink: <code>$userDbPath</code> → <code>$sharedDbPath</code></p>";
            } else {
                echo "<p>❌ Failed to create database symlink</p>";
            }
        }
        
        echo "<div class='success'>";
        echo "<h3>🎉 Multi-User Database Setup Complete!</h3>";
        echo "<p>Your shared database structure is now set up. Here's what was created:</p>";
        echo "<ul>";
        echo "<li><strong>Shared Database:</strong> <code>$sharedDbFile</code></li>";
        echo "<li><strong>Shared Config:</strong> <code>$configFile</code></li>";
        echo "<li><strong>Shared Uploads:</strong> <code>$sharedUploadsPath</code></li>";
        echo "<li><strong>Database Symlink:</strong> <code>$userDbPath</code></li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>📋 Next Steps for Other Users:</h4>";
        echo "<ol>";
        echo "<li>Each user should run this setup script from their project directory</li>";
        echo "<li>Or manually create symlinks to the shared directories</li>";
        echo "<li>Ensure all users are in the 'beersty-users' group</li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} elseif ($action === 'link_existing') {
    echo "<h2>🔗 Link to Existing Shared Database</h2>";
    
    $currentUser = getCurrentUser();
    $currentProjectPath = __DIR__;
    $sharedDbPath = '/opt/beersty-shared/database';
    
    if (!is_dir($sharedDbPath)) {
        echo "<div class='error'>";
        echo "<h3>❌ Shared Database Not Found</h3>";
        echo "<p>The shared database directory doesn't exist. Please run the full setup first.</p>";
        echo "</div>";
    } else {
        try {
            $userDbPath = $currentProjectPath . '/database';
            
            // Backup existing database if it exists
            if (is_dir($userDbPath) && !is_link($userDbPath)) {
                $backupPath = $userDbPath . '_backup_' . date('Y-m-d_H-i-s');
                rename($userDbPath, $backupPath);
                echo "<p>📦 Backed up existing database to: <code>$backupPath</code></p>";
            }
            
            // Remove existing symlink if it exists
            if (is_link($userDbPath)) {
                unlink($userDbPath);
            }
            
            // Create new symlink
            if (symlink($sharedDbPath, $userDbPath)) {
                echo "<div class='success'>";
                echo "<h3>✅ Successfully Linked to Shared Database</h3>";
                echo "<p>Your project is now using the shared database at: <code>$sharedDbPath</code></p>";
                echo "</div>";
            } else {
                throw new Exception("Failed to create symlink");
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Linking Failed</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} else {
    echo "<h2>🔧 Multi-User Database Setup Options</h2>";
    
    $currentUser = getCurrentUser();
    $systemUsers = getSystemUsers();
    
    echo "<div class='info'>";
    echo "<h3>📊 System Information</h3>";
    echo "<p><strong>Current User:</strong> $currentUser</p>";
    echo "<p><strong>System Users:</strong> " . implode(', ', $systemUsers) . "</p>";
    echo "<p><strong>Project Path:</strong> " . __DIR__ . "</p>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>⚠️ Important Notes for Multi-User Setup</h3>";
    echo "<ul>";
    echo "<li>This setup creates a shared database that all users can access</li>";
    echo "<li>Requires sudo privileges to set up groups and permissions</li>";
    echo "<li>All users will share the same data and login credentials</li>";
    echo "<li>File uploads will be stored in a shared location</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🚀 Setup Options</h3>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=setup_shared' style='display: inline-block; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>🔧 Full Multi-User Setup</a>";
    echo "<a href='?action=link_existing' style='display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>🔗 Link to Existing Shared DB</a>";
    echo "</div>";
    
    echo "<h3>📋 What Each Option Does:</h3>";
    echo "<ul>";
    echo "<li><strong>Full Multi-User Setup:</strong> Creates shared directories, sets up permissions, creates the shared database</li>";
    echo "<li><strong>Link to Existing:</strong> Links your current project to an already-created shared database</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>Multi-User Database Setup for Beersty on Kali Linux | Current User: " . getCurrentUser() . "</small></p>";
?>
