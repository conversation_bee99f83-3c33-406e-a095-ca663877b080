# PORT 8000 + MYSQL SETUP (Based on successful session)

Write-Host "SETTING UP PORT 8000 + MYSQL" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

# Stop Apache
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 2

# Configure Apache for port 8000
$httpdConf = "C:\xampp\apache\conf\httpd.conf"
if (Test-Path $httpdConf) {
    $content = Get-Content $httpdConf
    for ($i = 0; $i -lt $content.Length; $i++) {
        if ($content[$i] -match "^Listen\s+\d+") {
            $content[$i] = "Listen 8000"
            Write-Host "Apache set to port 8000" -ForegroundColor Green
            break
        }
    }
    $content | Set-Content $httpdConf -Encoding UTF8
}

# Start Apache
Start-Process "C:\xampp\apache\bin\httpd.exe" -WindowStyle Hidden
Write-Host "Apache starting..." -ForegroundColor Cyan

# Start MySQL
Start-Process "C:\xampp\mysql_start.bat" -WindowStyle Hidden
Write-Host "MySQL starting..." -ForegroundColor Cyan

Start-Sleep 5

# Test port 8000
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -UseBasicParsing
    Write-Host "SUCCESS: Port 8000 working!" -ForegroundColor Green
    
    # Open URLs
    Start-Process "http://localhost:8000/beersty-lovable"
    Start-Process "http://localhost:8000/beersty-lovable/admin/user-management.php"
    
    Write-Host "URLs opened - check your browser!" -ForegroundColor Green
    
} catch {
    Write-Host "Port 8000 test failed" -ForegroundColor Red
}

# Test MySQL connection
Write-Host "Testing MySQL connection..." -ForegroundColor Cyan
try {
    $mysqlTest = & "C:\xampp\mysql\bin\mysql.exe" -u root -e "SELECT 1;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "MySQL connection successful!" -ForegroundColor Green
    } else {
        Write-Host "MySQL connection failed" -ForegroundColor Red
    }
} catch {
    Write-Host "MySQL test error" -ForegroundColor Red
}

Write-Host ""
Write-Host "SETUP COMPLETE!" -ForegroundColor Green
Write-Host "Your URLs:" -ForegroundColor Yellow
Write-Host "  http://localhost:8000/beersty-lovable" -ForegroundColor White
Write-Host "  http://localhost:8000/beersty-lovable/admin/user-management.php" -ForegroundColor White

Read-Host "Press Enter to continue"
