/**
 * Beersty Service Worker
 * Phase 9: Design & Mobile Optimization
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'beersty-v1.0.0';
const OFFLINE_URL = '/beersty/offline.html';

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/beersty/',
    '/beersty/index.php',
    '/beersty/offline.html',
    '/beersty/manifest.json',
    
    // CSS Files
    '/beersty/assets/css/style.css',
    '/beersty/assets/css/mobile.css',
    '/beersty/assets/css/pwa.css',
    
    // JavaScript Files
    '/beersty/assets/js/main.js',
    '/beersty/assets/js/mobile.js',
    '/beersty/assets/js/pwa.js',
    
    // External CDN resources
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://code.jquery.com/jquery-3.7.1.min.js',
    
    // Icons
    '/beersty/assets/icons/icon-192x192.png',
    '/beersty/assets/icons/icon-512x512.png',
    '/beersty/public/favicon.ico'
];

// API endpoints to cache
const API_CACHE_URLS = [
    '/beersty/api/notifications.php',
    '/beersty/api/global-search.php'
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Try to fetch from network
                return fetch(request)
                    .then(response => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Cache successful responses
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                // Cache API responses with shorter TTL
                                if (isApiRequest(request.url)) {
                                    cache.put(request, responseToCache);
                                }
                                // Cache static resources
                                else if (isStaticResource(request.url)) {
                                    cache.put(request, responseToCache);
                                }
                            });
                        
                        return response;
                    })
                    .catch(() => {
                        // Network failed, try to serve offline page for navigation requests
                        if (request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // For other requests, return a generic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable'
                        });
                    });
            })
    );
});

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync-checkin') {
        event.waitUntil(syncOfflineCheckins());
    }
    
    if (event.tag === 'background-sync-rating') {
        event.waitUntil(syncOfflineRatings());
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: 'You have new activity on Beersty!',
        icon: '/beersty/assets/icons/icon-192x192.png',
        badge: '/beersty/assets/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View',
                icon: '/beersty/assets/icons/view-action.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/beersty/assets/icons/close-action.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'Beersty';
        options.data = { ...options.data, ...data };
    }
    
    event.waitUntil(
        self.registration.showNotification('Beersty', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/beersty/user/notifications.php')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/beersty/')
        );
    }
});

// Helper functions
function isApiRequest(url) {
    return url.includes('/api/') || url.includes('api.php');
}

function isStaticResource(url) {
    return url.includes('/assets/') || 
           url.includes('.css') || 
           url.includes('.js') || 
           url.includes('.png') || 
           url.includes('.jpg') || 
           url.includes('.jpeg') || 
           url.includes('.gif') || 
           url.includes('.svg') ||
           url.includes('.ico');
}

async function syncOfflineCheckins() {
    try {
        // Get offline checkins from IndexedDB
        const offlineCheckins = await getOfflineData('checkins');
        
        for (const checkin of offlineCheckins) {
            try {
                const response = await fetch('/beersty/social/checkin.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(checkin)
                });
                
                if (response.ok) {
                    await removeOfflineData('checkins', checkin.id);
                    console.log('Synced offline checkin:', checkin.id);
                }
            } catch (error) {
                console.error('Failed to sync checkin:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

async function syncOfflineRatings() {
    try {
        // Get offline ratings from IndexedDB
        const offlineRatings = await getOfflineData('ratings');
        
        for (const rating of offlineRatings) {
            try {
                const response = await fetch('/beersty/beers/rate.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(rating)
                });
                
                if (response.ok) {
                    await removeOfflineData('ratings', rating.id);
                    console.log('Synced offline rating:', rating.id);
                }
            } catch (error) {
                console.error('Failed to sync rating:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// IndexedDB helpers (simplified - would need full implementation)
async function getOfflineData(store) {
    // This would connect to IndexedDB and retrieve offline data
    return [];
}

async function removeOfflineData(store, id) {
    // This would remove synced data from IndexedDB
    return true;
}
