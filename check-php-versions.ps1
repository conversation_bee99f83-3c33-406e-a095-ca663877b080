# CHECK PHP VERSIONS - Find all PHP installations

Write-Host "🔍 CHECKING PHP VERSIONS AND INSTALLATIONS" -ForegroundColor Yellow
Write-Host "===========================================" -ForegroundColor Yellow

# 1. Check command line PHP version
Write-Host ""
Write-Host "1. COMMAND LINE PHP VERSION:" -ForegroundColor Cyan
try {
    $phpVersion = php -v 2>$null
    if ($phpVersion) {
        Write-Host "✅ PHP found in PATH:" -ForegroundColor Green
        $phpVersion | Select-Object -First 3 | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
        
        # Get PHP location
        $phpLocation = where.exe php 2>$null
        if ($phpLocation) {
            Write-Host "   Location: $phpLocation" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ No PHP found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ No PHP found in PATH" -ForegroundColor Red
}

# 2. Check XAMPP PHP version
Write-Host ""
Write-Host "2. XAMPP PHP VERSION:" -ForegroundColor Cyan

$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if ($XamppPath) {
    $XamppPhp = Join-Path $XamppPath "php\php.exe"
    if (Test-Path $XamppPhp) {
        Write-Host "✅ XAMPP PHP found at: $XamppPhp" -ForegroundColor Green
        try {
            $xamppPhpVersion = & $XamppPhp -v 2>$null
            if ($xamppPhpVersion) {
                $xamppPhpVersion | Select-Object -First 3 | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
            }
        } catch {
            Write-Host "   Could not get XAMPP PHP version" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ XAMPP PHP not found at: $XamppPhp" -ForegroundColor Red
    }
} else {
    Write-Host "❌ XAMPP not found" -ForegroundColor Red
}

# 3. Find all PHP installations
Write-Host ""
Write-Host "3. ALL PHP INSTALLATIONS ON SYSTEM:" -ForegroundColor Cyan

$PhpLocations = @()

# Common PHP installation paths
$CommonPaths = @(
    "C:\php",
    "C:\Program Files\PHP",
    "C:\Program Files (x86)\PHP",
    "C:\xampp\php",
    "C:\wamp\bin\php",
    "C:\laragon\bin\php",
    "C:\Users\<USER>\AppData\Local\Programs\PHP"
)

foreach ($path in $CommonPaths) {
    if (Test-Path $path) {
        $phpExe = Join-Path $path "php.exe"
        if (Test-Path $phpExe) {
            $PhpLocations += $path
            Write-Host "   FOUND: $path" -ForegroundColor Green
            
            # Get version
            try {
                $version = & $phpExe -v 2>$null | Select-Object -First 1
                if ($version) {
                    Write-Host "     Version: $($version.Split(' ')[1])" -ForegroundColor Gray
                }
            } catch {
                Write-Host "     Version: Could not determine" -ForegroundColor Gray
            }
        }
    }
}

# Search in Program Files for any PHP
Write-Host ""
Write-Host "   Searching Program Files..." -ForegroundColor Gray
try {
    $ProgramFilesPHP = Get-ChildItem -Path "C:\Program Files" -Directory -Name "*php*" -ErrorAction SilentlyContinue
    $ProgramFilesx86PHP = Get-ChildItem -Path "C:\Program Files (x86)" -Directory -Name "*php*" -ErrorAction SilentlyContinue
    
    foreach ($phpDir in $ProgramFilesPHP) {
        $fullPath = "C:\Program Files\$phpDir"
        if (Test-Path "$fullPath\php.exe") {
            Write-Host "   FOUND: $fullPath" -ForegroundColor Green
        }
    }
    
    foreach ($phpDir in $ProgramFilesx86PHP) {
        $fullPath = "C:\Program Files (x86)\$phpDir"
        if (Test-Path "$fullPath\php.exe") {
            Write-Host "   FOUND: $fullPath" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "   Could not search Program Files" -ForegroundColor Gray
}

# 4. Check PATH environment variable
Write-Host ""
Write-Host "4. PHP IN SYSTEM PATH:" -ForegroundColor Cyan

$PathEntries = $env:PATH -split ';'
$PhpInPath = $PathEntries | Where-Object { $_ -match 'php' -or (Test-Path "$_\php.exe") }

if ($PhpInPath) {
    Write-Host "   PHP-related PATH entries:" -ForegroundColor Yellow
    foreach ($entry in $PhpInPath) {
        Write-Host "     $entry" -ForegroundColor White
        if (Test-Path "$entry\php.exe") {
            Write-Host "       ✅ Contains php.exe" -ForegroundColor Green
        }
    }
} else {
    Write-Host "   No PHP found in PATH" -ForegroundColor Gray
}

# 5. Check which PHP Apache is using
Write-Host ""
Write-Host "5. APACHE PHP CONFIGURATION:" -ForegroundColor Cyan

if ($XamppPath) {
    $ApacheConf = Join-Path $XamppPath "apache\conf\httpd.conf"
    if (Test-Path $ApacheConf) {
        Write-Host "   Checking Apache configuration..." -ForegroundColor Gray
        $phpModule = Select-String -Path $ApacheConf -Pattern "LoadModule.*php" -ErrorAction SilentlyContinue
        if ($phpModule) {
            Write-Host "   PHP module configuration:" -ForegroundColor Yellow
            $phpModule | ForEach-Object { Write-Host "     $($_.Line.Trim())" -ForegroundColor White }
        } else {
            Write-Host "   No PHP module configuration found" -ForegroundColor Red
        }
    }
    
    # Check php.ini location
    $PhpIni = Join-Path $XamppPath "php\php.ini"
    if (Test-Path $PhpIni) {
        Write-Host "   ✅ php.ini found at: $PhpIni" -ForegroundColor Green
    } else {
        Write-Host "   ❌ php.ini not found at: $PhpIni" -ForegroundColor Red
    }
}

# 6. Recommendations
Write-Host ""
Write-Host "6. RECOMMENDATIONS:" -ForegroundColor Red
Write-Host "==================" -ForegroundColor Red

if ($PhpLocations.Count -gt 1) {
    Write-Host ""
    Write-Host "⚠️ MULTIPLE PHP INSTALLATIONS DETECTED!" -ForegroundColor Yellow
    Write-Host "This can cause conflicts. Here's what to do:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "OPTION 1: Use only XAMPP PHP (Recommended)" -ForegroundColor Cyan
    Write-Host "1. Remove PHP from system PATH" -ForegroundColor White
    Write-Host "2. Use only XAMPP's PHP for web development" -ForegroundColor White
    Write-Host "3. Access via: http://localhost/beersty-lovable" -ForegroundColor White
    Write-Host ""
    Write-Host "OPTION 2: Fix PATH priority" -ForegroundColor Cyan
    Write-Host "1. Make sure XAMPP PHP comes first in PATH" -ForegroundColor White
    Write-Host "2. Remove conflicting PHP installations" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "✅ Single PHP installation detected - good!" -ForegroundColor Green
}

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Make sure Apache is running in XAMPP Control Panel" -ForegroundColor White
Write-Host "2. Test: http://localhost/beersty-lovable" -ForegroundColor White
Write-Host "3. If issues persist, we'll fix the PHP configuration" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to continue"
