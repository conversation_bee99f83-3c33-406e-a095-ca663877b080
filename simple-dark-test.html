<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Dark Mode Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <style>
        .test-container {
            min-height: 100vh;
            padding: 50px 20px;
        }
        
        .status-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #28a745;
            color: white;
            z-index: 1000;
            font-weight: bold;
        }
    </style>
</head>
<body class="dark-mode">
    <div class="status-box">
        🌙 DARK MODE FORCED ON
    </div>

    <div class="container test-container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="text-center mb-5">🔍 Simple Dark Mode Test</h1>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <h3>✅ This Should Be Dark</h3>
                        <p>If you see this with a dark background, the CSS is working!</p>
                        
                        <form>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" placeholder="<EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" placeholder="Password">
                            </div>
                            
                            <div class="mb-3">
                                <label for="select" class="form-label">Select</label>
                                <select class="form-select" id="select">
                                    <option>Choose...</option>
                                    <option value="1">Option 1</option>
                                    <option value="2">Option 2</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary">Primary Button</button>
                            <button type="button" class="btn btn-secondary">Secondary Button</button>
                        </form>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <h5>🎯 What to Check:</h5>
                    <ul>
                        <li>Background should be dark (#1a1a1a)</li>
                        <li>Text should be white</li>
                        <li>Form inputs should have dark backgrounds</li>
                        <li>Cards should be dark gray</li>
                    </ul>
                </div>
                
                <div class="text-center">
                    <button onclick="toggleMode()" class="btn btn-warning btn-lg">
                        🔄 Toggle Dark/Light Mode
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleMode() {
            const html = document.documentElement;
            const body = document.body;
            const statusBox = document.querySelector('.status-box');
            
            if (html.classList.contains('dark-mode')) {
                html.classList.remove('dark-mode');
                body.classList.remove('dark-mode');
                statusBox.textContent = '☀️ LIGHT MODE FORCED ON';
                statusBox.style.background = '#007bff';
            } else {
                html.classList.add('dark-mode');
                body.classList.add('dark-mode');
                statusBox.textContent = '🌙 DARK MODE FORCED ON';
                statusBox.style.background = '#28a745';
            }
        }
        
        // Log current styles for debugging
        console.log('HTML classes:', document.documentElement.className);
        console.log('Body background:', getComputedStyle(document.body).backgroundColor);
        console.log('CSS --bg-primary:', getComputedStyle(document.documentElement).getPropertyValue('--bg-primary'));
    </script>
</body>
</html>
