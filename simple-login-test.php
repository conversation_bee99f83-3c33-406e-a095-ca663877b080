<?php
/**
 * Simple Login Test
 * Minimal login test without complex error handling
 */

// Start session
session_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔐 Simple Login Test</h1>";

// Include database config
require_once 'config/config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<h2>Processing Login...</h2>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($email) . "</p>";
    echo "<p><strong>Password Length:</strong> " . strlen($password) . " characters</p>";
    
    if (empty($email) || empty($password)) {
        echo "<div style='color: red; padding: 10px; background: #ffe6e6; border-radius: 5px;'>";
        echo "❌ Please fill in all fields.";
        echo "</div>";
    } else {
        try {
            // Connect to database
            $db = new Database();
            $conn = $db->getConnection();
            echo "<p>✅ Database connected</p>";
            
            // Query user
            $sql = "SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
                    FROM users u 
                    JOIN profiles p ON u.id = p.id 
                    WHERE u.email = ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<p>✅ User found in database</p>";
                echo "<p><strong>User Role:</strong> " . htmlspecialchars($user['role']) . "</p>";
                
                // Verify password
                if (password_verify($password, $user['password_hash'])) {
                    echo "<p>✅ Password verified successfully</p>";
                    
                    // Set session variables
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['brewery_id'] = $user['brewery_id'];
                    
                    echo "<p>✅ Session variables set</p>";
                    
                    // Update last login (optional, skip if it causes issues)
                    try {
                        $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                        $updateStmt->execute([$user['id']]);
                        echo "<p>✅ Last login updated</p>";
                    } catch (Exception $e) {
                        echo "<p>⚠️ Last login update failed (non-critical): " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                    
                    // Success message
                    echo "<div style='color: green; padding: 20px; background: #e6ffe6; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h3>🎉 LOGIN SUCCESSFUL!</h3>";
                    echo "<p>Welcome back, " . htmlspecialchars($user['email']) . "!</p>";
                    echo "<p>Your role: " . htmlspecialchars($user['role']) . "</p>";
                    
                    // Determine redirect URL
                    if ($user['role'] === 'admin') {
                        $redirectUrl = '/admin/dashboard.php';
                        echo "<p><a href='$redirectUrl' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Dashboard</a></p>";
                    } elseif ($user['role'] === 'brewery') {
                        $redirectUrl = '/brewery/profile.php';
                        echo "<p><a href='$redirectUrl' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Brewery Profile</a></p>";
                    } else {
                        $redirectUrl = '/index.php';
                        echo "<p><a href='$redirectUrl' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Homepage</a></p>";
                    }
                    echo "</div>";
                    
                } else {
                    echo "<div style='color: red; padding: 10px; background: #ffe6e6; border-radius: 5px;'>";
                    echo "❌ Invalid password. Please check your password and try again.";
                    echo "</div>";
                    
                    // Debug password (remove in production)
                    echo "<p><small>Debug: Hash starts with " . substr($user['password_hash'], 0, 10) . "...</small></p>";
                }
            } else {
                echo "<div style='color: red; padding: 10px; background: #ffe6e6; border-radius: 5px;'>";
                echo "❌ No user found with that email address.";
                echo "</div>";
                
                // Check if user exists in users table only
                $stmt = $conn->prepare("SELECT email FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $userOnly = $stmt->fetch();
                
                if ($userOnly) {
                    echo "<p>⚠️ User exists in users table but missing profile. Contact administrator.</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 10px; background: #ffe6e6; border-radius: 5px;'>";
            echo "❌ Database error: " . htmlspecialchars($e->getMessage());
            echo "</div>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        }
    }
}

// Check current login status
echo "<h2>🔍 Current Login Status</h2>";
if (isset($_SESSION['user_id']) && isset($_SESSION['user_email']) && isset($_SESSION['user_role'])) {
    echo "<div style='color: green; padding: 10px; background: #e6ffe6; border-radius: 5px;'>";
    echo "✅ You are currently logged in as: " . htmlspecialchars($_SESSION['user_email']);
    echo "<br>Role: " . htmlspecialchars($_SESSION['user_role']);
    echo "</div>";
} else {
    echo "<div style='color: orange; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
    echo "⚠️ You are not logged in.";
    echo "</div>";
}
?>

<h2>🔐 Login Form</h2>
<form method="POST" style="max-width: 400px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
    <div style="margin-bottom: 15px;">
        <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
        <input type="email" id="email" name="email" value="<EMAIL>" 
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password:</label>
        <input type="password" id="password" name="password" value="admin123"
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
        🔐 Login
    </button>
</form>

<h2>🔗 Other Tests</h2>
<ul>
    <li><a href="auth/login.php">Try Official Login Page</a></li>
    <li><a href="login-debug-live.php">Live Login Debug</a></li>
    <li><a href="test-session.php">Test Sessions</a></li>
    <li><a href="check-database-structure.php">Check Database</a></li>
    <li><a href="/">Back to Homepage</a></li>
</ul>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}
</style>
