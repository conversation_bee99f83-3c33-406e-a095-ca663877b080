<?php
/**
 * Phase 7 Setup Script
 * Notifications & Communication
 */

require_once 'config/config.php';

echo "<h1>🔔 Phase 7 Setup: Notifications & Communication</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Setting up Phase 7 database tables...</h2>";
    
    // Read and execute the Phase 7 SQL file
    $sqlFile = 'database/phase7_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $conn->exec($statement);
                $successCount++;
                
                // Show progress for major operations
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    echo "<p>✅ Created table: <strong>$tableName</strong></p>";
                }
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<p>❌ Error executing statement: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>📊 Setup Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p><strong>✅ Successful operations:</strong> $successCount</p>";
    echo "<p><strong>❌ Failed operations:</strong> $errorCount</p>";
    echo "</div>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Phase 7 Setup Complete!</h3>";
        echo "<p>All database tables have been created successfully. You can now use:</p>";
        echo "<ul>";
        echo "<li><strong>Notifications System:</strong> Real-time notifications for user activities</li>";
        echo "<li><strong>Messaging System:</strong> Direct messaging between users</li>";
        echo "<li><strong>Email Notifications:</strong> Configurable email alerts</li>";
        echo "<li><strong>Notification Preferences:</strong> User-controlled notification settings</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🚀 Next Steps</h2>";
        echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<ol>";
        echo "<li><strong>Test Notifications:</strong> Follow a user to see new follower notifications</li>";
        echo "<li><strong>Try Messaging:</strong> Send a direct message to another user</li>";
        echo "<li><strong>Configure Email:</strong> Set up SMTP settings for email notifications</li>";
        echo "<li><strong>Customize Preferences:</strong> Visit user preferences to configure notifications</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<h2>📱 New Features Available</h2>";
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>🔔 Notification Types:</h4>";
        echo "<ul>";
        echo "<li>New follower notifications</li>";
        echo "<li>Friend check-in notifications</li>";
        echo "<li>Achievement unlocked notifications</li>";
        echo "<li>Message received notifications</li>";
        echo "<li>Rating liked notifications</li>";
        echo "<li>Comment received notifications</li>";
        echo "</ul>";
        
        echo "<h4>💬 Messaging Features:</h4>";
        echo "<ul>";
        echo "<li>Direct messaging between users</li>";
        echo "<li>Real-time message updates</li>";
        echo "<li>Message read status</li>";
        echo "<li>Message moderation tools</li>";
        echo "<li>Conversation management</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔗 Quick Links</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p><a href='/beersty/user/notifications.php' class='btn btn-primary' style='margin-right: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>View Notifications</a>";
        echo "<a href='/beersty/user/messages.php' class='btn btn-success' style='margin-right: 10px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Open Messages</a>";
        echo "<a href='/beersty/user/preferences.php' class='btn btn-secondary' style='padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>Notification Settings</a></p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Setup Completed with Errors</h3>";
        echo "<p>Some operations failed. Please check the error messages above and try running the setup again.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
