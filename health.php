<?php
/**
 * Health Check Endpoint
 * Phase 8 - Production Deployment & Optimization
 * 
 * Comprehensive health check for monitoring and load balancers
 */

// Prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: application/json');

// Start timing
$startTime = microtime(true);

// Initialize health status
$health = [
    'status' => 'healthy',
    'timestamp' => date('c'),
    'version' => '6.0.0',
    'environment' => $_ENV['APP_ENV'] ?? 'production',
    'checks' => [],
    'metrics' => []
];

try {
    // Load configuration
    require_once 'config/database.php';
    
    // Initialize monitoring system if available
    if (file_exists('includes/MonitoringSystem.php')) {
        require_once 'includes/MonitoringSystem.php';
        
        try {
            $pdo = new PDO($dsn, $username, $password, $options);
            
            // Initialize Redis if available
            $redis = null;
            if (class_exists('Redis') && isset($_ENV['REDIS_HOST'])) {
                $redis = new Redis();
                try {
                    $redis->connect($_ENV['REDIS_HOST'], $_ENV['REDIS_PORT'] ?? 6379);
                    if (!empty($_ENV['REDIS_PASSWORD'])) {
                        $redis->auth($_ENV['REDIS_PASSWORD']);
                    }
                } catch (Exception $e) {
                    $redis = null;
                }
            }
            
            $monitoring = new MonitoringSystem($pdo, $redis);
            $healthCheck = $monitoring->performHealthCheck();
            
            $health['checks'] = $healthCheck['checks'];
            $health['status'] = $healthCheck['overall_status'];
            
        } catch (Exception $e) {
            // Fallback to basic health checks
            $health['checks'] = performBasicHealthChecks();
        }
    } else {
        // Perform basic health checks without monitoring system
        $health['checks'] = performBasicHealthChecks();
    }
    
    // Add performance metrics
    $health['metrics'] = [
        'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
        'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
        'memory_peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
        'php_version' => PHP_VERSION,
        'server_time' => date('c'),
        'uptime' => getServerUptime()
    ];
    
    // Determine overall status
    $health['status'] = determineOverallStatus($health['checks']);
    
    // Set appropriate HTTP status code
    $httpStatus = 200;
    if ($health['status'] === 'unhealthy') {
        $httpStatus = 503; // Service Unavailable
    } elseif ($health['status'] === 'warning') {
        $httpStatus = 200; // OK but with warnings
    }
    
    http_response_code($httpStatus);
    
} catch (Exception $e) {
    // Critical error - system is unhealthy
    $health['status'] = 'unhealthy';
    $health['error'] = $e->getMessage();
    $health['checks']['critical_error'] = [
        'status' => 'unhealthy',
        'message' => 'Critical system error occurred',
        'error' => $e->getMessage()
    ];
    
    http_response_code(503);
}

// Output health status
echo json_encode($health, JSON_PRETTY_PRINT);

/**
 * Perform basic health checks without monitoring system
 */
function performBasicHealthChecks() {
    $checks = [];
    
    // PHP Health Check
    $checks['php'] = [
        'status' => 'healthy',
        'version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'message' => 'PHP is running normally'
    ];
    
    // Database Health Check
    try {
        global $dsn, $username, $password, $options;
        $pdo = new PDO($dsn, $username, $password, $options);
        $stmt = $pdo->query('SELECT 1');
        
        $checks['database'] = [
            'status' => 'healthy',
            'connection' => 'successful',
            'message' => 'Database connection successful'
        ];
    } catch (Exception $e) {
        $checks['database'] = [
            'status' => 'unhealthy',
            'connection' => 'failed',
            'error' => $e->getMessage(),
            'message' => 'Database connection failed'
        ];
    }
    
    // File System Health Check
    $checks['filesystem'] = checkFilesystem();
    
    // Session Health Check
    $checks['sessions'] = [
        'status' => session_status() !== PHP_SESSION_DISABLED ? 'healthy' : 'unhealthy',
        'session_status' => session_status(),
        'message' => 'Session functionality check'
    ];
    
    // Redis Health Check (if available)
    if (class_exists('Redis') && isset($_ENV['REDIS_HOST'])) {
        try {
            $redis = new Redis();
            $redis->connect($_ENV['REDIS_HOST'], $_ENV['REDIS_PORT'] ?? 6379);
            
            if (!empty($_ENV['REDIS_PASSWORD'])) {
                $redis->auth($_ENV['REDIS_PASSWORD']);
            }
            
            $redis->ping();
            
            $checks['redis'] = [
                'status' => 'healthy',
                'connection' => 'successful',
                'message' => 'Redis connection successful'
            ];
        } catch (Exception $e) {
            $checks['redis'] = [
                'status' => 'unhealthy',
                'connection' => 'failed',
                'error' => $e->getMessage(),
                'message' => 'Redis connection failed'
            ];
        }
    } else {
        $checks['redis'] = [
            'status' => 'disabled',
            'message' => 'Redis not configured or not available'
        ];
    }
    
    // Configuration Health Check
    $checks['configuration'] = checkConfiguration();
    
    return $checks;
}

/**
 * Check filesystem health
 */
function checkFilesystem() {
    $issues = [];
    
    // Check critical directories
    $criticalDirs = [
        'config' => 'Configuration directory',
        'includes' => 'Includes directory',
        'assets' => 'Assets directory',
        'uploads' => 'Uploads directory'
    ];
    
    foreach ($criticalDirs as $dir => $description) {
        if (!is_dir($dir)) {
            $issues[] = "$description missing";
        } elseif (!is_readable($dir)) {
            $issues[] = "$description not readable";
        }
    }
    
    // Check writable directories
    $writableDirs = ['uploads', 'logs', 'cache'];
    foreach ($writableDirs as $dir) {
        if (is_dir($dir) && !is_writable($dir)) {
            $issues[] = "$dir directory not writable";
        }
    }
    
    // Check disk space
    $freeSpace = disk_free_space('.');
    $totalSpace = disk_total_space('.');
    $usagePercent = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 2);
    
    if ($usagePercent > 90) {
        $issues[] = "Disk usage critical: {$usagePercent}%";
    } elseif ($usagePercent > 80) {
        $issues[] = "Disk usage warning: {$usagePercent}%";
    }
    
    return [
        'status' => empty($issues) ? 'healthy' : (count($issues) > 2 ? 'unhealthy' : 'warning'),
        'disk_usage_percent' => $usagePercent,
        'free_space_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
        'issues' => $issues,
        'message' => empty($issues) ? 'Filesystem healthy' : 'Filesystem issues detected'
    ];
}

/**
 * Check configuration health
 */
function checkConfiguration() {
    $issues = [];
    
    // Check critical configuration files
    $configFiles = [
        'config/database.php' => 'Database configuration',
        '.htaccess' => 'Apache configuration',
        'includes/auth.php' => 'Authentication configuration'
    ];
    
    foreach ($configFiles as $file => $description) {
        if (!file_exists($file)) {
            $issues[] = "$description file missing";
        } elseif (!is_readable($file)) {
            $issues[] = "$description file not readable";
        }
    }
    
    // Check environment variables
    $requiredEnvVars = ['DB_HOST', 'DB_NAME', 'DB_USER'];
    foreach ($requiredEnvVars as $var) {
        if (empty($_ENV[$var])) {
            $issues[] = "Environment variable $var not set";
        }
    }
    
    // Check PHP extensions
    $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $issues[] = "PHP extension $ext not loaded";
        }
    }
    
    return [
        'status' => empty($issues) ? 'healthy' : (count($issues) > 2 ? 'unhealthy' : 'warning'),
        'php_extensions' => get_loaded_extensions(),
        'issues' => $issues,
        'message' => empty($issues) ? 'Configuration healthy' : 'Configuration issues detected'
    ];
}

/**
 * Determine overall status from individual checks
 */
function determineOverallStatus($checks) {
    $hasUnhealthy = false;
    $hasWarning = false;
    
    foreach ($checks as $check) {
        if (isset($check['status'])) {
            if ($check['status'] === 'unhealthy') {
                $hasUnhealthy = true;
            } elseif ($check['status'] === 'warning') {
                $hasWarning = true;
            }
        }
    }
    
    if ($hasUnhealthy) {
        return 'unhealthy';
    } elseif ($hasWarning) {
        return 'warning';
    }
    
    return 'healthy';
}

/**
 * Get server uptime (simplified)
 */
function getServerUptime() {
    if (function_exists('sys_getloadavg') && is_readable('/proc/uptime')) {
        $uptime = file_get_contents('/proc/uptime');
        $uptime = floatval($uptime);
        
        $days = floor($uptime / 86400);
        $hours = floor(($uptime % 86400) / 3600);
        $minutes = floor(($uptime % 3600) / 60);
        
        return "{$days}d {$hours}h {$minutes}m";
    }
    
    return 'unknown';
}
?>
