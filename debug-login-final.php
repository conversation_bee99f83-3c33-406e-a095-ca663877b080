<?php
/**
 * Final Login Debug
 * Comprehensive login debugging with real form submission
 */

// Start session first
session_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Final Login Debug</h1>";

// Include config
require_once 'config/config.php';

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>📝 Processing Login Form</h2>";
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Form Data Received:</strong><br>";
    echo "Email: " . htmlspecialchars($email) . "<br>";
    echo "Password: " . (empty($password) ? 'EMPTY' : '[' . strlen($password) . ' characters]') . "<br>";
    echo "</div>";
    
    // Step-by-step debugging
    try {
        echo "<h3>Step 1: Input Validation</h3>";
        if (empty($email) || empty($password)) {
            echo "<p style='color: red;'>❌ Empty fields detected</p>";
            $_SESSION['error_message'] = 'Please fill in all fields.';
        } else {
            echo "<p style='color: green;'>✅ Fields are not empty</p>";
            
            echo "<h3>Step 2: Sanitize Input</h3>";
            $cleanEmail = sanitizeInput($email);
            echo "<p>✅ Email sanitized: " . htmlspecialchars($cleanEmail) . "</p>";
            
            echo "<h3>Step 3: Database Connection</h3>";
            $db = new Database();
            $conn = $db->getConnection();
            echo "<p style='color: green;'>✅ Database connected successfully</p>";
            
            echo "<h3>Step 4: Query User</h3>";
            $sql = "SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
                    FROM users u 
                    JOIN profiles p ON u.id = p.id 
                    WHERE u.email = ?";
            
            echo "<p><strong>SQL Query:</strong></p>";
            echo "<pre>" . htmlspecialchars($sql) . "</pre>";
            echo "<p><strong>Parameters:</strong> ['" . htmlspecialchars($cleanEmail) . "']</p>";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([$cleanEmail]);
            $user = $stmt->fetch();
            
            if ($user) {
                echo "<p style='color: green;'>✅ User found in database</p>";
                echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
                echo "<strong>User Data:</strong><br>";
                echo "ID: " . htmlspecialchars($user['id']) . "<br>";
                echo "Email: " . htmlspecialchars($user['email']) . "<br>";
                echo "Role: " . htmlspecialchars($user['role']) . "<br>";
                echo "Brewery ID: " . htmlspecialchars($user['brewery_id'] ?? 'null') . "<br>";
                echo "Password Hash: " . substr($user['password_hash'], 0, 30) . "...<br>";
                echo "</div>";
                
                echo "<h3>Step 5: Password Verification</h3>";
                echo "<p><strong>Testing password:</strong> '$password'</p>";
                echo "<p><strong>Against hash:</strong> " . substr($user['password_hash'], 0, 50) . "...</p>";
                
                if (password_verify($password, $user['password_hash'])) {
                    echo "<p style='color: green;'>✅ Password verification successful!</p>";
                    
                    echo "<h3>Step 6: Set Session Variables</h3>";
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['brewery_id'] = $user['brewery_id'];
                    
                    echo "<p style='color: green;'>✅ Session variables set:</p>";
                    echo "<ul>";
                    echo "<li>user_id: " . htmlspecialchars($_SESSION['user_id']) . "</li>";
                    echo "<li>user_email: " . htmlspecialchars($_SESSION['user_email']) . "</li>";
                    echo "<li>user_role: " . htmlspecialchars($_SESSION['user_role']) . "</li>";
                    echo "<li>brewery_id: " . htmlspecialchars($_SESSION['brewery_id'] ?? 'null') . "</li>";
                    echo "</ul>";
                    
                    echo "<h3>Step 7: Test Login Functions</h3>";
                    if (isLoggedIn()) {
                        echo "<p style='color: green;'>✅ isLoggedIn() returns true</p>";
                        
                        $currentUser = getCurrentUser();
                        if ($currentUser) {
                            echo "<p style='color: green;'>✅ getCurrentUser() works</p>";
                            echo "<p>Current user: " . htmlspecialchars($currentUser['email']) . " (" . htmlspecialchars($currentUser['role']) . ")</p>";
                        } else {
                            echo "<p style='color: red;'>❌ getCurrentUser() returns null</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ isLoggedIn() returns false</p>";
                    }
                    
                    echo "<h3>Step 8: Update Last Login</h3>";
                    try {
                        $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                        $updateStmt->execute([$user['id']]);
                        echo "<p style='color: green;'>✅ Last login updated</p>";
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠️ Last login update failed (non-critical): " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                    
                    echo "<h3>Step 9: Determine Redirect</h3>";
                    if ($user['role'] === 'admin') {
                        $redirectUrl = url('admin/dashboard.php');
                        echo "<p>✅ Admin user - would redirect to: <strong>$redirectUrl</strong></p>";
                    } elseif ($user['role'] === 'brewery') {
                        $redirectUrl = url('brewery/profile.php');
                        echo "<p>✅ Brewery user - would redirect to: <strong>$redirectUrl</strong></p>";
                    } else {
                        $redirectUrl = url('index.php');
                        echo "<p>✅ Regular user - would redirect to: <strong>$redirectUrl</strong></p>";
                    }
                    
                    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
                    echo "<h3>🎉 LOGIN SUCCESSFUL!</h3>";
                    echo "<p>All steps completed successfully. You are now logged in!</p>";
                    echo "<p><a href='$redirectUrl' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>";
                    echo "<p><a href='auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Real Login Page</a></p>";
                    echo "</div>";
                    
                } else {
                    echo "<p style='color: red;'>❌ Password verification failed!</p>";
                    
                    // Debug password hash
                    echo "<h4>Password Debug:</h4>";
                    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
                    echo "<strong>Input Password:</strong> '$password'<br>";
                    echo "<strong>Stored Hash:</strong> " . $user['password_hash'] . "<br>";
                    echo "<strong>Hash Algorithm:</strong> " . (substr($user['password_hash'], 0, 4) === '$2y$' ? 'bcrypt' : 'unknown') . "<br>";
                    echo "<strong>Hash Length:</strong> " . strlen($user['password_hash']) . " characters<br>";
                    echo "</div>";
                    
                    // Try to fix the password
                    echo "<h4>🔧 Fixing Password Hash:</h4>";
                    $newHash = password_hash($password, PASSWORD_DEFAULT);
                    $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
                    $updateStmt->execute([$newHash, $cleanEmail]);
                    echo "<p style='color: green;'>✅ Password hash updated. Try logging in again.</p>";
                    
                    $_SESSION['error_message'] = 'Password updated. Please try logging in again.';
                }
            } else {
                echo "<p style='color: red;'>❌ No user found with email: " . htmlspecialchars($cleanEmail) . "</p>";
                
                // Check if user exists in users table only
                echo "<h4>Checking users table only:</h4>";
                $stmt = $conn->prepare("SELECT id, email FROM users WHERE email = ?");
                $stmt->execute([$cleanEmail]);
                $userOnly = $stmt->fetch();
                
                if ($userOnly) {
                    echo "<p style='color: orange;'>⚠️ User exists in users table but missing profile</p>";
                    echo "<p>User ID: " . htmlspecialchars($userOnly['id']) . "</p>";
                    
                    // Create missing profile
                    echo "<p>🔧 Creating missing profile...</p>";
                    $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
                    $stmt->execute([$userOnly['id'], $userOnly['email']]);
                    echo "<p style='color: green;'>✅ Profile created. Try logging in again.</p>";
                } else {
                    echo "<p style='color: red;'>❌ User does not exist in users table either</p>";
                    
                    // Create the user
                    echo "<p>🔧 Creating admin user...</p>";
                    $userId = bin2hex(random_bytes(16));
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
                    $stmt->execute([$userId, $cleanEmail, $passwordHash]);
                    
                    $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
                    $stmt->execute([$userId, $cleanEmail]);
                    
                    echo "<p style='color: green;'>✅ Admin user created. Try logging in again.</p>";
                }
                
                $_SESSION['error_message'] = 'User account fixed. Please try logging in again.';
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ Exception Occurred!</h3>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<p><strong>Stack Trace:</strong></p>";
        echo "<pre style='font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
        
        $_SESSION['error_message'] = 'Database error: ' . $e->getMessage();
    }
}

// Show current session status
echo "<h2>🔍 Current Session Status</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<strong>Session ID:</strong> " . session_id() . "<br>";
echo "<strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "<br>";

if (!empty($_SESSION)) {
    echo "<strong>Session Data:</strong><br>";
    foreach ($_SESSION as $key => $value) {
        echo "  $key: " . htmlspecialchars($value) . "<br>";
    }
} else {
    echo "<strong>Session Data:</strong> Empty<br>";
}

if (function_exists('isLoggedIn')) {
    echo "<strong>isLoggedIn():</strong> " . (isLoggedIn() ? 'true' : 'false') . "<br>";
} else {
    echo "<strong>isLoggedIn():</strong> Function not found<br>";
}
echo "</div>";

// Show error messages
if (isset($_SESSION['error_message'])) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>Error Message:</strong> " . $_SESSION['error_message'];
    echo "</div>";
    unset($_SESSION['error_message']);
}
?>

<h2>🔐 Test Login Form</h2>
<form method="POST" style="max-width: 400px; background: #f8f9fa; padding: 20px; border-radius: 8px;">
    <div style="margin-bottom: 15px;">
        <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
        <input type="email" id="email" name="email" value="<EMAIL>" 
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password:</label>
        <input type="password" id="password" name="password" value="admin123"
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;" required>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%;">
        🔍 Debug Login Process
    </button>
</form>

<h2>🔗 Quick Links</h2>
<ul>
    <li><a href="auth/login.php">Try Real Login Page</a></li>
    <li><a href="simple-login-test.php">Simple Login Test</a></li>
    <li><a href="fix-mysql-connection.php">Fix MySQL Connection</a></li>
    <li><a href="/">Back to Homepage</a></li>
</ul>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
