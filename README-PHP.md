# Beersty - PHP Brewery Management System

A complete PHP web application for managing breweries, built with Bootstrap, FontAwesome, JavaScript, AJAX, CSS, and MySQL.

## 🍺 Features

- **Multi-Role Authentication System**
  - Admin users: Full system management
  - Brewery users: Manage their own brewery profile
  - Customer users: Browse and interact with breweries

- **Brewery Management**
  - Profile management (name, address, description, images)
  - Social media integration (Facebook, Instagram, Twitter)
  - Gallery management for brewery images
  - Social features (followers, likes, posts)

- **Menu & Digital Board System**
  - Menu management for food and beverages
  - Digital board displays for in-brewery screens
  - Customizable display settings

- **Coupon & Promotion System**
  - Create and manage discount coupons
  - Multiple discount types (percentage, fixed, BOGO, etc.)
  - QR code generation for redemption

- **Data Import/Export**
  - CSV import functionality for bulk brewery data
  - Admin dashboard for system oversight

## 🛠 Technology Stack

- **Backend**: PHP 8.0+ (via XAMPP)
- **Database**: MySQL 8.0+ (via XAMPP)
- **Web Server**: Apache (via XAMPP)
- **Frontend**: Bootstrap 5.3, FontAwesome 6.4, jQuery 3.7
- **Styling**: Custom CSS with Bootstrap components
- **JavaScript**: Vanilla JS + jQuery for AJAX functionality
- **Development Environment**: XAMPP (No Node.js or Tailwind required)

## 📁 Project Structure

```
/
├── config/                 # Configuration files
│   ├── database.php       # Database connection
│   ├── config.php         # Application configuration
│   └── .env.example       # Environment variables template
├── includes/              # Common includes
│   ├── header.php         # Common header
│   └── footer.php         # Common footer
├── models/                # Data models (to be created)
├── controllers/           # Controllers (to be created)
├── api/                   # API endpoints (to be created)
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Images
├── uploads/               # File uploads
├── auth/                  # Authentication pages
│   ├── login.php         # Login page
│   ├── register.php      # Registration page
│   └── logout.php        # Logout handler
├── admin/                 # Admin pages
│   └── dashboard.php     # Admin dashboard
├── brewery/               # Brewery management pages
│   └── profile.php       # Brewery profile management
├── breweries/             # Public brewery pages
│   └── listing.php       # Brewery listing
├── database/              # Database files
│   └── schema.sql        # Database schema
├── index.php             # Homepage
└── setup-database.ps1   # Database setup script
```

## 🚀 XAMPP Installation (Recommended)

### Quick Setup with XAMPP

1. **Install XAMPP** from [https://www.apachefriends.org/download.html](https://www.apachefriends.org/download.html)
2. **Copy project** to `C:\xampp\htdocs\beersty\`
3. **Run setup** as Administrator:
   ```batch
   setup-xampp.bat
   ```
4. **Access application** at [http://localhost/beersty/](http://localhost/beersty/)

### Manual XAMPP Setup

#### Step 1: Install XAMPP
- Download and install XAMPP to `C:\xampp`
- Start Apache and MySQL services in XAMPP Control Panel

#### Step 2: Copy Project Files
```powershell
# Copy to XAMPP htdocs
Copy-Item -Path ".\*" -Destination "C:\xampp\htdocs\beersty\" -Recurse
```

#### Step 3: Set Up Database
```powershell
cd C:\xampp\htdocs\beersty
.\setup-database.ps1
```

### Alternative Installation (Advanced Users)

If you prefer a custom setup without XAMPP:

#### Prerequisites
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Web server (Apache, Nginx, or IIS)
- PowerShell (for setup script)

### Step 3: Configure Web Server

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
```nginx
server {
    listen 80;
    server_name localhost;
    root /path/to/beersty;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### Step 4: Set Permissions

```powershell
# Windows (PowerShell as Administrator)
icacls "uploads" /grant "IIS_IUSRS:(OI)(CI)F"
icacls "config" /grant "IIS_IUSRS:(OI)(CI)R"
```

### Step 5: Access the Application

1. Start your web server
2. Navigate to `http://localhost/beersty` (or your configured URL)
3. Log in with the default admin account:
   - Email: `<EMAIL>`
   - Password: `admin123`

## 🔧 Configuration

### Environment Variables

Copy `config/.env.example` to `config/.env` and configure:

```env
DB_HOST=localhost
DB_NAME=beersty_db
DB_USER=root
DB_PASSWORD=your_password

APP_NAME="Beersty - Brewery Management System"
APP_URL=http://localhost
APP_ENV=development
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880

SESSION_LIFETIME=7200
```

### Database Configuration

The database configuration is handled in `config/database.php`. The setup script automatically creates the `.env` file with your database credentials.

## 📊 Database Schema

The application uses the following main tables:

- `users` - User authentication
- `profiles` - User profiles and roles
- `breweries` - Brewery information
- `beer_menu` - Beer menu items
- `food_menu` - Food menu items
- `brewery_coupons` - Discount coupons
- `digital_boards` - Digital board configurations
- `gallery_images` - Brewery gallery images
- `posts` - Brewery posts
- `brewery_followers` - Follower relationships
- `brewery_likes` - Like relationships

## 🎨 Customization

### Styling

- Main styles: `assets/css/style.css`
- Page-specific styles: `assets/css/[page].css`
- Bootstrap variables can be customized by overriding CSS custom properties

### JavaScript

- Main functionality: `assets/js/main.js`
- Page-specific scripts can be added via the `$pageJS` variable in PHP files

### Themes

The application supports light/dark themes through CSS custom properties and Bootstrap's color system.

## 🔐 Security Features

- Password hashing with PHP's `password_hash()`
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- CSRF protection (to be implemented)
- Session security with proper configuration
- File upload validation and restrictions

## 🚀 Development

### Adding New Pages

1. Create PHP file in appropriate directory
2. Include header and footer
3. Add page-specific CSS/JS if needed
4. Update navigation in `includes/header.php`

### Database Operations

Use the Database class for all database operations:

```php
$db = new Database();
$conn = $db->getConnection();
$stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
$stmt->execute([$breweryId]);
$brewery = $stmt->fetch();
```

### AJAX Endpoints

Create API endpoints in the `api/` directory for AJAX functionality.

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions, please create an issue in the repository.

---

**Happy Brewing! 🍺**
