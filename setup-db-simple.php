<?php
/**
 * Simple Database Setup for Beersty
 * Run this in your browser to set up the database
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Beersty Database Setup</h1>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'beersty_db';

try {
    // Connect to MySQL (without selecting database first)
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Connected to MySQL successfully</p>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "<p style='color: green;'>✓ Database '$database' created</p>";
    
    // Select the database
    $pdo->exec("USE $database");
    
    // Create tables
    $sql = "
    -- Users table
    CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        email_verified BOOLEAN DEFAULT FALSE,
        last_login TIMESTAMP NULL
    );

    -- Profiles table (Enhanced for Phase 1)
    CREATE TABLE IF NOT EXISTS profiles (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        role ENUM('admin', 'brewery', 'customer', 'beer_enthusiast', 'beer_expert') NOT NULL DEFAULT 'customer',
        brewery_id VARCHAR(36) NULL,

        -- Enhanced Profile Fields (Phase 1)
        first_name VARCHAR(100) NULL,
        last_name VARCHAR(100) NULL,
        username VARCHAR(50) UNIQUE NULL,
        bio TEXT NULL,
        avatar VARCHAR(255) NULL,
        location VARCHAR(255) NULL,
        hometown VARCHAR(255) NULL,
        date_of_birth DATE NULL,

        -- Social Media Links
        website VARCHAR(255) NULL,
        instagram VARCHAR(100) NULL,
        twitter VARCHAR(100) NULL,
        facebook VARCHAR(100) NULL,

        -- Privacy Settings
        profile_visibility ENUM('public', 'friends', 'private') DEFAULT 'public',
        show_location BOOLEAN DEFAULT TRUE,
        show_age BOOLEAN DEFAULT FALSE,
        allow_messages BOOLEAN DEFAULT TRUE,

        -- Stats (will be calculated)
        total_checkins INT DEFAULT 0,
        total_reviews INT DEFAULT 0,
        total_photos INT DEFAULT 0,
        follower_count INT DEFAULT 0,
        following_count INT DEFAULT 0,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Breweries table
    CREATE TABLE IF NOT EXISTS breweries (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(255) NOT NULL,
        address TEXT NULL,
        city VARCHAR(100) NULL,
        state VARCHAR(50) NULL,
        zip VARCHAR(20) NULL,
        phone VARCHAR(20) NULL,
        website VARCHAR(255) NULL,
        email VARCHAR(255) NULL,
        description TEXT NULL,
        logo VARCHAR(255) NULL,
        feature_image VARCHAR(255) NULL,
        avatar VARCHAR(255) NULL,
        brewery_type VARCHAR(50) NOT NULL DEFAULT 'micro',
        social_links JSON NULL,
        claimable BOOLEAN DEFAULT TRUE,
        claimed BOOLEAN DEFAULT FALSE,
        verification_open BOOLEAN DEFAULT TRUE,
        verified BOOLEAN DEFAULT FALSE,
        follower_count INT DEFAULT 0,
        like_count INT DEFAULT 0,

        -- Location data (Phase 5)
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- Beer Styles table (Phase 2)
    CREATE TABLE IF NOT EXISTS beer_styles (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(100) NOT NULL UNIQUE,
        category VARCHAR(50) NOT NULL,
        description TEXT NULL,
        characteristics JSON NULL,
        abv_min DECIMAL(4,2) NULL,
        abv_max DECIMAL(4,2) NULL,
        ibu_min INT NULL,
        ibu_max INT NULL,
        srm_min INT NULL,
        srm_max INT NULL,
        parent_style_id VARCHAR(36) NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
    );

    -- Enhanced Beer Menu table (Phase 2)
    CREATE TABLE IF NOT EXISTS beer_menu (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        brewery_id VARCHAR(36) NOT NULL,
        beer_style_id VARCHAR(36) NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NULL,
        description TEXT NULL,
        abv DECIMAL(4,2) NULL,
        ibu INT NULL,
        srm INT NULL,
        price DECIMAL(8,2) NULL,
        origin VARCHAR(100) NULL,

        -- Enhanced beer information (Phase 2)
        hops TEXT NULL,
        malts TEXT NULL,
        yeast VARCHAR(100) NULL,
        brewing_process TEXT NULL,
        serving_temp_min INT NULL,
        serving_temp_max INT NULL,
        food_pairings TEXT NULL,

        -- Media and availability
        thumbnail VARCHAR(255) NULL,
        images JSON NULL,
        featured BOOLEAN DEFAULT FALSE,
        seasonal BOOLEAN DEFAULT FALSE,
        limited_edition BOOLEAN DEFAULT FALSE,
        available BOOLEAN DEFAULT TRUE,
        availability_start DATE NULL,
        availability_end DATE NULL,
        added_date DATE NULL,

        -- Statistics (calculated fields)
        average_rating DECIMAL(3,2) DEFAULT 0.00,
        total_ratings INT DEFAULT 0,
        total_checkins INT DEFAULT 0,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        FOREIGN KEY (beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
    );

    -- Food Menu table
    CREATE TABLE IF NOT EXISTS food_menu (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        brewery_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        category VARCHAR(100) NULL,
        description TEXT NULL,
        price DECIMAL(8,2) NULL,
        image VARCHAR(255) NULL,
        available BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
    );

    -- Brewery Coupons table
    CREATE TABLE IF NOT EXISTS brewery_coupons (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        brewery_id VARCHAR(36) NOT NULL,
        code VARCHAR(50) NOT NULL,
        description TEXT NOT NULL,
        discount_value VARCHAR(50) NOT NULL,
        expiry_date DATE NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        qr_code_url VARCHAR(255) NULL,
        redemption_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        UNIQUE KEY unique_brewery_code (brewery_id, code)
    );

    -- User Beer Preferences table (Phase 1)
    CREATE TABLE IF NOT EXISTS user_beer_preferences (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        favorite_styles JSON NULL,
        favorite_breweries JSON NULL,
        preferred_abv_min DECIMAL(4,2) NULL,
        preferred_abv_max DECIMAL(4,2) NULL,
        preferred_ibu_min INT NULL,
        preferred_ibu_max INT NULL,
        avoid_styles JSON NULL,
        dietary_restrictions JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_preferences (user_id)
    );

    -- User Follows table (Phase 1 - Social Features Foundation)
    CREATE TABLE IF NOT EXISTS user_follows (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        follower_id VARCHAR(36) NOT NULL,
        following_id VARCHAR(36) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_follow (follower_id, following_id)
    );

    -- User Activities table (Phase 1 - Activity Feed Foundation)
    CREATE TABLE IF NOT EXISTS user_activities (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        activity_type ENUM('profile_update', 'brewery_follow', 'brewery_like', 'joined', 'beer_rating', 'beer_review', 'beer_checkin', 'user_follow') NOT NULL,
        target_type ENUM('user', 'brewery', 'beer') NULL,
        target_id VARCHAR(36) NULL,
        metadata JSON NULL,
        is_public BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Beer Check-ins table (Phase 3)
    CREATE TABLE IF NOT EXISTS beer_checkins (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        beer_id VARCHAR(36) NOT NULL,
        brewery_id VARCHAR(36) NOT NULL,
        rating_id VARCHAR(36) NULL,

        -- Check-in location
        checkin_location VARCHAR(255) NULL,
        checkin_latitude DECIMAL(10, 8) NULL,
        checkin_longitude DECIMAL(11, 8) NULL,

        -- Check-in details
        serving_style ENUM('draft', 'bottle', 'can', 'growler', 'other') NULL,
        checkin_comment TEXT NULL,
        photos JSON NULL,

        -- Social features
        is_public BOOLEAN DEFAULT TRUE,
        like_count INT DEFAULT 0,
        comment_count INT DEFAULT 0,

        -- Metadata
        device_info VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE SET NULL
    );

    -- Activity Likes table (Phase 3 - Social Interactions)
    CREATE TABLE IF NOT EXISTS activity_likes (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        activity_id VARCHAR(36) NULL,
        checkin_id VARCHAR(36) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (activity_id) REFERENCES user_activities(id) ON DELETE CASCADE,
        FOREIGN KEY (checkin_id) REFERENCES beer_checkins(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_activity_like (user_id, activity_id),
        UNIQUE KEY unique_user_checkin_like (user_id, checkin_id),
        CHECK ((activity_id IS NOT NULL AND checkin_id IS NULL) OR (activity_id IS NULL AND checkin_id IS NOT NULL))
    );

    -- Activity Comments table (Phase 3 - Social Interactions)
    CREATE TABLE IF NOT EXISTS activity_comments (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        activity_id VARCHAR(36) NULL,
        checkin_id VARCHAR(36) NULL,
        comment_text TEXT NOT NULL,
        is_public BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (activity_id) REFERENCES user_activities(id) ON DELETE CASCADE,
        FOREIGN KEY (checkin_id) REFERENCES beer_checkins(id) ON DELETE CASCADE,
        CHECK ((activity_id IS NOT NULL AND checkin_id IS NULL) OR (activity_id IS NULL AND checkin_id IS NOT NULL))
    );

    -- Badges table (Phase 4 - Gamification)
    CREATE TABLE IF NOT EXISTS badges (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT NOT NULL,
        category ENUM('explorer', 'connoisseur', 'social', 'seasonal', 'location', 'special') NOT NULL,
        icon VARCHAR(255) NULL,
        color VARCHAR(7) DEFAULT '#007bff',

        -- Badge earning criteria
        criteria_type ENUM('checkin_count', 'beer_count', 'brewery_count', 'style_count', 'rating_count', 'follower_count', 'review_count', 'special') NOT NULL,
        criteria_value INT NULL,
        criteria_metadata JSON NULL,

        -- Badge properties
        is_active BOOLEAN DEFAULT TRUE,
        is_hidden BOOLEAN DEFAULT FALSE,
        rarity ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') DEFAULT 'common',
        points_value INT DEFAULT 10,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- User Badges table (Phase 4 - User Badge Tracking)
    CREATE TABLE IF NOT EXISTS user_badges (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        badge_id VARCHAR(36) NOT NULL,
        earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        progress_data JSON NULL,
        is_featured BOOLEAN DEFAULT FALSE,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (badge_id) REFERENCES badges(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_badge (user_id, badge_id)
    );

    -- User Statistics table (Phase 4 - Detailed User Stats)
    CREATE TABLE IF NOT EXISTS user_statistics (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,

        -- Check-in statistics
        total_checkins INT DEFAULT 0,
        unique_beers_tried INT DEFAULT 0,
        unique_breweries_visited INT DEFAULT 0,
        unique_styles_tried INT DEFAULT 0,

        -- Rating statistics
        total_ratings INT DEFAULT 0,
        average_rating_given DECIMAL(3,2) DEFAULT 0.00,
        highest_rating_given DECIMAL(2,1) DEFAULT 0.0,
        lowest_rating_given DECIMAL(2,1) DEFAULT 5.0,

        -- Social statistics
        total_followers INT DEFAULT 0,
        total_following INT DEFAULT 0,
        total_likes_received INT DEFAULT 0,
        total_likes_given INT DEFAULT 0,

        -- Achievement statistics
        total_badges_earned INT DEFAULT 0,
        total_points_earned INT DEFAULT 0,
        current_streak_days INT DEFAULT 0,
        longest_streak_days INT DEFAULT 0,

        -- Activity statistics
        most_active_day_of_week TINYINT NULL,
        most_active_hour_of_day TINYINT NULL,
        favorite_beer_style_id VARCHAR(36) NULL,

        -- Dates
        first_checkin_date DATE NULL,
        last_checkin_date DATE NULL,
        last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (favorite_beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL,
        UNIQUE KEY unique_user_stats (user_id)
    );

    -- User Lists table (Phase 6 - Lists & Collections)
    CREATE TABLE IF NOT EXISTS user_lists (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        is_public BOOLEAN DEFAULT FALSE,
        is_default BOOLEAN DEFAULT FALSE,
        list_type ENUM('custom', 'want_to_try', 'favorites', 'tried', 'wishlist') DEFAULT 'custom',
        beer_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- User List Items table (Phase 6 - List Items)
    CREATE TABLE IF NOT EXISTS user_list_items (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        list_id VARCHAR(36) NOT NULL,
        beer_id VARCHAR(36) NOT NULL,
        notes TEXT NULL,
        priority INT DEFAULT 0,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (list_id) REFERENCES user_lists(id) ON DELETE CASCADE,
        FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
        UNIQUE KEY unique_list_beer (list_id, beer_id)
    );

    -- Photos table (Phase 6.3 - Photo & Media Features)
    CREATE TABLE IF NOT EXISTS photos (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        filename VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        thumbnail_path VARCHAR(500) NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        width INT NULL,
        height INT NULL,

        -- Photo categorization
        type ENUM('checkin', 'beer', 'brewery', 'user', 'review', 'general') NOT NULL,
        target_id VARCHAR(36) NULL,
        user_id VARCHAR(36) NOT NULL,

        -- Photo metadata
        title VARCHAR(255) NULL,
        description TEXT NULL,
        alt_text VARCHAR(255) NULL,

        -- Photo properties
        is_featured BOOLEAN DEFAULT FALSE,
        is_public BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,

        -- Engagement
        like_count INT DEFAULT 0,
        view_count INT DEFAULT 0,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Photo Tags table (Phase 6.3 - Photo Tagging)
    CREATE TABLE IF NOT EXISTS photo_tags (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        photo_id VARCHAR(36) NOT NULL,
        tag_type ENUM('user', 'beer', 'brewery', 'location', 'custom') NOT NULL,
        tag_value VARCHAR(255) NOT NULL,
        tag_id VARCHAR(36) NULL,
        x_position DECIMAL(5,2) NULL,
        y_position DECIMAL(5,2) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE
    );

    -- Beer Ratings table (Phase 2)
    CREATE TABLE IF NOT EXISTS beer_ratings (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        beer_id VARCHAR(36) NOT NULL,
        brewery_id VARCHAR(36) NOT NULL,

        -- Overall rating and detailed ratings
        overall_rating DECIMAL(2,1) NOT NULL CHECK (overall_rating >= 0.5 AND overall_rating <= 5.0),
        taste_rating DECIMAL(2,1) NULL CHECK (taste_rating >= 0.5 AND taste_rating <= 5.0),
        aroma_rating DECIMAL(2,1) NULL CHECK (aroma_rating >= 0.5 AND aroma_rating <= 5.0),
        appearance_rating DECIMAL(2,1) NULL CHECK (appearance_rating >= 0.5 AND appearance_rating <= 5.0),
        mouthfeel_rating DECIMAL(2,1) NULL CHECK (mouthfeel_rating >= 0.5 AND mouthfeel_rating <= 5.0),

        -- Review content
        review_text TEXT NULL,
        review_title VARCHAR(255) NULL,

        -- Check-in information
        checkin_location VARCHAR(255) NULL,
        checkin_latitude DECIMAL(10, 8) NULL,
        checkin_longitude DECIMAL(11, 8) NULL,
        serving_style ENUM('draft', 'bottle', 'can', 'growler', 'other') NULL,

        -- Media
        photos JSON NULL,

        -- Metadata
        device_info VARCHAR(255) NULL,
        is_public BOOLEAN DEFAULT TRUE,
        is_verified BOOLEAN DEFAULT FALSE,
        helpful_count INT DEFAULT 0,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
        FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_beer_rating (user_id, beer_id)
    );

    -- Beer Rating Helpfulness table (Phase 2)
    CREATE TABLE IF NOT EXISTS beer_rating_helpfulness (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        rating_id VARCHAR(36) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        is_helpful BOOLEAN NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_rating_helpfulness (rating_id, user_id)
    );

    -- Beer Rating Reports table (Phase 2 - Moderation)
    CREATE TABLE IF NOT EXISTS beer_rating_reports (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        rating_id VARCHAR(36) NOT NULL,
        reporter_user_id VARCHAR(36) NOT NULL,
        reason ENUM('spam', 'inappropriate', 'fake', 'offensive', 'other') NOT NULL,
        description TEXT NULL,
        status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
        admin_notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE CASCADE,
        FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE
    );
    ";

    // Execute table creation
    $pdo->exec($sql);
    echo "<p style='color: green;'>✓ Database tables created successfully</p>";

    // Create indexes for better performance
    $indexSql = "
    CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
    CREATE INDEX IF NOT EXISTS idx_profiles_brewery_id ON profiles(brewery_id);
    CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
    CREATE INDEX IF NOT EXISTS idx_profiles_visibility ON profiles(profile_visibility);
    CREATE INDEX IF NOT EXISTS idx_user_follows_follower ON user_follows(follower_id);
    CREATE INDEX IF NOT EXISTS idx_user_follows_following ON user_follows(following_id);
    CREATE INDEX IF NOT EXISTS idx_user_activities_user ON user_activities(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
    CREATE INDEX IF NOT EXISTS idx_user_activities_created ON user_activities(created_at DESC);

    -- Check-in system indexes (Phase 3)
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_user ON beer_checkins(user_id);
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_beer ON beer_checkins(beer_id);
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_brewery ON beer_checkins(brewery_id);
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_created ON beer_checkins(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_public ON beer_checkins(is_public);
    CREATE INDEX IF NOT EXISTS idx_beer_checkins_location ON beer_checkins(checkin_latitude, checkin_longitude);

    -- Social interaction indexes (Phase 3)
    CREATE INDEX IF NOT EXISTS idx_activity_likes_user ON activity_likes(user_id);
    CREATE INDEX IF NOT EXISTS idx_activity_likes_activity ON activity_likes(activity_id);
    CREATE INDEX IF NOT EXISTS idx_activity_likes_checkin ON activity_likes(checkin_id);
    CREATE INDEX IF NOT EXISTS idx_activity_comments_user ON activity_comments(user_id);
    CREATE INDEX IF NOT EXISTS idx_activity_comments_activity ON activity_comments(activity_id);
    CREATE INDEX IF NOT EXISTS idx_activity_comments_checkin ON activity_comments(checkin_id);
    CREATE INDEX IF NOT EXISTS idx_activity_comments_created ON activity_comments(created_at DESC);

    -- Badge system indexes (Phase 4)
    CREATE INDEX IF NOT EXISTS idx_badges_category ON badges(category);
    CREATE INDEX IF NOT EXISTS idx_badges_criteria_type ON badges(criteria_type);
    CREATE INDEX IF NOT EXISTS idx_badges_active ON badges(is_active);
    CREATE INDEX IF NOT EXISTS idx_badges_rarity ON badges(rarity);
    CREATE INDEX IF NOT EXISTS idx_user_badges_user ON user_badges(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_badges_badge ON user_badges(badge_id);
    CREATE INDEX IF NOT EXISTS idx_user_badges_earned ON user_badges(earned_at DESC);
    CREATE INDEX IF NOT EXISTS idx_user_badges_featured ON user_badges(is_featured);
    CREATE INDEX IF NOT EXISTS idx_user_statistics_user ON user_statistics(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_statistics_calculated ON user_statistics(last_calculated_at);

    -- Location-based indexes (Phase 5)
    CREATE INDEX IF NOT EXISTS idx_breweries_location ON breweries(latitude, longitude);

    -- User lists indexes (Phase 6)
    CREATE INDEX IF NOT EXISTS idx_user_lists_user ON user_lists(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_lists_public ON user_lists(is_public);
    CREATE INDEX IF NOT EXISTS idx_user_lists_type ON user_lists(list_type);
    CREATE INDEX IF NOT EXISTS idx_list_items_list ON user_list_items(list_id);
    CREATE INDEX IF NOT EXISTS idx_list_items_beer ON user_list_items(beer_id);
    CREATE INDEX IF NOT EXISTS idx_list_items_priority ON user_list_items(priority);

    -- Photo system indexes (Phase 6.3)
    CREATE INDEX IF NOT EXISTS idx_photos_type_target ON photos(type, target_id);
    CREATE INDEX IF NOT EXISTS idx_photos_user ON photos(user_id);
    CREATE INDEX IF NOT EXISTS idx_photos_created ON photos(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_photos_featured ON photos(is_featured);
    CREATE INDEX IF NOT EXISTS idx_photos_public ON photos(is_public);
    CREATE INDEX IF NOT EXISTS idx_photo_tags_photo ON photo_tags(photo_id);
    CREATE INDEX IF NOT EXISTS idx_photo_tags_type ON photo_tags(tag_type);
    CREATE INDEX IF NOT EXISTS idx_photo_tags_value ON photo_tags(tag_value);
    ";

    $pdo->exec($indexSql);
    echo "<p style='color: green;'>✓ Database indexes created successfully</p>";

    // Insert beer styles data (Phase 2)
    $stylesExists = $pdo->query("SELECT COUNT(*) FROM beer_styles")->fetchColumn();

    if ($stylesExists == 0) {
        // Read and execute beer styles data
        $stylesData = file_get_contents('database/beer_styles_data.sql');
        if ($stylesData) {
            $pdo->exec($stylesData);
            echo "<p style='color: green;'>✓ Beer styles data populated</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Beer styles data file not found, skipping...</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ Beer styles already exist</p>";
    }

    // Insert badge data (Phase 4)
    $badgesExists = $pdo->query("SELECT COUNT(*) FROM badges")->fetchColumn();

    if ($badgesExists == 0) {
        // Read and execute badge data
        $badgeData = file_get_contents('database/badges_data.sql');
        if ($badgeData) {
            $pdo->exec($badgeData);
            echo "<p style='color: green;'>✓ Badge system initialized with achievement badges</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Badge data file not found, skipping...</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ Badges already exist</p>";
    }

    // Insert default admin user (password: admin123)
    $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")->fetchColumn();
    
    if ($adminExists == 0) {
        $adminId = 'admin-user-id';
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $pdo->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)")
            ->execute([$adminId, '<EMAIL>', $passwordHash]);
            
        $pdo->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)")
            ->execute([$adminId, '<EMAIL>', 'admin']);
            
        echo "<p style='color: green;'>✓ Default admin user created</p>";
    } else {
        echo "<p style='color: blue;'>ℹ Admin user already exists</p>";
    }
    
    // Insert sample brewery
    $breweryExists = $pdo->query("SELECT COUNT(*) FROM breweries WHERE name = 'Demo Craft Brewery'")->fetchColumn();
    
    if ($breweryExists == 0) {
        $pdo->prepare("
            INSERT INTO breweries (id, name, description, city, state, brewery_type, claimed, verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ")->execute([
            'sample-brewery-1', 
            'Demo Craft Brewery', 
            'A sample brewery for testing the system', 
            'Demo City', 
            'Demo State', 
            'micro', 
            1, 
            1
        ]);
        echo "<p style='color: green;'>✓ Sample brewery created</p>";
    } else {
        echo "<p style='color: blue;'>ℹ Sample brewery already exists</p>";
    }
    
    echo "<hr>";
    echo "<h2 style='color: green;'>Database Setup Complete!</h2>";
    echo "<p><strong>Default Admin Login:</strong></p>";
    echo "<ul>";
    echo "<li>Email: <code><EMAIL></code></li>";
    echo "<li>Password: <code>admin123</code></li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li><a href='index.php'>Go to Beersty Homepage</a></li>";
    echo "<li><a href='auth/login.php'>Login as Admin</a></li>";
    echo "<li><a href='admin/dashboard.php'>Access Admin Dashboard</a></li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    echo "<p>Make sure MySQL is running in XAMPP Control Panel</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
