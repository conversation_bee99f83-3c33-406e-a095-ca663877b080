<?php
// Simple debug script to identify the issue

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Beersty Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Version</h2>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";

// Check if we can access the current directory
echo "<h2>File System Check</h2>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

// Check if config files exist
echo "<h2>Config Files Check</h2>";
$configFiles = ['config/config.php', 'config/database.php', 'includes/header.php'];
foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file missing</p>";
    }
}

// Test basic PHP functionality
echo "<h2>PHP Functionality Test</h2>";
try {
    // Test session
    if (session_start()) {
        echo "<p style='color: green;'>✓ Session support working</p>";
    } else {
        echo "<p style='color: red;'>✗ Session support failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Session error: " . $e->getMessage() . "</p>";
}

// Test database extensions
echo "<h2>Database Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'mysqli'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext extension loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext extension missing</p>";
    }
}

// Try to include config file and see what happens
echo "<h2>Config File Test</h2>";
try {
    if (file_exists('config/config.php')) {
        echo "<p>Attempting to include config/config.php...</p>";
        include_once 'config/config.php';
        echo "<p style='color: green;'>✓ Config file included successfully</p>";
        echo "<p>APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</p>";
    } else {
        echo "<p style='color: red;'>✗ config/config.php not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Config file error: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>✗ Config file fatal error: " . $e->getMessage() . "</p>";
}

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    if (file_exists('config/database.php')) {
        include_once 'config/database.php';
        $db = new Database();
        if ($db->testConnection()) {
            echo "<p style='color: green;'>✓ Database connection successful</p>";
        } else {
            echo "<p style='color: red;'>✗ Database connection failed</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Database config file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>✗ Database fatal error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Try Main Application</a> | <a href='phpinfo.php'>PHP Info</a></p>";
?>
