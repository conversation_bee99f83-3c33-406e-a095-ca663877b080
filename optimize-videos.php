<?php
/**
 * Video Optimization Utility
 * Beersty Platform - Optimize hero banner videos for web performance
 * 
 * This script provides information about video optimization
 * and can be extended to actually compress videos if FFmpeg is available
 */

echo "=== Beersty Video Optimization Utility ===\n\n";

$videoDir = __DIR__ . '/newvideos/';
$optimizedDir = __DIR__ . '/assets/videos/optimized/';

// Create optimized directory if it doesn't exist
if (!is_dir($optimizedDir)) {
    mkdir($optimizedDir, 0755, true);
    echo "Created optimized videos directory: $optimizedDir\n";
}

// Get video files
$videos = glob($videoDir . '*.mp4');

if (empty($videos)) {
    echo "No MP4 videos found in $videoDir\n";
    exit(1);
}

echo "Found " . count($videos) . " video files:\n\n";

// Analyze current videos
foreach ($videos as $video) {
    $filename = basename($video);
    $filesize = filesize($video);
    $filesizeMB = round($filesize / (1024 * 1024), 2);
    
    echo "📹 $filename\n";
    echo "   Size: {$filesizeMB} MB\n";
    
    // Check if video is one of our hero videos
    $heroVideos = ['beersty_beachside.mp4', 'beersty_tvs.mp4', 'beersty_pub.mp4', 'beersty_couple.mp4'];
    if (in_array($filename, $heroVideos)) {
        echo "   Status: ✅ Hero banner video\n";
        
        // Provide optimization recommendations
        if ($filesizeMB > 5) {
            echo "   Recommendation: Consider compression (>5MB)\n";
        } elseif ($filesizeMB > 3) {
            echo "   Recommendation: Good size for web (3-5MB)\n";
        } else {
            echo "   Recommendation: ✅ Optimal size for web (<3MB)\n";
        }
    } else {
        echo "   Status: Not used in hero banner\n";
    }
    echo "\n";
}

// Check if FFmpeg is available (for future optimization)
$ffmpegAvailable = false;
$ffmpegPath = null;

// Common FFmpeg locations
$possiblePaths = [
    'ffmpeg',
    '/usr/bin/ffmpeg',
    '/usr/local/bin/ffmpeg',
    'C:\\ffmpeg\\bin\\ffmpeg.exe',
    'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe'
];

foreach ($possiblePaths as $path) {
    $output = [];
    $returnCode = 0;
    exec("\"$path\" -version 2>&1", $output, $returnCode);
    
    if ($returnCode === 0 && strpos(implode(' ', $output), 'ffmpeg version') !== false) {
        $ffmpegAvailable = true;
        $ffmpegPath = $path;
        break;
    }
}

echo "=== Optimization Tools ===\n";
if ($ffmpegAvailable) {
    echo "✅ FFmpeg found at: $ffmpegPath\n";
    echo "   Video compression is available\n\n";
    
    // Provide FFmpeg commands for manual optimization
    echo "=== Manual Optimization Commands ===\n";
    echo "To compress videos manually, use these FFmpeg commands:\n\n";
    
    foreach ($heroVideos as $heroVideo) {
        $inputPath = $videoDir . $heroVideo;
        $outputPath = $optimizedDir . 'optimized_' . $heroVideo;
        
        if (file_exists($inputPath)) {
            echo "# Optimize $heroVideo\n";
            echo "\"$ffmpegPath\" -i \"$inputPath\" -c:v libx264 -crf 28 -preset medium -c:a aac -b:a 128k -movflags +faststart \"$outputPath\"\n\n";
        }
    }
    
} else {
    echo "❌ FFmpeg not found\n";
    echo "   Install FFmpeg for video compression capabilities\n";
    echo "   Download from: https://ffmpeg.org/download.html\n\n";
}

echo "=== Web Optimization Tips ===\n";
echo "1. Keep hero videos under 5MB each for good performance\n";
echo "2. Use H.264 codec with web-optimized settings\n";
echo "3. Add 'faststart' flag for progressive loading\n";
echo "4. Consider creating mobile-optimized versions\n";
echo "5. Use proper video attributes (autoplay, muted, loop, playsinline)\n\n";

echo "=== Current Implementation Status ===\n";
echo "✅ Video hero banner implemented with 4 videos\n";
echo "✅ Auto-rotation every 8 seconds\n";
echo "✅ User navigation with dots\n";
echo "✅ Responsive design for mobile\n";
echo "✅ Performance optimizations (preload, intersection observer)\n";
echo "✅ Accessibility features (ARIA labels, focus states)\n";
echo "✅ Brewery-themed color scheme\n\n";

// Check if videos are accessible via web
echo "=== Web Accessibility Check ===\n";
$baseUrl = 'http://localhost:8000';
foreach ($heroVideos as $heroVideo) {
    $videoUrl = $baseUrl . '/newvideos/' . $heroVideo;
    echo "🔗 $videoUrl\n";
}
echo "\nMake sure these URLs are accessible when the server is running.\n\n";

echo "=== Next Steps ===\n";
echo "1. Test the video hero banner at: http://localhost:8000\n";
echo "2. Check performance on different devices\n";
echo "3. Monitor loading times and user experience\n";
echo "4. Consider creating WebM versions for better compression\n";
echo "5. Add video preloading for smoother transitions\n\n";

echo "Video optimization analysis complete! 🎬\n";
?>
