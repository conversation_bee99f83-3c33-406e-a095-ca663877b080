<?php
echo "=== Creating Simple Admin Profile ===" . PHP_EOL;

try {
    $pdo = new PDO('mysql:host=localhost;dbname=beersty_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get admin user ID
    $stmt = $pdo->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "❌ Admin user not found!" . PHP_EOL;
        exit;
    }
    
    $userId = $user['id'];
    echo "Found admin user ID: $userId" . PHP_EOL;
    
    // Check if profile already exists
    $stmt = $pdo->prepare('SELECT id FROM profiles WHERE id = ?');
    $stmt->execute([$userId]);
    $existingProfile = $stmt->fetch();
    
    if ($existingProfile) {
        echo "✅ Profile already exists" . PHP_EOL;
    } else {
        // Create simple admin profile with only required fields
        $stmt = $pdo->prepare('INSERT INTO profiles (id, email, role, created_at) VALUES (?, ?, ?, NOW())');
        $result = $stmt->execute([$userId, '<EMAIL>', 'admin']);
        
        if ($result) {
            echo "✅ Admin profile created successfully!" . PHP_EOL;
        } else {
            echo "❌ Failed to create profile" . PHP_EOL;
        }
    }
    
    // Verify the profile
    echo "\n=== Verification ===" . PHP_EOL;
    $stmt = $pdo->prepare('SELECT u.email, p.role FROM users u JOIN profiles p ON u.id = p.id WHERE u.email = ?');
    $stmt->execute(['<EMAIL>']);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "✅ Profile verified!" . PHP_EOL;
        echo "Email: " . $result['email'] . PHP_EOL;
        echo "Role: " . $result['role'] . PHP_EOL;
        echo "\nYou can now login with:" . PHP_EOL;
        echo "Email: <EMAIL>" . PHP_EOL;
        echo "Password: admin123" . PHP_EOL;
    } else {
        echo "❌ Profile verification failed" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
