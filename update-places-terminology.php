<?php
// Update Database to Use "Places" Terminology
require_once 'config/config.php';

echo "=== Updating Database to Use 'Places' Terminology ===" . PHP_EOL;
echo "Supporting: Breweries, Restaurants, Pubs, Party Stores, Production Facilities" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Check current table structure
    echo "\nChecking current table structure..." . PHP_EOL;
    
    // Check if we need to rename columns to use place_id instead of brewery_id
    $tables_to_update = [
        'brewery_beers' => 'place_beers',
        'brewery_food' => 'place_food'
    ];
    
    foreach ($tables_to_update as $old_table => $new_table) {
        // Check if old table exists
        $stmt = $pdo->query("SHOW TABLES LIKE '$old_table'");
        $table_exists = $stmt->fetch();
        
        if ($table_exists) {
            echo "Found table: $old_table" . PHP_EOL;
            
            // Check if new table already exists
            $stmt = $pdo->query("SHOW TABLES LIKE '$new_table'");
            $new_table_exists = $stmt->fetch();
            
            if (!$new_table_exists) {
                echo "Renaming $old_table to $new_table..." . PHP_EOL;
                $pdo->exec("RENAME TABLE $old_table TO $new_table");
                echo "✓ Renamed $old_table to $new_table" . PHP_EOL;
            } else {
                echo "✓ $new_table already exists" . PHP_EOL;
            }
            
            // Update column names from brewery_id to place_id
            echo "Checking column names in $new_table..." . PHP_EOL;
            $stmt = $pdo->query("DESCRIBE $new_table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $has_brewery_id = false;
            $has_place_id = false;
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'brewery_id') {
                    $has_brewery_id = true;
                }
                if ($column['Field'] === 'place_id') {
                    $has_place_id = true;
                }
            }
            
            if ($has_brewery_id && !$has_place_id) {
                echo "Renaming brewery_id to place_id in $new_table..." . PHP_EOL;
                $pdo->exec("ALTER TABLE $new_table CHANGE brewery_id place_id VARCHAR(36) NOT NULL");
                echo "✓ Renamed brewery_id to place_id in $new_table" . PHP_EOL;
            } elseif ($has_place_id) {
                echo "✓ $new_table already uses place_id" . PHP_EOL;
            }
        } else {
            echo "Table $old_table not found, checking for $new_table..." . PHP_EOL;
            
            // Check if new table exists
            $stmt = $pdo->query("SHOW TABLES LIKE '$new_table'");
            $new_table_exists = $stmt->fetch();
            
            if ($new_table_exists) {
                echo "✓ $new_table already exists" . PHP_EOL;
            } else {
                echo "Creating $new_table..." . PHP_EOL;
                
                if ($new_table === 'place_beers') {
                    $pdo->exec("
                        CREATE TABLE place_beers (
                            id VARCHAR(36) PRIMARY KEY,
                            place_id VARCHAR(36) NOT NULL,
                            beer_style_id VARCHAR(36),
                            name VARCHAR(100) NOT NULL,
                            description TEXT,
                            abv DECIMAL(3,1),
                            ibu INT,
                            srm INT,
                            price DECIMAL(6,2),
                            availability ENUM('year_round', 'seasonal', 'limited', 'one_off') DEFAULT 'year_round',
                            is_active BOOLEAN DEFAULT 1,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (place_id) REFERENCES breweries(id) ON DELETE CASCADE,
                            FOREIGN KEY (beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
                        )
                    ");
                    echo "✓ Created place_beers table" . PHP_EOL;
                } elseif ($new_table === 'place_food') {
                    $pdo->exec("
                        CREATE TABLE place_food (
                            id VARCHAR(36) PRIMARY KEY,
                            place_id VARCHAR(36) NOT NULL,
                            food_category_id VARCHAR(36),
                            name VARCHAR(100) NOT NULL,
                            description TEXT,
                            price DECIMAL(6,2),
                            ingredients TEXT,
                            allergens TEXT,
                            is_vegetarian BOOLEAN DEFAULT 0,
                            is_vegan BOOLEAN DEFAULT 0,
                            is_gluten_free BOOLEAN DEFAULT 0,
                            is_active BOOLEAN DEFAULT 1,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (place_id) REFERENCES breweries(id) ON DELETE CASCADE,
                            FOREIGN KEY (food_category_id) REFERENCES food_categories(id) ON DELETE SET NULL
                        )
                    ");
                    echo "✓ Created place_food table" . PHP_EOL;
                }
            }
        }
    }
    
    // Update place types in breweries table to be more descriptive
    echo "\nUpdating place types..." . PHP_EOL;
    
    $place_type_mapping = [
        'micro' => 'brewery',
        'nano' => 'brewery', 
        'regional' => 'brewery',
        'brewpub' => 'brewpub',
        'large' => 'brewery',
        'planning' => 'brewery',
        'bar' => 'pub',
        'contract' => 'production_facility',
        'proprietor' => 'brewery',
        'closed' => 'brewery'
    ];
    
    foreach ($place_type_mapping as $old_type => $new_type) {
        $stmt = $pdo->prepare("UPDATE breweries SET brewery_type = ? WHERE brewery_type = ?");
        $stmt->execute([$new_type, $old_type]);
        $updated = $stmt->rowCount();
        if ($updated > 0) {
            echo "✓ Updated $updated places from '$old_type' to '$new_type'" . PHP_EOL;
        }
    }
    
    // Show current place types
    echo "\nCurrent place types in database:" . PHP_EOL;
    $stmt = $pdo->query("SELECT brewery_type as place_type, COUNT(*) as count FROM breweries GROUP BY brewery_type ORDER BY count DESC");
    $place_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($place_types as $type) {
        echo "  • " . ucfirst(str_replace('_', ' ', $type['place_type'])) . ": " . $type['count'] . " places" . PHP_EOL;
    }
    
    // Test the updated structure
    echo "\nTesting updated structure..." . PHP_EOL;
    
    // Test place_beers table
    $stmt = $pdo->query("SELECT COUNT(*) FROM place_beers");
    $beer_count = $stmt->fetchColumn();
    echo "✓ place_beers table: $beer_count items" . PHP_EOL;
    
    // Test place_food table  
    $stmt = $pdo->query("SELECT COUNT(*) FROM place_food");
    $food_count = $stmt->fetchColumn();
    echo "✓ place_food table: $food_count items" . PHP_EOL;
    
    echo "\n=== Places Terminology Update Complete ===" . PHP_EOL;
    echo "✓ Tables renamed to use 'place' terminology" . PHP_EOL;
    echo "✓ Columns renamed from brewery_id to place_id" . PHP_EOL;
    echo "✓ Place types standardized" . PHP_EOL;
    echo "✓ Foreign key relationships maintained" . PHP_EOL;
    echo "\nSupported place types:" . PHP_EOL;
    echo "• Brewery (craft breweries, microbreweries, etc.)" . PHP_EOL;
    echo "• Brewpub (brewery with restaurant)" . PHP_EOL;
    echo "• Restaurant (food establishments)" . PHP_EOL;
    echo "• Pub (bars and pubs)" . PHP_EOL;
    echo "• Party Store (retail beer/wine shops)" . PHP_EOL;
    echo "• Production Facility (contract brewing, etc.)" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
