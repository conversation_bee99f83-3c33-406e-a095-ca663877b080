# Test Category Management System
Write-Host "=== Testing Category Management System ===" -ForegroundColor Green

# Check database status
Write-Host "Checking category database status..." -ForegroundColor Yellow
C:\xampp\php\php.exe -r "
require_once 'config/config.php';
try {
    \$db = new Database();
    \$pdo = \$db->getConnection();
    
    echo 'Category System Status:' . PHP_EOL;
    
    // Check beer styles
    \$stmt = \$pdo->query('SELECT COUNT(*) FROM beer_styles WHERE is_active = 1');
    \$beer_styles_count = \$stmt->fetchColumn();
    echo 'Active Beer Styles: ' . \$beer_styles_count . PHP_EOL;
    
    // Check food categories
    \$stmt = \$pdo->query('SELECT COUNT(*) FROM food_categories WHERE is_active = 1');
    \$food_categories_count = \$stmt->fetchColumn();
    echo 'Active Food Categories: ' . \$food_categories_count . PHP_EOL;
    
    // Show beer style categories
    echo PHP_EOL . 'Beer Style Categories:' . PHP_EOL;
    \$stmt = \$pdo->query('SELECT category, COUNT(*) as count FROM beer_styles WHERE is_active = 1 GROUP BY category ORDER BY count DESC');
    \$categories = \$stmt->fetchAll();
    foreach (\$categories as \$cat) {
        echo '  ' . \$cat['category'] . ': ' . \$cat['count'] . ' styles' . PHP_EOL;
    }
    
    // Show food categories
    echo PHP_EOL . 'Food Categories:' . PHP_EOL;
    \$stmt = \$pdo->query('SELECT name, sort_order FROM food_categories WHERE is_active = 1 ORDER BY sort_order, name');
    \$food_cats = \$stmt->fetchAll();
    foreach (\$food_cats as \$cat) {
        echo '  ' . \$cat['name'] . ' (Order: ' . \$cat['sort_order'] . ')' . PHP_EOL;
    }
    
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

Write-Host "`n=== Category Management Features ===" -ForegroundColor Cyan
Write-Host "✓ Beer Style Management with categories" -ForegroundColor Green
Write-Host "✓ Food Category Management with sort order" -ForegroundColor Green
Write-Host "✓ Search functionality for both beer and food" -ForegroundColor Green
Write-Host "✓ Filter by beer style and category" -ForegroundColor Green
Write-Host "✓ Filter by food category" -ForegroundColor Green
Write-Host "✓ Admin can add/edit/delete categories" -ForegroundColor Green

Write-Host "`n=== Menu Search & Filter Features ===" -ForegroundColor Cyan
Write-Host "• Beer search by name and description" -ForegroundColor White
Write-Host "• Beer filter by style (IPA, Stout, etc.)" -ForegroundColor White
Write-Host "• Beer filter by category (IPA, Stout, Lager, etc.)" -ForegroundColor White
Write-Host "• Food search by name and description" -ForegroundColor White
Write-Host "• Food filter by category (Appetizers, Entrees, etc.)" -ForegroundColor White
Write-Host "• Real-time filtering as you type" -ForegroundColor White

Write-Host "`n=== Business Customization ===" -ForegroundColor Cyan
Write-Host "• Admins can create custom beer styles" -ForegroundColor White
Write-Host "• Admins can create custom food categories" -ForegroundColor White
Write-Host "• Categories can be reordered with sort_order" -ForegroundColor White
Write-Host "• Different places can use different categories" -ForegroundColor White
Write-Host "• Flexible naming for different business types" -ForegroundColor White

Write-Host "`nAccess category management at:" -ForegroundColor Yellow
Write-Host "http://localhost:8000/admin/category-management.php" -ForegroundColor White

Write-Host "`nAccess menu management with search at:" -ForegroundColor Yellow
Write-Host "http://localhost:8000/admin/menu-management.php" -ForegroundColor White

Write-Host "`nTest the new features:" -ForegroundColor Yellow
Write-Host "1. Go to Category Management to add beer styles/food categories" -ForegroundColor White
Write-Host "2. Go to Menu Management and select a place" -ForegroundColor White
Write-Host "3. Use the search boxes to filter items" -ForegroundColor White
Write-Host "4. Use the dropdown filters to filter by category" -ForegroundColor White

Write-Host "`nCategory management system is ready!" -ForegroundColor Green
