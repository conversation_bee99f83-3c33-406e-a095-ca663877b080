# Beersty Database Setup Script
# PowerShell script to set up MySQL database for Beersty application

param(
    [string]$MySQLPath = "C:\xampp\mysql\bin\mysql.exe",
    [string]$Host = "localhost",
    [string]$Port = "3306",
    [string]$Username = "root",
    [string]$Password = "",
    [string]$DatabaseName = "beersty_db",
    [string]$XAMPPPath = "C:\xampp"
)

Write-Host "=== Beersty Database Setup ===" -ForegroundColor Green
Write-Host ""

# Check if XAMPP is installed
if (-not (Test-Path $XAMPPPath)) {
    Write-Host "XAMPP not found at: $XAMPPPath" -ForegroundColor Red
    Write-Host "Please install XAMPP or provide the correct path using -XAMPPPath parameter" -ForegroundColor Yellow
    Write-Host "Download XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    exit 1
}

# Check if MySQL is installed
if (-not (Test-Path $MySQLPath)) {
    Write-Host "MySQL not found at: $MySQLPath" -ForegroundColor Red
    Write-Host "Please make sure XAMPP is properly installed with MySQL" -ForegroundColor Yellow
    Write-Host "Expected MySQL path: $MySQLPath" -ForegroundColor Yellow
    exit 1
}

Write-Host "Found XAMPP at: $XAMPPPath" -ForegroundColor Green
Write-Host "Found MySQL at: $MySQLPath" -ForegroundColor Green

# Check if XAMPP services are running
Write-Host "Checking XAMPP services..." -ForegroundColor Yellow

$ApacheService = Get-Service -Name "Apache*" -ErrorAction SilentlyContinue | Where-Object { $_.Status -eq "Running" }
$MySQLService = Get-Service -Name "MySQL*" -ErrorAction SilentlyContinue | Where-Object { $_.Status -eq "Running" }

if (-not $MySQLService) {
    Write-Host "⚠ MySQL service is not running" -ForegroundColor Yellow
    Write-Host "Please start MySQL service in XAMPP Control Panel" -ForegroundColor Yellow

    $StartServices = Read-Host "Would you like to try starting MySQL service? (y/N)"
    if ($StartServices -eq "y" -or $StartServices -eq "Y") {
        try {
            Start-Service -Name "MySQL" -ErrorAction Stop
            Write-Host "✓ MySQL service started" -ForegroundColor Green
        } catch {
            Write-Host "✗ Could not start MySQL service automatically" -ForegroundColor Red
            Write-Host "Please start it manually in XAMPP Control Panel" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "✓ MySQL service is running" -ForegroundColor Green
}

if (-not $ApacheService) {
    Write-Host "⚠ Apache service is not running" -ForegroundColor Yellow
    Write-Host "You'll need to start Apache in XAMPP Control Panel to access the web application" -ForegroundColor Yellow
} else {
    Write-Host "✓ Apache service is running" -ForegroundColor Green
}

# Prompt for password if not provided
if ([string]::IsNullOrEmpty($Password)) {
    $SecurePassword = Read-Host "Enter MySQL root password" -AsSecureString
    $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
}

# Test MySQL connection
Write-Host "Testing MySQL connection..." -ForegroundColor Yellow

$TestCommand = "SELECT 1;"
$TestArgs = @(
    "-h", $Host,
    "-P", $Port,
    "-u", $Username
)

if (-not [string]::IsNullOrEmpty($Password)) {
    $TestArgs += "-p$Password"
}

$TestArgs += "-e", $TestCommand

try {
    $TestResult = & $MySQLPath $TestArgs 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Connection failed: $TestResult"
    }
    Write-Host "✓ MySQL connection successful" -ForegroundColor Green
} catch {
    Write-Host "✗ MySQL connection failed: $_" -ForegroundColor Red
    exit 1
}

# Check if database exists
Write-Host "Checking if database '$DatabaseName' exists..." -ForegroundColor Yellow

$CheckDbCommand = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$DatabaseName';"
$CheckDbArgs = @(
    "-h", $Host,
    "-P", $Port,
    "-u", $Username
)

if (-not [string]::IsNullOrEmpty($Password)) {
    $CheckDbArgs += "-p$Password"
}

$CheckDbArgs += "-e", $CheckDbCommand

$DbExists = & $MySQLPath $CheckDbArgs 2>&1
if ($DbExists -match $DatabaseName) {
    Write-Host "Database '$DatabaseName' already exists." -ForegroundColor Yellow
    $Overwrite = Read-Host "Do you want to recreate it? This will delete all existing data! (y/N)"
    
    if ($Overwrite -eq "y" -or $Overwrite -eq "Y") {
        Write-Host "Dropping existing database..." -ForegroundColor Yellow
        $DropCommand = "DROP DATABASE IF EXISTS $DatabaseName;"
        $DropArgs = @(
            "-h", $Host,
            "-P", $Port,
            "-u", $Username
        )
        
        if (-not [string]::IsNullOrEmpty($Password)) {
            $DropArgs += "-p$Password"
        }
        
        $DropArgs += "-e", $DropCommand
        
        & $MySQLPath $DropArgs
        if ($LASTEXITCODE -ne 0) {
            Write-Host "✗ Failed to drop database" -ForegroundColor Red
            exit 1
        }
        Write-Host "✓ Database dropped" -ForegroundColor Green
    } else {
        Write-Host "Setup cancelled." -ForegroundColor Yellow
        exit 0
    }
}

# Create database and import schema
Write-Host "Creating database and importing schema..." -ForegroundColor Yellow

$SchemaFile = Join-Path $PSScriptRoot "database\schema.sql"
if (-not (Test-Path $SchemaFile)) {
    Write-Host "✗ Schema file not found: $SchemaFile" -ForegroundColor Red
    exit 1
}

$ImportArgs = @(
    "-h", $Host,
    "-P", $Port,
    "-u", $Username
)

if (-not [string]::IsNullOrEmpty($Password)) {
    $ImportArgs += "-p$Password"
}

try {
    Get-Content $SchemaFile | & $MySQLPath $ImportArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Schema import failed"
    }
    Write-Host "✓ Database schema imported successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to import schema: $_" -ForegroundColor Red
    exit 1
}

# Verify tables were created
Write-Host "Verifying database setup..." -ForegroundColor Yellow

$VerifyCommand = "USE $DatabaseName; SHOW TABLES;"
$VerifyArgs = @(
    "-h", $Host,
    "-P", $Port,
    "-u", $Username
)

if (-not [string]::IsNullOrEmpty($Password)) {
    $VerifyArgs += "-p$Password"
}

$VerifyArgs += "-e", $VerifyCommand

$Tables = & $MySQLPath $VerifyArgs 2>&1
$ExpectedTables = @(
    "users", "profiles", "breweries", "beer_menu", "food_menu", 
    "brewery_coupons", "digital_boards", "gallery_images", 
    "posts", "brewery_followers", "brewery_likes"
)

$CreatedTables = $Tables | Where-Object { $_ -match "^\w+$" }
$MissingTables = $ExpectedTables | Where-Object { $CreatedTables -notcontains $_ }

if ($MissingTables.Count -eq 0) {
    Write-Host "✓ All tables created successfully" -ForegroundColor Green
    Write-Host "Tables created: $($CreatedTables -join ', ')" -ForegroundColor Cyan
} else {
    Write-Host "✗ Some tables are missing: $($MissingTables -join ', ')" -ForegroundColor Red
    exit 1
}

# Create .env file
Write-Host "Creating .env configuration file..." -ForegroundColor Yellow

$EnvContent = @"
DB_HOST=$Host
DB_NAME=$DatabaseName
DB_USER=$Username
DB_PASSWORD=$Password

# Application Settings
APP_NAME="Beersty - Brewery Management System"
APP_URL=http://localhost
APP_ENV=development
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880

# Session Settings
SESSION_LIFETIME=7200
"@

$EnvFile = Join-Path $PSScriptRoot "config\.env"
$EnvContent | Out-File -FilePath $EnvFile -Encoding UTF8

Write-Host "✓ Configuration file created: $EnvFile" -ForegroundColor Green

# Create uploads directory
$UploadsDir = Join-Path $PSScriptRoot "uploads"
if (-not (Test-Path $UploadsDir)) {
    New-Item -ItemType Directory -Path $UploadsDir -Force | Out-Null
    Write-Host "✓ Uploads directory created: $UploadsDir" -ForegroundColor Green
}

# Set permissions for uploads directory (if on Windows with proper permissions)
try {
    $Acl = Get-Acl $UploadsDir
    $AccessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $Acl.SetAccessRule($AccessRule)
    Set-Acl -Path $UploadsDir -AclObject $Acl
    Write-Host "✓ Uploads directory permissions set" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not set uploads directory permissions: $_" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Database Details:" -ForegroundColor Cyan
Write-Host "  Host: $Host" -ForegroundColor White
Write-Host "  Port: $Port" -ForegroundColor White
Write-Host "  Database: $DatabaseName" -ForegroundColor White
Write-Host "  Username: $Username" -ForegroundColor White
Write-Host ""
Write-Host "Default Admin Account:" -ForegroundColor Cyan
Write-Host "  Email: <EMAIL>" -ForegroundColor White
Write-Host "  Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Open XAMPP Control Panel and start Apache (if not already running)" -ForegroundColor White
Write-Host "2. Copy this project to C:\xampp\htdocs\beersty\" -ForegroundColor White
Write-Host "3. Navigate to http://localhost/beersty/" -ForegroundColor White
Write-Host "4. Log in with the admin account" -ForegroundColor White
Write-Host "5. Start adding breweries and managing the system" -ForegroundColor White
Write-Host ""
Write-Host "XAMPP URLs:" -ForegroundColor Cyan
Write-Host "  Application: http://localhost/beersty/" -ForegroundColor White
Write-Host "  phpMyAdmin: http://localhost/phpmyadmin/" -ForegroundColor White
Write-Host "  XAMPP Dashboard: http://localhost/" -ForegroundColor White
Write-Host ""
Write-Host "Happy brewing! 🍺" -ForegroundColor Green
