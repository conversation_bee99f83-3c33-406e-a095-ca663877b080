# SIMPLE PHP AND <PERSON>I CHECK

Write-Host "PHP VERSION AND CONFIGURATION CHECK" -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor Yellow

# 1. Check command line PHP
Write-Host ""
Write-Host "1. COMMAND LINE PHP:" -ForegroundColor Cyan
try {
    $phpVersion = php -v 2>$null
    if ($phpVersion) {
        Write-Host "Found PHP in PATH:" -ForegroundColor Green
        $phpVersion[0] | Write-Host -ForegroundColor White
        
        $phpLocation = where.exe php 2>$null
        if ($phpLocation) {
            Write-Host "Location: $phpLocation" -ForegroundColor Gray
        }
    } else {
        Write-Host "No PHP in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "No PHP in PATH" -ForegroundColor Red
}

# 2. Check XAMPP PHP
Write-Host ""
Write-Host "2. XAMPP PHP:" -ForegroundColor Cyan

$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP")
$XamppPath = $null

foreach ($path in $XamppPaths) {
    if (Test-Path $path) {
        $XamppPath = $path
        break
    }
}

if ($XamppPath) {
    Write-Host "XAMPP found at: $XamppPath" -ForegroundColor Green
    
    $XamppPhp = Join-Path $XamppPath "php\php.exe"
    if (Test-Path $XamppPhp) {
        Write-Host "XAMPP PHP found: $XamppPhp" -ForegroundColor Green
        try {
            $xamppVersion = & $XamppPhp -v 2>$null
            if ($xamppVersion) {
                $xamppVersion[0] | Write-Host -ForegroundColor White
            }
        } catch {
            Write-Host "Could not get XAMPP PHP version" -ForegroundColor Red
        }
    } else {
        Write-Host "XAMPP PHP not found" -ForegroundColor Red
    }
} else {
    Write-Host "XAMPP not found" -ForegroundColor Red
}

# 3. Find php.ini files
Write-Host ""
Write-Host "3. PHP.INI FILES:" -ForegroundColor Cyan

$IniFiles = @()

# Check XAMPP php.ini
if ($XamppPath) {
    $XamppIni = Join-Path $XamppPath "php\php.ini"
    if (Test-Path $XamppIni) {
        $IniFiles += $XamppIni
        Write-Host "XAMPP php.ini: $XamppIni" -ForegroundColor Green
        
        # Check PDO status
        $content = Get-Content $XamppIni -ErrorAction SilentlyContinue
        if ($content) {
            $pdoEnabled = $content | Where-Object { $_ -match "^extension=pdo_mysql" }
            $pdoCommented = $content | Where-Object { $_ -match "^;\s*extension=pdo_mysql" }
            
            if ($pdoEnabled) {
                Write-Host "  PDO MySQL: ENABLED" -ForegroundColor Green
            } elseif ($pdoCommented) {
                Write-Host "  PDO MySQL: COMMENTED OUT" -ForegroundColor Red
            } else {
                Write-Host "  PDO MySQL: NOT FOUND" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "XAMPP php.ini not found at: $XamppIni" -ForegroundColor Red
    }
}

# Search for other php.ini files
Write-Host ""
Write-Host "Searching for other php.ini files..." -ForegroundColor Gray

$SearchPaths = @("C:\php", "C:\Program Files\PHP", "C:\Windows")
foreach ($searchPath in $SearchPaths) {
    if (Test-Path $searchPath) {
        $iniPath = Join-Path $searchPath "php.ini"
        if (Test-Path $iniPath) {
            $IniFiles += $iniPath
            Write-Host "Found: $iniPath" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "TOTAL PHP.INI FILES FOUND: $($IniFiles.Count)" -ForegroundColor Yellow

# 4. Create phpinfo test
Write-Host ""
Write-Host "4. CREATING PHPINFO TEST:" -ForegroundColor Cyan

$PhpInfoContent = '<?php
echo "<h1>PHP Configuration</h1>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Config File:</strong> " . php_ini_loaded_file() . "</p>";
echo "<h2>PDO Status:</h2>";
echo "<p>PDO: " . (extension_loaded("pdo") ? "LOADED" : "NOT LOADED") . "</p>";
echo "<p>PDO MySQL: " . (extension_loaded("pdo_mysql") ? "LOADED" : "NOT LOADED") . "</p>";
echo "<p>MySQLi: " . (extension_loaded("mysqli") ? "LOADED" : "NOT LOADED") . "</p>";
?>'

$PhpInfoFile = "phpinfo-test.php"
$PhpInfoContent | Set-Content $PhpInfoFile -Encoding UTF8
Write-Host "Created: $PhpInfoFile" -ForegroundColor Green

# 5. Summary
Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Red
Write-Host "========" -ForegroundColor Red

if ($IniFiles.Count -gt 1) {
    Write-Host ""
    Write-Host "MULTIPLE PHP.INI FILES FOUND!" -ForegroundColor Yellow
    Write-Host "This can cause conflicts." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "FOR WEB DEVELOPMENT:" -ForegroundColor Cyan
    Write-Host "- Focus on XAMPP's php.ini" -ForegroundColor White
    Write-Host "- Enable PDO in XAMPP's php.ini only" -ForegroundColor White
    Write-Host "- Test via web browser" -ForegroundColor White
} else {
    Write-Host "Single php.ini found - good!" -ForegroundColor Green
}

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Make sure Apache is running in XAMPP" -ForegroundColor White
Write-Host "2. Test: http://localhost/beersty-lovable/phpinfo-test.php" -ForegroundColor White
Write-Host "3. Check PDO status in browser" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to continue"
