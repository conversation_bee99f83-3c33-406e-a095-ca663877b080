# DIAGNOSE CONNECTION REFUSED ERROR
# This script will help us figure out why localhost is refusing connections

Write-Host "🔍 DIAGNOSING CONNECTION REFUSED ERROR" -ForegroundColor Red
Write-Host "=======================================" -ForegroundColor Red

# Step 1: Check if Apache process is actually running
Write-Host ""
Write-Host "STEP 1: Checking Apache processes..." -ForegroundColor Cyan

$ApacheProcesses = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($ApacheProcesses) {
    Write-Host "✅ Apache processes found:" -ForegroundColor Green
    $ApacheProcesses | Format-Table ProcessName, Id, CPU -AutoSize
} else {
    Write-Host "❌ NO Apache processes running!" -ForegroundColor Red
    Write-Host "This is why localhost is refusing connections." -ForegroundColor Yellow
}

# Step 2: Check what's listening on port 80
Write-Host ""
Write-Host "STEP 2: Checking what's listening on port 80..." -ForegroundColor Cyan

try {
    $Port80 = netstat -an | findstr ":80 "
    if ($Port80) {
        Write-Host "Port 80 listeners:" -ForegroundColor Yellow
        $Port80 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "❌ NOTHING is listening on port 80!" -ForegroundColor Red
        Write-Host "This confirms Apache is not running." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Could not check port 80" -ForegroundColor Red
}

# Step 3: Check port 8080 as well
Write-Host ""
Write-Host "STEP 3: Checking port 8080..." -ForegroundColor Cyan

try {
    $Port8080 = netstat -an | findstr ":8080 "
    if ($Port8080) {
        Write-Host "Port 8080 listeners:" -ForegroundColor Yellow
        $Port8080 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "Nothing listening on port 8080 either" -ForegroundColor Gray
    }
} catch {
    Write-Host "Could not check port 8080" -ForegroundColor Red
}

# Step 4: Find XAMPP and check status
Write-Host ""
Write-Host "STEP 4: Checking XAMPP status..." -ForegroundColor Cyan

$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if ($XamppPath) {
    Write-Host "✅ XAMPP found at: $XamppPath" -ForegroundColor Green
    
    # Check if XAMPP Control Panel is running
    $XamppControl = Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue
    if ($XamppControl) {
        Write-Host "✅ XAMPP Control Panel is running" -ForegroundColor Green
    } else {
        Write-Host "❌ XAMPP Control Panel is NOT running" -ForegroundColor Red
    }
    
    # Check Apache executable
    $ApacheExe = Join-Path $XamppPath "apache\bin\httpd.exe"
    if (Test-Path $ApacheExe) {
        Write-Host "✅ Apache executable found: $ApacheExe" -ForegroundColor Green
    } else {
        Write-Host "❌ Apache executable NOT found: $ApacheExe" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ XAMPP not found!" -ForegroundColor Red
}

# Step 5: Check for common blocking services
Write-Host ""
Write-Host "STEP 5: Checking for blocking services..." -ForegroundColor Cyan

# Check IIS
$IIS = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
if ($IIS -and $IIS.Status -eq "Running") {
    Write-Host "⚠️ IIS is running - this blocks Apache!" -ForegroundColor Yellow
} else {
    Write-Host "✅ IIS is not blocking" -ForegroundColor Green
}

# Check Windows Firewall
try {
    $FirewallStatus = Get-NetFirewallProfile | Select-Object Name, Enabled
    Write-Host "Windows Firewall status:" -ForegroundColor Yellow
    $FirewallStatus | ForEach-Object { 
        $status = if ($_.Enabled) { "ON" } else { "OFF" }
        Write-Host "  $($_.Name): $status" -ForegroundColor White
    }
} catch {
    Write-Host "Could not check firewall status" -ForegroundColor Gray
}

# Step 6: Manual fix instructions
Write-Host ""
Write-Host "STEP 6: MANUAL FIX REQUIRED" -ForegroundColor Red
Write-Host "============================" -ForegroundColor Red

if (-not $ApacheProcesses) {
    Write-Host ""
    Write-Host "🔧 APACHE IS NOT RUNNING - Here's how to fix it:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "METHOD 1: Start Apache manually" -ForegroundColor Cyan
    Write-Host "1. Open XAMPP Control Panel (if not open)" -ForegroundColor White
    Write-Host "2. Click START next to Apache" -ForegroundColor White
    Write-Host "3. If it shows an error, click LOGS to see what's wrong" -ForegroundColor White
    Write-Host ""
    
    Write-Host "METHOD 2: Try starting Apache directly" -ForegroundColor Cyan
    if ($XamppPath) {
        $ApacheStart = Join-Path $XamppPath "apache_start.bat"
        Write-Host "Run this file: $ApacheStart" -ForegroundColor White
    }
    Write-Host ""
    
    Write-Host "METHOD 3: Use port 8080 instead" -ForegroundColor Cyan
    Write-Host "Run: .\beersty-dev-start.ps1 -Port8080" -ForegroundColor White
    Write-Host ""
    
    Write-Host "METHOD 4: Run as Administrator" -ForegroundColor Cyan
    Write-Host "1. Close XAMPP Control Panel" -ForegroundColor White
    Write-Host "2. Right-click XAMPP Control Panel" -ForegroundColor White
    Write-Host "3. Select 'Run as Administrator'" -ForegroundColor White
    Write-Host "4. Try starting Apache again" -ForegroundColor White
}

# Step 7: Open XAMPP Control Panel
Write-Host ""
Write-Host "STEP 7: Opening XAMPP Control Panel..." -ForegroundColor Cyan

if ($XamppPath) {
    $XamppControlExe = Join-Path $XamppPath "xampp-control.exe"
    if (Test-Path $XamppControlExe) {
        Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
        Start-Process $XamppControlExe -WindowStyle Normal
        Write-Host "✅ XAMPP Control Panel started" -ForegroundColor Green
        Write-Host ""
        Write-Host "NOW: Click START next to Apache in the Control Panel!" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🔄 After starting Apache, run this to test:" -ForegroundColor Cyan
Write-Host ".\beersty-status.ps1" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to exit"
