/*!
              - C S S -
        VERSION 1.0.0 - DATE: 2023-01-06
	@author: <PERSON><PERSON><PERSON> ThemePunch OHG.
        - Slider Revolution 7.0 -

        GET LICENSE AT:
https://www.themepunch.com/links/slider_revolution_wordpress_regular_license

LICENSE:
Copyright (c) 2023, ThemePunch. All rights reserved.
This work is subject to the terms at https://www.themepunch.com/links/slider_revolution_wordpress_regular_license (Regular / Extended)
*/.rs-p-wp-fix{display:none!important;margin:0!important;height:0!important}body{--sr-7-css-loaded:1}.wp-block-themepunch-revslider{position:relative}.wp-block-themepunch-revslider.revslider{margin:0}canvas.sr7-pbar{z-index:500;position:absolute;pointer-events:none}sr7-module{display:block;position:relative;overflow-x:visible}sr7-adjuster{position:relative;display:block;width:100%}sr7-content{position:absolute;top:0;height:100%;contain:size layout style;z-index:1;overflow-x:visible}sr7-carousel{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden;z-index:1}sr7-slide{position:absolute;width:100%;height:100%;contain:strict;top:0;left:0;z-index:1;visibility:hidden;pointer-events:none}.sr7-overflow-force sr7-slide,sr7-carousel sr7-slide{contain:layout style}sr7-module-bg{top:0;z-index:0;position:absolute}sr7-3dwrap{pointer-events:none;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}sr7-overlay{display:block;width:100%;height:100%;top:0;left:0;position:absolute;z-index:3;background-repeat:repeat}sr7-btn,sr7-col,sr7-content .sr7-layer,sr7-grp,sr7-img,sr7-layer,sr7-linebrk,sr7-row,sr7-shp,sr7-svg,sr7-txt,sr7-zone{user-select:none;visibility:hidden;display:none;box-sizing:border-box;backface-visibility:hidden}.sr7-layer[data-subtype=slidebg],sr7bg{pointer-events:none}sr7-content .sr7-layer.sr7-withclip,sr7-content .sr7-layer.sr7-withclip .sr7-layer{backface-visibility:visible}sr7-grp.sr7-clear:after{content:"";clear:both;display:block;height:0}sr7-zone{pointer-events:none}sr7-row.sr7-layer.sr7-galign{box-sizing:border-box}.sr7-layer[data-type=svg]>svg,sr7-layer[data-type=svg]>svg{width:100%;height:100%}sr7-row-con{table-layout:fixed;font-size:0;display:block}sr7-module .sr7-layer,sr7-module sr7-layer{-webkit-font-smoothing:antialiased!important;-webkit-tap-highlight-color:transparent;-moz-osx-font-smoothing:grayscale;font-display:swap}sr7-slide.sr7-staticslide-low{z-index:0!important}sr7-slide.sr7-staticslide-high{z-index:105!important}sr7-slide a{transition:none}sr7-module.sr7-top-fixed{width:100%;height:auto!important;backface-visibility:hidden}sr7-module.sr7-top-fixed sr7-adjuster{height:auto!important}sr7-module.sr7-top-fixed sr7-content,sr7-module.sr7-top-fixed sr7-content .sr7-layer:not(.sr7-ignore-pe) *{pointer-events:none}sr7-prl{top:50%;left:50%;z-index:10000;position:absolute}sr7-prl.off{display:none!important}sr7-prl.sr7-fade-in{animation:tp-fade-in 1s linear}.sr-force-hidden{display:none!important}@keyframes tp-rotateplane{0%{transform:perspective(120px) rotateX(0) rotateY(0)}50%{transform:perspective(120px) rotateX(-180.1deg) rotateY(0)}100%{transform:perspective(120px) rotateX(-180deg) rotateY(-179.9deg)}}@keyframes tp-fade-in{0%{opacity:0}20%{opacity:0}100%{opacity:100}}@keyframes tp-rotate{100%{transform:rotate(360deg)}}sr7-fonttest{width:auto;display:inline-block;white-space:nowrap;font-size:12px;color:#000}sr7-fonttest-wrap{position:absolute;contain:strict;top:-1500px;left:-1500px;width:1500px;height:1000px;pointer-events:none;opacity:0}.fn-wrap{display:flex;position:fixed;width:100px;height:50px;z-index:100;background-color:rgba(66,66,66,.2);justify-content:space-around;align-items:center;bottom:0;left:50%;transform:translateX(-50%)}.fn-left,.fn-right{display:flex;width:40px;height:40px;justify-content:center;align-items:center;background-color:gray;color:#fff;font-weight:700;font-size:20px;cursor:pointer}.sr7-layer .materical-icons,sr7-module .material-icons,sr7-txt .material-icons{font-size:inherit;vertical-align:top;line-height:inherit}