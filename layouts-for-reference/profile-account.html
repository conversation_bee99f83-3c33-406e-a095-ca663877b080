<? session_start(); require("../../inc/info.php"); ?><!doctype html>
						<!--[if lt IE 7]> <html class="ie6 oldie"> <![endif]-->
						<!--[if IE 7]>    <html class="ie7 oldie"> <![endif]-->
						<!--[if IE 8]>    <html class="ie8 oldie"> <![endif]-->
						<!--[if gt IE 8]><!-->
						<html class="">
						<!--<![endif]--><head>
					<meta charset="UTF-8"> 
					<meta name="viewport" content="width=device-width, maximum-scale=1.0, minimum-scale=1.0, initial-scale=1">

			
					<title>Get Beersty</title>
					
					<meta name="title" content="Get Beersty">
					<meta name="Keywords" content="Get Beersty">
					<meta name="Subject" content="Get Beersty">
					<meta name="Description" content="Get Beersty">
					<meta name="Language" content="English">
					<meta property="og:title" content="Get Beersty">
					<meta property="og:description" content="Get Beersty">
					<meta name="twitter:card" content="summary">
					<meta name="twitter:title" content="Get Beersty">
					<meta name="twitter:description" content="Get Beersty">
					<script type="text/javascript" src="/scripts/menu.js"></script><link rel="stylesheet" type="text/css" href="/static/fonts.css">
					<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
					<script type="text/javascript" src="/classes/slideshow.class.js"></script>
					<script type="text/javascript" src="/classes/div.class.js"></script> <link href="/css/bootstrap.min.css" rel="stylesheet">
    					<script type="text/javascript" src="/js/respond.min.js"></script>
						<script type="text/javascript" src="/js/bootstrap.min.js"></script>
				
			<style>
			html {
			    position: relative;
			    min-height: 100%;
			}
			#body_div {
				margin-bottom:0px;
			
			}
			#footer {
			    position: absolute;
			    left: 0;
			    bottom: 0;
			    height: 0px;
			    width: 100%;
			}
							
			</style>
			
			<link rel="stylesheet" type="text/css" href="/static/style_.css"></head><body><script type="text/javascript">
				var slideshow_id = 0;
				var slideshow2_id = 0;
				var slideshow3_id = 0;
			</script><div style="width:100%;" align="center"><? require(STATIC_ROOT."/header.php"); ?></div><div class="container-fluid header-bg">

<div class="container">
		<div class="row">
        
        	 <div class="hidden-lg hidden-md hidden-sm col-xs-4">
              	         	    </div>
            
        	 <div class="hidden-lg hidden-md hidden-sm col-xs-8">
             
                                
             </div>    
        </div><!---end of mobi row -->
 
		<div class="row"> 
            
        	<div class="col-lg-4 col-md-4 col-sm-4 hidden-xs">
                     </div>
           <div class="col-lg-8 col-md-8 col-sm-8 hidden-xs">
            
            	<div class="row"> 
                		<div class="col-sm-12">
                        	                         </div><!-- end of col row1 -->
                
               </div><!-- end of row1 -->
               <div class="row">
               			<div class="col-sm-12">
                        		<div class="row">
                                	<div class="col-sm-12">
                                                                        </div><!-- end col -->
                                	<div class="col-sm-12">
                                                                        </div><!-- end col -->
                                </div><!-- end of split row -->
                        
                        </div><!-- end of col row2 -->
               </div><!-- end of row2 -->
               
               <div class="row">
               			<div class="col-sm-12">
                        	                        </div><!-- end of col row3 -->
               </div><!-- end of row3 -->
            
    </div><!-- end of the outer row -->

        
</div><!-- header container --> 

 </div>
  
</div><!-- end fluid header -->


<div class="container-fluid nav-bg1">
			
        
				 
		
</div><!-- end fluid container -->




<div class="container-fluid main_body_bg">


<div class="container container-width">

  <div class="col-lg-12 col-md-12 col-xs-12">
  </div>


    
    		<div class="col-lg-8 col-md-8 col-sm-8 col-xs-12 left-body-margin">

            
                        
            </div>

			<div class="col-lg-4 col-md-4 col-sm-4 col-xs-12 right-body-margin">
            		<div class="row">
                        <div class="col-sm-12">
                                                    </div>
                       
            		</div>
            </div>
            


</div><!---container  --> 

</div><!---end fluid main body -->    
            
<div class="container-fluid social-bg">
    <div class="container">
        <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12">
            
                           
            </div>
            
       </div>
    </div>
</div>

          

<div class="container-fluid cf-background">

<div class="container">
  <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12">
            
                       
      </div>
            
  </div><!---end row -->
</div><!---end container  -->

<div class="container">

        <div class="row">

                        <div class="col-md-4 col-sm-4 foot-rules">
                                              
                        </div>
                        
                        <div class="col-md-4 col-sm-4 foot-rules">
                                                      
                        </div>
                        
                        
                        <div class="col-md-4 col-sm-4 foot-rules">
                                                 
                       
                        </div>
                        
                        
                        
                        
                        
           
           </div><!---close row -->

</div><!-- inside container -->

                     


<!--close container 5--> 
    <div class="container">
        <div class="row foot_bottom3">
            
           
                            
            </div>
             
       </div>
        
        
    
      
                <div class="col-lg-12 col-md-12 foot_bottom2">
                              </div>
                
            
       
        
 
    <div class="container"> 
           <div class="row foot_bottom1">
                             
            </div>
       </div>
        
        
     </div><!--footer container -->    <footer></footer></body></html>