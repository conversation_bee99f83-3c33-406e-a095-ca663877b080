(function(global,factory){typeof exports==="object"&&typeof module!=="undefined"?factory(exports,require("jquery")):typeof define==="function"&&define.amd?define(["exports","jquery"],factory):(global=typeof globalThis!=="undefined"?globalThis:global||self,factory(global.bootstrap={},global.jQuery))})(this,function(exports,$){"use strict";function _interopDefaultLegacy(e){return e&&typeof e==="object"&&"default"in e?e:{default:e}}var $__default=_interopDefaultLegacy($);function _defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor)}}function _createClass(Constructor,protoProps,staticProps){if(protoProps)_defineProperties(Constructor.prototype,protoProps);if(staticProps)_defineProperties(Constructor,staticProps);Object.defineProperty(Constructor,"prototype",{writable:false});return Constructor}function _extends$1(){_extends$1=Object.assign?Object.assign.bind():function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source){if(Object.prototype.hasOwnProperty.call(source,key)){target[key]=source[key]}}}return target};return _extends$1.apply(this,arguments)}function _inheritsLoose(subClass,superClass){subClass.prototype=Object.create(superClass.prototype);subClass.prototype.constructor=subClass;_setPrototypeOf(subClass,superClass)}function _setPrototypeOf(o,p){_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function _setPrototypeOf(o,p){o.__proto__=p;return o};return _setPrototypeOf(o,p)}var TRANSITION_END="transitionend";var MAX_UID=1e6;var MILLISECONDS_MULTIPLIER=1e3;function toType(obj){if(obj===null||typeof obj==="undefined"){return""+obj}return{}.toString.call(obj).match(/\s([a-z]+)/i)[1].toLowerCase()}function getSpecialTransitionEndEvent(){return{bindType:TRANSITION_END,delegateType:TRANSITION_END,handle:function handle(event){if($__default["default"](event.target).is(this)){return event.handleObj.handler.apply(this,arguments)}return undefined}}}function transitionEndEmulator(duration){var _this=this;var called=false;$__default["default"](this).one(Util.TRANSITION_END,function(){called=true});setTimeout(function(){if(!called){Util.triggerTransitionEnd(_this)}},duration);return this}function setTransitionEndSupport(){$__default["default"].fn.emulateTransitionEnd=transitionEndEmulator;$__default["default"].event.special[Util.TRANSITION_END]=getSpecialTransitionEndEvent()}var Util={TRANSITION_END:"bsTransitionEnd",getUID:function getUID(prefix){do{prefix+=~~(Math.random()*MAX_UID)}while(document.getElementById(prefix));return prefix},getSelectorFromElement:function getSelectorFromElement(element){var selector=element.getAttribute("data-target");if(!selector||selector==="#"){var hrefAttr=element.getAttribute("href");selector=hrefAttr&&hrefAttr!=="#"?hrefAttr.trim():""}try{return document.querySelector(selector)?selector:null}catch(_){return null}},getTransitionDurationFromElement:function getTransitionDurationFromElement(element){if(!element){return 0}var transitionDuration=$__default["default"](element).css("transition-duration");var transitionDelay=$__default["default"](element).css("transition-delay");var floatTransitionDuration=parseFloat(transitionDuration);var floatTransitionDelay=parseFloat(transitionDelay);if(!floatTransitionDuration&&!floatTransitionDelay){return 0}transitionDuration=transitionDuration.split(",")[0];transitionDelay=transitionDelay.split(",")[0];return(parseFloat(transitionDuration)+parseFloat(transitionDelay))*MILLISECONDS_MULTIPLIER},reflow:function reflow(element){return element.offsetHeight},triggerTransitionEnd:function triggerTransitionEnd(element){$__default["default"](element).trigger(TRANSITION_END)},supportsTransitionEnd:function supportsTransitionEnd(){return Boolean(TRANSITION_END)},isElement:function isElement(obj){return(obj[0]||obj).nodeType},typeCheckConfig:function typeCheckConfig(componentName,config,configTypes){for(var property in configTypes){if(Object.prototype.hasOwnProperty.call(configTypes,property)){var expectedTypes=configTypes[property];var value=config[property];var valueType=value&&Util.isElement(value)?"element":toType(value);if(!new RegExp(expectedTypes).test(valueType)){throw new Error(componentName.toUpperCase()+": "+('Option "'+property+'" provided type "'+valueType+'" ')+('but expected type "'+expectedTypes+'".'))}}}},findShadowRoot:function findShadowRoot(element){if(!document.documentElement.attachShadow){return null}if(typeof element.getRootNode==="function"){var root=element.getRootNode();return root instanceof ShadowRoot?root:null}if(element instanceof ShadowRoot){return element}if(!element.parentNode){return null}return Util.findShadowRoot(element.parentNode)},jQueryDetection:function jQueryDetection(){if(typeof $__default["default"]==="undefined"){throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.")}var version=$__default["default"].fn.jquery.split(" ")[0].split(".");var minMajor=1;var ltMajor=2;var minMinor=9;var minPatch=1;var maxMajor=4;if(version[0]<ltMajor&&version[1]<minMinor||version[0]===minMajor&&version[1]===minMinor&&version[2]<minPatch||version[0]>=maxMajor){throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}}};Util.jQueryDetection();setTransitionEndSupport();var NAME$a="alert";var VERSION$a="4.6.2";var DATA_KEY$a="bs.alert";var EVENT_KEY$a="."+DATA_KEY$a;var DATA_API_KEY$7=".data-api";var JQUERY_NO_CONFLICT$a=$__default["default"].fn[NAME$a];var CLASS_NAME_ALERT="alert";var CLASS_NAME_FADE$5="fade";var CLASS_NAME_SHOW$7="show";var EVENT_CLOSE="close"+EVENT_KEY$a;var EVENT_CLOSED="closed"+EVENT_KEY$a;var EVENT_CLICK_DATA_API$6="click"+EVENT_KEY$a+DATA_API_KEY$7;var SELECTOR_DISMISS='[data-dismiss="alert"]';var Alert=function(){function Alert(element){this._element=element}var _proto=Alert.prototype;_proto.close=function close(element){var rootElement=this._element;if(element){rootElement=this._getRootElement(element)}var customEvent=this._triggerCloseEvent(rootElement);if(customEvent.isDefaultPrevented()){return}this._removeElement(rootElement)};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$a);this._element=null};_proto._getRootElement=function _getRootElement(element){var selector=Util.getSelectorFromElement(element);var parent=false;if(selector){parent=document.querySelector(selector)}if(!parent){parent=$__default["default"](element).closest("."+CLASS_NAME_ALERT)[0]}return parent};_proto._triggerCloseEvent=function _triggerCloseEvent(element){var closeEvent=$__default["default"].Event(EVENT_CLOSE);$__default["default"](element).trigger(closeEvent);return closeEvent};_proto._removeElement=function _removeElement(element){var _this=this;$__default["default"](element).removeClass(CLASS_NAME_SHOW$7);if(!$__default["default"](element).hasClass(CLASS_NAME_FADE$5)){this._destroyElement(element);return}var transitionDuration=Util.getTransitionDurationFromElement(element);$__default["default"](element).one(Util.TRANSITION_END,function(event){return _this._destroyElement(element,event)}).emulateTransitionEnd(transitionDuration)};_proto._destroyElement=function _destroyElement(element){$__default["default"](element).detach().trigger(EVENT_CLOSED).remove()};Alert._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var $element=$__default["default"](this);var data=$element.data(DATA_KEY$a);if(!data){data=new Alert(this);$element.data(DATA_KEY$a,data)}if(config==="close"){data[config](this)}})};Alert._handleDismiss=function _handleDismiss(alertInstance){return function(event){if(event){event.preventDefault()}alertInstance.close(this)}};_createClass(Alert,null,[{key:"VERSION",get:function get(){return VERSION$a}}]);return Alert}();$__default["default"](document).on(EVENT_CLICK_DATA_API$6,SELECTOR_DISMISS,Alert._handleDismiss(new Alert));$__default["default"].fn[NAME$a]=Alert._jQueryInterface;$__default["default"].fn[NAME$a].Constructor=Alert;$__default["default"].fn[NAME$a].noConflict=function(){$__default["default"].fn[NAME$a]=JQUERY_NO_CONFLICT$a;return Alert._jQueryInterface};var NAME$9="button";var VERSION$9="4.6.2";var DATA_KEY$9="bs.button";var EVENT_KEY$9="."+DATA_KEY$9;var DATA_API_KEY$6=".data-api";var JQUERY_NO_CONFLICT$9=$__default["default"].fn[NAME$9];var CLASS_NAME_ACTIVE$3="active";var CLASS_NAME_BUTTON="btn";var CLASS_NAME_FOCUS="focus";var EVENT_CLICK_DATA_API$5="click"+EVENT_KEY$9+DATA_API_KEY$6;var EVENT_FOCUS_BLUR_DATA_API="focus"+EVENT_KEY$9+DATA_API_KEY$6+" "+("blur"+EVENT_KEY$9+DATA_API_KEY$6);var EVENT_LOAD_DATA_API$2="load"+EVENT_KEY$9+DATA_API_KEY$6;var SELECTOR_DATA_TOGGLE_CARROT='[data-toggle^="button"]';var SELECTOR_DATA_TOGGLES='[data-toggle="buttons"]';var SELECTOR_DATA_TOGGLE$4='[data-toggle="button"]';var SELECTOR_DATA_TOGGLES_BUTTONS='[data-toggle="buttons"] .btn';var SELECTOR_INPUT='input:not([type="hidden"])';var SELECTOR_ACTIVE$2=".active";var SELECTOR_BUTTON=".btn";var Button=function(){function Button(element){this._element=element;this.shouldAvoidTriggerChange=false}var _proto=Button.prototype;_proto.toggle=function toggle(){var triggerChangeEvent=true;var addAriaPressed=true;var rootElement=$__default["default"](this._element).closest(SELECTOR_DATA_TOGGLES)[0];if(rootElement){var input=this._element.querySelector(SELECTOR_INPUT);if(input){if(input.type==="radio"){if(input.checked&&this._element.classList.contains(CLASS_NAME_ACTIVE$3)){triggerChangeEvent=false}else{var activeElement=rootElement.querySelector(SELECTOR_ACTIVE$2);if(activeElement){$__default["default"](activeElement).removeClass(CLASS_NAME_ACTIVE$3)}}}if(triggerChangeEvent){if(input.type==="checkbox"||input.type==="radio"){input.checked=!this._element.classList.contains(CLASS_NAME_ACTIVE$3)}if(!this.shouldAvoidTriggerChange){$__default["default"](input).trigger("change")}}input.focus();addAriaPressed=false}}if(!(this._element.hasAttribute("disabled")||this._element.classList.contains("disabled"))){if(addAriaPressed){this._element.setAttribute("aria-pressed",!this._element.classList.contains(CLASS_NAME_ACTIVE$3))}if(triggerChangeEvent){$__default["default"](this._element).toggleClass(CLASS_NAME_ACTIVE$3)}}};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$9);this._element=null};Button._jQueryInterface=function _jQueryInterface(config,avoidTriggerChange){return this.each(function(){var $element=$__default["default"](this);var data=$element.data(DATA_KEY$9);if(!data){data=new Button(this);$element.data(DATA_KEY$9,data)}data.shouldAvoidTriggerChange=avoidTriggerChange;if(config==="toggle"){data[config]()}})};_createClass(Button,null,[{key:"VERSION",get:function get(){return VERSION$9}}]);return Button}();$__default["default"](document).on(EVENT_CLICK_DATA_API$5,SELECTOR_DATA_TOGGLE_CARROT,function(event){var button=event.target;var initialButton=button;if(!$__default["default"](button).hasClass(CLASS_NAME_BUTTON)){button=$__default["default"](button).closest(SELECTOR_BUTTON)[0]}if(!button||button.hasAttribute("disabled")||button.classList.contains("disabled")){event.preventDefault()}else{var inputBtn=button.querySelector(SELECTOR_INPUT);if(inputBtn&&(inputBtn.hasAttribute("disabled")||inputBtn.classList.contains("disabled"))){event.preventDefault();return}if(initialButton.tagName==="INPUT"||button.tagName!=="LABEL"){Button._jQueryInterface.call($__default["default"](button),"toggle",initialButton.tagName==="INPUT")}}}).on(EVENT_FOCUS_BLUR_DATA_API,SELECTOR_DATA_TOGGLE_CARROT,function(event){var button=$__default["default"](event.target).closest(SELECTOR_BUTTON)[0];$__default["default"](button).toggleClass(CLASS_NAME_FOCUS,/^focus(in)?$/.test(event.type))});$__default["default"](window).on(EVENT_LOAD_DATA_API$2,function(){var buttons=[].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLES_BUTTONS));for(var i=0,len=buttons.length;i<len;i++){var button=buttons[i];var input=button.querySelector(SELECTOR_INPUT);if(input.checked||input.hasAttribute("checked")){button.classList.add(CLASS_NAME_ACTIVE$3)}else{button.classList.remove(CLASS_NAME_ACTIVE$3)}}buttons=[].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE$4));for(var _i=0,_len=buttons.length;_i<_len;_i++){var _button=buttons[_i];if(_button.getAttribute("aria-pressed")==="true"){_button.classList.add(CLASS_NAME_ACTIVE$3)}else{_button.classList.remove(CLASS_NAME_ACTIVE$3)}}});$__default["default"].fn[NAME$9]=Button._jQueryInterface;$__default["default"].fn[NAME$9].Constructor=Button;$__default["default"].fn[NAME$9].noConflict=function(){$__default["default"].fn[NAME$9]=JQUERY_NO_CONFLICT$9;return Button._jQueryInterface};var NAME$8="carousel";var VERSION$8="4.6.2";var DATA_KEY$8="bs.carousel";var EVENT_KEY$8="."+DATA_KEY$8;var DATA_API_KEY$5=".data-api";var JQUERY_NO_CONFLICT$8=$__default["default"].fn[NAME$8];var ARROW_LEFT_KEYCODE=37;var ARROW_RIGHT_KEYCODE=39;var TOUCHEVENT_COMPAT_WAIT=500;var SWIPE_THRESHOLD=40;var CLASS_NAME_CAROUSEL="carousel";var CLASS_NAME_ACTIVE$2="active";var CLASS_NAME_SLIDE="slide";var CLASS_NAME_RIGHT="carousel-item-right";var CLASS_NAME_LEFT="carousel-item-left";var CLASS_NAME_NEXT="carousel-item-next";var CLASS_NAME_PREV="carousel-item-prev";var CLASS_NAME_POINTER_EVENT="pointer-event";var DIRECTION_NEXT="next";var DIRECTION_PREV="prev";var DIRECTION_LEFT="left";var DIRECTION_RIGHT="right";var EVENT_SLIDE="slide"+EVENT_KEY$8;var EVENT_SLID="slid"+EVENT_KEY$8;var EVENT_KEYDOWN="keydown"+EVENT_KEY$8;var EVENT_MOUSEENTER="mouseenter"+EVENT_KEY$8;var EVENT_MOUSELEAVE="mouseleave"+EVENT_KEY$8;var EVENT_TOUCHSTART="touchstart"+EVENT_KEY$8;var EVENT_TOUCHMOVE="touchmove"+EVENT_KEY$8;var EVENT_TOUCHEND="touchend"+EVENT_KEY$8;var EVENT_POINTERDOWN="pointerdown"+EVENT_KEY$8;var EVENT_POINTERUP="pointerup"+EVENT_KEY$8;var EVENT_DRAG_START="dragstart"+EVENT_KEY$8;var EVENT_LOAD_DATA_API$1="load"+EVENT_KEY$8+DATA_API_KEY$5;var EVENT_CLICK_DATA_API$4="click"+EVENT_KEY$8+DATA_API_KEY$5;var SELECTOR_ACTIVE$1=".active";var SELECTOR_ACTIVE_ITEM=".active.carousel-item";var SELECTOR_ITEM=".carousel-item";var SELECTOR_ITEM_IMG=".carousel-item img";var SELECTOR_NEXT_PREV=".carousel-item-next, .carousel-item-prev";var SELECTOR_INDICATORS=".carousel-indicators";var SELECTOR_DATA_SLIDE="[data-slide], [data-slide-to]";var SELECTOR_DATA_RIDE='[data-ride="carousel"]';var Default$7={interval:5e3,keyboard:true,slide:false,pause:"hover",wrap:true,touch:true};var DefaultType$7={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"};var PointerType={TOUCH:"touch",PEN:"pen"};var Carousel=function(){function Carousel(element,config){this._items=null;this._interval=null;this._activeElement=null;this._isPaused=false;this._isSliding=false;this.touchTimeout=null;this.touchStartX=0;this.touchDeltaX=0;this._config=this._getConfig(config);this._element=element;this._indicatorsElement=this._element.querySelector(SELECTOR_INDICATORS);this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0;this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent);this._addEventListeners()}var _proto=Carousel.prototype;_proto.next=function next(){if(!this._isSliding){this._slide(DIRECTION_NEXT)}};_proto.nextWhenVisible=function nextWhenVisible(){var $element=$__default["default"](this._element);if(!document.hidden&&$element.is(":visible")&&$element.css("visibility")!=="hidden"){this.next()}};_proto.prev=function prev(){if(!this._isSliding){this._slide(DIRECTION_PREV)}};_proto.pause=function pause(event){if(!event){this._isPaused=true}if(this._element.querySelector(SELECTOR_NEXT_PREV)){Util.triggerTransitionEnd(this._element);this.cycle(true)}clearInterval(this._interval);this._interval=null};_proto.cycle=function cycle(event){if(!event){this._isPaused=false}if(this._interval){clearInterval(this._interval);this._interval=null}if(this._config.interval&&!this._isPaused){this._updateInterval();this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval)}};_proto.to=function to(index){var _this=this;this._activeElement=this._element.querySelector(SELECTOR_ACTIVE_ITEM);var activeIndex=this._getItemIndex(this._activeElement);if(index>this._items.length-1||index<0){return}if(this._isSliding){$__default["default"](this._element).one(EVENT_SLID,function(){return _this.to(index)});return}if(activeIndex===index){this.pause();this.cycle();return}var direction=index>activeIndex?DIRECTION_NEXT:DIRECTION_PREV;this._slide(direction,this._items[index])};_proto.dispose=function dispose(){$__default["default"](this._element).off(EVENT_KEY$8);$__default["default"].removeData(this._element,DATA_KEY$8);this._items=null;this._config=null;this._element=null;this._interval=null;this._isPaused=null;this._isSliding=null;this._activeElement=null;this._indicatorsElement=null};_proto._getConfig=function _getConfig(config){config=_extends$1({},Default$7,config);Util.typeCheckConfig(NAME$8,config,DefaultType$7);return config};_proto._handleSwipe=function _handleSwipe(){var absDeltax=Math.abs(this.touchDeltaX);if(absDeltax<=SWIPE_THRESHOLD){return}var direction=absDeltax/this.touchDeltaX;this.touchDeltaX=0;if(direction>0){this.prev()}if(direction<0){this.next()}};_proto._addEventListeners=function _addEventListeners(){var _this2=this;if(this._config.keyboard){$__default["default"](this._element).on(EVENT_KEYDOWN,function(event){return _this2._keydown(event)})}if(this._config.pause==="hover"){$__default["default"](this._element).on(EVENT_MOUSEENTER,function(event){return _this2.pause(event)}).on(EVENT_MOUSELEAVE,function(event){return _this2.cycle(event)})}if(this._config.touch){this._addTouchEventListeners()}};_proto._addTouchEventListeners=function _addTouchEventListeners(){var _this3=this;if(!this._touchSupported){return}var start=function start(event){if(_this3._pointerEvent&&PointerType[event.originalEvent.pointerType.toUpperCase()]){_this3.touchStartX=event.originalEvent.clientX}else if(!_this3._pointerEvent){_this3.touchStartX=event.originalEvent.touches[0].clientX}};var move=function move(event){_this3.touchDeltaX=event.originalEvent.touches&&event.originalEvent.touches.length>1?0:event.originalEvent.touches[0].clientX-_this3.touchStartX};var end=function end(event){if(_this3._pointerEvent&&PointerType[event.originalEvent.pointerType.toUpperCase()]){_this3.touchDeltaX=event.originalEvent.clientX-_this3.touchStartX}_this3._handleSwipe();if(_this3._config.pause==="hover"){_this3.pause();if(_this3.touchTimeout){clearTimeout(_this3.touchTimeout)}_this3.touchTimeout=setTimeout(function(event){return _this3.cycle(event)},TOUCHEVENT_COMPAT_WAIT+_this3._config.interval)}};$__default["default"](this._element.querySelectorAll(SELECTOR_ITEM_IMG)).on(EVENT_DRAG_START,function(e){return e.preventDefault()});if(this._pointerEvent){$__default["default"](this._element).on(EVENT_POINTERDOWN,function(event){return start(event)});$__default["default"](this._element).on(EVENT_POINTERUP,function(event){return end(event)});this._element.classList.add(CLASS_NAME_POINTER_EVENT)}else{$__default["default"](this._element).on(EVENT_TOUCHSTART,function(event){return start(event)});$__default["default"](this._element).on(EVENT_TOUCHMOVE,function(event){return move(event)});$__default["default"](this._element).on(EVENT_TOUCHEND,function(event){return end(event)})}};_proto._keydown=function _keydown(event){if(/input|textarea/i.test(event.target.tagName)){return}switch(event.which){case ARROW_LEFT_KEYCODE:event.preventDefault();this.prev();break;case ARROW_RIGHT_KEYCODE:event.preventDefault();this.next();break}};_proto._getItemIndex=function _getItemIndex(element){this._items=element&&element.parentNode?[].slice.call(element.parentNode.querySelectorAll(SELECTOR_ITEM)):[];return this._items.indexOf(element)};_proto._getItemByDirection=function _getItemByDirection(direction,activeElement){var isNextDirection=direction===DIRECTION_NEXT;var isPrevDirection=direction===DIRECTION_PREV;var activeIndex=this._getItemIndex(activeElement);var lastItemIndex=this._items.length-1;var isGoingToWrap=isPrevDirection&&activeIndex===0||isNextDirection&&activeIndex===lastItemIndex;if(isGoingToWrap&&!this._config.wrap){return activeElement}var delta=direction===DIRECTION_PREV?-1:1;var itemIndex=(activeIndex+delta)%this._items.length;return itemIndex===-1?this._items[this._items.length-1]:this._items[itemIndex]};_proto._triggerSlideEvent=function _triggerSlideEvent(relatedTarget,eventDirectionName){var targetIndex=this._getItemIndex(relatedTarget);var fromIndex=this._getItemIndex(this._element.querySelector(SELECTOR_ACTIVE_ITEM));var slideEvent=$__default["default"].Event(EVENT_SLIDE,{relatedTarget:relatedTarget,direction:eventDirectionName,from:fromIndex,to:targetIndex});$__default["default"](this._element).trigger(slideEvent);return slideEvent};_proto._setActiveIndicatorElement=function _setActiveIndicatorElement(element){if(this._indicatorsElement){var indicators=[].slice.call(this._indicatorsElement.querySelectorAll(SELECTOR_ACTIVE$1));$__default["default"](indicators).removeClass(CLASS_NAME_ACTIVE$2);var nextIndicator=this._indicatorsElement.children[this._getItemIndex(element)];if(nextIndicator){$__default["default"](nextIndicator).addClass(CLASS_NAME_ACTIVE$2)}}};_proto._updateInterval=function _updateInterval(){var element=this._activeElement||this._element.querySelector(SELECTOR_ACTIVE_ITEM);if(!element){return}var elementInterval=parseInt(element.getAttribute("data-interval"),10);if(elementInterval){this._config.defaultInterval=this._config.defaultInterval||this._config.interval;this._config.interval=elementInterval}else{this._config.interval=this._config.defaultInterval||this._config.interval}};_proto._slide=function _slide(direction,element){var _this4=this;var activeElement=this._element.querySelector(SELECTOR_ACTIVE_ITEM);var activeElementIndex=this._getItemIndex(activeElement);var nextElement=element||activeElement&&this._getItemByDirection(direction,activeElement);var nextElementIndex=this._getItemIndex(nextElement);var isCycling=Boolean(this._interval);var directionalClassName;var orderClassName;var eventDirectionName;if(direction===DIRECTION_NEXT){directionalClassName=CLASS_NAME_LEFT;orderClassName=CLASS_NAME_NEXT;eventDirectionName=DIRECTION_LEFT}else{directionalClassName=CLASS_NAME_RIGHT;orderClassName=CLASS_NAME_PREV;eventDirectionName=DIRECTION_RIGHT}if(nextElement&&$__default["default"](nextElement).hasClass(CLASS_NAME_ACTIVE$2)){this._isSliding=false;return}var slideEvent=this._triggerSlideEvent(nextElement,eventDirectionName);if(slideEvent.isDefaultPrevented()){return}if(!activeElement||!nextElement){return}this._isSliding=true;if(isCycling){this.pause()}this._setActiveIndicatorElement(nextElement);this._activeElement=nextElement;var slidEvent=$__default["default"].Event(EVENT_SLID,{relatedTarget:nextElement,direction:eventDirectionName,from:activeElementIndex,to:nextElementIndex});if($__default["default"](this._element).hasClass(CLASS_NAME_SLIDE)){$__default["default"](nextElement).addClass(orderClassName);Util.reflow(nextElement);$__default["default"](activeElement).addClass(directionalClassName);$__default["default"](nextElement).addClass(directionalClassName);var transitionDuration=Util.getTransitionDurationFromElement(activeElement);$__default["default"](activeElement).one(Util.TRANSITION_END,function(){$__default["default"](nextElement).removeClass(directionalClassName+" "+orderClassName).addClass(CLASS_NAME_ACTIVE$2);$__default["default"](activeElement).removeClass(CLASS_NAME_ACTIVE$2+" "+orderClassName+" "+directionalClassName);_this4._isSliding=false;setTimeout(function(){return $__default["default"](_this4._element).trigger(slidEvent)},0)}).emulateTransitionEnd(transitionDuration)}else{$__default["default"](activeElement).removeClass(CLASS_NAME_ACTIVE$2);$__default["default"](nextElement).addClass(CLASS_NAME_ACTIVE$2);this._isSliding=false;$__default["default"](this._element).trigger(slidEvent)}if(isCycling){this.cycle()}};Carousel._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var data=$__default["default"](this).data(DATA_KEY$8);var _config=_extends$1({},Default$7,$__default["default"](this).data());if(typeof config==="object"){_config=_extends$1({},_config,config)}var action=typeof config==="string"?config:_config.slide;if(!data){data=new Carousel(this,_config);$__default["default"](this).data(DATA_KEY$8,data)}if(typeof config==="number"){data.to(config)}else if(typeof action==="string"){if(typeof data[action]==="undefined"){throw new TypeError('No method named "'+action+'"')}data[action]()}else if(_config.interval&&_config.ride){data.pause();data.cycle()}})};Carousel._dataApiClickHandler=function _dataApiClickHandler(event){var selector=Util.getSelectorFromElement(this);if(!selector){return}var target=$__default["default"](selector)[0];if(!target||!$__default["default"](target).hasClass(CLASS_NAME_CAROUSEL)){return}var config=_extends$1({},$__default["default"](target).data(),$__default["default"](this).data());var slideIndex=this.getAttribute("data-slide-to");if(slideIndex){config.interval=false}Carousel._jQueryInterface.call($__default["default"](target),config);if(slideIndex){$__default["default"](target).data(DATA_KEY$8).to(slideIndex)}event.preventDefault()};_createClass(Carousel,null,[{key:"VERSION",get:function get(){return VERSION$8}},{key:"Default",get:function get(){return Default$7}}]);return Carousel}();$__default["default"](document).on(EVENT_CLICK_DATA_API$4,SELECTOR_DATA_SLIDE,Carousel._dataApiClickHandler);$__default["default"](window).on(EVENT_LOAD_DATA_API$1,function(){var carousels=[].slice.call(document.querySelectorAll(SELECTOR_DATA_RIDE));for(var i=0,len=carousels.length;i<len;i++){var $carousel=$__default["default"](carousels[i]);Carousel._jQueryInterface.call($carousel,$carousel.data())}});$__default["default"].fn[NAME$8]=Carousel._jQueryInterface;$__default["default"].fn[NAME$8].Constructor=Carousel;$__default["default"].fn[NAME$8].noConflict=function(){$__default["default"].fn[NAME$8]=JQUERY_NO_CONFLICT$8;return Carousel._jQueryInterface};var NAME$7="collapse";var VERSION$7="4.6.2";var DATA_KEY$7="bs.collapse";var EVENT_KEY$7="."+DATA_KEY$7;var DATA_API_KEY$4=".data-api";var JQUERY_NO_CONFLICT$7=$__default["default"].fn[NAME$7];var CLASS_NAME_SHOW$6="show";var CLASS_NAME_COLLAPSE="collapse";var CLASS_NAME_COLLAPSING="collapsing";var CLASS_NAME_COLLAPSED="collapsed";var DIMENSION_WIDTH="width";var DIMENSION_HEIGHT="height";var EVENT_SHOW$4="show"+EVENT_KEY$7;var EVENT_SHOWN$4="shown"+EVENT_KEY$7;var EVENT_HIDE$4="hide"+EVENT_KEY$7;var EVENT_HIDDEN$4="hidden"+EVENT_KEY$7;var EVENT_CLICK_DATA_API$3="click"+EVENT_KEY$7+DATA_API_KEY$4;var SELECTOR_ACTIVES=".show, .collapsing";var SELECTOR_DATA_TOGGLE$3='[data-toggle="collapse"]';var Default$6={toggle:true,parent:""};var DefaultType$6={toggle:"boolean",parent:"(string|element)"};var Collapse=function(){function Collapse(element,config){this._isTransitioning=false;this._element=element;this._config=this._getConfig(config);this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+element.id+'"],'+('[data-toggle="collapse"][data-target="#'+element.id+'"]')));var toggleList=[].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE$3));for(var i=0,len=toggleList.length;i<len;i++){var elem=toggleList[i];var selector=Util.getSelectorFromElement(elem);var filterElement=[].slice.call(document.querySelectorAll(selector)).filter(function(foundElem){return foundElem===element});if(selector!==null&&filterElement.length>0){this._selector=selector;this._triggerArray.push(elem)}}this._parent=this._config.parent?this._getParent():null;if(!this._config.parent){this._addAriaAndCollapsedClass(this._element,this._triggerArray)}if(this._config.toggle){this.toggle()}}var _proto=Collapse.prototype;_proto.toggle=function toggle(){if($__default["default"](this._element).hasClass(CLASS_NAME_SHOW$6)){this.hide()}else{this.show()}};_proto.show=function show(){var _this=this;if(this._isTransitioning||$__default["default"](this._element).hasClass(CLASS_NAME_SHOW$6)){return}var actives;var activesData;if(this._parent){actives=[].slice.call(this._parent.querySelectorAll(SELECTOR_ACTIVES)).filter(function(elem){if(typeof _this._config.parent==="string"){return elem.getAttribute("data-parent")===_this._config.parent}return elem.classList.contains(CLASS_NAME_COLLAPSE)});if(actives.length===0){actives=null}}if(actives){activesData=$__default["default"](actives).not(this._selector).data(DATA_KEY$7);if(activesData&&activesData._isTransitioning){return}}var startEvent=$__default["default"].Event(EVENT_SHOW$4);$__default["default"](this._element).trigger(startEvent);if(startEvent.isDefaultPrevented()){return}if(actives){Collapse._jQueryInterface.call($__default["default"](actives).not(this._selector),"hide");if(!activesData){$__default["default"](actives).data(DATA_KEY$7,null)}}var dimension=this._getDimension();$__default["default"](this._element).removeClass(CLASS_NAME_COLLAPSE).addClass(CLASS_NAME_COLLAPSING);this._element.style[dimension]=0;if(this._triggerArray.length){$__default["default"](this._triggerArray).removeClass(CLASS_NAME_COLLAPSED).attr("aria-expanded",true)}this.setTransitioning(true);var complete=function complete(){$__default["default"](_this._element).removeClass(CLASS_NAME_COLLAPSING).addClass(CLASS_NAME_COLLAPSE+" "+CLASS_NAME_SHOW$6);_this._element.style[dimension]="";_this.setTransitioning(false);$__default["default"](_this._element).trigger(EVENT_SHOWN$4)};var capitalizedDimension=dimension[0].toUpperCase()+dimension.slice(1);var scrollSize="scroll"+capitalizedDimension;var transitionDuration=Util.getTransitionDurationFromElement(this._element);$__default["default"](this._element).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration);this._element.style[dimension]=this._element[scrollSize]+"px"};_proto.hide=function hide(){var _this2=this;if(this._isTransitioning||!$__default["default"](this._element).hasClass(CLASS_NAME_SHOW$6)){return}var startEvent=$__default["default"].Event(EVENT_HIDE$4);$__default["default"](this._element).trigger(startEvent);if(startEvent.isDefaultPrevented()){return}var dimension=this._getDimension();this._element.style[dimension]=this._element.getBoundingClientRect()[dimension]+"px";Util.reflow(this._element);$__default["default"](this._element).addClass(CLASS_NAME_COLLAPSING).removeClass(CLASS_NAME_COLLAPSE+" "+CLASS_NAME_SHOW$6);var triggerArrayLength=this._triggerArray.length;if(triggerArrayLength>0){for(var i=0;i<triggerArrayLength;i++){var trigger=this._triggerArray[i];var selector=Util.getSelectorFromElement(trigger);if(selector!==null){var $elem=$__default["default"]([].slice.call(document.querySelectorAll(selector)));if(!$elem.hasClass(CLASS_NAME_SHOW$6)){$__default["default"](trigger).addClass(CLASS_NAME_COLLAPSED).attr("aria-expanded",false)}}}}this.setTransitioning(true);var complete=function complete(){_this2.setTransitioning(false);$__default["default"](_this2._element).removeClass(CLASS_NAME_COLLAPSING).addClass(CLASS_NAME_COLLAPSE).trigger(EVENT_HIDDEN$4)};this._element.style[dimension]="";var transitionDuration=Util.getTransitionDurationFromElement(this._element);$__default["default"](this._element).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)};_proto.setTransitioning=function setTransitioning(isTransitioning){this._isTransitioning=isTransitioning};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$7);this._config=null;this._parent=null;this._element=null;this._triggerArray=null;this._isTransitioning=null};_proto._getConfig=function _getConfig(config){config=_extends$1({},Default$6,config);config.toggle=Boolean(config.toggle);Util.typeCheckConfig(NAME$7,config,DefaultType$6);return config};_proto._getDimension=function _getDimension(){var hasWidth=$__default["default"](this._element).hasClass(DIMENSION_WIDTH);return hasWidth?DIMENSION_WIDTH:DIMENSION_HEIGHT};_proto._getParent=function _getParent(){var _this3=this;var parent;if(Util.isElement(this._config.parent)){parent=this._config.parent;if(typeof this._config.parent.jquery!=="undefined"){parent=this._config.parent[0]}}else{parent=document.querySelector(this._config.parent)}var selector='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]';var children=[].slice.call(parent.querySelectorAll(selector));$__default["default"](children).each(function(i,element){_this3._addAriaAndCollapsedClass(Collapse._getTargetFromElement(element),[element])});return parent};_proto._addAriaAndCollapsedClass=function _addAriaAndCollapsedClass(element,triggerArray){var isOpen=$__default["default"](element).hasClass(CLASS_NAME_SHOW$6);if(triggerArray.length){$__default["default"](triggerArray).toggleClass(CLASS_NAME_COLLAPSED,!isOpen).attr("aria-expanded",isOpen)}};Collapse._getTargetFromElement=function _getTargetFromElement(element){var selector=Util.getSelectorFromElement(element);return selector?document.querySelector(selector):null};Collapse._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var $element=$__default["default"](this);var data=$element.data(DATA_KEY$7);var _config=_extends$1({},Default$6,$element.data(),typeof config==="object"&&config?config:{});if(!data&&_config.toggle&&typeof config==="string"&&/show|hide/.test(config)){_config.toggle=false}if(!data){data=new Collapse(this,_config);$element.data(DATA_KEY$7,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};_createClass(Collapse,null,[{key:"VERSION",get:function get(){return VERSION$7}},{key:"Default",get:function get(){return Default$6}}]);return Collapse}();$__default["default"](document).on(EVENT_CLICK_DATA_API$3,SELECTOR_DATA_TOGGLE$3,function(event){if(event.currentTarget.tagName==="A"){event.preventDefault()}var $trigger=$__default["default"](this);var selector=Util.getSelectorFromElement(this);var selectors=[].slice.call(document.querySelectorAll(selector));$__default["default"](selectors).each(function(){var $target=$__default["default"](this);var data=$target.data(DATA_KEY$7);var config=data?"toggle":$trigger.data();Collapse._jQueryInterface.call($target,config)})});$__default["default"].fn[NAME$7]=Collapse._jQueryInterface;$__default["default"].fn[NAME$7].Constructor=Collapse;$__default["default"].fn[NAME$7].noConflict=function(){$__default["default"].fn[NAME$7]=JQUERY_NO_CONFLICT$7;return Collapse._jQueryInterface};var isBrowser=typeof window!=="undefined"&&typeof document!=="undefined"&&typeof navigator!=="undefined";var timeoutDuration=function(){var longerTimeoutBrowsers=["Edge","Trident","Firefox"];for(var i=0;i<longerTimeoutBrowsers.length;i+=1){if(isBrowser&&navigator.userAgent.indexOf(longerTimeoutBrowsers[i])>=0){return 1}}return 0}();function microtaskDebounce(fn){var called=false;return function(){if(called){return}called=true;window.Promise.resolve().then(function(){called=false;fn()})}}function taskDebounce(fn){var scheduled=false;return function(){if(!scheduled){scheduled=true;setTimeout(function(){scheduled=false;fn()},timeoutDuration)}}}var supportsMicroTasks=isBrowser&&window.Promise;var debounce=supportsMicroTasks?microtaskDebounce:taskDebounce;function isFunction(functionToCheck){var getType={};return functionToCheck&&getType.toString.call(functionToCheck)==="[object Function]"}function getStyleComputedProperty(element,property){if(element.nodeType!==1){return[]}var window=element.ownerDocument.defaultView;var css=window.getComputedStyle(element,null);return property?css[property]:css}function getParentNode(element){if(element.nodeName==="HTML"){return element}return element.parentNode||element.host}function getScrollParent(element){if(!element){return document.body}switch(element.nodeName){case"HTML":case"BODY":return element.ownerDocument.body;case"#document":return element.body}var _getStyleComputedProp=getStyleComputedProperty(element),overflow=_getStyleComputedProp.overflow,overflowX=_getStyleComputedProp.overflowX,overflowY=_getStyleComputedProp.overflowY;if(/(auto|scroll|overlay)/.test(overflow+overflowY+overflowX)){return element}return getScrollParent(getParentNode(element))}function getReferenceNode(reference){return reference&&reference.referenceNode?reference.referenceNode:reference}var isIE11=isBrowser&&!!(window.MSInputMethodContext&&document.documentMode);var isIE10=isBrowser&&/MSIE 10/.test(navigator.userAgent);function isIE(version){if(version===11){return isIE11}if(version===10){return isIE10}return isIE11||isIE10}function getOffsetParent(element){if(!element){return document.documentElement}var noOffsetParent=isIE(10)?document.body:null;var offsetParent=element.offsetParent||null;while(offsetParent===noOffsetParent&&element.nextElementSibling){offsetParent=(element=element.nextElementSibling).offsetParent}var nodeName=offsetParent&&offsetParent.nodeName;if(!nodeName||nodeName==="BODY"||nodeName==="HTML"){return element?element.ownerDocument.documentElement:document.documentElement}if(["TH","TD","TABLE"].indexOf(offsetParent.nodeName)!==-1&&getStyleComputedProperty(offsetParent,"position")==="static"){return getOffsetParent(offsetParent)}return offsetParent}function isOffsetContainer(element){var nodeName=element.nodeName;if(nodeName==="BODY"){return false}return nodeName==="HTML"||getOffsetParent(element.firstElementChild)===element}function getRoot(node){if(node.parentNode!==null){return getRoot(node.parentNode)}return node}function findCommonOffsetParent(element1,element2){if(!element1||!element1.nodeType||!element2||!element2.nodeType){return document.documentElement}var order=element1.compareDocumentPosition(element2)&Node.DOCUMENT_POSITION_FOLLOWING;var start=order?element1:element2;var end=order?element2:element1;var range=document.createRange();range.setStart(start,0);range.setEnd(end,0);var commonAncestorContainer=range.commonAncestorContainer;if(element1!==commonAncestorContainer&&element2!==commonAncestorContainer||start.contains(end)){if(isOffsetContainer(commonAncestorContainer)){return commonAncestorContainer}return getOffsetParent(commonAncestorContainer)}var element1root=getRoot(element1);if(element1root.host){return findCommonOffsetParent(element1root.host,element2)}else{return findCommonOffsetParent(element1,getRoot(element2).host)}}function getScroll(element){var side=arguments.length>1&&arguments[1]!==undefined?arguments[1]:"top";var upperSide=side==="top"?"scrollTop":"scrollLeft";var nodeName=element.nodeName;if(nodeName==="BODY"||nodeName==="HTML"){var html=element.ownerDocument.documentElement;var scrollingElement=element.ownerDocument.scrollingElement||html;return scrollingElement[upperSide]}return element[upperSide]}function includeScroll(rect,element){var subtract=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var scrollTop=getScroll(element,"top");var scrollLeft=getScroll(element,"left");var modifier=subtract?-1:1;rect.top+=scrollTop*modifier;rect.bottom+=scrollTop*modifier;rect.left+=scrollLeft*modifier;rect.right+=scrollLeft*modifier;return rect}function getBordersSize(styles,axis){var sideA=axis==="x"?"Left":"Top";var sideB=sideA==="Left"?"Right":"Bottom";return parseFloat(styles["border"+sideA+"Width"])+parseFloat(styles["border"+sideB+"Width"])}function getSize(axis,body,html,computedStyle){return Math.max(body["offset"+axis],body["scroll"+axis],html["client"+axis],html["offset"+axis],html["scroll"+axis],isIE(10)?parseInt(html["offset"+axis])+parseInt(computedStyle["margin"+(axis==="Height"?"Top":"Left")])+parseInt(computedStyle["margin"+(axis==="Height"?"Bottom":"Right")]):0)}function getWindowSizes(document){var body=document.body;var html=document.documentElement;var computedStyle=isIE(10)&&getComputedStyle(html);return{height:getSize("Height",body,html,computedStyle),width:getSize("Width",body,html,computedStyle)}}var classCallCheck=function(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function")}};var createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){if(protoProps)defineProperties(Constructor.prototype,protoProps);if(staticProps)defineProperties(Constructor,staticProps);return Constructor}}();var defineProperty=function(obj,key,value){if(key in obj){Object.defineProperty(obj,key,{value:value,enumerable:true,configurable:true,writable:true})}else{obj[key]=value}return obj};var _extends=Object.assign||function(target){for(var i=1;i<arguments.length;i++){var source=arguments[i];for(var key in source){if(Object.prototype.hasOwnProperty.call(source,key)){target[key]=source[key]}}}return target};function getClientRect(offsets){return _extends({},offsets,{right:offsets.left+offsets.width,bottom:offsets.top+offsets.height})}function getBoundingClientRect(element){var rect={};try{if(isIE(10)){rect=element.getBoundingClientRect();var scrollTop=getScroll(element,"top");var scrollLeft=getScroll(element,"left");rect.top+=scrollTop;rect.left+=scrollLeft;rect.bottom+=scrollTop;rect.right+=scrollLeft}else{rect=element.getBoundingClientRect()}}catch(e){}var result={left:rect.left,top:rect.top,width:rect.right-rect.left,height:rect.bottom-rect.top};var sizes=element.nodeName==="HTML"?getWindowSizes(element.ownerDocument):{};var width=sizes.width||element.clientWidth||result.width;var height=sizes.height||element.clientHeight||result.height;var horizScrollbar=element.offsetWidth-width;var vertScrollbar=element.offsetHeight-height;if(horizScrollbar||vertScrollbar){var styles=getStyleComputedProperty(element);horizScrollbar-=getBordersSize(styles,"x");vertScrollbar-=getBordersSize(styles,"y");result.width-=horizScrollbar;result.height-=vertScrollbar}return getClientRect(result)}function getOffsetRectRelativeToArbitraryNode(children,parent){var fixedPosition=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var isIE10=isIE(10);var isHTML=parent.nodeName==="HTML";var childrenRect=getBoundingClientRect(children);var parentRect=getBoundingClientRect(parent);var scrollParent=getScrollParent(children);var styles=getStyleComputedProperty(parent);var borderTopWidth=parseFloat(styles.borderTopWidth);var borderLeftWidth=parseFloat(styles.borderLeftWidth);if(fixedPosition&&isHTML){parentRect.top=Math.max(parentRect.top,0);parentRect.left=Math.max(parentRect.left,0)}var offsets=getClientRect({top:childrenRect.top-parentRect.top-borderTopWidth,left:childrenRect.left-parentRect.left-borderLeftWidth,width:childrenRect.width,height:childrenRect.height});offsets.marginTop=0;offsets.marginLeft=0;if(!isIE10&&isHTML){var marginTop=parseFloat(styles.marginTop);var marginLeft=parseFloat(styles.marginLeft);offsets.top-=borderTopWidth-marginTop;offsets.bottom-=borderTopWidth-marginTop;offsets.left-=borderLeftWidth-marginLeft;offsets.right-=borderLeftWidth-marginLeft;offsets.marginTop=marginTop;offsets.marginLeft=marginLeft}if(isIE10&&!fixedPosition?parent.contains(scrollParent):parent===scrollParent&&scrollParent.nodeName!=="BODY"){offsets=includeScroll(offsets,parent)}return offsets}function getViewportOffsetRectRelativeToArtbitraryNode(element){var excludeScroll=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var html=element.ownerDocument.documentElement;var relativeOffset=getOffsetRectRelativeToArbitraryNode(element,html);var width=Math.max(html.clientWidth,window.innerWidth||0);var height=Math.max(html.clientHeight,window.innerHeight||0);var scrollTop=!excludeScroll?getScroll(html):0;var scrollLeft=!excludeScroll?getScroll(html,"left"):0;var offset={top:scrollTop-relativeOffset.top+relativeOffset.marginTop,left:scrollLeft-relativeOffset.left+relativeOffset.marginLeft,width:width,height:height};return getClientRect(offset)}function isFixed(element){var nodeName=element.nodeName;if(nodeName==="BODY"||nodeName==="HTML"){return false}if(getStyleComputedProperty(element,"position")==="fixed"){return true}var parentNode=getParentNode(element);if(!parentNode){return false}return isFixed(parentNode)}function getFixedPositionOffsetParent(element){if(!element||!element.parentElement||isIE()){return document.documentElement}var el=element.parentElement;while(el&&getStyleComputedProperty(el,"transform")==="none"){el=el.parentElement}return el||document.documentElement}function getBoundaries(popper,reference,padding,boundariesElement){var fixedPosition=arguments.length>4&&arguments[4]!==undefined?arguments[4]:false;var boundaries={top:0,left:0};var offsetParent=fixedPosition?getFixedPositionOffsetParent(popper):findCommonOffsetParent(popper,getReferenceNode(reference));if(boundariesElement==="viewport"){boundaries=getViewportOffsetRectRelativeToArtbitraryNode(offsetParent,fixedPosition)}else{var boundariesNode=void 0;if(boundariesElement==="scrollParent"){boundariesNode=getScrollParent(getParentNode(reference));if(boundariesNode.nodeName==="BODY"){boundariesNode=popper.ownerDocument.documentElement}}else if(boundariesElement==="window"){boundariesNode=popper.ownerDocument.documentElement}else{boundariesNode=boundariesElement}var offsets=getOffsetRectRelativeToArbitraryNode(boundariesNode,offsetParent,fixedPosition);if(boundariesNode.nodeName==="HTML"&&!isFixed(offsetParent)){var _getWindowSizes=getWindowSizes(popper.ownerDocument),height=_getWindowSizes.height,width=_getWindowSizes.width;boundaries.top+=offsets.top-offsets.marginTop;boundaries.bottom=height+offsets.top;boundaries.left+=offsets.left-offsets.marginLeft;boundaries.right=width+offsets.left}else{boundaries=offsets}}padding=padding||0;var isPaddingNumber=typeof padding==="number";boundaries.left+=isPaddingNumber?padding:padding.left||0;boundaries.top+=isPaddingNumber?padding:padding.top||0;boundaries.right-=isPaddingNumber?padding:padding.right||0;boundaries.bottom-=isPaddingNumber?padding:padding.bottom||0;return boundaries}function getArea(_ref){var width=_ref.width,height=_ref.height;return width*height}function computeAutoPlacement(placement,refRect,popper,reference,boundariesElement){var padding=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;if(placement.indexOf("auto")===-1){return placement}var boundaries=getBoundaries(popper,reference,padding,boundariesElement);var rects={top:{width:boundaries.width,height:refRect.top-boundaries.top},right:{width:boundaries.right-refRect.right,height:boundaries.height},bottom:{width:boundaries.width,height:boundaries.bottom-refRect.bottom},left:{width:refRect.left-boundaries.left,height:boundaries.height}};var sortedAreas=Object.keys(rects).map(function(key){return _extends({key:key},rects[key],{area:getArea(rects[key])})}).sort(function(a,b){return b.area-a.area});var filteredAreas=sortedAreas.filter(function(_ref2){var width=_ref2.width,height=_ref2.height;return width>=popper.clientWidth&&height>=popper.clientHeight});var computedPlacement=filteredAreas.length>0?filteredAreas[0].key:sortedAreas[0].key;var variation=placement.split("-")[1];return computedPlacement+(variation?"-"+variation:"")}function getReferenceOffsets(state,popper,reference){var fixedPosition=arguments.length>3&&arguments[3]!==undefined?arguments[3]:null;var commonOffsetParent=fixedPosition?getFixedPositionOffsetParent(popper):findCommonOffsetParent(popper,getReferenceNode(reference));return getOffsetRectRelativeToArbitraryNode(reference,commonOffsetParent,fixedPosition)}function getOuterSizes(element){var window=element.ownerDocument.defaultView;var styles=window.getComputedStyle(element);var x=parseFloat(styles.marginTop||0)+parseFloat(styles.marginBottom||0);var y=parseFloat(styles.marginLeft||0)+parseFloat(styles.marginRight||0);var result={width:element.offsetWidth+y,height:element.offsetHeight+x};return result}function getOppositePlacement(placement){var hash={left:"right",right:"left",bottom:"top",top:"bottom"};return placement.replace(/left|right|bottom|top/g,function(matched){return hash[matched]})}function getPopperOffsets(popper,referenceOffsets,placement){placement=placement.split("-")[0];var popperRect=getOuterSizes(popper);var popperOffsets={width:popperRect.width,height:popperRect.height};var isHoriz=["right","left"].indexOf(placement)!==-1;var mainSide=isHoriz?"top":"left";var secondarySide=isHoriz?"left":"top";var measurement=isHoriz?"height":"width";var secondaryMeasurement=!isHoriz?"height":"width";popperOffsets[mainSide]=referenceOffsets[mainSide]+referenceOffsets[measurement]/2-popperRect[measurement]/2;if(placement===secondarySide){popperOffsets[secondarySide]=referenceOffsets[secondarySide]-popperRect[secondaryMeasurement]}else{popperOffsets[secondarySide]=referenceOffsets[getOppositePlacement(secondarySide)]}return popperOffsets}function find(arr,check){if(Array.prototype.find){return arr.find(check)}return arr.filter(check)[0]}function findIndex(arr,prop,value){if(Array.prototype.findIndex){return arr.findIndex(function(cur){return cur[prop]===value})}var match=find(arr,function(obj){return obj[prop]===value});return arr.indexOf(match)}function runModifiers(modifiers,data,ends){var modifiersToRun=ends===undefined?modifiers:modifiers.slice(0,findIndex(modifiers,"name",ends));modifiersToRun.forEach(function(modifier){if(modifier["function"]){console.warn("`modifier.function` is deprecated, use `modifier.fn`!")}var fn=modifier["function"]||modifier.fn;if(modifier.enabled&&isFunction(fn)){data.offsets.popper=getClientRect(data.offsets.popper);data.offsets.reference=getClientRect(data.offsets.reference);data=fn(data,modifier)}});return data}function update(){if(this.state.isDestroyed){return}var data={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:false,offsets:{}};data.offsets.reference=getReferenceOffsets(this.state,this.popper,this.reference,this.options.positionFixed);data.placement=computeAutoPlacement(this.options.placement,data.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding);data.originalPlacement=data.placement;data.positionFixed=this.options.positionFixed;data.offsets.popper=getPopperOffsets(this.popper,data.offsets.reference,data.placement);data.offsets.popper.position=this.options.positionFixed?"fixed":"absolute";data=runModifiers(this.modifiers,data);if(!this.state.isCreated){this.state.isCreated=true;this.options.onCreate(data)}else{this.options.onUpdate(data)}}function isModifierEnabled(modifiers,modifierName){return modifiers.some(function(_ref){var name=_ref.name,enabled=_ref.enabled;return enabled&&name===modifierName})}function getSupportedPropertyName(property){var prefixes=[false,"ms","Webkit","Moz","O"];var upperProp=property.charAt(0).toUpperCase()+property.slice(1);for(var i=0;i<prefixes.length;i++){var prefix=prefixes[i];var toCheck=prefix?""+prefix+upperProp:property;if(typeof document.body.style[toCheck]!=="undefined"){return toCheck}}return null}function destroy(){this.state.isDestroyed=true;if(isModifierEnabled(this.modifiers,"applyStyle")){this.popper.removeAttribute("x-placement");this.popper.style.position="";this.popper.style.top="";this.popper.style.left="";this.popper.style.right="";this.popper.style.bottom="";this.popper.style.willChange="";this.popper.style[getSupportedPropertyName("transform")]=""}this.disableEventListeners();if(this.options.removeOnDestroy){this.popper.parentNode.removeChild(this.popper)}return this}function getWindow(element){var ownerDocument=element.ownerDocument;return ownerDocument?ownerDocument.defaultView:window}function attachToScrollParents(scrollParent,event,callback,scrollParents){var isBody=scrollParent.nodeName==="BODY";var target=isBody?scrollParent.ownerDocument.defaultView:scrollParent;target.addEventListener(event,callback,{passive:true});if(!isBody){attachToScrollParents(getScrollParent(target.parentNode),event,callback,scrollParents)}scrollParents.push(target)}function setupEventListeners(reference,options,state,updateBound){state.updateBound=updateBound;getWindow(reference).addEventListener("resize",state.updateBound,{passive:true});var scrollElement=getScrollParent(reference);attachToScrollParents(scrollElement,"scroll",state.updateBound,state.scrollParents);state.scrollElement=scrollElement;state.eventsEnabled=true;return state}function enableEventListeners(){if(!this.state.eventsEnabled){this.state=setupEventListeners(this.reference,this.options,this.state,this.scheduleUpdate)}}function removeEventListeners(reference,state){getWindow(reference).removeEventListener("resize",state.updateBound);state.scrollParents.forEach(function(target){target.removeEventListener("scroll",state.updateBound)});state.updateBound=null;state.scrollParents=[];state.scrollElement=null;state.eventsEnabled=false;return state}function disableEventListeners(){if(this.state.eventsEnabled){cancelAnimationFrame(this.scheduleUpdate);this.state=removeEventListeners(this.reference,this.state)}}function isNumeric(n){return n!==""&&!isNaN(parseFloat(n))&&isFinite(n)}function setStyles(element,styles){Object.keys(styles).forEach(function(prop){var unit="";if(["width","height","top","right","bottom","left"].indexOf(prop)!==-1&&isNumeric(styles[prop])){unit="px"}element.style[prop]=styles[prop]+unit})}function setAttributes(element,attributes){Object.keys(attributes).forEach(function(prop){var value=attributes[prop];if(value!==false){element.setAttribute(prop,attributes[prop])}else{element.removeAttribute(prop)}})}function applyStyle(data){setStyles(data.instance.popper,data.styles);setAttributes(data.instance.popper,data.attributes);if(data.arrowElement&&Object.keys(data.arrowStyles).length){setStyles(data.arrowElement,data.arrowStyles)}return data}function applyStyleOnLoad(reference,popper,options,modifierOptions,state){var referenceOffsets=getReferenceOffsets(state,popper,reference,options.positionFixed);var placement=computeAutoPlacement(options.placement,referenceOffsets,popper,reference,options.modifiers.flip.boundariesElement,options.modifiers.flip.padding);popper.setAttribute("x-placement",placement);setStyles(popper,{position:options.positionFixed?"fixed":"absolute"});return options}function getRoundedOffsets(data,shouldRound){var _data$offsets=data.offsets,popper=_data$offsets.popper,reference=_data$offsets.reference;var round=Math.round,floor=Math.floor;var noRound=function noRound(v){return v};var referenceWidth=round(reference.width);var popperWidth=round(popper.width);var isVertical=["left","right"].indexOf(data.placement)!==-1;var isVariation=data.placement.indexOf("-")!==-1;var sameWidthParity=referenceWidth%2===popperWidth%2;var bothOddWidth=referenceWidth%2===1&&popperWidth%2===1;var horizontalToInteger=!shouldRound?noRound:isVertical||isVariation||sameWidthParity?round:floor;var verticalToInteger=!shouldRound?noRound:round;return{left:horizontalToInteger(bothOddWidth&&!isVariation&&shouldRound?popper.left-1:popper.left),top:verticalToInteger(popper.top),bottom:verticalToInteger(popper.bottom),right:horizontalToInteger(popper.right)}}var isFirefox=isBrowser&&/Firefox/i.test(navigator.userAgent);function computeStyle(data,options){var x=options.x,y=options.y;var popper=data.offsets.popper;var legacyGpuAccelerationOption=find(data.instance.modifiers,function(modifier){return modifier.name==="applyStyle"}).gpuAcceleration;if(legacyGpuAccelerationOption!==undefined){console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!")}var gpuAcceleration=legacyGpuAccelerationOption!==undefined?legacyGpuAccelerationOption:options.gpuAcceleration;var offsetParent=getOffsetParent(data.instance.popper);var offsetParentRect=getBoundingClientRect(offsetParent);var styles={position:popper.position};var offsets=getRoundedOffsets(data,window.devicePixelRatio<2||!isFirefox);var sideA=x==="bottom"?"top":"bottom";var sideB=y==="right"?"left":"right";var prefixedProperty=getSupportedPropertyName("transform");var left=void 0,top=void 0;if(sideA==="bottom"){if(offsetParent.nodeName==="HTML"){top=-offsetParent.clientHeight+offsets.bottom}else{top=-offsetParentRect.height+offsets.bottom}}else{top=offsets.top}if(sideB==="right"){if(offsetParent.nodeName==="HTML"){left=-offsetParent.clientWidth+offsets.right}else{left=-offsetParentRect.width+offsets.right}}else{left=offsets.left}if(gpuAcceleration&&prefixedProperty){styles[prefixedProperty]="translate3d("+left+"px, "+top+"px, 0)";styles[sideA]=0;styles[sideB]=0;styles.willChange="transform"}else{var invertTop=sideA==="bottom"?-1:1;var invertLeft=sideB==="right"?-1:1;styles[sideA]=top*invertTop;styles[sideB]=left*invertLeft;styles.willChange=sideA+", "+sideB}var attributes={"x-placement":data.placement};data.attributes=_extends({},attributes,data.attributes);data.styles=_extends({},styles,data.styles);data.arrowStyles=_extends({},data.offsets.arrow,data.arrowStyles);return data}function isModifierRequired(modifiers,requestingName,requestedName){var requesting=find(modifiers,function(_ref){var name=_ref.name;return name===requestingName});var isRequired=!!requesting&&modifiers.some(function(modifier){return modifier.name===requestedName&&modifier.enabled&&modifier.order<requesting.order});if(!isRequired){var _requesting="`"+requestingName+"`";var requested="`"+requestedName+"`";console.warn(requested+" modifier is required by "+_requesting+" modifier in order to work, be sure to include it before "+_requesting+"!")}return isRequired}function arrow(data,options){var _data$offsets$arrow;if(!isModifierRequired(data.instance.modifiers,"arrow","keepTogether")){return data}var arrowElement=options.element;if(typeof arrowElement==="string"){arrowElement=data.instance.popper.querySelector(arrowElement);if(!arrowElement){return data}}else{if(!data.instance.popper.contains(arrowElement)){console.warn("WARNING: `arrow.element` must be child of its popper element!");return data}}var placement=data.placement.split("-")[0];var _data$offsets=data.offsets,popper=_data$offsets.popper,reference=_data$offsets.reference;var isVertical=["left","right"].indexOf(placement)!==-1;var len=isVertical?"height":"width";var sideCapitalized=isVertical?"Top":"Left";var side=sideCapitalized.toLowerCase();var altSide=isVertical?"left":"top";var opSide=isVertical?"bottom":"right";var arrowElementSize=getOuterSizes(arrowElement)[len];if(reference[opSide]-arrowElementSize<popper[side]){data.offsets.popper[side]-=popper[side]-(reference[opSide]-arrowElementSize)}if(reference[side]+arrowElementSize>popper[opSide]){data.offsets.popper[side]+=reference[side]+arrowElementSize-popper[opSide]}data.offsets.popper=getClientRect(data.offsets.popper);var center=reference[side]+reference[len]/2-arrowElementSize/2;var css=getStyleComputedProperty(data.instance.popper);var popperMarginSide=parseFloat(css["margin"+sideCapitalized]);var popperBorderSide=parseFloat(css["border"+sideCapitalized+"Width"]);var sideValue=center-data.offsets.popper[side]-popperMarginSide-popperBorderSide;sideValue=Math.max(Math.min(popper[len]-arrowElementSize,sideValue),0);data.arrowElement=arrowElement;data.offsets.arrow=(_data$offsets$arrow={},defineProperty(_data$offsets$arrow,side,Math.round(sideValue)),defineProperty(_data$offsets$arrow,altSide,""),_data$offsets$arrow);return data}function getOppositeVariation(variation){if(variation==="end"){return"start"}else if(variation==="start"){return"end"}return variation}var placements=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"];var validPlacements=placements.slice(3);function clockwise(placement){var counter=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var index=validPlacements.indexOf(placement);var arr=validPlacements.slice(index+1).concat(validPlacements.slice(0,index));return counter?arr.reverse():arr}var BEHAVIORS={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"};function flip(data,options){if(isModifierEnabled(data.instance.modifiers,"inner")){return data}if(data.flipped&&data.placement===data.originalPlacement){return data}var boundaries=getBoundaries(data.instance.popper,data.instance.reference,options.padding,options.boundariesElement,data.positionFixed);var placement=data.placement.split("-")[0];var placementOpposite=getOppositePlacement(placement);var variation=data.placement.split("-")[1]||"";var flipOrder=[];switch(options.behavior){case BEHAVIORS.FLIP:flipOrder=[placement,placementOpposite];break;case BEHAVIORS.CLOCKWISE:flipOrder=clockwise(placement);break;case BEHAVIORS.COUNTERCLOCKWISE:flipOrder=clockwise(placement,true);break;default:flipOrder=options.behavior}flipOrder.forEach(function(step,index){if(placement!==step||flipOrder.length===index+1){return data}placement=data.placement.split("-")[0];placementOpposite=getOppositePlacement(placement);var popperOffsets=data.offsets.popper;var refOffsets=data.offsets.reference;var floor=Math.floor;var overlapsRef=placement==="left"&&floor(popperOffsets.right)>floor(refOffsets.left)||placement==="right"&&floor(popperOffsets.left)<floor(refOffsets.right)||placement==="top"&&floor(popperOffsets.bottom)>floor(refOffsets.top)||placement==="bottom"&&floor(popperOffsets.top)<floor(refOffsets.bottom);var overflowsLeft=floor(popperOffsets.left)<floor(boundaries.left);var overflowsRight=floor(popperOffsets.right)>floor(boundaries.right);var overflowsTop=floor(popperOffsets.top)<floor(boundaries.top);var overflowsBottom=floor(popperOffsets.bottom)>floor(boundaries.bottom);var overflowsBoundaries=placement==="left"&&overflowsLeft||placement==="right"&&overflowsRight||placement==="top"&&overflowsTop||placement==="bottom"&&overflowsBottom;var isVertical=["top","bottom"].indexOf(placement)!==-1;var flippedVariationByRef=!!options.flipVariations&&(isVertical&&variation==="start"&&overflowsLeft||isVertical&&variation==="end"&&overflowsRight||!isVertical&&variation==="start"&&overflowsTop||!isVertical&&variation==="end"&&overflowsBottom);var flippedVariationByContent=!!options.flipVariationsByContent&&(isVertical&&variation==="start"&&overflowsRight||isVertical&&variation==="end"&&overflowsLeft||!isVertical&&variation==="start"&&overflowsBottom||!isVertical&&variation==="end"&&overflowsTop);var flippedVariation=flippedVariationByRef||flippedVariationByContent;if(overlapsRef||overflowsBoundaries||flippedVariation){data.flipped=true;if(overlapsRef||overflowsBoundaries){placement=flipOrder[index+1]}if(flippedVariation){variation=getOppositeVariation(variation)}data.placement=placement+(variation?"-"+variation:"");data.offsets.popper=_extends({},data.offsets.popper,getPopperOffsets(data.instance.popper,data.offsets.reference,data.placement));data=runModifiers(data.instance.modifiers,data,"flip")}});return data}function keepTogether(data){var _data$offsets=data.offsets,popper=_data$offsets.popper,reference=_data$offsets.reference;var placement=data.placement.split("-")[0];var floor=Math.floor;var isVertical=["top","bottom"].indexOf(placement)!==-1;var side=isVertical?"right":"bottom";var opSide=isVertical?"left":"top";var measurement=isVertical?"width":"height";if(popper[side]<floor(reference[opSide])){data.offsets.popper[opSide]=floor(reference[opSide])-popper[measurement]}if(popper[opSide]>floor(reference[side])){data.offsets.popper[opSide]=floor(reference[side])}return data}function toValue(str,measurement,popperOffsets,referenceOffsets){var split=str.match(/((?:\-|\+)?\d*\.?\d*)(.*)/);var value=+split[1];var unit=split[2];if(!value){return str}if(unit.indexOf("%")===0){var element=void 0;switch(unit){case"%p":element=popperOffsets;break;case"%":case"%r":default:element=referenceOffsets}var rect=getClientRect(element);return rect[measurement]/100*value}else if(unit==="vh"||unit==="vw"){var size=void 0;if(unit==="vh"){size=Math.max(document.documentElement.clientHeight,window.innerHeight||0)}else{size=Math.max(document.documentElement.clientWidth,window.innerWidth||0)}return size/100*value}else{return value}}function parseOffset(offset,popperOffsets,referenceOffsets,basePlacement){var offsets=[0,0];var useHeight=["right","left"].indexOf(basePlacement)!==-1;var fragments=offset.split(/(\+|\-)/).map(function(frag){return frag.trim()});var divider=fragments.indexOf(find(fragments,function(frag){return frag.search(/,|\s/)!==-1}));if(fragments[divider]&&fragments[divider].indexOf(",")===-1){console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.")}var splitRegex=/\s*,\s*|\s+/;var ops=divider!==-1?[fragments.slice(0,divider).concat([fragments[divider].split(splitRegex)[0]]),[fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider+1))]:[fragments];ops=ops.map(function(op,index){var measurement=(index===1?!useHeight:useHeight)?"height":"width";var mergeWithPrevious=false;return op.reduce(function(a,b){if(a[a.length-1]===""&&["+","-"].indexOf(b)!==-1){a[a.length-1]=b;mergeWithPrevious=true;return a}else if(mergeWithPrevious){a[a.length-1]+=b;mergeWithPrevious=false;return a}else{return a.concat(b)}},[]).map(function(str){return toValue(str,measurement,popperOffsets,referenceOffsets)})});ops.forEach(function(op,index){op.forEach(function(frag,index2){if(isNumeric(frag)){offsets[index]+=frag*(op[index2-1]==="-"?-1:1)}})});return offsets}function offset(data,_ref){var offset=_ref.offset;var placement=data.placement,_data$offsets=data.offsets,popper=_data$offsets.popper,reference=_data$offsets.reference;var basePlacement=placement.split("-")[0];var offsets=void 0;if(isNumeric(+offset)){offsets=[+offset,0]}else{offsets=parseOffset(offset,popper,reference,basePlacement)}if(basePlacement==="left"){popper.top+=offsets[0];popper.left-=offsets[1]}else if(basePlacement==="right"){popper.top+=offsets[0];popper.left+=offsets[1]}else if(basePlacement==="top"){popper.left+=offsets[0];popper.top-=offsets[1]}else if(basePlacement==="bottom"){popper.left+=offsets[0];popper.top+=offsets[1]}data.popper=popper;return data}function preventOverflow(data,options){var boundariesElement=options.boundariesElement||getOffsetParent(data.instance.popper);if(data.instance.reference===boundariesElement){boundariesElement=getOffsetParent(boundariesElement)}var transformProp=getSupportedPropertyName("transform");var popperStyles=data.instance.popper.style;var top=popperStyles.top,left=popperStyles.left,transform=popperStyles[transformProp];popperStyles.top="";popperStyles.left="";popperStyles[transformProp]="";var boundaries=getBoundaries(data.instance.popper,data.instance.reference,options.padding,boundariesElement,data.positionFixed);popperStyles.top=top;popperStyles.left=left;popperStyles[transformProp]=transform;options.boundaries=boundaries;var order=options.priority;var popper=data.offsets.popper;var check={primary:function primary(placement){var value=popper[placement];if(popper[placement]<boundaries[placement]&&!options.escapeWithReference){value=Math.max(popper[placement],boundaries[placement])}return defineProperty({},placement,value)},secondary:function secondary(placement){var mainSide=placement==="right"?"left":"top";var value=popper[mainSide];if(popper[placement]>boundaries[placement]&&!options.escapeWithReference){value=Math.min(popper[mainSide],boundaries[placement]-(placement==="right"?popper.width:popper.height))}return defineProperty({},mainSide,value)}};order.forEach(function(placement){var side=["left","top"].indexOf(placement)!==-1?"primary":"secondary";popper=_extends({},popper,check[side](placement))});data.offsets.popper=popper;return data}function shift(data){var placement=data.placement;var basePlacement=placement.split("-")[0];var shiftvariation=placement.split("-")[1];if(shiftvariation){var _data$offsets=data.offsets,reference=_data$offsets.reference,popper=_data$offsets.popper;var isVertical=["bottom","top"].indexOf(basePlacement)!==-1;var side=isVertical?"left":"top";var measurement=isVertical?"width":"height";var shiftOffsets={start:defineProperty({},side,reference[side]),end:defineProperty({},side,reference[side]+reference[measurement]-popper[measurement])};data.offsets.popper=_extends({},popper,shiftOffsets[shiftvariation])}return data}function hide(data){if(!isModifierRequired(data.instance.modifiers,"hide","preventOverflow")){return data}var refRect=data.offsets.reference;var bound=find(data.instance.modifiers,function(modifier){return modifier.name==="preventOverflow"}).boundaries;if(refRect.bottom<bound.top||refRect.left>bound.right||refRect.top>bound.bottom||refRect.right<bound.left){if(data.hide===true){return data}data.hide=true;data.attributes["x-out-of-boundaries"]=""}else{if(data.hide===false){return data}data.hide=false;data.attributes["x-out-of-boundaries"]=false}return data}function inner(data){var placement=data.placement;var basePlacement=placement.split("-")[0];var _data$offsets=data.offsets,popper=_data$offsets.popper,reference=_data$offsets.reference;var isHoriz=["left","right"].indexOf(basePlacement)!==-1;var subtractLength=["top","left"].indexOf(basePlacement)===-1;popper[isHoriz?"left":"top"]=reference[basePlacement]-(subtractLength?popper[isHoriz?"width":"height"]:0);data.placement=getOppositePlacement(placement);data.offsets.popper=getClientRect(popper);return data}var modifiers={shift:{order:100,enabled:true,fn:shift},offset:{order:200,enabled:true,fn:offset,offset:0},preventOverflow:{order:300,enabled:true,fn:preventOverflow,priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:true,fn:keepTogether},arrow:{order:500,enabled:true,fn:arrow,element:"[x-arrow]"},flip:{order:600,enabled:true,fn:flip,behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:false,flipVariationsByContent:false},inner:{order:700,enabled:false,fn:inner},hide:{order:800,enabled:true,fn:hide},computeStyle:{order:850,enabled:true,fn:computeStyle,gpuAcceleration:true,x:"bottom",y:"right"},applyStyle:{order:900,enabled:true,fn:applyStyle,onLoad:applyStyleOnLoad,gpuAcceleration:undefined}};var Defaults={placement:"bottom",positionFixed:false,eventsEnabled:true,removeOnDestroy:false,onCreate:function onCreate(){},onUpdate:function onUpdate(){},modifiers:modifiers};var Popper=function(){function Popper(reference,popper){var _this=this;var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};classCallCheck(this,Popper);this.scheduleUpdate=function(){return requestAnimationFrame(_this.update)};this.update=debounce(this.update.bind(this));this.options=_extends({},Popper.Defaults,options);this.state={isDestroyed:false,isCreated:false,scrollParents:[]};this.reference=reference&&reference.jquery?reference[0]:reference;this.popper=popper&&popper.jquery?popper[0]:popper;this.options.modifiers={};Object.keys(_extends({},Popper.Defaults.modifiers,options.modifiers)).forEach(function(name){_this.options.modifiers[name]=_extends({},Popper.Defaults.modifiers[name]||{},options.modifiers?options.modifiers[name]:{})});this.modifiers=Object.keys(this.options.modifiers).map(function(name){return _extends({name:name},_this.options.modifiers[name])}).sort(function(a,b){return a.order-b.order});this.modifiers.forEach(function(modifierOptions){if(modifierOptions.enabled&&isFunction(modifierOptions.onLoad)){modifierOptions.onLoad(_this.reference,_this.popper,_this.options,modifierOptions,_this.state)}});this.update();var eventsEnabled=this.options.eventsEnabled;if(eventsEnabled){this.enableEventListeners()}this.state.eventsEnabled=eventsEnabled}createClass(Popper,[{key:"update",value:function update$$1(){return update.call(this)}},{key:"destroy",value:function destroy$$1(){return destroy.call(this)}},{key:"enableEventListeners",value:function enableEventListeners$$1(){return enableEventListeners.call(this)}},{key:"disableEventListeners",value:function disableEventListeners$$1(){return disableEventListeners.call(this)}}]);return Popper}();Popper.Utils=(typeof window!=="undefined"?window:global).PopperUtils;Popper.placements=placements;Popper.Defaults=Defaults;var Popper$1=Popper;var NAME$6="dropdown";var VERSION$6="4.6.2";var DATA_KEY$6="bs.dropdown";var EVENT_KEY$6="."+DATA_KEY$6;var DATA_API_KEY$3=".data-api";var JQUERY_NO_CONFLICT$6=$__default["default"].fn[NAME$6];var ESCAPE_KEYCODE$1=27;var SPACE_KEYCODE=32;var TAB_KEYCODE=9;var ARROW_UP_KEYCODE=38;var ARROW_DOWN_KEYCODE=40;var RIGHT_MOUSE_BUTTON_WHICH=3;var REGEXP_KEYDOWN=new RegExp(ARROW_UP_KEYCODE+"|"+ARROW_DOWN_KEYCODE+"|"+ESCAPE_KEYCODE$1);var CLASS_NAME_DISABLED$1="disabled";var CLASS_NAME_SHOW$5="show";var CLASS_NAME_DROPUP="dropup";var CLASS_NAME_DROPRIGHT="dropright";var CLASS_NAME_DROPLEFT="dropleft";var CLASS_NAME_MENURIGHT="dropdown-menu-right";var CLASS_NAME_POSITION_STATIC="position-static";var EVENT_HIDE$3="hide"+EVENT_KEY$6;var EVENT_HIDDEN$3="hidden"+EVENT_KEY$6;var EVENT_SHOW$3="show"+EVENT_KEY$6;var EVENT_SHOWN$3="shown"+EVENT_KEY$6;var EVENT_CLICK="click"+EVENT_KEY$6;var EVENT_CLICK_DATA_API$2="click"+EVENT_KEY$6+DATA_API_KEY$3;var EVENT_KEYDOWN_DATA_API="keydown"+EVENT_KEY$6+DATA_API_KEY$3;var EVENT_KEYUP_DATA_API="keyup"+EVENT_KEY$6+DATA_API_KEY$3;var SELECTOR_DATA_TOGGLE$2='[data-toggle="dropdown"]';var SELECTOR_FORM_CHILD=".dropdown form";var SELECTOR_MENU=".dropdown-menu";var SELECTOR_NAVBAR_NAV=".navbar-nav";var SELECTOR_VISIBLE_ITEMS=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)";var PLACEMENT_TOP="top-start";var PLACEMENT_TOPEND="top-end";var PLACEMENT_BOTTOM="bottom-start";var PLACEMENT_BOTTOMEND="bottom-end";var PLACEMENT_RIGHT="right-start";var PLACEMENT_LEFT="left-start";var Default$5={offset:0,flip:true,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null};var DefaultType$5={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"};var Dropdown=function(){function Dropdown(element,config){this._element=element;this._popper=null;this._config=this._getConfig(config);this._menu=this._getMenuElement();this._inNavbar=this._detectNavbar();this._addEventListeners()}var _proto=Dropdown.prototype;_proto.toggle=function toggle(){if(this._element.disabled||$__default["default"](this._element).hasClass(CLASS_NAME_DISABLED$1)){return}var isActive=$__default["default"](this._menu).hasClass(CLASS_NAME_SHOW$5);Dropdown._clearMenus();if(isActive){return}this.show(true)};_proto.show=function show(usePopper){if(usePopper===void 0){usePopper=false}if(this._element.disabled||$__default["default"](this._element).hasClass(CLASS_NAME_DISABLED$1)||$__default["default"](this._menu).hasClass(CLASS_NAME_SHOW$5)){return}var relatedTarget={relatedTarget:this._element};var showEvent=$__default["default"].Event(EVENT_SHOW$3,relatedTarget);var parent=Dropdown._getParentFromElement(this._element);$__default["default"](parent).trigger(showEvent);if(showEvent.isDefaultPrevented()){return}if(!this._inNavbar&&usePopper){if(typeof Popper$1==="undefined"){throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)")}var referenceElement=this._element;if(this._config.reference==="parent"){referenceElement=parent}else if(Util.isElement(this._config.reference)){referenceElement=this._config.reference;if(typeof this._config.reference.jquery!=="undefined"){referenceElement=this._config.reference[0]}}if(this._config.boundary!=="scrollParent"){$__default["default"](parent).addClass(CLASS_NAME_POSITION_STATIC)}this._popper=new Popper$1(referenceElement,this._menu,this._getPopperConfig())}if("ontouchstart"in document.documentElement&&$__default["default"](parent).closest(SELECTOR_NAVBAR_NAV).length===0){$__default["default"](document.body).children().on("mouseover",null,$__default["default"].noop)}this._element.focus();this._element.setAttribute("aria-expanded",true);$__default["default"](this._menu).toggleClass(CLASS_NAME_SHOW$5);$__default["default"](parent).toggleClass(CLASS_NAME_SHOW$5).trigger($__default["default"].Event(EVENT_SHOWN$3,relatedTarget))};_proto.hide=function hide(){if(this._element.disabled||$__default["default"](this._element).hasClass(CLASS_NAME_DISABLED$1)||!$__default["default"](this._menu).hasClass(CLASS_NAME_SHOW$5)){return}var relatedTarget={relatedTarget:this._element};var hideEvent=$__default["default"].Event(EVENT_HIDE$3,relatedTarget);var parent=Dropdown._getParentFromElement(this._element);$__default["default"](parent).trigger(hideEvent);if(hideEvent.isDefaultPrevented()){return}if(this._popper){this._popper.destroy()}$__default["default"](this._menu).toggleClass(CLASS_NAME_SHOW$5);$__default["default"](parent).toggleClass(CLASS_NAME_SHOW$5).trigger($__default["default"].Event(EVENT_HIDDEN$3,relatedTarget))};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$6);$__default["default"](this._element).off(EVENT_KEY$6);this._element=null;this._menu=null;if(this._popper!==null){this._popper.destroy();this._popper=null}};_proto.update=function update(){this._inNavbar=this._detectNavbar();if(this._popper!==null){this._popper.scheduleUpdate()}};_proto._addEventListeners=function _addEventListeners(){var _this=this;$__default["default"](this._element).on(EVENT_CLICK,function(event){event.preventDefault();event.stopPropagation();_this.toggle()})};_proto._getConfig=function _getConfig(config){config=_extends$1({},this.constructor.Default,$__default["default"](this._element).data(),config);Util.typeCheckConfig(NAME$6,config,this.constructor.DefaultType);return config};_proto._getMenuElement=function _getMenuElement(){if(!this._menu){var parent=Dropdown._getParentFromElement(this._element);if(parent){this._menu=parent.querySelector(SELECTOR_MENU)}}return this._menu};_proto._getPlacement=function _getPlacement(){var $parentDropdown=$__default["default"](this._element.parentNode);var placement=PLACEMENT_BOTTOM;if($parentDropdown.hasClass(CLASS_NAME_DROPUP)){placement=$__default["default"](this._menu).hasClass(CLASS_NAME_MENURIGHT)?PLACEMENT_TOPEND:PLACEMENT_TOP}else if($parentDropdown.hasClass(CLASS_NAME_DROPRIGHT)){placement=PLACEMENT_RIGHT}else if($parentDropdown.hasClass(CLASS_NAME_DROPLEFT)){placement=PLACEMENT_LEFT}else if($__default["default"](this._menu).hasClass(CLASS_NAME_MENURIGHT)){placement=PLACEMENT_BOTTOMEND}return placement};_proto._detectNavbar=function _detectNavbar(){return $__default["default"](this._element).closest(".navbar").length>0};_proto._getOffset=function _getOffset(){var _this2=this;var offset={};if(typeof this._config.offset==="function"){offset.fn=function(data){data.offsets=_extends$1({},data.offsets,_this2._config.offset(data.offsets,_this2._element));return data}}else{offset.offset=this._config.offset}return offset};_proto._getPopperConfig=function _getPopperConfig(){var popperConfig={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};if(this._config.display==="static"){popperConfig.modifiers.applyStyle={enabled:false}}return _extends$1({},popperConfig,this._config.popperConfig)};Dropdown._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var data=$__default["default"](this).data(DATA_KEY$6);var _config=typeof config==="object"?config:null;if(!data){data=new Dropdown(this,_config);$__default["default"](this).data(DATA_KEY$6,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};Dropdown._clearMenus=function _clearMenus(event){if(event&&(event.which===RIGHT_MOUSE_BUTTON_WHICH||event.type==="keyup"&&event.which!==TAB_KEYCODE)){return}var toggles=[].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE$2));for(var i=0,len=toggles.length;i<len;i++){var parent=Dropdown._getParentFromElement(toggles[i]);var context=$__default["default"](toggles[i]).data(DATA_KEY$6);var relatedTarget={relatedTarget:toggles[i]};if(event&&event.type==="click"){relatedTarget.clickEvent=event}if(!context){continue}var dropdownMenu=context._menu;if(!$__default["default"](parent).hasClass(CLASS_NAME_SHOW$5)){continue}if(event&&(event.type==="click"&&/input|textarea/i.test(event.target.tagName)||event.type==="keyup"&&event.which===TAB_KEYCODE)&&$__default["default"].contains(parent,event.target)){continue}var hideEvent=$__default["default"].Event(EVENT_HIDE$3,relatedTarget);$__default["default"](parent).trigger(hideEvent);if(hideEvent.isDefaultPrevented()){continue}if("ontouchstart"in document.documentElement){$__default["default"](document.body).children().off("mouseover",null,$__default["default"].noop)}toggles[i].setAttribute("aria-expanded","false");if(context._popper){context._popper.destroy()}$__default["default"](dropdownMenu).removeClass(CLASS_NAME_SHOW$5);$__default["default"](parent).removeClass(CLASS_NAME_SHOW$5).trigger($__default["default"].Event(EVENT_HIDDEN$3,relatedTarget))}};Dropdown._getParentFromElement=function _getParentFromElement(element){var parent;var selector=Util.getSelectorFromElement(element);if(selector){parent=document.querySelector(selector)}return parent||element.parentNode};Dropdown._dataApiKeydownHandler=function _dataApiKeydownHandler(event){if(/input|textarea/i.test(event.target.tagName)?event.which===SPACE_KEYCODE||event.which!==ESCAPE_KEYCODE$1&&(event.which!==ARROW_DOWN_KEYCODE&&event.which!==ARROW_UP_KEYCODE||$__default["default"](event.target).closest(SELECTOR_MENU).length):!REGEXP_KEYDOWN.test(event.which)){return}if(this.disabled||$__default["default"](this).hasClass(CLASS_NAME_DISABLED$1)){return}var parent=Dropdown._getParentFromElement(this);var isActive=$__default["default"](parent).hasClass(CLASS_NAME_SHOW$5);if(!isActive&&event.which===ESCAPE_KEYCODE$1){return}event.preventDefault();event.stopPropagation();if(!isActive||event.which===ESCAPE_KEYCODE$1||event.which===SPACE_KEYCODE){if(event.which===ESCAPE_KEYCODE$1){$__default["default"](parent.querySelector(SELECTOR_DATA_TOGGLE$2)).trigger("focus")}$__default["default"](this).trigger("click");return}var items=[].slice.call(parent.querySelectorAll(SELECTOR_VISIBLE_ITEMS)).filter(function(item){return $__default["default"](item).is(":visible")});if(items.length===0){return}var index=items.indexOf(event.target);if(event.which===ARROW_UP_KEYCODE&&index>0){index--}if(event.which===ARROW_DOWN_KEYCODE&&index<items.length-1){index++}if(index<0){index=0}items[index].focus()};_createClass(Dropdown,null,[{key:"VERSION",get:function get(){return VERSION$6}},{key:"Default",get:function get(){return Default$5}},{key:"DefaultType",get:function get(){return DefaultType$5}}]);return Dropdown}();$__default["default"](document).on(EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE$2,Dropdown._dataApiKeydownHandler).on(EVENT_KEYDOWN_DATA_API,SELECTOR_MENU,Dropdown._dataApiKeydownHandler).on(EVENT_CLICK_DATA_API$2+" "+EVENT_KEYUP_DATA_API,Dropdown._clearMenus).on(EVENT_CLICK_DATA_API$2,SELECTOR_DATA_TOGGLE$2,function(event){event.preventDefault();event.stopPropagation();Dropdown._jQueryInterface.call($__default["default"](this),"toggle")}).on(EVENT_CLICK_DATA_API$2,SELECTOR_FORM_CHILD,function(e){e.stopPropagation()});$__default["default"].fn[NAME$6]=Dropdown._jQueryInterface;$__default["default"].fn[NAME$6].Constructor=Dropdown;$__default["default"].fn[NAME$6].noConflict=function(){$__default["default"].fn[NAME$6]=JQUERY_NO_CONFLICT$6;return Dropdown._jQueryInterface};var NAME$5="modal";var VERSION$5="4.6.2";var DATA_KEY$5="bs.modal";var EVENT_KEY$5="."+DATA_KEY$5;var DATA_API_KEY$2=".data-api";var JQUERY_NO_CONFLICT$5=$__default["default"].fn[NAME$5];var ESCAPE_KEYCODE=27;var CLASS_NAME_SCROLLABLE="modal-dialog-scrollable";var CLASS_NAME_SCROLLBAR_MEASURER="modal-scrollbar-measure";var CLASS_NAME_BACKDROP="modal-backdrop";var CLASS_NAME_OPEN="modal-open";var CLASS_NAME_FADE$4="fade";var CLASS_NAME_SHOW$4="show";var CLASS_NAME_STATIC="modal-static";var EVENT_HIDE$2="hide"+EVENT_KEY$5;var EVENT_HIDE_PREVENTED="hidePrevented"+EVENT_KEY$5;var EVENT_HIDDEN$2="hidden"+EVENT_KEY$5;var EVENT_SHOW$2="show"+EVENT_KEY$5;var EVENT_SHOWN$2="shown"+EVENT_KEY$5;var EVENT_FOCUSIN="focusin"+EVENT_KEY$5;var EVENT_RESIZE="resize"+EVENT_KEY$5;var EVENT_CLICK_DISMISS$1="click.dismiss"+EVENT_KEY$5;var EVENT_KEYDOWN_DISMISS="keydown.dismiss"+EVENT_KEY$5;var EVENT_MOUSEUP_DISMISS="mouseup.dismiss"+EVENT_KEY$5;var EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss"+EVENT_KEY$5;var EVENT_CLICK_DATA_API$1="click"+EVENT_KEY$5+DATA_API_KEY$2;var SELECTOR_DIALOG=".modal-dialog";var SELECTOR_MODAL_BODY=".modal-body";var SELECTOR_DATA_TOGGLE$1='[data-toggle="modal"]';var SELECTOR_DATA_DISMISS$1='[data-dismiss="modal"]';var SELECTOR_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top";var SELECTOR_STICKY_CONTENT=".sticky-top";var Default$4={backdrop:true,keyboard:true,focus:true,show:true};var DefaultType$4={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"};var Modal=function(){function Modal(element,config){this._config=this._getConfig(config);this._element=element;this._dialog=element.querySelector(SELECTOR_DIALOG);this._backdrop=null;this._isShown=false;this._isBodyOverflowing=false;this._ignoreBackdropClick=false;this._isTransitioning=false;this._scrollbarWidth=0}var _proto=Modal.prototype;_proto.toggle=function toggle(relatedTarget){return this._isShown?this.hide():this.show(relatedTarget)};_proto.show=function show(relatedTarget){var _this=this;if(this._isShown||this._isTransitioning){return}var showEvent=$__default["default"].Event(EVENT_SHOW$2,{relatedTarget:relatedTarget});$__default["default"](this._element).trigger(showEvent);if(showEvent.isDefaultPrevented()){return}this._isShown=true;if($__default["default"](this._element).hasClass(CLASS_NAME_FADE$4)){this._isTransitioning=true}this._checkScrollbar();this._setScrollbar();this._adjustDialog();this._setEscapeEvent();this._setResizeEvent();$__default["default"](this._element).on(EVENT_CLICK_DISMISS$1,SELECTOR_DATA_DISMISS$1,function(event){return _this.hide(event)});$__default["default"](this._dialog).on(EVENT_MOUSEDOWN_DISMISS,function(){$__default["default"](_this._element).one(EVENT_MOUSEUP_DISMISS,function(event){if($__default["default"](event.target).is(_this._element)){_this._ignoreBackdropClick=true}})});this._showBackdrop(function(){return _this._showElement(relatedTarget)})};_proto.hide=function hide(event){var _this2=this;if(event){event.preventDefault()}if(!this._isShown||this._isTransitioning){return}var hideEvent=$__default["default"].Event(EVENT_HIDE$2);$__default["default"](this._element).trigger(hideEvent);if(!this._isShown||hideEvent.isDefaultPrevented()){return}this._isShown=false;var transition=$__default["default"](this._element).hasClass(CLASS_NAME_FADE$4);if(transition){this._isTransitioning=true}this._setEscapeEvent();this._setResizeEvent();$__default["default"](document).off(EVENT_FOCUSIN);$__default["default"](this._element).removeClass(CLASS_NAME_SHOW$4);$__default["default"](this._element).off(EVENT_CLICK_DISMISS$1);$__default["default"](this._dialog).off(EVENT_MOUSEDOWN_DISMISS);if(transition){var transitionDuration=Util.getTransitionDurationFromElement(this._element);$__default["default"](this._element).one(Util.TRANSITION_END,function(event){return _this2._hideModal(event)}).emulateTransitionEnd(transitionDuration)}else{this._hideModal()}};_proto.dispose=function dispose(){[window,this._element,this._dialog].forEach(function(htmlElement){return $__default["default"](htmlElement).off(EVENT_KEY$5)});$__default["default"](document).off(EVENT_FOCUSIN);$__default["default"].removeData(this._element,DATA_KEY$5);this._config=null;this._element=null;this._dialog=null;this._backdrop=null;this._isShown=null;this._isBodyOverflowing=null;this._ignoreBackdropClick=null;this._isTransitioning=null;this._scrollbarWidth=null};_proto.handleUpdate=function handleUpdate(){this._adjustDialog()};_proto._getConfig=function _getConfig(config){config=_extends$1({},Default$4,config);Util.typeCheckConfig(NAME$5,config,DefaultType$4);return config};_proto._triggerBackdropTransition=function _triggerBackdropTransition(){var _this3=this;var hideEventPrevented=$__default["default"].Event(EVENT_HIDE_PREVENTED);$__default["default"](this._element).trigger(hideEventPrevented);if(hideEventPrevented.isDefaultPrevented()){return}var isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight;if(!isModalOverflowing){this._element.style.overflowY="hidden"}this._element.classList.add(CLASS_NAME_STATIC);var modalTransitionDuration=Util.getTransitionDurationFromElement(this._dialog);$__default["default"](this._element).off(Util.TRANSITION_END);$__default["default"](this._element).one(Util.TRANSITION_END,function(){_this3._element.classList.remove(CLASS_NAME_STATIC);if(!isModalOverflowing){$__default["default"](_this3._element).one(Util.TRANSITION_END,function(){_this3._element.style.overflowY=""}).emulateTransitionEnd(_this3._element,modalTransitionDuration)}}).emulateTransitionEnd(modalTransitionDuration);this._element.focus()};_proto._showElement=function _showElement(relatedTarget){var _this4=this;var transition=$__default["default"](this._element).hasClass(CLASS_NAME_FADE$4);var modalBody=this._dialog?this._dialog.querySelector(SELECTOR_MODAL_BODY):null;if(!this._element.parentNode||this._element.parentNode.nodeType!==Node.ELEMENT_NODE){document.body.appendChild(this._element)}this._element.style.display="block";this._element.removeAttribute("aria-hidden");this._element.setAttribute("aria-modal",true);this._element.setAttribute("role","dialog");if($__default["default"](this._dialog).hasClass(CLASS_NAME_SCROLLABLE)&&modalBody){modalBody.scrollTop=0}else{this._element.scrollTop=0}if(transition){Util.reflow(this._element)}$__default["default"](this._element).addClass(CLASS_NAME_SHOW$4);if(this._config.focus){this._enforceFocus()}var shownEvent=$__default["default"].Event(EVENT_SHOWN$2,{relatedTarget:relatedTarget});var transitionComplete=function transitionComplete(){if(_this4._config.focus){_this4._element.focus()}_this4._isTransitioning=false;$__default["default"](_this4._element).trigger(shownEvent)};if(transition){var transitionDuration=Util.getTransitionDurationFromElement(this._dialog);$__default["default"](this._dialog).one(Util.TRANSITION_END,transitionComplete).emulateTransitionEnd(transitionDuration)}else{transitionComplete()}};_proto._enforceFocus=function _enforceFocus(){var _this5=this;$__default["default"](document).off(EVENT_FOCUSIN).on(EVENT_FOCUSIN,function(event){if(document!==event.target&&_this5._element!==event.target&&$__default["default"](_this5._element).has(event.target).length===0){_this5._element.focus()}})};_proto._setEscapeEvent=function _setEscapeEvent(){var _this6=this;if(this._isShown){$__default["default"](this._element).on(EVENT_KEYDOWN_DISMISS,function(event){if(_this6._config.keyboard&&event.which===ESCAPE_KEYCODE){event.preventDefault();_this6.hide()}else if(!_this6._config.keyboard&&event.which===ESCAPE_KEYCODE){_this6._triggerBackdropTransition()}})}else if(!this._isShown){$__default["default"](this._element).off(EVENT_KEYDOWN_DISMISS)}};_proto._setResizeEvent=function _setResizeEvent(){var _this7=this;if(this._isShown){$__default["default"](window).on(EVENT_RESIZE,function(event){return _this7.handleUpdate(event)})}else{$__default["default"](window).off(EVENT_RESIZE)}};_proto._hideModal=function _hideModal(){var _this8=this;this._element.style.display="none";this._element.setAttribute("aria-hidden",true);this._element.removeAttribute("aria-modal");this._element.removeAttribute("role");this._isTransitioning=false;this._showBackdrop(function(){$__default["default"](document.body).removeClass(CLASS_NAME_OPEN);_this8._resetAdjustments();_this8._resetScrollbar();$__default["default"](_this8._element).trigger(EVENT_HIDDEN$2)})};_proto._removeBackdrop=function _removeBackdrop(){if(this._backdrop){$__default["default"](this._backdrop).remove();this._backdrop=null}};_proto._showBackdrop=function _showBackdrop(callback){var _this9=this;var animate=$__default["default"](this._element).hasClass(CLASS_NAME_FADE$4)?CLASS_NAME_FADE$4:"";if(this._isShown&&this._config.backdrop){this._backdrop=document.createElement("div");this._backdrop.className=CLASS_NAME_BACKDROP;if(animate){this._backdrop.classList.add(animate)}$__default["default"](this._backdrop).appendTo(document.body);$__default["default"](this._element).on(EVENT_CLICK_DISMISS$1,function(event){if(_this9._ignoreBackdropClick){_this9._ignoreBackdropClick=false;return}if(event.target!==event.currentTarget){return}if(_this9._config.backdrop==="static"){_this9._triggerBackdropTransition()}else{_this9.hide()}});if(animate){Util.reflow(this._backdrop)}$__default["default"](this._backdrop).addClass(CLASS_NAME_SHOW$4);if(!callback){return}if(!animate){callback();return}var backdropTransitionDuration=Util.getTransitionDurationFromElement(this._backdrop);$__default["default"](this._backdrop).one(Util.TRANSITION_END,callback).emulateTransitionEnd(backdropTransitionDuration)}else if(!this._isShown&&this._backdrop){$__default["default"](this._backdrop).removeClass(CLASS_NAME_SHOW$4);var callbackRemove=function callbackRemove(){_this9._removeBackdrop();if(callback){callback()}};if($__default["default"](this._element).hasClass(CLASS_NAME_FADE$4)){var _backdropTransitionDuration=Util.getTransitionDurationFromElement(this._backdrop);$__default["default"](this._backdrop).one(Util.TRANSITION_END,callbackRemove).emulateTransitionEnd(_backdropTransitionDuration)}else{callbackRemove()}}else if(callback){callback()}};_proto._adjustDialog=function _adjustDialog(){var isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight;if(!this._isBodyOverflowing&&isModalOverflowing){this._element.style.paddingLeft=this._scrollbarWidth+"px"}if(this._isBodyOverflowing&&!isModalOverflowing){this._element.style.paddingRight=this._scrollbarWidth+"px"}};_proto._resetAdjustments=function _resetAdjustments(){this._element.style.paddingLeft="";this._element.style.paddingRight=""};_proto._checkScrollbar=function _checkScrollbar(){var rect=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(rect.left+rect.right)<window.innerWidth;this._scrollbarWidth=this._getScrollbarWidth()};_proto._setScrollbar=function _setScrollbar(){var _this10=this;if(this._isBodyOverflowing){var fixedContent=[].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT));var stickyContent=[].slice.call(document.querySelectorAll(SELECTOR_STICKY_CONTENT));$__default["default"](fixedContent).each(function(index,element){var actualPadding=element.style.paddingRight;var calculatedPadding=$__default["default"](element).css("padding-right");$__default["default"](element).data("padding-right",actualPadding).css("padding-right",parseFloat(calculatedPadding)+_this10._scrollbarWidth+"px")});$__default["default"](stickyContent).each(function(index,element){var actualMargin=element.style.marginRight;var calculatedMargin=$__default["default"](element).css("margin-right");$__default["default"](element).data("margin-right",actualMargin).css("margin-right",parseFloat(calculatedMargin)-_this10._scrollbarWidth+"px")});var actualPadding=document.body.style.paddingRight;var calculatedPadding=$__default["default"](document.body).css("padding-right");$__default["default"](document.body).data("padding-right",actualPadding).css("padding-right",parseFloat(calculatedPadding)+this._scrollbarWidth+"px")}$__default["default"](document.body).addClass(CLASS_NAME_OPEN)};_proto._resetScrollbar=function _resetScrollbar(){var fixedContent=[].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT));$__default["default"](fixedContent).each(function(index,element){var padding=$__default["default"](element).data("padding-right");$__default["default"](element).removeData("padding-right");element.style.paddingRight=padding?padding:""});var elements=[].slice.call(document.querySelectorAll(""+SELECTOR_STICKY_CONTENT));$__default["default"](elements).each(function(index,element){var margin=$__default["default"](element).data("margin-right");if(typeof margin!=="undefined"){$__default["default"](element).css("margin-right",margin).removeData("margin-right")}});var padding=$__default["default"](document.body).data("padding-right");$__default["default"](document.body).removeData("padding-right");document.body.style.paddingRight=padding?padding:""};_proto._getScrollbarWidth=function _getScrollbarWidth(){var scrollDiv=document.createElement("div");scrollDiv.className=CLASS_NAME_SCROLLBAR_MEASURER;document.body.appendChild(scrollDiv);var scrollbarWidth=scrollDiv.getBoundingClientRect().width-scrollDiv.clientWidth;document.body.removeChild(scrollDiv);return scrollbarWidth};Modal._jQueryInterface=function _jQueryInterface(config,relatedTarget){return this.each(function(){var data=$__default["default"](this).data(DATA_KEY$5);var _config=_extends$1({},Default$4,$__default["default"](this).data(),typeof config==="object"&&config?config:{});if(!data){data=new Modal(this,_config);$__default["default"](this).data(DATA_KEY$5,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config](relatedTarget)}else if(_config.show){data.show(relatedTarget)}})};_createClass(Modal,null,[{key:"VERSION",get:function get(){return VERSION$5}},{key:"Default",get:function get(){return Default$4}}]);return Modal}();$__default["default"](document).on(EVENT_CLICK_DATA_API$1,SELECTOR_DATA_TOGGLE$1,function(event){var _this11=this;var target;var selector=Util.getSelectorFromElement(this);if(selector){target=document.querySelector(selector)}var config=$__default["default"](target).data(DATA_KEY$5)?"toggle":_extends$1({},$__default["default"](target).data(),$__default["default"](this).data());if(this.tagName==="A"||this.tagName==="AREA"){event.preventDefault()}var $target=$__default["default"](target).one(EVENT_SHOW$2,function(showEvent){if(showEvent.isDefaultPrevented()){return}$target.one(EVENT_HIDDEN$2,function(){if($__default["default"](_this11).is(":visible")){_this11.focus()}})});Modal._jQueryInterface.call($__default["default"](target),config,this)});$__default["default"].fn[NAME$5]=Modal._jQueryInterface;$__default["default"].fn[NAME$5].Constructor=Modal;$__default["default"].fn[NAME$5].noConflict=function(){$__default["default"].fn[NAME$5]=JQUERY_NO_CONFLICT$5;return Modal._jQueryInterface};var uriAttrs=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"];var ARIA_ATTRIBUTE_PATTERN=/^aria-[\w-]*$/i;var DefaultWhitelist={"*":["class","dir","id","lang","role",ARIA_ATTRIBUTE_PATTERN],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};var SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i;var DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function allowedAttribute(attr,allowedAttributeList){var attrName=attr.nodeName.toLowerCase();if(allowedAttributeList.indexOf(attrName)!==-1){if(uriAttrs.indexOf(attrName)!==-1){return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue)||DATA_URL_PATTERN.test(attr.nodeValue))}return true}var regExp=allowedAttributeList.filter(function(attrRegex){return attrRegex instanceof RegExp});for(var i=0,len=regExp.length;i<len;i++){if(regExp[i].test(attrName)){return true}}return false}function sanitizeHtml(unsafeHtml,whiteList,sanitizeFn){if(unsafeHtml.length===0){return unsafeHtml}if(sanitizeFn&&typeof sanitizeFn==="function"){return sanitizeFn(unsafeHtml)}var domParser=new window.DOMParser;var createdDocument=domParser.parseFromString(unsafeHtml,"text/html");var whitelistKeys=Object.keys(whiteList);var elements=[].slice.call(createdDocument.body.querySelectorAll("*"));var _loop=function _loop(i,len){var el=elements[i];var elName=el.nodeName.toLowerCase();if(whitelistKeys.indexOf(el.nodeName.toLowerCase())===-1){el.parentNode.removeChild(el);return"continue"}var attributeList=[].slice.call(el.attributes);var whitelistedAttributes=[].concat(whiteList["*"]||[],whiteList[elName]||[]);attributeList.forEach(function(attr){if(!allowedAttribute(attr,whitelistedAttributes)){el.removeAttribute(attr.nodeName)}})};for(var i=0,len=elements.length;i<len;i++){var _ret=_loop(i);if(_ret==="continue")continue}return createdDocument.body.innerHTML}var NAME$4="tooltip";var VERSION$4="4.6.2";var DATA_KEY$4="bs.tooltip";var EVENT_KEY$4="."+DATA_KEY$4;var JQUERY_NO_CONFLICT$4=$__default["default"].fn[NAME$4];var CLASS_PREFIX$1="bs-tooltip";var BSCLS_PREFIX_REGEX$1=new RegExp("(^|\\s)"+CLASS_PREFIX$1+"\\S+","g");var DISALLOWED_ATTRIBUTES=["sanitize","whiteList","sanitizeFn"];var CLASS_NAME_FADE$3="fade";var CLASS_NAME_SHOW$3="show";var HOVER_STATE_SHOW="show";var HOVER_STATE_OUT="out";var SELECTOR_TOOLTIP_INNER=".tooltip-inner";var SELECTOR_ARROW=".arrow";var TRIGGER_HOVER="hover";var TRIGGER_FOCUS="focus";var TRIGGER_CLICK="click";var TRIGGER_MANUAL="manual";var AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"};var Default$3={animation:true,template:'<div class="tooltip" role="tooltip">'+'<div class="arrow"></div>'+'<div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:false,selector:false,placement:"top",offset:0,container:false,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:true,sanitizeFn:null,whiteList:DefaultWhitelist,popperConfig:null};var DefaultType$3={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"};var Event$1={HIDE:"hide"+EVENT_KEY$4,HIDDEN:"hidden"+EVENT_KEY$4,SHOW:"show"+EVENT_KEY$4,SHOWN:"shown"+EVENT_KEY$4,INSERTED:"inserted"+EVENT_KEY$4,CLICK:"click"+EVENT_KEY$4,FOCUSIN:"focusin"+EVENT_KEY$4,FOCUSOUT:"focusout"+EVENT_KEY$4,MOUSEENTER:"mouseenter"+EVENT_KEY$4,MOUSELEAVE:"mouseleave"+EVENT_KEY$4};var Tooltip=function(){function Tooltip(element,config){if(typeof Popper$1==="undefined"){throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)")}this._isEnabled=true;this._timeout=0;this._hoverState="";this._activeTrigger={};this._popper=null;this.element=element;this.config=this._getConfig(config);this.tip=null;this._setListeners()}var _proto=Tooltip.prototype;_proto.enable=function enable(){this._isEnabled=true};_proto.disable=function disable(){this._isEnabled=false};_proto.toggleEnabled=function toggleEnabled(){this._isEnabled=!this._isEnabled};_proto.toggle=function toggle(event){if(!this._isEnabled){return}if(event){var dataKey=this.constructor.DATA_KEY;var context=$__default["default"](event.currentTarget).data(dataKey);if(!context){context=new this.constructor(event.currentTarget,this._getDelegateConfig());$__default["default"](event.currentTarget).data(dataKey,context)}context._activeTrigger.click=!context._activeTrigger.click;if(context._isWithActiveTrigger()){context._enter(null,context)}else{context._leave(null,context)}}else{if($__default["default"](this.getTipElement()).hasClass(CLASS_NAME_SHOW$3)){this._leave(null,this);return}this._enter(null,this)}};_proto.dispose=function dispose(){clearTimeout(this._timeout);$__default["default"].removeData(this.element,this.constructor.DATA_KEY);$__default["default"](this.element).off(this.constructor.EVENT_KEY);$__default["default"](this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler);if(this.tip){$__default["default"](this.tip).remove()}this._isEnabled=null;this._timeout=null;this._hoverState=null;this._activeTrigger=null;if(this._popper){this._popper.destroy()}this._popper=null;this.element=null;this.config=null;this.tip=null};_proto.show=function show(){var _this=this;if($__default["default"](this.element).css("display")==="none"){throw new Error("Please use show on visible elements")}var showEvent=$__default["default"].Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){$__default["default"](this.element).trigger(showEvent);var shadowRoot=Util.findShadowRoot(this.element);var isInTheDom=$__default["default"].contains(shadowRoot!==null?shadowRoot:this.element.ownerDocument.documentElement,this.element);if(showEvent.isDefaultPrevented()||!isInTheDom){return}var tip=this.getTipElement();var tipId=Util.getUID(this.constructor.NAME);tip.setAttribute("id",tipId);this.element.setAttribute("aria-describedby",tipId);this.setContent();if(this.config.animation){$__default["default"](tip).addClass(CLASS_NAME_FADE$3)}var placement=typeof this.config.placement==="function"?this.config.placement.call(this,tip,this.element):this.config.placement;var attachment=this._getAttachment(placement);this.addAttachmentClass(attachment);var container=this._getContainer();$__default["default"](tip).data(this.constructor.DATA_KEY,this);if(!$__default["default"].contains(this.element.ownerDocument.documentElement,this.tip)){$__default["default"](tip).appendTo(container)}$__default["default"](this.element).trigger(this.constructor.Event.INSERTED);this._popper=new Popper$1(this.element,tip,this._getPopperConfig(attachment));$__default["default"](tip).addClass(CLASS_NAME_SHOW$3);$__default["default"](tip).addClass(this.config.customClass);if("ontouchstart"in document.documentElement){$__default["default"](document.body).children().on("mouseover",null,$__default["default"].noop)}var complete=function complete(){if(_this.config.animation){_this._fixTransition()}var prevHoverState=_this._hoverState;_this._hoverState=null;$__default["default"](_this.element).trigger(_this.constructor.Event.SHOWN);if(prevHoverState===HOVER_STATE_OUT){_this._leave(null,_this)}};if($__default["default"](this.tip).hasClass(CLASS_NAME_FADE$3)){var transitionDuration=Util.getTransitionDurationFromElement(this.tip);$__default["default"](this.tip).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else{complete()}}};_proto.hide=function hide(callback){var _this2=this;var tip=this.getTipElement();var hideEvent=$__default["default"].Event(this.constructor.Event.HIDE);var complete=function complete(){if(_this2._hoverState!==HOVER_STATE_SHOW&&tip.parentNode){tip.parentNode.removeChild(tip)}_this2._cleanTipClass();_this2.element.removeAttribute("aria-describedby");$__default["default"](_this2.element).trigger(_this2.constructor.Event.HIDDEN);if(_this2._popper!==null){_this2._popper.destroy()}if(callback){callback()}};$__default["default"](this.element).trigger(hideEvent);if(hideEvent.isDefaultPrevented()){return}$__default["default"](tip).removeClass(CLASS_NAME_SHOW$3);if("ontouchstart"in document.documentElement){$__default["default"](document.body).children().off("mouseover",null,$__default["default"].noop)}this._activeTrigger[TRIGGER_CLICK]=false;this._activeTrigger[TRIGGER_FOCUS]=false;this._activeTrigger[TRIGGER_HOVER]=false;if($__default["default"](this.tip).hasClass(CLASS_NAME_FADE$3)){var transitionDuration=Util.getTransitionDurationFromElement(tip);$__default["default"](tip).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else{complete()}this._hoverState=""};_proto.update=function update(){if(this._popper!==null){this._popper.scheduleUpdate()}};_proto.isWithContent=function isWithContent(){return Boolean(this.getTitle())};_proto.addAttachmentClass=function addAttachmentClass(attachment){$__default["default"](this.getTipElement()).addClass(CLASS_PREFIX$1+"-"+attachment)};_proto.getTipElement=function getTipElement(){this.tip=this.tip||$__default["default"](this.config.template)[0];return this.tip};_proto.setContent=function setContent(){var tip=this.getTipElement();this.setElementContent($__default["default"](tip.querySelectorAll(SELECTOR_TOOLTIP_INNER)),this.getTitle());$__default["default"](tip).removeClass(CLASS_NAME_FADE$3+" "+CLASS_NAME_SHOW$3)};_proto.setElementContent=function setElementContent($element,content){if(typeof content==="object"&&(content.nodeType||content.jquery)){if(this.config.html){if(!$__default["default"](content).parent().is($element)){$element.empty().append(content)}}else{$element.text($__default["default"](content).text())}return}if(this.config.html){if(this.config.sanitize){content=sanitizeHtml(content,this.config.whiteList,this.config.sanitizeFn)}$element.html(content)}else{$element.text(content)}};_proto.getTitle=function getTitle(){var title=this.element.getAttribute("data-original-title");if(!title){title=typeof this.config.title==="function"?this.config.title.call(this.element):this.config.title}return title};_proto._getPopperConfig=function _getPopperConfig(attachment){var _this3=this;var defaultBsConfig={placement:attachment,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:SELECTOR_ARROW},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function onCreate(data){if(data.originalPlacement!==data.placement){_this3._handlePopperPlacementChange(data)}},onUpdate:function onUpdate(data){return _this3._handlePopperPlacementChange(data)}};return _extends$1({},defaultBsConfig,this.config.popperConfig)};_proto._getOffset=function _getOffset(){var _this4=this;var offset={};if(typeof this.config.offset==="function"){offset.fn=function(data){data.offsets=_extends$1({},data.offsets,_this4.config.offset(data.offsets,_this4.element));return data}}else{offset.offset=this.config.offset}return offset};_proto._getContainer=function _getContainer(){if(this.config.container===false){return document.body}if(Util.isElement(this.config.container)){return $__default["default"](this.config.container)}return $__default["default"](document).find(this.config.container)};_proto._getAttachment=function _getAttachment(placement){return AttachmentMap[placement.toUpperCase()]};_proto._setListeners=function _setListeners(){var _this5=this;var triggers=this.config.trigger.split(" ");triggers.forEach(function(trigger){if(trigger==="click"){$__default["default"](_this5.element).on(_this5.constructor.Event.CLICK,_this5.config.selector,function(event){return _this5.toggle(event)})}else if(trigger!==TRIGGER_MANUAL){var eventIn=trigger===TRIGGER_HOVER?_this5.constructor.Event.MOUSEENTER:_this5.constructor.Event.FOCUSIN;var eventOut=trigger===TRIGGER_HOVER?_this5.constructor.Event.MOUSELEAVE:_this5.constructor.Event.FOCUSOUT;$__default["default"](_this5.element).on(eventIn,_this5.config.selector,function(event){return _this5._enter(event)}).on(eventOut,_this5.config.selector,function(event){return _this5._leave(event)})}});this._hideModalHandler=function(){if(_this5.element){_this5.hide()}};$__default["default"](this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler);if(this.config.selector){this.config=_extends$1({},this.config,{trigger:"manual",selector:""})}else{this._fixTitle()}};_proto._fixTitle=function _fixTitle(){var titleType=typeof this.element.getAttribute("data-original-title");if(this.element.getAttribute("title")||titleType!=="string"){this.element.setAttribute("data-original-title",this.element.getAttribute("title")||"");this.element.setAttribute("title","")}};_proto._enter=function _enter(event,context){var dataKey=this.constructor.DATA_KEY;context=context||$__default["default"](event.currentTarget).data(dataKey);if(!context){context=new this.constructor(event.currentTarget,this._getDelegateConfig());$__default["default"](event.currentTarget).data(dataKey,context)}if(event){context._activeTrigger[event.type==="focusin"?TRIGGER_FOCUS:TRIGGER_HOVER]=true}if($__default["default"](context.getTipElement()).hasClass(CLASS_NAME_SHOW$3)||context._hoverState===HOVER_STATE_SHOW){context._hoverState=HOVER_STATE_SHOW;return}clearTimeout(context._timeout);context._hoverState=HOVER_STATE_SHOW;if(!context.config.delay||!context.config.delay.show){context.show();return}context._timeout=setTimeout(function(){if(context._hoverState===HOVER_STATE_SHOW){context.show()}},context.config.delay.show)};_proto._leave=function _leave(event,context){var dataKey=this.constructor.DATA_KEY;context=context||$__default["default"](event.currentTarget).data(dataKey);if(!context){context=new this.constructor(event.currentTarget,this._getDelegateConfig());$__default["default"](event.currentTarget).data(dataKey,context)}if(event){context._activeTrigger[event.type==="focusout"?TRIGGER_FOCUS:TRIGGER_HOVER]=false}if(context._isWithActiveTrigger()){return}clearTimeout(context._timeout);context._hoverState=HOVER_STATE_OUT;if(!context.config.delay||!context.config.delay.hide){context.hide();return}context._timeout=setTimeout(function(){if(context._hoverState===HOVER_STATE_OUT){context.hide()}},context.config.delay.hide)};_proto._isWithActiveTrigger=function _isWithActiveTrigger(){for(var trigger in this._activeTrigger){if(this._activeTrigger[trigger]){return true}}return false};_proto._getConfig=function _getConfig(config){var dataAttributes=$__default["default"](this.element).data();Object.keys(dataAttributes).forEach(function(dataAttr){if(DISALLOWED_ATTRIBUTES.indexOf(dataAttr)!==-1){delete dataAttributes[dataAttr]}});config=_extends$1({},this.constructor.Default,dataAttributes,typeof config==="object"&&config?config:{});if(typeof config.delay==="number"){config.delay={show:config.delay,hide:config.delay}}if(typeof config.title==="number"){config.title=config.title.toString()}if(typeof config.content==="number"){config.content=config.content.toString()}Util.typeCheckConfig(NAME$4,config,this.constructor.DefaultType);if(config.sanitize){config.template=sanitizeHtml(config.template,config.whiteList,config.sanitizeFn)}return config};_proto._getDelegateConfig=function _getDelegateConfig(){var config={};if(this.config){for(var key in this.config){if(this.constructor.Default[key]!==this.config[key]){config[key]=this.config[key]}}}return config};_proto._cleanTipClass=function _cleanTipClass(){var $tip=$__default["default"](this.getTipElement());var tabClass=$tip.attr("class").match(BSCLS_PREFIX_REGEX$1);if(tabClass!==null&&tabClass.length){$tip.removeClass(tabClass.join(""))}};_proto._handlePopperPlacementChange=function _handlePopperPlacementChange(popperData){this.tip=popperData.instance.popper;this._cleanTipClass();this.addAttachmentClass(this._getAttachment(popperData.placement))};_proto._fixTransition=function _fixTransition(){var tip=this.getTipElement();var initConfigAnimation=this.config.animation;if(tip.getAttribute("x-placement")!==null){return}$__default["default"](tip).removeClass(CLASS_NAME_FADE$3);this.config.animation=false;this.hide();this.show();this.config.animation=initConfigAnimation};Tooltip._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var $element=$__default["default"](this);var data=$element.data(DATA_KEY$4);var _config=typeof config==="object"&&config;if(!data&&/dispose|hide/.test(config)){return}if(!data){data=new Tooltip(this,_config);$element.data(DATA_KEY$4,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};_createClass(Tooltip,null,[{key:"VERSION",get:function get(){return VERSION$4}},{key:"Default",get:function get(){return Default$3}},{key:"NAME",get:function get(){return NAME$4}},{key:"DATA_KEY",get:function get(){return DATA_KEY$4}},{key:"Event",get:function get(){return Event$1}},{key:"EVENT_KEY",get:function get(){return EVENT_KEY$4}},{key:"DefaultType",get:function get(){return DefaultType$3}}]);return Tooltip}();$__default["default"].fn[NAME$4]=Tooltip._jQueryInterface;$__default["default"].fn[NAME$4].Constructor=Tooltip;$__default["default"].fn[NAME$4].noConflict=function(){$__default["default"].fn[NAME$4]=JQUERY_NO_CONFLICT$4;return Tooltip._jQueryInterface};var NAME$3="popover";var VERSION$3="4.6.2";var DATA_KEY$3="bs.popover";var EVENT_KEY$3="."+DATA_KEY$3;var JQUERY_NO_CONFLICT$3=$__default["default"].fn[NAME$3];var CLASS_PREFIX="bs-popover";var BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)"+CLASS_PREFIX+"\\S+","g");var CLASS_NAME_FADE$2="fade";var CLASS_NAME_SHOW$2="show";var SELECTOR_TITLE=".popover-header";var SELECTOR_CONTENT=".popover-body";var Default$2=_extends$1({},Tooltip.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip">'+'<div class="arrow"></div>'+'<h3 class="popover-header"></h3>'+'<div class="popover-body"></div></div>'});var DefaultType$2=_extends$1({},Tooltip.DefaultType,{content:"(string|element|function)"});var Event={HIDE:"hide"+EVENT_KEY$3,HIDDEN:"hidden"+EVENT_KEY$3,SHOW:"show"+EVENT_KEY$3,SHOWN:"shown"+EVENT_KEY$3,INSERTED:"inserted"+EVENT_KEY$3,CLICK:"click"+EVENT_KEY$3,FOCUSIN:"focusin"+EVENT_KEY$3,FOCUSOUT:"focusout"+EVENT_KEY$3,MOUSEENTER:"mouseenter"+EVENT_KEY$3,MOUSELEAVE:"mouseleave"+EVENT_KEY$3};var Popover=function(_Tooltip){_inheritsLoose(Popover,_Tooltip);function Popover(){return _Tooltip.apply(this,arguments)||this}var _proto=Popover.prototype;_proto.isWithContent=function isWithContent(){return this.getTitle()||this._getContent()};_proto.addAttachmentClass=function addAttachmentClass(attachment){$__default["default"](this.getTipElement()).addClass(CLASS_PREFIX+"-"+attachment)};_proto.getTipElement=function getTipElement(){this.tip=this.tip||$__default["default"](this.config.template)[0];return this.tip};_proto.setContent=function setContent(){var $tip=$__default["default"](this.getTipElement());this.setElementContent($tip.find(SELECTOR_TITLE),this.getTitle());var content=this._getContent();if(typeof content==="function"){content=content.call(this.element)}this.setElementContent($tip.find(SELECTOR_CONTENT),content);$tip.removeClass(CLASS_NAME_FADE$2+" "+CLASS_NAME_SHOW$2)};_proto._getContent=function _getContent(){return this.element.getAttribute("data-content")||this.config.content};_proto._cleanTipClass=function _cleanTipClass(){var $tip=$__default["default"](this.getTipElement());var tabClass=$tip.attr("class").match(BSCLS_PREFIX_REGEX);if(tabClass!==null&&tabClass.length>0){$tip.removeClass(tabClass.join(""))}};Popover._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var data=$__default["default"](this).data(DATA_KEY$3);var _config=typeof config==="object"?config:null;if(!data&&/dispose|hide/.test(config)){return}if(!data){data=new Popover(this,_config);$__default["default"](this).data(DATA_KEY$3,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};_createClass(Popover,null,[{key:"VERSION",get:function get(){return VERSION$3}},{key:"Default",get:function get(){return Default$2}},{key:"NAME",get:function get(){return NAME$3}},{key:"DATA_KEY",get:function get(){return DATA_KEY$3}},{key:"Event",get:function get(){return Event}},{key:"EVENT_KEY",get:function get(){return EVENT_KEY$3}},{key:"DefaultType",get:function get(){return DefaultType$2}}]);return Popover}(Tooltip);$__default["default"].fn[NAME$3]=Popover._jQueryInterface;$__default["default"].fn[NAME$3].Constructor=Popover;$__default["default"].fn[NAME$3].noConflict=function(){$__default["default"].fn[NAME$3]=JQUERY_NO_CONFLICT$3;return Popover._jQueryInterface};var NAME$2="scrollspy";var VERSION$2="4.6.2";var DATA_KEY$2="bs.scrollspy";var EVENT_KEY$2="."+DATA_KEY$2;var DATA_API_KEY$1=".data-api";var JQUERY_NO_CONFLICT$2=$__default["default"].fn[NAME$2];var CLASS_NAME_DROPDOWN_ITEM="dropdown-item";var CLASS_NAME_ACTIVE$1="active";var EVENT_ACTIVATE="activate"+EVENT_KEY$2;var EVENT_SCROLL="scroll"+EVENT_KEY$2;var EVENT_LOAD_DATA_API="load"+EVENT_KEY$2+DATA_API_KEY$1;var METHOD_OFFSET="offset";var METHOD_POSITION="position";var SELECTOR_DATA_SPY='[data-spy="scroll"]';var SELECTOR_NAV_LIST_GROUP$1=".nav, .list-group";var SELECTOR_NAV_LINKS=".nav-link";var SELECTOR_NAV_ITEMS=".nav-item";var SELECTOR_LIST_ITEMS=".list-group-item";var SELECTOR_DROPDOWN$1=".dropdown";var SELECTOR_DROPDOWN_ITEMS=".dropdown-item";var SELECTOR_DROPDOWN_TOGGLE$1=".dropdown-toggle";var Default$1={offset:10,method:"auto",target:""};var DefaultType$1={offset:"number",method:"string",target:"(string|element)"};var ScrollSpy=function(){function ScrollSpy(element,config){var _this=this;this._element=element;this._scrollElement=element.tagName==="BODY"?window:element;this._config=this._getConfig(config);this._selector=this._config.target+" "+SELECTOR_NAV_LINKS+","+(this._config.target+" "+SELECTOR_LIST_ITEMS+",")+(this._config.target+" "+SELECTOR_DROPDOWN_ITEMS);this._offsets=[];this._targets=[];this._activeTarget=null;this._scrollHeight=0;$__default["default"](this._scrollElement).on(EVENT_SCROLL,function(event){return _this._process(event)});this.refresh();this._process()}var _proto=ScrollSpy.prototype;_proto.refresh=function refresh(){var _this2=this;var autoMethod=this._scrollElement===this._scrollElement.window?METHOD_OFFSET:METHOD_POSITION;var offsetMethod=this._config.method==="auto"?autoMethod:this._config.method;var offsetBase=offsetMethod===METHOD_POSITION?this._getScrollTop():0;this._offsets=[];this._targets=[];this._scrollHeight=this._getScrollHeight();var targets=[].slice.call(document.querySelectorAll(this._selector));targets.map(function(element){var target;var targetSelector=Util.getSelectorFromElement(element);if(targetSelector){target=document.querySelector(targetSelector)}if(target){var targetBCR=target.getBoundingClientRect();if(targetBCR.width||targetBCR.height){return[$__default["default"](target)[offsetMethod]().top+offsetBase,targetSelector]}}return null}).filter(Boolean).sort(function(a,b){return a[0]-b[0]}).forEach(function(item){_this2._offsets.push(item[0]);_this2._targets.push(item[1])})};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$2);$__default["default"](this._scrollElement).off(EVENT_KEY$2);this._element=null;this._scrollElement=null;this._config=null;this._selector=null;this._offsets=null;this._targets=null;this._activeTarget=null;this._scrollHeight=null};_proto._getConfig=function _getConfig(config){config=_extends$1({},Default$1,typeof config==="object"&&config?config:{});if(typeof config.target!=="string"&&Util.isElement(config.target)){var id=$__default["default"](config.target).attr("id");if(!id){id=Util.getUID(NAME$2);$__default["default"](config.target).attr("id",id)}config.target="#"+id}Util.typeCheckConfig(NAME$2,config,DefaultType$1);return config};_proto._getScrollTop=function _getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop};_proto._getScrollHeight=function _getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)};_proto._getOffsetHeight=function _getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height};_proto._process=function _process(){var scrollTop=this._getScrollTop()+this._config.offset;var scrollHeight=this._getScrollHeight();var maxScroll=this._config.offset+scrollHeight-this._getOffsetHeight();if(this._scrollHeight!==scrollHeight){this.refresh()}if(scrollTop>=maxScroll){var target=this._targets[this._targets.length-1];if(this._activeTarget!==target){this._activate(target)}return}if(this._activeTarget&&scrollTop<this._offsets[0]&&this._offsets[0]>0){this._activeTarget=null;this._clear();return}for(var i=this._offsets.length;i--;){var isActiveTarget=this._activeTarget!==this._targets[i]&&scrollTop>=this._offsets[i]&&(typeof this._offsets[i+1]==="undefined"||scrollTop<this._offsets[i+1]);if(isActiveTarget){this._activate(this._targets[i])}}};_proto._activate=function _activate(target){this._activeTarget=target;this._clear();var queries=this._selector.split(",").map(function(selector){return selector+'[data-target="'+target+'"],'+selector+'[href="'+target+'"]'});var $link=$__default["default"]([].slice.call(document.querySelectorAll(queries.join(","))));if($link.hasClass(CLASS_NAME_DROPDOWN_ITEM)){$link.closest(SELECTOR_DROPDOWN$1).find(SELECTOR_DROPDOWN_TOGGLE$1).addClass(CLASS_NAME_ACTIVE$1);$link.addClass(CLASS_NAME_ACTIVE$1)}else{$link.addClass(CLASS_NAME_ACTIVE$1);$link.parents(SELECTOR_NAV_LIST_GROUP$1).prev(SELECTOR_NAV_LINKS+", "+SELECTOR_LIST_ITEMS).addClass(CLASS_NAME_ACTIVE$1);$link.parents(SELECTOR_NAV_LIST_GROUP$1).prev(SELECTOR_NAV_ITEMS).children(SELECTOR_NAV_LINKS).addClass(CLASS_NAME_ACTIVE$1)}$__default["default"](this._scrollElement).trigger(EVENT_ACTIVATE,{relatedTarget:target})};_proto._clear=function _clear(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(node){return node.classList.contains(CLASS_NAME_ACTIVE$1)}).forEach(function(node){return node.classList.remove(CLASS_NAME_ACTIVE$1)})};ScrollSpy._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var data=$__default["default"](this).data(DATA_KEY$2);var _config=typeof config==="object"&&config;if(!data){data=new ScrollSpy(this,_config);$__default["default"](this).data(DATA_KEY$2,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};_createClass(ScrollSpy,null,[{key:"VERSION",get:function get(){return VERSION$2}},{key:"Default",get:function get(){return Default$1}}]);return ScrollSpy}();$__default["default"](window).on(EVENT_LOAD_DATA_API,function(){var scrollSpys=[].slice.call(document.querySelectorAll(SELECTOR_DATA_SPY));var scrollSpysLength=scrollSpys.length;for(var i=scrollSpysLength;i--;){var $spy=$__default["default"](scrollSpys[i]);ScrollSpy._jQueryInterface.call($spy,$spy.data())}});$__default["default"].fn[NAME$2]=ScrollSpy._jQueryInterface;$__default["default"].fn[NAME$2].Constructor=ScrollSpy;$__default["default"].fn[NAME$2].noConflict=function(){$__default["default"].fn[NAME$2]=JQUERY_NO_CONFLICT$2;return ScrollSpy._jQueryInterface};var NAME$1="tab";var VERSION$1="4.6.2";var DATA_KEY$1="bs.tab";var EVENT_KEY$1="."+DATA_KEY$1;var DATA_API_KEY=".data-api";var JQUERY_NO_CONFLICT$1=$__default["default"].fn[NAME$1];var CLASS_NAME_DROPDOWN_MENU="dropdown-menu";var CLASS_NAME_ACTIVE="active";var CLASS_NAME_DISABLED="disabled";var CLASS_NAME_FADE$1="fade";var CLASS_NAME_SHOW$1="show";var EVENT_HIDE$1="hide"+EVENT_KEY$1;var EVENT_HIDDEN$1="hidden"+EVENT_KEY$1;var EVENT_SHOW$1="show"+EVENT_KEY$1;var EVENT_SHOWN$1="shown"+EVENT_KEY$1;var EVENT_CLICK_DATA_API="click"+EVENT_KEY$1+DATA_API_KEY;var SELECTOR_DROPDOWN=".dropdown";var SELECTOR_NAV_LIST_GROUP=".nav, .list-group";var SELECTOR_ACTIVE=".active";var SELECTOR_ACTIVE_UL="> li > .active";var SELECTOR_DATA_TOGGLE='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]';var SELECTOR_DROPDOWN_TOGGLE=".dropdown-toggle";var SELECTOR_DROPDOWN_ACTIVE_CHILD="> .dropdown-menu .active";var Tab=function(){function Tab(element){this._element=element}var _proto=Tab.prototype;_proto.show=function show(){var _this=this;if(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&$__default["default"](this._element).hasClass(CLASS_NAME_ACTIVE)||$__default["default"](this._element).hasClass(CLASS_NAME_DISABLED)||this._element.hasAttribute("disabled")){return}var target;var previous;var listElement=$__default["default"](this._element).closest(SELECTOR_NAV_LIST_GROUP)[0];var selector=Util.getSelectorFromElement(this._element);if(listElement){var itemSelector=listElement.nodeName==="UL"||listElement.nodeName==="OL"?SELECTOR_ACTIVE_UL:SELECTOR_ACTIVE;previous=$__default["default"].makeArray($__default["default"](listElement).find(itemSelector));previous=previous[previous.length-1]}var hideEvent=$__default["default"].Event(EVENT_HIDE$1,{relatedTarget:this._element});var showEvent=$__default["default"].Event(EVENT_SHOW$1,{relatedTarget:previous});if(previous){$__default["default"](previous).trigger(hideEvent)}$__default["default"](this._element).trigger(showEvent);if(showEvent.isDefaultPrevented()||hideEvent.isDefaultPrevented()){return}if(selector){target=document.querySelector(selector)}this._activate(this._element,listElement);var complete=function complete(){var hiddenEvent=$__default["default"].Event(EVENT_HIDDEN$1,{relatedTarget:_this._element});var shownEvent=$__default["default"].Event(EVENT_SHOWN$1,{relatedTarget:previous});$__default["default"](previous).trigger(hiddenEvent);$__default["default"](_this._element).trigger(shownEvent)};if(target){this._activate(target,target.parentNode,complete)}else{complete()}};_proto.dispose=function dispose(){$__default["default"].removeData(this._element,DATA_KEY$1);this._element=null};_proto._activate=function _activate(element,container,callback){var _this2=this;var activeElements=container&&(container.nodeName==="UL"||container.nodeName==="OL")?$__default["default"](container).find(SELECTOR_ACTIVE_UL):$__default["default"](container).children(SELECTOR_ACTIVE);var active=activeElements[0];var isTransitioning=callback&&active&&$__default["default"](active).hasClass(CLASS_NAME_FADE$1);var complete=function complete(){return _this2._transitionComplete(element,active,callback)};if(active&&isTransitioning){var transitionDuration=Util.getTransitionDurationFromElement(active);$__default["default"](active).removeClass(CLASS_NAME_SHOW$1).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else{complete()}};_proto._transitionComplete=function _transitionComplete(element,active,callback){if(active){$__default["default"](active).removeClass(CLASS_NAME_ACTIVE);var dropdownChild=$__default["default"](active.parentNode).find(SELECTOR_DROPDOWN_ACTIVE_CHILD)[0];if(dropdownChild){$__default["default"](dropdownChild).removeClass(CLASS_NAME_ACTIVE)}if(active.getAttribute("role")==="tab"){active.setAttribute("aria-selected",false)}}$__default["default"](element).addClass(CLASS_NAME_ACTIVE);if(element.getAttribute("role")==="tab"){element.setAttribute("aria-selected",true)}Util.reflow(element);if(element.classList.contains(CLASS_NAME_FADE$1)){element.classList.add(CLASS_NAME_SHOW$1)}var parent=element.parentNode;if(parent&&parent.nodeName==="LI"){parent=parent.parentNode}if(parent&&$__default["default"](parent).hasClass(CLASS_NAME_DROPDOWN_MENU)){var dropdownElement=$__default["default"](element).closest(SELECTOR_DROPDOWN)[0];if(dropdownElement){var dropdownToggleList=[].slice.call(dropdownElement.querySelectorAll(SELECTOR_DROPDOWN_TOGGLE));$__default["default"](dropdownToggleList).addClass(CLASS_NAME_ACTIVE)}element.setAttribute("aria-expanded",true)}if(callback){callback()}};Tab._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var $this=$__default["default"](this);var data=$this.data(DATA_KEY$1);if(!data){data=new Tab(this);$this.data(DATA_KEY$1,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config]()}})};_createClass(Tab,null,[{key:"VERSION",get:function get(){return VERSION$1}}]);return Tab}();$__default["default"](document).on(EVENT_CLICK_DATA_API,SELECTOR_DATA_TOGGLE,function(event){event.preventDefault();Tab._jQueryInterface.call($__default["default"](this),"show")});$__default["default"].fn[NAME$1]=Tab._jQueryInterface;$__default["default"].fn[NAME$1].Constructor=Tab;$__default["default"].fn[NAME$1].noConflict=function(){$__default["default"].fn[NAME$1]=JQUERY_NO_CONFLICT$1;return Tab._jQueryInterface};var NAME="toast";var VERSION="4.6.2";var DATA_KEY="bs.toast";var EVENT_KEY="."+DATA_KEY;var JQUERY_NO_CONFLICT=$__default["default"].fn[NAME];var CLASS_NAME_FADE="fade";var CLASS_NAME_HIDE="hide";var CLASS_NAME_SHOW="show";var CLASS_NAME_SHOWING="showing";var EVENT_CLICK_DISMISS="click.dismiss"+EVENT_KEY;var EVENT_HIDE="hide"+EVENT_KEY;var EVENT_HIDDEN="hidden"+EVENT_KEY;var EVENT_SHOW="show"+EVENT_KEY;var EVENT_SHOWN="shown"+EVENT_KEY;var SELECTOR_DATA_DISMISS='[data-dismiss="toast"]';var Default={animation:true,autohide:true,delay:500};var DefaultType={animation:"boolean",autohide:"boolean",delay:"number"};var Toast=function(){function Toast(element,config){this._element=element;this._config=this._getConfig(config);this._timeout=null;this._setListeners()}var _proto=Toast.prototype;_proto.show=function show(){var _this=this;var showEvent=$__default["default"].Event(EVENT_SHOW);$__default["default"](this._element).trigger(showEvent);if(showEvent.isDefaultPrevented()){return}this._clearTimeout();if(this._config.animation){this._element.classList.add(CLASS_NAME_FADE)}var complete=function complete(){_this._element.classList.remove(CLASS_NAME_SHOWING);_this._element.classList.add(CLASS_NAME_SHOW);$__default["default"](_this._element).trigger(EVENT_SHOWN);if(_this._config.autohide){_this._timeout=setTimeout(function(){_this.hide()},_this._config.delay)}};this._element.classList.remove(CLASS_NAME_HIDE);Util.reflow(this._element);this._element.classList.add(CLASS_NAME_SHOWING);if(this._config.animation){var transitionDuration=Util.getTransitionDurationFromElement(this._element);$__default["default"](this._element).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else{complete()}};_proto.hide=function hide(){if(!this._element.classList.contains(CLASS_NAME_SHOW)){return}var hideEvent=$__default["default"].Event(EVENT_HIDE);$__default["default"](this._element).trigger(hideEvent);if(hideEvent.isDefaultPrevented()){return}this._close()};_proto.dispose=function dispose(){this._clearTimeout();if(this._element.classList.contains(CLASS_NAME_SHOW)){this._element.classList.remove(CLASS_NAME_SHOW)}$__default["default"](this._element).off(EVENT_CLICK_DISMISS);$__default["default"].removeData(this._element,DATA_KEY);this._element=null;this._config=null};_proto._getConfig=function _getConfig(config){config=_extends$1({},Default,$__default["default"](this._element).data(),typeof config==="object"&&config?config:{});Util.typeCheckConfig(NAME,config,this.constructor.DefaultType);return config};_proto._setListeners=function _setListeners(){var _this2=this;$__default["default"](this._element).on(EVENT_CLICK_DISMISS,SELECTOR_DATA_DISMISS,function(){return _this2.hide()})};_proto._close=function _close(){var _this3=this;var complete=function complete(){_this3._element.classList.add(CLASS_NAME_HIDE);$__default["default"](_this3._element).trigger(EVENT_HIDDEN)};this._element.classList.remove(CLASS_NAME_SHOW);if(this._config.animation){var transitionDuration=Util.getTransitionDurationFromElement(this._element);$__default["default"](this._element).one(Util.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else{complete()}};_proto._clearTimeout=function _clearTimeout(){clearTimeout(this._timeout);this._timeout=null};Toast._jQueryInterface=function _jQueryInterface(config){return this.each(function(){var $element=$__default["default"](this);var data=$element.data(DATA_KEY);var _config=typeof config==="object"&&config;if(!data){data=new Toast(this,_config);$element.data(DATA_KEY,data)}if(typeof config==="string"){if(typeof data[config]==="undefined"){throw new TypeError('No method named "'+config+'"')}data[config](this)}})};_createClass(Toast,null,[{key:"VERSION",get:function get(){return VERSION}},{key:"DefaultType",get:function get(){return DefaultType}},{key:"Default",get:function get(){return Default}}]);return Toast}();$__default["default"].fn[NAME]=Toast._jQueryInterface;$__default["default"].fn[NAME].Constructor=Toast;$__default["default"].fn[NAME].noConflict=function(){$__default["default"].fn[NAME]=JQUERY_NO_CONFLICT;return Toast._jQueryInterface};exports.Alert=Alert;exports.Button=Button;exports.Carousel=Carousel;exports.Collapse=Collapse;exports.Dropdown=Dropdown;exports.Modal=Modal;exports.Popover=Popover;exports.Scrollspy=ScrollSpy;exports.Tab=Tab;exports.Toast=Toast;exports.Tooltip=Tooltip;exports.Util=Util;Object.defineProperty(exports,"__esModule",{value:true})});