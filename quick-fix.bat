@echo off
echo QUICK LOCALHOST FIX
echo ==================

echo.
echo Step 1: Checking for XAMPP...
if exist "C:\xampp\xampp-control.exe" (
    echo Found XAMPP at C:\xampp
    set XAMPP_PATH=C:\xampp
    goto :found
)

if exist "C:\Program Files\XAMPP\xampp-control.exe" (
    echo Found XAMPP at C:\Program Files\XAMPP
    set XAMPP_PATH=C:\Program Files\XAMPP
    goto :found
)

echo ERROR: XAMPP not found!
echo Please install XAMPP first.
pause
exit /b 1

:found
echo.
echo Step 2: Stopping existing processes...
taskkill /f /im httpd.exe >nul 2>&1
taskkill /f /im mysqld.exe >nul 2>&1
echo Processes stopped.

echo.
echo Step 3: Checking what's using port 80...
netstat -an | findstr ":80"

echo.
echo Step 4: Starting XAMPP Control Panel...
start "" "%XAMPP_PATH%\xampp-control.exe"

echo.
echo IMPORTANT: In the XAMPP Control Panel that just opened:
echo 1. Click START next to Apache
echo 2. Click START next to MySQL
echo 3. Both should show "Running" in green
echo.
echo If Apache won't start (shows error):
echo - Click "Logs" next to Apache to see the error
echo - Common issue: Port 80 is already in use
echo - Solution: Stop IIS service or use port 8080
echo.

pause

echo.
echo Step 5: Testing localhost...
echo Opening test page...
start "" "http://localhost"
timeout /t 2 /nobreak >nul

echo Opening project...
start "" "http://localhost/beersty-lovable"
timeout /t 2 /nobreak >nul

echo.
echo If localhost doesn't work, try:
echo http://localhost:8080
echo.
echo Next: Test PDO and User Management
start "" "http://localhost/beersty-lovable/test-pdo-simple.php"

echo.
echo TROUBLESHOOTING:
echo - If pages don't load: Apache isn't running
echo - Check XAMPP Control Panel for errors
echo - Try running XAMPP as Administrator
echo - Check Windows Firewall
echo.
pause
