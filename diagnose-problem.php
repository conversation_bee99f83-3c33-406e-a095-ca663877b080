<?php
/**
 * Simple Problem Diagnosis
 * Let's figure out what's actually wrong
 */

echo "<h1>🔍 Problem Diagnosis</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;} .error{background:#f8d7da;color:#721c24;padding:10px;border-radius:5px;} .success{background:#d4edda;color:#155724;padding:10px;border-radius:5px;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:5px;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. PHP Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>PDO MySQL Available:</strong> " . (extension_loaded('pdo_mysql') ? '✅ YES' : '❌ NO') . "</p>";
echo "<p><strong>Password Functions:</strong> " . (function_exists('password_hash') ? '✅ YES' : '❌ NO') . "</p>";

echo "<h2>2. Database Connection Test</h2>";

// Test 1: Basic MySQL connection
echo "<h3>Test 1: Basic MySQL Connection</h3>";
try {
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $conn = new PDO($dsn, 'root', '');
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ MySQL connection successful</div>";
    
    // Get MySQL version
    $stmt = $conn->prepare("SELECT VERSION() as version");
    $stmt->execute();
    $version = $stmt->fetch();
    echo "<p><strong>MySQL Version:</strong> " . htmlspecialchars($version['version']) . "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ MySQL connection failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<p><strong>Possible issues:</strong></p>";
    echo "<ul>";
    echo "<li>XAMPP MySQL service not running</li>";
    echo "<li>MySQL port 3306 blocked</li>";
    echo "<li>MySQL authentication issues</li>";
    echo "</ul>";
    exit;
}

// Test 2: Check if database exists
echo "<h3>Test 2: Database Existence</h3>";
try {
    $stmt = $conn->prepare("SHOW DATABASES LIKE 'beersty_db'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>✅ Database 'beersty_db' exists</div>";
        
        // Connect to the database
        $conn->exec("USE beersty_db");
        echo "<div class='success'>✅ Connected to beersty_db</div>";
        
    } else {
        echo "<div class='warning'>⚠️ Database 'beersty_db' does not exist</div>";
        echo "<p><strong>Solution:</strong> Create the database first</p>";
        
        // Try to create it
        try {
            $conn->exec("CREATE DATABASE beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<div class='success'>✅ Database 'beersty_db' created successfully</div>";
            $conn->exec("USE beersty_db");
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create database: " . htmlspecialchars($e->getMessage()) . "</div>";
            exit;
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database check failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    exit;
}

// Test 3: Check tables
echo "<h3>Test 3: Table Structure</h3>";
$requiredTables = ['users', 'profiles'];
$missingTables = [];

foreach ($requiredTables as $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ Table '$table' exists</div>";
            
            // Show table structure
            $stmt = $conn->prepare("DESCRIBE $table");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            echo "<p><strong>Columns in $table:</strong> ";
            $columnNames = array_column($columns, 'Field');
            echo implode(', ', $columnNames) . "</p>";
            
        } else {
            echo "<div class='warning'>⚠️ Table '$table' missing</div>";
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking table '$table': " . htmlspecialchars($e->getMessage()) . "</div>";
        $missingTables[] = $table;
    }
}

// Create missing tables
if (!empty($missingTables)) {
    echo "<h3>Creating Missing Tables</h3>";
    
    if (in_array('users', $missingTables)) {
        try {
            $sql = "CREATE TABLE users (
                id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL
            )";
            $conn->exec($sql);
            echo "<div class='success'>✅ Users table created</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create users table: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    if (in_array('profiles', $missingTables)) {
        try {
            $sql = "CREATE TABLE profiles (
                id VARCHAR(36) PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                role VARCHAR(50) DEFAULT 'customer',
                brewery_id VARCHAR(36) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            echo "<div class='success'>✅ Profiles table created</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Failed to create profiles table: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
}

// Test 4: Check for users
echo "<h3>Test 4: User Data</h3>";
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $userCount = $stmt->fetch()['count'];
    
    if ($userCount > 0) {
        echo "<div class='success'>✅ Found $userCount users in database</div>";
        
        // Show existing users
        $stmt = $conn->prepare("
            SELECT u.email, p.role, u.created_at 
            FROM users u 
            LEFT JOIN profiles p ON u.id = p.id 
            LIMIT 5
        ");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>Email</th><th>Role</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role'] ?? 'No Profile') . "</td>";
            echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<div class='warning'>⚠️ No users found in database</div>";
        echo "<p><strong>Creating test users...</strong></p>";
        
        // Create test users
        $testUsers = [
            ['email' => '<EMAIL>', 'password' => 'admin123', 'role' => 'admin'],
            ['email' => '<EMAIL>', 'password' => 'test123', 'role' => 'customer']
        ];
        
        foreach ($testUsers as $userData) {
            try {
                $userId = uniqid('user_');
                $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
                
                // Insert user
                $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
                $stmt->execute([$userId, $userData['email'], $passwordHash]);
                
                // Insert profile
                $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)");
                $stmt->execute([$userId, $userData['email'], $userData['role']]);
                
                echo "<div class='success'>✅ Created user: {$userData['email']}</div>";
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Failed to create user {$userData['email']}: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ User check failed: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test 5: Authentication test
echo "<h3>Test 5: Authentication Test</h3>";
try {
    $testEmail = '<EMAIL>';
    $testPassword = 'admin123';
    
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role 
        FROM users u 
        LEFT JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute([$testEmail]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<div class='success'>✅ User '$testEmail' found in database</div>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($user['role'] ?? 'No Profile') . "</p>";
        
        if (password_verify($testPassword, $user['password_hash'])) {
            echo "<div class='success'>✅ Password verification successful</div>";
        } else {
            echo "<div class='error'>❌ Password verification failed</div>";
            echo "<p><strong>Hash in DB:</strong> " . htmlspecialchars(substr($user['password_hash'], 0, 50)) . "...</p>";
        }
    } else {
        echo "<div class='error'>❌ User '$testEmail' not found</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Authentication test failed: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>6. Summary & Next Steps</h2>";
echo "<div class='warning'>";
echo "<h3>🎯 What to do next:</h3>";
echo "<ol>";
echo "<li><strong>If all tests passed:</strong> Try logging in at <a href='auth/login.php'>auth/login.php</a></li>";
echo "<li><strong>If database issues:</strong> Check XAMPP Control Panel - make sure MySQL is running</li>";
echo "<li><strong>If authentication fails:</strong> The password hashing might be the issue</li>";
echo "<li><strong>If tables missing:</strong> This script should have created them</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔗 Quick Links</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php'>🔐 Try Login</a></li>";
echo "<li><a href='http://localhost/phpmyadmin'>🐬 phpMyAdmin</a></li>";
echo "<li><a href='/'>🏠 Homepage</a></li>";
echo "</ul>";
?>
