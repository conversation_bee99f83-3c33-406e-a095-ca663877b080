# BASIC DIAGNOSIS - Let's figure out what's actually happening

Write-Host "🔍 BASIC SYSTEM DIAGNOSIS" -ForegroundColor Red
Write-Host "=========================" -ForegroundColor Red
Write-Host "Let's find out what's really going on..." -ForegroundColor Yellow
Write-Host ""

# 1. Check if XAMPP exists at all
Write-Host "1. CHECKING IF XAMPP EXISTS..." -ForegroundColor Cyan
$XamppLocations = @(
    "C:\xampp",
    "C:\Program Files\XAMPP", 
    "C:\Program Files (x86)\XAMPP",
    "D:\xampp",
    "E:\xampp"
)

$XamppFound = $false
foreach ($location in $XamppLocations) {
    if (Test-Path $location) {
        Write-Host "   FOUND: $location" -ForegroundColor Green
        $XamppPath = $location
        $XamppFound = $true
        
        # Check key files
        $ApacheExe = Join-Path $location "apache\bin\httpd.exe"
        $MysqlExe = Join-Path $location "mysql\bin\mysqld.exe"
        $ControlPanel = Join-Path $location "xampp-control.exe"
        
        Write-Host "   Apache exe: $(if (Test-Path $ApacheExe) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if (Test-Path $ApacheExe) { 'Green' } else { 'Red' })
        Write-Host "   MySQL exe: $(if (Test-Path $MysqlExe) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if (Test-Path $MysqlExe) { 'Green' } else { 'Red' })
        Write-Host "   Control Panel: $(if (Test-Path $ControlPanel) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if (Test-Path $ControlPanel) { 'Green' } else { 'Red' })
        break
    }
}

if (-not $XamppFound) {
    Write-Host "   NO XAMPP INSTALLATION FOUND!" -ForegroundColor Red
    Write-Host "   You need to install XAMPP first!" -ForegroundColor Yellow
    Write-Host "   Download from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# 2. Check what processes are actually running
Write-Host ""
Write-Host "2. CHECKING RUNNING PROCESSES..." -ForegroundColor Cyan

$AllProcesses = Get-Process | Where-Object { $_.ProcessName -match "httpd|apache|mysql|xampp" }
if ($AllProcesses) {
    Write-Host "   Web server related processes:" -ForegroundColor Yellow
    $AllProcesses | Format-Table ProcessName, Id, CPU -AutoSize
} else {
    Write-Host "   NO web server processes running" -ForegroundColor Red
}

# 3. Check what's using common ports
Write-Host ""
Write-Host "3. CHECKING PORT USAGE..." -ForegroundColor Cyan

$Ports = @(80, 8080, 3306, 443)
foreach ($port in $Ports) {
    try {
        $PortCheck = netstat -an | findstr ":$port "
        if ($PortCheck) {
            Write-Host "   Port $port is in use:" -ForegroundColor Yellow
            $PortCheck | ForEach-Object { Write-Host "     $_" -ForegroundColor White }
        } else {
            Write-Host "   Port $port is FREE" -ForegroundColor Green
        }
    } catch {
        Write-Host "   Could not check port $port" -ForegroundColor Gray
    }
}

# 4. Try to manually start XAMPP Control Panel
Write-Host ""
Write-Host "4. STARTING XAMPP CONTROL PANEL..." -ForegroundColor Cyan

if ($XamppFound) {
    $ControlPanel = Join-Path $XamppPath "xampp-control.exe"
    if (Test-Path $ControlPanel) {
        Write-Host "   Starting XAMPP Control Panel..." -ForegroundColor Yellow
        Start-Process $ControlPanel -WindowStyle Normal
        Write-Host "   XAMPP Control Panel should be opening now" -ForegroundColor Green
        Start-Sleep 3
        
        # Check if it's running
        $XamppControlProcess = Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue
        if ($XamppControlProcess) {
            Write-Host "   ✅ XAMPP Control Panel is running" -ForegroundColor Green
        } else {
            Write-Host "   ❌ XAMPP Control Panel failed to start" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ XAMPP Control Panel not found at: $ControlPanel" -ForegroundColor Red
    }
}

# 5. Manual instructions
Write-Host ""
Write-Host "5. WHAT YOU NEED TO DO NOW..." -ForegroundColor Red
Write-Host "==============================" -ForegroundColor Red

if ($XamppFound) {
    Write-Host ""
    Write-Host "XAMPP is installed. Here's what to do:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "STEP 1: Look for the XAMPP Control Panel window" -ForegroundColor Cyan
    Write-Host "        (It should have opened - look in your taskbar)" -ForegroundColor White
    Write-Host ""
    Write-Host "STEP 2: In XAMPP Control Panel:" -ForegroundColor Cyan
    Write-Host "        - Find the row that says 'Apache'" -ForegroundColor White
    Write-Host "        - Click the 'Start' button next to Apache" -ForegroundColor White
    Write-Host "        - Find the row that says 'MySQL'" -ForegroundColor White
    Write-Host "        - Click the 'Start' button next to MySQL" -ForegroundColor White
    Write-Host ""
    Write-Host "STEP 3: Watch for:" -ForegroundColor Cyan
    Write-Host "        - Apache should turn GREEN and say 'Running'" -ForegroundColor White
    Write-Host "        - MySQL should turn GREEN and say 'Running'" -ForegroundColor White
    Write-Host "        - If you see RED or errors, tell me what it says" -ForegroundColor White
    Write-Host ""
    Write-Host "STEP 4: If Apache won't start:" -ForegroundColor Cyan
    Write-Host "        - Click 'Logs' next to Apache to see the error" -ForegroundColor White
    Write-Host "        - Try running XAMPP as Administrator" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "❌ XAMPP is NOT installed!" -ForegroundColor Red
    Write-Host "You need to install XAMPP first:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://www.apachefriends.org/download.html" -ForegroundColor White
    Write-Host "2. Download XAMPP for Windows" -ForegroundColor White
    Write-Host "3. Install it (usually to C:\xampp)" -ForegroundColor White
    Write-Host "4. Then run this script again" -ForegroundColor White
}

Write-Host ""
Write-Host "🔄 After starting Apache and MySQL, run this to test:" -ForegroundColor Cyan
Write-Host "   .\test-localhost.ps1" -ForegroundColor White

Write-Host ""
$status = Read-Host "What do you see in XAMPP Control Panel? (describe what you see or type 'help')"

if ($status -eq "help") {
    Write-Host ""
    Write-Host "COMMON XAMPP CONTROL PANEL SCENARIOS:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "SCENARIO 1: Apache shows 'Running' (green)" -ForegroundColor Green
    Write-Host "   - Good! Apache is working" -ForegroundColor White
    Write-Host "   - Test: .\test-localhost.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "SCENARIO 2: Apache shows 'Stopped' (red)" -ForegroundColor Red
    Write-Host "   - Click 'Start' next to Apache" -ForegroundColor White
    Write-Host "   - If it fails, click 'Logs' to see why" -ForegroundColor White
    Write-Host ""
    Write-Host "SCENARIO 3: Apache shows error when starting" -ForegroundColor Red
    Write-Host "   - Common: 'Port 80 in use'" -ForegroundColor White
    Write-Host "   - Solution: Use port 8080 instead" -ForegroundColor White
    Write-Host ""
    Write-Host "SCENARIO 4: XAMPP Control Panel won't open" -ForegroundColor Red
    Write-Host "   - Try running as Administrator" -ForegroundColor White
    Write-Host "   - Check if XAMPP is properly installed" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
