# PWA Icons Directory

This directory contains icons for the Progressive Web App (PWA) functionality.

## Required Icons

The following icon files are needed for full PWA support:

### App Icons
- `icon-72x72.png` - 72x72 pixels
- `icon-96x96.png` - 96x96 pixels  
- `icon-128x128.png` - 128x128 pixels
- `icon-144x144.png` - 144x144 pixels
- `icon-152x152.png` - 152x152 pixels
- `icon-192x192.png` - 192x192 pixels
- `icon-384x384.png` - 384x384 pixels
- `icon-512x512.png` - 512x512 pixels

### Favicon Icons
- `favicon-16x16.png` - 16x16 pixels
- `favicon-32x32.png` - 32x32 pixels
- `apple-touch-icon.png` - 180x180 pixels

### Shortcut Icons
- `checkin-96x96.png` - Check-in shortcut icon
- `discover-96x96.png` - Discover shortcut icon
- `profile-96x96.png` - Profile shortcut icon
- `map-96x96.png` - Map shortcut icon

### Notification Icons
- `badge-72x72.png` - Notification badge icon
- `view-action.png` - View action icon
- `close-action.png` - Close action icon

## Icon Guidelines

- Use the Beersty brand colors (#f8b500 primary)
- Include the beer mug icon as the main symbol
- Ensure icons are optimized for different sizes
- Use PNG format with transparency
- Follow platform-specific guidelines for maskable icons

## Generating Icons

You can use tools like:
- [PWA Builder](https://www.pwabuilder.com/)
- [RealFaviconGenerator](https://realfavicongenerator.net/)
- [App Icon Generator](https://appicon.co/)

## Temporary Placeholder

Until proper icons are created, the PWA will use FontAwesome icons as fallbacks.
