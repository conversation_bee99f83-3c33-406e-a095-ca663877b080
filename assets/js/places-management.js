/**
 * Places Management JavaScript
 * Handle place editing, creation, and management
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePlacesManagement();
    setupModalHandlers();
});

function setupModalHandlers() {
    // Simple modal z-index fix without complex interference
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            // Lower admin sidebar z-index when modal opens
            const adminSidebar = document.querySelector('.admin-sidebar');
            if (adminSidebar) {
                adminSidebar.style.zIndex = '1';
            }

            // Ensure modal has proper z-index
            this.style.zIndex = '1060';

            // Remove any backdrop that might appear
            setTimeout(() => {
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }, 10);
        });

        modal.addEventListener('hidden.bs.modal', function() {
            // Restore admin sidebar z-index when modal closes
            const adminSidebar = document.querySelector('.admin-sidebar');
            if (adminSidebar) {
                adminSidebar.style.zIndex = '100';
            }
        });

        modal.addEventListener('shown.bs.modal', function() {
            // Simple validation setup
            setupFormValidation(this);

            // Focus first input field
            const firstInput = this.querySelector('input[type="text"], input[type="email"], select');
            if (firstInput) {
                setTimeout(() => {
                    firstInput.focus();
                }, 100);
            }
        });
    });
}


function initializePlacesManagement() {
    // Initialize form handlers
    initializeFormHandlers();
}

function initializeFormHandlers() {
    // Create place form
    const createForm = document.getElementById('createPlaceForm');
    if (createForm) {
        createForm.addEventListener('submit', handleCreatePlace);
    }

    // Edit place form
    const editForm = document.getElementById('editPlaceForm');
    if (editForm) {
        editForm.addEventListener('submit', handleEditPlace);
    }
}

function setupFormValidation(modal) {
    // Simple validation setup for the specific modal
    if (!modal) return;

    // Phone number formatting
    const phoneInputs = modal.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', formatPhoneNumber);
    });

    // URL validation
    const urlInputs = modal.querySelectorAll('input[type="url"]');
    urlInputs.forEach(input => {
        input.addEventListener('blur', validateURL);
    });

    // Email validation
    const emailInputs = modal.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', validateEmail);
    });

    // Brewery type validation
    const typeSelects = modal.querySelectorAll('select[name="brewery_type"]');
    typeSelects.forEach(select => {
        select.addEventListener('change', validateBreweryType);
    });
}

function validateBreweryType() {
    if (this.value === '') {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
}

// Removed complex protectFormFields and ensureFormFieldsInteractive functions
// These were causing the form input issues by overriding event listeners
// and continuously modifying form field properties

function formatPhoneNumber(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.length >= 6) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
    }
    
    e.target.value = value;
}

function validateURL(e) {
    const url = e.target.value.trim();
    if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
        e.target.value = 'https://' + url;
    }
}

function validateEmail(e) {
    const email = e.target.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        e.target.setCustomValidity('Please enter a valid email address');
    } else {
        e.target.setCustomValidity('');
    }
}

function handleCreatePlace(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;
    
    // Submit form (will reload page on success)
    e.target.submit();
}

function handleEditPlace(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Submit form (will reload page on success)
    e.target.submit();
}

function editPlace(placeId) {
    // Fetch place data
    fetch(`../api/places-management.php?action=get&id=${placeId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateEditForm(data.place);
            const modal = new bootstrap.Modal(document.getElementById('editPlaceModal'));
            modal.show();
        } else {
            showNotification('Failed to load place details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error loading place:', error);
        showNotification('Failed to load place details', 'error');
    });
}

function populateEditForm(place) {
    // Populate form fields
    document.getElementById('edit_place_id').value = place.id;
    document.getElementById('edit_name').value = place.name || '';
    document.getElementById('edit_brewery_type').value = place.brewery_type || 'brewery';
    document.getElementById('edit_description').value = place.description || '';
    document.getElementById('edit_phone').value = place.phone || '';
    document.getElementById('edit_email').value = place.email || '';
    document.getElementById('edit_website').value = place.website || '';
    document.getElementById('edit_address').value = place.address || '';
    document.getElementById('edit_city').value = place.city || '';
    document.getElementById('edit_state').value = place.state || '';
    document.getElementById('edit_zip').value = place.zip || '';
    document.getElementById('edit_latitude').value = place.latitude || '';
    document.getElementById('edit_longitude').value = place.longitude || '';
    document.getElementById('edit_verified').checked = place.verified == 1;
    document.getElementById('edit_claimed').checked = place.claimed == 1;
}

function deletePlace(placeId) {
    if (!confirm('Are you sure you want to delete this place? This action cannot be undone and will remove all associated data including photos, coupons, and reviews.')) {
        return;
    }
    
    fetch('../api/places-management.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'delete',
            id: placeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Place deleted successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to delete place: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showNotification('Failed to delete place', 'error');
    });
}

function togglePlaceStatus(placeId, field, value) {
    const action = value ? 'verify' : 'unverify';
    if (field === 'claimed') {
        action = value ? 'claim' : 'unclaim';
    }
    
    fetch('../api/places-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            id: placeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Place ${action}ed successfully!`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(`Failed to ${action} place: ` + data.message, 'error');
        }
    })
    .catch(error => {
        console.error(`${action} error:`, error);
        showNotification(`Failed to ${action} place`, 'error');
    });
}

function bulkAction(action) {
    const selectedPlaces = Array.from(document.querySelectorAll('.place-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedPlaces.length === 0) {
        alert('Please select places to perform bulk action.');
        return;
    }
    
    let confirmMessage = '';
    switch (action) {
        case 'verify':
            confirmMessage = `Are you sure you want to verify ${selectedPlaces.length} places?`;
            break;
        case 'unverify':
            confirmMessage = `Are you sure you want to unverify ${selectedPlaces.length} places?`;
            break;
        case 'delete':
            confirmMessage = `Are you sure you want to delete ${selectedPlaces.length} places? This action cannot be undone.`;
            break;
        default:
            return;
    }
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    fetch('../api/places-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: `bulk_${action}`,
            place_ids: selectedPlaces
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully ${action}ed ${data.affected_count} places!`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(`Failed to ${action} places: ` + data.message, 'error');
        }
    })
    .catch(error => {
        console.error(`Bulk ${action} error:`, error);
        showNotification(`Failed to ${action} places`, 'error');
    });
}

function selectAllPlaces() {
    const checkboxes = document.querySelectorAll('.place-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateBulkActions();
}

function deselectAllPlaces() {
    const checkboxes = document.querySelectorAll('.place-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const selectedCount = document.querySelectorAll('.place-checkbox:checked').length;
    const bulkActions = document.getElementById('bulkActions');
    
    if (bulkActions) {
        if (selectedCount > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

function exportPlaces(format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    window.location.href = `../api/places-management.php?${params.toString()}`;
}

function importPlaces() {
    // TODO: Implement import functionality
    alert('Import functionality coming soon!');
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function formatAddress(place) {
    const parts = [];
    if (place.address) parts.push(place.address);
    if (place.city) parts.push(place.city);
    if (place.state) parts.push(place.state);
    if (place.zip) parts.push(place.zip);
    return parts.join(', ');
}

function formatPhoneDisplay(phone) {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
}

// Auto-complete functionality for addresses
function initializeAddressAutocomplete() {
    // TODO: Implement Google Places API integration for address autocomplete
    console.log('Address autocomplete initialization placeholder');
}

// Geolocation functionality
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                document.getElementById('edit_latitude').value = position.coords.latitude;
                document.getElementById('edit_longitude').value = position.coords.longitude;
                showNotification('Location updated successfully!', 'success');
            },
            function(error) {
                showNotification('Unable to get current location: ' + error.message, 'error');
            }
        );
    } else {
        showNotification('Geolocation is not supported by this browser', 'error');
    }
}
