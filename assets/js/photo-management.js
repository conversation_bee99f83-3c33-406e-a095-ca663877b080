/**
 * Photo Management JavaScript
 * Handle photo uploads, viewing, editing, and management
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePhotoManagement();
});

function initializePhotoManagement() {
    // Initialize drag and drop for photo uploads
    initializeDragAndDrop();
    
    // Initialize photo viewer
    initializePhotoViewer();
    
    // Initialize form handlers
    initializeFormHandlers();
}

// Drag and Drop Upload
function initializeDragAndDrop() {
    const uploadArea = document.querySelector('.upload-area');
    if (!uploadArea) return;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    uploadArea.addEventListener('drop', handleDrop, false);
    uploadArea.addEventListener('click', () => {
        document.getElementById('photoFiles').click();
    });
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    e.currentTarget.classList.add('dragover');
}

function unhighlight(e) {
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    handleFiles(files);
}

function handleFiles(files) {
    [...files].forEach(uploadFile);
}

function uploadFile(file) {
    if (!file.type.startsWith('image/')) {
        alert('Please select only image files.');
        return;
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
        alert('File size must be less than 10MB.');
        return;
    }
    
    const formData = new FormData();
    formData.append('photo', file);
    formData.append('action', 'upload');
    
    // Get form data if available
    const ownerType = document.getElementById('upload_owner_type')?.value;
    const ownerId = document.getElementById('upload_owner_id')?.value;
    const albumId = document.getElementById('upload_album_id')?.value;
    
    if (ownerType) formData.append('owner_type', ownerType);
    if (ownerId) formData.append('owner_id', ownerId);
    if (albumId) formData.append('album_id', albumId);
    
    fetch('../api/photo-management.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Photo uploaded successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Upload failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        showNotification('Upload failed: ' + error.message, 'error');
    });
}

// Photo Viewer
function initializePhotoViewer() {
    // Create photo viewer modal if it doesn't exist
    if (!document.getElementById('photoViewerModal')) {
        createPhotoViewerModal();
    }
}

function createPhotoViewerModal() {
    const modal = document.createElement('div');
    modal.className = 'modal photo-viewer-modal';
    modal.id = 'photoViewerModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="photoViewerTitle">Photo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <img id="photoViewerImage" src="" alt="" class="img-fluid">
                </div>
                <div class="modal-footer">
                    <div id="photoViewerDetails" class="photo-details w-100"></div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function viewPhoto(photoId) {
    fetch(`../api/photo-management.php?action=get&id=${photoId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const photo = data.photo;
            
            document.getElementById('photoViewerTitle').textContent = photo.title || photo.original_filename;
            document.getElementById('photoViewerImage').src = `../uploads/photos/${photo.filename}`;
            document.getElementById('photoViewerImage').alt = photo.alt_text || photo.title || 'Photo';
            
            // Update details
            const details = document.getElementById('photoViewerDetails');
            details.innerHTML = `
                <h6>Photo Details</h6>
                <div class="detail-row">
                    <span class="detail-label">Owner:</span>
                    <span class="detail-value">${photo.owner_name} (${photo.owner_type})</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Album:</span>
                    <span class="detail-value">${photo.album_name || 'No album'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Uploaded:</span>
                    <span class="detail-value">${formatDate(photo.created_at)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Size:</span>
                    <span class="detail-value">${photo.width} × ${photo.height} px</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">File Size:</span>
                    <span class="detail-value">${formatFileSize(photo.file_size)}</span>
                </div>
                ${photo.description ? `
                <div class="detail-row">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">${photo.description}</span>
                </div>
                ` : ''}
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('photoViewerModal'));
            modal.show();
        } else {
            showNotification('Failed to load photo details', 'error');
        }
    })
    .catch(error => {
        console.error('Error loading photo:', error);
        showNotification('Failed to load photo', 'error');
    });
}

function editPhoto(photoId) {
    // TODO: Implement photo editing modal
    alert('Photo editing functionality coming soon!');
}

function deletePhoto(photoId) {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
        return;
    }
    
    fetch('../api/photo-management.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'delete',
            id: photoId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Photo deleted successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to delete photo: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showNotification('Failed to delete photo', 'error');
    });
}

// Form Handlers
function initializeFormHandlers() {
    // File input change handler
    const fileInput = document.getElementById('photoFiles');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });
    }
}

// Filter Updates
function updateOwnerOptions() {
    const ownerType = document.getElementById('owner_type').value;
    const form = document.querySelector('form');
    
    // Reset owner_id and album_id
    document.getElementById('owner_id').value = '';
    document.getElementById('album_id').value = '';
    
    // Submit form to reload with new owner type
    form.submit();
}

function updateAlbumOptions() {
    const ownerId = document.getElementById('owner_id').value;
    const albumSelect = document.getElementById('album_id');
    
    if (!ownerId) {
        albumSelect.innerHTML = '<option value="">All Albums</option>';
        return;
    }
    
    // Fetch albums for selected owner
    fetch(`../api/photo-management.php?action=get_albums&owner_id=${ownerId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            albumSelect.innerHTML = '<option value="">All Albums</option>';
            data.albums.forEach(album => {
                const option = document.createElement('option');
                option.value = album.id;
                option.textContent = album.name;
                albumSelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading albums:', error);
    });
}

// Utility Functions
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Bulk Operations
function selectAllPhotos() {
    const checkboxes = document.querySelectorAll('.photo-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateBulkActions();
}

function deselectAllPhotos() {
    const checkboxes = document.querySelectorAll('.photo-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const selectedCount = document.querySelectorAll('.photo-checkbox:checked').length;
    const bulkActions = document.getElementById('bulkActions');
    
    if (bulkActions) {
        if (selectedCount > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

function bulkDeletePhotos() {
    const selectedPhotos = Array.from(document.querySelectorAll('.photo-checkbox:checked'))
        .map(checkbox => checkbox.value);

    if (selectedPhotos.length === 0) {
        alert('Please select photos to delete.');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedPhotos.length} photos? This action cannot be undone.`)) {
        return;
    }

    fetch('../api/photo-management.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'bulk_delete',
            photo_ids: selectedPhotos
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully deleted ${data.deleted_count} photos!`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to delete photos: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Bulk delete error:', error);
        showNotification('Failed to delete photos', 'error');
    });
}

// Album Management Functions
function createAlbum() {
    const name = prompt('Enter album name:');
    if (!name) return;

    const description = prompt('Enter album description (optional):') || '';
    const ownerType = document.getElementById('owner_type')?.value || 'user';
    const ownerId = document.getElementById('owner_id')?.value;

    if (!ownerId) {
        alert('Please select an owner first.');
        return;
    }

    fetch('../api/photo-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create_album',
            name: name,
            description: description,
            owner_type: ownerType,
            owner_id: ownerId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Album created successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to create album: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Create album error:', error);
        showNotification('Failed to create album', 'error');
    });
}
