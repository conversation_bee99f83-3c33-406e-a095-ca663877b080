/**
 * Home Page Smart Search Enhancement
 * Adds additional functionality specific to the home page search
 */

document.addEventListener('DOMContentLoaded', function() {
    const locationInput = document.querySelector('input[name="location"]');
    const searchForm = document.querySelector('.search-container form');
    
    if (!locationInput) return;
    
    // Add geolocation button
    addGeolocationButton();
    
    // Add search form enhancements
    enhanceSearchForm();
    
    // Add demo functionality for testing
    addDemoFunctionality();
});

/**
 * Add a geolocation button next to the location input
 */
function addGeolocationButton() {
    const locationInput = document.querySelector('input[name="location"]');
    const inputGroup = locationInput.closest('.input-group');
    
    // Create geolocation button
    const geoButton = document.createElement('button');
    geoButton.type = 'button';
    geoButton.className = 'btn btn-outline-secondary';
    geoButton.innerHTML = '<i class="fas fa-crosshairs"></i>';
    geoButton.title = 'Use my current location';
    geoButton.setAttribute('data-bs-toggle', 'tooltip');
    geoButton.setAttribute('data-bs-placement', 'top');
    
    // Add click handler
    geoButton.addEventListener('click', function() {
        detectCurrentLocation(this);
    });
    
    // Insert button after the input
    inputGroup.appendChild(geoButton);
    
    // Initialize tooltip
    new bootstrap.Tooltip(geoButton);
}

/**
 * Detect and use current location
 */
function detectCurrentLocation(button) {
    const locationInput = document.querySelector('input[name="location"]');
    const originalHTML = button.innerHTML;
    
    if (!navigator.geolocation) {
        showLocationError('Geolocation is not supported by this browser');
        return;
    }
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    navigator.geolocation.getCurrentPosition(
        position => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            
            // Store coordinates
            document.getElementById('location_lat').value = lat;
            document.getElementById('location_lng').value = lng;
            
            // Reverse geocode to get city name
            reverseGeocode(lat, lng)
                .then(cityName => {
                    locationInput.value = cityName;
                    locationInput.closest('.location-input-container').classList.add('has-location');
                    
                    // Show success state
                    button.innerHTML = '<i class="fas fa-check text-success"></i>';
                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                        button.disabled = false;
                    }, 2000);
                })
                .catch(error => {
                    console.error('Reverse geocoding failed:', error);
                    locationInput.value = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
                    
                    button.innerHTML = '<i class="fas fa-check text-warning"></i>';
                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                        button.disabled = false;
                    }, 2000);
                });
        },
        error => {
            showLocationError('Unable to detect your location: ' + error.message);
            button.innerHTML = originalHTML;
            button.disabled = false;
        },
        {
            timeout: 15000,
            enableHighAccuracy: true,
            maximumAge: 60000 // 1 minute for better accuracy
        }
    );
}

/**
 * Reverse geocode coordinates to city name
 */
function reverseGeocode(lat, lng) {
    return fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10`)
        .then(response => response.json())
        .then(data => {
            if (data && data.address) {
                const city = data.address.city || data.address.town || data.address.village;
                const state = data.address.state;
                
                if (city && state) {
                    return `${city}, ${state}`;
                } else if (city) {
                    return city;
                } else {
                    return data.display_name.split(',').slice(0, 2).join(', ');
                }
            }
            throw new Error('No address found');
        });
}

/**
 * Show location error message
 */
function showLocationError(message) {
    // Create or update error alert
    let errorAlert = document.querySelector('.location-error-alert');
    if (!errorAlert) {
        errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-warning alert-dismissible fade show location-error-alert mt-2';
        errorAlert.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span class="error-message"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const searchContainer = document.querySelector('.search-container');
        searchContainer.appendChild(errorAlert);
    }
    
    errorAlert.querySelector('.error-message').textContent = message;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (errorAlert && errorAlert.parentNode) {
            errorAlert.remove();
        }
    }, 5000);
}

/**
 * Enhance the search form with additional functionality
 */
function enhanceSearchForm() {
    const searchForm = document.querySelector('.search-container form');
    const locationInput = document.querySelector('input[name="location"]');
    
    if (!searchForm || !locationInput) return;
    
    // Add form submission handler
    searchForm.addEventListener('submit', function(e) {
        const location = locationInput.value.trim();
        const lat = document.getElementById('location_lat').value;
        const lng = document.getElementById('location_lng').value;
        
        // If we have coordinates, add them to the form
        if (lat && lng) {
            // Create hidden inputs if they don't exist
            if (!searchForm.querySelector('input[name="lat"]')) {
                const latInput = document.createElement('input');
                latInput.type = 'hidden';
                latInput.name = 'lat';
                latInput.value = lat;
                searchForm.appendChild(latInput);
            }
            
            if (!searchForm.querySelector('input[name="lng"]')) {
                const lngInput = document.createElement('input');
                lngInput.type = 'hidden';
                lngInput.name = 'lng';
                lngInput.value = lng;
                searchForm.appendChild(lngInput);
            }
        }
        
        // Show loading state on submit button
        const submitBtn = searchForm.querySelector('button[type="submit"]');
        if (submitBtn) {
            const originalHTML = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
            submitBtn.disabled = true;
            
            // Re-enable after a delay (in case form submission fails)
            setTimeout(() => {
                submitBtn.innerHTML = originalHTML;
                submitBtn.disabled = false;
            }, 3000);
        }
    });
    
    // Add location input change handler
    locationInput.addEventListener('change', function() {
        const container = this.closest('.location-input-container');
        if (this.value.trim()) {
            container.classList.add('has-location');
        } else {
            container.classList.remove('has-location');
            // Clear coordinates
            document.getElementById('location_lat').value = '';
            document.getElementById('location_lng').value = '';
        }
    });
}

/**
 * Add demo functionality for testing (removed for production)
 */
function addDemoFunctionality() {
    // Demo functionality removed for cleaner homepage
    return;
}

/**
 * Add keyboard shortcuts for power users
 */
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + L to focus location input
    if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
        e.preventDefault();
        const locationInput = document.querySelector('input[name="location"]');
        if (locationInput) {
            locationInput.focus();
            locationInput.select();
        }
    }
    
    // Ctrl/Cmd + G to trigger geolocation
    if ((e.ctrlKey || e.metaKey) && e.key === 'g') {
        e.preventDefault();
        const geoButton = document.querySelector('.btn[title="Use my current location"]');
        if (geoButton) {
            geoButton.click();
        }
    }
});
