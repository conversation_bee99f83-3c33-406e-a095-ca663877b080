/**
 * API Documentation JavaScript
 * Phase 10: Advanced Features & API Development
 * Interactive functionality for API documentation
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeApiDocs();
});

function initializeApiDocs() {
    setupCodeTabs();
    setupEndpointToggle();
    setupApiTester();
    setupCopyButtons();
    setupSmoothScrolling();
    setupSidebarNavigation();
}

/**
 * Setup code example tabs
 */
function setupCodeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            const parentExample = this.closest('.code-example');
            
            // Remove active class from all tabs in this example
            parentExample.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            parentExample.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            parentExample.querySelector(`#${targetTab}`).classList.add('active');
        });
    });
}

/**
 * Setup endpoint expand/collapse functionality
 */
function setupEndpointToggle() {
    const endpointHeaders = document.querySelectorAll('.endpoint-header');
    
    endpointHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const endpoint = this.closest('.endpoint');
            endpoint.classList.toggle('expanded');
            
            // Smooth animation
            const details = endpoint.querySelector('.endpoint-details');
            if (endpoint.classList.contains('expanded')) {
                details.style.display = 'block';
                details.style.maxHeight = details.scrollHeight + 'px';
            } else {
                details.style.maxHeight = '0';
                setTimeout(() => {
                    details.style.display = 'none';
                }, 300);
            }
        });
    });
}

/**
 * Setup API tester functionality
 */
function setupApiTester() {
    const apiTesterBtn = document.getElementById('api-tester-btn');
    const apiTesterModal = new bootstrap.Modal(document.getElementById('apiTesterModal'));
    const testForm = document.getElementById('api-test-form');
    
    if (apiTesterBtn) {
        apiTesterBtn.addEventListener('click', function() {
            apiTesterModal.show();
        });
    }
    
    if (testForm) {
        testForm.addEventListener('submit', function(e) {
            e.preventDefault();
            executeApiTest();
        });
    }
}

/**
 * Execute API test request
 */
async function executeApiTest() {
    const method = document.getElementById('test-method').value;
    const endpoint = document.getElementById('test-endpoint').value;
    const apiKey = document.getElementById('test-api-key').value;
    const body = document.getElementById('test-body').value;
    const responseDiv = document.getElementById('test-response');
    const responseContent = document.getElementById('response-content');
    
    if (!apiKey) {
        alert('Please enter your API key');
        return;
    }
    
    // Show loading state
    const submitBtn = document.querySelector('#api-test-form button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner"></span> Sending...';
    submitBtn.disabled = true;
    
    try {
        const baseUrl = window.location.origin + '/beersty/api/v1';
        const url = baseUrl + endpoint;
        
        const options = {
            method: method,
            headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            }
        };
        
        if (method !== 'GET' && body.trim()) {
            try {
                JSON.parse(body); // Validate JSON
                options.body = body;
            } catch (e) {
                throw new Error('Invalid JSON in request body');
            }
        }
        
        const response = await fetch(url, options);
        const data = await response.json();
        
        // Format response
        const formattedResponse = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            data: data
        };
        
        responseContent.textContent = JSON.stringify(formattedResponse, null, 2);
        responseDiv.style.display = 'block';
        
        // Syntax highlighting if available
        if (typeof hljs !== 'undefined') {
            hljs.highlightElement(responseContent);
        }
        
    } catch (error) {
        responseContent.textContent = JSON.stringify({
            error: error.message,
            timestamp: new Date().toISOString()
        }, null, 2);
        responseDiv.style.display = 'block';
    } finally {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Setup copy to clipboard functionality
 */
function setupCopyButtons() {
    // Add copy buttons to code examples
    const codeExamples = document.querySelectorAll('.code-example');
    
    codeExamples.forEach(example => {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
        copyBtn.title = 'Copy to clipboard';
        
        copyBtn.addEventListener('click', function() {
            const activeTab = example.querySelector('.tab-content.active');
            const code = activeTab.querySelector('code').textContent;
            
            navigator.clipboard.writeText(code).then(() => {
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                    copyBtn.style.background = '';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        });
        
        example.appendChild(copyBtn);
    });
}

/**
 * Setup smooth scrolling for navigation
 */
function setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('.sidebar .nav-link[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active nav item
                updateActiveNavItem(this);
            }
        });
    });
}

/**
 * Setup sidebar navigation highlighting
 */
function setupSidebarNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.sidebar .nav-link[href^="#"]');
    
    // Intersection Observer for automatic highlighting
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const id = entry.target.id;
                const correspondingLink = document.querySelector(`.sidebar .nav-link[href="#${id}"]`);
                
                if (correspondingLink) {
                    updateActiveNavItem(correspondingLink);
                }
            }
        });
    }, {
        rootMargin: '-20% 0px -70% 0px'
    });
    
    sections.forEach(section => {
        observer.observe(section);
    });
}

/**
 * Update active navigation item
 */
function updateActiveNavItem(activeLink) {
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    activeLink.classList.add('active');
}

/**
 * Populate endpoint examples from current page
 */
function populateEndpointExample(endpoint, method = 'GET') {
    const testEndpoint = document.getElementById('test-endpoint');
    const testMethod = document.getElementById('test-method');
    
    if (testEndpoint) testEndpoint.value = endpoint;
    if (testMethod) testMethod.value = method;
}

/**
 * Format JSON response for display
 */
function formatJsonResponse(json) {
    try {
        return JSON.stringify(json, null, 2);
    } catch (e) {
        return json;
    }
}

/**
 * Add syntax highlighting to code blocks
 */
function highlightCodeBlocks() {
    if (typeof hljs !== 'undefined') {
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
        });
    }
}

/**
 * Setup endpoint quick-test buttons
 */
function setupQuickTestButtons() {
    const endpoints = document.querySelectorAll('.endpoint');
    
    endpoints.forEach(endpoint => {
        const header = endpoint.querySelector('.endpoint-header');
        const method = header.querySelector('.method').textContent;
        const path = header.querySelector('.path').textContent;
        
        // Add quick test button
        const quickTestBtn = document.createElement('button');
        quickTestBtn.className = 'btn btn-sm btn-outline-primary ms-auto';
        quickTestBtn.innerHTML = '<i class="fas fa-play me-1"></i>Test';
        quickTestBtn.title = 'Quick test this endpoint';
        
        quickTestBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            populateEndpointExample(path, method);
            
            const apiTesterModal = new bootstrap.Modal(document.getElementById('apiTesterModal'));
            apiTesterModal.show();
        });
        
        header.appendChild(quickTestBtn);
    });
}

/**
 * Setup search functionality
 */
function setupSearch() {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'Search endpoints...';
    
    const sidebar = document.querySelector('.sidebar-sticky');
    if (sidebar) {
        sidebar.insertBefore(searchInput, sidebar.firstChild.nextSibling);
    }
    
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const endpoints = document.querySelectorAll('.endpoint');
        
        endpoints.forEach(endpoint => {
            const text = endpoint.textContent.toLowerCase();
            const isVisible = text.includes(query);
            
            endpoint.style.display = isVisible ? 'block' : 'none';
        });
    });
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.sidebar input[type="text"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
}

/**
 * Initialize additional features
 */
function initializeAdditionalFeatures() {
    setupQuickTestButtons();
    setupSearch();
    setupKeyboardShortcuts();
    highlightCodeBlocks();
}

// Initialize additional features after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAdditionalFeatures, 100);
});

/**
 * Export functions for external use
 */
window.ApiDocs = {
    populateEndpointExample,
    formatJsonResponse,
    executeApiTest
};
