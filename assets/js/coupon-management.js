/**
 * Coupon Management JavaScript
 * Handle coupon creation, editing, and management
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeCouponManagement();
});

function initializeCouponManagement() {
    // Initialize form handlers
    initializeFormHandlers();
    
    // Initialize discount type preview
    initializeDiscountPreview();
    
    // Initialize date validation
    initializeDateValidation();
}

function initializeFormHandlers() {
    // Coupon creation form
    const createForm = document.getElementById('createCouponForm');
    if (createForm) {
        createForm.addEventListener('submit', handleCouponCreate);
    }
    
    // Coupon edit form
    const editForm = document.getElementById('editCouponForm');
    if (editForm) {
        editForm.addEventListener('submit', handleCouponEdit);
    }
}

function initializeDiscountPreview() {
    const discountType = document.getElementById('discount_type');
    const discountValue = document.getElementById('discount_value');
    const minimumPurchase = document.getElementById('minimum_purchase');
    
    if (discountType && discountValue) {
        [discountType, discountValue, minimumPurchase].forEach(element => {
            if (element) {
                element.addEventListener('change', updateDiscountPreview);
                element.addEventListener('input', updateDiscountPreview);
            }
        });
        
        // Initial preview
        updateDiscountPreview();
    }
}

function updateDiscountPreview() {
    const discountType = document.getElementById('discount_type')?.value;
    const discountValue = parseFloat(document.getElementById('discount_value')?.value) || 0;
    const minimumPurchase = parseFloat(document.getElementById('minimum_purchase')?.value) || 0;
    
    const preview = document.getElementById('discountPreview');
    if (!preview) return;
    
    let previewText = '';
    let previewDescription = '';
    
    switch (discountType) {
        case 'percentage':
            previewText = `${discountValue}% OFF`;
            previewDescription = minimumPurchase > 0 
                ? `${discountValue}% discount on orders over $${minimumPurchase}`
                : `${discountValue}% discount on your order`;
            break;
        case 'fixed_amount':
            previewText = `$${discountValue} OFF`;
            previewDescription = minimumPurchase > 0 
                ? `$${discountValue} off orders over $${minimumPurchase}`
                : `$${discountValue} off your order`;
            break;
        case 'buy_x_get_y':
            previewText = 'BOGO';
            previewDescription = 'Buy one, get one special offer';
            break;
        case 'free_item':
            previewText = 'FREE ITEM';
            previewDescription = minimumPurchase > 0 
                ? `Free item with orders over $${minimumPurchase}`
                : 'Free item with your order';
            break;
        default:
            previewText = 'DISCOUNT';
            previewDescription = 'Special offer discount';
    }
    
    preview.innerHTML = `
        <div class="preview-value">${previewText}</div>
        <div class="preview-description">${previewDescription}</div>
    `;
}

function initializeDateValidation() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (startDate && endDate) {
        startDate.addEventListener('change', validateDates);
        endDate.addEventListener('change', validateDates);
        
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        startDate.min = today;
        endDate.min = today;
    }
}

function validateDates() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (startDate.value && endDate.value) {
        if (new Date(endDate.value) <= new Date(startDate.value)) {
            endDate.setCustomValidity('End date must be after start date');
        } else {
            endDate.setCustomValidity('');
        }
    }
}

function handleCouponCreate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    formData.append('action', 'create');
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;
    
    fetch('../api/coupon-management.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Coupon created successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to create coupon: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Create error:', error);
        showNotification('Failed to create coupon: ' + error.message, 'error');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function handleCouponEdit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    formData.append('action', 'update');
    
    fetch('../api/coupon-management.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Coupon updated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to update coupon: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Update error:', error);
        showNotification('Failed to update coupon', 'error');
    });
}

function editCoupon(couponId) {
    fetch(`../api/coupon-management.php?action=get&id=${couponId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showEditCouponModal(data.coupon);
        } else {
            showNotification('Failed to load coupon details', 'error');
        }
    })
    .catch(error => {
        console.error('Error loading coupon:', error);
        showNotification('Failed to load coupon', 'error');
    });
}

function showEditCouponModal(coupon) {
    // Create or update edit modal
    let modal = document.getElementById('editCouponModal');
    if (!modal) {
        modal = createEditCouponModal();
    }
    
    // Populate form with coupon data
    const form = modal.querySelector('#editCouponForm');
    form.querySelector('#edit_coupon_id').value = coupon.id;
    form.querySelector('#edit_title').value = coupon.title;
    form.querySelector('#edit_description').value = coupon.description || '';
    form.querySelector('#edit_code').value = coupon.code;
    form.querySelector('#edit_discount_type').value = coupon.discount_type;
    form.querySelector('#edit_discount_value').value = coupon.discount_value;
    form.querySelector('#edit_minimum_purchase').value = coupon.minimum_purchase || '';
    form.querySelector('#edit_start_date').value = coupon.start_date;
    form.querySelector('#edit_end_date').value = coupon.end_date;
    form.querySelector('#edit_usage_limit').value = coupon.usage_limit || '';
    form.querySelector('#edit_is_active').checked = coupon.is_active == 1;
    form.querySelector('#edit_is_featured').checked = coupon.is_featured == 1;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

function createEditCouponModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'editCouponModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Coupon</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editCouponForm" class="coupon-form">
                        <input type="hidden" id="edit_coupon_id" name="coupon_id">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="edit_title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_code" class="form-label">Coupon Code *</label>
                                <input type="text" class="form-control" id="edit_code" name="code" required>
                            </div>
                            <div class="col-12">
                                <label for="edit_description" class="form-label">Description</label>
                                <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_discount_type" class="form-label">Discount Type *</label>
                                <select class="form-select" id="edit_discount_type" name="discount_type" required>
                                    <option value="percentage">Percentage Off</option>
                                    <option value="fixed_amount">Fixed Amount Off</option>
                                    <option value="buy_x_get_y">Buy X Get Y</option>
                                    <option value="free_item">Free Item</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_discount_value" class="form-label">Discount Value *</label>
                                <input type="number" class="form-control" id="edit_discount_value" name="discount_value" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_minimum_purchase" class="form-label">Minimum Purchase</label>
                                <input type="number" class="form-control" id="edit_minimum_purchase" name="minimum_purchase" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="edit_start_date" class="form-label">Start Date *</label>
                                <input type="date" class="form-control" id="edit_start_date" name="start_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_end_date" class="form-label">End Date *</label>
                                <input type="date" class="form-control" id="edit_end_date" name="end_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_usage_limit" class="form-label">Usage Limit</label>
                                <input type="number" class="form-control" id="edit_usage_limit" name="usage_limit" min="1">
                                <small class="text-muted">Leave empty for unlimited uses</small>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active" value="1">
                                    <label class="form-check-label" for="edit_is_active">Active</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="edit_is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="edit_is_featured">Featured</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="editCouponForm" class="btn btn-primary">Update Coupon</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    return modal;
}

function deleteCoupon(couponId) {
    if (!confirm('Are you sure you want to delete this coupon? This action cannot be undone.')) {
        return;
    }
    
    fetch('../api/coupon-management.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'delete',
            id: couponId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Coupon deleted successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to delete coupon: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        showNotification('Failed to delete coupon', 'error');
    });
}

function approveCoupon(couponId) {
    fetch('../api/coupon-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'approve',
            id: couponId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Coupon approved successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to approve coupon: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Approve error:', error);
        showNotification('Failed to approve coupon', 'error');
    });
}

function toggleCouponStatus(couponId, isActive) {
    const action = isActive ? 'activate' : 'deactivate';
    
    fetch('../api/coupon-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            id: couponId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Coupon ${action}d successfully!`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification(`Failed to ${action} coupon: ` + data.message, 'error');
        }
    })
    .catch(error => {
        console.error(`${action} error:`, error);
        showNotification(`Failed to ${action} coupon`, 'error');
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function generateCouponCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Auto-generate coupon code
function autoGenerateCode() {
    const codeInput = document.getElementById('code') || document.getElementById('edit_code');
    if (codeInput) {
        codeInput.value = generateCouponCode();
    }
}
