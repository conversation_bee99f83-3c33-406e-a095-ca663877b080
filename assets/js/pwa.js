/**
 * PWA JavaScript
 * Phase 9: Design & Mobile Optimization
 * Progressive Web App functionality
 */

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        this.swRegistration = null;
        
        this.init();
    }
    
    init() {
        this.checkInstallStatus();
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupOfflineHandling();
        this.setupUpdateHandling();
        this.setupNotifications();
        this.setupBackgroundSync();
        this.setupSharing();
        
        // Show splash screen on first load
        this.showSplashScreen();
    }
    
    checkInstallStatus() {
        // Check if app is installed (standalone mode)
        this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone === true;
        
        if (this.isInstalled) {
            document.body.classList.add('pwa-installed');
            console.log('PWA is installed');
        }
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.swRegistration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered:', this.swRegistration);
                
                // Listen for updates
                this.swRegistration.addEventListener('updatefound', () => {
                    this.handleServiceWorkerUpdate();
                });
                
                // Check for existing service worker
                if (this.swRegistration.active) {
                    console.log('Service Worker is active');
                }
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }
    
    setupInstallPrompt() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallPrompt();
        });
        
        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.isInstalled = true;
            this.hideInstallPrompt();
            this.showInstallSuccess();
        });
    }
    
    showInstallPrompt() {
        if (this.isInstalled || !this.deferredPrompt) return;
        
        // Check if user dismissed recently
        const dismissed = localStorage.getItem('pwa-install-dismissed');
        if (dismissed && Date.now() - parseInt(dismissed) < 24 * 60 * 60 * 1000) {
            return; // Don't show for 24 hours
        }
        
        const prompt = document.createElement('div');
        prompt.className = 'pwa-install-prompt';
        prompt.innerHTML = `
            <div class="install-content">
                <div class="install-text">
                    <div class="install-title">Install Beersty</div>
                    <div class="install-description">Get the full app experience with offline access</div>
                </div>
                <div class="install-actions">
                    <button class="btn btn-install" onclick="pwaManager.installApp()">Install</button>
                    <button class="btn btn-dismiss" onclick="pwaManager.dismissInstallPrompt()">Later</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(prompt);
        
        // Show with animation
        setTimeout(() => {
            prompt.classList.add('show');
        }, 100);
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            this.dismissInstallPrompt();
        }, 10000);
    }
    
    async installApp() {
        if (!this.deferredPrompt) return;
        
        try {
            this.deferredPrompt.prompt();
            const result = await this.deferredPrompt.userChoice;
            
            if (result.outcome === 'accepted') {
                console.log('User accepted install prompt');
            } else {
                console.log('User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
            this.hideInstallPrompt();
            
        } catch (error) {
            console.error('Install failed:', error);
        }
    }
    
    dismissInstallPrompt() {
        this.hideInstallPrompt();
        
        // Don't show again for 24 hours
        localStorage.setItem('pwa-install-dismissed', Date.now().toString());
    }
    
    hideInstallPrompt() {
        const prompt = document.querySelector('.pwa-install-prompt');
        if (prompt) {
            prompt.classList.remove('show');
            setTimeout(() => {
                prompt.remove();
            }, 300);
        }
    }
    
    showInstallSuccess() {
        this.showNotification({
            title: 'App Installed!',
            body: 'Beersty has been installed successfully. Enjoy the full app experience!',
            icon: '/beersty/assets/icons/icon-192x192.png'
        });
    }
    
    setupOfflineHandling() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOfflineStatus();
        });
        
        // Initial status
        this.updateOfflineIndicator();
    }
    
    handleOnlineStatus() {
        console.log('App is online');
        this.updateOfflineIndicator();
        this.syncOfflineData();
        
        // Hide offline banner if shown
        const banner = document.querySelector('.pwa-offline-banner');
        if (banner) {
            banner.remove();
        }
    }
    
    handleOfflineStatus() {
        console.log('App is offline');
        this.updateOfflineIndicator();
        this.showOfflineBanner();
    }
    
    updateOfflineIndicator() {
        let indicator = document.querySelector('.pwa-offline-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'pwa-offline-indicator';
            document.body.appendChild(indicator);
        }
        
        if (this.isOnline) {
            indicator.innerHTML = '<i class="fas fa-wifi status-icon"></i>Back online';
            indicator.classList.add('online');
            indicator.classList.add('show');
            
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        } else {
            indicator.innerHTML = '<i class="fas fa-wifi-slash status-icon"></i>You\'re offline';
            indicator.classList.remove('online');
            indicator.classList.add('show');
        }
    }
    
    showOfflineBanner() {
        const banner = document.createElement('div');
        banner.className = 'pwa-offline-banner';
        banner.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <span>You're offline. Some features may be limited.</span>
                <button class="btn btn-sm btn-light" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.insertBefore(banner, document.body.firstChild);
    }
    
    handleServiceWorkerUpdate() {
        const newWorker = this.swRegistration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.showUpdateBanner();
            }
        });
    }
    
    showUpdateBanner() {
        const banner = document.createElement('div');
        banner.className = 'pwa-update-banner';
        banner.innerHTML = `
            <span class="update-text">A new version is available!</span>
            <button class="btn" onclick="pwaManager.applyUpdate()">Update</button>
        `;
        
        document.body.insertBefore(banner, document.body.firstChild);
        
        setTimeout(() => {
            banner.classList.add('show');
        }, 100);
    }
    
    applyUpdate() {
        if (this.swRegistration && this.swRegistration.waiting) {
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                window.location.reload();
            });
        }
    }
    
    setupUpdateHandling() {
        // Listen for service worker updates
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
                    this.showUpdateBanner();
                }
            });
        }
    }
    
    async setupNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            // Request permission if not granted
            if (Notification.permission === 'default') {
                await this.requestNotificationPermission();
            }
            
            // Setup push notifications if supported
            if ('PushManager' in window) {
                this.setupPushNotifications();
            }
        }
    }
    
    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                console.log('Notification permission granted');
                this.showNotification({
                    title: 'Notifications Enabled',
                    body: 'You\'ll now receive updates about your beer journey!',
                    icon: '/beersty/assets/icons/icon-192x192.png'
                });
            } else {
                console.log('Notification permission denied');
            }
            
        } catch (error) {
            console.error('Error requesting notification permission:', error);
        }
    }
    
    showNotification(options) {
        if (Notification.permission === 'granted') {
            const notification = new Notification(options.title, {
                body: options.body,
                icon: options.icon || '/beersty/assets/icons/icon-192x192.png',
                badge: '/beersty/assets/icons/badge-72x72.png',
                tag: options.tag || 'beersty-notification',
                requireInteraction: false,
                silent: false
            });
            
            // Auto-close after 5 seconds
            setTimeout(() => {
                notification.close();
            }, 5000);
            
            return notification;
        }
    }
    
    async setupPushNotifications() {
        if (!this.swRegistration) return;
        
        try {
            // Check if already subscribed
            const existingSubscription = await this.swRegistration.pushManager.getSubscription();
            
            if (!existingSubscription && Notification.permission === 'granted') {
                console.log('Push notifications available but not implemented yet');
                // Would implement VAPID key subscription here
            }
            
        } catch (error) {
            console.error('Push notification setup failed:', error);
        }
    }
    
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Register for background sync when offline actions occur
            window.addEventListener('offline-action', (event) => {
                this.registerBackgroundSync(event.detail.type);
            });
        }
    }
    
    async registerBackgroundSync(tag) {
        if (this.swRegistration) {
            try {
                await this.swRegistration.sync.register(tag);
                console.log('Background sync registered:', tag);
            } catch (error) {
                console.error('Background sync registration failed:', error);
            }
        }
    }
    
    setupSharing() {
        // Setup Web Share API
        if (navigator.share) {
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('pwa-share-btn')) {
                    e.preventDefault();
                    this.shareContent(e.target.dataset);
                }
            });
        } else {
            // Fallback for browsers without Web Share API
            this.setupShareFallback();
        }
    }
    
    async shareContent(data) {
        try {
            await navigator.share({
                title: data.title || 'Beersty',
                text: data.text || 'Check out this beer on Beersty!',
                url: data.url || window.location.href
            });
        } catch (error) {
            console.error('Sharing failed:', error);
            this.fallbackShare(data);
        }
    }
    
    setupShareFallback() {
        // Add fallback share buttons for browsers without Web Share API
        const shareButtons = document.querySelectorAll('.pwa-share-btn');
        
        shareButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.fallbackShare(button.dataset);
            });
        });
    }
    
    fallbackShare(data) {
        // Copy to clipboard as fallback
        const text = `${data.title || 'Beersty'}\n${data.text || ''}\n${data.url || window.location.href}`;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showNotification({
                    title: 'Link Copied',
                    body: 'Share link has been copied to clipboard'
                });
            });
        }
    }
    
    showSplashScreen() {
        // Only show splash on first load or when installed
        if (sessionStorage.getItem('splash-shown') && !this.isInstalled) return;
        
        const splash = document.createElement('div');
        splash.className = 'pwa-splash';
        splash.innerHTML = `
            <div class="pwa-splash-logo">
                <i class="fas fa-beer"></i>
            </div>
            <div class="pwa-splash-title">Beersty</div>
            <div class="pwa-splash-subtitle">Your Social Beer Journey</div>
        `;
        
        document.body.appendChild(splash);
        
        // Hide splash after 2 seconds
        setTimeout(() => {
            splash.classList.add('hide');
            setTimeout(() => {
                splash.remove();
            }, 500);
        }, 2000);
        
        sessionStorage.setItem('splash-shown', 'true');
    }
    
    async syncOfflineData() {
        // Sync any offline data when back online
        if ('serviceWorker' in navigator && this.swRegistration) {
            try {
                await this.swRegistration.sync.register('background-sync-all');
                this.showSyncStatus('Syncing offline data...');
            } catch (error) {
                console.error('Sync failed:', error);
            }
        }
    }
    
    showSyncStatus(message, type = 'syncing') {
        let status = document.querySelector('.pwa-sync-status');
        
        if (!status) {
            status = document.createElement('div');
            status.className = 'pwa-sync-status';
            document.body.appendChild(status);
        }
        
        const iconClass = type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : 'fa-sync-alt';
        
        status.innerHTML = `
            <i class="fas ${iconClass} sync-icon"></i>
            <span class="sync-text">${message}</span>
        `;
        
        status.className = `pwa-sync-status ${type}`;
        status.classList.add('show');
        
        if (type !== 'syncing') {
            setTimeout(() => {
                status.classList.remove('show');
            }, 3000);
        }
    }
}

// Initialize PWA manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAManager;
}
