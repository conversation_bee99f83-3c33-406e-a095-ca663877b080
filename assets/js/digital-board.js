/**
 * Digital Beer Board Management JavaScript
 * Handles digital board operations and real-time updates
 */

class DigitalBoardManager {
    constructor() {
        this.breweryId = null;
        this.boardId = null;
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        // Get brewery ID from page
        const breweryIdElement = document.querySelector('[data-brewery-id]');
        if (breweryIdElement) {
            this.breweryId = breweryIdElement.dataset.breweryId;
        }
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Load initial data
        this.loadBoardInfo();
        
        // Set up auto-refresh for menu data
        this.startAutoRefresh();
    }
    
    setupEventListeners() {
        // Board toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="toggle-board"]')) {
                const boardId = e.target.dataset.boardId;
                const isActive = e.target.dataset.isActive === 'true';
                this.toggleBoard(boardId, !isActive);
            }
        });
        
        // Beer availability toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="toggle-beer"]')) {
                const beerId = e.target.dataset.beerId;
                const isAvailable = e.target.dataset.isAvailable === 'true';
                this.toggleBeerAvailability(beerId, !isAvailable);
            }
        });
        
        // Settings form
        const settingsForm = document.getElementById('settingsForm');
        if (settingsForm) {
            settingsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
        }
        
        // Refresh display button
        const refreshBtn = document.querySelector('[data-action="refresh-display"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshDisplay();
            });
        }
    }
    
    async loadBoardInfo() {
        try {
            const response = await fetch(`/api/digital-board.php?action=board_info&brewery_id=${this.breweryId}`);
            const data = await response.json();
            
            if (data.success && data.board) {
                this.boardId = data.board.id;
                this.updateBoardStatus(data.board);
                this.populateSettings(data.board.settings);
            }
        } catch (error) {
            console.error('Error loading board info:', error);
            this.showNotification('Failed to load board information', 'error');
        }
    }
    
    async createBoard() {
        try {
            const response = await fetch('/api/digital-board.php?action=create_board', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    brewery_id: this.breweryId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Digital board created successfully!', 'success');
                this.boardId = data.board_id;
                
                // Update UI
                this.updateBoardUrl(data.board_url);
                
                // Reload page to show new board
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotification(data.message || 'Failed to create board', 'error');
            }
        } catch (error) {
            console.error('Error creating board:', error);
            this.showNotification('Failed to create digital board', 'error');
        }
    }
    
    async toggleBoard(boardId, isActive) {
        try {
            const response = await fetch('/api/digital-board.php?action=toggle_board', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    board_id: boardId,
                    is_active: isActive
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(
                    `Board ${isActive ? 'enabled' : 'disabled'} successfully!`, 
                    'success'
                );
                
                // Update UI
                this.updateBoardStatusUI(isActive);
            } else {
                this.showNotification(data.message || 'Failed to update board status', 'error');
            }
        } catch (error) {
            console.error('Error toggling board:', error);
            this.showNotification('Failed to update board status', 'error');
        }
    }
    
    async toggleBeerAvailability(beerId, isAvailable) {
        try {
            const response = await fetch('/api/digital-board.php?action=toggle_beer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    beer_id: beerId,
                    is_available: isAvailable
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification(
                    `Beer ${isAvailable ? 'enabled' : 'disabled'} successfully!`, 
                    'success'
                );
                
                // Update UI
                this.updateBeerStatusUI(beerId, isAvailable);
                
                // Refresh display if board is active
                this.refreshDisplay();
            } else {
                this.showNotification(data.message || 'Failed to update beer availability', 'error');
            }
        } catch (error) {
            console.error('Error toggling beer availability:', error);
            this.showNotification('Failed to update beer availability', 'error');
        }
    }
    
    async saveSettings() {
        if (!this.boardId) {
            this.showNotification('No board configured', 'error');
            return;
        }
        
        const form = document.getElementById('settingsForm');
        const formData = new FormData(form);
        
        const settings = {
            theme: formData.get('theme'),
            layout: formData.get('layout'),
            refresh_interval: parseInt(formData.get('refresh_interval')),
            show_prices: formData.get('show_prices') === 'on',
            show_descriptions: formData.get('show_descriptions') === 'on',
            ticker_message: formData.get('ticker_message'),
            auto_rotate: formData.get('auto_rotate') === 'on',
            display_time: parseInt(formData.get('display_time'))
        };
        
        try {
            const response = await fetch('/api/digital-board.php?action=update_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    board_id: this.boardId,
                    settings: settings
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Settings saved successfully!', 'success');
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Refresh display
                this.refreshDisplay();
            } else {
                this.showNotification(data.message || 'Failed to save settings', 'error');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Failed to save settings', 'error');
        }
    }
    
    async refreshDisplay() {
        try {
            // Notify any open display windows to refresh
            if (window.opener) {
                window.opener.postMessage('refresh', '*');
            }
            
            // Update timestamp
            const timestampElement = document.querySelector('.last-updated');
            if (timestampElement) {
                timestampElement.textContent = 'Updated: ' + new Date().toLocaleTimeString();
            }
            
            this.showNotification('Display refreshed', 'info');
        } catch (error) {
            console.error('Error refreshing display:', error);
        }
    }
    
    updateBoardStatus(board) {
        const statusElement = document.querySelector('.board-status');
        if (statusElement) {
            statusElement.innerHTML = board.is_active ? 
                '<span class="badge bg-success">ACTIVE</span>' : 
                '<span class="badge bg-secondary">INACTIVE</span>';
        }
        
        const toggleBtn = document.querySelector('[data-action="toggle-board"]');
        if (toggleBtn) {
            toggleBtn.className = `btn btn-${board.is_active ? 'warning' : 'success'}`;
            toggleBtn.innerHTML = `<i class="fas fa-power-off me-2"></i>${board.is_active ? 'Disable' : 'Enable'} Board`;
            toggleBtn.dataset.isActive = board.is_active;
            toggleBtn.dataset.boardId = board.id;
        }
    }
    
    updateBoardStatusUI(isActive) {
        const statusElement = document.querySelector('.board-status');
        if (statusElement) {
            statusElement.innerHTML = isActive ? 
                '<span class="badge bg-success">ACTIVE</span>' : 
                '<span class="badge bg-secondary">INACTIVE</span>';
        }
        
        const toggleBtn = document.querySelector('[data-action="toggle-board"]');
        if (toggleBtn) {
            toggleBtn.className = `btn btn-${isActive ? 'warning' : 'success'}`;
            toggleBtn.innerHTML = `<i class="fas fa-power-off me-2"></i>${isActive ? 'Disable' : 'Enable'} Board`;
            toggleBtn.dataset.isActive = isActive;
        }
    }
    
    updateBeerStatusUI(beerId, isAvailable) {
        const beerRow = document.querySelector(`[data-beer-id="${beerId}"]`);
        if (beerRow) {
            const statusBadge = beerRow.querySelector('.status-badge');
            if (statusBadge) {
                statusBadge.className = `badge bg-${isAvailable ? 'success' : 'secondary'}`;
                statusBadge.textContent = isAvailable ? 'Available' : 'Unavailable';
            }
            
            const toggleBtn = beerRow.querySelector('[data-action="toggle-beer"]');
            if (toggleBtn) {
                toggleBtn.className = `btn btn-outline-${isAvailable ? 'warning' : 'success'} btn-sm`;
                toggleBtn.innerHTML = `<i class="fas fa-${isAvailable ? 'eye-slash' : 'eye'}"></i>`;
                toggleBtn.dataset.isAvailable = isAvailable;
            }
        }
    }
    
    updateBoardUrl(url) {
        const urlElements = document.querySelectorAll('.board-url');
        urlElements.forEach(element => {
            element.textContent = url;
            element.href = url;
        });
    }
    
    populateSettings(settings) {
        if (!settings) return;
        
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        // Populate form fields
        Object.keys(settings).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = settings[key];
                } else {
                    field.value = settings[key];
                }
            }
        });
    }
    
    startAutoRefresh() {
        // Refresh menu data every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.loadMenuData();
        }, 300000);
    }
    
    async loadMenuData() {
        try {
            const response = await fetch(`/api/digital-board.php?action=menu_data&brewery_id=${this.breweryId}`);
            const data = await response.json();
            
            if (data.success) {
                // Update last updated timestamp
                const timestampElement = document.querySelector('.last-updated');
                if (timestampElement) {
                    timestampElement.textContent = 'Updated: ' + new Date(data.last_updated).toLocaleTimeString();
                }
            }
        } catch (error) {
            console.error('Error loading menu data:', error);
        }
    }
    
    generateQRCode() {
        if (!this.boardId) {
            this.showNotification('No board configured', 'error');
            return;
        }
        
        const boardUrl = `${window.location.origin}/business/digital-board/display.php?brewery_id=${this.breweryId}&board_id=${this.boardId}`;
        
        // Open QR code generator (you can integrate with a QR code library)
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(boardUrl)}`;
        
        // Show QR code in modal or new window
        const qrModal = document.getElementById('qrModal');
        if (qrModal) {
            const qrImage = qrModal.querySelector('.qr-image');
            if (qrImage) {
                qrImage.src = qrUrl;
            }
            
            const modal = new bootstrap.Modal(qrModal);
            modal.show();
        } else {
            window.open(qrUrl, '_blank');
        }
    }
    
    exportMenu() {
        if (!this.breweryId) {
            this.showNotification('No brewery ID available', 'error');
            return;
        }
        
        // Create download link for CSV export
        const exportUrl = `/api/digital-board.php?action=export_menu&brewery_id=${this.breweryId}&format=csv`;
        
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `beer_menu_${this.breweryId}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showNotification('Menu exported successfully', 'success');
    }
    
    showNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add to page
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// Global functions for inline event handlers
function createBoard() {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.createBoard();
    }
}

function toggleBoard(boardId, isActive) {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.toggleBoard(boardId, isActive);
    }
}

function toggleAvailability(beerId, isAvailable) {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.toggleBeerAvailability(beerId, isAvailable);
    }
}

function refreshDisplay() {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.refreshDisplay();
    }
}

function generateQRCode() {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.generateQRCode();
    }
}

function exportMenu() {
    if (window.digitalBoardManager) {
        window.digitalBoardManager.exportMenu();
    }
}

function editBeer(beerId) {
    // Redirect to beer edit page
    window.location.href = `/business/menu/edit.php?id=${beerId}`;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.digitalBoardManager = new DigitalBoardManager();
});
