/**
 * Universal Image Fallback System
 * Handles image loading errors and provides consistent placeholder images
 */

// Default placeholder images for different types
const PLACEHOLDER_IMAGES = {
    place: '/placeholders/450x300_beer_placeholder1.jpg',
    beer: '/placeholders/450x300_beer_placeholder2.jpg',
    brewery: '/placeholders/450x300_beer_placeholder3.jpg',
    default: '/placeholders/450x300_beer_placeholder1.jpg'
};

/**
 * Handle image loading errors
 * @param {HTMLImageElement} img - The image element that failed to load
 * @param {string} type - Type of placeholder to use (place, beer, brewery, default)
 */
function handleImageError(img, type = 'default') {
    if (img.dataset.fallbackAttempted) {
        // If fallback already attempted, show CSS placeholder
        img.style.display = 'none';
        const placeholder = createPlaceholderDiv(img);
        img.parentNode.insertBefore(placeholder, img);
        return;
    }
    
    // Mark as fallback attempted
    img.dataset.fallbackAttempted = 'true';
    
    // Try placeholder image
    const placeholderUrl = PLACEHOLDER_IMAGES[type] || PLACEHOLDER_IMAGES.default;
    img.src = placeholderUrl;
}

/**
 * Create a CSS-based placeholder div
 * @param {HTMLImageElement} img - The original image element
 * @returns {HTMLDivElement} - Placeholder div element
 */
function createPlaceholderDiv(img) {
    const placeholder = document.createElement('div');
    placeholder.className = 'image-placeholder';
    placeholder.style.cssText = `
        width: 100%;
        height: ${img.style.height || '200px'};
        background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #D69A6B;
        font-size: 3rem;
        border-radius: 0.5rem 0.5rem 0 0;
    `;
    placeholder.innerHTML = '🍺';
    return placeholder;
}

/**
 * Initialize image fallback system
 */
function initImageFallback() {
    // Add error handlers to existing images
    document.querySelectorAll('img[data-type]').forEach(img => {
        img.addEventListener('error', function() {
            handleImageError(this, this.dataset.type);
        });
    });
    
    // Add error handlers to images without data-type
    document.querySelectorAll('img:not([data-type])').forEach(img => {
        // Determine type based on class or context
        let type = 'default';
        if (img.classList.contains('place-img') || img.closest('.place-card')) {
            type = 'place';
        } else if (img.classList.contains('beer-image') || img.closest('.beer-card')) {
            type = 'beer';
        } else if (img.classList.contains('brewery-image') || img.closest('.brewery-card')) {
            type = 'brewery';
        }
        
        img.addEventListener('error', function() {
            handleImageError(this, type);
        });
    });
}

/**
 * Preload placeholder images
 */
function preloadPlaceholders() {
    Object.values(PLACEHOLDER_IMAGES).forEach(url => {
        const img = new Image();
        img.src = url;
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    preloadPlaceholders();
    initImageFallback();
});

// Re-initialize when new content is added dynamically
window.reinitImageFallback = initImageFallback;
