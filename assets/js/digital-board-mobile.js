/**
 * Digital Board Mobile JavaScript
 * Phase 6 - Mobile & Responsive Optimization
 * 
 * Mobile-optimized interactions for digital board management
 */

class DigitalBoardMobile {
    constructor() {
        this.isMobile = this.detectMobile();
        this.isTouch = this.detectTouch();
        this.viewport = this.getViewport();
        this.activeModal = null;
        this.swipeThreshold = 50;
        this.touchStartX = 0;
        this.touchStartY = 0;
        
        this.init();
    }
    
    init() {
        if (!this.isMobile) return;
        
        this.setupMobileLayout();
        this.setupTouchInteractions();
        this.setupMobileNavigation();
        this.setupMobileModals();
        this.setupMobileForms();
        this.setupMobileCards();
        this.setupPullToRefresh();
        this.setupVirtualKeyboard();
        this.setupMobileSearch();
        
        // Add mobile class to body
        document.body.classList.add('mobile-device');
        
        if (this.isTouch) {
            document.body.classList.add('touch-device');
        }
        
        // Setup orientation change handling
        this.setupOrientationChange();
    }
    
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }
    
    detectTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    getViewport() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
        };
    }
    
    setupMobileLayout() {
        // Create mobile header if it doesn't exist
        if (!document.querySelector('.mobile-header')) {
            this.createMobileHeader();
        }
        
        // Create mobile bottom navigation
        if (!document.querySelector('.mobile-bottom-nav')) {
            this.createMobileBottomNav();
        }
        
        // Wrap content in mobile container
        this.wrapContentForMobile();
    }
    
    createMobileHeader() {
        const header = document.createElement('div');
        header.className = 'mobile-header';
        
        // Get page title
        const pageTitle = document.title.split(' - ')[0] || 'Digital Board';
        
        header.innerHTML = `
            <img src="../../assets/images/beersty-logo.png" alt="Beersty" class="logo">
            <div class="title">${pageTitle}</div>
            <div class="actions">
                <button class="mobile-header-btn" onclick="digitalBoardMobile.toggleMobileMenu()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        `;
        
        document.body.insertBefore(header, document.body.firstChild);
    }
    
    createMobileBottomNav() {
        const nav = document.createElement('nav');
        nav.className = 'mobile-bottom-nav';
        
        // Determine current page for active state
        const currentPath = window.location.pathname;
        
        nav.innerHTML = `
            <a href="index.php" class="mobile-nav-item ${currentPath.includes('index.php') ? 'active' : ''}">
                <i class="fas fa-tachometer-alt mobile-nav-icon"></i>
                <span class="mobile-nav-text">Dashboard</span>
            </a>
            <a href="templates.php" class="mobile-nav-item ${currentPath.includes('templates.php') ? 'active' : ''}">
                <i class="fas fa-layer-group mobile-nav-icon"></i>
                <span class="mobile-nav-text">Templates</span>
            </a>
            <a href="slideshow-builder.php" class="mobile-nav-item ${currentPath.includes('slideshow') ? 'active' : ''}">
                <i class="fas fa-play-circle mobile-nav-icon"></i>
                <span class="mobile-nav-text">Slideshows</span>
            </a>
            <a href="media-manager.php" class="mobile-nav-item ${currentPath.includes('media') ? 'active' : ''}">
                <i class="fas fa-images mobile-nav-icon"></i>
                <span class="mobile-nav-text">Media</span>
            </a>
            <a href="user-management.php" class="mobile-nav-item ${currentPath.includes('user') ? 'active' : ''}">
                <i class="fas fa-users mobile-nav-icon"></i>
                <span class="mobile-nav-text">Users</span>
            </a>
        `;
        
        document.body.appendChild(nav);
    }
    
    wrapContentForMobile() {
        const existingContent = document.querySelector('.container, .container-fluid, main');
        if (existingContent && !existingContent.classList.contains('mobile-content')) {
            existingContent.classList.add('mobile-content');
        }
    }
    
    setupTouchInteractions() {
        // Setup swipe gestures for cards and modals
        this.setupSwipeGestures();
        
        // Setup touch feedback
        this.setupTouchFeedback();
        
        // Setup long press interactions
        this.setupLongPress();
    }
    
    setupSwipeGestures() {
        document.addEventListener('touchstart', (e) => {
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (!this.touchStartX || !this.touchStartY) return;
            
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - this.touchStartX;
            const deltaY = touchEndY - this.touchStartY;
            
            // Horizontal swipe
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > this.swipeThreshold) {
                if (deltaX > 0) {
                    this.handleSwipeRight(e.target);
                } else {
                    this.handleSwipeLeft(e.target);
                }
            }
            
            // Vertical swipe
            if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > this.swipeThreshold) {
                if (deltaY > 0) {
                    this.handleSwipeDown(e.target);
                } else {
                    this.handleSwipeUp(e.target);
                }
            }
            
            this.touchStartX = 0;
            this.touchStartY = 0;
        }, { passive: true });
    }
    
    handleSwipeRight(target) {
        // Close modal or go back
        if (this.activeModal) {
            this.closeMobileModal(this.activeModal);
        } else {
            // Navigate back if possible
            if (window.history.length > 1) {
                window.history.back();
            }
        }
    }
    
    handleSwipeLeft(target) {
        // Open actions menu for cards
        const card = target.closest('.mobile-card, .card');
        if (card) {
            this.showCardActions(card);
        }
    }
    
    handleSwipeDown(target) {
        // Close modal
        if (this.activeModal) {
            this.closeMobileModal(this.activeModal);
        }
    }
    
    handleSwipeUp(target) {
        // Show more options or expand content
        const card = target.closest('.mobile-card, .card');
        if (card) {
            this.expandCard(card);
        }
    }
    
    setupTouchFeedback() {
        // Add touch feedback to interactive elements
        const interactiveElements = document.querySelectorAll(
            '.mobile-btn, .mobile-nav-item, .mobile-card, .btn, .card'
        );
        
        interactiveElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.classList.add('touch-active');
            }, { passive: true });
            
            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            }, { passive: true });
        });
    }
    
    setupLongPress() {
        let longPressTimer;
        
        document.addEventListener('touchstart', (e) => {
            longPressTimer = setTimeout(() => {
                this.handleLongPress(e.target);
            }, 500);
        }, { passive: true });
        
        document.addEventListener('touchend', () => {
            clearTimeout(longPressTimer);
        }, { passive: true });
        
        document.addEventListener('touchmove', () => {
            clearTimeout(longPressTimer);
        }, { passive: true });
    }
    
    handleLongPress(target) {
        // Show context menu for cards
        const card = target.closest('.mobile-card, .card');
        if (card) {
            this.showContextMenu(card, target);
        }
        
        // Haptic feedback if available
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
    
    setupMobileNavigation() {
        // Handle mobile menu toggle
        window.toggleMobileMenu = () => {
            document.body.classList.toggle('mobile-menu-open');
        };
        
        // Handle back button
        window.addEventListener('popstate', () => {
            if (this.activeModal) {
                this.closeMobileModal(this.activeModal);
            }
        });
    }
    
    setupMobileModals() {
        // Convert existing modals to mobile-friendly
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            this.convertToMobileModal(modal);
        });
        
        // Setup modal functions
        window.showMobileModal = (modalId) => this.showMobileModal(modalId);
        window.closeMobileModal = (modalId) => this.closeMobileModal(modalId);
    }
    
    convertToMobileModal(modal) {
        modal.classList.add('mobile-modal');
        
        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');
        
        if (modalContent) {
            modalContent.classList.add('mobile-modal-content');
        }
        
        // Add swipe to close
        modal.addEventListener('touchstart', (e) => {
            if (e.target === modal) {
                this.touchStartY = e.touches[0].clientY;
            }
        }, { passive: true });
        
        modal.addEventListener('touchend', (e) => {
            if (e.target === modal && this.touchStartY) {
                const deltaY = e.changedTouches[0].clientY - this.touchStartY;
                if (deltaY > 100) {
                    this.closeMobileModal(modal.id);
                }
            }
        }, { passive: true });
    }
    
    showMobileModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        modal.classList.add('show');
        this.activeModal = modalId;
        document.body.style.overflow = 'hidden';
        
        // Add to history for back button handling
        history.pushState({ modal: modalId }, '', '');
    }
    
    closeMobileModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        modal.classList.remove('show');
        this.activeModal = null;
        document.body.style.overflow = '';
        
        // Remove from history
        if (history.state && history.state.modal === modalId) {
            history.back();
        }
    }
    
    setupMobileForms() {
        // Optimize form inputs for mobile
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.classList.add('mobile-form-control');
            
            // Prevent zoom on focus for iOS
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                input.addEventListener('focus', () => {
                    input.style.fontSize = '16px';
                });
                
                input.addEventListener('blur', () => {
                    input.style.fontSize = '';
                });
            }
        });
        
        // Convert buttons to mobile-friendly
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            if (!button.classList.contains('mobile-btn')) {
                button.classList.add('mobile-btn');
                
                if (button.classList.contains('btn-primary')) {
                    button.classList.add('mobile-btn-primary');
                } else if (button.classList.contains('btn-secondary')) {
                    button.classList.add('mobile-btn-secondary');
                }
            }
        });
    }
    
    setupMobileCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            if (!card.classList.contains('mobile-card')) {
                card.classList.add('mobile-card');
            }
        });
    }
    
    setupPullToRefresh() {
        let startY = 0;
        let currentY = 0;
        let isPulling = false;
        const threshold = 80;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        }, { passive: true });
        
        document.addEventListener('touchmove', (e) => {
            if (startY && window.scrollY === 0) {
                currentY = e.touches[0].clientY;
                const pullDistance = currentY - startY;
                
                if (pullDistance > 0) {
                    isPulling = true;
                    
                    if (pullDistance > threshold) {
                        document.body.classList.add('pull-to-refresh-ready');
                    }
                }
            }
        }, { passive: true });
        
        document.addEventListener('touchend', () => {
            if (isPulling) {
                const pullDistance = currentY - startY;
                
                if (pullDistance > threshold) {
                    // Trigger refresh
                    this.handlePullToRefresh();
                }
                
                document.body.classList.remove('pull-to-refresh-ready');
                isPulling = false;
            }
            
            startY = currentY = 0;
        }, { passive: true });
    }
    
    handlePullToRefresh() {
        // Show loading indicator
        this.showMobileAlert('Refreshing...', 'info');
        
        // Reload the page or refresh data
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
    
    setupVirtualKeyboard() {
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            if (heightDifference > 150) {
                document.body.classList.add('virtual-keyboard-open');
            } else {
                document.body.classList.remove('virtual-keyboard-open');
            }
        });
    }
    
    setupMobileSearch() {
        // Enhance search inputs for mobile
        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="search" i]');
        searchInputs.forEach(input => {
            input.setAttribute('autocomplete', 'off');
            input.setAttribute('autocorrect', 'off');
            input.setAttribute('autocapitalize', 'off');
            input.setAttribute('spellcheck', 'false');
        });
    }
    
    setupOrientationChange() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.viewport = this.getViewport();
                this.handleOrientationChange();
            }, 100);
        });
    }
    
    handleOrientationChange() {
        // Adjust layout for orientation change
        document.body.classList.toggle('landscape', this.viewport.orientation === 'landscape');
        document.body.classList.toggle('portrait', this.viewport.orientation === 'portrait');
        
        // Close any open modals on orientation change
        if (this.activeModal) {
            this.closeMobileModal(this.activeModal);
        }
    }
    
    // Utility methods
    showMobileAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `mobile-alert mobile-alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-info-circle mobile-alert-icon"></i>
            <div class="mobile-alert-content">
                <div class="mobile-alert-message">${message}</div>
            </div>
        `;
        
        const container = document.querySelector('.mobile-content') || document.body;
        container.insertBefore(alert, container.firstChild);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
    
    showCardActions(card) {
        // Implementation for card actions
        console.log('Show card actions for:', card);
    }
    
    expandCard(card) {
        // Implementation for card expansion
        card.classList.toggle('expanded');
    }
    
    showContextMenu(card, target) {
        // Implementation for context menu
        console.log('Show context menu for:', card, 'at:', target);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.digitalBoardMobile = new DigitalBoardMobile();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DigitalBoardMobile;
}
