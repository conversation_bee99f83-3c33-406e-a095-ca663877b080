/**
 * Live Feed JavaScript
 * Real-time activity feed with live updates and interactions
 */

class LiveFeed {
    constructor() {
        this.lastActivityId = null;
        this.updateInterval = 30000; // 30 seconds
        this.isUpdating = false;
        this.activityOffset = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.startLiveUpdates();
        this.loadLiveStats();
        this.setupInfiniteScroll();
    }
    
    setupEventListeners() {
        // Quick post functionality
        document.getElementById('postQuickUpdate')?.addEventListener('click', () => {
            this.postQuickUpdate();
        });
        
        // Enter key for quick post
        document.getElementById('quickPostContent')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.postQuickUpdate();
            }
        });
        
        // Refresh feed
        document.getElementById('refreshFeed')?.addEventListener('click', () => {
            this.refreshFeed();
        });
        
        // Like buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-btn')) {
                const btn = e.target.closest('.like-btn');
                const activityId = btn.dataset.activityId;
                this.toggleLike(activityId, btn);
            }
        });
        
        // Comment buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.comment-btn')) {
                const btn = e.target.closest('.comment-btn');
                const activityId = btn.dataset.activityId;
                this.toggleComments(activityId);
            }
        });
        
        // Submit comment
        document.addEventListener('click', (e) => {
            if (e.target.closest('.submit-comment')) {
                const btn = e.target.closest('.submit-comment');
                const activityId = btn.dataset.activityId;
                this.submitComment(activityId);
            }
        });
        
        // Comment input enter key
        document.addEventListener('keypress', (e) => {
            if (e.target.classList.contains('comment-input') && e.key === 'Enter') {
                const activityId = e.target.dataset.activityId;
                this.submitComment(activityId);
            }
        });
        
        // Share buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                const btn = e.target.closest('.share-btn');
                const activityId = btn.dataset.activityId;
                this.shareActivity(activityId);
            }
        });
        
        // Hashtag clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('hashtag-link')) {
                e.preventDefault();
                const hashtag = e.target.dataset.hashtag;
                this.filterByHashtag(hashtag);
            }
        });
        
        // Load more activities
        document.getElementById('loadMoreActivities')?.addEventListener('click', () => {
            this.loadMoreActivities();
        });
    }
    
    async postQuickUpdate() {
        const content = document.getElementById('quickPostContent').value.trim();
        if (!content) return;
        
        const btn = document.getElementById('postQuickUpdate');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Posting...';
        btn.disabled = true;
        
        try {
            const response = await fetch('/api/activities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'create_activity',
                    activity_type: 'social_post',
                    activity_data: content
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('quickPostContent').value = '';
                this.showNotification('Post shared successfully!', 'success');
                
                // Refresh feed to show new post
                setTimeout(() => {
                    this.refreshFeed();
                }, 1000);
            } else {
                this.showNotification('Failed to post update', 'error');
            }
        } catch (error) {
            console.error('Error posting update:', error);
            this.showNotification('Failed to post update', 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    async toggleLike(activityId, btn) {
        const isLiked = btn.classList.contains('active');
        const likeCountSpan = btn.querySelector('.like-count');
        const currentCount = parseInt(likeCountSpan.textContent);
        
        // Optimistic update
        if (isLiked) {
            btn.classList.remove('active');
            likeCountSpan.textContent = Math.max(0, currentCount - 1);
        } else {
            btn.classList.add('active');
            likeCountSpan.textContent = currentCount + 1;
        }
        
        try {
            const response = await fetch('/api/activities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: isLiked ? 'unlike_activity' : 'like_activity',
                    activity_id: activityId
                })
            });
            
            const data = await response.json();
            
            if (!data.success) {
                // Revert optimistic update
                if (isLiked) {
                    btn.classList.add('active');
                    likeCountSpan.textContent = currentCount;
                } else {
                    btn.classList.remove('active');
                    likeCountSpan.textContent = currentCount;
                }
                this.showNotification('Failed to update like', 'error');
            }
        } catch (error) {
            console.error('Error toggling like:', error);
            // Revert optimistic update
            if (isLiked) {
                btn.classList.add('active');
                likeCountSpan.textContent = currentCount;
            } else {
                btn.classList.remove('active');
                likeCountSpan.textContent = currentCount;
            }
        }
    }
    
    toggleComments(activityId) {
        const commentsSection = document.getElementById(`comments-${activityId}`);
        if (commentsSection.style.display === 'none') {
            commentsSection.style.display = 'block';
            this.loadComments(activityId);
        } else {
            commentsSection.style.display = 'none';
        }
    }
    
    async loadComments(activityId) {
        try {
            const response = await fetch(`/api/activities.php?action=get_comments&activity_id=${activityId}`);
            const data = await response.json();
            
            if (data.success) {
                const commentsList = document.querySelector(`#comments-${activityId} .comments-list`);
                commentsList.innerHTML = data.comments.map(comment => `
                    <div class="comment mb-2">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-2">
                                ${comment.avatar ? 
                                    `<img src="${comment.avatar}" class="rounded-circle" width="24" height="24">` :
                                    `<div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px;">
                                        <i class="fas fa-user text-white small"></i>
                                    </div>`
                                }
                            </div>
                            <div class="flex-grow-1">
                                <div class="bg-light rounded p-2">
                                    <strong class="small">${comment.first_name} ${comment.last_name}</strong>
                                    <div class="small">${comment.content}</div>
                                </div>
                                <small class="text-muted">${this.timeAgo(comment.created_at)}</small>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        } catch (error) {
            console.error('Error loading comments:', error);
        }
    }
    
    async submitComment(activityId) {
        const input = document.querySelector(`#comments-${activityId} .comment-input`);
        const content = input.value.trim();
        if (!content) return;
        
        try {
            const response = await fetch('/api/activities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'add_comment',
                    activity_id: activityId,
                    content: content
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                input.value = '';
                this.loadComments(activityId);
                
                // Update comment count
                const commentBtn = document.querySelector(`[data-activity-id="${activityId}"].comment-btn`);
                const countSpan = commentBtn.querySelector('.comment-count');
                countSpan.textContent = parseInt(countSpan.textContent) + 1;
            } else {
                this.showNotification('Failed to add comment', 'error');
            }
        } catch (error) {
            console.error('Error submitting comment:', error);
            this.showNotification('Failed to add comment', 'error');
        }
    }
    
    async shareActivity(activityId) {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'Check out this activity on Beersty!',
                    url: `${window.location.origin}/social/activity.php?id=${activityId}`
                });
            } catch (error) {
                console.log('Share cancelled');
            }
        } else {
            // Fallback to copying URL
            const url = `${window.location.origin}/social/activity.php?id=${activityId}`;
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('Link copied to clipboard!', 'success');
            });
        }
    }
    
    startLiveUpdates() {
        // Get initial last activity ID
        const activities = document.querySelectorAll('.activity-item');
        if (activities.length > 0) {
            this.lastActivityId = activities[0].dataset.activityId;
        }
        
        // Start polling for updates
        setInterval(() => {
            this.checkForUpdates();
        }, this.updateInterval);
    }
    
    async checkForUpdates() {
        if (this.isUpdating) return;
        this.isUpdating = true;
        
        try {
            const response = await fetch(`/api/activities.php?action=check_updates&last_id=${this.lastActivityId}`);
            const data = await response.json();
            
            if (data.success && data.new_activities && data.new_activities.length > 0) {
                this.showUpdateIndicator(`${data.new_activities.length} new activities`);
                this.prependNewActivities(data.new_activities);
            }
        } catch (error) {
            console.error('Error checking for updates:', error);
        } finally {
            this.isUpdating = false;
        }
    }
    
    prependNewActivities(activities) {
        const stream = document.getElementById('activityStream');
        activities.forEach(activity => {
            const activityElement = this.createActivityElement(activity);
            activityElement.classList.add('new-activity');
            stream.insertBefore(activityElement, stream.firstChild);
            
            // Update last activity ID
            this.lastActivityId = activity.id;
        });
        
        // Remove new-activity class after animation
        setTimeout(() => {
            document.querySelectorAll('.new-activity').forEach(el => {
                el.classList.remove('new-activity');
            });
        }, 3000);
    }
    
    createActivityElement(activity) {
        // This would create the HTML for a new activity
        // For now, return a placeholder
        const div = document.createElement('div');
        div.className = 'activity-item card mb-3';
        div.dataset.activityId = activity.id;
        div.innerHTML = `
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-3">
                        ${activity.avatar ? 
                            `<img src="${activity.avatar}" class="rounded-circle" width="40" height="40">` :
                            `<div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>`
                        }
                    </div>
                    <div class="flex-grow-1">
                        <div class="activity-header mb-2">
                            <strong>${activity.first_name} ${activity.last_name}</strong>
                            <span class="text-muted">@${activity.username}</span>
                            <small class="text-muted ms-2">
                                <i class="fas fa-clock me-1"></i>just now
                            </small>
                        </div>
                        <div class="activity-content mb-3">
                            ${activity.activity_data}
                        </div>
                        <div class="activity-actions d-flex justify-content-between align-items-center">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-danger like-btn" data-activity-id="${activity.id}">
                                    <i class="fas fa-heart me-1"></i>
                                    <span class="like-count">0</span>
                                </button>
                                <button class="btn btn-outline-primary comment-btn" data-activity-id="${activity.id}">
                                    <i class="fas fa-comment me-1"></i>
                                    <span class="comment-count">0</span>
                                </button>
                                <button class="btn btn-outline-success share-btn" data-activity-id="${activity.id}">
                                    <i class="fas fa-share me-1"></i>Share
                                </button>
                            </div>
                            <small class="text-muted">
                                <span class="badge bg-success">NEW</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return div;
    }
    
    async refreshFeed() {
        const btn = document.getElementById('refreshFeed');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
        btn.disabled = true;
        
        try {
            // Reload the page for now - in production, this would be an AJAX call
            window.location.reload();
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
    
    async loadLiveStats() {
        try {
            const response = await fetch('/api/activities.php?action=get_live_stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('liveCheckins').textContent = data.stats.checkins_today || 0;
                document.getElementById('liveUsers').textContent = data.stats.active_users || 0;
            }
        } catch (error) {
            console.error('Error loading live stats:', error);
        }
        
        // Update stats every minute
        setTimeout(() => {
            this.loadLiveStats();
        }, 60000);
    }
    
    setupInfiniteScroll() {
        window.addEventListener('scroll', () => {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 1000) {
                this.loadMoreActivities();
            }
        });
    }
    
    async loadMoreActivities() {
        const btn = document.getElementById('loadMoreActivities');
        if (btn.disabled) return;
        
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        
        try {
            this.activityOffset += 50;
            const response = await fetch(`/api/activities.php?action=get_activities&offset=${this.activityOffset}&limit=20`);
            const data = await response.json();
            
            if (data.success && data.activities.length > 0) {
                const stream = document.getElementById('activityStream');
                data.activities.forEach(activity => {
                    const activityElement = this.createActivityElement(activity);
                    stream.appendChild(activityElement);
                });
            } else {
                btn.style.display = 'none';
            }
        } catch (error) {
            console.error('Error loading more activities:', error);
        } finally {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-chevron-down me-2"></i>Load More Activities';
        }
    }
    
    filterByHashtag(hashtag) {
        // Filter activities by hashtag
        const activities = document.querySelectorAll('.activity-item');
        activities.forEach(activity => {
            const content = activity.querySelector('.activity-content').textContent;
            if (content.includes(`#${hashtag}`)) {
                activity.style.display = 'block';
            } else {
                activity.style.display = 'none';
            }
        });
        
        this.showNotification(`Filtered by #${hashtag}`, 'info');
    }
    
    showUpdateIndicator(message) {
        const indicator = document.getElementById('liveUpdateIndicator');
        const messageSpan = document.getElementById('updateMessage');
        messageSpan.textContent = message;
        indicator.style.display = 'block';
        
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 3000);
    }
    
    showNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add to page
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    timeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
        return Math.floor(diffInSeconds / 86400) + 'd ago';
    }
}

// Add emoji to quick post
function addEmoji(emoji) {
    const textarea = document.getElementById('quickPostContent');
    const cursorPos = textarea.selectionStart;
    const textBefore = textarea.value.substring(0, cursorPos);
    const textAfter = textarea.value.substring(cursorPos);
    
    textarea.value = textBefore + emoji + ' ' + textAfter;
    textarea.focus();
    textarea.setSelectionRange(cursorPos + emoji.length + 1, cursorPos + emoji.length + 1);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LiveFeed();
});
