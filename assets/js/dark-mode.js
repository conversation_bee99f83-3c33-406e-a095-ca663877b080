/**
 * Simple Dark Mode Toggle
 */

class DarkModeManager {
    constructor() {
        this.isDarkMode = this.getStoredPreference();
        this.init();
    }

    init() {
        this.applyTheme();
        this.bindEvents();
    }

    getStoredPreference() {
        const stored = localStorage.getItem('beersty-theme');
        if (stored !== null) {
            return stored === 'dark';
        }
        return false; // Default to light mode
    }

    applyTheme() {
        const html = document.documentElement;
        const body = document.body;

        if (this.isDarkMode) {
            html.classList.add('dark-mode');
            body.classList.add('dark-mode');
            console.log('Dark mode enabled');
        } else {
            html.classList.remove('dark-mode');
            body.classList.remove('dark-mode');
            console.log('Light mode enabled');
        }

        localStorage.setItem('beersty-theme', this.isDarkMode ? 'dark' : 'light');
        this.updateDropdownButtons();

        // Force a style recalculation
        document.body.style.display = 'none';
        document.body.offsetHeight; // trigger reflow
        document.body.style.display = '';
    }

    updateDropdownButtons() {
        const lightBtn = document.getElementById('theme-toggle-light');
        const darkBtn = document.getElementById('theme-toggle-dark');

        if (lightBtn && darkBtn) {
            if (this.isDarkMode) {
                lightBtn.classList.remove('active');
                darkBtn.classList.add('active');
            } else {
                lightBtn.classList.add('active');
                darkBtn.classList.remove('active');
            }
        }
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('#theme-toggle-light')) {
                this.enableLightMode();
            } else if (e.target.closest('#theme-toggle-dark')) {
                this.enableDarkMode();
            }
        });
    }

    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        this.applyTheme();
    }

    enableDarkMode() {
        this.isDarkMode = true;
        this.applyTheme();
    }

    enableLightMode() {
        this.isDarkMode = false;
        this.applyTheme();
    }

    getCurrentTheme() {
        return this.isDarkMode ? 'dark' : 'light';
    }
}

// Initialize dark mode when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.darkModeManager = new DarkModeManager();
    console.log('Dark mode manager initialized');
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.darkModeManager) {
            window.darkModeManager = new DarkModeManager();
            console.log('Dark mode manager initialized (fallback)');
        }
    });
} else {
    // DOM is already loaded
    if (!window.darkModeManager) {
        window.darkModeManager = new DarkModeManager();
        console.log('Dark mode manager initialized (immediate)');
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DarkModeManager;
}
