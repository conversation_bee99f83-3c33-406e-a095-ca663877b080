/**
 * Activity Feed JavaScript - Fun Social Interactions
 * Beersty Platform
 */

class ActivityFeed {
    constructor() {
        this.currentPage = 1;
        this.loading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeAnimations();
    }

    bindEvents() {
        // Like button functionality
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-btn')) {
                e.preventDefault();
                this.handleLike(e.target.closest('.like-btn'));
            }
        });

        // Comment button functionality
        document.addEventListener('click', (e) => {
            if (e.target.closest('.comment-btn')) {
                e.preventDefault();
                this.handleComment(e.target.closest('.comment-btn'));
            }
        });

        // Share button functionality
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                e.preventDefault();
                this.handleShare(e.target.closest('.share-btn'));
            }
        });

        // Load more button
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => this.loadMoreActivities());
        }

        // Feed filter buttons
        document.querySelectorAll('input[name="feedFilter"]').forEach(radio => {
            radio.addEventListener('change', () => this.filterFeed(radio.id));
        });

        // Auto-refresh feed every 30 seconds
        setInterval(() => this.refreshFeed(), 30000);
    }

    initializeAnimations() {
        // Animate activity cards on load
        const cards = document.querySelectorAll('.activity-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Add hover effects to social buttons
        document.querySelectorAll('.social-btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'scale(1)';
            });
        });
    }

    async handleLike(button) {
        const activityId = button.dataset.activityId;
        const likeCount = button.querySelector('.like-count');
        const icon = button.querySelector('i');
        
        // Optimistic UI update
        const isLiked = button.classList.contains('liked');
        const currentCount = parseInt(likeCount.textContent);
        
        if (isLiked) {
            button.classList.remove('liked');
            likeCount.textContent = currentCount - 1;
            icon.classList.remove('fas');
            icon.classList.add('far');
        } else {
            button.classList.add('liked');
            likeCount.textContent = currentCount + 1;
            icon.classList.remove('far');
            icon.classList.add('fas');
            
            // Add heart animation
            this.createHeartAnimation(button);
        }

        try {
            const response = await fetch('/api/social/like.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    activity_id: activityId,
                    activity_type: 'checkin'
                })
            });

            const result = await response.json();
            
            if (!result.success) {
                // Revert optimistic update on error
                if (isLiked) {
                    button.classList.add('liked');
                    likeCount.textContent = currentCount;
                    icon.classList.add('fas');
                    icon.classList.remove('far');
                } else {
                    button.classList.remove('liked');
                    likeCount.textContent = currentCount;
                    icon.classList.add('far');
                    icon.classList.remove('fas');
                }
                this.showNotification('Error updating like', 'error');
            }
        } catch (error) {
            console.error('Like error:', error);
            this.showNotification('Network error', 'error');
        }
    }

    createHeartAnimation(button) {
        const heart = document.createElement('div');
        heart.innerHTML = '❤️';
        heart.style.cssText = `
            position: absolute;
            font-size: 1.5rem;
            pointer-events: none;
            z-index: 1000;
            animation: heartFloat 1s ease-out forwards;
        `;
        
        const rect = button.getBoundingClientRect();
        heart.style.left = rect.left + rect.width / 2 + 'px';
        heart.style.top = rect.top + 'px';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
            heart.remove();
        }, 1000);
    }

    async handleComment(button) {
        const activityId = button.dataset.activityId;
        
        // Create comment modal or inline comment form
        this.showCommentModal(activityId);
    }

    showCommentModal(activityId) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-comment me-2"></i>Add Comment
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <textarea class="form-control" id="commentText" rows="3" 
                                  placeholder="Share your thoughts..."></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="activityFeed.submitComment('${activityId}')">
                            <i class="fas fa-paper-plane me-2"></i>Post Comment
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    async submitComment(activityId) {
        const commentText = document.getElementById('commentText').value.trim();
        
        if (!commentText) {
            this.showNotification('Please enter a comment', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/social/comment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    activity_id: activityId,
                    activity_type: 'checkin',
                    comment_text: commentText
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Comment posted!', 'success');
                bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
                this.refreshFeed();
            } else {
                this.showNotification('Error posting comment', 'error');
            }
        } catch (error) {
            console.error('Comment error:', error);
            this.showNotification('Network error', 'error');
        }
    }

    async handleShare(button) {
        const activityId = button.dataset.activityId;
        
        if (navigator.share) {
            // Use native sharing if available
            try {
                await navigator.share({
                    title: 'Check out this beer!',
                    text: 'Found this great beer on Beersty',
                    url: window.location.origin + '/activity/' + activityId
                });
            } catch (error) {
                console.log('Share cancelled');
            }
        } else {
            // Fallback to custom share modal
            this.showShareModal(activityId);
        }
    }

    showShareModal(activityId) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-share me-2"></i>Share
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="activityFeed.shareToTwitter('${activityId}')">
                                <i class="fab fa-twitter me-2"></i>Twitter
                            </button>
                            <button class="btn btn-primary" onclick="activityFeed.shareToFacebook('${activityId}')">
                                <i class="fab fa-facebook me-2"></i>Facebook
                            </button>
                            <button class="btn btn-outline-primary" onclick="activityFeed.copyLink('${activityId}')">
                                <i class="fas fa-link me-2"></i>Copy Link
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }

    shareToTwitter(activityId) {
        const url = encodeURIComponent(window.location.origin + '/activity/' + activityId);
        const text = encodeURIComponent('Check out this great beer I found on Beersty!');
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
    }

    shareToFacebook(activityId) {
        const url = encodeURIComponent(window.location.origin + '/activity/' + activityId);
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
    }

    async copyLink(activityId) {
        const url = window.location.origin + '/activity/' + activityId;
        
        try {
            await navigator.clipboard.writeText(url);
            this.showNotification('Link copied to clipboard!', 'success');
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
        } catch (error) {
            console.error('Copy failed:', error);
            this.showNotification('Failed to copy link', 'error');
        }
    }

    async loadMoreActivities() {
        if (this.loading) return;
        
        this.loading = true;
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        const originalText = loadMoreBtn.innerHTML;
        loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        loadMoreBtn.disabled = true;

        try {
            const response = await fetch(`/api/social/activities.php?page=${this.currentPage + 1}`);
            const result = await response.json();
            
            if (result.success && result.activities.length > 0) {
                this.appendActivities(result.activities);
                this.currentPage++;
            } else {
                loadMoreBtn.style.display = 'none';
                this.showNotification('No more activities to load', 'info');
            }
        } catch (error) {
            console.error('Load more error:', error);
            this.showNotification('Error loading activities', 'error');
        } finally {
            this.loading = false;
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
        }
    }

    appendActivities(activities) {
        const feed = document.querySelector('.activity-feed');
        // Implementation would append new activity cards
        // This is a placeholder for the actual implementation
    }

    async filterFeed(filterType) {
        // Implementation for filtering feed by All/Friends/Nearby
        console.log('Filtering feed by:', filterType);
    }

    async refreshFeed() {
        // Silently refresh the feed with new activities
        try {
            const response = await fetch('/api/social/activities.php?latest=true');
            const result = await response.json();
            
            if (result.success && result.activities.length > 0) {
                // Show notification for new activities
                this.showNotification(`${result.activities.length} new activities`, 'info');
            }
        } catch (error) {
            console.error('Refresh error:', error);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 1060;
            min-width: 300px;
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes heartFloat {
        0% {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(-50px) scale(1.5);
            opacity: 0;
        }
    }
    
    .social-btn {
        transition: all 0.3s ease;
    }
    
    .social-btn.liked {
        background: linear-gradient(135deg, var(--social-like) 0%, #ff5252 100%);
        color: white;
        transform: scale(1.05);
    }
    
    .activity-card {
        transition: all 0.3s ease;
    }
    
    .activity-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
`;
document.head.appendChild(style);

// Initialize activity feed when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.activityFeed = new ActivityFeed();
});
