/**
 * Media Manager JavaScript
 * Phase 4 12.0 - Media Management System
 */

class MediaManager {
    constructor() {
        this.config = window.mediaManagerConfig;
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.viewMode = 'grid';
        this.selectedItems = new Set();
        this.currentFilter = {
            type: '',
            search: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.loadMediaLibrary();
        this.loadStorageStats();
    }
    
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('mediaSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentFilter.search = e.target.value;
                    this.currentPage = 1;
                    this.loadMediaLibrary();
                }, 300);
            });
        }
        
        // Type filter
        const typeFilter = document.getElementById('mediaTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.currentFilter.type = e.target.value;
                this.currentPage = 1;
                this.loadMediaLibrary();
            });
        }
        
        // File input
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }
        
        // Tab switching
        const tabs = document.querySelectorAll('#mediaTabs button[data-bs-toggle="pill"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                if (e.target.id === 'library-tab') {
                    this.loadMediaLibrary();
                } else if (e.target.id === 'organize-tab') {
                    this.loadStorageStats();
                }
            });
        });
    }
    
    setupDragAndDrop() {
        const dropZone = document.getElementById('dropZone');
        if (!dropZone) return;
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
        });
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.parentElement.classList.add('dragover');
            }, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.parentElement.classList.remove('dragover');
            }, false);
        });
        
        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelection(files);
        }, false);
        
        dropZone.addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
    }
    
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    async loadMediaLibrary() {
        if (!this.config.breweryId) return;
        
        const container = document.getElementById('mediaContainer');
        if (!container) return;
        
        // Show loading
        container.innerHTML = this.getLoadingHTML();
        
        try {
            const params = new URLSearchParams({
                brewery_id: this.config.breweryId,
                page: this.currentPage,
                limit: this.itemsPerPage
            });
            
            if (this.currentFilter.type) {
                params.append('content_type', this.currentFilter.type);
            }
            
            if (this.currentFilter.search) {
                params.append('search', this.currentFilter.search);
            }
            
            const response = await fetch(`${this.config.apiBaseUrl}media.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderMediaLibrary(result.data);
            } else {
                this.showError('Failed to load media library');
            }
        } catch (error) {
            console.error('Error loading media library:', error);
            this.showError('Error loading media library');
        }
    }
    
    renderMediaLibrary(data) {
        const container = document.getElementById('mediaContainer');
        if (!container) return;
        
        if (!data.data || data.data.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }
        
        container.className = this.viewMode === 'grid' ? 'media-grid' : 'media-list';
        
        const mediaHTML = data.data.map(item => this.renderMediaItem(item)).join('');
        container.innerHTML = mediaHTML;
        
        // Render pagination
        this.renderPagination(data.pagination);
        
        // Setup item event listeners
        this.setupMediaItemListeners();
    }
    
    renderMediaItem(item) {
        const isImage = item.content_type === 'image';
        const isVideo = item.content_type === 'video';
        const isAudio = item.content_type === 'audio';
        
        const thumbnailHTML = isImage 
            ? `<img src="${this.getThumbnailUrl(item)}" alt="${item.title}" loading="lazy">`
            : `<i class="fas ${isVideo ? 'fa-video' : isAudio ? 'fa-music' : 'fa-file'} media-icon"></i>`;
        
        const sizeFormatted = this.formatFileSize(item.file_size);
        const dateFormatted = new Date(item.created_at).toLocaleDateString();
        
        return `
            <div class="media-item" data-media-id="${item.id}" data-media-type="${item.content_type}">
                <div class="media-thumbnail">
                    ${thumbnailHTML}
                    <div class="media-type-icon ${item.content_type}">
                        <i class="fas ${isImage ? 'fa-image' : isVideo ? 'fa-video' : 'fa-music'}"></i>
                    </div>
                    <div class="selection-checkbox">
                        <input type="checkbox" class="form-check-input" onchange="mediaManager.toggleSelection('${item.id}', this.checked)">
                    </div>
                </div>
                <div class="media-info">
                    <div class="media-title" title="${item.title}">${item.title}</div>
                    <div class="media-meta">${sizeFormatted} • ${dateFormatted}</div>
                    <div class="media-actions">
                        <button type="button" class="btn btn-sm btn-primary" onclick="mediaManager.previewMedia('${item.id}')">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="mediaManager.editMedia('${item.id}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="mediaManager.deleteMedia('${item.id}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    setupMediaItemListeners() {
        const mediaItems = document.querySelectorAll('.media-item');
        mediaItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.type === 'checkbox' || e.target.closest('button')) {
                    return; // Don't trigger preview for checkbox or button clicks
                }
                
                const mediaId = item.dataset.mediaId;
                this.previewMedia(mediaId);
            });
        });
    }
    
    async handleFileSelection(files) {
        if (!files || files.length === 0) return;
        
        // Switch to upload tab
        const uploadTab = document.getElementById('upload-tab');
        if (uploadTab) {
            uploadTab.click();
        }
        
        // Show upload progress
        const progressContainer = document.getElementById('uploadProgress');
        const uploadList = document.getElementById('uploadList');
        
        if (progressContainer && uploadList) {
            progressContainer.style.display = 'block';
            uploadList.innerHTML = '';
        }
        
        // Process each file
        for (let file of files) {
            await this.uploadFile(file);
        }
    }
    
    async uploadFile(file) {
        const uploadId = 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const uploadList = document.getElementById('uploadList');
        
        // Create upload item
        const uploadItem = document.createElement('div');
        uploadItem.className = 'upload-item';
        uploadItem.id = uploadId;
        uploadItem.innerHTML = this.getUploadItemHTML(file);
        uploadList.appendChild(uploadItem);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('brewery_id', this.config.breweryId);
            formData.append('title', file.name.split('.')[0]);
            
            const xhr = new XMLHttpRequest();
            
            // Progress tracking
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    this.updateUploadProgress(uploadId, percentComplete);
                }
            });
            
            // Success/Error handling
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        this.updateUploadStatus(uploadId, 'success', 'Upload completed');
                        // Refresh media library
                        setTimeout(() => {
                            this.loadMediaLibrary();
                        }, 1000);
                    } else {
                        this.updateUploadStatus(uploadId, 'error', response.data.error || 'Upload failed');
                    }
                } else {
                    this.updateUploadStatus(uploadId, 'error', 'Upload failed');
                }
            });
            
            xhr.addEventListener('error', () => {
                this.updateUploadStatus(uploadId, 'error', 'Upload failed');
            });
            
            xhr.open('POST', `${this.config.apiBaseUrl}media.php`);
            xhr.send(formData);
            
        } catch (error) {
            console.error('Upload error:', error);
            this.updateUploadStatus(uploadId, 'error', 'Upload failed');
        }
    }
    
    getUploadItemHTML(file) {
        const sizeFormatted = this.formatFileSize(file.size);
        
        return `
            <div class="upload-item-header">
                <span class="upload-filename">${file.name}</span>
                <span class="upload-size">${sizeFormatted}</span>
            </div>
            <div class="upload-progress">
                <div class="upload-progress-bar" style="width: 0%"></div>
            </div>
            <div class="upload-status uploading">
                <span><i class="fas fa-spinner fa-spin"></i> Uploading...</span>
                <span class="upload-percent">0%</span>
            </div>
        `;
    }
    
    updateUploadProgress(uploadId, percent) {
        const uploadItem = document.getElementById(uploadId);
        if (!uploadItem) return;
        
        const progressBar = uploadItem.querySelector('.upload-progress-bar');
        const percentSpan = uploadItem.querySelector('.upload-percent');
        
        if (progressBar) progressBar.style.width = percent + '%';
        if (percentSpan) percentSpan.textContent = Math.round(percent) + '%';
    }
    
    updateUploadStatus(uploadId, status, message) {
        const uploadItem = document.getElementById(uploadId);
        if (!uploadItem) return;
        
        const statusDiv = uploadItem.querySelector('.upload-status');
        if (!statusDiv) return;
        
        statusDiv.className = `upload-status ${status}`;
        
        const icon = status === 'success' ? 'fa-check' : status === 'error' ? 'fa-times' : 'fa-spinner fa-spin';
        statusDiv.innerHTML = `
            <span><i class="fas ${icon}"></i> ${message}</span>
            <span class="upload-percent">100%</span>
        `;
    }
    
    getThumbnailUrl(item) {
        // For now, return the original file path
        // In a production system, you'd have thumbnail generation
        return item.file_path.replace('../../', '');
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    getLoadingHTML() {
        return `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading media files...</p>
            </div>
        `;
    }
    
    getEmptyStateHTML() {
        return `
            <div class="text-center py-5">
                <i class="fas fa-images fa-4x text-muted mb-3"></i>
                <h4>No Media Files Found</h4>
                <p class="text-muted">Upload some images, videos, or audio files to get started.</p>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('upload-tab').click()">
                    <i class="fas fa-upload"></i> Upload Files
                </button>
            </div>
        `;
    }
    
    showError(message) {
        // You can implement a toast notification system here
        console.error(message);
        alert(message); // Temporary fallback
    }

    async loadStorageStats() {
        if (!this.config.breweryId) return;

        const statsContainer = document.getElementById('storageStats');
        if (!statsContainer) return;

        try {
            const response = await fetch(`${this.config.apiBaseUrl}media.php?brewery_id=${this.config.breweryId}&stats=true`);
            const result = await response.json();

            if (result.success && result.data.stats) {
                this.renderStorageStats(result.data.stats);
            } else {
                // Fallback to basic stats calculation
                this.calculateBasicStats();
            }
        } catch (error) {
            console.error('Error loading storage stats:', error);
            this.calculateBasicStats();
        }
    }

    async calculateBasicStats() {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}media.php?brewery_id=${this.config.breweryId}&limit=1000`);
            const result = await response.json();

            if (result.success) {
                const files = result.data.data || [];
                const stats = {
                    total_files: files.length,
                    total_size: files.reduce((sum, file) => sum + parseInt(file.file_size), 0),
                    images: files.filter(f => f.content_type === 'image').length,
                    videos: files.filter(f => f.content_type === 'video').length,
                    audio: files.filter(f => f.content_type === 'audio').length
                };

                this.renderStorageStats(stats);
            }
        } catch (error) {
            console.error('Error calculating stats:', error);
        }
    }

    renderStorageStats(stats) {
        const statsContainer = document.getElementById('storageStats');
        if (!statsContainer) return;

        const totalSizeFormatted = this.formatFileSize(stats.total_size || 0);
        const maxSize = 1024 * 1024 * 1024; // 1GB limit example
        const usagePercent = Math.min((stats.total_size / maxSize) * 100, 100);

        statsContainer.innerHTML = `
            <div class="storage-stat">
                <span class="stat-label">Total Files</span>
                <span class="stat-value">${stats.total_files || 0}</span>
            </div>
            <div class="storage-stat">
                <span class="stat-label">Total Size</span>
                <span class="stat-value">${totalSizeFormatted}</span>
            </div>
            <div class="storage-progress">
                <div class="storage-progress-bar" style="width: ${usagePercent}%">
                    ${usagePercent.toFixed(1)}%
                </div>
            </div>
            <div class="storage-stat">
                <span class="stat-label">Images</span>
                <span class="stat-value">${stats.images || 0}</span>
            </div>
            <div class="storage-stat">
                <span class="stat-label">Videos</span>
                <span class="stat-value">${stats.videos || 0}</span>
            </div>
            <div class="storage-stat">
                <span class="stat-label">Audio</span>
                <span class="stat-value">${stats.audio || 0}</span>
            </div>
        `;
    }

    renderPagination(pagination) {
        const paginationContainer = document.getElementById('mediaPagination');
        if (!paginationContainer || !pagination) return;

        let paginationHTML = '';

        // Previous button
        if (pagination.has_prev) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="mediaManager.goToPage(${pagination.page - 1})">Previous</a>
                </li>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="mediaManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // Next button
        if (pagination.has_next) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="mediaManager.goToPage(${pagination.page + 1})">Next</a>
                </li>
            `;
        }

        paginationContainer.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadMediaLibrary();
    }

    toggleSelection(mediaId, selected) {
        if (selected) {
            this.selectedItems.add(mediaId);
        } else {
            this.selectedItems.delete(mediaId);
        }

        this.updateBulkActionsBar();
    }

    updateBulkActionsBar() {
        let bulkBar = document.querySelector('.bulk-actions-bar');

        if (this.selectedItems.size > 0) {
            if (!bulkBar) {
                bulkBar = this.createBulkActionsBar();
                document.body.appendChild(bulkBar);
            }

            bulkBar.querySelector('.selected-count').textContent = `${this.selectedItems.size} selected`;
            bulkBar.classList.add('show');
        } else if (bulkBar) {
            bulkBar.classList.remove('show');
        }
    }

    createBulkActionsBar() {
        const bulkBar = document.createElement('div');
        bulkBar.className = 'bulk-actions-bar';
        bulkBar.innerHTML = `
            <span class="selected-count">0 selected</span>
            <button type="button" class="btn btn-sm btn-danger" onclick="mediaManager.bulkDelete()">
                <i class="fas fa-trash"></i> Delete
            </button>
            <button type="button" class="btn btn-sm btn-secondary" onclick="mediaManager.clearSelection()">
                <i class="fas fa-times"></i> Clear
            </button>
        `;
        return bulkBar;
    }

    clearSelection() {
        this.selectedItems.clear();

        // Uncheck all checkboxes
        const checkboxes = document.querySelectorAll('.media-item .selection-checkbox input');
        checkboxes.forEach(cb => cb.checked = false);

        // Remove selected class
        const selectedItems = document.querySelectorAll('.media-item.selected');
        selectedItems.forEach(item => item.classList.remove('selected'));

        this.updateBulkActionsBar();
    }

    async previewMedia(mediaId) {
        // Implementation for media preview modal
        console.log('Preview media:', mediaId);
        // This would open a modal with media preview and details
    }

    async editMedia(mediaId) {
        // Implementation for media editing
        console.log('Edit media:', mediaId);
        // This would open an edit modal for title, description, tags
    }

    async deleteMedia(mediaId) {
        if (!confirm('Are you sure you want to delete this media file?')) {
            return;
        }

        try {
            const response = await fetch(`${this.config.apiBaseUrl}media.php?id=${mediaId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.loadMediaLibrary();
                this.showSuccess('Media file deleted successfully');
            } else {
                this.showError(result.data.error || 'Failed to delete media file');
            }
        } catch (error) {
            console.error('Error deleting media:', error);
            this.showError('Error deleting media file');
        }
    }

    async bulkDelete() {
        if (this.selectedItems.size === 0) return;

        if (!confirm(`Are you sure you want to delete ${this.selectedItems.size} media files?`)) {
            return;
        }

        const deletePromises = Array.from(this.selectedItems).map(mediaId =>
            fetch(`${this.config.apiBaseUrl}media.php?id=${mediaId}`, { method: 'DELETE' })
        );

        try {
            await Promise.all(deletePromises);
            this.clearSelection();
            this.loadMediaLibrary();
            this.showSuccess('Selected media files deleted successfully');
        } catch (error) {
            console.error('Error in bulk delete:', error);
            this.showError('Error deleting some media files');
        }
    }

    showSuccess(message) {
        // Temporary implementation
        console.log('Success:', message);
        // You can implement a toast notification system here
    }
}

// Global functions for onclick handlers
function setViewMode(mode) {
    if (window.mediaManager) {
        window.mediaManager.viewMode = mode;
        window.mediaManager.loadMediaLibrary();
        
        // Update button states
        document.getElementById('gridViewBtn').classList.toggle('active', mode === 'grid');
        document.getElementById('listViewBtn').classList.toggle('active', mode === 'list');
    }
}

function changeBrewery(breweryId) {
    if (breweryId) {
        window.location.href = `?brewery_id=${breweryId}`;
    }
}

function openUploadModal() {
    const uploadTab = document.getElementById('upload-tab');
    if (uploadTab) {
        uploadTab.click();
    }
}

function regenerateThumbnails() {
    if (!confirm('This will regenerate thumbnails for all images. This may take some time. Continue?')) {
        return;
    }

    // Implementation for thumbnail regeneration
    console.log('Regenerating thumbnails...');
    alert('Thumbnail regeneration started. This process will run in the background.');
}

function optimizeStorage() {
    if (!confirm('This will optimize storage by compressing images and removing duplicates. Continue?')) {
        return;
    }

    // Implementation for storage optimization
    console.log('Optimizing storage...');
    alert('Storage optimization started. This process will run in the background.');
}

function cleanupUnused() {
    if (!confirm('This will remove media files that are not used in any slides. This action cannot be undone. Continue?')) {
        return;
    }

    // Implementation for cleanup
    console.log('Cleaning up unused files...');
    alert('Cleanup process started. Unused files will be removed.');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.mediaManagerConfig && window.mediaManagerConfig.breweryId) {
        window.mediaManager = new MediaManager();
    }
});
