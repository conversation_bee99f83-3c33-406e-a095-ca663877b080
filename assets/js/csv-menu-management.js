/**
 * CSV Menu Management JavaScript
 * Handle CSV import/export operations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize event listeners
    initializeEventListeners();
    loadImportHistory();
});

function initializeEventListeners() {
    // CSV Import Form
    const importForm = document.getElementById('csvImportForm');
    if (importForm) {
        importForm.addEventListener('submit', handleImportSubmit);
    }
    
    // CSV Export Form
    const exportForm = document.getElementById('csvExportForm');
    if (exportForm) {
        exportForm.addEventListener('submit', handleExportSubmit);
        
        // Update export stats when form changes
        const exportInputs = exportForm.querySelectorAll('select');
        exportInputs.forEach(input => {
            input.addEventListener('change', updateExportStats);
        });
    }
    
    // File input change handler
    const fileInput = document.querySelector('input[name="csv_file"]');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
}

function handleImportSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const validateOnly = formData.get('validate_only');
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    submitBtn.disabled = true;
    
    fetch('../api/csv-menu-operations.php?action=import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showImportResults(data);
        if (data.success && !validateOnly) {
            // Reload import history
            loadImportHistory();
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        alert('An error occurred during import: ' + error.message);
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function handleExportSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    // Trigger download
    const downloadUrl = `../api/csv-menu-operations.php?action=export&${params.toString()}`;
    window.location.href = downloadUrl;
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    // Validate file
    if (file.size > 5 * 1024 * 1024) {
        alert('File size exceeds 5MB limit');
        e.target.value = '';
        return;
    }
    
    if (!file.name.toLowerCase().endsWith('.csv')) {
        alert('Only CSV files are allowed');
        e.target.value = '';
        return;
    }
    
    // Show file info
    const fileInfo = document.createElement('div');
    fileInfo.className = 'alert alert-info mt-2';
    fileInfo.innerHTML = `
        <i class="fas fa-file-csv me-2"></i>
        Selected: ${file.name} (${formatFileSize(file.size)})
    `;
    
    // Remove existing file info
    const existingInfo = e.target.parentNode.querySelector('.alert');
    if (existingInfo) {
        existingInfo.remove();
    }
    
    e.target.parentNode.appendChild(fileInfo);
}

function showImportPreview() {
    const fileInput = document.querySelector('input[name="csv_file"]');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Please select a CSV file first');
        return;
    }
    
    const formData = new FormData();
    formData.append('csv_file', file);
    
    fetch('../api/csv-menu-operations.php?action=preview', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPreviewModal(data);
        } else {
            alert('Error previewing file: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Preview error:', error);
        alert('An error occurred while previewing the file');
    });
}

function showPreviewModal(data) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">CSV Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Preview of first 5 rows:</p>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    ${data.header.map(col => `<th>${col}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.preview_data.map(row => 
                                    `<tr>${data.header.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>`
                                ).join('')}
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted">Total rows in file: ${data.total_rows}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

function showImportResults(data) {
    const modal = document.getElementById('importResultsModal');
    const content = document.getElementById('importResultsContent');
    
    let html = '';
    
    if (data.success) {
        html += `<div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            ${data.validate_only ? 'Validation completed successfully!' : data.message}
        </div>`;
    } else {
        html += `<div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            Import failed due to validation errors
        </div>`;
    }
    
    // Summary
    html += `<div class="row mb-3">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">${data.total_rows || 0}</h5>
                    <p class="card-text">Total Rows</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">${data.imported_count || 0}</h5>
                    <p class="card-text">Imported</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-danger">${data.errors ? data.errors.length : 0}</h5>
                    <p class="card-text">Errors</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">${data.warnings ? data.warnings.length : 0}</h5>
                    <p class="card-text">Warnings</p>
                </div>
            </div>
        </div>
    </div>`;
    
    // Errors
    if (data.errors && data.errors.length > 0) {
        html += `<div class="mb-3">
            <h6 class="text-danger">Errors:</h6>
            <ul class="list-group">
                ${data.errors.map(error => `<li class="list-group-item list-group-item-danger">${error}</li>`).join('')}
            </ul>
        </div>`;
    }
    
    // Warnings
    if (data.warnings && data.warnings.length > 0) {
        html += `<div class="mb-3">
            <h6 class="text-warning">Warnings:</h6>
            <ul class="list-group">
                ${data.warnings.map(warning => `<li class="list-group-item list-group-item-warning">${warning}</li>`).join('')}
            </ul>
        </div>`;
    }
    
    content.innerHTML = html;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

function updateExportStats() {
    const form = document.getElementById('csvExportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    fetch(`../api/csv-menu-operations.php?action=stats&${params.toString()}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayExportStats(data.data);
        }
    })
    .catch(error => {
        console.error('Stats error:', error);
    });
}

function displayExportStats(stats) {
    const container = document.getElementById('exportStats');
    
    let html = '<h6>Export Preview:</h6>';
    
    if (stats.beer_count !== undefined) {
        html += `<p><i class="fas fa-beer me-2"></i>Beer items: ${stats.beer_count}</p>`;
    }
    
    if (stats.food_count !== undefined) {
        html += `<p><i class="fas fa-utensils me-2"></i>Food items: ${stats.food_count}</p>`;
    }
    
    if (stats.beer_count === 0 && stats.food_count === 0) {
        html += '<p class="text-muted">No items found for export</p>';
    }
    
    container.innerHTML = html;
}

function previewExport() {
    updateExportStats();
}

function loadImportHistory() {
    fetch('../api/csv-menu-operations.php?action=history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayImportHistory(data.data);
        }
    })
    .catch(error => {
        console.error('History error:', error);
    });
}

function displayImportHistory(history) {
    const container = document.getElementById('importHistory');
    
    if (history.length === 0) {
        container.innerHTML = '<p class="text-muted">No import history found</p>';
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Place</th>
                        <th>Type</th>
                        <th>Imported</th>
                        <th>Errors</th>
                        <th>Warnings</th>
                        <th>By</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    history.forEach(record => {
        html += `
            <tr>
                <td>${formatDate(record.created_at)}</td>
                <td>${record.place_name || 'Unknown'}</td>
                <td>
                    <span class="badge ${record.menu_type === 'beer' ? 'bg-warning' : 'bg-info'}">
                        ${record.menu_type}
                    </span>
                </td>
                <td><span class="badge bg-success">${record.imported_count}</span></td>
                <td><span class="badge bg-danger">${record.error_count}</span></td>
                <td><span class="badge bg-warning">${record.warning_count}</span></td>
                <td>${record.imported_by_email || 'Unknown'}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
}
