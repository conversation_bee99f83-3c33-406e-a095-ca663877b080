/**
 * Brewery CSV Import JavaScript
 * Handle file upload validation and progress
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeBreweryImport();
});

function initializeBreweryImport() {
    const form = document.getElementById('csvUploadForm');
    const fileInput = document.getElementById('csv_file');
    
    if (form && fileInput) {
        setupFileValidation();
        setupFormSubmission();
        setupDragAndDrop();
    }
}

/**
 * Setup file input validation
 */
function setupFileValidation() {
    const fileInput = document.getElementById('csv_file');
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            validateFile(file);
        }
    });
}

/**
 * Validate selected file
 */
function validateFile(file) {
    const errors = [];
    
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
        errors.push('Please select a CSV file');
    }
    
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        errors.push('File size must be less than 10MB');
    }
    
    // Display validation results
    displayValidationResults(file, errors);
    
    return errors.length === 0;
}

/**
 * Display file validation results
 */
function displayValidationResults(file, errors) {
    // Remove existing validation messages
    const existingAlert = document.querySelector('.file-validation-alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const fileInput = document.getElementById('csv_file');
    const container = fileInput.closest('.mb-3');
    
    if (errors.length > 0) {
        // Show errors
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger file-validation-alert mt-2';
        alertDiv.innerHTML = `
            <small>
                <i class="fas fa-exclamation-triangle me-1"></i>
                ${errors.join('<br>')}
            </small>
        `;
        container.appendChild(alertDiv);
        
        // Disable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
        }
    } else {
        // Show success
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success file-validation-alert mt-2';
        alertDiv.innerHTML = `
            <small>
                <i class="fas fa-check-circle me-1"></i>
                File ready: <strong>${file.name}</strong> (${formatFileSize(file.size)})
            </small>
        `;
        container.appendChild(alertDiv);
        
        // Enable submit button
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
        }
        
        // Preview file content
        previewCSVFile(file);
    }
}

/**
 * Preview CSV file content
 */
function previewCSVFile(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        const text = e.target.result;
        const lines = text.split('\n');
        
        if (lines.length > 0) {
            const headers = parseCSVLine(lines[0]);
            displayCSVPreview(headers, lines.length - 1);
        }
    };
    
    // Read first 1KB for preview
    const blob = file.slice(0, 1024);
    reader.readAsText(blob);
}

/**
 * Display CSV preview
 */
function displayCSVPreview(headers, rowCount) {
    // Remove existing preview
    const existingPreview = document.querySelector('.csv-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    const container = document.querySelector('.card-body');
    
    const previewDiv = document.createElement('div');
    previewDiv.className = 'csv-preview mt-3 p-3 bg-light rounded';
    previewDiv.innerHTML = `
        <h6><i class="fas fa-eye me-2"></i>CSV Preview</h6>
        <p><strong>Columns found:</strong> ${headers.length}</p>
        <p><strong>Data rows:</strong> ~${rowCount}</p>
        <div class="row">
            <div class="col-md-6">
                <strong>Detected columns:</strong>
                <ul class="small mb-0">
                    ${headers.slice(0, 8).map(header => `<li>${header}</li>`).join('')}
                    ${headers.length > 8 ? `<li><em>... and ${headers.length - 8} more</em></li>` : ''}
                </ul>
            </div>
            <div class="col-md-6">
                <strong>Field mapping:</strong>
                <ul class="small mb-0">
                    ${getFieldMappingPreview(headers)}
                </ul>
            </div>
        </div>
    `;
    
    container.appendChild(previewDiv);
}

/**
 * Get field mapping preview
 */
function getFieldMappingPreview(headers) {
    const mappings = [];
    
    headers.forEach(header => {
        const lower = header.toLowerCase().trim();
        let mapped = null;
        
        if (['name', 'brewery_name', 'brewery name'].includes(lower)) {
            mapped = 'name ✓';
        } else if (['address', 'street'].includes(lower)) {
            mapped = 'address ✓';
        } else if (['city', 'town'].includes(lower)) {
            mapped = 'city ✓';
        } else if (['state', 'province'].includes(lower)) {
            mapped = 'state ✓';
        } else if (['phone', 'telephone'].includes(lower)) {
            mapped = 'phone ✓';
        } else if (['website', 'url'].includes(lower)) {
            mapped = 'website ✓';
        } else if (['email'].includes(lower)) {
            mapped = 'email ✓';
        }
        
        if (mapped) {
            mappings.push(`<li>${header} → ${mapped}</li>`);
        }
    });
    
    if (mappings.length === 0) {
        mappings.push('<li><em>No standard fields detected</em></li>');
    }
    
    return mappings.slice(0, 6).join('') + 
           (mappings.length > 6 ? `<li><em>... and ${mappings.length - 6} more</em></li>` : '');
}

/**
 * Setup form submission with progress
 */
function setupFormSubmission() {
    const form = document.getElementById('csvUploadForm');
    
    form.addEventListener('submit', function(e) {
        const fileInput = document.getElementById('csv_file');
        const file = fileInput.files[0];
        
        if (!file || !validateFile(file)) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        showUploadProgress();
    });
}

/**
 * Show upload progress
 */
function showUploadProgress() {
    const submitBtn = document.querySelector('button[type="submit"]');
    
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Uploading and Processing...
        `;
    }
    
    // Add progress indicator
    const form = document.getElementById('csvUploadForm');
    const progressDiv = document.createElement('div');
    progressDiv.className = 'upload-progress mt-3';
    progressDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-spinner fa-spin me-2"></i>
            Processing CSV file... This may take a few moments for large files.
        </div>
    `;
    
    form.appendChild(progressDiv);
}

/**
 * Setup drag and drop functionality
 */
function setupDragAndDrop() {
    const fileInput = document.getElementById('csv_file');
    const dropZone = fileInput.closest('.card-body');
    
    if (!dropZone) return;
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        dropZone.classList.add('drag-over');
    }
    
    function unhighlight(e) {
        dropZone.classList.remove('drag-over');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }
}

/**
 * Parse CSV line (simple implementation)
 */
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    
    result.push(current.trim());
    return result;
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Add CSS for drag and drop
 */
const style = document.createElement('style');
style.textContent = `
    .drag-over {
        border: 2px dashed #007bff !important;
        background-color: rgba(0, 123, 255, 0.1) !important;
    }
    
    .csv-preview {
        border-left: 4px solid #28a745;
    }
    
    .file-validation-alert {
        font-size: 0.875rem;
    }
    
    .upload-progress {
        animation: fadeIn 0.3s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;
document.head.appendChild(style);
