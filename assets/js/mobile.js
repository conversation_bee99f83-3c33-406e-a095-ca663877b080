/**
 * Mobile-Optimized JavaScript
 * Phase 9: Design & Mobile Optimization
 * Touch interactions and mobile-specific functionality
 */

class MobileOptimizer {
    constructor() {
        this.isMobile = this.detectMobile();
        this.isTouch = this.detectTouch();
        this.viewport = this.getViewport();
        
        this.init();
    }
    
    init() {
        this.setupViewport();
        this.setupTouchHandlers();
        this.setupMobileNavigation();
        this.setupFormOptimizations();
        this.setupPerformanceOptimizations();
        this.setupAccessibility();
        
        if (this.isMobile) {
            document.body.classList.add('mobile-device');
        }
        
        if (this.isTouch) {
            document.body.classList.add('touch-device');
        }
    }
    
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }
    
    detectTouch() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    getViewport() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
        };
    }
    
    setupViewport() {
        // Prevent zoom on input focus (iOS)
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 
                    'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
                );
            }
        }
        
        // Handle viewport changes
        window.addEventListener('resize', () => {
            this.viewport = this.getViewport();
            this.handleOrientationChange();
        });
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.viewport = this.getViewport();
                this.handleOrientationChange();
            }, 100);
        });
    }
    
    handleOrientationChange() {
        document.body.classList.toggle('landscape', this.viewport.orientation === 'landscape');
        document.body.classList.toggle('portrait', this.viewport.orientation === 'portrait');
        
        // Trigger custom event
        window.dispatchEvent(new CustomEvent('mobileOrientationChange', {
            detail: { viewport: this.viewport }
        }));
    }
    
    setupTouchHandlers() {
        if (!this.isTouch) return;
        
        // Add touch feedback to buttons
        this.addTouchFeedback();
        
        // Setup swipe gestures
        this.setupSwipeGestures();
        
        // Setup pull-to-refresh
        this.setupPullToRefresh();
        
        // Prevent double-tap zoom on buttons
        this.preventDoubleTapZoom();
    }
    
    addTouchFeedback() {
        const touchElements = document.querySelectorAll('.btn, .card, .list-group-item, .nav-link');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.classList.add('touch-active');
            });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            });
            
            element.addEventListener('touchcancel', (e) => {
                element.classList.remove('touch-active');
            });
        });
    }
    
    setupSwipeGestures() {
        let startX, startY, startTime;
        
        document.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();
        });
        
        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const touch = e.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;
            
            // Check if it's a swipe (fast movement)
            if (deltaTime < 300 && Math.abs(deltaX) > 50) {
                const direction = deltaX > 0 ? 'right' : 'left';
                
                // Trigger custom swipe event
                window.dispatchEvent(new CustomEvent('mobileSwipe', {
                    detail: { direction, deltaX, deltaY, element: e.target }
                }));
            }
            
            startX = startY = null;
        });
    }
    
    setupPullToRefresh() {
        let startY, currentY, isPulling = false;
        const threshold = 100;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (!startY) return;
            
            currentY = e.touches[0].clientY;
            const pullDistance = currentY - startY;
            
            if (pullDistance > 0 && window.scrollY === 0) {
                isPulling = true;
                
                // Visual feedback
                if (pullDistance > threshold) {
                    document.body.classList.add('pull-to-refresh-ready');
                } else {
                    document.body.classList.remove('pull-to-refresh-ready');
                }
                
                // Prevent default scrolling
                e.preventDefault();
            }
        });
        
        document.addEventListener('touchend', (e) => {
            if (isPulling) {
                const pullDistance = currentY - startY;
                
                if (pullDistance > threshold) {
                    // Trigger refresh
                    window.dispatchEvent(new CustomEvent('mobilePullToRefresh'));
                }
                
                document.body.classList.remove('pull-to-refresh-ready');
                isPulling = false;
            }
            
            startY = currentY = null;
        });
    }
    
    preventDoubleTapZoom() {
        let lastTouchEnd = 0;
        
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }
    
    setupMobileNavigation() {
        // Setup bottom navigation
        this.setupBottomNavigation();
        
        // Setup mobile menu
        this.setupMobileMenu();
        
        // Setup back button handling
        this.setupBackButton();
    }
    
    setupBottomNavigation() {
        const bottomNav = document.querySelector('.mobile-bottom-nav');
        if (!bottomNav) return;
        
        // Add body class for bottom nav spacing
        document.body.classList.add('has-bottom-nav');
        
        // Handle active states
        const navLinks = bottomNav.querySelectorAll('.nav-link');
        const currentPath = window.location.pathname;
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href)) {
                link.classList.add('active');
            }
            
            // Add touch feedback
            link.addEventListener('touchstart', () => {
                link.style.transform = 'scale(0.95)';
            });
            
            link.addEventListener('touchend', () => {
                setTimeout(() => {
                    link.style.transform = '';
                }, 100);
            });
        });
    }
    
    setupMobileMenu() {
        const menuToggle = document.querySelector('.navbar-toggler');
        const mobileMenu = document.querySelector('.navbar-collapse');
        
        if (!menuToggle || !mobileMenu) return;
        
        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenu.contains(e.target) && !menuToggle.contains(e.target)) {
                if (mobileMenu.classList.contains('show')) {
                    menuToggle.click();
                }
            }
        });
        
        // Close menu when clicking on a link
        const menuLinks = mobileMenu.querySelectorAll('.nav-link');
        menuLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (mobileMenu.classList.contains('show')) {
                    setTimeout(() => menuToggle.click(), 100);
                }
            });
        });
    }
    
    setupBackButton() {
        // Handle browser back button
        window.addEventListener('popstate', (e) => {
            // Custom back button handling if needed
            window.dispatchEvent(new CustomEvent('mobileBackButton', {
                detail: { state: e.state }
            }));
        });
    }
    
    setupFormOptimizations() {
        // Auto-focus prevention on mobile
        if (this.isMobile) {
            const autoFocusElements = document.querySelectorAll('[autofocus]');
            autoFocusElements.forEach(element => {
                element.removeAttribute('autofocus');
            });
        }
        
        // Improve input types for mobile
        this.optimizeInputTypes();
        
        // Setup form validation feedback
        this.setupFormValidation();
        
        // Setup virtual keyboard handling
        this.setupVirtualKeyboard();
    }
    
    optimizeInputTypes() {
        const inputs = document.querySelectorAll('input[type="text"]');
        
        inputs.forEach(input => {
            const name = input.name || input.id || '';
            const placeholder = input.placeholder || '';
            
            // Email inputs
            if (name.includes('email') || placeholder.includes('email')) {
                input.type = 'email';
            }
            
            // Phone inputs
            if (name.includes('phone') || name.includes('tel') || placeholder.includes('phone')) {
                input.type = 'tel';
            }
            
            // Number inputs
            if (name.includes('number') || name.includes('age') || name.includes('quantity')) {
                input.type = 'number';
            }
            
            // URL inputs
            if (name.includes('url') || name.includes('website') || placeholder.includes('http')) {
                input.type = 'url';
            }
        });
    }
    
    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateInput(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('is-invalid')) {
                        this.validateInput(input);
                    }
                });
            });
        });
    }
    
    validateInput(input) {
        const isValid = input.checkValidity();
        
        input.classList.toggle('is-valid', isValid);
        input.classList.toggle('is-invalid', !isValid);
        
        // Show/hide feedback
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = isValid ? 'none' : 'block';
        }
    }
    
    setupVirtualKeyboard() {
        if (!this.isMobile) return;
        
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            // Virtual keyboard is likely open if height decreased significantly
            if (heightDifference > 150) {
                document.body.classList.add('virtual-keyboard-open');
            } else {
                document.body.classList.remove('virtual-keyboard-open');
            }
        });
    }
    
    setupPerformanceOptimizations() {
        // Lazy load images
        this.setupLazyLoading();
        
        // Optimize scrolling
        this.optimizeScrolling();
        
        // Preload critical resources
        this.preloadResources();
    }
    
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    optimizeScrolling() {
        // Use passive event listeners for better performance
        document.addEventListener('touchstart', () => {}, { passive: true });
        document.addEventListener('touchmove', () => {}, { passive: true });
        
        // Throttle scroll events
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (scrollTimeout) return;
            
            scrollTimeout = setTimeout(() => {
                window.dispatchEvent(new CustomEvent('optimizedScroll'));
                scrollTimeout = null;
            }, 16); // ~60fps
        }, { passive: true });
    }
    
    preloadResources() {
        // Preload critical CSS
        const criticalCSS = [
            '/beersty/assets/css/mobile.css',
            '/beersty/assets/css/pwa.css'
        ];
        
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
    }
    
    setupAccessibility() {
        // Improve focus management
        this.improveFocusManagement();
        
        // Setup screen reader optimizations
        this.setupScreenReaderOptimizations();
        
        // Handle reduced motion preferences
        this.handleReducedMotion();
    }
    
    improveFocusManagement() {
        // Skip to main content link
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'sr-only sr-only-focusable';
        document.body.insertBefore(skipLink, document.body.firstChild);
        
        // Focus trap for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    this.trapFocus(e, modal);
                }
            }
        });
    }
    
    trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
    
    setupScreenReaderOptimizations() {
        // Add live regions for dynamic content
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
        
        // Announce page changes
        window.addEventListener('popstate', () => {
            const pageTitle = document.title;
            this.announceToScreenReader(`Navigated to ${pageTitle}`);
        });
    }
    
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }
    
    handleReducedMotion() {
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
            
            // Disable animations
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            `;
            document.head.appendChild(style);
        }
    }
}

// Initialize mobile optimizer when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.mobileOptimizer = new MobileOptimizer();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileOptimizer;
}
