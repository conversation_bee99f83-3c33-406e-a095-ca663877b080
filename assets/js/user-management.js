/**
 * User Management JavaScript
 * Phase 5 - User Management & Authentication
 */

class UserManagement {
    constructor() {
        this.config = window.userManagementConfig;
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.currentFilters = {
            search: '',
            role: '',
            status: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUsers();
    }
    
    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('userSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.currentFilters.search = e.target.value;
                    this.currentPage = 1;
                    this.loadUsers();
                }, 300);
            });
        }
        
        // Role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.currentFilters.role = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }
        
        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilters.status = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }
        
        // Form submission
        const addUserForm = document.getElementById('addUserForm');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddUser(new FormData(addUserForm));
            });
        }
    }
    
    async loadUsers() {
        const tableBody = document.getElementById('usersTableBody');
        const userCount = document.getElementById('userCount');
        
        if (!tableBody) return;
        
        // Show loading
        tableBody.innerHTML = this.getLoadingHTML();
        
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage
            });
            
            if (this.config.breweryId) {
                params.append('brewery_id', this.config.breweryId);
            }
            
            if (this.currentFilters.search) {
                params.append('search', this.currentFilters.search);
            }
            
            if (this.currentFilters.role) {
                params.append('role', this.currentFilters.role);
            }
            
            if (this.currentFilters.status) {
                params.append('status', this.currentFilters.status);
            }
            
            const response = await fetch(`${this.config.apiBaseUrl}users.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderUsers(result.data);
                this.renderPagination(result.pagination);
                
                if (userCount) {
                    userCount.textContent = `Showing ${result.data.length} of ${result.pagination.total} users`;
                }
            } else {
                this.showError('Failed to load users');
            }
        } catch (error) {
            console.error('Error loading users:', error);
            this.showError('Error loading users');
        }
    }
    
    renderUsers(users) {
        const tableBody = document.getElementById('usersTableBody');
        if (!tableBody) return;
        
        if (users.length === 0) {
            tableBody.innerHTML = this.getEmptyStateHTML();
            return;
        }
        
        tableBody.innerHTML = users.map(user => this.renderUserRow(user)).join('');
    }
    
    renderUserRow(user) {
        const initials = this.getUserInitials(user);
        const statusBadge = this.getStatusBadge(user.status);
        const roleBadge = this.getRoleBadge(user.role);
        const permissions = this.getPermissionBadges(user.role);
        const lastActivity = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
        
        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">${initials}</div>
                        <div>
                            <div class="fw-bold">${this.escapeHtml(user.first_name || user.email)} ${this.escapeHtml(user.last_name || '')}</div>
                            <small class="text-muted">${this.escapeHtml(user.email)}</small>
                        </div>
                    </div>
                </td>
                <td>${roleBadge}</td>
                <td>
                    ${user.brewery_name ? `<span class="badge bg-info">${this.escapeHtml(user.brewery_name)}</span>` : '<span class="text-muted">No brewery</span>'}
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="d-flex flex-wrap">
                        ${permissions}
                    </div>
                </td>
                <td>
                    <small class="text-muted">${lastActivity}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="userManagement.editUser('${user.id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="userManagement.viewPermissions('${user.id}')" title="View Permissions">
                            <i class="fas fa-key"></i>
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="userManagement.viewActivity('${user.id}')" title="View Activity">
                            <i class="fas fa-history"></i>
                        </button>
                        ${user.status === 'active' ? 
                            `<button type="button" class="btn btn-outline-danger" onclick="userManagement.suspendUser('${user.id}')" title="Suspend User">
                                <i class="fas fa-ban"></i>
                            </button>` :
                            `<button type="button" class="btn btn-outline-success" onclick="userManagement.activateUser('${user.id}')" title="Activate User">
                                <i class="fas fa-check"></i>
                            </button>`
                        }
                    </div>
                </td>
            </tr>
        `;
    }
    
    getUserInitials(user) {
        const firstName = user.first_name || user.email.charAt(0);
        const lastName = user.last_name || '';
        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
    }
    
    getStatusBadge(status) {
        const statusClasses = {
            'active': 'bg-success',
            'inactive': 'bg-secondary',
            'suspended': 'bg-danger',
            'pending': 'bg-warning',
            'locked': 'bg-dark'
        };
        
        const statusLabels = {
            'active': 'Active',
            'inactive': 'Inactive',
            'suspended': 'Suspended',
            'pending': 'Pending',
            'locked': 'Locked'
        };
        
        const badgeClass = statusClasses[status] || 'bg-secondary';
        const label = statusLabels[status] || status;
        
        return `<span class="badge ${badgeClass}">${label}</span>`;
    }
    
    getRoleBadge(role) {
        const roleClasses = {
            'admin': 'bg-danger',
            'site_moderator': 'bg-warning',
            'business_owner': 'bg-primary',
            'business_manager': 'bg-info',
            'digital_board_operator': 'bg-success',
            'user': 'bg-secondary'
        };
        
        const roleLabels = {
            'admin': 'Admin',
            'site_moderator': 'Moderator',
            'business_owner': 'Owner',
            'business_manager': 'Manager',
            'digital_board_operator': 'Operator',
            'user': 'User'
        };
        
        const badgeClass = roleClasses[role] || 'bg-secondary';
        const label = roleLabels[role] || role;
        
        return `<span class="badge ${badgeClass}">${label}</span>`;
    }
    
    getPermissionBadges(role) {
        const rolePermissions = {
            'admin': ['All Permissions'],
            'site_moderator': ['View', 'Edit', 'Moderate'],
            'business_owner': ['Manage Business', 'Users', 'Analytics'],
            'business_manager': ['Manage Content', 'View Analytics'],
            'digital_board_operator': ['Manage Boards', 'Edit Content'],
            'user': ['View Only']
        };
        
        const permissions = rolePermissions[role] || ['Limited'];
        
        return permissions.map(permission => 
            `<span class="badge bg-dark permission-badge">${permission}</span>`
        ).join('');
    }
    
    renderPagination(pagination) {
        const paginationContainer = document.getElementById('usersPagination');
        if (!paginationContainer || !pagination) return;
        
        let paginationHTML = '';
        
        // Previous button
        if (pagination.has_prev) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${pagination.page - 1})">Previous</a>
                </li>
            `;
        }
        
        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        // Next button
        if (pagination.has_next) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManagement.goToPage(${pagination.page + 1})">Next</a>
                </li>
            `;
        }
        
        paginationContainer.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadUsers();
    }
    
    async handleAddUser(formData) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}users.php`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('User created successfully');
                this.loadUsers();
                
                // Close modal and reset form
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                document.getElementById('addUserForm').reset();
            } else {
                this.showError(result.error || 'Failed to create user');
            }
        } catch (error) {
            console.error('Error creating user:', error);
            this.showError('Error creating user');
        }
    }
    
    async editUser(userId) {
        // Implementation for editing user
        console.log('Edit user:', userId);
        // This would open an edit modal with user data
    }
    
    async viewPermissions(userId) {
        // Implementation for viewing user permissions
        console.log('View permissions for user:', userId);
        // This would open a modal showing detailed permissions
    }
    
    async viewActivity(userId) {
        // Implementation for viewing user activity
        console.log('View activity for user:', userId);
        // This would open a modal showing user activity log
    }
    
    async suspendUser(userId) {
        if (!confirm('Are you sure you want to suspend this user?')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.config.apiBaseUrl}users.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: userId,
                    status: 'suspended'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('User suspended successfully');
                this.loadUsers();
            } else {
                this.showError(result.error || 'Failed to suspend user');
            }
        } catch (error) {
            console.error('Error suspending user:', error);
            this.showError('Error suspending user');
        }
    }
    
    async activateUser(userId) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}users.php`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: userId,
                    status: 'active'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('User activated successfully');
                this.loadUsers();
            } else {
                this.showError(result.error || 'Failed to activate user');
            }
        } catch (error) {
            console.error('Error activating user:', error);
            this.showError('Error activating user');
        }
    }
    
    getLoadingHTML() {
        return `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Loading users...</p>
                </td>
            </tr>
        `;
    }
    
    getEmptyStateHTML() {
        return `
            <tr>
                <td colspan="7" class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h5>No Users Found</h5>
                    <p class="text-muted">No users match your current filters.</p>
                </td>
            </tr>
        `;
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'danger');
    }
    
    showAlert(message, type) {
        // Create alert element
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insert at top of container
        const container = document.querySelector('.container-fluid .row .col-12');
        if (container) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = alertHTML;
            container.insertBefore(tempDiv.firstElementChild, container.children[1]);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.userManagementConfig) {
        window.userManagement = new UserManagement();
    }
});
