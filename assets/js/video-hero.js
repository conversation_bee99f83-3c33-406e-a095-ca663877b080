/**
 * Video Hero Banner Controller
 * Handles video rotation, optimization, and user interactions
 * Beersty Platform - Brewery-themed video experience
 */

class VideoHeroController {
    constructor() {
        this.currentVideoIndex = 0;
        this.videos = [
            { name: 'beachside', src: '/newvideos/beersty_beachside.mp4', title: 'Beachside Experience' },
            { name: 'tvs', src: '/newvideos/beersty_tvs.mp4', title: 'Sports & Entertainment' },
            { name: 'pub', src: '/newvideos/beersty_pub.mp4', title: 'Classic Pub Atmosphere' },
            { name: 'couple', src: '/newvideos/beersty_couple.mp4', title: 'Social Connections' }
        ];
        
        this.videoElement = null;
        this.dots = [];
        this.autoRotateInterval = null;
        this.autoRotateDelay = 5000; // 5 seconds per video
        this.isUserInteracting = false;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.videoElement = document.getElementById('heroVideo');
        this.dots = document.querySelectorAll('.video-dot');

        if (!this.videoElement || this.dots.length === 0) {
            console.warn('Video hero elements not found');
            return;
        }

        this.showLoadingIndicator();
        this.setupEventListeners();
        this.optimizeVideo();

        // Start auto-rotation after initial video loads
        this.videoElement.addEventListener('canplay', () => {
            console.log('Initial video loaded, starting auto-rotation');
            this.startAutoRotation();
        }, { once: true });

        this.preloadVideos();
    }
    
    setupEventListeners() {
        // Dot navigation
        this.dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                console.log(`Dot ${index} clicked`);
                this.switchToVideo(index);
                this.pauseAutoRotation();
                this.resumeAutoRotationAfterDelay();
            });
        });

        console.log(`Found ${this.dots.length} navigation dots`);
        
        // Video events
        this.videoElement.addEventListener('loadstart', () => {
            console.log('Video loading started');
            this.showLoadingIndicator();
        });

        this.videoElement.addEventListener('canplay', () => {
            console.log('Video can start playing');
            this.hideLoadingIndicator();
        });

        this.videoElement.addEventListener('error', (e) => {
            console.error('Video error:', e);
            this.hideLoadingIndicator();
            this.handleVideoError();
        });
        
        // Pause auto-rotation on user interaction
        this.videoElement.addEventListener('click', () => {
            this.pauseAutoRotation();
            this.resumeAutoRotationAfterDelay();
        });
        
        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.videoElement.pause();
                this.pauseAutoRotation();
            } else {
                this.videoElement.play();
                this.startAutoRotation();
            }
        });
        
        // Intersection Observer for performance
        this.setupIntersectionObserver();
    }
    
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.videoElement.play();
                    this.startAutoRotation();
                } else {
                    this.videoElement.pause();
                    this.pauseAutoRotation();
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(this.videoElement);
    }
    
    switchToVideo(index) {
        if (index === this.currentVideoIndex || index >= this.videos.length) {
            console.log(`Skipping video switch: current=${this.currentVideoIndex}, requested=${index}`);
            return;
        }

        const newVideo = this.videos[index];
        console.log(`Switching to video ${index}: ${newVideo.name} (${newVideo.src})`);

        // Add fade transition
        this.videoElement.classList.add('fade-transition');

        setTimeout(() => {
            // Change video source
            this.videoElement.src = newVideo.src;
            this.videoElement.load();

            // Update current index
            this.currentVideoIndex = index;

            // Update active dot
            this.updateActiveDot(index);

            // Play new video and remove fade
            const playHandler = () => {
                console.log(`Video ${index} can play, starting playback`);
                this.videoElement.classList.remove('fade-transition');
                this.videoElement.classList.add('fade-in');
                this.videoElement.play().catch(e => {
                    console.error('Error playing video:', e);
                });

                setTimeout(() => {
                    this.videoElement.classList.remove('fade-in');
                }, 800);
            };

            this.videoElement.addEventListener('canplay', playHandler, { once: true });

            // Fallback timeout in case canplay doesn't fire
            setTimeout(() => {
                if (this.videoElement.classList.contains('fade-transition')) {
                    console.log('Fallback: forcing video play after timeout');
                    playHandler();
                }
            }, 2000);

        }, 400); // Half of the CSS transition duration
    }
    
    updateActiveDot(activeIndex) {
        this.dots.forEach((dot, index) => {
            if (index === activeIndex) {
                dot.classList.add('active');
                dot.setAttribute('aria-pressed', 'true');
            } else {
                dot.classList.remove('active');
                dot.setAttribute('aria-pressed', 'false');
            }
        });
    }
    
    startAutoRotation() {
        if (this.autoRotateInterval) {
            clearInterval(this.autoRotateInterval);
        }

        console.log(`Starting auto-rotation with ${this.autoRotateDelay}ms delay`);

        this.autoRotateInterval = setInterval(() => {
            if (!this.isUserInteracting) {
                const nextIndex = (this.currentVideoIndex + 1) % this.videos.length;
                console.log(`Auto-rotating from video ${this.currentVideoIndex} to ${nextIndex}`);
                this.switchToVideo(nextIndex);
            } else {
                console.log('Auto-rotation paused due to user interaction');
            }
        }, this.autoRotateDelay);
    }
    
    pauseAutoRotation() {
        this.isUserInteracting = true;
        if (this.autoRotateInterval) {
            clearInterval(this.autoRotateInterval);
            this.autoRotateInterval = null;
        }
    }
    
    resumeAutoRotationAfterDelay() {
        setTimeout(() => {
            this.isUserInteracting = false;
            this.startAutoRotation();
        }, 15000); // Resume after 15 seconds of no interaction
    }
    
    optimizeVideo() {
        // Set video attributes for better performance
        this.videoElement.setAttribute('preload', 'metadata');
        this.videoElement.setAttribute('playsinline', '');
        this.videoElement.setAttribute('webkit-playsinline', '');
        this.videoElement.muted = true; // Ensure muted for autoplay

        // Reduce quality on mobile devices
        if (window.innerWidth <= 768) {
            this.videoElement.style.filter = 'brightness(0.9)';
        }

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.autoRotateDelay = 12000; // Slower transitions
            this.videoElement.style.animationDuration = '0.01ms';
        }

        // Add error handling for video loading
        this.videoElement.addEventListener('loadeddata', () => {
            console.log('Video data loaded successfully');
            this.hideLoadingIndicator();
        });

        // Fallback for older browsers
        if (!this.videoElement.canPlayType || !this.videoElement.canPlayType('video/mp4')) {
            console.warn('MP4 not supported, showing fallback');
            this.handleVideoError();
        }
    }
    
    preloadVideos() {
        // Preload next video for smoother transitions
        const preloadNext = () => {
            const nextIndex = (this.currentVideoIndex + 1) % this.videos.length;
            const nextVideo = this.videos[nextIndex];
            
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = nextVideo.src;
            document.head.appendChild(link);
        };
        
        // Preload after initial video loads
        this.videoElement.addEventListener('canplay', preloadNext, { once: true });
    }
    
    showLoadingIndicator() {
        const existingLoader = document.querySelector('.video-loading');
        if (existingLoader) return;

        const loader = document.createElement('div');
        loader.className = 'video-loading';
        loader.innerHTML = 'Loading video...';

        const videoContainer = document.querySelector('.video-background');
        if (videoContainer) {
            videoContainer.appendChild(loader);
        }
    }

    hideLoadingIndicator() {
        const loader = document.querySelector('.video-loading');
        if (loader) {
            loader.remove();
        }
    }

    handleVideoError() {
        console.error('Video failed to load, showing fallback');

        // Create fallback background
        const fallbackBg = document.createElement('div');
        fallbackBg.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
            z-index: 1;
        `;

        this.videoElement.parentNode.appendChild(fallbackBg);
        this.videoElement.style.display = 'none';

        // Hide video navigation
        const videoNav = document.querySelector('.video-nav');
        if (videoNav) {
            videoNav.style.display = 'none';
        }
    }
    
    // Public methods for external control
    nextVideo() {
        const nextIndex = (this.currentVideoIndex + 1) % this.videos.length;
        this.switchToVideo(nextIndex);
    }
    
    previousVideo() {
        const prevIndex = this.currentVideoIndex === 0 ? this.videos.length - 1 : this.currentVideoIndex - 1;
        this.switchToVideo(prevIndex);
    }
    
    getCurrentVideo() {
        return this.videos[this.currentVideoIndex];
    }
    
    // Test function for debugging
    testVideoSwitch() {
        console.log('Testing video switch...');
        console.log('Current video index:', this.currentVideoIndex);
        console.log('Available videos:', this.videos.length);
        console.log('Auto-rotation active:', !!this.autoRotateInterval);

        // Test switching to next video
        const nextIndex = (this.currentVideoIndex + 1) % this.videos.length;
        console.log('Switching to video:', nextIndex);
        this.switchToVideo(nextIndex);
    }

    destroy() {
        this.pauseAutoRotation();
        if (this.videoElement) {
            this.videoElement.pause();
        }
    }
}

// Initialize when DOM is ready
let videoHeroController;

document.addEventListener('DOMContentLoaded', () => {
    videoHeroController = new VideoHeroController();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (videoHeroController) {
        videoHeroController.destroy();
    }
});

// Export for potential external use
window.VideoHeroController = VideoHeroController;

// Expose test function globally for debugging
window.testVideoHero = function() {
    if (videoHeroController) {
        videoHeroController.testVideoSwitch();
    } else {
        console.error('Video hero controller not initialized');
    }
};
