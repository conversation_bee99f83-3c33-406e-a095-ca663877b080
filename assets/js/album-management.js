/**
 * Album Management JavaScript
 * Handle album creation, editing, and management
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAlbumManagement();
});

function initializeAlbumManagement() {
    // Initialize form handlers
    initializeFormHandlers();
    
    // Initialize owner type handlers
    initializeOwnerTypeHandlers();
}

function initializeFormHandlers() {
    // Create album form
    const createForm = document.getElementById('createAlbumForm');
    if (createForm) {
        createForm.addEventListener('submit', handleCreateAlbum);
    }
    
    // Edit album form
    const editForm = document.getElementById('editAlbumForm');
    if (editForm) {
        editForm.addEventListener('submit', handleEditAlbum);
    }
    
    // Delete album form
    const deleteForm = document.getElementById('deleteAlbumForm');
    if (deleteForm) {
        deleteForm.addEventListener('submit', handleDeleteAlbum);
    }
}

function initializeOwnerTypeHandlers() {
    // Owner type change handler for create form
    const createOwnerType = document.getElementById('create_owner_type');
    if (createOwnerType) {
        createOwnerType.addEventListener('change', updateCreateOwnerOptions);
    }
}

function handleCreateAlbum(e) {
    e.preventDefault();
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;
    
    // Submit form (will reload page on success)
    e.target.submit();
}

function handleEditAlbum(e) {
    e.preventDefault();
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Submit form (will reload page on success)
    e.target.submit();
}

function handleDeleteAlbum(e) {
    e.preventDefault();
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';
    submitBtn.disabled = true;
    
    // Submit form (will reload page on success)
    e.target.submit();
}

function updateCreateOwnerOptions() {
    const ownerType = document.getElementById('create_owner_type').value;
    const ownerSelect = document.getElementById('create_owner_id');
    
    // Clear existing options
    ownerSelect.innerHTML = '<option value="">Select Owner</option>';
    
    if (!ownerType) return;
    
    // Fetch owners based on type
    fetch(`../api/album-management.php?action=get_owners&type=${ownerType}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            data.owners.forEach(owner => {
                const option = document.createElement('option');
                option.value = owner.id;
                option.textContent = ownerType === 'user' ? owner.email : owner.name;
                ownerSelect.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error loading owners:', error);
        showNotification('Failed to load owners', 'error');
    });
}

function updateOwnerOptions() {
    const ownerType = document.getElementById('owner_type').value;
    const form = document.querySelector('form');
    
    // Reset owner_id
    document.getElementById('owner_id').value = '';
    
    // Submit form to reload with new owner type
    form.submit();
}

function editAlbum(albumId) {
    // Fetch album data
    fetch(`../api/album-management.php?action=get&id=${albumId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateEditForm(data.album);
            const modal = new bootstrap.Modal(document.getElementById('editAlbumModal'));
            modal.show();
        } else {
            showNotification('Failed to load album details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error loading album:', error);
        showNotification('Failed to load album details', 'error');
    });
}

function populateEditForm(album) {
    // Populate form fields
    document.getElementById('edit_album_id').value = album.id;
    document.getElementById('edit_name').value = album.name || '';
    document.getElementById('edit_description').value = album.description || '';
    document.getElementById('edit_sort_order').value = album.sort_order || 0;
    document.getElementById('edit_is_public').checked = album.is_public == 1;
    
    // Display owner info
    const ownerDisplay = document.getElementById('edit_owner_display');
    const ownerType = album.owner_type === 'user' ? 'User' : 'Place';
    ownerDisplay.innerHTML = `
        <i class="fas fa-${album.owner_type === 'user' ? 'user' : 'building'} me-2"></i>
        ${ownerType}: ${album.owner_name}
    `;
}

function deleteAlbum(albumId) {
    // Fetch album data for confirmation
    fetch(`../api/album-management.php?action=get&id=${albumId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Populate delete form
            document.getElementById('delete_album_id').value = data.album.id;
            document.getElementById('delete_album_name').textContent = data.album.name;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteAlbumModal'));
            modal.show();
        } else {
            showNotification('Failed to load album details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error loading album:', error);
        showNotification('Failed to load album details', 'error');
    });
}

function setCoverPhoto(albumId, photoId) {
    fetch('../api/album-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'set_cover_photo',
            album_id: albumId,
            photo_id: photoId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Cover photo updated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to update cover photo: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Set cover photo error:', error);
        showNotification('Failed to update cover photo', 'error');
    });
}

function bulkDeleteAlbums() {
    const selectedAlbums = Array.from(document.querySelectorAll('.album-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedAlbums.length === 0) {
        alert('Please select albums to delete.');
        return;
    }
    
    if (!confirm(`Are you sure you want to delete ${selectedAlbums.length} albums? Photos will be moved to "No Album".`)) {
        return;
    }
    
    fetch('../api/album-management.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'bulk_delete',
            album_ids: selectedAlbums
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully deleted ${data.deleted_count} albums!`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to delete albums: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Bulk delete error:', error);
        showNotification('Failed to delete albums', 'error');
    });
}

function selectAllAlbums() {
    const checkboxes = document.querySelectorAll('.album-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateBulkActions();
}

function deselectAllAlbums() {
    const checkboxes = document.querySelectorAll('.album-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const selectedCount = document.querySelectorAll('.album-checkbox:checked').length;
    const bulkActions = document.getElementById('bulkActions');
    
    if (bulkActions) {
        if (selectedCount > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = selectedCount;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

function exportAlbums(format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    window.location.href = `../api/album-management.php?${params.toString()}`;
}

function viewAlbumPhotos(albumId) {
    // Redirect to photo management with album filter
    window.location.href = `photo-management.php?album_id=${albumId}`;
}

function duplicateAlbum(albumId) {
    if (!confirm('Are you sure you want to duplicate this album? This will create a copy with the same settings but no photos.')) {
        return;
    }
    
    fetch('../api/album-management.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'duplicate',
            album_id: albumId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Album duplicated successfully!', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to duplicate album: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Duplicate album error:', error);
        showNotification('Failed to duplicate album', 'error');
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
}

// Album sorting functionality
function sortAlbums(criteria) {
    const params = new URLSearchParams(window.location.search);
    params.set('sort', criteria);
    window.location.href = `album-management.php?${params.toString()}`;
}

// Album search functionality
function searchAlbums() {
    const searchTerm = document.getElementById('search').value;
    const params = new URLSearchParams(window.location.search);
    
    if (searchTerm) {
        params.set('search', searchTerm);
    } else {
        params.delete('search');
    }
    
    window.location.href = `album-management.php?${params.toString()}`;
}
