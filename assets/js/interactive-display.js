/**
 * Interactive Display JavaScript
 * Phase 7 - Advanced Features
 * 
 * Interactive elements and customer engagement for digital board displays
 */

class InteractiveDisplay {
    constructor(config = {}) {
        this.config = {
            boardId: null,
            sessionId: this.generateSessionId(),
            trackingEnabled: true,
            interactionTimeout: 30000, // 30 seconds
            ...config
        };
        
        this.activeInteractions = new Map();
        this.interactionQueue = [];
        this.isOnline = navigator.onLine;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupInteractionTracking();
        this.setupQRCodeScanning();
        this.setupTouchInteractions();
        this.setupOfflineHandling();
        this.startHeartbeat();
    }
    
    setupEventListeners() {
        // Touch and click events
        document.addEventListener('click', (e) => this.handleClick(e));
        document.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });
        
        // Long press detection
        document.addEventListener('contextmenu', (e) => this.handleLongPress(e));
        
        // Keyboard interactions
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        
        // Online/offline detection
        window.addEventListener('online', () => this.handleOnline());
        window.addEventListener('offline', () => this.handleOffline());
    }
    
    setupInteractionTracking() {
        // Track page views
        this.trackInteraction('page_view', {
            url: window.location.href,
            timestamp: Date.now(),
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            viewport_size: `${window.innerWidth}x${window.innerHeight}`
        });
        
        // Track scroll behavior
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.trackInteraction('scroll', {
                    scroll_position: window.scrollY,
                    scroll_percentage: Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100)
                });
            }, 500);
        });
        
        // Track time on page
        this.startTime = Date.now();
        window.addEventListener('beforeunload', () => {
            this.trackInteraction('page_exit', {
                time_spent: Date.now() - this.startTime
            });
        });
    }
    
    setupQRCodeScanning() {
        // QR code interaction tracking
        document.querySelectorAll('.qr-code, [data-qr-code]').forEach(element => {
            element.addEventListener('click', (e) => {
                const qrData = element.dataset.qrCode || element.getAttribute('data-url');
                this.handleQRCodeScan(qrData, element);
            });
        });
    }
    
    setupTouchInteractions() {
        let touchStartTime;
        let touchStartPos;
        
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartPos = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (!touchStartTime || !touchStartPos) return;
            
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - touchStartTime;
            const touchEndPos = {
                x: e.changedTouches[0].clientX,
                y: e.changedTouches[0].clientY
            };
            
            const distance = Math.sqrt(
                Math.pow(touchEndPos.x - touchStartPos.x, 2) + 
                Math.pow(touchEndPos.y - touchStartPos.y, 2)
            );
            
            // Detect gesture type
            if (touchDuration > 500 && distance < 10) {
                this.handleLongPress(e);
            } else if (distance > 50) {
                this.handleSwipe(touchStartPos, touchEndPos, touchDuration);
            }
            
            touchStartTime = null;
            touchStartPos = null;
        }, { passive: true });
    }
    
    setupOfflineHandling() {
        // Store interactions offline
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                this.serviceWorker = registration;
            });
        }
    }
    
    handleClick(event) {
        const element = event.target.closest('[data-interactive]');
        if (!element) return;
        
        const interactionType = element.dataset.interactive;
        const elementData = this.getElementData(element);
        
        this.trackInteraction('click', {
            element_type: interactionType,
            element_id: element.id || null,
            element_class: element.className,
            x_coordinate: event.clientX,
            y_coordinate: event.clientY,
            ...elementData
        });
        
        // Handle specific interaction types
        switch (interactionType) {
            case 'beer-item':
                this.handleBeerInteraction(element, elementData);
                break;
            case 'menu-category':
                this.handleMenuCategoryInteraction(element, elementData);
                break;
            case 'social-post':
                this.handleSocialPostInteraction(element, elementData);
                break;
            case 'rating':
                this.handleRatingInteraction(element, elementData);
                break;
            case 'poll':
                this.handlePollInteraction(element, elementData);
                break;
            default:
                this.handleGenericInteraction(element, elementData);
        }
    }
    
    handleTouchStart(event) {
        // Add touch feedback
        const element = event.target.closest('[data-interactive]');
        if (element) {
            element.classList.add('touch-active');
            
            // Haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(10);
            }
        }
    }
    
    handleTouchEnd(event) {
        // Remove touch feedback
        const element = event.target.closest('[data-interactive]');
        if (element) {
            setTimeout(() => {
                element.classList.remove('touch-active');
            }, 150);
        }
    }
    
    handleLongPress(event) {
        event.preventDefault();
        
        const element = event.target.closest('[data-interactive]');
        if (!element) return;
        
        // Stronger haptic feedback for long press
        if (navigator.vibrate) {
            navigator.vibrate([50, 30, 50]);
        }
        
        this.trackInteraction('long_press', {
            element_id: element.id || null,
            element_class: element.className,
            x_coordinate: event.clientX || event.touches[0].clientX,
            y_coordinate: event.clientY || event.touches[0].clientY
        });
        
        // Show context menu or additional options
        this.showContextMenu(element, event);
    }
    
    handleSwipe(startPos, endPos, duration) {
        const deltaX = endPos.x - startPos.x;
        const deltaY = endPos.y - startPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const velocity = distance / duration;
        
        let direction;
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            direction = deltaX > 0 ? 'right' : 'left';
        } else {
            direction = deltaY > 0 ? 'down' : 'up';
        }
        
        this.trackInteraction('swipe', {
            direction: direction,
            distance: Math.round(distance),
            velocity: Math.round(velocity * 1000), // pixels per second
            start_x: startPos.x,
            start_y: startPos.y,
            end_x: endPos.x,
            end_y: endPos.y
        });
        
        // Handle swipe actions
        this.handleSwipeAction(direction, distance, velocity);
    }
    
    handleKeyPress(event) {
        // Track keyboard interactions for accessibility
        if (event.target.closest('[data-interactive]')) {
            this.trackInteraction('keypress', {
                key: event.key,
                element_id: event.target.id || null,
                element_class: event.target.className
            });
        }
    }
    
    handleQRCodeScan(qrData, element) {
        this.trackInteraction('qr_scan', {
            qr_data: qrData,
            element_id: element.id || null,
            scan_method: 'click' // Could be 'camera' for actual QR scanning
        });
        
        // Open URL or trigger action
        if (qrData.startsWith('http')) {
            window.open(qrData, '_blank');
        } else {
            this.handleCustomQRAction(qrData);
        }
    }
    
    handleBeerInteraction(element, data) {
        const beerId = data.beer_id;
        const action = data.action || 'view_details';
        
        this.trackInteraction('beer_interaction', {
            beer_id: beerId,
            beer_name: data.beer_name,
            action: action,
            interaction_duration: 0
        });
        
        // Show beer details modal or perform action
        switch (action) {
            case 'view_details':
                this.showBeerDetails(beerId);
                break;
            case 'rate':
                this.showRatingInterface(beerId);
                break;
            case 'share':
                this.shareBeer(beerId);
                break;
        }
    }
    
    handleRatingInteraction(element, data) {
        const rating = parseInt(element.dataset.rating);
        const itemId = data.item_id;
        const itemType = data.item_type || 'beer';
        
        this.trackInteraction('rating', {
            item_id: itemId,
            item_type: itemType,
            rating: rating,
            max_rating: 5
        });
        
        // Submit rating
        this.submitRating(itemId, itemType, rating);
        
        // Visual feedback
        this.showRatingFeedback(element, rating);
    }
    
    handlePollInteraction(element, data) {
        const pollId = data.poll_id;
        const optionId = data.option_id;
        
        this.trackInteraction('poll_vote', {
            poll_id: pollId,
            option_id: optionId,
            option_text: element.textContent.trim()
        });
        
        // Submit vote
        this.submitPollVote(pollId, optionId);
        
        // Show results
        this.showPollResults(pollId);
    }
    
    trackInteraction(type, data = {}) {
        if (!this.config.trackingEnabled) return;
        
        const interaction = {
            id: this.generateInteractionId(),
            board_id: this.config.boardId,
            session_id: this.config.sessionId,
            interaction_type: type,
            timestamp: Date.now(),
            data: data
        };
        
        if (this.isOnline) {
            this.sendInteraction(interaction);
        } else {
            this.queueInteraction(interaction);
        }
    }
    
    async sendInteraction(interaction) {
        try {
            const response = await fetch('/api/track-interaction.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(interaction)
            });
            
            if (!response.ok) {
                throw new Error('Failed to send interaction');
            }
            
        } catch (error) {
            console.error('Interaction tracking error:', error);
            this.queueInteraction(interaction);
        }
    }
    
    queueInteraction(interaction) {
        this.interactionQueue.push(interaction);
        
        // Store in localStorage for persistence
        try {
            localStorage.setItem('interactionQueue', JSON.stringify(this.interactionQueue));
        } catch (error) {
            console.error('Failed to store interaction queue:', error);
        }
    }
    
    async flushInteractionQueue() {
        if (this.interactionQueue.length === 0) return;
        
        try {
            const response = await fetch('/api/track-interactions-batch.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    interactions: this.interactionQueue
                })
            });
            
            if (response.ok) {
                this.interactionQueue = [];
                localStorage.removeItem('interactionQueue');
            }
            
        } catch (error) {
            console.error('Failed to flush interaction queue:', error);
        }
    }
    
    handleOnline() {
        this.isOnline = true;
        this.flushInteractionQueue();
    }
    
    handleOffline() {
        this.isOnline = false;
    }
    
    startHeartbeat() {
        // Send periodic heartbeat to track session duration
        setInterval(() => {
            this.trackInteraction('heartbeat', {
                session_duration: Date.now() - this.startTime
            });
        }, 60000); // Every minute
    }
    
    // Utility methods
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    generateInteractionId() {
        return 'interaction_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    getElementData(element) {
        const data = {};
        
        // Extract all data attributes
        Object.keys(element.dataset).forEach(key => {
            data[key] = element.dataset[key];
        });
        
        return data;
    }
    
    showContextMenu(element, event) {
        // Implementation for context menu
        console.log('Show context menu for:', element);
    }
    
    handleSwipeAction(direction, distance, velocity) {
        // Implementation for swipe actions
        console.log('Swipe action:', direction, distance, velocity);
    }
    
    handleCustomQRAction(qrData) {
        // Implementation for custom QR actions
        console.log('Custom QR action:', qrData);
    }
    
    showBeerDetails(beerId) {
        // Implementation for showing beer details
        console.log('Show beer details:', beerId);
    }
    
    showRatingInterface(beerId) {
        // Implementation for rating interface
        console.log('Show rating interface:', beerId);
    }
    
    shareBeer(beerId) {
        // Implementation for sharing beer
        console.log('Share beer:', beerId);
    }
    
    submitRating(itemId, itemType, rating) {
        // Implementation for submitting rating
        console.log('Submit rating:', itemId, itemType, rating);
    }
    
    showRatingFeedback(element, rating) {
        // Implementation for rating feedback
        element.classList.add('rated');
        setTimeout(() => {
            element.classList.remove('rated');
        }, 2000);
    }
    
    submitPollVote(pollId, optionId) {
        // Implementation for submitting poll vote
        console.log('Submit poll vote:', pollId, optionId);
    }
    
    showPollResults(pollId) {
        // Implementation for showing poll results
        console.log('Show poll results:', pollId);
    }
    
    handleGenericInteraction(element, data) {
        // Implementation for generic interactions
        console.log('Generic interaction:', element, data);
    }
}

// Initialize interactive display when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Get board configuration from page
    const boardConfig = window.digitalBoardConfig || {};
    
    // Initialize interactive display
    window.interactiveDisplay = new InteractiveDisplay(boardConfig);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InteractiveDisplay;
}
