/**
 * Beersty - Main JavaScript File
 * Handles common functionality across the application
 */

// Global app object
window.Beersty = {
    // Configuration
    config: {
        apiUrl: '/api',
        uploadUrl: '/uploads',
        maxFileSize: 5242880, // 5MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    
    // Utility functions
    utils: {},
    
    // Components
    components: {},
    
    // API functions
    api: {}
};

/**
 * Utility Functions
 */
Beersty.utils = {
    /**
     * Show loading state on element
     */
    showLoading: function(element, text = 'Loading...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.add('loading');
            const originalText = element.textContent;
            element.setAttribute('data-original-text', originalText);
            
            if (element.tagName === 'BUTTON') {
                element.disabled = true;
                element.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
            }
        }
    },
    
    /**
     * Hide loading state on element
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.remove('loading');
            
            if (element.tagName === 'BUTTON') {
                element.disabled = false;
                const originalText = element.getAttribute('data-original-text');
                if (originalText) {
                    element.textContent = originalText;
                    element.removeAttribute('data-original-text');
                }
            }
        }
    },
    
    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info', duration = 5000) {
        // Create toast container if it doesn't exist
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', toastHtml);
        
        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    },
    
    /**
     * Format file size
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Validate image file
     */
    validateImageFile: function(file) {
        const errors = [];
        
        if (!Beersty.config.allowedImageTypes.includes(file.type)) {
            errors.push('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
        }
        
        if (file.size > Beersty.config.maxFileSize) {
            errors.push(`File size must be less than ${Beersty.utils.formatFileSize(Beersty.config.maxFileSize)}.`);
        }
        
        return errors;
    },
    
    /**
     * Debounce function
     */
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    
    /**
     * Serialize form data to object
     */
    serializeForm: function(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }
};

/**
 * API Functions
 */
Beersty.api = {
    /**
     * Make AJAX request
     */
    request: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = Object.assign({}, defaults, options);
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API request failed:', error);
                throw error;
            });
    },
    
    /**
     * GET request
     */
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = url + (Object.keys(params).length ? '?' + urlParams : '');
        
        return this.request(fullUrl);
    },
    
    /**
     * POST request
     */
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * PUT request
     */
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * DELETE request
     */
    delete: function(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
};

/**
 * Common Components
 */
Beersty.components = {
    /**
     * Initialize file upload component
     */
    initFileUpload: function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach(element => {
            element.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                const errors = Beersty.utils.validateImageFile(file);
                if (errors.length > 0) {
                    Beersty.utils.showToast(errors.join('<br>'), 'danger');
                    e.target.value = '';
                    return;
                }
                
                // Show preview if preview element exists
                const previewId = e.target.getAttribute('data-preview');
                if (previewId) {
                    const preview = document.getElementById(previewId);
                    if (preview) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                            preview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    }
                }
                
                // Call custom callback if provided
                if (options.onFileSelect) {
                    options.onFileSelect(file, e.target);
                }
            });
        });
    },
    
    /**
     * Initialize form validation
     */
    initFormValidation: function(selector) {
        const forms = document.querySelectorAll(selector);
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                form.classList.add('was-validated');
            });
        });
    },
    
    /**
     * Initialize search with debounce
     */
    initSearch: function(inputSelector, callback, delay = 300) {
        const inputs = document.querySelectorAll(inputSelector);

        inputs.forEach(input => {
            const debouncedCallback = Beersty.utils.debounce(callback, delay);
            input.addEventListener('input', debouncedCallback);
        });
    },

    /**
     * Initialize smart location search with geolocation
     */
    initSmartLocationSearch: function(inputSelector) {
        const inputs = document.querySelectorAll(inputSelector);

        inputs.forEach(input => {
            this.setupLocationAutocomplete(input);
        });
    },

    /**
     * Setup location autocomplete for a specific input
     */
    setupLocationAutocomplete: function(input) {
        const container = input.parentElement;
        const suggestionsId = 'location-suggestions-' + Math.random().toString(36).substr(2, 9);

        // Create suggestions dropdown
        const suggestionsDiv = document.createElement('div');
        suggestionsDiv.id = suggestionsId;
        suggestionsDiv.className = 'location-suggestions dropdown-menu';
        suggestionsDiv.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            display: none;
        `;

        container.style.position = 'relative';
        container.appendChild(suggestionsDiv);

        // Get user's location on focus
        input.addEventListener('focus', () => {
            this.getUserLocation().then(location => {
                input.dataset.userLat = location.lat;
                input.dataset.userLng = location.lng;
            }).catch(() => {
                // Fallback to IP-based location or default
                console.log('Geolocation not available, using fallback');
            });
        });

        // Setup input event listener with debounce
        const debouncedSearch = Beersty.utils.debounce((query) => {
            this.searchLocations(query, input, suggestionsDiv);
        }, 300);

        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length >= 2) {
                debouncedSearch(query);
            } else {
                suggestionsDiv.style.display = 'none';
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                suggestionsDiv.style.display = 'none';
            }
        });

        // Handle keyboard navigation
        input.addEventListener('keydown', (e) => {
            this.handleLocationKeyNavigation(e, suggestionsDiv);
        });
    },

    /**
     * Get user's current location
     */
    getUserLocation: function() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation not supported'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                position => {
                    console.log('Geolocation success:', position.coords.latitude, position.coords.longitude, 'accuracy:', position.coords.accuracy);
                    resolve({
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                        accuracy: position.coords.accuracy
                    });
                },
                error => {
                    console.error('Geolocation error:', error);
                    reject(error);
                },
                {
                    timeout: 15000,
                    enableHighAccuracy: true,
                    maximumAge: 60000 // 1 minute for better accuracy
                }
            );
        });
    },

    /**
     * Search for locations using multiple APIs
     */
    searchLocations: function(query, input, suggestionsDiv) {
        const userLat = input.dataset.userLat;
        const userLng = input.dataset.userLng;

        // Show loading state
        suggestionsDiv.innerHTML = '<div class="dropdown-item" style="color: #495057; background-color: #ffffff;"><i class="fas fa-spinner fa-spin me-2" style="color: #0d6efd;"></i>Searching...</div>';
        suggestionsDiv.style.display = 'block';

        // Search using multiple sources
        Promise.allSettled([
            this.searchLocalDatabase(query, userLat, userLng),
            this.searchOpenStreetMap(query),
            this.searchNearbyPlaces(query, userLat, userLng)
        ]).then(results => {
            const allSuggestions = [];

            results.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value && Array.isArray(result.value)) {
                    console.log(`Search source ${index} returned:`, result.value.length, 'results');
                    allSuggestions.push(...result.value);
                } else if (result.status === 'rejected') {
                    console.warn(`Search source ${index} failed:`, result.reason);
                }
            });

            console.log('Total suggestions found:', allSuggestions.length);
            this.displayLocationSuggestions(allSuggestions, input, suggestionsDiv, userLat, userLng);
        }).catch(error => {
            console.error('Location search error:', error);
            suggestionsDiv.innerHTML = '<div class="dropdown-item" style="color: #dc3545; background-color: #ffffff;"><i class="fas fa-exclamation-triangle me-2" style="color: #dc3545;"></i>Search error: ' + error.message + '</div>';
            suggestionsDiv.style.display = 'block';
        });
    },

    /**
     * Search nearby places using geolocation
     */
    searchNearbyPlaces: function(query, userLat, userLng) {
        if (!userLat || !userLng) {
            return Promise.resolve([]);
        }

        // Create a nearby suggestion based on the query
        return Promise.resolve([
            {
                name: `${query} near me`,
                type: 'nearby',
                lat: userLat,
                lng: userLng,
                distance: 0,
                icon: 'fas fa-location-arrow',
                priority: 0
            }
        ]);
    },

    /**
     * Search using OpenStreetMap Nominatim API
     */
    searchOpenStreetMap: function(query) {
        // Skip OSM for now to avoid CORS issues and focus on local data
        return Promise.resolve([]);

        /* Commented out due to potential CORS issues
        const url = `https://nominatim.openstreetmap.org/search?format=json&limit=3&q=${encodeURIComponent(query)}&countrycodes=us,ca&addressdetails=1`;

        return fetch(url, {
            headers: {
                'User-Agent': 'Beersty Location Search'
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('OSM API error');
                return response.json();
            })
            .then(data => {
                return data.map(place => ({
                    name: place.display_name.split(',').slice(0, 2).join(', '),
                    fullName: place.display_name,
                    type: 'osm',
                    lat: parseFloat(place.lat),
                    lng: parseFloat(place.lon),
                    icon: this.getLocationIcon(place.type),
                    priority: 3
                }));
            })
            .catch(error => {
                console.warn('OSM search failed:', error);
                return [];
            });
        */
    },

    /**
     * Search local database for cities/locations
     */
    searchLocalDatabase: function(query, userLat, userLng) {
        const params = new URLSearchParams({
            q: query,
            limit: 5
        });

        if (userLat && userLng) {
            params.append('lat', userLat);
            params.append('lng', userLng);
        }

        const url = `/api/location-search.php?${params}`;
        console.log('Fetching from:', url);

        return fetch(url)
            .then(response => {
                console.log('API response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API response data:', data);
                if (data.success && data.suggestions) {
                    return data.suggestions;
                }
                return [];
            })
            .catch(error => {
                console.error('Local database search failed:', error);
                return [];
            });
    },

    /**
     * Get appropriate icon for location type
     */
    getLocationIcon: function(type) {
        const iconMap = {
            'city': 'fas fa-city',
            'town': 'fas fa-home',
            'village': 'fas fa-home',
            'county': 'fas fa-map',
            'state': 'fas fa-flag',
            'country': 'fas fa-globe'
        };
        return iconMap[type] || 'fas fa-map-marker-alt';
    },

    /**
     * Display location suggestions
     */
    displayLocationSuggestions: function(suggestions, input, suggestionsDiv, userLat, userLng) {
        if (suggestions.length === 0) {
            suggestionsDiv.innerHTML = '<div class="dropdown-item" style="color: #6c757d; background-color: #ffffff;"><i class="fas fa-search me-2" style="color: #6c757d;"></i>No locations found</div>';
            return;
        }

        // Sort by priority and add distance if user location is available
        if (userLat && userLng) {
            suggestions.forEach(suggestion => {
                if (suggestion.lat && suggestion.lng) {
                    suggestion.distance = this.calculateDistance(
                        userLat, userLng, suggestion.lat, suggestion.lng
                    );
                }
            });
        }

        suggestions.sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            if (a.distance && b.distance) return a.distance - b.distance;
            return 0;
        });

        // Limit to top 8 suggestions
        const topSuggestions = suggestions.slice(0, 8);

        const html = topSuggestions.map(suggestion => {
            const distanceText = suggestion.distance ?
                `<small class="text-muted ms-auto">${suggestion.distance.toFixed(1)} mi</small>` : '';

            return `
                <div class="dropdown-item location-suggestion"
                     data-name="${suggestion.name}"
                     data-lat="${suggestion.lat || ''}"
                     data-lng="${suggestion.lng || ''}"
                     data-priority="${suggestion.priority}"
                     style="cursor: pointer; display: flex; align-items: center; color: #212529; background-color: #ffffff;">
                    <i class="${suggestion.icon} me-2"></i>
                    <div class="flex-grow-1">
                        <div style="color: #212529; font-weight: 500;">${suggestion.name}</div>
                        ${suggestion.brewery ? `<small class="text-muted" style="color: #6c757d;">${suggestion.brewery}</small>` : ''}
                    </div>
                    ${distanceText}
                </div>
            `;
        }).join('');

        suggestionsDiv.innerHTML = html;

        // Add click handlers
        suggestionsDiv.querySelectorAll('.location-suggestion').forEach(item => {
            item.addEventListener('click', () => {
                input.value = item.dataset.name;
                input.dataset.selectedLat = item.dataset.lat;
                input.dataset.selectedLng = item.dataset.lng;
                suggestionsDiv.style.display = 'none';

                // Trigger change event
                input.dispatchEvent(new Event('change', { bubbles: true }));
            });
        });

        // Force display and add animation class
        suggestionsDiv.style.display = 'block';
        suggestionsDiv.classList.add('show');
        console.log('Suggestions displayed:', topSuggestions.length, 'items');
    },

    /**
     * Calculate distance between two points in miles
     */
    calculateDistance: function(lat1, lng1, lat2, lng2) {
        const R = 3959; // Earth's radius in miles
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    },

    /**
     * Handle keyboard navigation in location suggestions
     */
    handleLocationKeyNavigation: function(e, suggestionsDiv) {
        const suggestions = suggestionsDiv.querySelectorAll('.location-suggestion');
        if (suggestions.length === 0) return;

        const currentActive = suggestionsDiv.querySelector('.location-suggestion.active');
        let activeIndex = currentActive ? Array.from(suggestions).indexOf(currentActive) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                activeIndex = (activeIndex + 1) % suggestions.length;
                break;
            case 'ArrowUp':
                e.preventDefault();
                activeIndex = activeIndex <= 0 ? suggestions.length - 1 : activeIndex - 1;
                break;
            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    currentActive.click();
                }
                return;
            case 'Escape':
                suggestionsDiv.style.display = 'none';
                return;
            default:
                return;
        }

        // Update active state
        suggestions.forEach((item, index) => {
            item.classList.toggle('active', index === activeIndex);
            if (index === activeIndex) {
                item.style.backgroundColor = '#f8f9fa';
            } else {
                item.style.backgroundColor = '';
            }
        });
    }
};

/**
 * Initialize application when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize file uploads
    Beersty.components.initFileUpload('input[type="file"][accept*="image"]');

    // Initialize form validation
    Beersty.components.initFormValidation('.needs-validation');

    // Initialize smart location search
    Beersty.components.initSmartLocationSearch('input[name="location"]');

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    console.log('Beersty application initialized');
});
