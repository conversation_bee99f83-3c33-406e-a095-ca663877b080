/**
 * Beersty - Main JavaScript File
 * Handles common functionality across the application
 */

// Global app object
window.Beersty = {
    // Configuration
    config: {
        apiUrl: '/api',
        uploadUrl: '/uploads',
        maxFileSize: 5242880, // 5MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    
    // Utility functions
    utils: {},
    
    // Components
    components: {},
    
    // API functions
    api: {}
};

/**
 * Utility Functions
 */
Beersty.utils = {
    /**
     * Show loading state on element
     */
    showLoading: function(element, text = 'Loading...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.add('loading');
            const originalText = element.textContent;
            element.setAttribute('data-original-text', originalText);
            
            if (element.tagName === 'BUTTON') {
                element.disabled = true;
                element.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
            }
        }
    },
    
    /**
     * Hide loading state on element
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.remove('loading');
            
            if (element.tagName === 'BUTTON') {
                element.disabled = false;
                const originalText = element.getAttribute('data-original-text');
                if (originalText) {
                    element.textContent = originalText;
                    element.removeAttribute('data-original-text');
                }
            }
        }
    },
    
    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info', duration = 5000) {
        // Create toast container if it doesn't exist
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', toastHtml);
        
        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    },
    
    /**
     * Format file size
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Validate image file
     */
    validateImageFile: function(file) {
        const errors = [];
        
        if (!Beersty.config.allowedImageTypes.includes(file.type)) {
            errors.push('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
        }
        
        if (file.size > Beersty.config.maxFileSize) {
            errors.push(`File size must be less than ${Beersty.utils.formatFileSize(Beersty.config.maxFileSize)}.`);
        }
        
        return errors;
    },
    
    /**
     * Debounce function
     */
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    
    /**
     * Serialize form data to object
     */
    serializeForm: function(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }
};

/**
 * API Functions
 */
Beersty.api = {
    /**
     * Make AJAX request
     */
    request: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = Object.assign({}, defaults, options);
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API request failed:', error);
                throw error;
            });
    },
    
    /**
     * GET request
     */
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = url + (Object.keys(params).length ? '?' + urlParams : '');
        
        return this.request(fullUrl);
    },
    
    /**
     * POST request
     */
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * PUT request
     */
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * DELETE request
     */
    delete: function(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
};

/**
 * Common Components
 */
Beersty.components = {
    /**
     * Initialize file upload component
     */
    initFileUpload: function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach(element => {
            element.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                const errors = Beersty.utils.validateImageFile(file);
                if (errors.length > 0) {
                    Beersty.utils.showToast(errors.join('<br>'), 'danger');
                    e.target.value = '';
                    return;
                }
                
                // Show preview if preview element exists
                const previewId = e.target.getAttribute('data-preview');
                if (previewId) {
                    const preview = document.getElementById(previewId);
                    if (preview) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                            preview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    }
                }
                
                // Call custom callback if provided
                if (options.onFileSelect) {
                    options.onFileSelect(file, e.target);
                }
            });
        });
    },
    
    /**
     * Initialize form validation
     */
    initFormValidation: function(selector) {
        const forms = document.querySelectorAll(selector);
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                form.classList.add('was-validated');
            });
        });
    },
    
    /**
     * Initialize search with debounce
     */
    initSearch: function(inputSelector, callback, delay = 300) {
        const inputs = document.querySelectorAll(inputSelector);
        
        inputs.forEach(input => {
            const debouncedCallback = Beersty.utils.debounce(callback, delay);
            input.addEventListener('input', debouncedCallback);
        });
    }
};

/**
 * Initialize application when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Initialize file uploads
    Beersty.components.initFileUpload('input[type="file"][accept*="image"]');
    
    // Initialize form validation
    Beersty.components.initFormValidation('.needs-validation');
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    console.log('Beersty application initialized');
});
