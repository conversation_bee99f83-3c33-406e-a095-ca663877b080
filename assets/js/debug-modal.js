/**
 * Debug Modal for Element Inspection
 * Shows all elements underneath mouse click to help debug modal issues
 */

class DebugModal {
    constructor() {
        this.isActive = false;
        this.modal = null;
        this.init();
    }

    init() {
        // Create debug modal HTML
        this.createModal();
        
        // Add global click listener when debug mode is active
        this.setupEventListeners();
        
        // Add keyboard shortcut to toggle debug mode (Ctrl+Shift+D)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggle();
            }
        });
    }

    createModal() {
        const modalHTML = `
            <div id="debugModal" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 400px;
                max-height: 80vh;
                background: white;
                border: 2px solid #333;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 99999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 12px;
                display: none;
            ">
                <div style="
                    background: #333;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px 6px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div style="font-weight: bold;">
                        🐛 Element Inspector
                    </div>
                    <button onclick="debugModal.close()" style="
                        background: none;
                        border: none;
                        color: white;
                        cursor: pointer;
                        font-size: 16px;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                    ">×</button>
                </div>

                <div style="padding: 12px; max-height: calc(80vh - 50px); overflow-y: auto;">
                    <div style="
                        background: #e3f2fd;
                        padding: 8px;
                        border-radius: 4px;
                        margin-bottom: 12px;
                        border-left: 4px solid #2196f3;
                    ">
                        <strong>Debug Mode Active!</strong><br>
                        <small>Click anywhere to inspect elements. Press <kbd>Ctrl+Shift+D</kbd> to toggle.</small>
                    </div>

                    <div style="margin-bottom: 12px;">
                        <strong>Last Click:</strong>
                        <span id="clickPosition" style="margin-left: 8px;">
                            <span style="background: #666; color: white; padding: 2px 6px; border-radius: 3px; margin-right: 4px;">
                                X: <span id="clickX">-</span>
                            </span>
                            <span style="background: #666; color: white; padding: 2px 6px; border-radius: 3px;">
                                Y: <span id="clickY">-</span>
                            </span>
                        </span>
                    </div>

                    <div>
                        <strong>Elements (top to bottom):</strong>
                        <div id="elementStack" style="
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            padding: 8px;
                            margin-top: 8px;
                            background: #fafafa;
                            max-height: 300px;
                            overflow-y: auto;
                        ">
                            <em style="color: #666;">Click somewhere to see elements...</em>
                        </div>
                    </div>

                    <div style="margin-top: 12px; text-align: center;">
                        <button onclick="debugModal.clearLog()" style="
                            background: #2196f3;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 8px;
                            font-size: 11px;
                        ">Clear</button>
                        <button onclick="debugModal.toggle()" style="
                            background: #ff9800;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 11px;
                        ">Toggle Debug</button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('debugModal');
    }

    setupEventListeners() {
        // Global click listener for debug mode
        document.addEventListener('click', (e) => {
            if (this.isActive && !this.modal.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                this.inspectClick(e);
                return false;
            }
        }, true); // Use capture phase to catch all clicks
    }

    toggle() {
        if (this.isActive) {
            this.deactivate();
        } else {
            this.activate();
        }
    }

    activate() {
        this.isActive = true;
        document.body.style.cursor = 'crosshair';
        document.body.classList.add('debug-mode-active');

        // Show modal (no Bootstrap modal, just show the div)
        this.modal.style.display = 'block';

        console.log('🐛 Debug mode activated - Click elements to inspect');
    }

    deactivate() {
        this.isActive = false;
        document.body.style.cursor = '';
        document.body.classList.remove('debug-mode-active');

        console.log('🐛 Debug mode deactivated');
    }

    close() {
        this.modal.style.display = 'none';
        this.deactivate();
    }

    show() {
        this.modal.style.display = 'block';
        this.activate();
    }

    inspectClick(event) {
        const x = event.clientX;
        const y = event.clientY;
        
        // Update click position
        document.getElementById('clickX').textContent = x;
        document.getElementById('clickY').textContent = y;
        
        // Get all elements at this point
        const elements = document.elementsFromPoint(x, y);
        
        // Display element stack
        this.displayElementStack(elements, x, y);
        
        // Log to console for additional debugging
        console.log('🐛 Click at:', { x, y });
        console.log('🐛 Elements:', elements);
    }

    displayElementStack(elements, x, y) {
        const container = document.getElementById('elementStack');
        container.innerHTML = '';

        elements.forEach((element, index) => {
            const elementInfo = this.getElementInfo(element);

            const elementDiv = document.createElement('div');
            elementDiv.style.cssText = `
                border-bottom: 1px solid #ddd;
                padding-bottom: 8px;
                margin-bottom: 8px;
                font-size: 11px;
            `;

            elementDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 4px;">
                    <div style="flex: 1;">
                        <strong style="color: #333;">${index + 1}. ${elementInfo.tagName}</strong>
                        ${elementInfo.id ? `<span style="background: #2196f3; color: white; padding: 1px 4px; border-radius: 2px; font-size: 10px; margin-left: 4px;">#${elementInfo.id}</span>` : ''}
                        ${elementInfo.classes.length ? `<span style="background: #666; color: white; padding: 1px 4px; border-radius: 2px; font-size: 10px; margin-left: 4px;">.${elementInfo.classes.join(' .')}</span>` : ''}
                    </div>
                    <button onclick="debugModal.highlightElement(this, ${index})" style="
                        background: #4caf50;
                        color: white;
                        border: none;
                        padding: 2px 6px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 10px;
                    ">Highlight</button>
                </div>
                ${elementInfo.attributes ? `<div style="color: #666; font-size: 10px; margin-bottom: 2px;">${elementInfo.attributes}</div>` : ''}
                <div style="color: #333; font-size: 10px; margin-bottom: 2px;">
                    <strong>Styles:</strong> ${elementInfo.computedStyles}
                </div>
                ${elementInfo.eventListeners ? `<div style="color: #e91e63; font-size: 10px;"><strong>Events:</strong> ${elementInfo.eventListeners}</div>` : ''}
            `;

            // Store element reference for highlighting
            elementDiv.dataset.elementIndex = index;

            container.appendChild(elementDiv);
        });

        // Store elements for highlighting
        this.currentElements = elements;
    }

    getElementInfo(element) {
        const computedStyle = window.getComputedStyle(element);
        
        return {
            tagName: element.tagName.toLowerCase(),
            id: element.id || '',
            classes: Array.from(element.classList),
            attributes: Array.from(element.attributes)
                .filter(attr => !['id', 'class'].includes(attr.name))
                .map(attr => `${attr.name}="${attr.value}"`)
                .join(' '),
            computedStyles: [
                `position: ${computedStyle.position}`,
                `z-index: ${computedStyle.zIndex}`,
                `pointer-events: ${computedStyle.pointerEvents}`,
                `display: ${computedStyle.display}`,
                `opacity: ${computedStyle.opacity}`
            ].join(', '),
            eventListeners: this.getEventListeners(element)
        };
    }

    getEventListeners(element) {
        // This is a simplified version - in real debugging you might want more detail
        const events = [];
        
        // Check for common event attributes
        ['onclick', 'onmousedown', 'onmouseup', 'onfocus', 'onblur', 'oninput', 'onchange'].forEach(event => {
            if (element[event]) {
                events.push(event.substring(2)); // Remove 'on' prefix
            }
        });
        
        // Check for data attributes that might indicate event handlers
        Array.from(element.attributes).forEach(attr => {
            if (attr.name.startsWith('data-bs-') || attr.name.startsWith('data-toggle')) {
                events.push(`${attr.name}="${attr.value}"`);
            }
        });
        
        return events.length ? events.join(', ') : '';
    }

    highlightElement(button, index) {
        if (!this.currentElements || !this.currentElements[index]) return;
        
        const element = this.currentElements[index];
        
        // Remove previous highlights
        document.querySelectorAll('.debug-highlight').forEach(el => {
            el.classList.remove('debug-highlight');
        });
        
        // Add highlight
        element.classList.add('debug-highlight');
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Remove highlight after 3 seconds
        setTimeout(() => {
            element.classList.remove('debug-highlight');
        }, 3000);
        
        // Update button text temporarily
        const originalText = button.textContent;
        button.textContent = 'Highlighted!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-primary');
        
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    }

    clearLog() {
        document.getElementById('elementStack').innerHTML = '<em style="color: #666;">Click somewhere to see elements...</em>';
        document.getElementById('clickX').textContent = '-';
        document.getElementById('clickY').textContent = '-';
    }
}

// Initialize debug modal when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.debugModal = new DebugModal();
    
    // Add CSS for debug mode
    const style = document.createElement('style');
    style.textContent = `
        .debug-mode-active * {
            outline: 1px solid rgba(255, 0, 0, 0.2) !important;
        }
        
        .debug-highlight {
            outline: 3px solid #ff6b6b !important;
            outline-offset: 2px !important;
            background-color: rgba(255, 107, 107, 0.1) !important;
        }
        
        .debug-mode-active .modal {
            pointer-events: auto !important;
        }
    `;
    document.head.appendChild(style);
    
    console.log('🐛 Debug Modal loaded! Press Ctrl+Shift+D to activate debug mode');
});
