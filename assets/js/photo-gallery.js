/**
 * Photo Gallery JavaScript
 * Handles photo upload, gallery display, and lightbox functionality
 */

// Global variables
let currentLightboxGallery = null;
let currentLightboxIndex = 0;

/**
 * Initialize photo gallery functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    initializePhotoUploads();
    initializeLightbox();
});

/**
 * Initialize photo upload functionality
 */
function initializePhotoUploads() {
    // Handle drag and drop for all upload dropzones
    document.querySelectorAll('.upload-dropzone').forEach(dropzone => {
        dropzone.addEventListener('dragover', handleDragOver);
        dropzone.addEventListener('dragleave', handleDragLeave);
        dropzone.addEventListener('drop', handleDrop);
    });
}

/**
 * Initialize lightbox functionality
 */
function initializeLightbox() {
    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (currentLightboxGallery) {
            if (e.key === 'ArrowLeft') {
                navigateLightbox(currentLightboxGallery, -1);
            } else if (e.key === 'ArrowRight') {
                navigateLightbox(currentLightboxGallery, 1);
            } else if (e.key === 'Escape') {
                closeLightbox();
            }
        }
    });
}

/**
 * Open photo upload modal
 */
function openPhotoUpload(type, targetId) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('photoUploadModal');
    if (!modal) {
        modal = createPhotoUploadModal();
        document.body.appendChild(modal);
    }
    
    // Set form data
    const form = modal.querySelector('.upload-form');
    form.querySelector('input[name="type"]').value = type;
    form.querySelector('input[name="target_id"]').value = targetId;
    
    // Reset form
    clearUploadForm(modal);
    
    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Create photo upload modal
 */
function createPhotoUploadModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'photoUploadModal';
    modal.tabIndex = -1;
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background-color: #6F4C3E !important; border: 1px solid #D69A6B !important;">
                <div class="modal-header" style="border-bottom: 1px solid #D69A6B !important; background-color: #6F4C3E !important;">
                    <h5 class="modal-title" style="color: #F5F5DC !important; font-weight: bold !important;">
                        <i class="fas fa-images me-2" style="color: #FFC107 !important;"></i><span style="color: #F5F5DC !important;">Upload Photos</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" style="filter: brightness(0) invert(1) !important; background: none !important; border: none !important;"></button>
                </div>
                <div class="modal-body" style="background-color: #6F4C3E !important; padding: 2rem !important; color: #F5F5DC !important;">
                    <form class="upload-form" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="">
                        <input type="hidden" name="target_id" value="">
                        
                        <div class="upload-area" style="border: 3px dashed #FFC107 !important; background-color: #3B2A2A !important; border-radius: 12px !important; padding: 3rem 2rem !important; text-align: center !important; cursor: pointer !important; transition: all 0.3s ease !important;">
                            <div class="upload-dropzone" onclick="document.getElementById('modal-photo-files').click()">
                                <i class="fas fa-cloud-upload-alt fa-3x mb-3" style="color: #FFC107 !important;"></i>
                                <h6 style="color: #F5F5DC !important; font-weight: bold !important; font-size: 1.2rem !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;">Drop photos here or click to browse</h6>
                                <p style="color: #F5F5DC !important; font-weight: 600 !important; margin-bottom: 0.5rem !important; text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;">Upload multiple photos (JPEG, PNG, GIF, WebP)</p>
                                <p style="color: #F5F5DC !important; font-size: 0.95rem !important; font-weight: 500 !important; text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;">Maximum file size: 5MB per photo</p>
                            </div>
                            
                            <input type="file" 
                                   id="modal-photo-files" 
                                   name="photos[]" 
                                   accept="image/*" 
                                   multiple
                                   style="display: none;"
                                   onchange="handleModalFileSelection()">
                        </div>
                        
                        <div class="file-preview" id="modal-preview" style="display: none;">
                            <h6 style="color: #F5F5DC !important; font-weight: bold !important;">Selected Photos:</h6>
                            <div class="preview-grid" id="modal-preview-grid"></div>
                        </div>
                        
                        <div class="upload-options mt-3" style="display: none;" id="modal-options">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label" style="color: #F5F5DC;">Title (optional)</label>
                                    <input type="text" class="form-control" name="title" placeholder="Photo title" style="background-color: #3B2A2A !important; border: 1px solid #D69A6B !important; color: #F5F5DC !important;" onchange="this.style.color='#F5F5DC'" onfocus="this.style.borderColor='#FFC107'; this.style.color='#F5F5DC';" onblur="this.style.borderColor='#D69A6B';" oninput="this.style.color='#F5F5DC';">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label" style="color: #F5F5DC;">Description (optional)</label>
                                    <textarea class="form-control" name="description" rows="2" placeholder="Photo description" style="background-color: #3B2A2A !important; border: 1px solid #D69A6B !important; color: #F5F5DC !important;" onchange="this.style.color='#F5F5DC'" onfocus="this.style.borderColor='#FFC107'; this.style.color='#F5F5DC';" onblur="this.style.borderColor='#D69A6B';" oninput="this.style.color='#F5F5DC';"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="upload-progress mt-3" style="display: none;" id="modal-progress">
                            <div class="progress" style="background-color: #3B2A2A;">
                                <div class="progress-bar" role="progressbar" style="width: 0%; background-color: #FFC107;"></div>
                            </div>
                            <div class="progress-text text-center mt-2" style="color: #F5F5DC;">Uploading...</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #D69A6B !important; background-color: #6F4C3E !important;">
                    <button type="button" class="btn" style="background-color: #3B2A2A !important; border: 1px solid #D69A6B !important; color: #F5F5DC !important; font-weight: bold !important;" data-bs-dismiss="modal" onmouseover="this.style.backgroundColor='#6F4C3E'" onmouseout="this.style.backgroundColor='#3B2A2A'">Cancel</button>
                    <button type="button" class="btn" id="modal-upload-btn" onclick="uploadModalPhotos()" style="display: none; background-color: #FFC107 !important; border: 1px solid #FFC107 !important; color: #3B2A2A !important; font-weight: bold !important;" onmouseover="this.style.backgroundColor='#E6AC00'" onmouseout="this.style.backgroundColor='#FFC107'">
                        <i class="fas fa-upload me-2" style="color: #3B2A2A !important;"></i><span style="color: #3B2A2A !important;">Upload Photos</span>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Add custom CSS to override ALL Bootstrap defaults and eliminate blue
    const style = document.createElement('style');
    style.textContent = `
        /* FORCE brewery colors - NO BLUE ANYWHERE */
        #photoUploadModal * {
            color: #F5F5DC !important;
        }

        #photoUploadModal .modal-content {
            background-color: #6F4C3E !important;
            border: 1px solid #D69A6B !important;
        }

        #photoUploadModal .modal-header {
            background-color: #6F4C3E !important;
            border-bottom: 1px solid #D69A6B !important;
        }

        #photoUploadModal .modal-body {
            background-color: #6F4C3E !important;
        }

        #photoUploadModal .modal-footer {
            background-color: #6F4C3E !important;
            border-top: 1px solid #D69A6B !important;
        }

        #photoUploadModal .modal-title {
            color: #F5F5DC !important;
            font-weight: bold !important;
        }

        #photoUploadModal .form-control {
            background-color: #3B2A2A !important;
            border: 1px solid #D69A6B !important;
            color: #F5F5DC !important;
        }

        #photoUploadModal .form-control::placeholder {
            color: #D69A6B !important;
            opacity: 1 !important;
        }

        #photoUploadModal .form-control:focus {
            background-color: #3B2A2A !important;
            border-color: #FFC107 !important;
            color: #F5F5DC !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
            outline: none !important;
        }

        #photoUploadModal .form-control:hover {
            border-color: #FFC107 !important;
        }

        #photoUploadModal .form-label {
            color: #F5F5DC !important;
            font-weight: 500 !important;
        }

        #photoUploadModal .btn {
            border: none !important;
            font-weight: bold !important;
        }

        #photoUploadModal .btn:hover {
            transform: translateY(-1px) !important;
            transition: all 0.3s ease !important;
        }

        #photoUploadModal .btn:focus {
            box-shadow: none !important;
            outline: none !important;
        }

        #photoUploadModal .btn-close {
            filter: brightness(0) invert(1) !important;
            opacity: 1 !important;
        }

        #photoUploadModal .btn-close:hover {
            filter: brightness(0) invert(1) !important;
            opacity: 0.8 !important;
        }

        #photoUploadModal .progress {
            background-color: #3B2A2A !important;
        }

        #photoUploadModal .progress-bar {
            background-color: #FFC107 !important;
        }

        /* Override any remaining blue colors */
        #photoUploadModal .text-primary,
        #photoUploadModal .btn-primary,
        #photoUploadModal .text-info,
        #photoUploadModal .btn-info {
            color: #F5F5DC !important;
            background-color: #FFC107 !important;
            border-color: #FFC107 !important;
        }

        #photoUploadModal .text-muted {
            color: #D69A6B !important;
        }

        /* Upload area specific styling - DARK background with readable text */
        #photoUploadModal .upload-area {
            background-color: #3B2A2A !important;
            border: 3px dashed #FFC107 !important;
        }

        #photoUploadModal .upload-area h6,
        #photoUploadModal .upload-area p {
            color: #F5F5DC !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
            font-weight: 600 !important;
        }

        #photoUploadModal .upload-area:hover {
            background-color: #6F4C3E !important;
            border-color: #FFC107 !important;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4) !important;
            transform: scale(1.02) !important;
        }

        #photoUploadModal .upload-area .fas {
            color: #FFC107 !important;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8)) !important;
        }
    `;
    document.head.appendChild(style);

    // Add hover effects for upload area - keep it DARK
    const uploadArea = modal.querySelector('.upload-area');
    uploadArea.addEventListener('mouseenter', function() {
        this.style.borderColor = '#FFC107';
        this.style.backgroundColor = '#6F4C3E'; // Slightly lighter dark brown on hover
        this.style.transform = 'scale(1.02)';
        this.style.boxShadow = '0 4px 15px rgba(255, 193, 7, 0.4)';
    });

    uploadArea.addEventListener('mouseleave', function() {
        this.style.borderColor = '#FFC107';
        this.style.backgroundColor = '#3B2A2A'; // Back to very dark brown
        this.style.transform = 'scale(1)';
        this.style.boxShadow = 'none';
    });

    // Add focus styles for form inputs
    const inputs = modal.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.backgroundColor = '#3B2A2A';
            this.style.borderColor = '#FFC107';
            this.style.color = '#F5F5DC';
            this.style.boxShadow = '0 0 0 0.2rem rgba(255, 193, 7, 0.25)';
        });

        input.addEventListener('blur', function() {
            this.style.backgroundColor = '#3B2A2A';
            this.style.borderColor = '#D69A6B';
            this.style.color = '#F5F5DC';
            this.style.boxShadow = 'none';
        });

        // Ensure text stays beige while typing
        input.addEventListener('input', function() {
            this.style.color = '#F5F5DC';
        });
    });

    return modal;
}

/**
 * Handle file selection for modal upload
 */
function handleModalFileSelection() {
    const fileInput = document.getElementById('modal-photo-files');
    const files = fileInput.files;
    
    if (files.length > 0) {
        displayFilePreview(files, 'modal-preview-grid');
        document.getElementById('modal-preview').style.display = 'block';
        document.getElementById('modal-options').style.display = 'block';
        document.getElementById('modal-upload-btn').style.display = 'inline-block';
    } else {
        clearModalSelection();
    }
}

/**
 * Handle file selection for inline forms
 */
function handleFileSelection(formId) {
    const fileInput = document.getElementById(`photo-files-${formId}`);
    const files = fileInput.files;
    
    if (files.length > 0) {
        displayFilePreview(files, `preview-grid-${formId}`);
        document.getElementById(`preview-${formId}`).style.display = 'block';
        document.getElementById(`options-${formId}`).style.display = 'block';
        document.getElementById(`actions-${formId}`).style.display = 'block';
    } else {
        clearSelection(formId);
    }
}

/**
 * Display file preview
 */
function displayFilePreview(files, containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    Array.from(files).forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="${file.name}" class="preview-image">
                    <button type="button" class="preview-remove" onclick="removePreviewItem(this, ${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        }
    });
}

/**
 * Remove preview item
 */
function removePreviewItem(button, index) {
    // This is a simplified version - in a full implementation,
    // you'd need to update the file input to remove the specific file
    button.parentElement.remove();
}

/**
 * Upload photos from modal
 */
async function uploadModalPhotos() {
    const form = document.querySelector('#photoUploadModal .upload-form');
    const fileInput = document.getElementById('modal-photo-files');
    
    if (!fileInput.files.length) {
        alert('Please select photos to upload');
        return;
    }
    
    await uploadPhotosFromForm(form, 'modal');
}

/**
 * Upload photos from inline form
 */
async function uploadPhotos(formId) {
    const form = document.querySelector(`#${formId} .upload-form`);
    const fileInput = document.getElementById(`photo-files-${formId}`);
    
    if (!fileInput.files.length) {
        alert('Please select photos to upload');
        return;
    }
    
    await uploadPhotosFromForm(form, formId);
}

/**
 * Upload photos from form
 */
async function uploadPhotosFromForm(form, prefix) {
    const formData = new FormData(form);
    const progressContainer = document.getElementById(`${prefix}-progress`);
    const progressBar = progressContainer.querySelector('.progress-bar');
    const progressText = progressContainer.querySelector('.progress-text');
    const uploadBtn = prefix === 'modal' ? 
        document.getElementById('modal-upload-btn') : 
        form.querySelector('button[onclick*="uploadPhotos"]');
    
    // Show progress
    progressContainer.style.display = 'block';
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
    
    try {
        const response = await fetch('/api/upload-photos.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            progressBar.style.width = '100%';
            progressText.textContent = `Successfully uploaded ${result.uploaded_count} photo(s)`;
            
            // Show success message
            setTimeout(() => {
                alert(result.message);
                
                // Close modal or reset form
                if (prefix === 'modal') {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('photoUploadModal'));
                    modal.hide();
                } else {
                    clearSelection(prefix);
                }
                
                // Reload page to show new photos
                location.reload();
            }, 1000);
            
        } else {
            throw new Error(result.message || 'Upload failed');
        }
        
    } catch (error) {
        console.error('Upload error:', error);
        progressText.textContent = 'Upload failed';
        alert('Upload failed: ' + error.message);
    } finally {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Photos';
        
        setTimeout(() => {
            progressContainer.style.display = 'none';
            progressBar.style.width = '0%';
        }, 3000);
    }
}

/**
 * Clear file selection
 */
function clearSelection(formId) {
    const fileInput = document.getElementById(`photo-files-${formId}`);
    fileInput.value = '';
    
    document.getElementById(`preview-${formId}`).style.display = 'none';
    document.getElementById(`options-${formId}`).style.display = 'none';
    document.getElementById(`actions-${formId}`).style.display = 'none';
    
    document.getElementById(`preview-grid-${formId}`).innerHTML = '';
}

/**
 * Clear modal selection
 */
function clearModalSelection() {
    const fileInput = document.getElementById('modal-photo-files');
    fileInput.value = '';
    
    document.getElementById('modal-preview').style.display = 'none';
    document.getElementById('modal-options').style.display = 'none';
    document.getElementById('modal-upload-btn').style.display = 'none';
    
    document.getElementById('modal-preview-grid').innerHTML = '';
}

/**
 * Clear upload form
 */
function clearUploadForm(modal) {
    const form = modal.querySelector('.upload-form');
    form.reset();
    clearModalSelection();
}

/**
 * Open lightbox
 */
function openLightbox(galleryId, index) {
    const photos = window.galleryData[galleryId];
    if (!photos || !photos[index]) return;
    
    currentLightboxGallery = galleryId;
    currentLightboxIndex = index;
    
    const modal = document.getElementById(`lightbox-${galleryId}`);
    if (!modal) return;
    
    updateLightboxContent(photos[index]);
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Navigate lightbox
 */
function navigateLightbox(galleryId, direction) {
    const photos = window.galleryData[galleryId];
    if (!photos) return;
    
    currentLightboxIndex += direction;
    
    if (currentLightboxIndex < 0) {
        currentLightboxIndex = photos.length - 1;
    } else if (currentLightboxIndex >= photos.length) {
        currentLightboxIndex = 0;
    }
    
    updateLightboxContent(photos[currentLightboxIndex]);
}

/**
 * Update lightbox content
 */
function updateLightboxContent(photo) {
    const modal = document.getElementById(`lightbox-${currentLightboxGallery}`);
    if (!modal) return;
    
    const image = modal.querySelector('#lightbox-image');
    const title = modal.querySelector('#lightbox-title');
    const description = modal.querySelector('#lightbox-description');
    const meta = modal.querySelector('#lightbox-meta');
    
    image.src = '/' + photo.file_path;
    image.alt = photo.alt_text || photo.title || 'Photo';
    
    title.textContent = photo.title || 'Photo';
    
    if (photo.description) {
        description.innerHTML = `<p>${photo.description}</p>`;
    } else {
        description.innerHTML = '';
    }
    
    const userName = (photo.first_name + ' ' + photo.last_name).trim() || photo.username || 'Anonymous';
    meta.innerHTML = `
        <small class="text-muted">
            <i class="fas fa-user me-1"></i>${userName}
            <i class="fas fa-clock ms-3 me-1"></i>${formatDateTime(photo.created_at)}
            ${photo.like_count > 0 ? `<i class="fas fa-heart ms-3 me-1 text-danger"></i>${photo.like_count}` : ''}
        </small>
    `;
}

/**
 * Close lightbox
 */
function closeLightbox() {
    if (currentLightboxGallery) {
        const modal = document.getElementById(`lightbox-${currentLightboxGallery}`);
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }
        currentLightboxGallery = null;
        currentLightboxIndex = 0;
    }
}

/**
 * Drag and drop handlers
 */
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        // Find the associated file input
        const fileInput = e.currentTarget.parentElement.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    }
}

/**
 * Format date time for display
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    // Less than 1 minute
    if (diff < 60000) {
        return 'Just now';
    }
    
    // Less than 1 hour
    if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }
    
    // Less than 1 day
    if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }
    
    // Less than 1 week
    if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
    
    // Format as date
    return date.toLocaleDateString();
}
