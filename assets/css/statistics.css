/* Statistics Page Styles */

.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card .card-body {
    padding: 2rem 1.5rem;
}

.stat-icon {
    opacity: 0.8;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Detailed Statistics Cards */
.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 12px 12px 0 0 !important;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.stat-item .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-item .stat-desc {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Journey Statistics */
.journey-stat {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.journey-stat:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.journey-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    margin-right: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.journey-icon i {
    font-size: 1.25rem;
}

.journey-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.journey-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Badge Items */
.badges-list {
    max-height: 400px;
    overflow-y: auto;
}

.badge-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.badge-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.badge-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.badge-emoji {
    font-size: 1.25rem;
}

.badge-name {
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.badge-desc {
    line-height: 1.4;
}

.badge-earned {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.badge-points {
    text-align: center;
}

/* Quick Actions */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn i {
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .journey-stat {
        flex-direction: column;
        text-align: center;
    }
    
    .journey-icon {
        margin-right: 0;
        margin-bottom: 0.75rem;
    }
    
    .badge-item {
        flex-direction: column;
        text-align: center;
    }
    
    .badge-icon {
        margin-right: 0;
        margin-bottom: 0.75rem;
        align-self: center;
    }
    
    .badge-points {
        margin-top: 0.75rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Color variations for different stats */
.stat-card:nth-child(4n+1) .stat-icon i { color: #007bff; }
.stat-card:nth-child(4n+2) .stat-icon i { color: #ffc107; }
.stat-card:nth-child(4n+3) .stat-icon i { color: #28a745; }
.stat-card:nth-child(4n+4) .stat-icon i { color: #dc3545; }

/* Progress bars */
.progress {
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 3px;
}

/* Custom scrollbar for badges list */
.badges-list::-webkit-scrollbar {
    width: 6px;
}

.badges-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.badges-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.badges-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading states */
.stat-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.stat-card.loading .stat-number::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Tooltips */
.tooltip-inner {
    background: #2c3e50;
    border-radius: 6px;
    font-size: 0.875rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #2c3e50;
}

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Badge rarity colors */
.bg-common { background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important; }
.bg-uncommon { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important; }
.bg-rare { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important; }
.bg-epic { background: linear-gradient(135deg, #6610f2 0%, #520dc2 100%) !important; }
.bg-legendary { background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%) !important; }
