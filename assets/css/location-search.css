/* Smart Location Search Styles */

.location-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    display: none;
    margin-top: 2px;
}

/* Clean desktop dropdown */
@media (min-width: 768px) {
    .location-suggestions {
        max-height: 350px;
    }
}

.location-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #212529 !important;
    background-color: #ffffff !important;
    font-weight: 500;
}

/* Clean desktop suggestions */
@media (min-width: 768px) {
    .location-suggestion {
        padding: 0.875rem 1.25rem;
        gap: 1rem;
    }
}

.location-suggestion:last-child {
    border-bottom: none;
}

.location-suggestion:hover,
.location-suggestion.active {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.location-suggestion:hover {
    background-color: #e9ecef !important;
    color: #212529 !important;
}

/* Force text color inheritance */
.location-suggestion * {
    color: inherit !important;
}

.location-suggestion div {
    color: #212529 !important;
}

.location-suggestion small {
    color: #6c757d !important;
}

.location-suggestion i {
    width: 18px;
    text-align: center;
    color: #0d6efd;
    flex-shrink: 0;
    font-size: 1rem;
}

.location-suggestion .text-primary {
    color: #0d6efd !important;
}

.location-suggestion .text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
}

.location-suggestion small {
    font-size: 0.75rem;
    line-height: 1.2;
    color: #6c757d;
}

/* Loading state */
.location-suggestions .dropdown-item {
    padding: 0.75rem 1rem;
    color: #495057;
    border: none;
    background: #ffffff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-suggestions .dropdown-item i {
    color: #0d6efd;
}

/* Enhanced search input styling */
.search-container input[name="location"] {
    position: relative;
}

.search-container .input-group {
    position: relative;
}

/* Clean desktop improvements */
@media (min-width: 768px) {
    .search-container input[name="location"] {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}

/* Geolocation indicator */
.location-input-container {
    position: relative;
}

.location-input-container::after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #28a745;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
    pointer-events: none;
}

.location-input-container.has-location::after {
    opacity: 1;
}

.location-input-container.has-location input {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
    .location-suggestions {
        max-height: 250px;
        font-size: 0.95rem;
    }

    .location-suggestion {
        padding: 0.75rem 1rem;
        gap: 0.5rem;
    }

    .location-suggestion i {
        width: 16px;
        font-size: 0.9rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .location-suggestions {
        background: #343a40;
        border-color: #495057;
        color: #f8f9fa;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    }

    .location-suggestion {
        border-bottom-color: #495057;
        color: #f8f9fa;
        background-color: #343a40;
    }

    .location-suggestion:hover,
    .location-suggestion.active {
        background-color: #495057 !important;
        color: #f8f9fa !important;
    }

    .location-suggestion:hover {
        background-color: #6c757d !important;
    }

    .location-suggestion .text-muted {
        color: #adb5bd !important;
    }

    .location-suggestions .dropdown-item {
        color: #f8f9fa;
        background: #343a40;
    }
}

/* Animation for suggestions appearing */
.location-suggestions.show {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Priority indicators */
.location-suggestion[data-priority="1"] i {
    color: #28a745; /* Nearby places - green */
}

.location-suggestion[data-priority="2"] i {
    color: #007bff; /* OSM results - blue */
}

.location-suggestion[data-priority="3"] i {
    color: #ffc107; /* Local database - amber */
}

/* Distance badge styling */
.location-suggestion .distance-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading spinner */
.location-suggestions .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced focus states */
input[name="location"]:focus + .location-suggestions {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Accessibility improvements */
.location-suggestion:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

/* No results state */
.location-suggestions .no-results {
    padding: 1rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    background-color: #ffffff;
}

/* Force text colors for better readability */
.location-suggestions * {
    color: inherit !important;
}

.location-suggestions .dropdown-item * {
    color: inherit !important;
}

/* Override any conflicting styles */
.location-suggestions .location-suggestion,
.location-suggestions .location-suggestion * {
    color: #212529 !important;
}

.location-suggestions .location-suggestion small {
    color: #6c757d !important;
}

.location-suggestions .location-suggestion i {
    color: #0d6efd !important;
}

/* Ensure text is always visible */
.location-suggestions {
    color: #212529 !important;
    background-color: #ffffff !important;
}

/* High specificity to override any other styles */
.location-suggestions .location-suggestion div,
.location-suggestions .location-suggestion span {
    color: #212529 !important;
    background: transparent !important;
}

/* Maximum specificity override */
.location-suggestions .location-suggestion .flex-grow-1 div {
    color: #212529 !important;
    font-weight: 500 !important;
}

.location-suggestions .location-suggestion .flex-grow-1 small {
    color: #6c757d !important;
}

/* Ensure all text elements are visible */
.location-suggestions .location-suggestion * {
    text-shadow: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Recent searches (if implemented) */
.location-suggestions .recent-header {
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #6c757d;
    letter-spacing: 0.5px;
}

.location-suggestions .recent-item {
    padding-left: 2rem;
}

.location-suggestions .recent-item i {
    color: #6c757d;
}
