/* Search Page Styles */

.search-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .search-card {
    background: var(--bg-secondary);
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.search-form {
    margin-bottom: 1rem;
}

.search-input-container {
    position: relative;
}

.search-input {
    border-radius: 8px;
    border: 2px solid var(--border-primary);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.search-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .search-input {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .search-input:focus {
    background-color: var(--bg-tertiary);
    border-color: var(--brand-primary);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    display: none;
    color: var(--text-primary);
}

.dark-mode .search-suggestions {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Search Results */
.search-result-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.search-result-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.dark-mode .search-result-card {
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.dark-mode .search-result-card:hover {
    box-shadow: 0 6px 20px rgba(0,0,0,0.4);
}

/* Search Header Dark Mode */
.search-header {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary);
}

.dark-mode .search-header {
    background-color: var(--bg-tertiary) !important;
}

/* Place Cards Dark Mode */
.place-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .place-card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.place-card .card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .place-card .card-body {
    background-color: var(--bg-secondary);
}

.place-card .card-title a {
    color: var(--text-primary);
}

.dark-mode .place-card .card-title a {
    color: var(--text-primary);
}

.place-card .card-title a:hover {
    color: var(--brand-primary);
}

/* Feature badges dark mode */
.place-features .badge.bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-primary);
}

.dark-mode .place-features .badge.bg-light {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

/* Filters Sidebar Dark Mode */
.filters-sidebar .card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .filters-sidebar .card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.filters-sidebar .card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .filters-sidebar .card-header {
    background-color: var(--bg-tertiary);
}

.filters-sidebar .card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .filters-sidebar .card-body {
    background-color: var(--bg-secondary);
}

.filter-title {
    color: var(--text-primary);
}

.form-check-label {
    color: var(--text-primary);
}

/* Form controls in filters */
.filters-sidebar .form-check-input {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
}

.dark-mode .filters-sidebar .form-check-input {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
}

.filters-sidebar .form-check-input:checked {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
}

/* Results header */
.results-header h5 {
    color: var(--text-primary);
}

.results-header .text-muted {
    color: var(--text-muted) !important;
}

/* Input groups dark mode */
.input-group-text {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

/* Search form input group sizing */
.search-form .input-group-text {
    height: calc(1.5em + 1.5rem + 2px) !important;
    padding: 0.75rem 1rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode .input-group-text {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

/* Form controls dark mode */
.form-control, .form-select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .form-control,
.dark-mode .form-select {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-primary);
    border-color: var(--brand-primary);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: var(--bg-tertiary);
    border-color: var(--brand-primary);
}

/* Pagination dark mode */
.pagination .page-link {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .pagination .page-link {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.pagination .page-item.active .page-link {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.pagination .page-link:hover {
    background-color: var(--bg-secondary);
    border-color: var(--brand-primary);
    color: var(--brand-primary);
}

.dark-mode .pagination .page-link:hover {
    background-color: var(--bg-tertiary);
}

/* View toggle buttons */
.view-toggle .btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-primary);
}

.dark-mode .view-toggle .btn-outline-secondary {
    color: var(--text-primary);
    border-color: var(--border-primary);
}

.view-toggle .btn-check:checked + .btn-outline-secondary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.result-image {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.beer-thumb {
    max-width: 50px;
    max-height: 50px;
    object-fit: contain;
    border-radius: 6px;
}

.beer-thumb-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.brewery-logo {
    max-width: 50px;
    max-height: 50px;
    object-fit: contain;
    border-radius: 6px;
}

.brewery-logo-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border: 2px dashed #28a745;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #28a745;
}

.user-avatar {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid #dee2e6;
}

.user-avatar-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px dashed #2196f3;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2196f3;
}

.result-info {
    min-width: 0;
}

.result-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.result-title a {
    color: inherit;
    transition: color 0.3s ease;
}

.result-title a:hover {
    color: #D69A6B;
}

.result-subtitle {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.rating .fas.fa-star.text-warning {
    color: #ffc107 !important;
}

.rating .fas.fa-star.text-muted {
    color: #dee2e6 !important;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    opacity: 0.5;
    margin-bottom: 1.5rem;
}

.empty-state h4, .empty-state h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.popular-searches {
    margin-top: 2rem;
}

.popular-search {
    transition: all 0.3s ease;
}

.popular-search:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Advanced Filters */
.card-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    color: white;
    border: none;
    border-radius: 12px 12px 0 0 !important;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem; /* Consistent padding */
    font-size: 1rem; /* Consistent font size */
    line-height: 1.5; /* Consistent line height */
    height: calc(1.5em + 0.75rem + 2px); /* Consistent height */
}

/* Search form specific sizing */
.search-form .form-control,
.search-form .form-select,
.search-form .btn {
    height: calc(1.5em + 1.5rem + 2px) !important; /* Uniform height for search form */
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
}

.form-control:focus, .form-select:focus {
    border-color: #D69A6B;
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

.form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.form-select-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #ff6600 !important; /* Solid orange */
    border: none !important;
    color: white !important;
    padding: 0.75rem 1rem !important; /* Match form control padding */
    height: calc(1.5em + 0.75rem + 2px) !important; /* Match form control height */
    font-size: 1rem !important; /* Match form control font size */
    line-height: 1.5 !important;
}

.btn-primary:hover {
    background: #e55a00 !important; /* Darker orange on hover */
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
}

.btn-primary:focus {
    background: #e55a00 !important;
    color: white !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 102, 0, 0.25);
}

.btn-primary:active {
    background: #cc4d00 !important; /* Even darker when clicked */
    color: white !important;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.3rem 0.5rem;
    font-size: 0.75rem;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-form .row {
        gap: 1rem !important;
    }
    
    .search-form .col-md-8,
    .search-form .col-md-3,
    .search-form .col-md-1 {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .search-result-card .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .result-image {
        margin-right: 0 !important;
        margin-bottom: 1rem;
        align-self: center;
    }
    
    .popular-searches .d-flex {
        flex-direction: column;
        align-items: center;
    }
    
    .popular-search {
        width: 100%;
        max-width: 200px;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .result-image {
        width: 50px;
        height: 50px;
    }

    .beer-thumb,
    .brewery-logo {
        max-width: 40px;
        max-height: 40px;
    }

    .beer-thumb-placeholder,
    .brewery-logo-placeholder {
        width: 40px;
        height: 40px;
    }

    .user-avatar,
    .user-avatar-placeholder {
        width: 40px;
        height: 40px;
    }

    /* Mobile place card button adjustments */
    .place-card .place-actions .btn {
        height: 28px;
        font-size: 0.8rem;
    }

    .place-card .place-actions .btn:not(.flex-fill) {
        width: 28px;
    }

    .place-card .place-actions .btn-sm i {
        font-size: 0.75rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-result-card {
    animation: fadeInUp 0.6s ease-out;
}

.search-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.search-result-card:nth-child(1) { animation-delay: 0.1s; }
.search-result-card:nth-child(2) { animation-delay: 0.2s; }
.search-result-card:nth-child(3) { animation-delay: 0.3s; }
.search-result-card:nth-child(4) { animation-delay: 0.4s; }
.search-result-card:nth-child(5) { animation-delay: 0.5s; }
.search-result-card:nth-child(6) { animation-delay: 0.6s; }

/* Loading States */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Custom Scrollbar for Suggestions */
.search-suggestions::-webkit-scrollbar {
    width: 6px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.search-suggestions::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus States */
.suggestion-item:focus {
    outline: 2px solid #D69A6B;
    outline-offset: -2px;
}

/* Search Type Indicators */
.result-title .badge {
    font-size: 0.7rem;
    vertical-align: middle;
}

/* Hover Effects */
.search-result-card:hover .result-title a {
    color: #D69A6B;
}

.suggestion-item:hover i {
    color: #D69A6B;
}

/* Place Card Button Fixes */
.place-card .place-actions .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.375rem;
}

.place-card .place-actions .btn-sm i {
    font-size: 0.875rem;
}

.place-card .place-actions .btn-outline-primary {
    border-width: 1px;
    padding: 0.375rem 0.5rem;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.place-card .place-actions .btn-outline-primary i {
    font-size: 0.8rem;
    margin: 0;
}

.place-card .place-actions {
    gap: 0.5rem;
}

.place-card .place-actions .flex-fill {
    flex: 1;
}

/* Ensure consistent button heights */
.place-card .place-actions .btn {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Icon-only buttons should be square */
.place-card .place-actions .btn:not(.flex-fill) {
    width: 32px;
    padding: 0;
}

/* Place Image Styling */
.place-img {
    transition: transform 0.3s ease;
    background-color: #f8f9fa;
}

.place-img:hover {
    transform: scale(1.05);
}

/* Image Loading States */
.place-img[src=""], .place-img:not([src]) {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #D69A6B;
    font-size: 2rem;
}

.place-img[src=""]:before, .place-img:not([src]):before {
    content: "🍺";
    font-size: 3rem;
}

/* Placeholder Image Fallback */
.place-image-placeholder {
    height: 200px;
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #D69A6B;
    font-size: 3rem;
    border-radius: 0.5rem 0.5rem 0 0;
}

.place-image-placeholder:before {
    content: "🍺";
}

/* Print Styles */
@media print {
    .search-suggestions {
        display: none !important;
    }

    .btn {
        display: none;
    }

    .search-result-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
    }
}
