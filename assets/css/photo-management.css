/**
 * Photo Management System CSS
 * Brewery-themed styling for photo galleries and management
 */

/* Photo Grid Layout */
.photo-grid {
    margin: 0 -0.5rem;
}

.photo-grid .col-md-3,
.photo-grid .col-sm-4,
.photo-grid .col-6 {
    padding: 0.5rem;
}

/* Photo Card Styling */
.photo-card {
    background: white;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(111, 76, 62, 0.1);
}

.photo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(111, 76, 62, 0.2);
    border-color: var(--brewery-medium-brown, #D69A6B);
}

/* Photo Thumbnail */
.photo-thumbnail {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background: var(--brewery-beige, #F5F5DC);
}

.photo-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.photo-thumbnail:hover img {
    transform: scale(1.05);
}

/* Photo Badges */
.photo-badges {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
}

.photo-badges .badge {
    margin-right: 4px;
    margin-bottom: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* Photo Actions */
.photo-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.photo-card:hover .photo-actions {
    opacity: 1;
}

.photo-actions .btn {
    margin-left: 4px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white !important;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.photo-actions .btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Photo Info */
.photo-info {
    padding: 12px;
    background: white;
}

.photo-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--brewery-very-dark, #2c1810) !important;
    margin-bottom: 6px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.photo-meta {
    margin: 0;
    font-size: 0.75rem;
    line-height: 1.4;
}

.photo-meta .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Album Grid (for album management) */
.album-grid {
    margin: 0 -0.75rem;
}

.album-grid .col-md-4,
.album-grid .col-sm-6 {
    padding: 0.75rem;
}

.album-card {
    background: white;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(111, 76, 62, 0.1);
}

.album-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 76, 62, 0.15);
    border-color: var(--brewery-medium-brown, #D69A6B);
}

.album-cover {
    position: relative;
    aspect-ratio: 16/9;
    background: linear-gradient(135deg, var(--brewery-beige, #F5F5DC) 0%, var(--brewery-light-brown, #E5D5C8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.album-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.album-cover .album-placeholder {
    color: var(--brewery-medium-brown, #D69A6B);
    font-size: 2rem;
    opacity: 0.6;
}

.album-info {
    padding: 16px;
}

.album-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brewery-very-dark, #2c1810) !important;
    margin-bottom: 8px;
    line-height: 1.3;
}

.album-description {
    font-size: 0.85rem;
    color: var(--brewery-text-light, #8B7355) !important;
    margin-bottom: 12px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.album-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Upload Area */
.upload-area {
    border: 3px dashed var(--brewery-medium-brown, #D69A6B);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: var(--brewery-beige, #F5F5DC);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--brewery-dark-brown, #6F4C3E);
    background: white;
    transform: scale(1.02);
}

.upload-area .upload-icon {
    font-size: 3rem;
    color: var(--brewery-medium-brown, #D69A6B);
    margin-bottom: 1rem;
}

.upload-area .upload-text {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.upload-area .upload-hint {
    color: var(--brewery-text-light, #8B7355) !important;
    font-size: 0.9rem;
}

/* Photo Viewer Modal */
.photo-viewer-modal .modal-dialog {
    max-width: 90vw;
    max-height: 90vh;
}

.photo-viewer-modal .modal-content {
    background: var(--brewery-very-dark, #2c1810);
    border: none;
    border-radius: 12px;
}

.photo-viewer-modal .modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: transparent;
}

.photo-viewer-modal .modal-title {
    color: white !important;
}

.photo-viewer-modal .btn-close {
    filter: invert(1);
}

.photo-viewer-modal .modal-body {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.photo-viewer-modal img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
}

/* Photo Details Panel */
.photo-details {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.photo-details h6 {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-weight: 700;
    margin-bottom: 1rem;
}

.photo-details .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.photo-details .detail-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
}

.photo-details .detail-value {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .photo-grid .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .photo-info {
        padding: 8px;
    }
    
    .photo-title {
        font-size: 0.8rem;
    }
    
    .photo-meta {
        font-size: 0.7rem;
    }
    
    .album-info {
        padding: 12px;
    }
    
    .album-title {
        font-size: 1rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .upload-area .upload-icon {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .photo-grid .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .album-grid .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Loading States */
.photo-loading {
    background: var(--brewery-beige, #F5F5DC);
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
}

.photo-loading .spinner-border {
    color: var(--brewery-medium-brown, #D69A6B);
}

/* Error States */
.photo-error {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
    color: #6c757d;
}

.photo-error .fas {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Ensure high contrast for all text */
.photo-card,
.album-card,
.photo-details {
    color: var(--brewery-very-dark, #2c1810) !important;
}

.photo-card .text-muted,
.album-card .text-muted,
.photo-details .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}
