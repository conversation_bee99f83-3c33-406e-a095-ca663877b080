/**
 * Album Management CSS
 * Brewery-themed styling for album management interface
 */

/* Album Grid Layout */
.album-grid {
    margin: 0 -0.75rem;
}

.album-grid .col-md-4,
.album-grid .col-sm-6 {
    padding: 0.75rem;
}

/* Album Card Styling */
.album-card {
    background: white;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(111, 76, 62, 0.1);
    position: relative;
}

.album-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(111, 76, 62, 0.2);
    border-color: var(--brewery-medium-brown, #D69A6B);
}

/* Album Cover */
.album-cover {
    position: relative;
    aspect-ratio: 16/9;
    background: linear-gradient(135deg, var(--brewery-beige, #F5F5DC) 0%, var(--brewery-light-brown, #E5D5C8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.album-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.album-card:hover .album-cover img {
    transform: scale(1.05);
}

.album-placeholder {
    color: var(--brewery-medium-brown, #D69A6B);
    font-size: 3rem;
    opacity: 0.6;
}

/* Album Actions */
.album-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.album-card:hover .album-actions {
    opacity: 1;
}

.album-actions .btn {
    margin-left: 4px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white !important;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.album-actions .btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Photo Count Badge */
.photo-count-badge {
    position: absolute;
    bottom: 12px;
    left: 12px;
    z-index: 2;
}

.photo-count-badge .badge {
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 6px 10px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Album Info */
.album-info {
    padding: 20px;
    background: white;
}

.album-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--brewery-very-dark, #2c1810) !important;
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.album-description {
    font-size: 0.9rem;
    color: var(--brewery-text-light, #8B7355) !important;
    margin-bottom: 12px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Album Meta */
.album-meta {
    border-top: 1px solid var(--brewery-light-brown, #E5D5C8);
    padding-top: 12px;
}

.album-owner {
    margin-bottom: 8px;
}

.album-owner i {
    color: var(--brewery-medium-brown, #D69A6B);
}

.album-owner small {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 600;
}

.album-stats {
    margin-bottom: 8px;
}

.album-stats .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
    font-size: 0.8rem;
}

.album-stats i {
    color: var(--brewery-medium-brown, #D69A6B);
}

.album-badges {
    display: flex;
    gap: 6px;
}

.album-badges .badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 4px 8px;
}

/* Modal Styling */
.modal .form-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 6px;
}

.modal .form-control,
.modal .form-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 8px;
    padding: 10px 12px;
    transition: border-color 0.3s ease;
}

.modal .form-control:focus,
.modal .form-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

.modal .form-check-input:checked {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    border-color: var(--brewery-dark-brown, #6F4C3E);
}

.modal .form-check-label {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 500;
}

/* Modal Header */
.modal-header {
    background: linear-gradient(135deg, var(--brewery-dark-brown, #6F4C3E) 0%, var(--brewery-medium-brown, #D69A6B) 100%);
    color: white;
    border-bottom: none;
}

.modal-header .modal-title {
    color: white !important;
    font-weight: 700;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Modal Body */
.modal-body {
    background-color: #fafafa;
    padding: 24px;
}

/* Modal Footer */
.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid var(--brewery-light-brown, #E5D5C8);
}

/* Owner Display */
.album-owner-info {
    background: var(--brewery-beige, #F5F5DC);
    border: 1px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 8px;
    padding: 12px;
}

.owner-display {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 4px;
}

/* Filter Form */
.filter-form {
    background-color: rgba(245, 245, 220, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
}

.filter-form .form-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 6px;
}

.filter-form .form-control,
.filter-form .form-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    background-color: white;
    color: var(--brewery-text-dark, #4A3728) !important;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

/* Statistics Cards - Use existing admin.css styles */
.stats-row .card {
    background-color: white !important;
    border: 2px solid var(--brewery-light-brown, #E5D5C8) !important;
}

.stats-row .card .card-title {
    color: #000000 !important;
    font-weight: 800 !important;
    font-size: 1.8rem !important;
}

.stats-row .card .card-text {
    color: #333333 !important;
    font-weight: 600 !important;
}

/* Table Header Actions */
.table-header-actions {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    color: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
    border-bottom: 2px solid var(--brewery-medium-brown, #D69A6B);
}

.table-header-actions h5 {
    color: white !important;
    margin: 0;
    font-weight: 700;
}

.table-header-actions .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .album-grid .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .album-info {
        padding: 16px;
    }
    
    .album-title {
        font-size: 1.1rem;
    }
    
    .album-description {
        font-size: 0.85rem;
    }
    
    .album-actions .btn {
        width: 32px;
        height: 32px;
    }
    
    .photo-count-badge .badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

@media (max-width: 576px) {
    .album-grid .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .album-cover {
        aspect-ratio: 4/3;
    }
    
    .album-placeholder {
        font-size: 2rem;
    }
    
    .modal-body {
        padding: 16px;
    }
}

/* Loading and Error States */
.album-loading {
    background: var(--brewery-beige, #F5F5DC);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    border-radius: 16px;
}

.album-loading .spinner-border {
    color: var(--brewery-medium-brown, #D69A6B);
}

.album-error {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #6c757d;
    border-radius: 16px;
}

.album-error .fas {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Ensure high contrast for all text */
.album-card,
.modal,
.filter-form {
    color: var(--brewery-very-dark, #2c1810) !important;
}

.album-card .text-muted,
.modal .text-muted,
.filter-form .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Badge customization */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: var(--brewery-amber, #FFC107) !important;
    color: var(--brewery-very-dark, #2c1810) !important;
}

.badge.bg-dark {
    background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Button styling consistency */
.btn-outline-info {
    color: var(--brewery-medium-brown, #D69A6B) !important;
    border-color: var(--brewery-medium-brown, #D69A6B) !important;
}

.btn-outline-info:hover {
    background-color: var(--brewery-medium-brown, #D69A6B) !important;
    border-color: var(--brewery-medium-brown, #D69A6B) !important;
    color: white !important;
}

/* Special album effects */
.album-card.featured {
    border-color: var(--brewery-amber, #FFC107);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.album-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--brewery-amber, #FFC107), var(--brewery-medium-brown, #D69A6B));
    z-index: 1;
}
