/* Authentication Pages CSS - Dark Mode Support */

/* Light Mode (Default) */
.auth-container {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
}

.auth-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .auth-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Card styling */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .card-header {
    background-color: var(--bg-tertiary);
}

.card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .card-body {
    background-color: var(--bg-secondary);
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.dark-mode .form-control,
.dark-mode .form-select {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--border-primary) !important;
    color: var(--text-primary) !important;
}

/* Ensure dark mode applies to all form inputs */
html.dark-mode input[type="email"],
html.dark-mode input[type="password"],
html.dark-mode input[type="text"],
html.dark-mode textarea,
html.dark-mode select {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--border-primary) !important;
    color: var(--text-primary) !important;
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-primary);
    border-color: var(--brand-primary);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: var(--bg-tertiary);
    border-color: var(--brand-primary);
    color: var(--text-primary);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
}

.form-check-label {
    color: var(--text-primary);
}

/* Buttons */
.btn-primary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--brand-secondary);
    border-color: var(--brand-secondary);
    color: white;
}

.btn-outline-primary {
    color: var(--brand-primary);
    border-color: var(--brand-primary);
}

.btn-outline-primary:hover {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-primary);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

/* Text colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--brand-primary) !important;
}

/* Demo accounts card */
.border-info {
    border-color: var(--info) !important;
}

.bg-info {
    background-color: var(--info) !important;
    color: white !important;
}

/* Debug card */
.border-warning {
    border-color: var(--warning) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
    color: var(--text-primary) !important;
}

.dark-mode .bg-warning {
    color: #000000 !important;
}

/* Alert styling */
.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: var(--info);
    color: var(--text-primary);
}

.dark-mode .alert-info {
    background-color: rgba(13, 202, 240, 0.2);
}

/* Table styling for debug info */
.table {
    color: var(--text-primary);
}

.dark-mode .table {
    color: var(--text-primary);
}

.table td, .table th {
    border-color: var(--border-primary);
    background-color: transparent;
}

.table-bordered {
    border: 1px solid var(--border-primary);
}

/* Input group styling */
.input-group .btn {
    border-color: var(--border-primary);
}

.dark-mode .input-group .btn {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .input-group .btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Links */
a {
    color: var(--brand-primary);
    text-decoration: none;
}

a:hover {
    color: var(--brand-secondary);
    text-decoration: underline;
}

/* Icons */
.fas, .fa {
    color: inherit;
}

/* Container styling */
.container {
    color: var(--text-primary);
}

/* HR styling */
hr {
    border-color: var(--border-primary);
    opacity: 0.5;
}

/* Form check input */
.form-check-input {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
}

.dark-mode .form-check-input {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
}

.form-check-input:checked {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
}

/* Small text */
small {
    color: var(--text-muted);
}

/* Ensure proper contrast */
.dark-mode .text-dark {
    color: var(--text-primary) !important;
}

.dark-mode .text-white {
    color: #ffffff !important;
}
