/* Recommendations Page Styles */

.personalization-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.personalization-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 1rem;
}

/* Section Headers */
.section-header {
    margin-bottom: 2rem;
}

.section-title {
    color: #2c3e50;
    font-weight: 700;
    margin: 0;
}

/* Beer Cards */
.beer-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.beer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.beer-image {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.beer-thumb {
    max-width: 80px;
    max-height: 100px;
    object-fit: contain;
    border-radius: 8px;
}

.beer-thumb-placeholder {
    width: 80px;
    height: 100px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.beer-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.beer-name a {
    color: #2c3e50;
    transition: color 0.3s ease;
}

.beer-name a:hover {
    color: #007bff;
}

.brewery-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.beer-stats {
    font-size: 0.875rem;
}

.rating .fas.fa-star.text-warning {
    color: #ffc107 !important;
}

.rating .fas.fa-star.text-muted {
    color: #dee2e6 !important;
}

.recommendation-reason {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 0.5rem;
    font-style: italic;
}

/* Brewery Cards */
.brewery-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.brewery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.brewery-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
}

.brewery-name a {
    color: inherit;
    transition: color 0.3s ease;
}

.brewery-name a:hover {
    color: #28a745;
}

.brewery-location {
    font-size: 0.9rem;
}

.brewery-stats .stat-number {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.brewery-stats .stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Trending Cards */
.trending-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.trending-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.trending-rank {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    z-index: 1;
}

.rank-number {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    font-weight: 700;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.trending-stats {
    margin-top: 0.75rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-outline-success {
    border: 2px solid #28a745;
    color: #28a745;
}

.btn-outline-success:hover {
    background: #28a745;
    border-color: #28a745;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.4rem 0.6rem;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    opacity: 0.5;
    margin-bottom: 1.5rem;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Card Footers */
.card-footer {
    border: none;
    background: #f8f9fa;
    padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .personalization-card .row {
        text-align: center;
    }
    
    .personalization-card .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .beer-card .card-footer .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .brewery-card .card-footer .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .trending-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .beer-image {
        height: 100px;
    }
    
    .beer-thumb {
        max-width: 60px;
        max-height: 80px;
    }
    
    .beer-thumb-placeholder {
        width: 60px;
        height: 80px;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.beer-card, .brewery-card, .trending-card {
    animation: fadeInUp 0.6s ease-out;
}

.personalization-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.beer-card:nth-child(1) { animation-delay: 0.1s; }
.beer-card:nth-child(2) { animation-delay: 0.2s; }
.beer-card:nth-child(3) { animation-delay: 0.3s; }
.beer-card:nth-child(4) { animation-delay: 0.4s; }

.brewery-card:nth-child(1) { animation-delay: 0.1s; }
.brewery-card:nth-child(2) { animation-delay: 0.2s; }
.brewery-card:nth-child(3) { animation-delay: 0.3s; }

.trending-card:nth-child(1) { animation-delay: 0.1s; }
.trending-card:nth-child(2) { animation-delay: 0.2s; }
.trending-card:nth-child(3) { animation-delay: 0.3s; }
.trending-card:nth-child(4) { animation-delay: 0.4s; }

/* Hover effects */
.beer-card:hover .beer-name a {
    color: #007bff;
}

.brewery-card:hover .brewery-name a {
    color: #28a745;
}

.trending-card:hover .rank-number {
    transform: scale(1.1);
}

/* Loading states */
.card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.card.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Special effects for trending items */
.trending-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #fd7e14, #ffc107);
    border-radius: 12px 12px 0 0;
}

/* Recommendation type indicators */
.recommendation-reason .fas.fa-lightbulb {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}
