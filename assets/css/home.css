/* Home Page Styles - Enhanced Social Dark Mode */

.hero-section {
    background: linear-gradient(135deg,
        var(--bg-primary) 0%,
        var(--beer-dark) 25%,
        var(--beer-copper) 50%,
        var(--beer-amber) 75%,
        var(--beer-gold) 100%);
    position: relative;
    overflow: hidden;
    min-height: 70vh;
}

/* Enhanced Text Styling */
.text-gradient {
    background: linear-gradient(45deg, var(--beer-gold), var(--beer-foam), var(--beer-amber));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.beer-emoji {
    font-size: 1.2em;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Enhanced Stats Styling */
.social-stats, .user-quick-stats {
    background: rgba(26, 26, 26, 0.8);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-primary);
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--beer-gold);
    text-shadow: 0 0 10px rgba(255, 179, 71, 0.5);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Pulse Button Animation */
.pulse-btn {
    animation: pulse 2s infinite;
    box-shadow: 0 0 20px rgba(255, 179, 71, 0.3);
}

@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 0 20px rgba(255, 179, 71, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(255, 179, 71, 0.5); }
    100% { transform: scale(1); box-shadow: 0 0 20px rgba(255, 179, 71, 0.3); }
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="beer-bubbles" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="rgba(255,245,220,0.1)" opacity="0.6"><animate attributeName="r" values="1;3;1" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="80" r="1.5" fill="rgba(255,179,71,0.15)" opacity="0.4"><animate attributeName="r" values="0.5;2.5;0.5" dur="4s" repeatCount="indefinite"/></circle><circle cx="50" cy="10" r="1" fill="rgba(255,245,220,0.08)" opacity="0.3"><animate attributeName="r" values="0.5;2;0.5" dur="2.5s" repeatCount="indefinite"/></circle><circle cx="10" cy="60" r="1.2" fill="rgba(255,140,66,0.12)" opacity="0.5"><animate attributeName="r" values="0.8;2.2;0.8" dur="3.5s" repeatCount="indefinite"/></circle><circle cx="90" cy="40" r="0.8" fill="rgba(255,245,220,0.06)" opacity="0.2"><animate attributeName="r" values="0.3;1.8;0.3" dur="2.8s" repeatCount="indefinite"/></circle></pattern></defs><rect width="100" height="100" fill="url(%23beer-bubbles)"/></svg>');
    opacity: 0.4;
}

/* Floating Beer Cards */
.hero-visual {
    height: 400px;
    position: relative;
}

.floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(26, 26, 26, 0.9);
    border: 2px solid var(--beer-gold);
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.card-3 {
    top: 30%;
    right: 5%;
    animation-delay: 4s;
}

.beer-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.beer-name {
    font-weight: bold;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.beer-rating {
    font-size: 0.8rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(-10px) rotate(-1deg); }
}

/* Central Beer Icon with Bubbles */
.hero-beer-icon {
    position: relative;
    display: inline-block;
    margin-top: 2rem;
}

.text-beer-gold {
    color: var(--beer-gold);
    text-shadow: 0 0 20px rgba(255, 179, 71, 0.5);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(255, 179, 71, 0.5); }
    to { text-shadow: 0 0 30px rgba(255, 179, 71, 0.8), 0 0 40px rgba(255, 179, 71, 0.6); }
}

/* Beer Bubbles Animation */
.beer-bubbles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.bubble {
    position: absolute;
    background: rgba(255, 245, 220, 0.6);
    border-radius: 50%;
    animation: bubble-rise 4s infinite linear;
}

.bubble-1 {
    width: 8px;
    height: 8px;
    left: 20%;
    animation-delay: 0s;
}

.bubble-2 {
    width: 12px;
    height: 12px;
    left: 40%;
    animation-delay: 1s;
}

.bubble-3 {
    width: 6px;
    height: 6px;
    left: 60%;
    animation-delay: 2s;
}

.bubble-4 {
    width: 10px;
    height: 10px;
    left: 80%;
    animation-delay: 3s;
}

.bubble-5 {
    width: 14px;
    height: 14px;
    left: 30%;
    animation-delay: 1.5s;
}

@keyframes bubble-rise {
    0% {
        bottom: -20px;
        opacity: 0;
        transform: scale(0);
    }
    10% {
        opacity: 1;
        transform: scale(1);
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100%;
        opacity: 0;
        transform: scale(0.5);
    }
}

/* Background Animation Bubbles */
.hero-bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-bubble {
    position: absolute;
    background: radial-gradient(circle, rgba(255, 179, 71, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: bg-float 20s infinite linear;
}

.bg-bubble-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: -100px;
    animation-delay: 0s;
}

.bg-bubble-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: -75px;
    animation-delay: 7s;
}

.bg-bubble-3 {
    width: 100px;
    height: 100px;
    top: 80%;
    left: 20%;
    animation-delay: 14s;
}

@keyframes bg-float {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    33% { transform: translateX(100px) translateY(-50px) rotate(120deg); }
    66% { transform: translateX(-50px) translateY(-100px) rotate(240deg); }
    100% { transform: translateX(0) translateY(0) rotate(360deg); }
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-section h1 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section .lead {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.hero-section .btn {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Feature Cards */
.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.card:hover .feature-icon i {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* CTA Section */
.bg-light {
    background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section .lead {
        font-size: 1.1rem;
    }
    
    .hero-section .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .hero-section .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-section .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .feature-icon i {
        font-size: 2rem !important;
    }
}
