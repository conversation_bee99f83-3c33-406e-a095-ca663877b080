/* Check-in Page Styles - Brewery Theme */

/* Page background */
body {
    background-color: #000000 !important;
    color: #F5F5DC !important;
}

.container {
    background: transparent;
}

.selected-beer {
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border: 2px solid #FFC107;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: #F5F5DC;
}

.selected-beer:hover {
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    border-color: #D69A6B;
}

.beer-thumb {
    max-width: 80px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.beer-thumb-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border: 2px dashed #D69A6B;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #F5F5DC;
}

/* Beer Search */
.beer-search {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #3B2A2A;
    border: 1px solid #D69A6B;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(214, 154, 107, 0.3);
    color: #F5F5DC;
}

.search-result-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #6F4C3E;
    color: #F5F5DC;
}

.search-result-item:hover {
    background-color: #6F4C3E;
}

.search-result-item:last-child {
    border-bottom: none;
}

.beer-thumb-small {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
}

.beer-thumb-small-placeholder {
    width: 40px;
    height: 40px;
    background: #6F4C3E;
    border: 1px solid #D69A6B;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: #F5F5DC;
}

/* Rating Section */
.rating-section {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    border: 2px solid #FFC107;
    border-radius: 12px;
    color: #F5F5DC;
}

.rating-input {
    padding: 1rem;
}

.stars {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.stars i {
    font-size: 1.5rem;
    color: #F5F5DC !important; /* White/beige by default */
    cursor: pointer;
    transition: all 0.3s ease;
}

.stars i:hover,
.stars i.hover {
    color: #FFC107 !important; /* Yellow on hover */
    transform: scale(1.1);
}

.stars i.active {
    color: #FFC107 !important; /* Always yellow when selected */
}

.rating-text {
    font-size: 0.875rem;
    color: #F5F5DC;
    font-weight: 500;
    text-align: center;
}

/* Cards */
.card {
    background: #3B2A2A;
    border: 1px solid #D69A6B;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(214, 154, 107, 0.3);
    color: #F5F5DC;
}

.card-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    color: #F5F5DC;
    border-radius: 12px 12px 0 0 !important;
    border: none;
    border-bottom: 1px solid #D69A6B;
}

.card-body {
    padding: 2rem;
    background: #3B2A2A;
}

/* Form Elements */
.form-label {
    font-weight: 600;
    color: #F5F5DC;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    background-color: #6F4C3E;
    border: 2px solid #D69A6B;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    color: #F5F5DC;
}

.form-control:focus, .form-select:focus {
    background-color: #3B2A2A;
    border-color: #FFC107;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    color: #F5F5DC;
}

.form-control::placeholder {
    color: #D69A6B;
    opacity: 0.8;
}

.input-group .btn {
    border-radius: 0 8px 8px 0;
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border: none;
    color: #3B2A2A;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%);
    color: #F5F5DC;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

/* Check-in button - Always yellow */
button[type="submit"] {
    background: linear-gradient(135deg, #FFC107 0%, #FFD700 100%) !important;
    border: none !important;
    color: #3B2A2A !important;
    font-weight: 700 !important;
}

button[type="submit"]:hover {
    background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%) !important;
    color: #3B2A2A !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.5) !important;
}

.btn-outline-secondary {
    border: 2px solid #F5F5DC;
    color: #F5F5DC;
    background: transparent;
    font-weight: 600;
}

.btn-outline-secondary:hover {
    background: #F5F5DC;
    border-color: #F5F5DC;
    color: #3B2A2A;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 245, 220, 0.3);
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.4rem 0.6rem;
}

.bg-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Checkbox Styles */
.form-check-input:checked {
    background-color: #FFC107;
    border-color: #FFC107;
}

.form-check-label {
    font-weight: 500;
    color: #F5F5DC;
}

/* Location Detection */
.btn#detectLocation {
    transition: all 0.3s ease;
}

.btn#detectLocation:hover {
    background: #6F4C3E;
    border-color: #FFC107;
    color: #FFC107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
    }
    
    .stars i {
        font-size: 1.25rem;
    }
    
    .selected-beer .row {
        text-align: center;
    }
    
    .selected-beer .col-md-2 {
        margin-bottom: 1rem;
    }
    
    .beer-thumb {
        max-width: 60px;
    }
    
    .beer-thumb-placeholder {
        width: 60px;
        height: 60px;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.selected-beer {
    animation: fadeInUp 0.8s ease-out;
}

/* Star animation effects */
.stars i {
    animation: starPulse 0.3s ease-out;
}

@keyframes starPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stars i.active {
    animation: starGlow 0.5s ease-out;
}

@keyframes starGlow {
    0% { 
        color: #dee2e6;
        text-shadow: none;
    }
    50% { 
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
    }
    100% { 
        color: #ffc107;
        text-shadow: none;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Quick Actions Readability Improvements */
.quick-actions .btn {
    min-height: 60px;
    padding: 12px 16px;
    font-weight: 600;
    border-width: 2px;
    transition: all 0.3s ease;
}

.quick-actions .btn small {
    font-size: 0.875rem;
    font-weight: 600;
    color: inherit;
}

.quick-actions .btn i {
    font-size: 1.25rem;
    margin-bottom: 4px;
}

/* Enhanced contrast for outline buttons */
.quick-actions .btn-outline-primary {
    color: #FFC107;
    border-color: #FFC107;
    background-color: rgba(255, 193, 7, 0.1);
}

.quick-actions .btn-outline-primary:hover {
    color: #3B2A2A;
    background-color: #FFC107;
    border-color: #FFC107;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.quick-actions .btn-outline-success {
    color: #155724;
    border-color: #155724;
    background-color: rgba(40, 167, 69, 0.05);
}

.quick-actions .btn-outline-success:hover {
    color: #ffffff;
    background-color: #155724;
    border-color: #155724;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(21, 87, 36, 0.3);
}

.quick-actions .btn-outline-info {
    color: #0c5460;
    border-color: #0c5460;
    background-color: rgba(23, 162, 184, 0.05);
}

.quick-actions .btn-outline-info:hover {
    color: #ffffff;
    background-color: #0c5460;
    border-color: #0c5460;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(12, 84, 96, 0.3);
}

.quick-actions .btn-outline-secondary {
    color: #495057;
    border-color: #495057;
    background-color: rgba(108, 117, 125, 0.05);
}

.quick-actions .btn-outline-secondary:hover {
    color: #ffffff;
    background-color: #495057;
    border-color: #495057;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(73, 80, 87, 0.3);
}

.quick-actions .btn-outline-warning {
    color: #856404;
    border-color: #856404;
    background-color: rgba(255, 193, 7, 0.05);
}

.quick-actions .btn-outline-warning:hover {
    color: #ffffff;
    background-color: #856404;
    border-color: #856404;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(133, 100, 4, 0.3);
}

/* Quick Actions Card Styling */
.quick-actions .card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.quick-actions .card-body {
    padding: 1.5rem;
}

/* Form validation styles */
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Search result highlighting */
.search-result-item .fw-bold {
    color: #FFC107;
}

.search-result-item:hover .fw-bold {
    color: #F5F5DC;
}

/* Location input enhancements */
.input-group .form-control {
    border-radius: 8px 0 0 8px;
}

.input-group .btn {
    border-left: none;
}

/* Rating section enhancements */
.rating-section h6 {
    color: #F5F5DC;
    margin-bottom: 1rem;
}

/* Serving style select styling */
.form-select option {
    padding: 0.5rem;
}

/* Comment textarea styling */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* Privacy section styling */
.form-check {
    padding: 1rem;
}

.form-check-label {
    color: #F5F5DC;
}

.form-check-input:checked {
    background-color: #FFC107;
    border-color: #FFC107;
}

.form-check-input:focus {
    border-color: #FFC107;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
}

/* Text colors */
.text-muted {
    color: #D69A6B !important;
}

.fw-bold {
    color: #F5F5DC !important;
}

/* Input group styling */
.input-group-text {
    background-color: #D69A6B;
    border-color: #D69A6B;
    color: #F5F5DC;
}

/* Badge styling */
.badge {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    color: #3B2A2A;
}

/* Photo upload styling - Dark theme */
.upload-dropzone {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px dashed #D69A6B !important;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #F5F5DC !important;
}

.upload-dropzone:hover {
    background: linear-gradient(135deg, #2d2d2d 0%, #3B2A2A 100%) !important;
    border-color: #FFC107 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.upload-dropzone i {
    color: #D69A6B !important;
}

.upload-dropzone:hover i {
    color: #FFC107 !important;
}

.upload-dropzone h6 {
    color: #F5F5DC !important;
    margin-bottom: 1rem;
}

.upload-dropzone p {
    color: #D69A6B !important;
    margin-bottom: 0.5rem;
}

.upload-dropzone .small {
    color: #6F4C3E !important;
}

/* Photo preview styling - Dark theme */
.file-preview {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #D69A6B !important;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
    color: #F5F5DC !important;
}

.file-preview h6 {
    color: #F5F5DC !important;
    margin-bottom: 1rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: #3B2A2A;
    border: 1px solid #D69A6B;
}

.preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
}

.preview-item .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 193, 7, 0.9);
    color: #3B2A2A;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-item .remove-btn:hover {
    background: #FFC107;
}
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.form-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}
