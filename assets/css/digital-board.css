/* Digital Beer Board Admin CSS */

/* Board Cards */
.board-card {
    transition: all 0.3s ease;
    border: 1px solid var(--border-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.board-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .board-card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.dark-mode .board-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Board Preview */
.board-preview {
    position: relative;
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #444;
}

.preview-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-content {
    text-align: center;
    color: #ffffff;
    font-size: 0.75rem;
    line-height: 1.2;
}

.preview-header {
    font-weight: bold;
    color: #ffc107;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
}

.preview-beer {
    margin: 0.2rem 0;
    color: #e9ecef;
    font-size: 0.7rem;
}

/* Activity Feed */
.activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.activity-icon i {
    color: white;
    font-size: 0.8rem;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Status Badges */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000000 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .card-header {
    background-color: var(--bg-tertiary);
}

.card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .card-body {
    background-color: var(--bg-secondary);
}

/* Buttons */
.btn-primary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--brand-secondary);
    border-color: var(--brand-secondary);
    color: white;
}

.btn-outline-primary {
    color: var(--brand-primary);
    border-color: var(--brand-primary);
}

.btn-outline-primary:hover {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-primary);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

/* Dropdown menus */
.dropdown-menu {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .dropdown-menu {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.dropdown-item {
    color: var(--text-primary);
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.dark-mode .dropdown-item:hover {
    background-color: var(--bg-tertiary);
}

/* View toggle buttons */
.btn-group .btn-check:checked + .btn-outline-secondary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

/* Text colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--brand-primary) !important;
}

/* Statistics cards */
.card.text-center {
    transition: transform 0.2s ease;
}

.card.text-center:hover {
    transform: translateY(-2px);
}

.card.text-center .card-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card.text-center .card-title.text-primary {
    color: var(--brand-primary) !important;
}

.card.text-center .card-title.text-success {
    color: var(--success) !important;
}

.card.text-center .card-title.text-warning {
    color: var(--warning) !important;
}

.card.text-center .card-title.text-info {
    color: var(--info) !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .board-preview {
        height: 100px;
    }
    
    .preview-content {
        font-size: 0.7rem;
    }
    
    .preview-header {
        font-size: 0.75rem;
    }
    
    .preview-beer {
        font-size: 0.65rem;
    }
    
    .activity-icon {
        width: 28px;
        height: 28px;
    }
    
    .activity-icon i {
        font-size: 0.7rem;
    }
}

/* Animation for board cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.board-card {
    animation: fadeInUp 0.5s ease-out;
}

.board-card:nth-child(1) { animation-delay: 0.1s; }
.board-card:nth-child(2) { animation-delay: 0.2s; }
.board-card:nth-child(3) { animation-delay: 0.3s; }
.board-card:nth-child(4) { animation-delay: 0.4s; }

/* Loading states */
.loading-placeholder {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
