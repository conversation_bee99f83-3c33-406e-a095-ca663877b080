/* Simplified Homepage Styles - Dark Mode Optimized */

/* Brewery-Themed Warm Palette */
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

/* Hero Banner */
.hero-banner {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    border-bottom: 1px solid #D69A6B !important;
    color: #F5F5DC !important;
}

/* Hero Section Overrides */
.hero-section {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    color: #F5F5DC !important;
}

.hero-section h1 {
    color: #F5F5DC !important;
}

.hero-section p {
    color: #D69A6B !important;
}

.hero-section .text-white {
    color: #F5F5DC !important;
}

/* Hero Search Form */
.hero-section .form-control {
    background-color: #6F4C3E !important;
    border-color: #D69A6B !important;
    color: #F5F5DC !important;
}

.hero-section .form-control::placeholder {
    color: #D69A6B !important;
}

.hero-section .form-control:focus {
    background-color: #6F4C3E !important;
    border-color: #FFC107 !important;
    color: #F5F5DC !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
}

.hero-section .input-group-text {
    background-color: #D69A6B !important;
    border-color: #D69A6B !important;
    color: #3B2A2A !important;
}

.hero-icon {
    animation: gentle-bounce 3s ease-in-out infinite;
}

@keyframes gentle-bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Featured Places Section */
.featured-places-section {
    background-color: #3B2A2A !important;
    border-bottom: 1px solid #D69A6B !important;
    color: #F5F5DC !important;
}

.featured-places-section h2 {
    color: #F5F5DC !important;
}

.featured-places-section p {
    color: #D69A6B !important;
}

/* Featured Place Cards - Compact Industry Standard */
.featured-place-card {
    background-color: #6F4C3E !important;
    border: 1px solid #D69A6B !important;
    border-radius: 0.5rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 100%;
    max-width: 100%;
}

.featured-place-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.3) !important;
    border-color: #FFC107 !important;
}

/* Card Image Container - Compact */
.featured-place-card .place-image {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem 0.5rem 0 0;
}

.featured-place-card .card-img-top {
    transition: transform 0.3s ease;
    width: 100%;
    height: 160px; /* Reduced from 200px */
    object-fit: cover;
}

.featured-place-card:hover .card-img-top {
    transform: scale(1.03); /* Subtle zoom */
}

/* Image Overlay Badges - Compact */
.featured-place-card .place-badge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    z-index: 2;
}

.featured-place-card .place-rating {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 2;
}

.featured-place-card .place-badge .badge,
.featured-place-card .place-rating .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    backdrop-filter: blur(4px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Card Content - Ultra Compact Layout */
.featured-place-card .card-body {
    padding: 0.625rem; /* Ultra compact */
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between; /* Push actions to bottom */
}

/* Content Group - Tight spacing for main content */
.card-content-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem; /* Minimal spacing between content elements */
}

.featured-place-card .card-title {
    margin-bottom: 0 !important; /* Remove default margin */
    font-size: 1rem;
    line-height: 1.2;
}

.featured-place-card .card-title a {
    color: #F5F5DC !important;
    text-decoration: none;
    font-weight: 600;
    display: block;
}

.featured-place-card .card-title a:hover {
    color: #FFC107 !important;
}

.featured-place-card .card-text {
    color: #D69A6B !important;
    line-height: 1.4;
    margin-bottom: 0 !important;
    font-size: 0.85rem;
}

.featured-beer {
    color: #FFC107 !important;
    font-weight: 500;
}

.featured-beer small {
    font-size: 0.8rem;
}

/* Place Actions - No Gap */
.place-actions {
    margin-top: 0; /* Remove gap completely */
}

.place-actions .row {
    margin-left: -0.125rem;
    margin-right: -0.125rem;
    align-items: stretch;
}

.place-actions .col-9,
.place-actions .col-3 {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
    display: flex;
}

/* Compact Button Styling */
.place-actions .btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32px; /* Reduced from 38px */
    font-size: 0.8rem;
}

/* Primary Action Button */
.place-actions .col-9 .btn {
    padding: 0.375rem 0.5rem;
}

/* Heart Button - Compact Square */
.place-actions .col-3 .btn {
    padding: 0.375rem;
    min-width: 32px;
    width: 32px;
    height: 32px;
    border-radius: 0.375rem;
}

.place-actions .col-3 .btn i {
    margin: 0;
    font-size: 0.9rem;
}

/* Subtle Hover Effects */
.place-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.place-actions .btn:active {
    transform: translateY(0);
}

/* Place Meta Information - Compact */
.place-meta {
    margin-bottom: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.place-meta .rating-info {
    color: #F5F5DC !important;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.place-meta .rating-stars {
    font-size: 0.75rem;
}

.place-meta .text-muted {
    color: #D69A6B !important;
    font-size: 0.75rem;
}

.place-meta .price-range .badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

/* Place Features - Compact */
.place-features {
    margin-bottom: 0 !important;
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.place-features .badge {
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    margin: 0;
    white-space: nowrap;
}

/* Responsive Design - Ultra Compact */
@media (max-width: 768px) {
    .featured-place-card .card-body {
        padding: 0.75rem;
        gap: 0.375rem;
    }

    .featured-place-card .card-img-top {
        height: 140px;
    }

    .place-actions .col-9 .btn {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
        min-height: 28px;
    }

    .place-actions .col-3 .btn {
        width: 28px;
        height: 28px;
        min-width: 28px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .featured-place-card {
        margin-bottom: 1rem;
    }

    .featured-place-card .card-body {
        padding: 0.625rem;
        gap: 0.25rem;
    }

    .featured-place-card .card-img-top {
        height: 120px;
    }

    .featured-place-card .card-title {
        font-size: 0.9rem;
    }

    .featured-place-card .card-text {
        font-size: 0.8rem;
    }

    .place-features .badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.3rem;
    }
}

/* Feature Cards */
.feature-card {
    background: #6F4C3E !important;
    border: 1px solid #D69A6B !important;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(59, 42, 42, 0.5) !important;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.8) !important;
    border-color: #FFC107 !important;
}

.feature-icon {
    margin-bottom: 1.5rem;
}

.feature-card h4 {
    color: #F5F5DC !important;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: #D69A6B !important;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Sidebar Cards */
.sidebar-card {
    background: #6F4C3E !important;
    border: 1px solid #D69A6B !important;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(59, 42, 42, 0.5) !important;
}

.card-title {
    color: #F5F5DC !important;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #D69A6B !important;
}

/* Activity List */
.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #D69A6B !important;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    width: 40px;
    text-align: center;
}

.activity-text {
    flex: 1;
    color: #D69A6B !important;
}

.activity-text strong {
    color: #F5F5DC !important;
}

/* Popular List */
.popular-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #D69A6B !important;
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-rank {
    background: #FFC107 !important;
    color: #3B2A2A !important;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 1rem;
}

.popular-info {
    flex: 1;
}

.popular-name {
    font-weight: 600;
    color: #F5F5DC !important;
    margin-bottom: 0.25rem;
}

.popular-brewery {
    font-size: 0.9rem;
    color: #D69A6B !important;
    margin-bottom: 0.25rem;
}

.popular-rating {
    font-size: 0.8rem;
    color: #D69A6B !important;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
}

/* Button Enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-banner {
        text-align: center;
    }

    .hero-banner .col-lg-4 {
        margin-top: 2rem;
    }

    .feature-card {
        margin-bottom: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }


}

@media (max-width: 576px) {
    .hero-banner {
        padding: 2rem 0;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .sidebar-card {
        margin-bottom: 1.5rem;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem !important;
    }

    .btn-lg {
        width: 100%;
    }


}

/* Smooth Animations */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Focus States */
.btn:focus,
.btn:focus-visible {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Brewery-Themed Text Overrides */
.text-muted {
    color: #D69A6B !important;
}

.text-dark {
    color: #F5F5DC !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #F5F5DC !important;
}

p {
    color: #D69A6B !important;
}

.lead {
    color: #D69A6B !important;
}

/* Main Content Section */
.main-content {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

/* CTA Section */
.cta-section h2 {
    color: #3B2A2A !important;
}

.cta-section p {
    color: #3B2A2A !important;
}

/* Badge Overrides */
.badge.bg-light {
    background-color: #D69A6B !important;
    color: #3B2A2A !important;
}

.badge.bg-dark {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

.badge.bg-primary {
    background-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.badge.bg-success {
    background-color: #6F4C3E !important;
    color: #F5F5DC !important;
}

/* Button Text Contrast */
.btn-outline-primary {
    color: #F5F5DC !important;
    border-color: #FFC107 !important;
}

.btn-outline-primary:hover {
    background-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.btn-primary {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.btn-primary:hover {
    background-color: #D69A6B !important;
    border-color: #D69A6B !important;
    color: #3B2A2A !important;
}

/* Force All Text to Use Warm Colors */
* {
    color: #F5F5DC !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #F5F5DC !important;
}

p, span, div, li, td, th, a {
    color: #F5F5DC !important;
}

.text-dark, .text-black {
    color: #F5F5DC !important;
}

.text-muted {
    color: #D69A6B !important;
}

small, .small {
    color: #D69A6B !important;
}

/* Card Text Overrides */
.card-title, .card-text, .card-body * {
    color: #F5F5DC !important;
}

/* Navigation Text */
.nav-link, .navbar-nav .nav-link {
    color: #F5F5DC !important;
}

/* Form Text */
.form-label, .form-text {
    color: #F5F5DC !important;
}

/* Button Text */
.btn {
    color: #3B2A2A !important;
}

.btn-outline-primary, .btn-outline-secondary {
    color: #F5F5DC !important;
}

/* Badge Text */
.badge {
    color: #3B2A2A !important;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .hero-banner,
    .cta-section {
        background: none !important;
        color: #000 !important;
    }
    
    .btn {
        border: 1px solid #000 !important;
        background: none !important;
        color: #000 !important;
    }
}
