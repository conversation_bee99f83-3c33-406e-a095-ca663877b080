/* Video Hero Banner - Brewery-Themed Design */

/* Video Hero Section */
.video-hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

/* Video Background */
.video-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-video {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
    z-index: 1;
}

/* Video Overlay - Brewery Colors */
.video-overlay {
    background: linear-gradient(
        135deg,
        rgba(59, 42, 42, 0.7) 0%,     /* Very Dark Brown */
        rgba(111, 76, 62, 0.6) 25%,   /* <PERSON> Brown */
        rgba(214, 154, 107, 0.4) 50%, /* Medium Brown */
        rgba(59, 42, 42, 0.8) 100%    /* Very Dark Brown */
    );
    z-index: 2;
}

/* Hero Content */
.hero-content {
    z-index: 3;
    padding: 4rem 0;
}

.hero-title {
    color: #F5F5DC !important; /* Beige */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    color: #D69A6B !important; /* Medium Brown */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
    font-size: 1.25rem;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Search Container - Brewery Theme */
.search-container {
    background: rgba(59, 42, 42, 0.95) !important; /* Very Dark Brown with transparency */
    border: 2px solid #D69A6B; /* Medium Brown border */
    backdrop-filter: blur(10px);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.search-container .form-control {
    background-color: #6F4C3E !important; /* Dark Brown */
    border-color: #D69A6B !important; /* Medium Brown */
    color: #F5F5DC !important; /* Beige */
}

.search-container .form-control::placeholder {
    color: #D69A6B !important; /* Medium Brown */
    opacity: 0.8;
}

.search-container .form-control:focus {
    background-color: #6F4C3E !important;
    border-color: #FFC107 !important; /* Amber/Gold */
    color: #F5F5DC !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
}

.video-hero-section .search-container .input-group-text {
    background-color: #D69A6B !important; /* Medium Brown */
    border-color: #D69A6B !important;
    color: #F5F5DC !important; /* Beige - more visible */
}

.video-hero-section .search-container .input-group-text i {
    color: #F5F5DC !important; /* Ensure icon is beige */
}

/* Override any conflicting styles from other CSS files */
.video-hero-section .input-group-text {
    background-color: #D69A6B !important;
    border-color: #D69A6B !important;
    color: #F5F5DC !important;
}

/* Remove the floating gray search icon from style.css */
.video-hero-section .search-container::before {
    display: none !important;
}

/* Ensure form control doesn't have extra left padding from the removed icon */
.video-hero-section .search-container .form-control {
    padding-left: 0.75rem !important;
}

/* Search Button */
.btn-search {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important; /* Amber to Medium Brown */
    border: none !important;
    color: #3B2A2A !important; /* Very Dark Brown text */
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-search:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #FFC107 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* Hero Buttons */
.btn-hero-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    border: none !important;
    color: #3B2A2A !important;
    font-weight: 600;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.btn-hero-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #FFC107 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.btn-hero-outline {
    background: transparent !important;
    border: 2px solid #F5F5DC !important; /* Beige border */
    color: #F5F5DC !important;
    font-weight: 600;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease-out 1.2s both;
}

.btn-hero-outline:hover {
    background: #F5F5DC !important;
    color: #3B2A2A !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 245, 220, 0.3);
}

/* Video Navigation Dots */
.video-nav {
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 4;
    display: flex;
    gap: 15px;
}

.video-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid #F5F5DC; /* Beige border */
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.video-dot:hover {
    background: rgba(245, 245, 220, 0.5); /* Semi-transparent beige */
    transform: scale(1.2);
}

.video-dot.active {
    background: #FFC107; /* Amber/Gold */
    border-color: #FFC107;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Video Transition Effect */
.hero-video.fade-transition {
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.hero-video.fade-in {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-hero-section {
        min-height: 80vh;
    }
    
    .hero-content {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .search-container {
        padding: 1.5rem !important;
    }
    
    .search-container .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .video-nav {
        bottom: 20px;
    }
    
    .video-dot {
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .search-container {
        margin: 0 1rem;
        padding: 1rem !important;
    }
    
    .video-nav {
        gap: 10px;
    }
}

/* Performance Optimizations */
.hero-video {
    will-change: transform;
}

.video-overlay {
    will-change: opacity;
}

/* Accessibility */
.video-dot:focus {
    outline: 2px solid #FFC107;
    outline-offset: 2px;
}

/* Preload hint for better performance */
.video-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #3B2A2A; /* Fallback color */
    z-index: 0;
}

/* Loading State */
.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #F5F5DC;
    font-size: 1.2rem;
    z-index: 5;
}

.video-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #F5F5DC;
    border-radius: 50%;
    border-top-color: #FFC107;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Video Quality Optimization */
@media (max-width: 768px) {
    .hero-video {
        filter: brightness(0.9) contrast(1.1);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .hero-video,
    .video-dot,
    .hero-title,
    .hero-subtitle,
    .search-container,
    .btn-hero-primary,
    .btn-hero-outline {
        animation: none !important;
        transition: none !important;
    }

    .video-dot:hover {
        transform: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .video-overlay {
        background: rgba(0, 0, 0, 0.8);
    }

    .hero-title {
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 1);
    }

    .hero-subtitle {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
    }
}
