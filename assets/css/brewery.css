/* Brewery Profile Styles */

/* Image Upload Areas */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #FFC107;
    background-color: rgba(255, 193, 7, 0.05);
}

.upload-area.dragover {
    border-color: #FFC107;
    background-color: rgba(255, 193, 7, 0.1);
}

.upload-preview {
    position: relative;
    display: inline-block;
}

.upload-preview .remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    z-index: 10;
}

.upload-preview .remove-image:hover {
    background: #c82333;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.upload-preview .remove-image:active {
    transform: scale(0.95);
}

.image-upload-btn {
    transition: all 0.3s ease;
}

.image-upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Image Preview Styling */
.upload-preview img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.upload-preview img:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Empty State Styling */
.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.bg-light:hover {
    border-color: #FFC107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
}

/* Current Image Label */
.text-muted {
    font-style: italic;
    font-size: 0.85rem;
}

/* Profile Stats */
.profile-stats {
    background: linear-gradient(135deg, #6F4C3E 0%, #D69A6B 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    color: white;
    margin-bottom: 2rem;
}

.profile-stats .stat-item {
    text-align: center;
    padding: 1rem;
}

.profile-stats .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Form Styling */
.brewery-form {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.brewery-form .form-label {
    font-weight: 600;
    color: #3B2A2A;
    margin-bottom: 0.5rem;
}

.brewery-form .form-control {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.brewery-form .form-control:focus {
    border-color: #FFC107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Badge Styling */
.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 1rem;
    font-weight: 600;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    text-decoration: none;
    color: #6c757d;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #FFC107;
    color: #3B2A2A;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .brewery-form {
        padding: 1rem;
        margin: 1rem;
    }
    
    .profile-stats {
        margin: 1rem;
        padding: 1rem;
    }
    
    .profile-stats .stat-item {
        padding: 0.5rem;
    }
    
    .profile-stats .stat-number {
        font-size: 1.5rem;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    border-radius: 0.75rem;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Food Menu Styling */
.food-item-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.food-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #FFC107;
}

.food-item-card .card-title {
    color: #3B2A2A;
    font-weight: 600;
}

.food-item-card .card-text {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Menu Category Headers */
.text-uppercase.text-muted {
    font-weight: 700;
    letter-spacing: 1px;
    color: #6F4C3E !important;
    border-bottom: 2px solid #FFC107;
    padding-bottom: 0.5rem;
}

/* Menu Stats Cards */
.menu-stats-card {
    background: linear-gradient(135deg, #6F4C3E 0%, #D69A6B 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.menu-stats-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.menu-stats-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Category Management */
.table-hover tbody tr:hover {
    background-color: rgba(255, 193, 7, 0.1);
}

.category-type-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.category-actions .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.category-tips {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.75rem;
    padding: 1rem;
}

.category-tips h6 {
    color: #6F4C3E;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-tips .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: 1px solid #b6d4da;
    color: #0c5460;
}

/* Tab Content Styling */
.tab-content {
    background: white;
    border-radius: 0 0 0.75rem 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-tabs {
    border-bottom: 2px solid #FFC107;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border: none;
    color: #6F4C3E;
    background: rgba(255, 193, 7, 0.1);
}

.nav-tabs .nav-link.active {
    background: #FFC107;
    color: #3B2A2A;
    border: none;
    font-weight: 600;
}

.nav-tabs .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Import/Export Styling */
.import-export-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.import-export-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #FFC107;
}

.csv-format-example {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #FFC107;
    font-family: 'Courier New', monospace;
}

.csv-format-example code {
    color: #6F4C3E;
    font-weight: 600;
}

.export-button {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.export-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.export-button.btn-outline-success:hover {
    border-color: #28a745;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.export-button.btn-outline-primary:hover {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.template-download-btn {
    transition: all 0.2s ease;
}

.template-download-btn:hover {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    transform: translateX(2px);
}

.import-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.export-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

/* File Upload Styling */
.form-control[type="file"] {
    border: 2px dashed #dee2e6;
    padding: 1rem;
    transition: all 0.3s ease;
}

.form-control[type="file"]:hover {
    border-color: #FFC107;
    background: rgba(255, 193, 7, 0.05);
}

.form-control[type="file"]:focus {
    border-color: #FFC107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Dark Mode Styles */
[data-bs-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #e9ecef;
    --bs-border-color: #404040;
    --bs-secondary-bg: #2d2d2d;
}

/* Smooth Transitions for Theme Changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Dark Mode Toggle Button */
#darkModeToggle {
    transition: all 0.3s ease;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#darkModeToggle:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

[data-bs-theme="dark"] #darkModeToggle {
    background: #FFC107;
    border-color: #FFC107;
    color: #1a1a1a;
}

[data-bs-theme="dark"] #darkModeToggle:hover {
    background: #e0a800;
    border-color: #e0a800;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5);
}

#darkModeIcon {
    transition: transform 0.3s ease;
}

[data-bs-theme="dark"] body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #e9ecef;
}

[data-bs-theme="dark"] .card {
    background: #2d2d2d;
    border-color: #404040;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .card-header {
    background: linear-gradient(135deg, #3B2A2A 0%, #4a3530 100%);
    border-bottom-color: #404040;
    color: #F5F5DC;
}

[data-bs-theme="dark"] .table {
    --bs-table-bg: #2d2d2d;
    --bs-table-color: #e9ecef;
    --bs-table-border-color: #404040;
    --bs-table-striped-bg: #333333;
    --bs-table-hover-bg: #383838;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

[data-bs-theme="dark"] .nav-tabs {
    border-bottom-color: #FFC107;
}

[data-bs-theme="dark"] .nav-tabs .nav-link {
    color: #adb5bd;
    background: transparent;
}

[data-bs-theme="dark"] .nav-tabs .nav-link:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #F5F5DC;
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    background: #FFC107;
    color: #1a1a1a;
    border-color: #FFC107;
}

[data-bs-theme="dark"] .modal-content {
    background: #2d2d2d;
    border-color: #404040;
}

[data-bs-theme="dark"] .modal-header {
    background: linear-gradient(135deg, #3B2A2A 0%, #4a3530 100%);
    border-bottom-color: #404040;
    color: #F5F5DC;
}

[data-bs-theme="dark"] .form-control {
    background: #383838;
    border-color: #555555;
    color: #e9ecef;
}

[data-bs-theme="dark"] .form-control:focus {
    background: #404040;
    border-color: #FFC107;
    color: #e9ecef;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

[data-bs-theme="dark"] .form-select {
    background: #383838;
    border-color: #555555;
    color: #e9ecef;
}

[data-bs-theme="dark"] .form-select:focus {
    background: #404040;
    border-color: #FFC107;
    color: #e9ecef;
}

[data-bs-theme="dark"] .btn-outline-primary {
    color: #FFC107;
    border-color: #FFC107;
}

[data-bs-theme="dark"] .btn-outline-primary:hover {
    background: #FFC107;
    color: #1a1a1a;
}

[data-bs-theme="dark"] .btn-outline-secondary {
    color: #adb5bd;
    border-color: #6c757d;
}

[data-bs-theme="dark"] .btn-outline-secondary:hover {
    background: #6c757d;
    color: #fff;
}

[data-bs-theme="dark"] .btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

[data-bs-theme="dark"] .btn-outline-danger:hover {
    background: #dc3545;
    color: #fff;
}

[data-bs-theme="dark"] .btn-outline-success {
    color: #28a745;
    border-color: #28a745;
}

[data-bs-theme="dark"] .btn-outline-success:hover {
    background: #28a745;
    color: #fff;
}

[data-bs-theme="dark"] .alert-success {
    background: linear-gradient(135deg, #1e4d2b 0%, #2d5a3d 100%);
    border-color: #28a745;
    color: #d4edda;
}

[data-bs-theme="dark"] .alert-danger {
    background: linear-gradient(135deg, #4d1e1e 0%, #5a2d2d 100%);
    border-color: #dc3545;
    color: #f8d7da;
}

[data-bs-theme="dark"] .alert-info {
    background: linear-gradient(135deg, #1e3a4d 0%, #2d4a5a 100%);
    border-color: #17a2b8;
    color: #d1ecf1;
}

[data-bs-theme="dark"] .bg-light {
    background: linear-gradient(135deg, #2d2d2d 0%, #383838 100%) !important;
    border-color: #555555 !important;
}

[data-bs-theme="dark"] .bg-light:hover {
    background: linear-gradient(135deg, #404040 0%, #4a4a4a 100%) !important;
    border-color: #FFC107 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #adb5bd !important;
}

[data-bs-theme="dark"] .food-item-card {
    background: #2d2d2d;
    border-color: #404040;
}

[data-bs-theme="dark"] .food-item-card:hover {
    background: #333333;
    border-color: #FFC107;
}

[data-bs-theme="dark"] .menu-stats-card {
    background: linear-gradient(135deg, #3B2A2A 0%, #4a3530 100%);
}

[data-bs-theme="dark"] .import-export-card {
    background: #2d2d2d;
    border-color: #404040;
}

[data-bs-theme="dark"] .import-export-card:hover {
    background: #333333;
    border-color: #FFC107;
}

[data-bs-theme="dark"] .csv-format-example {
    background: linear-gradient(135deg, #2d2d2d 0%, #383838 100%);
    border-left-color: #FFC107;
}

[data-bs-theme="dark"] .csv-format-example code {
    color: #FFC107;
}

[data-bs-theme="dark"] .text-uppercase.text-muted {
    color: #D69A6B !important;
}

[data-bs-theme="dark"] .form-control[type="file"] {
    background: #383838;
    border-color: #555555;
    color: #e9ecef;
}

[data-bs-theme="dark"] .form-control[type="file"]:hover {
    background: #404040;
    border-color: #FFC107;
}

[data-bs-theme="dark"] .dropdown-menu {
    background: #2d2d2d;
    border-color: #404040;
}

[data-bs-theme="dark"] .dropdown-item {
    color: #e9ecef;
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #F5F5DC;
}

/* Dark Mode Badge Styling */
[data-bs-theme="dark"] .badge {
    color: #fff;
}

[data-bs-theme="dark"] .badge.bg-success {
    background: #28a745 !important;
}

[data-bs-theme="dark"] .badge.bg-primary {
    background: #007bff !important;
}

[data-bs-theme="dark"] .badge.bg-secondary {
    background: #6c757d !important;
}

[data-bs-theme="dark"] .badge.bg-warning {
    background: #ffc107 !important;
    color: #000 !important;
}

[data-bs-theme="dark"] .badge.bg-danger {
    background: #dc3545 !important;
}

[data-bs-theme="dark"] .badge.bg-info {
    background: #17a2b8 !important;
}

/* Dark Mode Button Styling */
[data-bs-theme="dark"] .btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

[data-bs-theme="dark"] .btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

[data-bs-theme="dark"] .btn-success {
    background: #28a745;
    border-color: #28a745;
    color: #fff;
}

[data-bs-theme="dark"] .btn-success:hover {
    background: #1e7e34;
    border-color: #1e7e34;
}

[data-bs-theme="dark"] .btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

[data-bs-theme="dark"] .btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

/* Dark Mode Text Colors */
[data-bs-theme="dark"] .text-primary {
    color: #FFC107 !important;
}

[data-bs-theme="dark"] .text-success {
    color: #28a745 !important;
}

[data-bs-theme="dark"] .text-danger {
    color: #dc3545 !important;
}

[data-bs-theme="dark"] .text-warning {
    color: #ffc107 !important;
}

[data-bs-theme="dark"] .text-info {
    color: #17a2b8 !important;
}

/* Dark Mode Header Styling */
[data-bs-theme="dark"] h1, [data-bs-theme="dark"] h2, [data-bs-theme="dark"] h3,
[data-bs-theme="dark"] h4, [data-bs-theme="dark"] h5, [data-bs-theme="dark"] h6 {
    color: #F5F5DC;
}

/* Dark Mode Tab Content */
[data-bs-theme="dark"] .tab-content {
    background: #2d2d2d;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Dark Mode Empty States */
[data-bs-theme="dark"] .py-5 {
    color: #adb5bd;
}

[data-bs-theme="dark"] .py-5 h5 {
    color: #e9ecef;
}

/* Dark Mode Code Blocks */
[data-bs-theme="dark"] code {
    background: #383838;
    color: #FFC107;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

/* Dark Mode Borders */
[data-bs-theme="dark"] .border {
    border-color: #404040 !important;
}

[data-bs-theme="dark"] .border-top {
    border-top-color: #404040 !important;
}

[data-bs-theme="dark"] .border-bottom {
    border-bottom-color: #404040 !important;
}

[data-bs-theme="dark"] .border-start {
    border-left-color: #404040 !important;
}

[data-bs-theme="dark"] .border-end {
    border-right-color: #404040 !important;
}
