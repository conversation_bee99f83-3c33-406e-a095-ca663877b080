/* Social Media Components - Dark Mode */

/* Social Action Buttons */
.social-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem 0;
    border-top: 1px solid var(--border-secondary);
    margin-top: 1rem;
}

.social-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 20px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    transition: all 0.3s ease;
    font-size: 0.875rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.social-btn:hover::before {
    left: 100%;
}

.social-btn.liked {
    background: linear-gradient(135deg, var(--social-like) 0%, #ff5252 100%);
    color: white;
    transform: scale(1.05);
}

.social-btn.followed {
    background: linear-gradient(135deg, var(--social-follow) 0%, #26a69a 100%);
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.social-btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.social-btn:hover i {
    transform: scale(1.2);
}

/* Check-in Card */
.checkin-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, var(--beer-dark) 100%);
    border: 1px solid var(--beer-copper);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.checkin-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 179, 71, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.checkin-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--beer-gold);
    object-fit: cover;
}

.checkin-info h6 {
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.checkin-info .text-muted {
    color: var(--text-muted) !important;
    font-size: 0.8rem;
}

.beer-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.star-rating {
    display: flex;
    gap: 2px;
}

.star {
    color: var(--text-muted);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.star.filled {
    color: var(--beer-gold);
    text-shadow: 0 0 5px rgba(255, 179, 71, 0.5);
}

/* Activity Feed */
.activity-feed {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-secondary);
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: var(--bg-tertiary);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.checkin {
    background: linear-gradient(135deg, var(--beer-gold) 0%, var(--beer-amber) 100%);
    color: var(--bg-primary);
}

.activity-icon.follow {
    background: linear-gradient(135deg, var(--social-follow) 0%, #26a69a 100%);
    color: white;
}

.activity-icon.review {
    background: linear-gradient(135deg, var(--social-comment) 0%, #81c784 100%);
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-content h6 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.activity-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.8rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    white-space: nowrap;
}

/* User Profile Card */
.user-profile-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(78, 205, 196, 0.1) 100%);
    border: 1px solid var(--social-follow);
    border-radius: 15px;
    text-align: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.user-profile-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(78, 205, 196, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.profile-avatar-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 3px solid var(--social-follow);
    margin: 0 auto 1rem;
    object-fit: cover;
    position: relative;
    z-index: 1;
}

.user-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-secondary);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--beer-gold);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Brewery Card */
.brewery-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, var(--beer-dark) 100%);
    border: 1px solid var(--beer-copper);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.brewery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.brewery-header {
    height: 200px;
    background: linear-gradient(135deg, var(--beer-copper) 0%, var(--beer-amber) 100%);
    position: relative;
    overflow: hidden;
}

.brewery-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hops" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,245,220,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23hops)"/></svg>');
    opacity: 0.3;
}

.brewery-logo {
    position: absolute;
    bottom: -30px;
    left: 1rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid var(--bg-secondary);
    background: var(--bg-secondary);
    object-fit: cover;
}

.brewery-info {
    padding: 2rem 1rem 1rem;
    margin-top: 1rem;
}

.brewery-name {
    color: var(--text-primary);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.brewery-type {
    background: var(--beer-gold);
    color: var(--bg-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .social-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .social-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .checkin-card {
        padding: 1rem;
    }
    
    .user-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .brewery-header {
        height: 150px;
    }
}
