/**
 * Coupon Management System CSS
 * Brewery-themed styling for coupon cards and management
 */

/* Coupon Grid Layout */
.coupon-grid {
    margin: 0 -0.75rem;
}

.coupon-grid .col-md-4,
.coupon-grid .col-sm-6 {
    padding: 0.75rem;
}

/* Coupon Card Styling */
.coupon-card {
    background: white;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(111, 76, 62, 0.1);
    position: relative;
}

.coupon-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(111, 76, 62, 0.2);
    border-color: var(--brewery-medium-brown, #D69A6B);
}

/* Coupon Header */
.coupon-header {
    background: linear-gradient(135deg, #FFC107 0%, #FFB300 100%);
    padding: 12px 16px;
    position: relative;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coupon-category {
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.coupon-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.coupon-card:hover .coupon-actions {
    opacity: 1;
}

.coupon-actions .btn {
    margin-left: 4px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white !important;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.coupon-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Coupon Content */
.coupon-content {
    padding: 20px;
    background: white;
}

.coupon-discount {
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    text-align: center;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 1px;
}

.coupon-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brewery-very-dark, #2c1810) !important;
    margin-bottom: 8px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.coupon-description {
    font-size: 0.9rem;
    color: var(--brewery-text-light, #8B7355) !important;
    margin-bottom: 12px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.coupon-code {
    background: var(--brewery-beige, #F5F5DC);
    border: 2px dashed var(--brewery-medium-brown, #D69A6B);
    border-radius: 8px;
    padding: 8px 12px;
    text-align: center;
    margin-bottom: 12px;
    font-family: 'Courier New', monospace;
}

.coupon-code strong {
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    font-size: 0.95rem;
    letter-spacing: 1px;
}

.coupon-place {
    font-size: 0.85rem;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 8px;
    font-weight: 600;
}

.coupon-place i {
    color: var(--brewery-medium-brown, #D69A6B);
}

.coupon-validity {
    font-size: 0.8rem;
    color: var(--brewery-text-light, #8B7355) !important;
    margin-bottom: 12px;
}

.coupon-validity i {
    color: var(--brewery-medium-brown, #D69A6B);
}

/* Coupon Stats */
.coupon-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid var(--brewery-light-brown, #E5D5C8);
    border-bottom: 1px solid var(--brewery-light-brown, #E5D5C8);
    margin-bottom: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.75rem;
    color: var(--brewery-text-light, #8B7355) !important;
}

.stat-item i {
    font-size: 1rem;
    color: var(--brewery-medium-brown, #D69A6B);
    margin-bottom: 4px;
}

.stat-item span {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
}

/* Coupon Status */
.coupon-status {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.coupon-status .badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 4px 8px;
}

/* Coupon Creation Form */
.coupon-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(111, 76, 62, 0.1);
}

.coupon-form .form-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 6px;
}

.coupon-form .form-control,
.coupon-form .form-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 8px;
    padding: 10px 12px;
    transition: border-color 0.3s ease;
}

.coupon-form .form-control:focus,
.coupon-form .form-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

/* Discount Type Preview */
.discount-preview {
    background: var(--brewery-beige, #F5F5DC);
    border: 2px solid var(--brewery-medium-brown, #D69A6B);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    margin: 16px 0;
}

.discount-preview .preview-value {
    font-size: 2rem;
    font-weight: 900;
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    margin-bottom: 8px;
}

.discount-preview .preview-description {
    font-size: 0.9rem;
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Category Management */
.category-card {
    background: white;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(111, 76, 62, 0.1);
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 76, 62, 0.15);
    border-color: var(--brewery-medium-brown, #D69A6B);
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.category-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 1.2rem;
}

.category-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brewery-very-dark, #2c1810) !important;
    margin: 0;
}

.category-description {
    font-size: 0.85rem;
    color: var(--brewery-text-light, #8B7355) !important;
    margin-bottom: 12px;
    line-height: 1.4;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .coupon-grid .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .coupon-content {
        padding: 16px;
    }
    
    .coupon-discount {
        font-size: 1.5rem;
    }
    
    .coupon-title {
        font-size: 1rem;
    }
    
    .coupon-stats {
        padding: 8px 0;
    }
    
    .stat-item {
        font-size: 0.7rem;
    }
    
    .stat-item i {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .coupon-grid .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .coupon-header {
        padding: 10px 12px;
    }
    
    .coupon-category {
        font-size: 0.75rem;
    }
    
    .coupon-actions .btn {
        width: 28px;
        height: 28px;
    }
}

/* Loading and Error States */
.coupon-loading {
    background: var(--brewery-beige, #F5F5DC);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    border-radius: 16px;
}

.coupon-loading .spinner-border {
    color: var(--brewery-medium-brown, #D69A6B);
}

.coupon-error {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #6c757d;
    border-radius: 16px;
}

.coupon-error .fas {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Ensure high contrast for all text */
.coupon-card,
.category-card,
.coupon-form {
    color: var(--brewery-very-dark, #2c1810) !important;
}

.coupon-card .text-muted,
.category-card .text-muted,
.coupon-form .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Special coupon effects */
.coupon-card.featured {
    border-color: var(--brewery-amber, #FFC107);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.coupon-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--brewery-amber, #FFC107), var(--brewery-medium-brown, #D69A6B));
    z-index: 1;
}

.coupon-card.expired {
    opacity: 0.6;
    filter: grayscale(0.3);
}

.coupon-card.pending-approval {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff 0%, #fff9e6 100%);
}
