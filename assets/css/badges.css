/* Badges Page Styles */

.badge-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.summary-stat {
    padding: 1rem;
}

.summary-stat .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.summary-stat .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Badge Cards */
.badge-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.badge-card.earned {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.badge-card.locked {
    border-color: #dee2e6;
    background: #f8f9fa;
    opacity: 0.8;
}

.badge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.badge-card.earned:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

/* Badge Header */
.badge-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.badge-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 3px solid #e9ecef;
}

.badge-card.earned .badge-icon {
    border-color: #28a745;
    background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
}

.badge-emoji {
    font-size: 1.75rem;
}

.badge-rarity {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Badge Content */
.badge-content {
    text-align: center;
}

.badge-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.75rem;
}

.badge-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.badge-earned {
    color: #28a745;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.badge-progress {
    margin-bottom: 1rem;
}

.badge-progress .progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.badge-progress .progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 4px;
}

.badge-points {
    margin-top: auto;
}

/* Rarity Badges */
.badge.bg-common {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
    color: white;
}

.badge.bg-uncommon {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    color: white;
}

.badge.bg-rare {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white;
}

.badge.bg-epic {
    background: linear-gradient(135deg, #6610f2 0%, #520dc2 100%) !important;
    color: white;
}

.badge.bg-legendary {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%) !important;
    color: white;
    animation: legendaryGlow 2s ease-in-out infinite alternate;
}

@keyframes legendaryGlow {
    from {
        box-shadow: 0 0 5px rgba(253, 126, 20, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(253, 126, 20, 0.8);
    }
}

/* Category Headers */
.card-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.card-header.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.card-header.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Earned Badge Effects */
.badge-card.earned::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #28a745, #20c997, #17a2b8, #007bff);
    border-radius: 12px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% {
        opacity: 0.5;
    }
    50% {
        opacity: 1;
    }
}

/* Locked Badge Effects */
.badge-card.locked .badge-icon {
    filter: grayscale(100%);
}

.badge-card.locked .badge-emoji {
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .badge-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .badge-icon {
        width: 50px;
        height: 50px;
    }
    
    .badge-emoji {
        font-size: 1.5rem;
    }
    
    .badge-name {
        font-size: 1rem;
    }
    
    .badge-description {
        font-size: 0.85rem;
    }
    
    .summary-stat .stat-number {
        font-size: 2rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.badge-card {
    animation: fadeInUp 0.6s ease-out;
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.badge-card:nth-child(3n+1) { animation-delay: 0.1s; }
.badge-card:nth-child(3n+2) { animation-delay: 0.2s; }
.badge-card:nth-child(3n+3) { animation-delay: 0.3s; }

/* Hover effects for earned badges */
.badge-card.earned:hover .badge-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.badge-card.earned:hover .badge-emoji {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Progress bar animations */
.badge-progress .progress-bar {
    transition: width 1s ease-in-out;
}

/* Tooltip styles */
.badge-card[data-bs-toggle="tooltip"] {
    cursor: help;
}

/* Empty state */
.empty-badges {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-badges i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-badges h4 {
    color: #495057;
    margin-bottom: 1rem;
}

/* Quick actions */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn i {
    font-size: 1.5rem;
}

/* Special effects for legendary badges */
.badge-card.earned.legendary {
    position: relative;
    overflow: hidden;
}

.badge-card.earned.legendary::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

/* Badge category icons */
.card-header i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Loading states */
.badge-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.badge-card.loading .badge-icon::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
