/**
 * PWA-Specific CSS
 * Phase 9: Design & Mobile Optimization
 * Styles for Progressive Web App features
 */

/* PWA Display Mode Detection */
@media (display-mode: standalone) {
    /* Styles when app is installed and running in standalone mode */
    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
    }
    
    .navbar {
        padding-top: calc(0.5rem + env(safe-area-inset-top));
    }
    
    .mobile-bottom-nav {
        padding-bottom: env(safe-area-inset-bottom);
    }
    
    /* Hide browser-specific elements in standalone mode */
    .browser-only {
        display: none !important;
    }
    
    /* Show PWA-specific elements */
    .pwa-only {
        display: block !important;
    }
}

/* PWA Install Prompt */
.pwa-install-prompt {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    z-index: 1050;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.pwa-install-prompt.show {
    transform: translateY(0);
    opacity: 1;
}

.pwa-install-prompt .install-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pwa-install-prompt .install-text {
    flex: 1;
    margin-right: 1rem;
}

.pwa-install-prompt .install-title {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.pwa-install-prompt .install-description {
    font-size: 0.875rem;
    opacity: 0.9;
}

.pwa-install-prompt .install-actions {
    display: flex;
    gap: 0.5rem;
}

.pwa-install-prompt .btn {
    padding: 0.5rem 1rem;
    border: 2px solid white;
    background: transparent;
    color: white;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.pwa-install-prompt .btn:hover {
    background: white;
    color: #f8b500;
}

.pwa-install-prompt .btn-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    opacity: 0.8;
    padding: 0.25rem;
}

/* PWA Update Available Banner */
.pwa-update-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #17a2b8;
    color: white;
    padding: 0.75rem 1rem;
    text-align: center;
    z-index: 1060;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.pwa-update-banner.show {
    transform: translateY(0);
}

.pwa-update-banner .update-text {
    margin-right: 1rem;
}

.pwa-update-banner .btn {
    background: white;
    color: #17a2b8;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

/* PWA Offline Indicator */
.pwa-offline-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #6c757d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    z-index: 1055;
    opacity: 0;
    transition: all 0.3s ease;
}

.pwa-offline-indicator.show {
    opacity: 1;
}

.pwa-offline-indicator.online {
    background: #28a745;
}

.pwa-offline-indicator .status-icon {
    margin-right: 0.5rem;
}

/* PWA Loading States */
.pwa-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.pwa-loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.pwa-loading-content {
    text-align: center;
    padding: 2rem;
}

.pwa-loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid #f8f9fa;
    border-top: 4px solid #f8b500;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pwa-loading-text {
    color: #6c757d;
    font-weight: 600;
}

/* PWA Sync Status */
.pwa-sync-status {
    position: fixed;
    bottom: 100px;
    right: 20px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 0.75rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 1040;
    transform: translateX(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.pwa-sync-status.show {
    transform: translateX(0);
    opacity: 1;
}

.pwa-sync-status .sync-icon {
    color: #f8b500;
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

.pwa-sync-status.success .sync-icon {
    color: #28a745;
    animation: none;
}

.pwa-sync-status.error .sync-icon {
    color: #dc3545;
    animation: none;
}

.pwa-sync-text {
    font-size: 0.875rem;
    font-weight: 600;
}

/* PWA Navigation Enhancements */
.pwa-nav-back {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1030;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.pwa-nav-back:hover {
    background: white;
    transform: scale(1.1);
}

.pwa-nav-back i {
    color: #6c757d;
    font-size: 1.25rem;
}

/* PWA Splash Screen Styles */
.pwa-splash {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.pwa-splash.hide {
    opacity: 0;
    pointer-events: none;
}

.pwa-splash-logo {
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

.pwa-splash-logo i {
    font-size: 3rem;
    color: #f8b500;
}

.pwa-splash-title {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.pwa-splash-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    text-align: center;
    max-width: 300px;
}

/* PWA Share Button */
.pwa-share-btn {
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 4px 15px rgba(248, 181, 0, 0.3);
}

.pwa-share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(248, 181, 0, 0.4);
    color: white;
}

.pwa-share-btn:active {
    transform: translateY(0);
}

/* PWA Notification Styles */
.pwa-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    z-index: 1050;
    max-width: 300px;
    transform: translateX(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.pwa-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.pwa-notification .notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.pwa-notification .notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 0.5rem;
    background: #f8b500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pwa-notification .notification-icon i {
    color: white;
    font-size: 0.875rem;
}

.pwa-notification .notification-title {
    font-weight: bold;
    font-size: 0.875rem;
    color: #212529;
}

.pwa-notification .notification-body {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

.pwa-notification .notification-actions {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.5rem;
}

.pwa-notification .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 15px;
}

/* PWA-specific responsive adjustments */
@media (max-width: 576px) {
    .pwa-install-prompt {
        left: 10px;
        right: 10px;
        bottom: 10px;
    }
    
    .pwa-install-prompt .install-content {
        flex-direction: column;
        text-align: center;
    }
    
    .pwa-install-prompt .install-text {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .pwa-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .pwa-sync-status {
        right: 10px;
        bottom: 80px;
    }
}

/* Hide PWA elements by default */
.pwa-only {
    display: none;
}

/* Show browser elements by default */
.browser-only {
    display: block;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .pwa-install-prompt,
    .pwa-update-banner,
    .pwa-offline-indicator,
    .pwa-sync-status,
    .pwa-notification,
    .pwa-loading-overlay {
        transition: none;
    }
    
    .pwa-loading-spinner {
        animation: none;
    }
    
    .pwa-splash-logo {
        animation: none;
    }
}
