/**
 * Places Management CSS
 * Brewery-themed styling for places management interface
 */

/* Places Table Styling - Compact */
.places-table {
    font-size: 0.85rem;
    margin-bottom: 0;
}

.places-table th {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    color: white !important;
    border: none;
    font-weight: 600;
    padding: 8px 6px;
    vertical-align: middle;
    line-height: 1.2;
}

.places-table td {
    padding: 6px 6px;
    vertical-align: top;
    border-bottom: 1px solid var(--brewery-light-brown, #E5D5C8);
    line-height: 1.3;
}

.places-table tbody tr:hover {
    background-color: rgba(245, 245, 220, 0.5) !important;
}

/* Place Info Column - Compact */
.place-info {
    max-width: 220px;
}

.place-name {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-size: 0.9rem;
    font-weight: 700;
    line-height: 1.2;
    display: block;
    margin-bottom: 2px;
}

.place-description {
    color: var(--brewery-text-light, #8B7355) !important;
    font-size: 0.8rem;
    line-height: 1.3;
    margin-bottom: 3px;
}

.place-meta {
    font-size: 0.7rem;
    line-height: 1.2;
}

.place-meta .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Contact Info Column - Compact */
.contact-info {
    max-width: 170px;
}

.contact-item {
    margin-bottom: 2px;
    font-size: 0.8rem;
    line-height: 1.2;
}

.contact-item i {
    color: var(--brewery-medium-brown, #D69A6B);
    width: 12px;
    text-align: center;
    font-size: 0.75rem;
}

.contact-item a {
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    text-decoration: none;
}

.contact-item a:hover {
    color: var(--brewery-medium-brown, #D69A6B) !important;
    text-decoration: underline;
}

/* Location Info Column - Compact */
.location-info {
    max-width: 150px;
    font-size: 0.8rem;
    color: var(--brewery-text-dark, #4A3728) !important;
    line-height: 1.3;
}

/* Status Badges - Compact */
.status-badges {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.status-badges .badge {
    font-size: 0.65rem;
    font-weight: 600;
    padding: 2px 6px;
    align-self: flex-start;
    line-height: 1.2;
}

/* Action Buttons - Compact */
.btn-group-vertical .btn {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 3px;
    margin-bottom: 1px;
    line-height: 1.2;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

/* Form Styling */
.modal .form-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 6px;
}

.modal .form-control,
.modal .form-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    border-radius: 6px;
    padding: 8px 12px;
    transition: border-color 0.3s ease;
}

.modal .form-control:focus,
.modal .form-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

.modal .form-check-input:checked {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    border-color: var(--brewery-dark-brown, #6F4C3E);
}

.modal .form-check-label {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 500;
}

/* Modal Header */
.modal-header {
    background: linear-gradient(135deg, var(--brewery-dark-brown, #6F4C3E) 0%, var(--brewery-medium-brown, #D69A6B) 100%);
    color: white;
    border-bottom: none;
}

.modal-header .modal-title {
    color: white !important;
    font-weight: 700;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Modal Body - Compact */
.modal-body {
    background-color: #fafafa;
    padding: 16px;
}

/* Modal Footer - Compact */
.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid var(--brewery-light-brown, #E5D5C8);
    padding: 12px 16px;
}

/* Filter Form - Compact */
.filter-form {
    background-color: rgba(245, 245, 220, 0.3);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.filter-form .form-label {
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    margin-bottom: 6px;
}

.filter-form .form-control,
.filter-form .form-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    background-color: white;
    color: var(--brewery-text-dark, #4A3728) !important;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

/* Statistics Cards - Use existing admin.css styles */
.stats-row .card {
    background-color: white !important;
    border: 2px solid var(--brewery-light-brown, #E5D5C8) !important;
}

.stats-row .card .card-title {
    color: #000000 !important;
    font-weight: 800 !important;
    font-size: 1.8rem !important;
}

.stats-row .card .card-text {
    color: #333333 !important;
    font-weight: 600 !important;
}

/* Table Header Actions - Compact */
.table-header-actions {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    color: white;
    padding: 0.75rem;
    border-radius: 6px 6px 0 0;
    border-bottom: 2px solid var(--brewery-medium-brown, #D69A6B);
}

.table-header-actions h5 {
    color: white !important;
    margin: 0;
    font-weight: 700;
    font-size: 1.1rem;
}

.table-header-actions .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .places-table {
        font-size: 0.8rem;
    }
    
    .place-info,
    .contact-info,
    .location-info {
        max-width: none;
    }
    
    .place-name {
        font-size: 0.9rem;
    }
    
    .place-description {
        font-size: 0.8rem;
    }
    
    .contact-item {
        font-size: 0.8rem;
    }
    
    .btn-group-vertical .btn {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
    
    .modal-body {
        padding: 16px;
    }
}

@media (max-width: 576px) {
    .places-table th,
    .places-table td {
        padding: 8px 4px;
    }
    
    .place-info {
        max-width: 150px;
    }
    
    .contact-info {
        max-width: 120px;
    }
    
    .location-info {
        max-width: 100px;
    }
    
    .btn-group-vertical {
        width: 100%;
    }
    
    .btn-group-vertical .btn {
        width: 100%;
        margin-bottom: 1px;
    }
}

/* Loading and Error States */
.places-loading {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--brewery-text-light, #8B7355);
}

.places-loading .spinner-border {
    color: var(--brewery-medium-brown, #D69A6B);
}

.places-error {
    text-align: center;
    padding: 3rem 1rem;
    color: #dc3545;
}

/* Ensure high contrast for all text */
.places-table,
.modal,
.filter-form {
    color: var(--brewery-very-dark, #2c1810) !important;
}

.places-table .text-muted,
.modal .text-muted,
.filter-form .text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}

/* Badge customization */
.badge.bg-secondary {
    background-color: var(--brewery-medium-brown, #D69A6B) !important;
    color: white !important;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: var(--brewery-amber, #FFC107) !important;
    color: var(--brewery-very-dark, #2c1810) !important;
}

/* Button styling consistency */
.btn-outline-primary {
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    border-color: var(--brewery-dark-brown, #6F4C3E) !important;
}

.btn-outline-primary:hover {
    background-color: var(--brewery-dark-brown, #6F4C3E) !important;
    border-color: var(--brewery-dark-brown, #6F4C3E) !important;
    color: white !important;
}

.btn-outline-info {
    color: var(--brewery-medium-brown, #D69A6B) !important;
    border-color: var(--brewery-medium-brown, #D69A6B) !important;
}

.btn-outline-info:hover {
    background-color: var(--brewery-medium-brown, #D69A6B) !important;
    border-color: var(--brewery-medium-brown, #D69A6B) !important;
    color: white !important;
}

.btn-outline-danger:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

/* Additional Compact Layout Rules - Ultra Tight */
body.places-management .dashboard-content,
.dashboard-content {
    padding: 0.5rem !important;
}

/* Specific overrides for places management - Remove all extra spacing */
body.places-management .container-fluid {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
    max-width: none !important;
}

/* Make admin main content area tighter */
body.places-management .admin-main {
    margin-left: 285px !important; /* Slightly closer to sidebar */
}

/* Remove any extra spacing from main content wrapper */
body.places-management .main-content-wrapper {
    padding: 0 !important;
    margin: 0 !important;
}

.card {
    margin-bottom: 1rem !important;
}

.card-body {
    padding: 1rem !important;
}

.row {
    margin-bottom: 0.75rem;
}

.col-md-3, .col-md-4, .col-md-6, .col-md-8, .col-md-9, .col-md-12 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Compact form controls */
.form-control, .form-select {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.85rem !important;
}

.form-label {
    margin-bottom: 0.25rem !important;
    font-size: 0.85rem !important;
}

/* Compact pagination */
.pagination {
    margin-bottom: 0 !important;
}

.page-link {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.85rem !important;
}

/* Compact table wrapper */
.table-responsive {
    margin-bottom: 0.5rem !important;
}

/* Compact stats cards */
.stats-row .card {
    margin-bottom: 0.75rem !important;
}

.stats-row .card-body {
    padding: 0.75rem !important;
}

.stats-row .card-title {
    font-size: 1.5rem !important;
    margin-bottom: 0.25rem !important;
}

.stats-row .card-text {
    font-size: 0.8rem !important;
    margin-bottom: 0 !important;
}

/* Ultra-compact table for better data density */
.places-table-compact {
    font-size: 0.8rem !important;
}

.places-table-compact th,
.places-table-compact td {
    padding: 4px 5px !important;
    line-height: 1.2 !important;
}

.places-table-compact .place-name {
    font-size: 0.85rem !important;
    margin-bottom: 1px !important;
}

.places-table-compact .place-description {
    font-size: 0.75rem !important;
    margin-bottom: 2px !important;
}

.places-table-compact .contact-item {
    font-size: 0.75rem !important;
    margin-bottom: 1px !important;
}

.places-table-compact .badge {
    font-size: 0.6rem !important;
    padding: 1px 4px !important;
}

.places-table-compact .btn {
    font-size: 0.65rem !important;
    padding: 2px 4px !important;
}

/* Remove extra spacing from Bootstrap components */
.mb-4 {
    margin-bottom: 1rem !important;
}

.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

/* Make table headers more compact */
.places-table thead th {
    font-size: 0.8rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Compact alert messages */
.alert {
    padding: 0.5rem 0.75rem !important;
    margin-bottom: 0.75rem !important;
    font-size: 0.85rem !important;
}

/* Compact card headers */
.card-header {
    padding: 0.5rem 0.75rem !important;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0 !important;
    font-size: 0.9rem !important;
}

/* Compact buttons in action groups */
.btn-group .btn, .btn-group-vertical .btn {
    border-radius: 2px !important;
}

/* Tighter modal spacing */
.modal-header {
    padding: 0.75rem 1rem !important;
}

.modal-header .modal-title {
    font-size: 1rem !important;
}

/* Ultra-compact stats cards */
.stats-card-compact {
    border: 1px solid rgba(0,0,0,0.1) !important;
    box-shadow: none !important;
}

.stats-card-compact .card-body {
    padding: 0.5rem !important;
}

.stats-card-compact .h6 {
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    margin-bottom: 0 !important;
}

.stats-card-compact small {
    font-size: 0.7rem !important;
    line-height: 1 !important;
}

/* Compact filter form */
.filter-form-compact {
    background: rgba(245, 245, 220, 0.2) !important;
    border-radius: 4px !important;
}

.filter-form-compact .form-control-sm,
.filter-form-compact .form-select-sm {
    font-size: 0.8rem !important;
    padding: 0.25rem 0.4rem !important;
    height: auto !important;
}

.filter-form-compact .btn-sm {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
}

/* Remove all extra margins and padding */
.dashboard-content .row {
    margin-bottom: 0.5rem !important;
}

.dashboard-content .card {
    margin-bottom: 0.5rem !important;
}

/* Make table completely borderless with card */
.card-body.p-0 {
    padding: 0 !important;
}

.card-body.p-0 .table-responsive {
    margin: 0 !important;
    border-radius: 0 !important;
}

.card-body.p-0 .table {
    margin-bottom: 0 !important;
    border-radius: 0 !important;
}

/* Ultra-compact table header */
.table-header-actions.py-2 {
    padding: 0.5rem 0.75rem !important;
}

.table-header-actions h6 {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
}

/* Remove spacing between elements */
.g-2 > * {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
}

.row.g-2 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
}

/* Ultra-tight layout for places management */
body.places-management .admin-container {
    padding: 0 !important;
    margin: 0 !important;
}

body.places-management .main-header {
    margin-bottom: 0.5rem !important;
    padding: 1rem !important;
}

body.places-management .main-header h1 {
    font-size: 1.25rem !important;
    margin-bottom: 0 !important;
}

/* Eliminate all container spacing */
body.places-management .container,
body.places-management .container-fluid {
    padding: 0 !important;
    margin: 0 !important;
}

/* Make sidebar and content seamless */
body.places-management .admin-sidebar {
    border-right: 1px solid rgba(255, 193, 7, 0.2) !important;
}

/* Remove any wrapper padding */
body.places-management .admin-wrapper,
body.places-management .content-wrapper {
    padding: 0 !important;
    margin: 0 !important;
}

/* Tighten up the entire layout */
body.places-management * {
    box-sizing: border-box !important;
}

/* Make content area start immediately after sidebar */
body.places-management .admin-main {
    margin-left: 280px !important; /* Exact sidebar width */
    padding: 0 !important;
}

/* Remove any extra spacing from Bootstrap grid */
body.places-management .row {
    margin: 0 !important;
}

body.places-management .col,
body.places-management [class*="col-"] {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
}

/* Final ultra-tight adjustments */
body.places-management .dashboard-content {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    padding-top: 0.25rem !important;
}

/* Make the main header more compact */
body.places-management .main-header {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    padding-top: 0.75rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid rgba(255, 193, 7, 0.1) !important;
}

/* Remove any remaining gaps */
body.places-management .admin-main {
    background: #2C1810 !important;
    border-left: 1px solid rgba(255, 193, 7, 0.1) !important;
}

/* Ensure seamless connection */
body.places-management .admin-sidebar {
    border-right: none !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
}

/* Modern Action Bar - Industry Standard */
.action-bar {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 193, 7, 0.1);
    backdrop-filter: blur(10px);
}

.action-bar-left {
    flex: 1;
}

.action-bar-right {
    flex-shrink: 0;
}

/* Modern Button System */
.modern-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    border-radius: 6px;
    border: 1px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    cursor: pointer;
    white-space: nowrap;
    user-select: none;
}

.modern-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

/* Primary Modern Button */
.btn-primary.modern-btn {
    background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%);
    border-color: #FFC107;
    color: #000;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

.btn-primary.modern-btn:hover {
    background: linear-gradient(135deg, #FFD54F 0%, #FFA000 100%);
    border-color: #FFD54F;
    color: #000;
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
    transform: translateY(-1px);
}

.btn-primary.modern-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

/* Outline Modern Button */
.btn-outline-primary.modern-btn {
    background: rgba(255, 193, 7, 0.05);
    border-color: rgba(255, 193, 7, 0.3);
    color: #FFC107;
    backdrop-filter: blur(10px);
}

.btn-outline-primary.modern-btn:hover {
    background: rgba(255, 193, 7, 0.1);
    border-color: #FFC107;
    color: #FFC107;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
    transform: translateY(-1px);
}

/* Ghost Button */
.btn-ghost.modern-btn {
    background: transparent;
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

.btn-ghost.modern-btn:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
}

/* Button Icons */
.modern-btn i {
    font-size: 0.875rem;
    opacity: 0.9;
}

.modern-btn .btn-text {
    font-weight: 500;
}

/* Button Groups */
.btn-toolbar .btn-group {
    border-radius: 6px;
    overflow: hidden;
}

.btn-group .modern-btn {
    border-radius: 0;
}

.btn-group .modern-btn:first-child {
    border-radius: 6px 0 0 6px;
}

.btn-group .modern-btn:last-child {
    border-radius: 0 6px 6px 0;
}

.btn-group .modern-btn:only-child {
    border-radius: 6px;
}

/* Modern Dropdown */
.dropdown-menu {
    background: rgba(59, 42, 42, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
}

.dropdown-item i {
    opacity: 0.7;
    width: 16px;
    text-align: center;
}

.dropdown-divider {
    border-color: rgba(255, 193, 7, 0.2);
    margin: 0.5rem 0;
}

/* Responsive Button Behavior */
@media (max-width: 768px) {
    .action-bar {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch !important;
    }

    .action-bar-left,
    .action-bar-right {
        flex: none;
    }

    .btn-toolbar {
        justify-content: center;
    }

    .modern-btn .btn-text {
        display: none;
    }

    .modern-btn {
        padding: 0.5rem;
        min-width: 40px;
        justify-content: center;
    }
}

/* Modern Table Action Buttons */
.table-actions {
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-table-primary {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #FFC107;
    border-radius: 4px 0 0 4px;
    padding: 0.25rem 0.5rem;
    transition: all 0.2s ease;
}

.btn-table-primary:hover {
    background: rgba(255, 193, 7, 0.2);
    border-color: #FFC107;
    color: #FFC107;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

.btn-table-secondary {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    border-radius: 0 4px 4px 0;
    border-left: none;
    padding: 0.25rem 0.4rem;
    transition: all 0.2s ease;
}

.btn-table-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.btn-table-secondary::after {
    display: none; /* Hide default dropdown arrow */
}

/* Table Action Dropdown */
.table-actions .dropdown-menu {
    min-width: 160px;
    font-size: 0.8rem;
}

.table-actions .dropdown-item {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
}

.table-actions .dropdown-item.text-danger {
    color: #dc3545 !important;
}

.table-actions .dropdown-item.text-danger:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545 !important;
}

/* Compact table action buttons */
.places-table-compact .table-actions .btn {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
}

.places-table-compact .table-actions .btn i {
    font-size: 0.7rem;
}

/* Modern button hover effects */
.btn-table-primary:focus,
.btn-table-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

/* Ensure buttons stay connected */
.table-actions .btn-group .btn {
    border-radius: 0;
}

.table-actions .btn-group .btn:first-child {
    border-radius: 4px 0 0 4px;
}

.table-actions .btn-group .btn:last-child {
    border-radius: 0 4px 4px 0;
}

/* Modern loading state */
.btn-table-primary.loading {
    position: relative;
    color: transparent;
}

.btn-table-primary.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid transparent;
    border-top-color: #FFC107;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
