/* Beer Rating Page Styles */

.beer-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.beer-thumb {
    max-width: 80px;
    height: auto;
    border-radius: 8px;
}

.beer-thumb-placeholder {
    width: 80px;
    height: 80px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Rating Input Styles */
.rating-input {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.rating-input:hover {
    background: #e9ecef;
}

.stars {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.stars i {
    font-size: 1.5rem;
    color: #F5F5DC !important; /* White/beige by default */
    cursor: pointer;
    transition: all 0.3s ease;
}

.stars i:hover,
.stars i.hover {
    color: #FFC107 !important; /* Gold on hover */
    transform: scale(1.1);
}

.stars i.active {
    color: #FFC107 !important; /* Always gold when selected */
}

.rating-text {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

/* Overall rating special styling */
.rating-input[data-rating="overall_rating"] {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
}

.rating-input[data-rating="overall_rating"] .stars i {
    font-size: 2rem;
}

.rating-input[data-rating="overall_rating"] .rating-text {
    font-size: 1rem;
    font-weight: 600;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
    border: none;
}

.card-body {
    padding: 2rem;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: #adb5bd;
    opacity: 1;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    transform: translateY(-1px);
}

/* Badge Styles */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.4rem 0.6rem;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

/* Checkbox Styles */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
    }
    
    .stars i {
        font-size: 1.25rem;
    }
    
    .rating-input[data-rating="overall_rating"] .stars i {
        font-size: 1.75rem;
    }
    
    .beer-info .row {
        text-align: center;
    }
    
    .beer-info .col-md-2 {
        margin-bottom: 1rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.rating-input {
    animation: fadeInUp 0.8s ease-out;
}

/* Stagger animations for rating inputs */
.rating-input:nth-child(1) { animation-delay: 0.1s; }
.rating-input:nth-child(2) { animation-delay: 0.2s; }
.rating-input:nth-child(3) { animation-delay: 0.3s; }
.rating-input:nth-child(4) { animation-delay: 0.4s; }

/* Star animation effects */
.stars i {
    animation: starPulse 0.3s ease-out;
}

@keyframes starPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stars i.active {
    animation: starGlow 0.5s ease-out;
}

@keyframes starGlow {
    0% { 
        color: #dee2e6;
        text-shadow: none;
    }
    50% { 
        color: #ffc107;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
    }
    100% { 
        color: #ffc107;
        text-shadow: none;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Form validation styles */
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Special effects for different rating categories */
.rating-input[data-rating="taste_rating"]:hover {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.rating-input[data-rating="aroma_rating"]:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.rating-input[data-rating="appearance_rating"]:hover {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.rating-input[data-rating="mouthfeel_rating"]:hover {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
}

/* Tooltip styles for rating guidance */
.rating-input::before {
    content: attr(data-tooltip);
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.rating-input:hover::before {
    opacity: 1;
}
