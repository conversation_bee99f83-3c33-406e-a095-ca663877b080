/**
 * Places Import CSS
 * Brewery-themed styling for CSV import interface
 */

/* Import Steps */
.import-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    color: white;
}

.step-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    transition: color 0.3s ease;
}

.step.active .step-title {
    color: var(--brewery-dark-brown, #6F4C3E);
}

.step-connector {
    width: 100px;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 20px;
    margin-top: -20px;
}

/* Upload Area */
.upload-area {
    border: 3px dashed var(--brewery-light-brown, #E5D5C8);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(245, 245, 220, 0.3) 0%, rgba(229, 213, 200, 0.3) 100%);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--brewery-medium-brown, #D69A6B);
    background: linear-gradient(135deg, rgba(245, 245, 220, 0.5) 0%, rgba(229, 213, 200, 0.5) 100%);
}

.upload-area.dragover {
    border-color: var(--brewery-dark-brown, #6F4C3E);
    background: linear-gradient(135deg, rgba(111, 76, 62, 0.1) 0%, rgba(214, 154, 107, 0.1) 100%);
}

.upload-content {
    color: var(--brewery-text-dark, #4A3728);
}

.upload-content i {
    color: var(--brewery-medium-brown, #D69A6B);
}

.upload-content h6 {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-weight: 700;
    margin-bottom: 8px;
}

.upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Upload Info */
.upload-info {
    background: rgba(245, 245, 220, 0.5);
    border-radius: 8px;
    padding: 1.5rem;
}

.upload-info h6 {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-weight: 700;
    margin-bottom: 12px;
}

.upload-info .list-unstyled li {
    margin-bottom: 6px;
    color: var(--brewery-text-dark, #4A3728) !important;
}

.upload-info .fas.fa-check {
    color: #28a745 !important;
}

/* Analysis Summary */
.analysis-summary {
    background: linear-gradient(135deg, var(--brewery-beige, #F5F5DC) 0%, rgba(229, 213, 200, 0.5) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
}

.stat-card {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--brewery-light-brown, #E5D5C8);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 900;
    color: var(--brewery-dark-brown, #6F4C3E) !important;
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--brewery-text-dark, #4A3728) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Field Mapping Table */
.field-mapping-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
}

.field-mapping-table .table {
    margin-bottom: 0;
}

.field-mapping-table th {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    color: white !important;
    border: none;
    font-weight: 600;
    padding: 12px;
}

.field-mapping-table td {
    padding: 12px;
    vertical-align: middle;
    border-color: var(--brewery-light-brown, #E5D5C8);
}

.field-mapping-table tbody tr:hover {
    background-color: rgba(245, 245, 220, 0.3) !important;
}

/* Sample Data */
.sample-data {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sample-value {
    background: var(--brewery-beige, #F5F5DC);
    color: var(--brewery-text-dark, #4A3728) !important;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-family: monospace;
    border: 1px solid var(--brewery-light-brown, #E5D5C8);
}

/* Quality Info */
.quality-info {
    min-width: 120px;
}

.fill-rate {
    margin-bottom: 4px;
}

.fill-rate small {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 600;
    font-size: 0.75rem;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background-color: var(--brewery-medium-brown, #D69A6B);
}

/* Field Mapping Select */
.field-mapping-select {
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 500;
}

.field-mapping-select:focus {
    border-color: var(--brewery-medium-brown, #D69A6B);
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25);
}

.field-mapping-select option[value="ignore"] {
    color: #6c757d;
    font-style: italic;
}

.field-mapping-select option[value="name"] {
    font-weight: 700;
    color: var(--brewery-dark-brown, #6F4C3E);
}

/* Import Options */
.import-options {
    background: rgba(245, 245, 220, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--brewery-light-brown, #E5D5C8);
}

.import-options h6 {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-weight: 700;
    margin-bottom: 1rem;
}

.import-options .form-check {
    margin-bottom: 8px;
}

.import-options .form-check-input:checked {
    background-color: var(--brewery-dark-brown, #6F4C3E);
    border-color: var(--brewery-dark-brown, #6F4C3E);
}

.import-options .form-check-label {
    color: var(--brewery-text-dark, #4A3728) !important;
    font-weight: 500;
}

/* Sample Preview */
.sample-preview {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 2px solid var(--brewery-light-brown, #E5D5C8);
}

.sample-preview h6 {
    color: var(--brewery-very-dark, #2c1810) !important;
    font-weight: 700;
    margin-bottom: 1rem;
}

.sample-preview .table {
    font-size: 0.85rem;
}

.sample-preview .table th {
    background-color: var(--brewery-beige, #F5F5DC);
    color: var(--brewery-text-dark, #4A3728) !important;
    border-color: var(--brewery-light-brown, #E5D5C8);
    font-weight: 600;
}

.sample-preview .table td {
    border-color: var(--brewery-light-brown, #E5D5C8);
    color: var(--brewery-text-dark, #4A3728) !important;
}

/* Buttons */
.btn-lg {
    padding: 12px 24px;
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .import-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .step-connector {
        width: 2px;
        height: 30px;
        margin: 10px 0;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .analysis-summary .row {
        gap: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .field-mapping-table {
        font-size: 0.85rem;
    }
    
    .sample-data {
        max-width: 150px;
    }
    
    .quality-info {
        min-width: 100px;
    }
}

@media (max-width: 576px) {
    .upload-content h6 {
        font-size: 1rem;
    }
    
    .upload-content .fa-3x {
        font-size: 2rem !important;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .field-mapping-table th,
    .field-mapping-table td {
        padding: 8px;
    }
    
    .sample-value {
        font-size: 0.7rem;
    }
}

/* Loading States */
.upload-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.upload-loading .spinner-border {
    color: var(--brewery-medium-brown, #D69A6B);
}

/* Error States */
.upload-error {
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important;
}

.upload-error .upload-content {
    color: #dc3545 !important;
}

/* Success States */
.upload-success {
    border-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.1) !important;
}

.upload-success .upload-content {
    color: #28a745 !important;
}

/* Ensure high contrast for all text */
.upload-area,
.analysis-summary,
.field-mapping-table,
.import-options,
.sample-preview {
    color: var(--brewery-very-dark, #2c1810) !important;
}

.text-muted {
    color: var(--brewery-text-light, #8B7355) !important;
}
