/* Beersty - Main Stylesheet */

:root {
    /* Brand Colors */
    --brand-primary: #FFC107;        /* Amber/Gold */
    --brand-secondary: #6F4C3E;      /* Dark Brown */
    --brand-accent: #D69A6B;         /* Medium Brown */

    /* Status Colors */
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #8B7355;
}

/* Light Theme (Default) */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;

    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;

    --border-primary: #dee2e6;
    --border-secondary: #e9ecef;
}

/* Dark Theme */
html.dark-mode,
.dark-mode {
    --bg-primary: #1a1a1a !important;
    --bg-secondary: #2d2d2d !important;
    --bg-tertiary: #404040 !important;

    --text-primary: #ffffff !important;
    --text-secondary: #b3b3b3 !important;
    --text-muted: #6c757d !important;

    --border-primary: #404040 !important;
    --border-secondary: #2d2d2d !important;
}

/* Force dark mode styles */
html.dark-mode body,
.dark-mode body {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

html.dark-mode .container,
.dark-mode .container {
    background-color: transparent !important;
    color: #ffffff !important;
}

html.dark-mode .card,
.dark-mode .card {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border-color: #404040 !important;
}

html.dark-mode .form-control,
html.dark-mode .form-select,
.dark-mode .form-control,
.dark-mode .form-select {
    background-color: #404040 !important;
    color: #ffffff !important;
    border-color: #6c757d !important;
}

html.dark-mode .form-control:focus,
html.dark-mode .form-select:focus,
.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: #404040 !important;
    color: #ffffff !important;
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Additional Bootstrap overrides for dark mode */
html.dark-mode input,
html.dark-mode textarea,
html.dark-mode select,
html.dark-mode .form-control,
html.dark-mode .form-select,
html.dark-mode .form-check-input,
.dark-mode input,
.dark-mode textarea,
.dark-mode select,
.dark-mode .form-control,
.dark-mode .form-select,
.dark-mode .form-check-input {
    background-color: #404040 !important;
    color: #ffffff !important;
    border-color: #6c757d !important;
}

html.dark-mode .btn-primary,
.dark-mode .btn-primary {
    background-color: var(--brand-primary) !important;
    border-color: var(--brand-primary) !important;
    color: #000000 !important;
}

html.dark-mode .btn-secondary,
.dark-mode .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

html.dark-mode .alert,
.dark-mode .alert {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border-color: #404040 !important;
}

html.dark-mode .alert-info,
.dark-mode .alert-info {
    background-color: #1e3a5f !important;
    color: #b3d4fc !important;
    border-color: #2e5a8f !important;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.main-content {
    min-height: calc(100vh - 200px);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Dark mode scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-accent);
    border-radius: 6px;
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--beer-gold);
}

/* Navigation */
.navbar {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark-mode .navbar {
    background-color: #000000;
    border-bottom: 1px solid var(--brand-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-brand {
    font-weight: bold;
    color: var(--brand-primary) !important;
}

.navbar-nav .nav-link {
    color: var(--text-primary) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--brand-primary) !important;
}

.navbar-toggler {
    border-color: var(--border-primary);
}

/* User Avatar Styles */
.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border: 2px solid var(--brand-primary);
    border-radius: 50%;
}

.avatar-placeholder {
    width: 32px;
    height: 32px;
    background: var(--brand-primary);
    color: white;
    font-size: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-placeholder-large {
    width: 40px;
    height: 40px;
    background: var(--brand-primary);
    color: white;
    font-size: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-header .user-avatar img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border: 2px solid var(--brand-primary);
    border-radius: 50%;
}

/* Dropdown Styles */
.dropdown-menu {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.dropdown-menu .dropdown-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dropdown-menu .dropdown-item {
    color: var(--text-primary);
    transition: background-color 0.2s ease;
}

.dropdown-menu .dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--brand-primary);
}

.dropdown-menu .dropdown-item.text-danger {
    color: var(--danger);
}

.dropdown-menu .dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

/* Notification Badge */
#notification-badge, #message-badge-dropdown {
    font-size: 0.7rem;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    background-color: var(--danger);
}

/* Theme Toggle */
.dropdown-menu .btn-group .btn {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.dropdown-menu .btn-group .btn:hover {
    background-color: var(--brand-primary);
    color: white;
}

.dropdown-menu .btn-group .btn.active {
    background-color: var(--brand-primary);
    color: white;
    border-color: var(--brand-primary);
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.card-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--brand-secondary);
    border-color: var(--brand-secondary);
    color: white;
}

.btn-outline-primary {
    border-color: var(--brand-primary);
    color: var(--brand-primary);
}

.btn-outline-primary:hover {
    background-color: var(--brand-primary);
    color: white;
}

/* Forms */
.form-control, .form-select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 6px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.form-check-input {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
}

.form-check-input:checked {
    background-color: var(--beer-gold);
    border-color: var(--beer-gold);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 179, 71, 0.25);
}

.form-check-label {
    color: var(--text-secondary);
}

.input-group-text {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
}

/* Search bars */
.search-container {
    position: relative;
}

.search-container .form-control {
    padding-left: 2.5rem;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-accent) 100%);
    border: 2px solid var(--border-primary);
}

.search-container::before {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    z-index: 10;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

/* ALL alerts - Green with white text for better readability */
.alert-success,
.alert-danger,
.alert-warning,
.alert-info,
.alert {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: 1px solid #28a745 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
    padding: 1rem 1.25rem !important;
}

/* Ensure all text in ALL alerts is white */
.alert-success,
.alert-success *,
.alert-danger,
.alert-danger *,
.alert-warning,
.alert-warning *,
.alert-info,
.alert-info *,
.alert,
.alert *,
.alert .fas,
.alert .fa,
.alert .btn-close {
    color: #ffffff !important;
}

/* Alert close button styling */
.alert .btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.8;
}

.alert .btn-close:hover {
    opacity: 1;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    border-top: none;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

/* Badges */
.badge {
    font-weight: 500;
    font-size: 0.75em;
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

/* Text Color Overrides - Force Warm Colors */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

p, span, div, li, td, th {
    color: var(--text-primary) !important;
}

.text-dark {
    color: var(--text-primary) !important;
}

.text-muted {
    color: var(--text-secondary) !important;
}

.text-black {
    color: var(--text-primary) !important;
}

small {
    color: var(--text-secondary) !important;
}

/* Bootstrap Text Overrides */
.text-body {
    color: var(--text-primary) !important;
}

.text-body-secondary {
    color: var(--text-secondary) !important;
}

/* Utilities */
.text-brewery-gold {
    color: var(--brewery-gold) !important;
}

.text-brewery-brown {
    color: var(--brewery-brown) !important;
}

.bg-brewery-gold {
    background-color: var(--brewery-gold) !important;
}

.bg-brewery-brown {
    background-color: var(--brewery-brown) !important;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Mobile Navigation Adjustments */
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }

    .dropdown-menu {
        margin-top: 0.5rem;
        min-width: 280px !important;
    }

    #userDropdown span {
        display: none !important;
    }

    .user-avatar {
        margin-right: 0 !important;
    }

    #notificationsDropdown {
        padding: 0.75rem !important;
    }

    .avatar-placeholder {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .avatar-placeholder-large {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* SEO Breadcrumbs - Dark Mode */
.breadcrumb-nav {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-secondary);
}

.breadcrumb {
    margin: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--beer-gold);
    font-weight: bold;
    font-size: 1rem;
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--beer-gold);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Footer */
footer {
    margin-top: auto;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--brewery-gold) !important;
}

/* Print styles */
@media print {
    .navbar, .btn, .alert, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}
