/**
 * Media Manager Styles
 * Phase 4 12.0 - Media Management System
 */

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #d69a6b;
    border-radius: 8px;
    padding: 20px;
    background: rgba(108, 76, 62, 0.1);
    transition: all 0.3s ease;
}

.upload-area.dragover {
    border-color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
    transform: scale(1.02);
}

.upload-drop-zone {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-drop-zone:hover {
    background: rgba(108, 76, 62, 0.2);
}

/* Media Grid Styles */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.media-item {
    background: #3a3a3a;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.media-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.media-item.selected {
    border: 2px solid #ffc107;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
}

.media-thumbnail {
    width: 100%;
    height: 150px;
    background: #2c1810;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-thumbnail:hover img {
    transform: scale(1.1);
}

.media-thumbnail .media-icon {
    font-size: 3rem;
    color: #d69a6b;
}

.media-info {
    padding: 15px;
}

.media-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #f5f5dc;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-meta {
    font-size: 0.8rem;
    color: #999;
    margin-bottom: 10px;
}

.media-actions {
    display: flex;
    gap: 5px;
}

.media-actions .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Media List View */
.media-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.media-list .media-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #3a3a3a;
    border-radius: 8px;
}

.media-list .media-thumbnail {
    width: 80px;
    height: 60px;
    margin-right: 15px;
    flex-shrink: 0;
}

.media-list .media-info {
    flex: 1;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.media-list .media-details {
    flex: 1;
}

.media-list .media-actions {
    margin-left: 15px;
}

/* Upload Progress Styles */
.upload-item {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.upload-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.upload-filename {
    font-weight: 600;
    color: #f5f5dc;
}

.upload-size {
    color: #999;
    font-size: 0.9rem;
}

.upload-progress {
    height: 8px;
    background: #2c1810;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.upload-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffc107, #d69a6b);
    transition: width 0.3s ease;
}

.upload-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.upload-status.success {
    color: #28a745;
}

.upload-status.error {
    color: #dc3545;
}

.upload-status.uploading {
    color: #ffc107;
}

/* Media Preview Modal */
.media-preview-modal .modal-dialog {
    max-width: 90vw;
    max-height: 90vh;
}

.media-preview-content {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

.media-preview-info {
    background: #2c1810;
    padding: 20px;
    border-radius: 8px;
    margin-top: 15px;
}

.media-usage-list {
    max-height: 200px;
    overflow-y: auto;
}

.usage-item {
    background: #3a3a3a;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Storage Statistics */
.storage-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #444;
}

.storage-stat:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 600;
    color: #f5f5dc;
}

.stat-value {
    color: #ffc107;
    font-weight: 600;
}

.storage-progress {
    height: 20px;
    background: #2c1810;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.storage-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .media-thumbnail {
        height: 120px;
    }
    
    .media-info {
        padding: 10px;
    }
    
    .media-list .media-thumbnail {
        width: 60px;
        height: 45px;
    }
    
    .upload-drop-zone {
        min-height: 150px;
    }
}

@media (max-width: 576px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .media-thumbnail {
        height: 100px;
    }
    
    .media-actions {
        flex-direction: column;
        gap: 3px;
    }
    
    .media-actions .btn {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #3a3a3a 25%, #4a4a4a 50%, #3a3a3a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.media-item.loading .media-thumbnail {
    background: #3a3a3a;
}

.media-item.loading .media-info {
    background: linear-gradient(90deg, #3a3a3a 25%, #4a4a4a 50%, #3a3a3a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

/* Selection Styles */
.media-item .selection-checkbox {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-item:hover .selection-checkbox,
.media-item.selected .selection-checkbox {
    opacity: 1;
}

.bulk-actions-bar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #2c1810;
    border: 1px solid #d69a6b;
    border-radius: 25px;
    padding: 10px 20px;
    display: none;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

.bulk-actions-bar.show {
    display: flex;
}

.selected-count {
    color: #ffc107;
    font-weight: 600;
}

/* Media Type Icons */
.media-type-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.media-type-icon.image { color: #28a745; }
.media-type-icon.video { color: #dc3545; }
.media-type-icon.audio { color: #ffc107; }
