/**
 * Mobile-First CSS
 * Phase 9: Design & Mobile Optimization
 * Optimized for touch interfaces and mobile devices
 */

/* Mobile-First Base Styles */
:root {
    --primary-color: #f8b500;
    --primary-dark: #e6a200;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    /* Mobile-specific variables */
    --mobile-header-height: 60px;
    --mobile-bottom-nav-height: 70px;
    --mobile-touch-target: 44px;
    --mobile-padding: 1rem;
    --mobile-border-radius: 12px;
    
    /* Typography scale for mobile */
    --mobile-font-xs: 0.75rem;
    --mobile-font-sm: 0.875rem;
    --mobile-font-base: 1rem;
    --mobile-font-lg: 1.125rem;
    --mobile-font-xl: 1.25rem;
    --mobile-font-2xl: 1.5rem;
    --mobile-font-3xl: 1.875rem;
}

/* Mobile-First Typography */
@media (max-width: 768px) {
    body {
        font-size: var(--mobile-font-base);
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    h1 { font-size: var(--mobile-font-3xl); }
    h2 { font-size: var(--mobile-font-2xl); }
    h3 { font-size: var(--mobile-font-xl); }
    h4 { font-size: var(--mobile-font-lg); }
    h5 { font-size: var(--mobile-font-base); }
    h6 { font-size: var(--mobile-font-sm); }
    
    small { font-size: var(--mobile-font-xs); }
}

/* Touch-Optimized Buttons */
.btn {
    min-height: var(--mobile-touch-target);
    padding: 0.75rem 1.5rem;
    border-radius: var(--mobile-border-radius);
    font-weight: 600;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.btn:active {
    transform: scale(0.98);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Mobile-Optimized Forms */
@media (max-width: 768px) {
    .form-control,
    .form-select {
        min-height: var(--mobile-touch-target);
        padding: 0.75rem 1rem;
        border-radius: var(--mobile-border-radius);
        font-size: var(--mobile-font-base);
        border: 2px solid #dee2e6;
        transition: all 0.2s ease;
    }
    
    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(248, 181, 0, 0.25);
        transform: translateY(-1px);
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--dark-color);
    }
    
    .input-group {
        margin-bottom: 1rem;
    }
    
    /* Floating labels for better UX */
    .form-floating > .form-control,
    .form-floating > .form-select {
        padding: 1rem 0.75rem 0.5rem;
    }
    
    .form-floating > label {
        padding: 1rem 0.75rem;
        font-size: var(--mobile-font-sm);
    }
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem 1rem;
        min-height: var(--mobile-header-height);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .navbar-brand {
        font-size: var(--mobile-font-xl);
        font-weight: bold;
    }
    
    .navbar-toggler {
        border: none;
        padding: 0.5rem;
        border-radius: var(--mobile-border-radius);
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-radius: var(--mobile-border-radius);
        margin: 0.25rem 0;
        transition: all 0.2s ease;
    }
    
    .navbar-nav .nav-link:hover,
    .navbar-nav .nav-link:focus {
        background-color: rgba(248, 181, 0, 0.1);
        transform: translateX(5px);
    }
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--mobile-bottom-nav-height);
    background: white;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.mobile-bottom-nav .nav-item {
    flex: 1;
    text-align: center;
}

.mobile-bottom-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    color: var(--secondary-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: var(--mobile-border-radius);
    margin: 0.25rem;
}

.mobile-bottom-nav .nav-link:hover,
.mobile-bottom-nav .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(248, 181, 0, 0.1);
    transform: translateY(-2px);
}

.mobile-bottom-nav .nav-icon {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.mobile-bottom-nav .nav-text {
    font-size: var(--mobile-font-xs);
    font-weight: 600;
}

/* Add bottom padding to body when bottom nav is present */
@media (max-width: 768px) {
    body.has-bottom-nav {
        padding-bottom: calc(var(--mobile-bottom-nav-height) + 1rem);
    }
}

/* Mobile Cards */
@media (max-width: 768px) {
    .card {
        border-radius: var(--mobile-border-radius);
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
        padding: 1rem;
        border-bottom: none;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-footer {
        background: var(--light-color);
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 var(--mobile-border-radius) var(--mobile-border-radius);
        padding: 1rem;
    }
}

/* Mobile Tables */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: var(--mobile-border-radius);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
    }
    
    .table th {
        background: var(--light-color);
        font-weight: 600;
        border-bottom: 2px solid var(--primary-color);
    }
    
    /* Stack table on very small screens */
    @media (max-width: 576px) {
        .table-stack {
            display: block;
        }
        
        .table-stack thead {
            display: none;
        }
        
        .table-stack tbody,
        .table-stack tr,
        .table-stack td {
            display: block;
        }
        
        .table-stack tr {
            border: 1px solid #dee2e6;
            border-radius: var(--mobile-border-radius);
            margin-bottom: 1rem;
            padding: 1rem;
        }
        
        .table-stack td {
            border: none;
            padding: 0.5rem 0;
            position: relative;
            padding-left: 50%;
        }
        
        .table-stack td:before {
            content: attr(data-label);
            position: absolute;
            left: 0;
            width: 45%;
            font-weight: 600;
            color: var(--dark-color);
        }
    }
}

/* Mobile Modals */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
    
    .modal-content {
        border-radius: var(--mobile-border-radius);
        border: none;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    }
    
    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 var(--mobile-border-radius) var(--mobile-border-radius);
    }
    
    .modal-footer .btn {
        flex: 1;
        margin: 0 0.25rem;
    }
}

/* Mobile Alerts */
@media (max-width: 768px) {
    .alert {
        border-radius: var(--mobile-border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .alert-dismissible .btn-close {
        padding: 1rem;
    }
}

/* Mobile Badges */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: var(--mobile-font-xs);
}

/* Mobile Progress Bars */
.progress {
    height: 8px;
    border-radius: 50px;
    background-color: var(--light-color);
    overflow: hidden;
}

.progress-bar {
    border-radius: 50px;
    transition: width 0.6s ease;
}

/* Mobile Spinners */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Mobile Utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-only {
        display: block !important;
    }
    
    .mobile-text-center {
        text-align: center !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-no-padding {
        padding: 0 !important;
    }
    
    .mobile-small-padding {
        padding: 0.5rem !important;
    }
    
    .mobile-large-padding {
        padding: 2rem !important;
    }
}

/* Desktop-only utilities */
@media (min-width: 769px) {
    .mobile-only {
        display: none !important;
    }
    
    .mobile-bottom-nav {
        display: none !important;
    }
}

/* Touch-specific styles */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .btn:hover,
    .card:hover,
    .nav-link:hover {
        transform: none;
    }
    
    /* Increase touch targets */
    .btn,
    .nav-link,
    .form-control,
    .form-select {
        min-height: 48px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for retina displays */
    .card,
    .btn,
    .alert {
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-bottom-nav {
        height: 60px;
    }
    
    .mobile-bottom-nav .nav-icon {
        font-size: 1.1rem;
    }
    
    .mobile-bottom-nav .nav-text {
        font-size: 0.7rem;
    }
    
    body.has-bottom-nav {
        padding-bottom: calc(60px + 0.5rem);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --dark-color: #f7fafc;
        --secondary-color: #a0aec0;
    }
    
    .card {
        background-color: #2d3748;
        color: #f7fafc;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #f7fafc;
    }
    
    .mobile-bottom-nav {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }
}
