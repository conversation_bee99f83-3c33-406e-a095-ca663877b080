/**
 * API Documentation CSS
 * Phase 10: Advanced Features & API Development
 * Styling for interactive API documentation
 */

/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
    overflow-y: auto;
}

.sidebar-sticky {
    position: sticky;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 0.5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    color: #f8b500;
    background-color: rgba(248, 181, 0, 0.1);
}

.sidebar .nav-link.active {
    color: #f8b500;
    background-color: rgba(248, 181, 0, 0.15);
    font-weight: 600;
}

.sidebar-heading {
    font-size: 0.875rem;
    text-transform: uppercase;
    font-weight: 600;
    color: #6c757d;
    padding: 0 1rem;
    margin-bottom: 1rem;
}

/* Main Content */
.api-docs-content {
    margin-left: 0;
    padding: 2rem;
}

@media (min-width: 768px) {
    .api-docs-content {
        margin-left: 240px;
    }
}

/* API URL Styling */
.api-url {
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    font-weight: 600;
    display: block;
    text-align: center;
    box-shadow: 0 4px 15px rgba(248, 181, 0, 0.3);
}

/* Code Examples */
.code-example {
    background: #2d3748;
    border-radius: 0.5rem;
    overflow: hidden;
    margin: 1rem 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.code-tabs {
    background: #1a202c;
    display: flex;
    border-bottom: 1px solid #4a5568;
}

.tab-btn {
    background: none;
    border: none;
    color: #a0aec0;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.tab-btn:hover {
    color: #f8b500;
    background-color: rgba(248, 181, 0, 0.1);
}

.tab-btn.active {
    color: #f8b500;
    background-color: rgba(248, 181, 0, 0.15);
    border-bottom: 2px solid #f8b500;
}

.tab-content {
    display: none;
    padding: 0;
}

.tab-content.active {
    display: block;
}

.tab-content pre {
    margin: 0;
    padding: 1.5rem;
    background: #2d3748;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
}

.tab-content code {
    background: none;
    color: inherit;
    padding: 0;
    font-size: inherit;
}

/* Endpoint Styling */
.endpoint-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
    border-left: 4px solid #f8b500;
}

.endpoint {
    background: white;
    border-radius: 0.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.2s ease;
}

.endpoint:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.endpoint-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.endpoint-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.method {
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    min-width: 60px;
    text-align: center;
}

.method.get {
    background: #28a745;
    color: white;
}

.method.post {
    background: #007bff;
    color: white;
}

.method.put {
    background: #ffc107;
    color: #212529;
}

.method.delete {
    background: #dc3545;
    color: white;
}

.path {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
}

.description {
    color: #6c757d;
    flex: 1;
}

.endpoint-details {
    padding: 1.5rem;
    display: none;
}

.endpoint.expanded .endpoint-details {
    display: block;
}

/* Tables */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.table code {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    margin: 1rem 0;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
}

/* API Tester Modal */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    color: white;
    border-radius: 0.75rem 0.75rem 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

#test-response {
    background: #2d3748;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

#test-response pre {
    margin: 0;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 1rem 0;
    }
    
    .api-docs-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .endpoint-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .method {
        align-self: flex-start;
    }
    
    .code-tabs {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 80px;
    }
}

/* Syntax Highlighting */
.hljs {
    background: #2d3748 !important;
    color: #e2e8f0 !important;
}

.hljs-string {
    color: #68d391 !important;
}

.hljs-number {
    color: #f6ad55 !important;
}

.hljs-literal {
    color: #9f7aea !important;
}

.hljs-attr {
    color: #63b3ed !important;
}

.hljs-keyword {
    color: #f56565 !important;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #f8b500;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Section Anchors */
section {
    scroll-margin-top: 2rem;
}

/* Interactive Elements */
.endpoint-header {
    position: relative;
}

.endpoint-header::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.2s ease;
    color: #6c757d;
}

.endpoint.expanded .endpoint-header::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Copy Button for Code */
.code-example {
    position: relative;
}

.copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(248, 181, 0, 0.9);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.code-example:hover .copy-btn {
    opacity: 1;
}

.copy-btn:hover {
    background: #f8b500;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background: #28a745;
}

.status-offline {
    background: #dc3545;
}

.status-warning {
    background: #ffc107;
}
