/* Beersty Layout Styles - Brewery-Themed Warm Palette */

/* Global Brewery Theme Override */
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

/* Hero Section Enhancements */
.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    color: #F5F5DC !important;
}

.hero-bg {
    z-index: -1;
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-container .form-control:focus,
.search-container .form-select:focus {
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Stats Section */
.stats-section .stat-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.stats-section .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-icon {
    opacity: 0.8;
}

/* Feature Cards */
.feature-card {
    background: #6F4C3E !important;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #D69A6B !important;
    height: 100%;
    color: #F5F5DC !important;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(59, 42, 42, 0.8) !important;
    border-color: #FFC107 !important;
}

.feature-icon {
    margin-bottom: 1.5rem;
}

/* Sidebar Cards */
.sidebar-card {
    background: #6F4C3E !important;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(59, 42, 42, 0.5) !important;
    border: 1px solid #D69A6B !important;
    color: #F5F5DC !important;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #D69A6B !important;
    color: #F5F5DC !important;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    width: 40px;
    text-align: center;
}

.popular-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #D69A6B !important;
    color: #F5F5DC !important;
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-rank {
    background: #FFC107 !important;
    color: #3B2A2A !important;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    font-size: 0.9rem;
}

/* Places Search Page */
.search-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    color: #F5F5DC !important;
}

.search-form .input-group-text {
    background: #D69A6B !important;
    border-right: none;
    color: #3B2A2A !important;
}

.search-form .form-control {
    border-left: none;
    background-color: #6F4C3E !important;
    color: #F5F5DC !important;
    border-color: #D69A6B !important;
}

.search-form .form-control:focus {
    border-color: #FFC107 !important;
    box-shadow: none;
    background-color: #6F4C3E !important;
    color: #F5F5DC !important;
}

.view-toggle .btn-outline-secondary {
    border-color: #D69A6B !important;
    color: #F5F5DC !important;
}

.view-toggle .btn-check:checked + .btn-outline-secondary {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

/* Filters Sidebar */
.filters-sidebar .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filter-group {
    border-bottom: 1px solid #f8f9fa;
    padding-bottom: 1rem;
}

.filter-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.filter-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

/* Place Cards */
.place-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
}

.place-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.place-image {
    overflow: hidden;
}

.place-badge .badge {
    font-size: 0.75rem;
}

.place-distance .badge {
    font-size: 0.7rem;
}

.rating-stars {
    font-size: 0.9rem;
    line-height: 1;
}

.place-features .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Place Profile Page */
.place-hero {
    position: relative;
}

.place-hero .hero-image {
    position: relative;
}

.place-badges .badge {
    padding: 0.5rem 1rem;
}

.rating-display .rating-stars {
    margin-right: 0.5rem;
}

.place-actions .btn {
    min-width: 120px;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    border-bottom: 3px solid transparent;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #667eea;
}

.nav-tabs .nav-link.active {
    background: none;
    border-color: transparent transparent #667eea transparent;
    color: #667eea;
}

/* Contact Info Cards */
.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    margin-top: 0.25rem;
    width: 20px;
}

.hours-item {
    font-size: 0.9rem;
}

/* Beer Cards */
.beer-card {
    border: none;
    background: #f8f9fa;
    transition: transform 0.2s ease;
}

.beer-card:hover {
    transform: translateY(-2px);
}

/* Review Items */
.review-item {
    padding: 1rem 0;
}

.reviewer-info {
    display: flex;
    align-items: center;
}

/* Photo Gallery */
.photo-item img {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.photo-item img:hover {
    transform: scale(1.05);
}

/* Map Placeholder */
.map-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
    }
    
    .search-container {
        padding: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .place-actions {
        flex-direction: column;
    }
    
    .place-actions .btn {
        min-width: auto;
    }
    
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .feature-card,
    .sidebar-card,
    .filters-sidebar .card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .activity-item,
    .popular-item,
    .filter-group {
        border-color: #4a5568;
    }
    
    .beer-card {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .map-placeholder {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: #e2e8f0;
    }
}

/* Deal Cards */
.deal-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.deal-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.qr-code-placeholder {
    background: linear-gradient(135deg, #333 0%, #555 100%);
    border: 2px dashed #666;
}

.deal-subscription {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

/* Social Stats */
.stat-item h5 {
    color: #667eea;
    font-weight: 700;
}

.stat-item small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Photo Gallery */
.photo-thumbnail {
    transition: transform 0.3s ease, filter 0.3s ease;
    cursor: pointer;
}

.photo-item {
    overflow: hidden;
    border-radius: 8px;
}

.photo-item:hover .photo-thumbnail {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 1.5rem;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-item:hover .photo-actions {
    opacity: 1;
}

.photo-categories .nav-pills .nav-link {
    color: #6c757d;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.photo-categories .nav-pills .nav-link:hover {
    background-color: #e9ecef;
    color: #495057;
}

.photo-categories .nav-pills .nav-link.active {
    background-color: #667eea;
    color: white;
}

/* 360 Tour */
.tour-iframe-wrapper {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.tour-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

/* Claim Listing */
.card.border-warning {
    border-width: 2px;
}

.card.border-warning .card-header {
    border-bottom: 2px solid #ffc107;
}

/* Follow Button States */
.btn-follow {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-follow:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-follow.following {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-follow.following:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-follow.following:hover::before {
    content: 'Unfollow';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.btn-follow.following:hover .btn-text {
    opacity: 0;
}

/* Global Text Color Enforcement */
* {
    color: #F5F5DC !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #F5F5DC !important;
}

p, span, div, li, td, th, a, label {
    color: #F5F5DC !important;
}

.text-dark, .text-black, .text-body {
    color: #F5F5DC !important;
}

.text-muted, .text-secondary {
    color: #D69A6B !important;
}

small, .small {
    color: #D69A6B !important;
}

/* Card Content */
.card *, .card-body *, .card-header *, .card-footer * {
    color: #F5F5DC !important;
}

/* Form Elements */
.form-control, .form-select, .form-label, .form-text {
    color: #F5F5DC !important;
}

.form-control::placeholder {
    color: #D69A6B !important;
}

/* Navigation */
.nav-link, .navbar-nav .nav-link, .breadcrumb-item {
    color: #F5F5DC !important;
}

/* Tables */
.table, .table th, .table td {
    color: #F5F5DC !important;
}

/* Buttons */
.btn-primary, .btn-warning {
    color: #3B2A2A !important;
}

.btn-outline-primary, .btn-outline-secondary {
    color: #F5F5DC !important;
}

/* Badges */
.badge {
    color: #3B2A2A !important;
}

.badge.bg-light, .badge.bg-secondary {
    color: #3B2A2A !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Success States */
.success-flash {
    animation: successFlash 0.6s ease-out;
}

@keyframes successFlash {
    0% { background-color: transparent; }
    50% { background-color: rgba(40, 167, 69, 0.2); }
    100% { background-color: transparent; }
}
