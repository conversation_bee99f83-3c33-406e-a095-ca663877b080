/* Menu Management CSS - Dark Mode Support */

/* Menu Item Cards */
.menu-item-card {
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    overflow: hidden;
}

.menu-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .menu-item-card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.dark-mode .menu-item-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.menu-item-header {
    padding: 1rem 1rem 0.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.menu-item-body {
    padding: 0.5rem 1rem 1rem;
}

.menu-item-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.menu-item-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.menu-item-details {
    font-size: 0.85rem;
}

/* Navigation Tabs */
.nav-tabs {
    border-bottom: 2px solid var(--border-primary);
    background-color: var(--bg-secondary);
    border-radius: 8px 8px 0 0;
    padding: 0.5rem;
}

.dark-mode .nav-tabs {
    background-color: var(--bg-tertiary);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-tabs .nav-link.active {
    background-color: var(--brand-primary);
    color: white;
    border-color: var(--brand-primary);
}

.nav-tabs .nav-link .badge {
    font-size: 0.7rem;
}

/* Tab Content */
.tab-content {
    background-color: var(--bg-secondary);
    border-radius: 0 0 8px 8px;
    padding: 2rem;
    min-height: 500px;
}

.dark-mode .tab-content {
    background-color: var(--bg-secondary);
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    border-radius: 8px;
}

.dark-mode .card {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .card-header {
    background-color: var(--bg-tertiary);
}

.card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .card-body {
    background-color: var(--bg-secondary);
}

/* Statistics */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
}

.badge.bg-warning {
    background-color: var(--warning) !important;
    color: #000000 !important;
}

.badge.bg-success {
    background-color: var(--success) !important;
    color: white !important;
}

.badge.bg-danger {
    background-color: var(--danger) !important;
    color: white !important;
}

.badge.bg-info {
    background-color: var(--info) !important;
    color: white !important;
}

.badge.bg-dark {
    background-color: #343a40 !important;
    color: white !important;
}

.badge.bg-primary {
    background-color: var(--brand-primary) !important;
    color: white !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--brand-secondary);
    border-color: var(--brand-secondary);
    color: white;
}

.btn-outline-primary {
    color: var(--brand-primary);
    border-color: var(--brand-primary);
}

.btn-outline-primary:hover {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-primary);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

/* Form Controls */
.form-control, .form-select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .form-control,
.dark-mode .form-select {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.form-control:focus, .form-select:focus {
    background-color: var(--bg-primary);
    border-color: var(--brand-primary);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: var(--bg-tertiary);
    border-color: var(--brand-primary);
}

/* Dropdown Menus */
.dropdown-menu {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .dropdown-menu {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.dropdown-item {
    color: var(--text-primary);
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.dark-mode .dropdown-item:hover {
    background-color: var(--bg-tertiary);
}

/* View Toggle Buttons */
.btn-group .btn-check:checked + .btn-outline-secondary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

/* Text Colors */
.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--brand-primary) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

/* Empty State Styling */
.text-center.py-5 {
    color: var(--text-secondary);
}

.text-center.py-5 i {
    opacity: 0.6;
}

.text-center.py-5 h5 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-item-card {
        margin-bottom: 1rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .menu-item-details .row {
        margin-bottom: 0.5rem;
    }
    
    .stat-item {
        padding: 0.5rem 0;
    }
}

/* Animation */
.menu-item-card {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-item-card:nth-child(1) { animation-delay: 0.1s; }
.menu-item-card:nth-child(2) { animation-delay: 0.2s; }
.menu-item-card:nth-child(3) { animation-delay: 0.3s; }
.menu-item-card:nth-child(4) { animation-delay: 0.4s; }

/* Modal Styling */
.modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .modal-content {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
}

.modal-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.dark-mode .modal-header {
    background-color: var(--bg-tertiary);
    border-bottom-color: var(--border-primary);
}

.modal-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .modal-body {
    background-color: var(--bg-secondary);
}

.modal-footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
}

.dark-mode .modal-footer {
    background-color: var(--bg-tertiary);
    border-top-color: var(--border-primary);
}

.modal-title {
    color: var(--text-primary);
}

/* Form Labels */
.form-label {
    color: var(--text-primary);
    font-weight: 500;
}

/* Close Button */
.btn-close {
    filter: var(--bs-btn-close-white-filter, none);
}

.dark-mode .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}
