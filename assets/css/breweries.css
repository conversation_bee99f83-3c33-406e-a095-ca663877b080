/* Breweries Listing Styles */

.brewery-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.brewery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.brewery-logo {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 0.375rem;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brewery-stats {
    font-size: 0.875rem;
}

.brewery-stats i {
    color: #6c757d;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.brewery-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Filter form */
#filterForm .form-control,
#filterForm .form-select {
    border-radius: 0.375rem;
}

#filterForm .btn {
    height: calc(2.375rem + 2px);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
    transition: all 0.15s ease-in-out;
}

.pagination .page-link:hover {
    color: #0b5ed7;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* Empty state */
.text-center.py-5 {
    padding: 4rem 1rem !important;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .brewery-card {
        margin-bottom: 1.5rem;
    }
    
    .brewery-logo {
        width: 40px;
        height: 40px;
    }
    
    #filterForm .row {
        row-gap: 1rem;
    }
    
    #filterForm .col-md-1 {
        order: 6;
    }
    
    #filterForm .btn {
        width: 100%;
        height: auto;
        padding: 0.5rem;
    }
    
    .pagination {
        font-size: 0.875rem;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }
    
    .brewery-stats {
        font-size: 0.8rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Loading states */
.brewery-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.brewery-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search highlighting */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Filter active states */
.form-control:not(:placeholder-shown),
.form-select:not([value=""]) {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}
