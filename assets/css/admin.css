/* Beersty Admin Panel CSS - Industry Standard Sidebar Layout */

/* Modern WordPress-Style Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background-color: #2C1810;
    position: relative;
}

/* Modern Sliding Sidebar */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #3B2A2A 0%, #6F4C3E 100%);
    color: #F5F5DC;
    position: fixed;
    top: 80px; /* Start below main navigation */
    left: 0;
    height: calc(100vh - 80px); /* Adjust height to account for main nav */
    overflow-y: auto;
    z-index: 100; /* Lower z-index so it doesn't overlay main nav */
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.4);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

/* Collapsed Sidebar State */
.admin-sidebar.collapsed {
    width: 60px;
    background: linear-gradient(180deg, #3B2A2A 0%, #5A3E32 100%);
    border-right: 2px solid rgba(255, 193, 7, 0.3);
}

/* Add visual indicator for collapsed state */
.admin-sidebar.collapsed::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -1px;
    width: 2px;
    height: 40px;
    background: #FFC107;
    transform: translateY(-50%);
    border-radius: 0 2px 2px 0;
    opacity: 0.7;
    animation: pulse 2s infinite;
}

.admin-sidebar.collapsed .sidebar-header h4,
.admin-sidebar.collapsed .menu-title,
.admin-sidebar.collapsed .menu-item span,
.admin-sidebar.collapsed .item-badge,
.admin-sidebar.collapsed .sidebar-search {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

/* Keep toggle button visible when collapsed */
.admin-sidebar.collapsed .sidebar-toggle-btn {
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* Keep icons visible and functional when collapsed */
.admin-sidebar.collapsed .menu-item i {
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 1.2rem;
    color: #F5F5DC !important;
    transition: color 0.2s ease;
    margin-right: 0 !important;
    display: block !important;
}

.admin-sidebar.collapsed .menu-item:hover i {
    color: #FFC107;
}

.admin-sidebar.collapsed .menu-item {
    justify-content: center;
    padding: 0.75rem;
    position: relative;
    margin: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.admin-sidebar.collapsed .menu-item:hover {
    background: rgba(255, 193, 7, 0.2);
    transform: scale(1.05);
}



.admin-sidebar.collapsed .menu-item.active {
    background: rgba(255, 193, 7, 0.3);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
}

/* WordPress-Style Tooltips */
.admin-sidebar.collapsed .menu-item::after {
    content: attr(data-tooltip);
    position: fixed;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(59, 42, 42, 0.95);
    color: #F5F5DC;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1002;
    border: 1px solid rgba(255, 193, 7, 0.4);
    backdrop-filter: blur(15px);
    transform: translateY(-50%) translateX(-10px) scale(0.9);
}

.admin-sidebar.collapsed .menu-item:hover::after {
    opacity: 1;
    transform: translateY(-50%) translateX(0) scale(1);
}

/* Tooltip arrow */
.admin-sidebar.collapsed .menu-item::before {
    content: '';
    position: fixed;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-right: 5px solid rgba(59, 42, 42, 0.95);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
}

.admin-sidebar.collapsed .menu-item:hover::before {
    opacity: 1;
}

.sidebar-header {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid rgba(245, 245, 220, 0.2);
    background: rgba(59, 42, 42, 0.8);
    backdrop-filter: blur(10px);
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed sidebar header keeps horizontal layout */
.admin-sidebar.collapsed .sidebar-header {
    padding: 1rem 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.admin-sidebar.collapsed .sidebar-header .sidebar-toggle-btn {
    width: 32px;
    height: 32px;
    padding: 0.4rem;
    font-size: 0.8rem;
}



.sidebar-header h4 {
    margin: 0;
    color: #FFC107;
    font-weight: 600;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #F5F5DC;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
}

/* Modern Search Bar */
.sidebar-search {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(245, 245, 220, 0.1);
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    background: rgba(59, 42, 42, 0.5);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    color: #F5F5DC;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.search-input:focus {
    outline: none;
    border-color: #FFC107;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
    background: rgba(59, 42, 42, 0.8);
}

.search-input::placeholder {
    color: rgba(245, 245, 220, 0.6);
}

.search-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 193, 7, 0.7);
    font-size: 0.875rem;
    pointer-events: none;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(59, 42, 42, 0.95);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 0.5rem;
    margin-top: 0.25rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1002;
    display: none;
    backdrop-filter: blur(20px);
}

/* Sidebar Menu */
.sidebar-menu {
    padding: 1rem 0;
}

.menu-section {
    margin-bottom: 1.5rem;
}

/* Modern Collapsible Menu Sections */
.menu-title {
    padding: 0.75rem 1.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: rgba(245, 245, 220, 0.9);
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0.5rem;
    margin: 0.25rem 0.75rem 0.5rem;
}

.menu-title.collapsible:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
    transform: translateX(2px);
}

.toggle-icon {
    font-size: 0.7rem;
    transition: transform 0.3s ease;
}

.menu-section.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.menu-items {
    max-height: 500px;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-section.collapsed .menu-items {
    max-height: 0;
}

/* Collapsed sidebar hides section titles but shows separator */
.admin-sidebar.collapsed .menu-section {
    border-bottom: 1px solid rgba(255, 193, 7, 0.1);
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
}

.admin-sidebar.collapsed .menu-section:last-child {
    border-bottom: none;
}

/* Ensure menu items container is visible when collapsed */
.admin-sidebar.collapsed .menu-items {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    max-height: none !important;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: #F5F5DC;
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: rgba(255, 193, 7, 0.15);
    color: #FFC107;
    border-left-color: #FFC107;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.menu-item.active {
    background-color: rgba(255, 193, 7, 0.25);
    color: #FFC107;
    border-left-color: #FFC107;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.menu-item i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

.menu-item span {
    font-size: 0.875rem;
}

/* Modern Menu Item Badges */
.item-badge {
    margin-left: auto;
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 0.75rem;
    min-width: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.item-badge.new {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    animation: pulse 2s infinite;
}

.item-badge.warning {
    background: rgba(251, 146, 60, 0.2);
    color: #fb923c;
}

.menu-item:hover .item-badge {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Search Results Styling */
.search-result-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #F5F5DC;
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(255, 193, 7, 0.1);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: rgba(255, 193, 7, 0.15);
    color: #FFC107;
    transform: translateX(4px);
}

.search-result-item i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.no-results {
    padding: 1rem;
    text-align: center;
    color: rgba(245, 245, 220, 0.6);
    font-size: 0.875rem;
}

/* Modern Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.menu-item {
    animation: fadeInUp 0.3s ease forwards;
}

.stat-card {
    animation: fadeInUp 0.4s ease forwards;
}

.content-card {
    animation: fadeInUp 0.5s ease forwards;
}

/* Modern Invisible Scrollbars */
.admin-sidebar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.admin-sidebar::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Opera */
}

/* Modern Scroll Indicator */
.admin-sidebar::before {
    content: '';
    position: fixed;
    top: 0;
    right: 0;
    width: 2px;
    height: 100vh;
    background: rgba(255, 193, 7, 0.1);
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.admin-sidebar:hover::before {
    opacity: 1;
}

/* Main Content Area - WordPress Style */
.admin-main {
    flex: 1;
    margin-left: 280px;
    margin-top: 80px; /* Account for main navigation */
    background-color: #2C1810;
    min-height: calc(100vh - 80px);
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

/* Main content adjusts when sidebar is collapsed */
.admin-sidebar.collapsed ~ .admin-main {
    margin-left: 60px;
}

/* Ensure main content doesn't get overlapped */
.admin-main .main-header {
    position: relative;
    z-index: 10;
}

.admin-main .dashboard-content {
    position: relative;
    z-index: 5;
}

.main-header {
    background: #3B2A2A;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #6F4C3E;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 10;
    margin-bottom: 0;
}

/* Remove fixed positioning conflicts */
.admin-sidebar.collapsed ~ .admin-main .main-header {
    /* Header stays in normal flow */
}

/* Header Layout */
.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.main-header h1 {
    margin: 0;
    color: #F5F5DC;
    font-size: 1.5rem;
    font-weight: 600;
}

/* WordPress-Style Sidebar Toggle Button */
.sidebar-toggle-btn {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #FFC107;
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.sidebar-toggle-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 193, 7, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: 0;
}

.sidebar-toggle-btn:hover::before {
    width: 100%;
    height: 100%;
}

.sidebar-toggle-btn:hover {
    background: rgba(255, 193, 7, 0.2);
    border-color: #FFC107;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.sidebar-toggle-btn:active {
    transform: scale(0.95);
}

.sidebar-toggle-btn i {
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-toggle-btn:hover i {
    transform: scale(1.1);
}

/* Rotate icon when sidebar is collapsed to indicate expand direction */
.admin-sidebar.collapsed .sidebar-toggle-btn i {
    transform: rotate(180deg);
}

.admin-sidebar.collapsed .sidebar-toggle-btn:hover i {
    transform: rotate(180deg) scale(1.1);
}

.header-actions {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.header-actions .btn {
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.header-actions .btn-primary {
    background-color: #FFC107;
    border-color: #FFC107;
    color: #3B2A2A;
}

.header-actions .btn-primary:hover {
    background-color: #D69A6B;
    border-color: #D69A6B;
    color: #3B2A2A;
}

.header-actions .btn-outline-primary {
    border-color: #FFC107;
    color: #FFC107;
    background-color: transparent;
}

.header-actions .btn-outline-primary:hover {
    background-color: #FFC107;
    border-color: #FFC107;
    color: #3B2A2A;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #6F4C3E 0%, #5A3E32 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-left: 4px solid #FFC107;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    border-left-color: #D69A6B;
}

.stat-primary { border-left-color: #FFC107; }
.stat-success { border-left-color: #28a745; }
.stat-warning { border-left-color: #D69A6B; }
.stat-danger { border-left-color: #dc3545; }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.stat-primary .stat-icon { background: rgba(255, 193, 7, 0.1); color: #FFC107; }
.stat-success .stat-icon { background: rgba(40, 167, 69, 0.1); color: #28a745; }
.stat-warning .stat-icon { background: rgba(214, 154, 107, 0.1); color: #D69A6B; }
.stat-danger .stat-icon { background: rgba(220, 53, 69, 0.1); color: #dc3545; }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #F5F5DC;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #D69A6B;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-link {
    color: #D69A6B;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.stat-link:hover {
    color: #FFC107;
}

/* Dashboard Content - WordPress Style */
.dashboard-content {
    padding: 2rem;
    background-color: #2C1810;
    min-height: calc(100vh - 120px);
    margin-top: 0;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.content-card {
    background: linear-gradient(135deg, #6F4C3E 0%, #5A3E32 100%);
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    border: 1px solid rgba(214, 154, 107, 0.3);
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 193, 7, 0.5);
}

.content-card .card-header {
    background: #3B2A2A;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #D69A6B;
}

.content-card .card-header h5 {
    margin: 0;
    color: #F5F5DC;
    font-size: 1rem;
    font-weight: 600;
}

.content-card .card-body {
    padding: 1.5rem;
    background: #6F4C3E;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1050;
        width: 280px;
        position: fixed;
        top: 80px; /* Keep below main nav on mobile too */
        height: calc(100vh - 80px);
    }

    .admin-sidebar.show {
        transform: translateX(0);
        box-shadow: 4px 0 20px rgba(0, 0, 0, 0.5);
    }

    .admin-main {
        margin-left: 0;
        margin-top: 80px;
        width: 100%;
    }

    .main-header {
        padding: 1rem;
        position: relative;
        left: 0;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .header-actions .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Mobile overlay */
    .admin-sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

@media (max-width: 576px) {
    .main-header h1 {
        font-size: 1.25rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .content-card .card-body {
        padding: 1rem;
    }
}

/* Table Styling - Dark Mode */
.table {
    margin-bottom: 0;
    color: #F5F5DC;
    background-color: transparent;
}

.table th {
    border-top: none;
    border-bottom: 1px solid #D69A6B;
    font-weight: 600;
    color: #F5F5DC;
    font-size: 0.875rem;
    background-color: #3B2A2A;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
    color: #F5F5DC;
    border-bottom: 1px solid rgba(214, 154, 107, 0.3);
}

.table tbody tr:hover {
    background-color: rgba(255, 193, 7, 0.1);
}

.table a {
    color: #FFC107;
    text-decoration: none;
}

.table a:hover {
    color: #D69A6B;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.text-muted {
    color: #D69A6B !important;
}

/* Button Styling */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-primary {
    border-color: #FFC107;
    color: #FFC107;
}

.btn-outline-primary:hover {
    background-color: #FFC107;
    border-color: #FFC107;
    color: #3B2A2A;
}

/* Page Header */
.border-bottom {
    border-color: var(--border-primary) !important;
}

h1.h2 {
    color: var(--text-primary);
    font-weight: 600;
}

/* Cards */
.card {
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dark-mode .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.card-header {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    font-weight: 500;
}

.card-header h5 {
    margin: 0;
    color: var(--text-primary);
}

/* Statistics Cards */
.card.text-center {
    transition: transform 0.2s ease;
}

.card.text-center:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark-mode .card.text-center:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.card-title {
    color: var(--text-primary) !important;
    font-size: 1.8rem;
    font-weight: bold;
}

.card-text.small {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Buttons */
.btn-primary {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--brand-secondary);
    border-color: var(--brand-secondary);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-primary);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

/* Form Controls */
.form-control, .form-select {
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    padding: 0.6rem 0.75rem;
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: var(--brand-primary);
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Dropdown specific improvements */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    cursor: pointer;
}

.form-select option {
    background-color: #f8f9fa;
    color: #333333;
    padding: 0.5rem;
    font-weight: 500;
}

.form-select option:hover {
    background-color: var(--brewery-light-brown);
    color: #333333;
}

.form-select option:checked {
    background-color: var(--brewery-medium-brown);
    color: white;
}

/* Table Improvements */
.table {
    background-color: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    color: var(--text-primary);
}

/* Light mode table styling */
.table tbody tr {
    background-color: #ffffff;
    border-bottom: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-color: #dee2e6;
    color: #212529;
    background-color: #ffffff;
}

.table-dark th {
    background-color: #343a40;
    border-color: #454d55;
    color: #ffffff;
    font-weight: 600;
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dark mode table styling */
.dark-mode .table tbody tr {
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
}

.dark-mode .table tbody tr:hover {
    background-color: #404040;
}

.dark-mode .table td {
    color: #ffffff;
    background-color: #2d2d2d;
    border-color: #404040;
}

.dark-mode .table-dark th {
    background-color: #1a1a1a;
    border-color: #404040;
    color: #ffffff;
}

/* User Management Table Specific */
.user-management-table td {
    font-size: 0.9rem;
    line-height: 1.4;
}

.user-management-table .id-column {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.dark-mode .user-management-table .id-column {
    color: #b3b3b3;
}

.user-management-table .email-column {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-management-table .date-display {
    font-size: 0.85rem;
    color: #6c757d !important;
    font-weight: 500;
    background-color: transparent !important;
    padding: 0 !important;
    border: none !important;
}

.dark-mode .user-management-table .date-display {
    color: #e9ecef !important;
    background-color: transparent !important;
}

/* Fix "Never" and "Unknown" text in date columns */
.user-management-table .date-display.text-muted {
    color: #6c757d !important;
    background-color: transparent !important;
}

.dark-mode .user-management-table .date-display.text-muted {
    color: #adb5bd !important;
    background-color: transparent !important;
}

/* Force remove any white backgrounds from date columns */
.user-management-table td:nth-child(6) div,
.user-management-table td:nth-child(7) div {
    background-color: transparent !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

.dark-mode .user-management-table td:nth-child(6) div,
.dark-mode .user-management-table td:nth-child(7) div {
    background-color: transparent !important;
    background: none !important;
    color: inherit !important;
}

/* Ensure date display divs inherit table cell styling */
.user-management-table .date-display {
    display: inline !important;
    background: inherit !important;
    color: inherit !important;
}

.dark-mode .user-management-table .date-display {
    background: inherit !important;
    color: inherit !important;
}

/* Nuclear option - remove all white backgrounds from table in dark mode */
.dark-mode .user-management-table td,
.dark-mode .user-management-table td *,
.dark-mode .user-management-table td div,
.dark-mode .user-management-table td span:not(.badge) {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure proper text colors in dark mode */
.dark-mode .user-management-table td {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

.dark-mode .user-management-table td .date-display {
    color: #e9ecef !important;
}

.dark-mode .user-management-table td .date-display.text-muted {
    color: #adb5bd !important;
}

/* Simple Modal Z-Index Fix */
.modal {
    z-index: 1060 !important;
}

.modal-backdrop {
    display: none !important;
}

/* Prevent Bootstrap from creating backdrop overlays */
.modal-backdrop.show {
    display: none !important;
}

.modal-backdrop.fade {
    display: none !important;
}

/* No backdrop at all - completely clean modals */

/* Ensure modal content is always interactive */
.modal-content {
    pointer-events: auto !important;
    z-index: 1060 !important;
}

/* Ensure form fields in modals are always interactive and bright */
.modal input, .modal select, .modal textarea, .modal button {
    pointer-events: auto !important;
    position: relative !important;
    z-index: 1061 !important;
    opacity: 1 !important;
}

/* Ensure modal buttons are bright and fully visible */
.modal .btn {
    opacity: 1 !important;
    filter: none !important;
    background-color: var(--bs-btn-bg) !important;
    border-color: var(--bs-btn-border-color) !important;
    color: var(--bs-btn-color) !important;
}

/* Specific button styling to ensure brightness */
.modal .btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #fff !important;
}

.modal .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
}

.modal .btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #fff !important;
}

/* Remove any dimming effects from modal elements */
.modal * {
    opacity: 1 !important;
    filter: none !important;
}

/* Ensure modal dialog is never dimmed */
.modal-dialog {
    opacity: 1 !important;
    filter: none !important;
}

/* Ensure modal content is bright */
.modal-content {
    opacity: 1 !important;
    filter: none !important;
    background-color: #fff !important;
}

/* Override any Bootstrap modal dimming */
.modal.show {
    opacity: 1 !important;
}

.modal.show .modal-dialog {
    opacity: 1 !important;
    transform: none !important;
}

/* Ensure buttons are never dimmed during modal transitions */
.modal .btn:not(:disabled) {
    opacity: 1 !important;
    filter: brightness(1) !important;
}

/* Modal Dark Mode Styling */
.dark-mode .modal-content {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #ffffff !important;
}

.dark-mode .modal-header {
    background-color: #1a1a1a !important;
    border-bottom: 1px solid #404040 !important;
    color: #ffffff !important;
}

.dark-mode .modal-title {
    color: #ffffff !important;
}

.dark-mode .modal-body {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
}

.dark-mode .modal-footer {
    background-color: #2d2d2d !important;
    border-top: 1px solid #404040 !important;
}

/* Modal form controls */
.dark-mode .modal .form-control,
.dark-mode .modal .form-select {
    background-color: #404040 !important;
    border: 1px solid #6c757d !important;
    color: #ffffff !important;
}

/* Dark mode optgroup styling */
.dark-mode .modal .form-select optgroup {
    background-color: #2d3748 !important;
    color: #ffc107 !important;
    font-weight: 600;
    font-style: normal;
}

.dark-mode .modal .form-select option {
    background-color: #404040 !important;
    color: #ffffff !important;
    padding: 0.5rem;
}

.dark-mode .modal .form-control:focus,
.dark-mode .modal .form-select:focus {
    background-color: #404040 !important;
    border-color: #ffc107 !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.dark-mode .modal .form-label {
    color: #ffffff !important;
}

/* Modal tables (for view modal) */
.dark-mode .modal .table {
    color: #ffffff !important;
}

.dark-mode .modal .table td,
.dark-mode .modal .table th {
    border-color: #404040 !important;
    color: #ffffff !important;
}

.dark-mode .modal .table-sm td,
.dark-mode .modal .table-sm th {
    background-color: transparent !important;
}

/* Modal badges */
.dark-mode .modal .badge {
    color: #ffffff !important;
}

/* Close button */
.dark-mode .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%) !important;
}

/* Form check styling */
.dark-mode .modal .form-check-label {
    color: #ffffff !important;
}

.dark-mode .modal .form-check-input {
    background-color: #404040 !important;
    border-color: #6c757d !important;
}

.dark-mode .modal .form-check-input:checked {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
}

/* Modal backdrop */
.dark-mode .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Modal text elements */
.dark-mode .modal h6 {
    color: #ffffff !important;
}

.dark-mode .modal p {
    color: #e9ecef !important;
}

.dark-mode .modal strong {
    color: #ffffff !important;
}

/* Modal icons */
.dark-mode .modal .fas,
.dark-mode .modal .fa {
    color: inherit !important;
}

/* Ensure all text in modals is readable */
.dark-mode .modal * {
    color: inherit !important;
}

.dark-mode .modal .text-success {
    color: #28a745 !important;
}

.dark-mode .modal .text-danger {
    color: #dc3545 !important;
}

.dark-mode .modal .text-warning {
    color: #ffc107 !important;
}

/* Modal input placeholders */
.dark-mode .modal .form-control::placeholder {
    color: #adb5bd !important;
    opacity: 0.8;
}

/* Ensure all table text is visible in both modes */
.user-management-table td {
    color: #212529 !important;
}

.dark-mode .user-management-table td {
    color: #ffffff !important;
}

.user-management-table td .text-muted {
    color: #6c757d !important;
}

.dark-mode .user-management-table td .text-muted {
    color: #adb5bd !important;
}

/* Action button styling for better visibility */
.dark-mode .btn-outline-info {
    color: #8B7355;
    border-color: #8B7355;
}

.dark-mode .btn-outline-info:hover {
    background-color: #8B7355;
    border-color: #8B7355;
    color: #ffffff;
}

.dark-mode .btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;
}

.dark-mode .btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000000;
}

.dark-mode .btn-outline-success {
    color: #28a745;
    border-color: #28a745;
}

.dark-mode .btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: #ffffff;
}

.user-management-table .avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
}

/* No users state */
.no-users-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
}

.dark-mode .no-users-state {
    color: #6c757d;
}

/* Force text color for user data in dark mode */
.dark-mode .user-management-table td,
.dark-mode .user-management-table td * {
    color: #ffffff !important;
}

.dark-mode .user-management-table .text-muted {
    color: #b3b3b3 !important;
}

.dark-mode .user-management-table .date-display {
    color: #e9ecef !important;
}

.dark-mode .user-management-table .id-column {
    color: #adb5bd !important;
}

/* Badge colors for dark mode */
.dark-mode .badge.bg-info {
    background-color: #8B7355 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.dark-mode .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

/* Button styling for dark mode */
.dark-mode .btn-outline-primary {
    color: #ffc107;
    border-color: #ffc107;
}

.dark-mode .btn-outline-primary:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000000;
}

.dark-mode .btn-outline-secondary {
    color: #adb5bd;
    border-color: #6c757d;
}

.dark-mode .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.dark-mode .btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.dark-mode .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

/* Icon colors in dark mode */
.dark-mode .text-success {
    color: #28a745 !important;
}

.dark-mode .text-warning {
    color: #ffc107 !important;
}

.dark-mode .text-danger {
    color: #dc3545 !important;
}

.dark-mode .text-primary {
    color: #ffc107 !important;
}

/* User ID Column - Make it more readable */
.table td:first-child {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d !important;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dark-mode .table td:first-child {
    color: #adb5bd !important;
}

/* Avatar styling */
.avatar-sm {
    width: 32px;
    height: 32px;
    background-color: #ffc107 !important;
}

/* Badges */
.badge.bg-info {
    background-color: var(--info) !important;
    color: white;
}

.badge.bg-success {
    background-color: var(--success) !important;
    color: white;
}

.badge.bg-warning {
    background-color: var(--warning) !important;
    color: var(--text-primary);
}

.badge.bg-danger {
    background-color: var(--danger) !important;
    color: white;
}

.badge.bg-secondary {
    background-color: var(--text-secondary) !important;
    color: var(--bg-primary);
}

/* Action Buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Button styling is handled by the main dark mode rules above */

/* Pagination */
.pagination .page-link {
    color: var(--brewery-dark-brown);
    border-color: var(--brewery-light-brown);
    background-color: white;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    color: white;
    background-color: var(--brewery-medium-brown);
    border-color: var(--brewery-medium-brown);
}

.pagination .page-item.active .page-link {
    background-color: var(--brewery-dark-brown);
    border-color: var(--brewery-dark-brown);
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Dark Mode Pagination */
.dark-mode .pagination .page-link {
    color: #e9ecef;
    background-color: #2d3748;
    border-color: #4a5568;
}

.dark-mode .pagination .page-link:hover {
    color: #fff;
    background-color: var(--brewery-medium-brown);
    border-color: var(--brewery-medium-brown);
}

.dark-mode .pagination .page-item.active .page-link {
    background-color: var(--brewery-amber);
    border-color: var(--brewery-amber);
    color: #1a202c;
    font-weight: 600;
}

.dark-mode .pagination .page-item.disabled .page-link {
    color: #718096;
    background-color: #1a202c;
    border-color: #2d3748;
}

.dark-mode .pagination .page-item.disabled .page-link:hover {
    color: #718096;
    background-color: #1a202c;
    border-color: #2d3748;
}

/* Pagination Focus States */
.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(212, 154, 107, 0.25);
    z-index: 3;
}

.dark-mode .pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Pagination Size Variants */
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.pagination-lg .page-link {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Pagination Spacing */
.pagination {
    margin-bottom: 0;
}

.pagination .page-item:not(:first-child) .page-link {
    margin-left: -1px;
}

/* Dark Mode Pagination Container */
.dark-mode nav[aria-label*="pagination"] {
    background-color: transparent;
}

/* Pagination Ellipsis */
.pagination .page-item.disabled .page-link {
    cursor: not-allowed;
}

.dark-mode .pagination .page-item.disabled .page-link {
    cursor: not-allowed;
}

/* Enhanced Form Select Styling */
.form-select optgroup {
    background-color: #f8f9fa;
    color: var(--brewery-dark-brown);
    font-weight: 600;
    font-style: normal;
    padding: 0.5rem;
}

.form-select option {
    padding: 0.5rem;
}

/* Dark mode form select enhancements */
.dark-mode .form-select optgroup {
    background-color: #2d3748 !important;
    color: #ffc107 !important;
    font-weight: 600;
    font-style: normal;
}

.dark-mode .form-select option {
    background-color: #404040 !important;
    color: #ffffff !important;
    padding: 0.5rem;
}

.dark-mode .form-select {
    background-color: #404040 !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

/* Form validation styling */
.form-select.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.dark-mode .form-select.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

/* Alert Styling - Brewery Theme */
.alert-info {
    background-color: var(--brewery-light-gray) !important;
    border: 2px solid var(--brewery-medium-brown) !important;
    color: var(--brewery-text-dark) !important;
    border-radius: 8px !important;
}

.alert-info .fas {
    color: var(--brewery-medium-brown) !important;
}

.alert-info h6 {
    color: var(--brewery-very-dark) !important;
    font-weight: 600 !important;
}

.alert-info p {
    color: var(--brewery-text-light) !important;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: var(--brewery-pale-yellow) !important;
    border: 2px solid #28a745 !important;
    color: #155724 !important;
}

.alert-warning {
    background-color: var(--brewery-pale-yellow) !important;
    border: 2px solid var(--brewery-amber) !important;
    color: var(--brewery-text-dark) !important;
}

/* Place Selection Notification Bar */
.place-info-alert {
    background: linear-gradient(135deg, var(--brewery-light-gray) 0%, var(--brewery-beige) 100%) !important;
    border: 2px solid var(--brewery-medium-brown) !important;
    border-left: 5px solid var(--brewery-amber) !important;
    color: var(--brewery-text-dark) !important;
    box-shadow: 0 2px 8px rgba(111, 76, 62, 0.15) !important;
    border-radius: 8px !important;
}

.place-info-alert .fas {
    color: var(--brewery-amber) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.place-info-alert h6 {
    color: var(--brewery-very-dark) !important;
    font-weight: 700 !important;
    margin-bottom: 0.25rem !important;
}

.place-info-alert p {
    color: var(--brewery-text-light) !important;
    font-weight: 500 !important;
    margin-bottom: 0 !important;
}

.place-info-alert strong {
    color: var(--brewery-dark-brown) !important;
    font-weight: 700 !important;
}

/* Modal Improvements - Light Mode */
.modal-header {
    background-color: #f8f9fa;
    color: #212529;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #212529;
}

.btn-close {
    filter: none;
}

/* Responsive Table */
.table-responsive {
    border-radius: 8px;
    border: 1px solid var(--brewery-light-brown);
}

/* Text Improvements */
.text-muted {
    color: var(--brewery-text-light) !important;
}

.fw-bold {
    color: var(--brewery-text-dark) !important;
}

/* Force table text visibility - FIXED */
.table tbody td {
    color: #212529 !important;
    background-color: #ffffff !important;
}

.dark-mode .table tbody td {
    color: #ffffff !important;
    background-color: #2d2d2d !important;
}

.table tbody td * {
    color: inherit !important;
}

.table tbody td .text-muted {
    color: #6c757d !important;
}

.dark-mode .table tbody td .text-muted {
    color: #adb5bd !important;
}

.table tbody td .user-display-name {
    color: #212529 !important;
    font-weight: 600;
}

.dark-mode .table tbody td .user-display-name {
    color: #ffffff !important;
}

.table tbody td .user-real-name {
    color: #6c757d !important;
    font-size: 0.85rem;
}

.dark-mode .table tbody td .user-real-name {
    color: #adb5bd !important;
}

/* Small text readability */
small {
    color: #6c757d;
    font-size: 0.85rem;
}

.dark-mode small {
    color: #adb5bd;
}

/* Filter section improvements */
.card-body .row.g-3 {
    align-items: end;
}

/* Icon colors */
.fas.fa-users, .fas.fa-filter, .fas.fa-list {
    color: var(--brewery-amber);
}

/* Hover effects for interactive elements */
.table tbody tr:hover .btn-group .btn {
    opacity: 1;
}

.btn-group .btn {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.btn-group:hover .btn {
    opacity: 1;
}

/* Loading states */
.table tbody tr.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Status indicators */
.fas.fa-check-circle {
    color: #28a745;
}

.fas.fa-exclamation-circle {
    color: var(--brewery-amber);
}

/* Improved spacing */
.container-fluid {
    padding: 1rem 2rem;
}

/* User Management Specific Improvements */
.user-management-table {
    font-size: 0.9rem;
}

.user-management-table .table td {
    padding: 0.75rem 0.5rem;
}

/* User display improvements */
.user-display-name {
    font-weight: 600;
    color: var(--brewery-text-dark) !important;
    font-size: 0.95rem;
}

.user-real-name {
    color: var(--brewery-text-light) !important;
    font-size: 0.8rem;
    font-style: italic;
}

/* Email column improvements */
.email-column {
    max-width: 200px;
    word-break: break-word;
    color: var(--brewery-text-dark) !important;
    background-color: white !important;
}

/* ID column improvements - make it more compact */
.id-column {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    color: var(--brewery-text-light) !important;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
    background-color: white !important;
}

.id-column:hover {
    overflow: visible;
    white-space: normal;
    word-break: break-all;
    background-color: var(--brewery-beige) !important;
    position: relative;
    z-index: 10;
    color: var(--brewery-text-dark) !important;
}

/* Date formatting improvements */
.date-display {
    font-size: 0.85rem;
    color: var(--brewery-text-light) !important;
    white-space: nowrap;
    background-color: white !important;
}

/* Statistics cards row spacing */
.stats-row {
    margin-bottom: 2rem;
}

.stats-row .card {
    height: 100%;
    min-height: 100px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
}

.stats-row .card .card-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.5rem;
}

.stats-row .card .card-text {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
}

.stats-row .card .card-body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Filter form improvements */
.filter-form {
    background-color: rgba(245, 245, 220, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
}

/* Remove ugly blue focus colors and replace with brewery theme */
.form-control:focus,
.form-select:focus,
input:focus,
select:focus,
textarea:focus,
button:focus {
    border-color: var(--brewery-medium-brown) !important;
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25) !important;
    outline: none !important;
}

/* Override Bootstrap's blue colors */
.btn-primary:focus,
.btn-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(111, 76, 62, 0.5) !important;
}

/* Improve input placeholder text */
.form-control::placeholder,
.form-select::placeholder {
    color: #6c757d;
    opacity: 0.8;
    font-style: italic;
}

/* Better form row spacing */
.row.g-3 > * {
    margin-bottom: 1rem;
}

/* Search input specific styling */
input[type="text"],
input[type="email"],
input[type="search"] {
    background-color: white !important;
    color: #333333 !important;
    border: 2px solid var(--brewery-light-brown) !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="search"]:focus {
    background-color: white !important;
    color: #333333 !important;
    border-color: var(--brewery-medium-brown) !important;
}

/* Enhanced dropdown styling for better readability */
select.form-select {
    background-color: white !important;
    color: #333333 !important;
    font-weight: 500;
    border: 2px solid var(--brewery-light-brown) !important;
}

select.form-select:focus {
    background-color: white !important;
    color: #333333 !important;
    border-color: var(--brewery-medium-brown) !important;
    box-shadow: 0 0 0 0.2rem rgba(214, 154, 107, 0.25) !important;
}

/* Dropdown options styling - browser specific */
select.form-select option {
    background-color: #f8f9fa !important;
    color: #333333 !important;
    padding: 8px 12px;
    font-weight: 500;
    border: none;
}

select.form-select option:hover,
select.form-select option:focus {
    background-color: var(--brewery-light-brown) !important;
    color: #333333 !important;
}

select.form-select option:checked,
select.form-select option[selected] {
    background-color: var(--brewery-medium-brown) !important;
    color: white !important;
    font-weight: 600;
}

/* Remove any remaining blue highlights */
*:focus {
    outline: none !important;
}

/* Ensure all form elements have consistent styling */
.form-control,
.form-select,
input,
select,
textarea {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

/* Primary and Info Elements */
.text-primary {
    color: var(--brand-primary) !important;
}

.btn-outline-primary {
    color: var(--brand-primary);
    border-color: var(--brand-primary);
    background-color: transparent;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background-color: var(--brand-primary);
    border-color: var(--brand-primary);
    color: white;
}

.btn-outline-info {
    color: var(--info);
    border-color: var(--info);
    background-color: transparent;
}

.btn-outline-info:hover,
.btn-outline-info:focus,
.btn-outline-info:active {
    background-color: var(--info);
    border-color: var(--info);
    color: white;
}

/* Links */
a {
    color: var(--brand-primary);
    text-decoration: none;
}

a:hover,
a:focus {
    color: var(--brand-secondary);
    text-decoration: underline;
}

/* Status Colors */
.text-success {
    color: var(--success) !important;
}

.text-danger {
    color: var(--danger) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Table header improvements */
.table-header-actions {
    background-color: var(--brewery-dark-brown);
    color: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
    border-bottom: 2px solid var(--brewery-medium-brown);
}

.table-header-actions h5 {
    color: white;
    margin: 0;
}

.table-header-actions .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* No users found state */
.no-users-state {
    padding: 3rem 1rem;
    text-align: center;
    color: var(--brewery-text-light);
}

.no-users-state .fa-users {
    color: var(--brewery-medium-brown);
    opacity: 0.5;
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }

    .table td:first-child {
        max-width: 80px;
    }

    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    .user-management-table {
        font-size: 0.8rem;
    }

    .email-column {
        max-width: 150px;
    }

    .id-column {
        max-width: 60px;
    }
}
