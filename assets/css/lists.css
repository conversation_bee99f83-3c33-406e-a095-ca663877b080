/* User Lists Page Styles */

.section-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

/* List Cards */
.list-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    overflow: hidden;
}

.list-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.default-list {
    border-left: 4px solid #ffc107;
}

.custom-list {
    border-left: 4px solid #17a2b8;
}

.list-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    position: relative;
}

.list-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin-right: 1rem;
    flex-shrink: 0;
}

.default-list .list-icon {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.custom-list .list-icon {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.list-info {
    flex-grow: 1;
    min-width: 0;
}

.list-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.list-count {
    font-size: 0.9rem;
    margin: 0;
}

.list-menu {
    position: absolute;
    top: 0;
    right: 0;
}

.list-description {
    margin-bottom: 1rem;
}

.list-description p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.list-meta {
    margin-bottom: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.list-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.list-actions .btn {
    flex: 1;
    min-width: 80px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    opacity: 0.5;
    margin-bottom: 1.5rem;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
}

.btn-outline-info {
    border: 2px solid #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background: #17a2b8;
    border-color: #17a2b8;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.3rem 0.5rem;
    font-size: 0.75rem;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
}

.modal-footer {
    border: none;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* Dropdown Menu */
.dropdown-menu {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.text-danger:hover {
    background-color: #f8d7da;
    color: #721c24 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .list-header {
        flex-direction: column;
        text-align: center;
    }
    
    .list-icon {
        margin-right: 0;
        margin-bottom: 1rem;
        align-self: center;
    }
    
    .list-menu {
        position: static;
        margin-top: 1rem;
        align-self: center;
    }
    
    .list-actions {
        flex-direction: column;
    }
    
    .list-actions .btn {
        width: 100%;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .list-icon {
        width: 50px;
        height: 50px;
    }
    
    .list-icon i {
        font-size: 1.5rem !important;
    }
    
    .list-name {
        font-size: 1rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.list-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.list-card:nth-child(1) { animation-delay: 0.1s; }
.list-card:nth-child(2) { animation-delay: 0.2s; }
.list-card:nth-child(3) { animation-delay: 0.3s; }
.list-card:nth-child(4) { animation-delay: 0.4s; }
.list-card:nth-child(5) { animation-delay: 0.5s; }
.list-card:nth-child(6) { animation-delay: 0.6s; }

/* Hover Effects */
.list-card:hover .list-name {
    color: #007bff;
}

.default-list:hover {
    border-left-color: #e0a800;
}

.custom-list:hover {
    border-left-color: #138496;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Special Effects for Default Lists */
.default-list .list-icon i.text-warning {
    animation: pulse 2s ease-in-out infinite;
}

.default-list .list-icon i.text-danger {
    animation: heartbeat 1.5s ease-in-out infinite;
}

.default-list .list-icon i.text-success {
    animation: bounce 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    25%, 75% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

/* Focus States */
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* List Detail Page Styles */
.list-header-info {
    flex-grow: 1;
}

.list-meta {
    margin-top: 1rem;
}

.list-meta .badge {
    font-size: 0.8rem;
}

.list-actions {
    flex-shrink: 0;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Beer Cards in Lists */
.beer-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.beer-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.beer-image {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.beer-thumb {
    max-width: 50px;
    max-height: 50px;
    object-fit: contain;
    border-radius: 6px;
}

.beer-thumb-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.beer-info {
    min-width: 0;
}

.beer-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.beer-name a {
    color: inherit;
    transition: color 0.3s ease;
}

.beer-name a:hover {
    color: #007bff;
}

.brewery-name {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.beer-style {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.beer-stats {
    margin-bottom: 0.5rem;
}

.beer-stats .badge {
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
}

.list-notes {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.item-meta {
    border-top: 1px solid #e9ecef;
    padding-top: 0.5rem;
}

.item-actions {
    display: flex;
    gap: 0.25rem;
}

/* Print Styles */
@media print {
    .list-actions,
    .list-menu,
    .btn,
    .item-actions {
        display: none !important;
    }

    .list-card,
    .beer-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    .modal {
        display: none !important;
    }
}

/* Photo Gallery Styles */
.photo-gallery {
    margin-bottom: 2rem;
}

.gallery-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
}

.gallery-title {
    color: #2c3e50;
    font-weight: 600;
}

/* Empty Gallery */
.empty-gallery {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

/* Photo Grid */
.photo-grid {
    margin-top: 1rem;
}

.photo-item {
    margin-bottom: 1rem;
}

.photo-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    background: white;
}

.photo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.photo-image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.photo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.photo-image:hover {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
}

.photo-card:hover .photo-overlay {
    opacity: 1;
}

.photo-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.photo-actions .btn {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.photo-likes {
    background: rgba(255,255,255,0.9);
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.photo-info {
    padding: 1rem;
}

.photo-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.photo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Lightbox */
.lightbox-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.5);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.lightbox-nav:hover {
    background: rgba(0,0,0,0.8);
    color: white;
}

.lightbox-prev {
    left: 1rem;
}

.lightbox-next {
    right: 1rem;
}

.lightbox-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 100%);
    color: white;
    padding: 2rem 1rem 1rem;
}

/* Photo Upload Form */
.photo-upload-form {
    margin-bottom: 2rem;
}

.upload-area {
    margin-bottom: 1rem;
}

.upload-dropzone {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.upload-dropzone:hover {
    border-color: #007bff;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.upload-dropzone.dragover {
    border-color: #007bff;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.file-preview {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.preview-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: rgba(220, 53, 69, 0.9);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    cursor: pointer;
}

.upload-progress .progress {
    height: 8px;
    border-radius: 4px;
}

.upload-progress .progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .photo-grid[data-columns="3"] .col-lg-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .photo-grid[data-columns="4"] .col-lg-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .lightbox-nav {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .lightbox-prev {
        left: 0.5rem;
    }

    .lightbox-next {
        right: 0.5rem;
    }

    .upload-dropzone {
        padding: 2rem 1rem;
    }

    .preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }
}

@media (max-width: 576px) {
    .photo-grid .col-lg-4,
    .photo-grid .col-lg-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .photo-info {
        padding: 0.75rem;
    }

    .photo-title {
        font-size: 0.85rem;
    }

    .photo-meta {
        font-size: 0.75rem;
    }
}

/* Photo Statistics */
.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Navigation Pills */
.nav-pills .nav-link {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.nav-pills .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Pagination */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
    color: #007bff;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* Photo Management Specific */
.photo-card .photo-actions .btn-danger {
    background: rgba(220, 53, 69, 0.9);
    border: none;
    color: white;
}

.photo-card .photo-actions .btn-danger:hover {
    background: rgba(220, 53, 69, 1);
}

/* Photo Type Badges */
.photo-meta .text-muted {
    display: inline-flex;
    align-items: center;
}

.photo-meta .text-muted i {
    width: 12px;
    text-align: center;
}

/* Enhanced Photo Grid for Management */
.photo-grid[data-columns="4"] .col-lg-3 {
    transition: all 0.3s ease;
}

.photo-grid[data-columns="4"] .col-lg-3:hover {
    transform: scale(1.02);
    z-index: 10;
}

/* Loading States */
.photo-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #6c757d;
}

.photo-loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Error States */
.photo-error {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 1rem 0;
}

/* Success States */
.photo-success {
    text-align: center;
    padding: 1rem;
    color: #155724;
    background: #d4edda;
    border-radius: 8px;
    margin: 1rem 0;
}
