/* Social Features Styles */

/* User Cards */
.user-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.user-avatar img {
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.avatar-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
}

/* Feed Items */
.feed-item {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.feed-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.beer-thumb {
    max-width: 80px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.beer-thumb-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.beer-thumb-small {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
}

.beer-thumb-small-placeholder {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

/* Check-in Content */
.checkin-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.checkin-header h6 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.checkin-comment {
    background: #3B2A2A;
    border-left: 3px solid #FFC107;
    padding: 0.75rem;
    border-radius: 0 6px 6px 0;
    margin: 0.5rem 0;
    font-style: italic;
    color: #F5F5DC;
}

/* Activity Content */
.activity-content {
    padding: 0.5rem 0;
}

.activity-content p {
    font-size: 1rem;
    line-height: 1.5;
}

/* Social Actions */
.social-actions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
}

.social-actions .btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.social-actions .btn:hover {
    transform: translateY(-1px);
}

.like-btn.btn-primary {
    background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
    border: none;
    color: white;
}

.like-btn.btn-outline-primary {
    border-color: #e91e63;
    color: #e91e63;
}

.like-btn.btn-outline-primary:hover {
    background: #e91e63;
    border-color: #e91e63;
}

/* Rating Stars - White inactive, Gold when active */
.rating .fas.fa-star.text-warning,
.rating .fas.fa-star.active {
    color: #FFC107 !important; /* Always gold when selected */
}

.rating .fas.fa-star.text-muted,
.rating .fas.fa-star {
    color: #F5F5DC !important; /* White/beige when inactive */
}

.rating .fas.fa-star:hover {
    color: #FFC107 !important; /* Gold on hover */
}

/* Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.search-result-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.beer-search {
    position: relative;
}

/* Follow Button States */
.follow-btn {
    transition: all 0.3s ease;
    min-width: 100px;
}

.follow-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    color: #F5F5DC;
    border-radius: 12px 12px 0 0 !important;
    border: none;
}

.card-body {
    padding: 1.5rem;
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.4rem 0.6rem;
}

.bg-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border: none;
    color: #3B2A2A;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%);
    color: #F5F5DC;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.btn-outline-primary {
    border: 2px solid #FFC107;
    color: #FFC107;
    background: transparent;
    font-weight: 600;
}

.btn-outline-primary:hover {
    background: #FFC107;
    border-color: #FFC107;
    color: #3B2A2A;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-outline-success {
    border: 2px solid #28a745;
    color: #28a745;
}

.btn-outline-success:hover {
    background: #28a745;
    border-color: #28a745;
    transform: translateY(-1px);
}

/* Pagination */
.pagination .page-link {
    border-radius: 8px;
    border: 1px solid #D69A6B;
    color: #FFC107;
    background: #3B2A2A;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #FFC107;
    border-color: #FFC107;
    color: #3B2A2A;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border-color: #FFC107;
    color: #3B2A2A;
}

/* Form Elements */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #FFC107;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
    
    .d-flex.gap-3 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
    
    .social-actions .d-flex {
        flex-direction: row;
        justify-content: space-around;
    }
    
    .social-actions .btn {
        width: auto;
        margin-bottom: 0;
        flex: 1;
    }
    
    .beer-thumb {
        max-width: 60px;
    }
    
    .beer-thumb-placeholder {
        width: 60px;
        height: 60px;
    }
    
    .checkin-content .row {
        text-align: center;
    }
    
    .checkin-content .col-md-2 {
        margin-bottom: 1rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feed-item {
    animation: fadeInUp 0.6s ease-out;
}

.user-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.feed-item:nth-child(1) { animation-delay: 0.1s; }
.feed-item:nth-child(2) { animation-delay: 0.2s; }
.feed-item:nth-child(3) { animation-delay: 0.3s; }
.feed-item:nth-child(4) { animation-delay: 0.4s; }
.feed-item:nth-child(5) { animation-delay: 0.5s; }

.user-card:nth-child(1) { animation-delay: 0.1s; }
.user-card:nth-child(2) { animation-delay: 0.2s; }
.user-card:nth-child(3) { animation-delay: 0.3s; }

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.text-center.py-5 {
    padding: 4rem 2rem !important;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Enhanced Quick Actions for Better Readability */
.quick-actions .card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.quick-actions .card-body {
    padding: 1.5rem;
}

.quick-actions .btn,
.card .row.text-center .btn {
    min-height: 75px;
    padding: 16px 12px;
    font-weight: 600;
    border-width: 2px;
    border-radius: 12px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.quick-actions .btn::before,
.card .row.text-center .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quick-actions .btn:hover::before,
.card .row.text-center .btn:hover::before {
    opacity: 1;
}

.quick-actions .btn i,
.card .row.text-center .btn i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
}

.quick-actions .btn small,
.card .row.text-center .btn small {
    font-size: 0.875rem;
    font-weight: 600;
    color: inherit;
    line-height: 1.2;
    text-align: center;
}

/* Enhanced Contrast for Outline Buttons */
.quick-actions .btn-outline-primary,
.card .row.text-center .btn-outline-primary {
    color: #FFC107;
    border-color: #FFC107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(214, 154, 107, 0.05) 100%);
}

.quick-actions .btn-outline-primary:hover,
.quick-actions .btn-outline-primary:focus,
.card .row.text-center .btn-outline-primary:hover,
.card .row.text-center .btn-outline-primary:focus {
    color: #3B2A2A;
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border-color: #FFC107;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.quick-actions .btn-outline-success,
.card .row.text-center .btn-outline-success {
    color: #155724;
    border-color: #155724;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(21, 87, 36, 0.05) 100%);
}

.quick-actions .btn-outline-success:hover,
.quick-actions .btn-outline-success:focus,
.card .row.text-center .btn-outline-success:hover,
.card .row.text-center .btn-outline-success:focus {
    color: #ffffff;
    background: linear-gradient(135deg, #155724 0%, #0f3f1a 100%);
    border-color: #155724;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(21, 87, 36, 0.4);
}

.quick-actions .btn-outline-info,
.card .row.text-center .btn-outline-info {
    color: #0c5460;
    border-color: #0c5460;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.08) 0%, rgba(12, 84, 96, 0.05) 100%);
}

.quick-actions .btn-outline-info:hover,
.quick-actions .btn-outline-info:focus,
.card .row.text-center .btn-outline-info:hover,
.card .row.text-center .btn-outline-info:focus {
    color: #ffffff;
    background: linear-gradient(135deg, #0c5460 0%, #083238 100%);
    border-color: #0c5460;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(12, 84, 96, 0.4);
}

.quick-actions .btn-outline-secondary,
.card .row.text-center .btn-outline-secondary {
    color: #495057;
    border-color: #495057;
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.08) 0%, rgba(73, 80, 87, 0.05) 100%);
}

.quick-actions .btn-outline-secondary:hover,
.quick-actions .btn-outline-secondary:focus,
.card .row.text-center .btn-outline-secondary:hover,
.card .row.text-center .btn-outline-secondary:focus {
    color: #ffffff;
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    border-color: #495057;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(73, 80, 87, 0.4);
}

.quick-actions .btn-outline-warning,
.card .row.text-center .btn-outline-warning {
    color: #856404;
    border-color: #856404;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.08) 0%, rgba(133, 100, 4, 0.05) 100%);
}

.quick-actions .btn-outline-warning:hover,
.quick-actions .btn-outline-warning:focus,
.card .row.text-center .btn-outline-warning:hover,
.card .row.text-center .btn-outline-warning:focus {
    color: #ffffff;
    background: linear-gradient(135deg, #856404 0%, #533f03 100%);
    border-color: #856404;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(133, 100, 4, 0.4);
}

/* Activity Type Icons */
.activity-content i.fa-star {
    color: #ffc107;
}

.activity-content i.fa-user-plus {
    color: #28a745;
}

.activity-content i.fa-user-check {
    color: #FFC107;
}

/* Additional Readability Improvements for Quick Actions */
.quick-actions .btn small,
.card .row.text-center .btn small {
    text-shadow: none;
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Ensure text is always readable */
.quick-actions .btn-outline-primary small,
.card .row.text-center .btn-outline-primary small {
    color: #FFC107 !important;
}

.quick-actions .btn-outline-success small,
.card .row.text-center .btn-outline-success small {
    color: #155724 !important;
}

.quick-actions .btn-outline-info small,
.card .row.text-center .btn-outline-info small {
    color: #0c5460 !important;
}

.quick-actions .btn-outline-secondary small,
.card .row.text-center .btn-outline-secondary small {
    color: #495057 !important;
}

.quick-actions .btn-outline-warning small,
.card .row.text-center .btn-outline-warning small {
    color: #856404 !important;
}

/* Hover state text colors */
.quick-actions .btn-outline-primary:hover small,
.quick-actions .btn-outline-success:hover small,
.quick-actions .btn-outline-info:hover small,
.quick-actions .btn-outline-secondary:hover small,
.quick-actions .btn-outline-warning:hover small,
.card .row.text-center .btn-outline-primary:hover small,
.card .row.text-center .btn-outline-success:hover small,
.card .row.text-center .btn-outline-info:hover small,
.card .row.text-center .btn-outline-secondary:hover small,
.card .row.text-center .btn-outline-warning:hover small {
    color: #ffffff !important;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .quick-actions .btn-outline-primary,
    .card .row.text-center .btn-outline-primary {
        color: #000000;
        border-color: #000000;
        background-color: #ffffff;
    }

    .quick-actions .btn-outline-primary:hover,
    .card .row.text-center .btn-outline-primary:hover {
        color: #ffffff;
        background-color: #000000;
        border-color: #000000;
    }

    .action-card {
        border-color: #000000;
        background-color: #ffffff;
    }

    .action-content h5,
    .action-content p {
        color: #000000;
    }
}

.activity-content i.fa-activity {
    color: #17a2b8;
}
