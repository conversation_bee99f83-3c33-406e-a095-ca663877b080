/* Menu Preview CSS - Customer View Styling */

/* Brewery Header */
.brewery-header-preview {
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.brewery-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.brewery-description {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.brewery-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.brewery-type {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

.brewery-location {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

/* Menu Sections */
.menu-section {
    background-color: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    color: var(--text-primary);
}

.dark-mode .menu-section {
    background-color: var(--bg-secondary);
}

.section-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-primary);
}

.category-title {
    color: var(--brand-primary);
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-primary);
}

/* Menu Item Cards */
.menu-item-preview {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
}

.menu-item-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .menu-item-preview {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
}

.dark-mode .menu-item-preview:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.item-name-price {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.item-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brand-primary);
}

.item-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: flex-start;
}

.item-description {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.item-details {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    font-size: 0.9rem;
}

.detail-item {
    color: var(--text-secondary);
}

.detail-item strong {
    color: var(--text-primary);
}

/* Empty State */
.empty-menu {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-menu i {
    opacity: 0.6;
}

.empty-menu h5 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* Navigation Tabs */
.nav-tabs {
    border-bottom: 2px solid var(--border-primary);
    background-color: var(--bg-secondary);
    border-radius: 8px 8px 0 0;
    padding: 0.5rem;
}

.dark-mode .nav-tabs {
    background-color: var(--bg-tertiary);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-tabs .nav-link.active {
    background-color: var(--brand-primary);
    color: white;
    border-color: var(--brand-primary);
}

.nav-tabs .nav-link .badge {
    font-size: 0.7rem;
}

/* Tab Content */
.tab-content {
    background-color: var(--bg-secondary);
    border-radius: 0 0 8px 8px;
    padding: 0;
    min-height: 400px;
}

.dark-mode .tab-content {
    background-color: var(--bg-secondary);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
}

.badge.bg-warning {
    background-color: var(--warning) !important;
    color: #000000 !important;
}

.badge.bg-success {
    background-color: var(--success) !important;
    color: white !important;
}

.badge.bg-info {
    background-color: var(--info) !important;
    color: white !important;
}

.badge.bg-primary {
    background-color: var(--brand-primary) !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .brewery-header-preview {
        padding: 1.5rem;
    }
    
    .brewery-name {
        font-size: 1.5rem;
    }
    
    .brewery-description {
        font-size: 1rem;
    }
    
    .menu-section {
        padding: 1.5rem;
    }
    
    .item-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .item-badges {
        margin-top: 0.5rem;
    }
    
    .item-details {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 576px) {
    .brewery-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .menu-item-preview {
        padding: 1rem;
    }
    
    .item-name {
        font-size: 1.1rem;
    }
    
    .empty-menu {
        padding: 2rem 1rem;
    }
}

/* Animation */
.menu-item-preview {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-item-preview:nth-child(1) { animation-delay: 0.1s; }
.menu-item-preview:nth-child(2) { animation-delay: 0.2s; }
.menu-item-preview:nth-child(3) { animation-delay: 0.3s; }
.menu-item-preview:nth-child(4) { animation-delay: 0.4s; }
.menu-item-preview:nth-child(5) { animation-delay: 0.5s; }
.menu-item-preview:nth-child(6) { animation-delay: 0.6s; }
