/* Places Profile - Dark Mode Support */

/* Ensure navigation is always visible */
.navbar {
    position: relative !important;
    z-index: 1030 !important;
    background-color: var(--bg-primary) !important;
}

.dark-mode .navbar {
    background-color: #000000 !important;
}

/* Place Hero Section with Brewery Imagery */
.place-hero {
    position: relative;
    overflow: hidden;
    margin-top: 0;
    background: linear-gradient(
        135deg,
        rgba(139, 69, 19, 0.9) 0%,     /* <PERSON> brown */
        rgba(101, 67, 33, 0.8) 25%,    /* Dark wood */
        rgba(160, 82, 45, 0.7) 50%,    /* Saddle brown */
        rgba(205, 133, 63, 0.6) 75%,   /* Peru/barrel */
        rgba(222, 184, 135, 0.5) 100%  /* Burlywood */
    ),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><pattern id="wood" patternUnits="userSpaceOnUse" width="100" height="100"><rect width="100" height="100" fill="%23654321"/><path d="M0,20 Q50,10 100,20 L100,40 Q50,30 0,40 Z" fill="%238B4513" opacity="0.7"/><path d="M0,60 Q50,50 100,60 L100,80 Q50,70 0,80 Z" fill="%23A0522D" opacity="0.5"/></pattern><pattern id="barrel" patternUnits="userSpaceOnUse" width="80" height="120"><rect width="80" height="120" fill="%23654321"/><rect x="0" y="10" width="80" height="8" fill="%232F1B14"/><rect x="0" y="30" width="80" height="8" fill="%232F1B14"/><rect x="0" y="50" width="80" height="8" fill="%232F1B14"/><rect x="0" y="70" width="80" height="8" fill="%232F1B14"/><rect x="0" y="90" width="80" height="8" fill="%232F1B14"/></pattern></defs><rect width="1200" height="600" fill="url(%23wood)"/><ellipse cx="200" cy="300" rx="60" ry="80" fill="url(%23barrel)" opacity="0.6"/><ellipse cx="400" cy="250" rx="50" ry="70" fill="url(%23barrel)" opacity="0.4"/><ellipse cx="800" cy="320" rx="55" ry="75" fill="url(%23barrel)" opacity="0.5"/><ellipse cx="1000" cy="280" rx="45" ry="65" fill="url(%23barrel)" opacity="0.3"/></svg>') center/cover;
}

/* Brewery-themed hero background */
.brewery-hero {
    background:
        /* Dark overlay for text readability */
        linear-gradient(
            135deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(139, 69, 19, 0.6) 25%,
            rgba(101, 67, 33, 0.5) 50%,
            rgba(0, 0, 0, 0.8) 100%
        ),
        /* Brewery texture pattern */
        radial-gradient(circle at 20% 80%, rgba(222, 184, 135, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(160, 82, 45, 0.3) 0%, transparent 50%),
        /* Wood grain effect */
        linear-gradient(90deg,
            #654321 0%,
            #8B4513 25%,
            #A0522D 50%,
            #CD853F 75%,
            #DEB887 100%
        );
    background-size: cover, 300px 300px, 400px 400px, 100% 100%;
    background-position: center, 20% 80%, 80% 20%, center;
    background-attachment: fixed;
    position: relative;
}

/* Add barrel and tap elements */
.brewery-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        /* Beer taps */
        radial-gradient(ellipse 8px 40px at 15% 30%, #C0C0C0 0%, #808080 50%, transparent 50%),
        radial-gradient(ellipse 8px 40px at 25% 35%, #C0C0C0 0%, #808080 50%, transparent 50%),
        radial-gradient(ellipse 8px 40px at 35% 32%, #C0C0C0 0%, #808080 50%, transparent 50%),
        /* Barrels */
        radial-gradient(ellipse 60px 80px at 80% 70%, #654321 0%, #8B4513 30%, transparent 70%),
        radial-gradient(ellipse 45px 65px at 85% 75%, #654321 0%, #8B4513 30%, transparent 70%),
        /* Wood planks */
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 2px,
            rgba(139, 69, 19, 0.1) 2px,
            rgba(139, 69, 19, 0.1) 4px
        );
    opacity: 0.6;
    z-index: 1;
}

.place-hero .hero-image {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.place-hero .hero-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 1;
}

.place-hero .place-info {
    position: relative;
    z-index: 2;
}

.place-badges .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.place-hero h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
}

.rating-stars {
    color: var(--brand-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.place-actions .btn {
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.place-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Navigation Tabs */
.nav-tabs {
    border-bottom: 2px solid var(--border-primary);
    background: var(--bg-secondary);
    border-radius: 12px 12px 0 0;
    padding: 0.5rem;
}

.dark-mode .nav-tabs {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-primary);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 179, 71, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-tabs .nav-link:hover::before {
    left: 100%;
}

.nav-tabs .nav-link:hover {
    color: var(--beer-gold);
    background: var(--bg-tertiary);
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--beer-gold) 0%, var(--beer-amber) 100%);
    color: var(--bg-primary);
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(255, 179, 71, 0.3);
}

/* Tab Content */
.tab-content {
    background: var(--bg-secondary);
    border-radius: 0 0 12px 12px;
    padding: 2rem;
    min-height: 400px;
    color: var(--text-primary);
}

.dark-mode .tab-content {
    background: var(--bg-secondary);
}

/* Beer Cards - Brewery Themed */
.beer-card {
    background: linear-gradient(145deg,
        var(--bg-secondary) 0%,
        rgba(139, 69, 19, 0.1) 50%,
        rgba(255, 179, 71, 0.05) 100%
    );
    border: 2px solid var(--border-secondary);
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

/* Add subtle wood grain texture to beer cards */
.beer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 1px,
        rgba(139, 69, 19, 0.03) 1px,
        rgba(139, 69, 19, 0.03) 2px
    );
    pointer-events: none;
    z-index: 1;
}

.beer-card .card-body {
    position: relative;
    z-index: 2;
}

.beer-card:hover {
    border-color: var(--beer-gold);
    box-shadow: 0 8px 25px rgba(255, 179, 71, 0.2);
    transform: translateY(-3px);
}

.beer-card .card-title {
    color: var(--beer-gold);
    font-weight: 600;
    font-size: 1.1rem;
}

.beer-card .card-text {
    color: var(--text-secondary);
}

.beer-rating .rating-stars {
    font-size: 0.9rem;
}

/* Food Menu Styling */
.food-category {
    margin-bottom: 2.5rem;
}

.category-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.3rem;
    border-bottom: 2px solid var(--beer-gold) !important;
    padding-bottom: 0.5rem;
}

.food-item {
    background: linear-gradient(145deg,
        var(--bg-secondary) 0%,
        rgba(160, 82, 45, 0.08) 50%,
        rgba(255, 193, 7, 0.05) 100%
    );
    border: 2px solid var(--border-secondary) !important;
    transition: all 0.3s ease;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

/* Add subtle texture to food cards */
.food-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(222, 184, 135, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(160, 82, 45, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.food-item .card-body {
    position: relative;
    z-index: 2;
}

.food-item:hover {
    border-color: var(--warning-dark) !important;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
    transform: translateY(-3px);
}

.food-item .card-title {
    color: var(--warning-dark);
    font-weight: 600;
}

.price-tag {
    background: linear-gradient(135deg, var(--beer-gold) 0%, var(--beer-amber) 100%) !important;
    color: var(--bg-primary) !important;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

.food-actions .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

.food-info {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-accent) 100%) !important;
    border: 1px solid var(--warning-dark) !important;
    border-radius: 12px;
}

/* Deal Cards */
.deal-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(13, 110, 253, 0.05) 100%);
    border: 1px solid var(--border-secondary) !important;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.deal-card:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.2);
    transform: translateY(-3px);
}

.deal-card .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%) !important;
    border-bottom: none !important;
    color: white !important;
}

.qr-section {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
}

.qr-code-placeholder {
    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--border-accent) 100%) !important;
    color: var(--text-secondary) !important;
}

/* Sidebar Cards - Brewery Themed */
.card.mb-4 {
    background: linear-gradient(145deg,
        var(--bg-secondary) 0%,
        rgba(139, 69, 19, 0.05) 100%
    );
    border: 2px solid var(--border-secondary);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

/* Add subtle brewery texture to sidebar cards */
.card.mb-4::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 20px,
            rgba(139, 69, 19, 0.01) 20px,
            rgba(139, 69, 19, 0.01) 22px
        );
    pointer-events: none;
    z-index: 1;
}

.card.mb-4 .card-header,
.card.mb-4 .card-body {
    position: relative;
    z-index: 2;
}

.card.border-warning {
    border: 2px solid var(--warning-dark) !important;
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 193, 7, 0.05) 100%);
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--warning-dark) 0%, #f39c12 100%) !important;
    color: var(--bg-primary) !important;
}

/* Contact Info Icons */
.contact-item i {
    color: var(--beer-gold);
    width: 20px;
    text-align: center;
}

.contact-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: var(--beer-gold);
}

/* Hours Display */
.hours-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-secondary);
}

.hours-item:last-child {
    border-bottom: none;
}

/* Map Placeholder */
.map-placeholder {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-accent) 100%) !important;
    border-radius: 8px;
    color: var(--text-muted);
}

.map-placeholder i {
    color: var(--beer-gold);
}

/* Photo Gallery */
.photo-item {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.photo-thumbnail {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.photo-item:hover .photo-thumbnail {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-overlay i {
    color: var(--beer-gold);
    font-size: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .place-hero .hero-image {
        height: 300px !important;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .nav-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .food-item .card-body {
        padding: 1rem;
    }
    
    .price-tag {
        font-size: 0.8rem;
    }
}

/* Distance Pill Styling */
.place-distance .badge {
    background-color: rgba(0, 0, 0, 0.85) !important;
    color: #ffffff !important;
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.place-distance .badge:hover {
    background-color: rgba(0, 0, 0, 0.9) !important;
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* Ensure distance text is always white regardless of theme */
.badge.bg-dark {
    background-color: rgba(0, 0, 0, 0.8) !important;
    color: #ffffff !important;
}

/* Place badge styling for consistency */
.place-badge .badge {
    background-color: rgba(255, 193, 7, 0.9) !important;
    color: #000000 !important;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 193, 7, 0.3);
    text-shadow: none;
}

.place-badge .badge:hover {
    background-color: rgba(255, 193, 7, 1) !important;
    transform: scale(1.05);
    transition: all 0.2s ease;
}
