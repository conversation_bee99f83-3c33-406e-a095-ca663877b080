/* Beer Discovery Page - Brewery Theme */

/* Page Background */
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

.container {
    background-color: #3B2A2A !important;
}

/* Page Header */
.display-5 {
    color: #F5F5DC !important;
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(59, 42, 42, 0.3);
}

.lead {
    color: #D69A6B !important;
}

/* Beer Cards - Industry Standard */
.beer-card {
    background-color: #6F4C3E !important;
    border: 1px solid #D69A6B !important;
    border-radius: 0.5rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.beer-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.3) !important;
    border-color: #FFC107 !important;
}

.beer-image {
    height: 180px; /* Reduced for better proportions */
    object-fit: cover;
    transition: transform 0.3s ease;
}

.beer-card:hover .beer-image {
    transform: scale(1.03);
}

.beer-image-placeholder {
    height: 180px;
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border-bottom: 1px solid #D69A6B;
    color: #D69A6B;
}

/* Card Content */
.beer-card .card-body {
    padding: 1rem;
    background-color: #6F4C3E !important;
    color: #F5F5DC !important;
}

.beer-card .card-title {
    font-weight: 600;
    color: #F5F5DC !important;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    font-size: 1.1rem;
}

.beer-card .card-text {
    color: #D69A6B !important;
    line-height: 1.4;
}

.beer-card .text-muted {
    color: #D69A6B !important;
}

/* Beer Stats Badges */
.beer-stats .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* Card Footer */
.beer-card .card-footer {
    background-color: #3B2A2A !important;
    border-top: 1px solid #D69A6B !important;
    padding: 0.75rem 1rem;
}

/* Search Form - Brewery Theme */
.form-label {
    font-weight: 600;
    color: #F5F5DC !important;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #D69A6B;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

.form-control:focus, .form-select:focus {
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

.form-control::placeholder {
    color: #D69A6B !important;
    opacity: 0.8;
}

.form-select option {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

/* Buttons - Brewery Theme */
.btn {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    border: none !important;
    color: #3B2A2A !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    color: #F5F5DC !important;
}

.btn-outline-primary {
    border: 1px solid #FFC107 !important;
    color: #FFC107 !important;
    background: transparent !important;
}

.btn-outline-primary:hover {
    background: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border: 1px solid #D69A6B !important;
    color: #D69A6B !important;
    background: transparent !important;
}

.btn-outline-secondary:hover {
    background: #D69A6B !important;
    border-color: #D69A6B !important;
    color: #3B2A2A !important;
    transform: translateY(-1px);
}

/* Badge Styles - Brewery Theme */
.badge {
    border-radius: 0.375rem;
    font-weight: 500;
    border: 1px solid transparent;
}

.bg-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
    border-color: #FFC107 !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    color: #F5F5DC !important;
    border-color: #D69A6B !important;
}

.bg-warning {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
    border-color: #FFC107 !important;
}

.bg-success {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%) !important;
    color: #F5F5DC !important;
    border-color: #D69A6B !important;
}

.bg-light {
    background: #F5F5DC !important;
    color: #3B2A2A !important;
    border-color: #D69A6B !important;
}

/* Pagination - Brewery Theme */
.pagination .page-link {
    border-radius: 0.375rem;
    border: 1px solid #D69A6B !important;
    color: #FFC107 !important;
    background-color: #3B2A2A !important;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

/* Filter Card - Brewery Theme */
.card {
    background-color: #6F4C3E !important;
    border: 1px solid #D69A6B !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 2px 10px rgba(59, 42, 42, 0.3);
}

.card-body {
    padding: 1.5rem;
    background-color: #6F4C3E !important;
}

/* Results Header */
.container h5 {
    color: #F5F5DC !important;
}

.text-muted {
    color: #D69A6B !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .beer-stats .badge {
        display: block;
        margin-bottom: 0.25rem;
        text-align: center;
    }
    
    .card-footer .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-footer .btn {
        width: 100%;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.text-center.py-5 {
    padding: 4rem 2rem !important;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Form Enhancements */
.form-control::placeholder {
    color: #adb5bd;
    opacity: 1;
}

.form-select option {
    padding: 0.5rem;
}

/* Hover Effects - Brewery Theme */
.card:hover .card-title {
    color: #FFC107 !important;
}

.beer-card:hover .badge {
    transform: scale(1.05);
}

.beer-card:hover .card-title {
    color: #FFC107 !important;
}

/* Empty State - Brewery Theme */
.text-center.py-5 {
    padding: 4rem 2rem !important;
    color: #D69A6B !important;
}

.text-center.py-5 i {
    opacity: 0.6;
    color: #D69A6B !important;
}

.text-center.py-5 h4 {
    color: #F5F5DC !important;
}

/* Industry Standard Improvements */
.beer-card .card-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.beer-card .card-footer .d-flex {
    gap: 0.5rem;
}

.beer-card .card-footer .btn {
    flex: 1;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Better Typography */
.beer-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.beer-card .card-text {
    font-size: 0.875rem;
    line-height: 1.4;
}

.beer-card .text-muted {
    font-size: 0.8rem;
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.beer-card {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animation for multiple cards */
.beer-card:nth-child(1) { animation-delay: 0.1s; }
.beer-card:nth-child(2) { animation-delay: 0.2s; }
.beer-card:nth-child(3) { animation-delay: 0.3s; }
.beer-card:nth-child(4) { animation-delay: 0.4s; }
.beer-card:nth-child(5) { animation-delay: 0.5s; }
.beer-card:nth-child(6) { animation-delay: 0.6s; }

/* Search form animations */
.form-control:focus {
    animation: focusPulse 0.3s ease-out;
}

@keyframes focusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Beer Style Category Colors - Brewery Theme */
.badge.style-ipa {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
}
.badge.style-stout {
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%) !important;
    color: #F5F5DC !important;
}
.badge.style-lager {
    background: linear-gradient(135deg, #FFC107 0%, #F5F5DC 100%) !important;
    color: #3B2A2A !important;
}
.badge.style-wheat {
    background: linear-gradient(135deg, #D69A6B 0%, #FFC107 100%) !important;
    color: #3B2A2A !important;
}
.badge.style-sour {
    background: linear-gradient(135deg, #6F4C3E 0%, #D69A6B 100%) !important;
    color: #F5F5DC !important;
}
.badge.style-porter {
    background: linear-gradient(135deg, #3B2A2A 0%, #D69A6B 100%) !important;
    color: #F5F5DC !important;
}
.badge.style-pale-ale {
    background: linear-gradient(135deg, #D69A6B 0%, #FFC107 100%) !important;
    color: #3B2A2A !important;
}
.badge.style-belgian {
    background: linear-gradient(135deg, #6F4C3E 0%, #FFC107 100%) !important;
    color: #F5F5DC !important;
}
.badge.style-other {
    background: linear-gradient(135deg, #F5F5DC 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
}
