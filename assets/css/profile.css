/* Profile Page Styles - Brewery Theme with Black Background */

/* Keep black background for the page */
body {
    background-color: #000000 !important;
    color: #F5F5DC !important;
}

.profile-avatar img {
    object-fit: cover;
    border: 3px solid #FFC107;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
}

.avatar-placeholder {
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border: 2px dashed #D69A6B;
    transition: all 0.3s ease;
    color: #F5F5DC;
}

.avatar-placeholder:hover {
    border-color: #FFC107;
    background: linear-gradient(135deg, #6F4C3E 0%, #D69A6B 100%);
}

.card {
    background-color: #1a1a1a !important;
    border: 1px solid #D69A6B !important;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: #F5F5DC !important;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(214, 154, 107, 0.3) !important;
}

.card-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%) !important;
    color: #F5F5DC !important;
    border-radius: 12px 12px 0 0 !important;
    border: none;
    border-bottom: 1px solid #D69A6B !important;
}

.form-label {
    font-weight: 600;
    color: #F5F5DC !important;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    background-color: #3B2A2A !important;
    border: 2px solid #D69A6B !important;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    color: #F5F5DC !important;
}

.form-control::placeholder {
    color: #D69A6B !important;
    opacity: 0.8;
}

.form-control:focus, .form-select:focus {
    background-color: #6F4C3E !important;
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
    color: #F5F5DC !important;
}

.input-group-text {
    background: #D69A6B !important;
    border: 2px solid #D69A6B !important;
    border-radius: 8px 0 0 8px;
    color: #F5F5DC !important;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    border: none !important;
    color: #3B2A2A !important;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%) !important;
    color: #F5F5DC !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #F5F5DC !important;
    color: #F5F5DC !important;
    background: transparent !important;
    font-weight: 600;
}

.btn-outline-secondary:hover {
    background: #F5F5DC !important;
    border-color: #F5F5DC !important;
    color: #3B2A2A !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(245, 245, 220, 0.3);
}

.badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%) !important;
    color: #3B2A2A !important;
}

/* Quick Links buttons - different colors for variety */
.btn-outline-primary {
    border: 2px solid #FFC107 !important;
    color: #FFC107 !important;
    background: transparent !important;
}

.btn-outline-primary:hover {
    background: #FFC107 !important;
    color: #3B2A2A !important;
}

.btn-outline-warning {
    border: 2px solid #D69A6B !important;
    color: #D69A6B !important;
    background: transparent !important;
}

.btn-outline-warning:hover {
    background: #D69A6B !important;
    color: #F5F5DC !important;
}

.btn-outline-success {
    border: 2px solid #6F4C3E !important;
    color: #6F4C3E !important;
    background: transparent !important;
}

.btn-outline-success:hover {
    background: #6F4C3E !important;
    color: #F5F5DC !important;
}

.btn-outline-info {
    border: 2px solid #F5F5DC !important;
    color: #F5F5DC !important;
    background: transparent !important;
}

.btn-outline-info:hover {
    background: #F5F5DC !important;
    color: #3B2A2A !important;
}

.btn-outline-dark {
    border: 2px solid #3B2A2A !important;
    color: #3B2A2A !important;
    background: transparent !important;
}

.btn-outline-dark:hover {
    background: #3B2A2A !important;
    color: #F5F5DC !important;
}

/* Text colors for dark theme */
.text-muted {
    color: #D69A6B !important;
}

.fw-bold {
    color: #F5F5DC !important;
}

/* Form text styling */
.form-text {
    color: #D69A6B !important;
}

/* Form check styling */
.form-check-label {
    color: #F5F5DC !important;
}

.form-check-input:checked {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
}

.form-check-input:focus {
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
}

/* Stats styling */
.stat-number {
    color: #FFC107 !important;
}

.stat-label {
    color: #D69A6B !important;
}

/* Avatar upload styling */
.current-avatar img {
    object-fit: cover;
    border: 2px solid #FFC107 !important;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
    transition: all 0.3s ease;
}

.current-avatar img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.5);
}

.current-avatar .avatar-placeholder {
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border: 2px dashed #D69A6B;
    color: #F5F5DC;
    transition: all 0.3s ease;
}

.current-avatar .avatar-placeholder:hover {
    border-color: #FFC107;
    background: linear-gradient(135deg, #6F4C3E 0%, #D69A6B 100%);
}

/* File input styling */
input[type="file"].form-control {
    background-color: #3B2A2A !important;
    border: 2px dashed #D69A6B !important;
    color: #F5F5DC !important;
    padding: 1rem;
    transition: all 0.3s ease;
}

input[type="file"].form-control:hover {
    border-color: #FFC107 !important;
    background-color: #6F4C3E !important;
}

input[type="file"].form-control:focus {
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
    background-color: #6F4C3E !important;
}

/* File input custom styling */
input[type="file"].form-control::file-selector-button {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border: none;
    color: #3B2A2A;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    margin-right: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

input[type="file"].form-control::file-selector-button:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%);
    color: #F5F5DC;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

/* Stats styling */
.row.text-center .fw-bold {
    font-size: 1.25rem;
    color: #007bff;
}

.row.text-center small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form sections */
h6.fw-bold {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* Social icons */
.fab, .fas {
    color: #007bff;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
    }
    
    .profile-avatar {
        margin-bottom: 1rem;
    }
}

/* Animation for form validation */
.form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Profile visibility indicators */
.profile-visibility-public {
    color: #28a745;
}

.profile-visibility-friends {
    color: #ffc107;
}

.profile-visibility-private {
    color: #dc3545;
}

/* Enhanced form styling */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

/* ALL alert messages - Green with white text */
.alert-success,
.alert-danger,
.alert-warning,
.alert-info,
.alert {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    border: 1px solid #28a745 !important;
    border-radius: 8px;
    color: #ffffff !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    padding: 1rem 1.25rem !important;
    margin-bottom: 1rem !important;
}

/* Ensure ALL alert message text is always white */
.alert-success *,
.alert-danger *,
.alert-warning *,
.alert-info *,
.alert * {
    color: #ffffff !important;
}

/* Alert message icon styling */
.alert .fas,
.alert .fa {
    color: #ffffff !important;
}

/* Full-Width Hero Profile Layout - Industry Standard */
.profile-hero {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
}

.hero-image {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.profile-picture-hero img,
.avatar-placeholder-hero {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
    transition: transform 0.3s ease;
}

.profile-picture-hero img:hover,
.avatar-placeholder-hero:hover {
    transform: scale(1.02);
}

.profile-nav-container {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: sticky;
    top: 0;
    z-index: 100;
}

.profile-nav-container .nav-link {
    background: transparent !important;
    border: none !important;
    color: #F5F5DC !important;
    font-weight: 600;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent !important;
}

.profile-nav-container .nav-link:hover {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #FFC107 !important;
    border-bottom-color: #FFC107 !important;
}

.profile-nav-container .nav-link.active {
    background: rgba(255, 193, 7, 0.2) !important;
    color: #FFC107 !important;
    border-bottom-color: #FFC107 !important;
}

.profile-picture-large img,
.avatar-placeholder-large {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.profile-picture-large img:hover,
.avatar-placeholder-large:hover {
    transform: scale(1.05);
}

.profile-header-info h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

.profile-stats .stat-item {
    transition: transform 0.3s ease;
}

.profile-stats .stat-item:hover {
    transform: translateY(-2px);
}

.profile-nav .nav-tabs {
    background: transparent;
    border-bottom: 2px solid #D69A6B;
}

.profile-nav .nav-link {
    background: transparent;
    border: none;
    color: #F5F5DC;
    font-weight: 600;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.profile-nav .nav-link:hover {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
    border-bottom-color: #FFC107;
}

.profile-nav .nav-link.active {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    border-bottom-color: #FFC107;
}

.stat-box {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

/* Tab content styling */
.tab-content {
    background: transparent;
}

.tab-pane .card {
    background: #3B2A2A;
    border: 1px solid #D69A6B;
    color: #F5F5DC;
}

.tab-pane .card-header {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    border-bottom: 1px solid #D69A6B;
    color: #F5F5DC;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-cover {
        height: 250px !important;
    }

    .profile-picture-large img,
    .avatar-placeholder-large {
        width: 100px !important;
        height: 100px !important;
    }

    .profile-header-info h1 {
        font-size: 1.5rem;
    }

    .profile-stats {
        flex-direction: column;
        gap: 1rem !important;
    }

    .profile-nav .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    border: 1px solid #f1b0b7;
    border-radius: 8px;
    color: #721c24;
}
