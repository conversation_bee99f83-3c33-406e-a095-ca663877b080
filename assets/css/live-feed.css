/**
 * Live Feed Styles
 * Enhanced styling for the real-time activity feed
 */

/* Live Feed Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* Live Indicators */
.pulse {
    animation: pulse 2s infinite;
}

.live-indicator {
    position: relative;
}

.live-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff0000, #ff4444, #ff0000);
    border-radius: inherit;
    z-index: -1;
    animation: pulse 2s infinite;
}

/* Activity Items */
.activity-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.activity-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.activity-item.new-activity {
    border-left-color: #28a745;
    background: linear-gradient(90deg, #f8fff9 0%, #ffffff 100%);
    animation: fadeInUp 0.5s ease;
}

.activity-item.new-activity::before {
    content: 'NEW';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    z-index: 1;
}

/* Activity Header */
.activity-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.activity-type {
    font-size: 0.75rem;
    padding: 2px 8px;
}

/* Enhanced Content */
.enhanced-content {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.enhanced-checkin {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-left: 4px solid #2196f3;
}

.enhanced-rating {
    background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
    border-left: 4px solid #ff9800;
}

.enhanced-badge {
    background: linear-gradient(135deg, #fffde7 0%, #f1f8e9 100%);
    border-left: 4px solid #ffc107;
}

/* Activity Actions */
.activity-actions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f8f9fa;
}

.activity-actions .btn {
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.activity-actions .btn:hover {
    transform: translateY(-1px);
}

/* Like Button */
.like-btn {
    position: relative;
}

.like-btn.active {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
    animation: heartBeat 0.6s ease;
}

.like-btn.active .fas.fa-heart {
    color: white;
}

/* Comments Section */
.comments-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    border: 1px solid #e9ecef;
}

.comment {
    animation: fadeInUp 0.3s ease;
}

.comment .bg-light {
    background-color: white !important;
    border: 1px solid #e9ecef;
}

/* Quick Post */
.quick-post-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.quick-post-container:focus-within {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#quickPostContent {
    border: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
}

#quickPostContent:focus {
    box-shadow: none;
    outline: none;
}

/* Online Friends */
.online-friends .friend-avatar {
    position: relative;
    transition: transform 0.2s ease;
}

.online-friends .friend-avatar:hover {
    transform: scale(1.1);
}

.online-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: #28a745;
    border: 2px solid white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Trending Hashtags */
.hashtag-link {
    color: #0d6efd;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 16px;
    display: inline-block;
}

.hashtag-link:hover {
    background: #e7f1ff;
    color: #0a58ca !important;
    transform: translateX(4px);
}

/* Live Stats */
.stat-item {
    padding: 12px;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-item .h5 {
    font-weight: 700;
    margin-bottom: 4px;
}

/* Live Update Indicator */
#liveUpdateIndicator {
    animation: slideInDown 0.5s ease;
    z-index: 1050;
}

#liveUpdateIndicator .alert {
    border-radius: 25px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .activity-item {
        margin-bottom: 1rem;
    }
    
    .activity-header {
        font-size: 0.9rem;
    }
    
    .activity-actions .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
    
    .enhanced-content {
        font-size: 0.9rem;
    }
    
    .quick-post-container {
        margin-bottom: 1rem;
    }
    
    #quickPostContent {
        font-size: 0.9rem;
    }
    
    .online-friends .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    
    .hashtag-link {
        font-size: 0.9rem;
        padding: 2px 6px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .activity-item {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .activity-item.new-activity {
        background: linear-gradient(90deg, #2f4f2f 0%, #2d3748 100%);
    }
    
    .enhanced-content {
        border-color: #4a5568;
    }
    
    .enhanced-checkin {
        background: linear-gradient(135deg, #1a365d 0%, #553c9a 100%);
    }
    
    .enhanced-rating {
        background: linear-gradient(135deg, #744210 0%, #97266d 100%);
    }
    
    .enhanced-badge {
        background: linear-gradient(135deg, #975a16 0%, #276749 100%);
    }
    
    .comments-section {
        background: #1a202c;
        border-color: #4a5568;
    }
    
    .comment .bg-light {
        background-color: #2d3748 !important;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .quick-post-container {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
    }
    
    .stat-item {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #e2e8f0;
    }
    
    .hashtag-link:hover {
        background: #2a4365;
    }
}

/* Accessibility */
.activity-item:focus-within {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .activity-item {
        border: 2px solid #000;
    }
    
    .btn {
        border-width: 2px;
        font-weight: bold;
    }
    
    .hashtag-link {
        text-decoration: underline;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .pulse {
        animation: none;
    }
}

/* Print styles */
@media print {
    .activity-actions,
    .quick-post-container,
    #liveUpdateIndicator {
        display: none !important;
    }
    
    .activity-item {
        break-inside: avoid;
        border: 1px solid #000;
        margin-bottom: 1rem;
    }
}
