/* Onboarding Styles - Brewery Theme */

.onboarding-container {
    background: linear-gradient(135deg, #6F4C3E 0%, #3B2A2A 100%);
    min-height: 100vh;
    padding-top: 2rem;
}

.onboarding-container .container {
    position: relative;
    z-index: 2;
}

.onboarding-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.progress {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #FFC107 0%, #D69A6B 100%);
    transition: width 0.6s ease;
}

.card {
    border-radius: 20px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-body {
    border-radius: 20px;
}

.display-5 {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.form-control-lg {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 1rem 1.25rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.form-control-lg:focus {
    border-color: #FFC107;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25);
    transform: translateY(-1px);
}

.input-group-lg .input-group-text {
    border-radius: 12px 0 0 12px;
    border: 2px solid #D69A6B;
    border-right: none;
    background: #D69A6B;
    color: #F5F5DC;
    font-size: 1.1rem;
    padding: 1rem 1.25rem;
}

.input-group-lg .form-control {
    border-left: none;
    border-radius: 0 12px 12px 0;
}

.form-label.fw-bold {
    color: #3B2A2A;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.form-text {
    color: #6F4C3E;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.btn-lg {
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #D69A6B 100%);
    border: none;
    color: #3B2A2A;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #D69A6B 0%, #6F4C3E 100%);
    color: #F5F5DC;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(214, 154, 107, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #F5F5DC;
    color: #F5F5DC;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #F5F5DC;
    border-color: #F5F5DC;
    color: #3B2A2A;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(245, 245, 220, 0.3);
}

.badge {
    border-radius: 20px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 1.25rem;
}

.alert-info {
    background: linear-gradient(135deg, #F5F5DC 0%, #D69A6B 100%);
    color: #3B2A2A;
    border: 1px solid #D69A6B;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
}

/* What's Next Section */
.text-center .col-md-4 {
    padding: 1rem;
    transition: all 0.3s ease;
}

.text-center .col-md-4:hover {
    transform: translateY(-5px);
}

.text-center .col-md-4 i {
    transition: all 0.3s ease;
}

.text-center .col-md-4:hover i {
    transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.progress {
    animation: fadeInUp 0.4s ease-out;
}

.display-5 {
    animation: fadeInUp 0.8s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .onboarding-container {
        padding-top: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
    }
    
    .text-center .col-md-4 {
        margin-bottom: 1rem;
    }
}

/* Form Validation States */
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tooltip Styles */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    padding: 0.5rem 1rem;
}
