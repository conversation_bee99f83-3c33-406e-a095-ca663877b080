/* Map Page Styles */

#breweryMap {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Map Controls */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 12px 12px 0 0 !important;
}

/* Legend */
.legend-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
}

.legend-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.legend-marker.brewery-marker {
    background: #28a745;
}

.legend-marker.user-marker {
    background: #007bff;
}

.legend-marker.popular-marker {
    background: #ffc107;
}

/* Brewery List */
.brewery-list {
    max-height: 600px;
    overflow-y: auto;
    padding: 0;
}

.brewery-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.brewery-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.brewery-item:last-child {
    border-bottom: none;
}

.brewery-info {
    flex-grow: 1;
    margin-right: 1rem;
}

.brewery-name {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.brewery-name a {
    color: inherit;
    transition: color 0.3s ease;
}

.brewery-name a:hover {
    color: #007bff;
}

.brewery-location {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.brewery-stats {
    font-size: 0.75rem;
}

.brewery-distance {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.brewery-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 100px;
}

/* Brewery Popup */
.brewery-popup {
    min-width: 250px;
    padding: 0.5rem;
}

.brewery-popup h6 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.brewery-popup .brewery-stats {
    color: #6c757d;
}

.brewery-popup .brewery-actions {
    flex-direction: row;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group .btn {
    border-radius: 0 8px 8px 0;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-outline-primary {
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .brewery-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .brewery-actions {
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
    }
    
    .brewery-actions .btn {
        flex: 1;
    }
    
    #breweryMap {
        height: 400px !important;
    }
    
    .brewery-list {
        max-height: 400px;
    }
    
    .legend-items {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .card-body .row.g-3 {
        gap: 1rem !important;
    }
    
    .card-body .row.g-3 > div {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .brewery-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .brewery-popup .brewery-actions {
        flex-direction: column;
    }
    
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.brewery-item {
    animation: fadeInUp 0.6s ease-out;
}

/* Stagger animations */
.brewery-item:nth-child(1) { animation-delay: 0.1s; }
.brewery-item:nth-child(2) { animation-delay: 0.2s; }
.brewery-item:nth-child(3) { animation-delay: 0.3s; }
.brewery-item:nth-child(4) { animation-delay: 0.4s; }
.brewery-item:nth-child(5) { animation-delay: 0.5s; }

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
.brewery-list::-webkit-scrollbar {
    width: 6px;
}

.brewery-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.brewery-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.brewery-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Map Info Window Styles */
.gm-style .gm-style-iw-c {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.gm-style .gm-style-iw-d {
    overflow: hidden !important;
}

/* Badge Styles */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.4rem 0.6rem;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

/* Form Labels */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Distance Display */
.brewery-distance {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    display: inline-block;
}

/* Search Input Enhancement */
.input-group .form-control {
    border-radius: 8px 0 0 8px;
}

.input-group .btn {
    border-left: none;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
}

.modal-footer {
    border: none;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Hover Effects */
.brewery-item:hover .brewery-name a {
    color: #007bff;
}

.brewery-item:hover .view-on-map {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Focus States */
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Print Styles */
@media print {
    #breweryMap {
        height: 400px !important;
    }
    
    .brewery-actions {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
