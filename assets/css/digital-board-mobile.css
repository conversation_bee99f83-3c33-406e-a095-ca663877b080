/**
 * Digital Board Mobile CSS
 * Phase 6 - Mobile & Responsive Optimization
 * 
 * Mobile-optimized styles for digital board management system
 */

/* Mobile-First Variables for Digital Board System */
:root {
    /* Brewery-themed mobile colors */
    --mobile-primary: #ffc107;
    --mobile-primary-dark: #d69a6b;
    --mobile-secondary: #6f4c3e;
    --mobile-background: #1a1a1a;
    --mobile-surface: #2c1810;
    --mobile-text: #f5f5dc;
    --mobile-text-muted: #999;
    
    /* Mobile-specific spacing */
    --mobile-header-height: 64px;
    --mobile-bottom-nav-height: 80px;
    --mobile-fab-size: 56px;
    --mobile-card-padding: 16px;
    --mobile-section-padding: 20px;
    
    /* Touch targets */
    --mobile-touch-min: 48px;
    --mobile-touch-comfortable: 56px;
    --mobile-touch-large: 64px;
    
    /* Mobile typography */
    --mobile-h1: 2rem;
    --mobile-h2: 1.75rem;
    --mobile-h3: 1.5rem;
    --mobile-h4: 1.25rem;
    --mobile-body: 1rem;
    --mobile-small: 0.875rem;
    --mobile-tiny: 0.75rem;
}

/* Mobile Body Styles */
@media (max-width: 768px) {
    body {
        background: var(--mobile-background);
        color: var(--mobile-text);
        font-size: var(--mobile-body);
        line-height: 1.6;
        -webkit-text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
        padding-bottom: var(--mobile-bottom-nav-height);
    }
    
    body.mobile-menu-open {
        overflow: hidden;
    }
    
    body.virtual-keyboard-open {
        padding-bottom: 0;
    }
}

/* Mobile Header */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--mobile-header-height);
    background: var(--mobile-surface);
    border-bottom: 1px solid var(--mobile-primary-dark);
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.mobile-header .logo {
    height: 40px;
    width: auto;
}

.mobile-header .title {
    flex: 1;
    margin-left: 16px;
    font-size: var(--mobile-h4);
    font-weight: 600;
    color: var(--mobile-text);
}

.mobile-header .actions {
    display: flex;
    gap: 8px;
}

.mobile-header-btn {
    width: var(--mobile-touch-min);
    height: var(--mobile-touch-min);
    border: none;
    background: transparent;
    color: var(--mobile-text);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.mobile-header-btn:hover,
.mobile-header-btn:focus {
    background: rgba(255, 193, 7, 0.1);
    color: var(--mobile-primary);
}

/* Mobile Content Area */
.mobile-content {
    padding-top: calc(var(--mobile-header-height) + 16px);
    padding-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - var(--mobile-header-height) - var(--mobile-bottom-nav-height));
}

/* Mobile Cards */
@media (max-width: 768px) {
    .mobile-card {
        background: var(--mobile-surface);
        border: 1px solid var(--mobile-primary-dark);
        border-radius: 12px;
        margin-bottom: 16px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }
    
    .mobile-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    }
    
    .mobile-card-header {
        background: linear-gradient(135deg, var(--mobile-primary), var(--mobile-primary-dark));
        color: var(--mobile-background);
        padding: var(--mobile-card-padding);
        font-weight: 600;
    }
    
    .mobile-card-body {
        padding: var(--mobile-card-padding);
    }
    
    .mobile-card-footer {
        padding: var(--mobile-card-padding);
        border-top: 1px solid rgba(214, 154, 107, 0.2);
        background: rgba(44, 24, 16, 0.5);
    }
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--mobile-bottom-nav-height);
    background: var(--mobile-surface);
    border-top: 1px solid var(--mobile-primary-dark);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    min-width: var(--mobile-touch-comfortable);
    text-decoration: none;
    color: var(--mobile-text-muted);
    transition: all 0.2s ease;
    border-radius: 8px;
}

.mobile-nav-item.active {
    color: var(--mobile-primary);
    background: rgba(255, 193, 7, 0.1);
}

.mobile-nav-item:hover,
.mobile-nav-item:focus {
    color: var(--mobile-primary);
    text-decoration: none;
}

.mobile-nav-icon {
    font-size: 1.5rem;
    margin-bottom: 4px;
}

.mobile-nav-text {
    font-size: var(--mobile-tiny);
    font-weight: 500;
}

/* Mobile Floating Action Button */
.mobile-fab {
    position: fixed;
    bottom: calc(var(--mobile-bottom-nav-height) + 16px);
    right: 16px;
    width: var(--mobile-fab-size);
    height: var(--mobile-fab-size);
    background: linear-gradient(135deg, var(--mobile-primary), var(--mobile-primary-dark));
    color: var(--mobile-background);
    border: none;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    z-index: 999;
    transition: all 0.3s ease;
}

.mobile-fab:hover,
.mobile-fab:focus {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(255, 193, 7, 0.6);
}

.mobile-fab:active {
    transform: scale(0.95);
}

/* Mobile Forms */
@media (max-width: 768px) {
    .mobile-form-group {
        margin-bottom: 20px;
    }
    
    .mobile-form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--mobile-text);
        font-size: var(--mobile-body);
    }
    
    .mobile-form-control {
        width: 100%;
        min-height: var(--mobile-touch-min);
        padding: 12px 16px;
        background: rgba(58, 58, 58, 0.8);
        border: 1px solid var(--mobile-primary-dark);
        border-radius: 8px;
        color: var(--mobile-text);
        font-size: var(--mobile-body);
        transition: all 0.2s ease;
    }
    
    .mobile-form-control:focus {
        outline: none;
        border-color: var(--mobile-primary);
        box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
        background: rgba(58, 58, 58, 1);
    }
    
    .mobile-form-control::placeholder {
        color: var(--mobile-text-muted);
    }
}

/* Mobile Buttons */
@media (max-width: 768px) {
    .mobile-btn {
        min-height: var(--mobile-touch-min);
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: var(--mobile-body);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.2s ease;
        cursor: pointer;
        user-select: none;
    }
    
    .mobile-btn-primary {
        background: linear-gradient(135deg, var(--mobile-primary), var(--mobile-primary-dark));
        color: var(--mobile-background);
    }
    
    .mobile-btn-primary:hover,
    .mobile-btn-primary:focus {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    }
    
    .mobile-btn-secondary {
        background: var(--mobile-secondary);
        color: var(--mobile-text);
    }
    
    .mobile-btn-outline {
        background: transparent;
        border: 2px solid var(--mobile-primary);
        color: var(--mobile-primary);
    }
    
    .mobile-btn-full {
        width: 100%;
    }
    
    .mobile-btn-large {
        min-height: var(--mobile-touch-comfortable);
        padding: 16px 32px;
        font-size: var(--mobile-h4);
    }
    
    .mobile-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
}

/* Mobile Tables */
@media (max-width: 768px) {
    .mobile-table-container {
        background: var(--mobile-surface);
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .mobile-table-header {
        background: var(--mobile-primary);
        color: var(--mobile-background);
        padding: var(--mobile-card-padding);
        font-weight: 600;
    }
    
    .mobile-table-row {
        padding: var(--mobile-card-padding);
        border-bottom: 1px solid rgba(214, 154, 107, 0.2);
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .mobile-table-row:last-child {
        border-bottom: none;
    }
    
    .mobile-table-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-table-label {
        font-weight: 600;
        color: var(--mobile-text);
        min-width: 100px;
    }
    
    .mobile-table-value {
        color: var(--mobile-text-muted);
        text-align: right;
    }
}

/* Mobile Modals */
@media (max-width: 768px) {
    .mobile-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2000;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .mobile-modal.show {
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-modal-content {
        background: var(--mobile-surface);
        border-radius: 16px 16px 0 0;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        transform: translateY(100%);
        transition: transform 0.3s ease;
    }
    
    .mobile-modal.show .mobile-modal-content {
        transform: translateY(0);
    }
    
    .mobile-modal-header {
        padding: 20px;
        border-bottom: 1px solid rgba(214, 154, 107, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-modal-title {
        font-size: var(--mobile-h3);
        font-weight: 600;
        color: var(--mobile-text);
    }
    
    .mobile-modal-close {
        width: var(--mobile-touch-min);
        height: var(--mobile-touch-min);
        border: none;
        background: transparent;
        color: var(--mobile-text-muted);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }
    
    .mobile-modal-body {
        padding: 20px;
    }
    
    .mobile-modal-footer {
        padding: 20px;
        border-top: 1px solid rgba(214, 154, 107, 0.2);
        display: flex;
        gap: 12px;
    }
}

/* Mobile Alerts */
@media (max-width: 768px) {
    .mobile-alert {
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 16px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }
    
    .mobile-alert-success {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid rgba(40, 167, 69, 0.3);
        color: #90ee90;
    }
    
    .mobile-alert-danger {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid rgba(220, 53, 69, 0.3);
        color: #ffcccb;
    }
    
    .mobile-alert-warning {
        background: rgba(255, 193, 7, 0.2);
        border: 1px solid rgba(255, 193, 7, 0.3);
        color: #ffe0b3;
    }
    
    .mobile-alert-icon {
        font-size: 1.25rem;
        flex-shrink: 0;
    }
    
    .mobile-alert-content {
        flex: 1;
    }
    
    .mobile-alert-title {
        font-weight: 600;
        margin-bottom: 4px;
    }
    
    .mobile-alert-message {
        font-size: var(--mobile-small);
        opacity: 0.9;
    }
}

/* Mobile Loading States */
.mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--mobile-text-muted);
}

.mobile-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 193, 7, 0.3);
    border-top: 3px solid var(--mobile-primary);
    border-radius: 50%;
    animation: mobile-spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes mobile-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Utilities */
@media (max-width: 768px) {
    .mobile-hidden { display: none !important; }
    .mobile-only { display: block !important; }
    .mobile-flex { display: flex !important; }
    .mobile-text-center { text-align: center !important; }
    .mobile-text-left { text-align: left !important; }
    .mobile-text-right { text-align: right !important; }
    .mobile-full-width { width: 100% !important; }
    .mobile-half-width { width: 50% !important; }
    .mobile-no-padding { padding: 0 !important; }
    .mobile-small-padding { padding: 8px !important; }
    .mobile-large-padding { padding: 24px !important; }
    .mobile-no-margin { margin: 0 !important; }
    .mobile-small-margin { margin: 8px !important; }
    .mobile-large-margin { margin: 24px !important; }
}

/* Desktop-only utilities */
@media (min-width: 769px) {
    .mobile-only { display: none !important; }
    .mobile-bottom-nav { display: none !important; }
    .mobile-fab { display: none !important; }
    .mobile-header { display: none !important; }
}

/* Touch-specific optimizations */
@media (hover: none) and (pointer: coarse) {
    .mobile-btn,
    .mobile-nav-item,
    .mobile-header-btn {
        min-height: var(--mobile-touch-min);
        min-width: var(--mobile-touch-min);
    }
    
    /* Remove hover effects on touch devices */
    .mobile-card:hover,
    .mobile-btn:hover {
        transform: none;
    }
    
    /* Add active states for touch feedback */
    .mobile-btn:active {
        transform: scale(0.98);
    }
    
    .mobile-card:active {
        transform: scale(0.99);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-card,
    .mobile-modal-content,
    .mobile-bottom-nav {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-header {
        height: 56px;
    }

    .mobile-bottom-nav {
        height: 64px;
    }

    .mobile-content {
        padding-top: calc(56px + 12px);
    }

    body {
        padding-bottom: 64px;
    }
}

/* Mobile Display Templates */
@media (max-width: 768px) {
    .display-container {
        width: 100vw;
        height: 100vh;
        overflow: hidden;
        position: relative;
    }

    .display-template {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .template-header {
        flex-shrink: 0;
        padding: 12px 16px;
        background: var(--mobile-primary);
        color: var(--mobile-background);
    }

    .template-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
    }

    .template-footer {
        flex-shrink: 0;
        padding: 12px 16px;
        background: var(--mobile-surface);
        border-top: 1px solid var(--mobile-primary-dark);
    }

    /* Beer list mobile optimization */
    .beer-list-mobile {
        display: grid;
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .beer-item-mobile {
        background: var(--mobile-surface);
        border: 1px solid var(--mobile-primary-dark);
        border-radius: 8px;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .beer-info-mobile {
        flex: 1;
    }

    .beer-name-mobile {
        font-weight: 600;
        font-size: 1.1rem;
        color: var(--mobile-text);
        margin-bottom: 4px;
    }

    .beer-style-mobile {
        color: var(--mobile-text-muted);
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .beer-details-mobile {
        display: flex;
        gap: 16px;
        font-size: 0.85rem;
    }

    .beer-price-mobile {
        font-weight: 600;
        font-size: 1.2rem;
        color: var(--mobile-primary);
    }

    /* Slideshow mobile controls */
    .slideshow-controls-mobile {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        background: rgba(0, 0, 0, 0.8);
        padding: 12px;
        border-radius: 24px;
        z-index: 1000;
    }

    .slideshow-btn-mobile {
        width: 48px;
        height: 48px;
        border: none;
        border-radius: 50%;
        background: var(--mobile-primary);
        color: var(--mobile-background);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.2s ease;
    }

    .slideshow-btn-mobile:hover,
    .slideshow-btn-mobile:focus {
        background: var(--mobile-primary-dark);
        transform: scale(1.1);
    }

    /* Template builder mobile */
    .template-builder-mobile {
        display: flex;
        flex-direction: column;
        height: 100vh;
    }

    .builder-toolbar-mobile {
        background: var(--mobile-surface);
        border-bottom: 1px solid var(--mobile-primary-dark);
        padding: 12px;
        display: flex;
        gap: 8px;
        overflow-x: auto;
    }

    .builder-canvas-mobile {
        flex: 1;
        overflow: auto;
        padding: 16px;
        background: #f5f5f5;
    }

    .builder-properties-mobile {
        background: var(--mobile-surface);
        border-top: 1px solid var(--mobile-primary-dark);
        padding: 16px;
        max-height: 40vh;
        overflow-y: auto;
    }

    /* Drag and drop mobile */
    .draggable-mobile {
        touch-action: none;
        user-select: none;
        cursor: grab;
    }

    .draggable-mobile.dragging {
        opacity: 0.7;
        transform: scale(1.05);
        z-index: 1000;
        cursor: grabbing;
    }

    .drop-zone-mobile {
        min-height: 100px;
        border: 2px dashed var(--mobile-primary-dark);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--mobile-text-muted);
        transition: all 0.2s ease;
    }

    .drop-zone-mobile.drag-over {
        border-color: var(--mobile-primary);
        background: rgba(255, 193, 7, 0.1);
        color: var(--mobile-primary);
    }
}

/* Touch-specific display optimizations */
@media (hover: none) and (pointer: coarse) {
    .display-template {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    .beer-item-mobile {
        min-height: var(--mobile-touch-comfortable);
    }

    .slideshow-controls-mobile {
        bottom: 30px;
    }

    .slideshow-btn-mobile {
        width: var(--mobile-touch-comfortable);
        height: var(--mobile-touch-comfortable);
    }
}

/* PWA-specific styles */
@media (display-mode: standalone) {
    .mobile-header {
        padding-top: calc(12px + env(safe-area-inset-top));
        height: calc(var(--mobile-header-height) + env(safe-area-inset-top));
    }

    .mobile-content {
        padding-top: calc(var(--mobile-header-height) + env(safe-area-inset-top) + 16px);
    }

    .mobile-bottom-nav {
        padding-bottom: env(safe-area-inset-bottom);
        height: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
    }

    body {
        padding-bottom: calc(var(--mobile-bottom-nav-height) + env(safe-area-inset-bottom));
    }

    .slideshow-controls-mobile {
        bottom: calc(30px + env(safe-area-inset-bottom));
    }
}
