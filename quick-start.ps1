# Quick Start Script for Beersty Development
# Simple commands to start/stop/check XAMPP services

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "stop", "status", "restart", "open")]
    [string]$Action = "start"
)

# XAMPP paths
$xamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$xamppPath = $xamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $xamppPath) {
    Write-Host "❌ XAMPP not found. Please install XAMPP first." -ForegroundColor Red
    exit 1
}

$xamppControl = Join-Path $xamppPath "xampp-control.exe"

function Show-Status {
    Write-Host "🔍 Checking service status..." -ForegroundColor Cyan
    
    $apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
    $mysql = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
    
    if ($apache) {
        Write-Host "✅ Apache: Running" -ForegroundColor Green
    } else {
        Write-Host "❌ Apache: Stopped" -ForegroundColor Red
    }
    
    if ($mysql) {
        Write-Host "✅ MySQL: Running" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL: Stopped" -ForegroundColor Red
    }
    
    # Test web server
    try {
        $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ Web Server: Responding" -ForegroundColor Green
    } catch {
        Write-Host "❌ Web Server: Not responding" -ForegroundColor Red
    }
}

function Start-Services {
    Write-Host "🚀 Starting XAMPP services..." -ForegroundColor Yellow
    Start-Process $xamppControl -WindowStyle Normal
    Start-Sleep 3
    Show-Status
}

function Stop-Services {
    Write-Host "🛑 Stopping XAMPP services..." -ForegroundColor Yellow
    
    # Stop Apache
    Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
    
    # Stop MySQL
    Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
    
    Write-Host "✅ Services stopped" -ForegroundColor Green
}

function Open-Project {
    Write-Host "🌍 Opening project URLs..." -ForegroundColor Cyan
    Start-Process "http://localhost/beersty-lovable"
    Start-Process "http://localhost/beersty-lovable/admin/user-management.php"
    Start-Process "http://localhost/phpmyadmin"
}

# Execute action
switch ($Action) {
    "start" { 
        Start-Services 
        Write-Host ""
        Write-Host "📋 Quick URLs:" -ForegroundColor Cyan
        Write-Host "   Main: http://localhost/beersty-lovable" -ForegroundColor White
        Write-Host "   Admin: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
        Write-Host "   phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
    }
    "stop" { Stop-Services }
    "status" { Show-Status }
    "restart" { 
        Stop-Services 
        Start-Sleep 3
        Start-Services 
    }
    "open" { Open-Project }
}

Write-Host ""
Write-Host "💡 Usage: .\quick-start.ps1 [start|stop|status|restart|open]" -ForegroundColor Gray
