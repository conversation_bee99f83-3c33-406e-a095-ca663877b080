<?php
require_once 'config/config.php';

echo "<h1>Debug Links Test</h1>";

echo "<h2>Current User Status:</h2>";
echo "<p>Logged in: " . (isLoggedIn() ? 'YES' : 'NO') . "</p>";

echo "<h2>Test Links:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<h3>Navigation Links:</h3>";
echo "<a href='/account/login/' class='btn btn-primary' style='margin: 5px;'>Login (/account/login/)</a><br>";
echo "<a href='/account/register/' class='btn btn-success' style='margin: 5px;'>Register (/account/register/)</a><br>";
echo "<a href='/login/' class='btn btn-info' style='margin: 5px;'>Login Short (/login/)</a><br>";
echo "<a href='/register/' class='btn btn-warning' style='margin: 5px;'>Register Short (/register/)</a><br>";
echo "</div>";

echo "<h2>Hero Button Test:</h2>";
if (!isLoggedIn()) {
    echo "<div class='d-flex justify-content-center gap-3 mb-4'>";
    echo "<a href='/account/register/' class='btn btn-primary btn-lg'>";
    echo "<i class='fas fa-user-plus me-2'></i>Join Now";
    echo "</a>";
    echo "<a href='/account/login/' class='btn btn-outline-primary btn-lg'>";
    echo "<i class='fas fa-sign-in-alt me-2'></i>Sign In";
    echo "</a>";
    echo "</div>";
} else {
    echo "<p>User is logged in - hero buttons would not show</p>";
}

echo "<h2>URL Testing:</h2>";
echo "<p>Base URL: " . getBaseUrl() . "</p>";
echo "<p>Current URL: " . $_SERVER['REQUEST_URI'] . "</p>";

echo "<h2>File Existence Check:</h2>";
echo "<p>/account/login/index.php exists: " . (file_exists('account/login/index.php') ? 'YES' : 'NO') . "</p>";
echo "<p>/account/register/index.php exists: " . (file_exists('account/register/index.php') ? 'YES' : 'NO') . "</p>";
echo "<p>/auth/login.php exists: " . (file_exists('auth/login.php') ? 'YES' : 'NO') . "</p>";
echo "<p>/auth/register.php exists: " . (file_exists('auth/register.php') ? 'YES' : 'NO') . "</p>";

echo "<h2>Quick Test:</h2>";
echo "<p><a href='/' class='btn btn-secondary'>Back to Homepage</a></p>";
?>

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
