# Setup Social Features for Beersty
Write-Host "=== Setting up Beersty Social Features ===" -ForegroundColor Green

# Start server
$env:PATH = "C:\xampp\php;$env:PATH"
Get-Process -Name "php" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2
Start-Process PowerShell -ArgumentList "-Command", "C:\xampp\php\php.exe -S localhost:8000" -WindowStyle Minimized
Start-Sleep -Seconds 3

Write-Host "`n=== Creating Social Database Tables ===" -ForegroundColor Yellow

# Create a PHP script to execute the SQL
$setupSQL = @'
<?php
require_once 'config/config.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "Setting up social features database tables...\n";
    
    // Read and execute the social events system SQL
    $sql = file_get_contents('database/social_events_system.sql');
    
    // Split by semicolon and execute each statement
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $conn->exec($statement);
                echo "✓ Executed SQL statement successfully\n";
            } catch (Exception $e) {
                echo "⚠ Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n✅ Social features database setup completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
'@

# Save the setup script
$setupSQL | Out-File -FilePath "setup_social_db.php" -Encoding UTF8

# Run the setup script
Write-Host "Executing database setup..." -ForegroundColor Cyan
try {
    $output = & C:\xampp\php\php.exe setup_social_db.php 2>&1
    Write-Host $output -ForegroundColor White
} catch {
    Write-Host "Error running database setup: $_" -ForegroundColor Red
}

# Clean up
Remove-Item "setup_social_db.php" -ErrorAction SilentlyContinue

Write-Host "`n=== Testing Social Features ===" -ForegroundColor Yellow

Write-Host "Testing social dashboard..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/social/dashboard.php" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Social dashboard loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Social dashboard needs login: $_" -ForegroundColor Yellow
}

Write-Host "Testing events page..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/social/events.php" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Events page loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Events page needs login: $_" -ForegroundColor Yellow
}

Write-Host "Testing challenges page..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/social/challenges.php" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Challenges page loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Challenges page needs login: $_" -ForegroundColor Yellow
}

Write-Host "Testing clubs page..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/social/clubs.php" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Clubs page loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Clubs page needs login: $_" -ForegroundColor Yellow
}

Write-Host "Testing trading page..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/social/trading.php" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Trading page loads successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Trading page needs login: $_" -ForegroundColor Yellow
}

Write-Host "`n=== Social Features Overview ===" -ForegroundColor Cyan

Write-Host "`nEvents and Meetups System:" -ForegroundColor White
Write-Host "- Event creation and management" -ForegroundColor Green
Write-Host "- RSVP system with guest counts" -ForegroundColor Green
Write-Host "- Event types: brewery tours, tastings, meetups, festivals" -ForegroundColor Green
Write-Host "- Location integration with coordinates" -ForegroundColor Green
Write-Host "- Event comments and discussions" -ForegroundColor Green
Write-Host "- Check-in system for attendance" -ForegroundColor Green

Write-Host "`nSocial Challenges and Competitions:" -ForegroundColor White
Write-Host "- Challenge types: beer styles, brewery visits, check-ins, reviews" -ForegroundColor Green
Write-Host "- Progress tracking with JSON-based rules" -ForegroundColor Green
Write-Host "- Leaderboards and ranking system" -ForegroundColor Green
Write-Host "- Badge rewards and prizes" -ForegroundColor Green
Write-Host "- Participation management" -ForegroundColor Green

Write-Host "`nBeer Clubs and Groups:" -ForegroundColor White
Write-Host "- Club types: public, private, invite-only" -ForegroundColor Green
Write-Host "- Focus areas: general, style-specific, local, professional" -ForegroundColor Green
Write-Host "- Membership roles: owner, admin, moderator, member" -ForegroundColor Green
Write-Host "- Club discovery and search" -ForegroundColor Green
Write-Host "- Meeting frequency and location tracking" -ForegroundColor Green

Write-Host "`nBeer Trading and Sharing:" -ForegroundColor White
Write-Host "- Trade types: trade, share, sell, request" -ForegroundColor Green
Write-Host "- Offer management and negotiation" -ForegroundColor Green
Write-Host "- Beer listings with JSON data" -ForegroundColor Green
Write-Host "- Location filtering and shipping options" -ForegroundColor Green
Write-Host "- Trade history and reputation" -ForegroundColor Green

Write-Host "`nSocial Dashboard:" -ForegroundColor White
Write-Host "- Activity feed from friends" -ForegroundColor Green
Write-Host "- Social statistics and metrics" -ForegroundColor Green
Write-Host "- Quick actions for all features" -ForegroundColor Green
Write-Host "- Upcoming events calendar" -ForegroundColor Green
Write-Host "- Active challenges progress" -ForegroundColor Green
Write-Host "- Trending content discovery" -ForegroundColor Green

Write-Host "`nDatabase Tables Created:" -ForegroundColor White
Write-Host "- events - Event management" -ForegroundColor Gray
Write-Host "- event_rsvps - RSVP tracking" -ForegroundColor Gray
Write-Host "- event_comments - Event discussions" -ForegroundColor Gray
Write-Host "- social_challenges - Challenge definitions" -ForegroundColor Gray
Write-Host "- challenge_participants - User participation" -ForegroundColor Gray
Write-Host "- beer_clubs - Community groups" -ForegroundColor Gray
Write-Host "- club_memberships - Membership management" -ForegroundColor Gray
Write-Host "- beer_trades - Trading posts" -ForegroundColor Gray
Write-Host "- trade_offers - Offer negotiations" -ForegroundColor Gray

Write-Host "`nAccess Your Social Features:" -ForegroundColor White
Write-Host "Social Dashboard: http://localhost:8000/social/dashboard.php" -ForegroundColor Yellow
Write-Host "Events and Meetups: http://localhost:8000/social/events.php" -ForegroundColor Yellow
Write-Host "Challenges: http://localhost:8000/social/challenges.php" -ForegroundColor Yellow
Write-Host "Beer Clubs: http://localhost:8000/social/clubs.php" -ForegroundColor Yellow
Write-Host "Beer Trading: http://localhost:8000/social/trading.php" -ForegroundColor Yellow

Write-Host "`nNext Steps:" -ForegroundColor White
Write-Host "1. Login to your Beersty account" -ForegroundColor White
Write-Host "2. Visit the social dashboard to explore features" -ForegroundColor White
Write-Host "3. Create your first event or join a challenge" -ForegroundColor White
Write-Host "4. Join beer clubs and start trading" -ForegroundColor White
Write-Host "5. Invite friends to build your social network" -ForegroundColor White

Write-Host "`nBeersty Social Features are ready!" -ForegroundColor Green
