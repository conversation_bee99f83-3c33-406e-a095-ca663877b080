@echo off
echo ========================================
echo   Beersty Port 8080 Configuration
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Setting up Beersty on port 8080...
echo.

REM Check if XAMPP exists
if not exist "C:\xampp\apache\conf\httpd.conf" (
    echo ERROR: XAMPP not found at C:\xampp\
    echo Please install XAMPP first.
    pause
    exit /b 1
)

echo ✓ XAMPP found
echo.

REM Backup original Apache config
if not exist "C:\xampp\apache\conf\httpd.conf.backup" (
    echo Creating backup of Apache configuration...
    copy "C:\xampp\apache\conf\httpd.conf" "C:\xampp\apache\conf\httpd.conf.backup"
    echo ✓ Backup created
)

REM Create virtual host configuration for port 8080
echo Creating virtual host configuration...

REM Add Listen directive for port 8080
findstr /C:"Listen 8080" "C:\xampp\apache\conf\httpd.conf" >nul
if %errorLevel% neq 0 (
    echo. >> "C:\xampp\apache\conf\httpd.conf"
    echo # Beersty Application Port >> "C:\xampp\apache\conf\httpd.conf"
    echo Listen 8080 >> "C:\xampp\apache\conf\httpd.conf"
    echo ✓ Added Listen 8080 to Apache config
)

REM Create virtual host file
echo ^<VirtualHost *:8080^> > "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo     DocumentRoot "C:/xampp/htdocs/beersty" >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo     ServerName localhost >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo     DirectoryIndex index.php index.html >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo. >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo     ^<Directory "C:/xampp/htdocs/beersty"^> >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo         AllowOverride All >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo         Require all granted >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo         Options Indexes FollowSymLinks >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo     ^</Directory^> >> "C:\xampp\apache\conf\extra\beersty-8080.conf"
echo ^</VirtualHost^> >> "C:\xampp\apache\conf\extra\beersty-8080.conf"

echo ✓ Created virtual host configuration

REM Include the virtual host in main config
findstr /C:"beersty-8080.conf" "C:\xampp\apache\conf\httpd.conf" >nul
if %errorLevel% neq 0 (
    echo. >> "C:\xampp\apache\conf\httpd.conf"
    echo # Include Beersty Virtual Host >> "C:\xampp\apache\conf\httpd.conf"
    echo Include conf/extra/beersty-8080.conf >> "C:\xampp\apache\conf\httpd.conf"
    echo ✓ Added virtual host include to Apache config
)

REM Copy Beersty files if not already there
if not exist "C:\xampp\htdocs\beersty\index.php" (
    echo Copying Beersty files...
    if not exist "C:\xampp\htdocs\beersty" (
        mkdir "C:\xampp\htdocs\beersty"
    )
    
    REM Copy all files
    copy "*.php" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1
    copy "*.html" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1
    copy ".htaccess" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1
    
    REM Copy directories
    xcopy "config" "C:\xampp\htdocs\beersty\config\" /E /I /Y /Q >nul 2>&1
    xcopy "includes" "C:\xampp\htdocs\beersty\includes\" /E /I /Y /Q >nul 2>&1
    xcopy "assets" "C:\xampp\htdocs\beersty\assets\" /E /I /Y /Q >nul 2>&1
    xcopy "auth" "C:\xampp\htdocs\beersty\auth\" /E /I /Y /Q >nul 2>&1
    xcopy "admin" "C:\xampp\htdocs\beersty\admin\" /E /I /Y /Q >nul 2>&1
    xcopy "brewery" "C:\xampp\htdocs\beersty\brewery\" /E /I /Y /Q >nul 2>&1
    xcopy "breweries" "C:\xampp\htdocs\beersty\breweries\" /E /I /Y /Q >nul 2>&1
    xcopy "database" "C:\xampp\htdocs\beersty\database\" /E /I /Y /Q >nul 2>&1
    xcopy "uploads" "C:\xampp\htdocs\beersty\uploads\" /E /I /Y /Q >nul 2>&1
    
    echo ✓ Beersty files copied
) else (
    echo ✓ Beersty files already in place
)

REM Set permissions
icacls "C:\xampp\htdocs\beersty\uploads" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo.
echo ========================================
echo      Configuration Complete!
echo ========================================
echo.
echo Apache has been configured to serve Beersty on port 8080
echo.
echo IMPORTANT: You need to restart Apache for changes to take effect!
echo.
echo New URLs:
echo   Application: http://localhost:8080/
echo   Login:       http://localhost:8080/auth/login.php
echo   Admin:       http://localhost:8080/admin/dashboard.php
echo.
echo Steps to complete setup:
echo 1. Restart Apache in XAMPP Control Panel
echo 2. Visit: http://localhost:8080/setup-db-simple.php
echo 3. Login with: <EMAIL> / admin123
echo.

set /p restart="Restart Apache now? (y/n): "
if /i "%restart%"=="y" (
    echo Stopping Apache...
    taskkill /F /IM httpd.exe >nul 2>&1
    timeout /t 2 >nul
    
    echo Starting Apache...
    start "" "C:\xampp\apache\bin\httpd.exe"
    timeout /t 3 >nul
    
    echo ✓ Apache restarted
)

echo.
set /p openapp="Open Beersty on port 8080? (y/n): "
if /i "%openapp%"=="y" (
    timeout /t 2 >nul
    start "" "http://localhost:8080/"
)

echo.
echo Happy brewing on port 8080! 🍺
pause
