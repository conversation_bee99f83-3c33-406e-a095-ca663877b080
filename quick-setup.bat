@echo off
echo Setting up Beersty for XAMPP...
echo.

REM Create beersty directory in XAMPP htdocs
if not exist "C:\xampp\htdocs\beersty" (
    mkdir "C:\xampp\htdocs\beersty"
    echo Created directory: C:\xampp\htdocs\beersty
)

REM Copy all files to XAMPP htdocs
echo Copying files to XAMPP...
xcopy "%~dp0*" "C:\xampp\htdocs\beersty\" /E /I /Y /Q
echo Files copied successfully!

REM Create uploads directory with permissions
if not exist "C:\xampp\htdocs\beersty\uploads" (
    mkdir "C:\xampp\htdocs\beersty\uploads"
)

echo.
echo Setup complete! 
echo.
echo Next steps:
echo 1. Make sure XAMPP Apache and MySQL are running
echo 2. Open: http://localhost/beersty/
echo 3. Run database setup if needed
echo.
pause
