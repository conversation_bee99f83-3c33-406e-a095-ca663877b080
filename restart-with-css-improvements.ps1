# Restart Beersty with CSS Improvements
Write-Host "=== Restarting Beersty with CSS Improvements ===" -ForegroundColor Green

# Kill existing PHP development server
Write-Host "Stopping existing development server..." -ForegroundColor Yellow
Get-Process -Name "php" -ErrorAction SilentlyContinue | Stop-Process -Force

# Ensure XAMPP PHP is in PATH
$env:PATH = "C:\xampp\php;$env:PATH"

# Check MySQL status
$mysql = tasklist /FI "IMAGENAME eq mysqld.exe" 2>$null
if ($mysql -like "*mysqld.exe*") {
    Write-Host "MySQL: Running ✓" -ForegroundColor Green
} else {
    Write-Host "MySQL: Not Running - Starting XAMPP..." -ForegroundColor Red
    if (Test-Path "C:\xampp\xampp-control.exe") {
        Start-Process "C:\xampp\xampp-control.exe"
        Write-Host "Please start MySQL in XAMPP Control Panel and press Enter..." -ForegroundColor Yellow
        Read-Host
    }
}

# Display improvements made
Write-Host "`n=== CSS Improvements Applied ===" -ForegroundColor Cyan
Write-Host "✓ Brewery-themed color palette" -ForegroundColor Green
Write-Host "✓ Improved table readability" -ForegroundColor Green
Write-Host "✓ Better user ID display (truncated with hover)" -ForegroundColor Green
Write-Host "✓ Enhanced typography and spacing" -ForegroundColor Green
Write-Host "✓ Responsive design improvements" -ForegroundColor Green
Write-Host "✓ Better button and form styling" -ForegroundColor Green

# Start development server
Write-Host "`nStarting development server with improvements..." -ForegroundColor Yellow
Write-Host "Access your improved admin panel at:" -ForegroundColor Green
Write-Host "http://localhost:8000/admin/user-management.php" -ForegroundColor White

Start-Sleep -Seconds 2
C:\xampp\php\php.exe -S localhost:8000
