<?php
/**
 * XAMPP Database Setup - Fresh Start
 * Creates database and users for Beersty application using XAMPP
 */

echo "<h1>🔧 XAMPP Fresh Database Setup</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Step 1: Testing XAMPP MySQL Connection</h2>";

try {
    // Test XAMPP MySQL connection (default: localhost, root, no password)
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $conn = new PDO($dsn, 'root', '');
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ XAMPP MySQL connection successful</p>";
    
    // Get MySQL version
    $stmt = $conn->prepare("SELECT VERSION() as version");
    $stmt->execute();
    $version = $stmt->fetch();
    echo "<p>🐬 MySQL Version: " . htmlspecialchars($version['version']) . "</p>";
    
    echo "<h2>📊 Step 2: Creating Database</h2>";
    
    // Drop existing database if it exists
    $conn->exec("DROP DATABASE IF EXISTS beersty_db");
    echo "<p>🗑️ Dropped existing database (if any)</p>";
    
    // Create fresh database
    $conn->exec("CREATE DATABASE beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ Database 'beersty_db' created fresh</p>";
    
    // Switch to the new database
    $conn->exec("USE beersty_db");
    echo "<p>✅ Connected to beersty_db</p>";
    
    echo "<h2>🏗️ Step 3: Creating Tables</h2>";
    
    // Create users table
    $usersTable = "
    CREATE TABLE users (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL
    )";
    $conn->exec($usersTable);
    echo "<p>✅ Users table created</p>";
    
    // Create profiles table
    $profilesTable = "
    CREATE TABLE profiles (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(100) NULL,
        first_name VARCHAR(100) NULL,
        last_name VARCHAR(100) NULL,
        avatar VARCHAR(255) NULL,
        bio TEXT NULL,
        phone VARCHAR(20) NULL,
        city VARCHAR(100) NULL,
        state VARCHAR(100) NULL,
        country VARCHAR(100) NULL,
        role VARCHAR(50) DEFAULT 'customer',
        brewery_id VARCHAR(36) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $conn->exec($profilesTable);
    echo "<p>✅ Profiles table created</p>";
    
    // Create breweries table
    $breweriesTable = "
    CREATE TABLE breweries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        city VARCHAR(100),
        state VARCHAR(50),
        zip VARCHAR(20),
        phone VARCHAR(50),
        email VARCHAR(255),
        website VARCHAR(255),
        description TEXT,
        brewery_type VARCHAR(50) DEFAULT 'micro',
        verified BOOLEAN DEFAULT 0,
        claimed BOOLEAN DEFAULT 0,
        claimable BOOLEAN DEFAULT 1,
        follower_count INT DEFAULT 0,
        like_count INT DEFAULT 0,
        external_id VARCHAR(100),
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->exec($breweriesTable);
    echo "<p>✅ Breweries table created</p>";
    
    echo "<h2>👥 Step 4: Creating Fresh Users</h2>";
    
    // Create users with fresh IDs
    $users = [
        [
            'id' => 'admin-' . uniqid(),
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'role' => 'admin',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'username' => 'admin'
        ],
        [
            'id' => 'brewery-' . uniqid(),
            'email' => '<EMAIL>',
            'password' => 'brewery123',
            'role' => 'brewery',
            'first_name' => 'Brewery',
            'last_name' => 'Demo',
            'username' => 'brewerydemo'
        ],
        [
            'id' => 'customer-' . uniqid(),
            'email' => '<EMAIL>',
            'password' => 'customer123',
            'role' => 'customer',
            'first_name' => 'Customer',
            'last_name' => 'Demo',
            'username' => 'customerdemo'
        ],
        [
            'id' => 'user-' . uniqid(),
            'email' => '<EMAIL>',
            'password' => 'user123',
            'role' => 'customer',
            'first_name' => 'User',
            'last_name' => 'Demo',
            'username' => 'userdemo'
        ]
    ];
    
    foreach ($users as $userData) {
        echo "<p>➕ Creating user {$userData['email']}...</p>";
        
        $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
        
        // Insert user
        $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
        $stmt->execute([$userData['id'], $userData['email'], $passwordHash]);
        
        // Insert profile
        $stmt = $conn->prepare("INSERT INTO profiles (id, email, username, first_name, last_name, role) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$userData['id'], $userData['email'], $userData['username'], $userData['first_name'], $userData['last_name'], $userData['role']]);
        
        echo "<p>✅ User {$userData['email']} created successfully</p>";
    }
    
    echo "<h2>🍺 Step 5: Adding Sample Breweries</h2>";

    $sampleBreweries = [
        ['name' => 'Demo Brewery', 'city' => 'Demo City', 'state' => 'CA', 'brewery_type' => 'micro', 'description' => 'A demo brewery for testing the Beersty platform features.', 'claimed' => 1, 'verified' => 1],
        ['name' => 'Test Brewing Co', 'city' => 'Test Town', 'state' => 'NY', 'brewery_type' => 'brewpub', 'description' => 'Test brewery for development purposes.', 'claimed' => 0, 'verified' => 0],
        ['name' => 'Sample Craft Beer', 'city' => 'Sample City', 'state' => 'TX', 'brewery_type' => 'regional', 'description' => 'Sample brewery for demonstration.', 'claimed' => 0, 'verified' => 0]
    ];

    $breweryIds = [];
    foreach ($sampleBreweries as $brewery) {
        $stmt = $conn->prepare("INSERT INTO breweries (name, city, state, brewery_type, description, claimed, verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$brewery['name'], $brewery['city'], $brewery['state'], $brewery['brewery_type'], $brewery['description'], $brewery['claimed'], $brewery['verified']]);
        $breweryIds[] = $conn->lastInsertId();
        echo "<p>✅ Added brewery: {$brewery['name']} (Claimed: " . ($brewery['claimed'] ? 'Yes' : 'No') . ")</p>";
    }

    echo "<h2>🔗 Step 6: Linking Brewery User to Demo Brewery</h2>";

    // Find the brewery demo user and link to first brewery
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $breweryUser = $stmt->fetch();

    if ($breweryUser && !empty($breweryIds)) {
        $stmt = $conn->prepare("UPDATE profiles SET brewery_id = ? WHERE id = ?");
        $stmt->execute([$breweryIds[0], $breweryUser['id']]);
        echo "<p>✅ Linked <EMAIL> to Demo Brewery</p>";
    } else {
        echo "<p>❌ Could not link brewery user to brewery</p>";
    }
    
    echo "<h2>🔐 Step 7: Testing Authentication</h2>";
    
    // Test login for each user
    foreach ($users as $userData) {
        $stmt = $conn->prepare("
            SELECT u.id, u.email, u.password_hash, p.role 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.email = ?
        ");
        $stmt->execute([$userData['email']]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($userData['password'], $user['password_hash'])) {
            echo "<p>✅ {$userData['email']} login test PASSED</p>";
        } else {
            echo "<p>❌ {$userData['email']} login test FAILED</p>";
        }
    }
    
    // Get final user count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM breweries");
    $stmt->execute();
    $breweryCount = $stmt->fetch()['count'];
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 XAMPP Database Setup Complete!</h3>";
    echo "<p><strong>Database:</strong> beersty_db (fresh install)</p>";
    echo "<p><strong>Users Created:</strong> $userCount</p>";
    echo "<p><strong>Breweries Added:</strong> $breweryCount</p>";
    echo "<h4>🔑 Login Credentials:</h4>";
    echo "<ul>";
    foreach ($users as $user) {
        echo "<li><strong>{$user['role']}:</strong> {$user['email']} / {$user['password']}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Troubleshooting:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP is running (Apache and MySQL started)</li>";
    echo "<li>Check XAMPP Control Panel - both services should be green</li>";
    echo "<li>Try accessing phpMyAdmin: <a href='http://localhost/phpmyadmin'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Restart XAMPP services if needed</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='color: #007bff;'>🔐 Try Login Now</a></li>";
echo "<li><a href='test-database-connection.php' style='color: #007bff;'>🔍 Test Database Connection</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' style='color: #007bff;'>🐬 Open phpMyAdmin</a></li>";
echo "<li><a href='/' style='color: #007bff;'>🏠 Back to Homepage</a></li>";
echo "</ul>";

echo "<h2>📋 XAMPP Status Check</h2>";
echo "<p>If login still doesn't work, check:</p>";
echo "<ul>";
echo "<li>XAMPP Control Panel - Apache and MySQL should be running (green)</li>";
echo "<li>Your PHP server on port 8000 should be running</li>";
echo "<li>Database connection settings in config/database.php</li>";
echo "</ul>";
?>
