# 🐧 Multi-User Database Setup for Kali Linux

This guide explains how to set up a cohesive database structure for multiple users on a Kali Linux system accessing the Beersty application.

## 🎯 Overview

The multi-user setup creates a shared database structure that allows multiple users on the same Kali Linux system to:
- Share the same database and user accounts
- Maintain data consistency across all users
- Access shared file uploads
- Work collaboratively without data conflicts

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

Run the automated setup script:

```bash
./setup-kali-multiuser.sh
```

This script will:
1. Create shared directories in `/opt/beersty-shared/`
2. Set up proper group permissions
3. Create a shared database file
4. Link your project to the shared structure

### Option 2: Web-Based Setup

1. Start your PHP server:
   ```bash
   php -S localhost:8000 router.php
   ```

2. Open in browser:
   ```
   http://localhost:8000/setup-multi-user-database.php
   ```

3. Follow the web interface instructions

## 📁 Directory Structure

After setup, the following structure is created:

```
/opt/beersty-shared/
├── database/
│   ├── beersty_shared.json     # Shared database file
│   └── beersty_shared.lock     # File locking for concurrent access
├── config/
│   └── shared_database.php     # Shared database configuration
└── uploads/                    # Shared file uploads

Your Project Directory/
├── database -> /opt/beersty-shared/database/    # Symlink
├── uploads -> /opt/beersty-shared/uploads/      # Symlink
└── ... (other project files)
```

## 👥 User Management

### System Group

All users are added to the `beersty-users` group with appropriate permissions:

```bash
# Check if you're in the group
groups $USER | grep beersty-users

# If not, the setup script will add you
sudo usermod -a -G beersty-users $USER
```

### Adding New Users

When a new user needs access:

1. **Add them to the group:**
   ```bash
   sudo usermod -a -G beersty-users newusername
   ```

2. **Run setup from their project directory:**
   ```bash
   cd /path/to/their/beersty-project
   ./setup-kali-multiuser.sh
   ```

## 🔐 Login Credentials

The shared database includes a default admin account:

- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** `admin`

All users share these same login credentials.

## 🔧 Technical Details

### Database Implementation

- **Type:** JSON-based file database with file locking
- **Concurrency:** Uses `flock()` for safe concurrent access
- **Backup:** Automatic backup of existing databases before linking
- **Compatibility:** Drop-in replacement for PDO-style database operations

### File Permissions

```bash
# Shared directories
/opt/beersty-shared/     # 775 (rwxrwxr-x)
├── database/            # 775 with setgid
├── config/              # 775 with setgid  
└── uploads/             # 775 with setgid

# Database file
beersty_shared.json      # 664 (rw-rw-r--)
```

### Security Considerations

- All users in the `beersty-users` group can read/write the database
- File locking prevents data corruption during concurrent access
- Shared uploads directory allows collaborative file management
- Regular backups are created automatically

## 🛠️ Troubleshooting

### Permission Issues

If you get permission errors:

```bash
# Check group membership
groups $USER

# Re-login if recently added to group
# Or run: newgrp beersty-users

# Check file permissions
ls -la /opt/beersty-shared/database/
```

### Database Access Issues

If the application can't access the database:

```bash
# Check if symlink exists
ls -la database/

# Check if shared database file exists
ls -la /opt/beersty-shared/database/beersty_shared.json

# Test file access
cat /opt/beersty-shared/database/beersty_shared.json
```

### Restoring Individual Database

If you need to restore a user's individual database:

```bash
# Remove symlink
rm database

# Restore from backup (if exists)
mv database_backup_YYYYMMDD_HHMMSS database

# Or create new individual database
mkdir database
# Run individual setup...
```

## 🔄 Migration Between Setups

### From Individual to Shared

The setup script automatically:
1. Backs up your existing database
2. Creates symlinks to shared structure
3. Preserves your data in backup files

### From Shared to Individual

```bash
# Remove symlinks
rm database uploads

# Restore from backup or create new
mv database_backup_YYYYMMDD_HHMMSS database
mv uploads_backup_YYYYMMDD_HHMMSS uploads
```

## 📊 Monitoring and Maintenance

### Check Database Status

```bash
# View database metadata
cat /opt/beersty-shared/database/beersty_shared.json | grep -A 10 metadata

# Check file locks
lsof /opt/beersty-shared/database/beersty_shared.lock

# Monitor file access
sudo tail -f /var/log/syslog | grep beersty
```

### Backup Strategy

```bash
# Manual backup
cp /opt/beersty-shared/database/beersty_shared.json \
   /opt/beersty-shared/database/backup_$(date +%Y%m%d_%H%M%S).json

# Automated backup (add to crontab)
0 2 * * * cp /opt/beersty-shared/database/beersty_shared.json /opt/beersty-shared/database/backup_$(date +\%Y\%m\%d).json
```

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all users are in the `beersty-users` group
3. Ensure proper file permissions on shared directories
4. Test database access with the web setup tool
5. Check system logs for permission or file access errors

## 📝 Notes

- **Group Changes:** Users may need to log out and back in after being added to the group
- **Concurrent Access:** The system handles multiple users accessing the database simultaneously
- **Data Consistency:** All users see the same data in real-time
- **File Uploads:** Shared uploads directory allows collaborative file management
- **Scalability:** Suitable for small teams (2-10 users) on the same system
