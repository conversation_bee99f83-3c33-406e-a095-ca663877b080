<?php
/**
 * Save Deal API
 * Handles saving deals to user's saved deals list
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Get current user
$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['deal_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing deal_id']);
    exit;
}

$dealId = intval($input['deal_id']);

try {
    $conn->beginTransaction();
    
    // Check if deal exists and is active
    $stmt = $conn->prepare("
        SELECT d.*, p.name as place_name 
        FROM deals d 
        JOIN places p ON d.place_id = p.id 
        WHERE d.id = ? AND d.status = 'active' AND d.valid_until >= CURDATE()
    ");
    $stmt->execute([$dealId]);
    $deal = $stmt->fetch();
    
    if (!$deal) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Deal not found or expired']);
        exit;
    }
    
    // Check if user has already saved this deal
    $stmt = $conn->prepare("SELECT id FROM saved_deals WHERE user_id = ? AND deal_id = ?");
    $stmt->execute([$currentUser['id'], $dealId]);
    $existingSave = $stmt->fetch();
    
    if ($existingSave) {
        echo json_encode(['success' => false, 'message' => 'Deal already saved']);
        exit;
    }
    
    // Save the deal
    $stmt = $conn->prepare("
        INSERT INTO saved_deals (user_id, deal_id, saved_at) 
        VALUES (?, ?, NOW())
    ");
    $stmt->execute([$currentUser['id'], $dealId]);
    
    // Update save count for the deal
    $stmt = $conn->prepare("UPDATE deals SET save_count = save_count + 1 WHERE id = ?");
    $stmt->execute([$dealId]);
    
    // Log activity
    $stmt = $conn->prepare("
        INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata, created_at) 
        VALUES (?, 'deal_save', 'deal', ?, ?, NOW())
    ");
    $stmt->execute([
        $currentUser['id'], 
        $dealId, 
        json_encode([
            'deal_title' => $deal['title'],
            'place_name' => $deal['place_name']
        ])
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Deal saved successfully'
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    error_log("Save deal error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
