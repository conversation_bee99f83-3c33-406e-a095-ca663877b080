<?php
/**
 * Messages API
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';
require_once '../includes/auth_functions.php';
require_once '../includes/MessagingService.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$user = getCurrentUser();
$userId = $user['id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    $messagingService = new MessagingService($conn);
    
    switch ($method) {
        case 'GET':
            handleGetMessages($messagingService, $userId);
            break;
            
        case 'POST':
            handlePostMessages($messagingService, $userId);
            break;
            
        case 'PUT':
            handlePutMessages($messagingService, $userId);
            break;
            
        case 'DELETE':
            handleDeleteMessages($messagingService, $userId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Messages API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Handle GET requests - fetch conversations and messages
 */
function handleGetMessages($messagingService, $userId) {
    $action = $_GET['action'] ?? 'conversations';
    
    switch ($action) {
        case 'conversations':
            $limit = min((int)($_GET['limit'] ?? 20), 50);
            $conversations = $messagingService->getUserConversations($userId, $limit);
            
            echo json_encode([
                'success' => true,
                'conversations' => $conversations
            ]);
            break;
            
        case 'messages':
            $conversationId = sanitizeInput($_GET['conversation_id'] ?? '');
            
            if (empty($conversationId)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Conversation ID required']);
                return;
            }
            
            $limit = min((int)($_GET['limit'] ?? 50), 100);
            $offset = max((int)($_GET['offset'] ?? 0), 0);
            
            $messages = $messagingService->getConversationMessages($conversationId, $userId, $limit, $offset);
            
            echo json_encode([
                'success' => true,
                'messages' => $messages
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * Handle POST requests - create conversations and send messages
 */
function handlePostMessages($messagingService, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = sanitizeInput($input['action'] ?? '');
    
    switch ($action) {
        case 'create_conversation':
            $targetUserId = sanitizeInput($input['target_user_id'] ?? '');
            $type = sanitizeInput($input['type'] ?? 'direct');
            $title = sanitizeInput($input['title'] ?? '');
            $description = sanitizeInput($input['description'] ?? '');
            
            if (empty($targetUserId) && $type === 'direct') {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Target user ID required for direct conversation']);
                return;
            }
            
            if ($type === 'direct') {
                // Get or create direct conversation
                $conversationId = $messagingService->getOrCreateDirectConversation($userId, $targetUserId);
            } else {
                // Create group conversation
                $conversationId = $messagingService->createConversation($userId, $type, $title, $description);
                
                // Add target user if specified
                if (!empty($targetUserId)) {
                    $messagingService->addParticipant($conversationId, $targetUserId);
                }
            }
            
            if ($conversationId) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Conversation created successfully',
                    'conversation_id' => $conversationId
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to create conversation']);
            }
            break;
            
        case 'send_message':
            $conversationId = sanitizeInput($input['conversation_id'] ?? '');
            $content = sanitizeInput($input['content'] ?? '');
            $messageType = sanitizeInput($input['message_type'] ?? 'text');
            $attachments = $input['attachments'] ?? null;
            
            if (empty($conversationId) || empty($content)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Conversation ID and content required']);
                return;
            }
            
            // Validate message content
            if (strlen($content) > 2000) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message too long (max 2000 characters)']);
                return;
            }
            
            $messageId = $messagingService->sendMessage($conversationId, $userId, $content, $messageType, $attachments);
            
            if ($messageId) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Message sent successfully',
                    'message_id' => $messageId
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to send message']);
            }
            break;
            
        case 'start_direct_message':
            $targetUserId = sanitizeInput($input['target_user_id'] ?? '');
            $initialMessage = sanitizeInput($input['message'] ?? '');
            
            if (empty($targetUserId)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Target user ID required']);
                return;
            }
            
            // Check if target user allows messages
            $db = new Database();
            $conn = $db->getConnection();
            $stmt = $conn->prepare("SELECT allow_messages FROM profiles WHERE id = ?");
            $stmt->execute([$targetUserId]);
            $targetUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$targetUser || !$targetUser['allow_messages']) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'User does not accept messages']);
                return;
            }
            
            // Get or create conversation
            $conversationId = $messagingService->getOrCreateDirectConversation($userId, $targetUserId);
            
            if ($conversationId && !empty($initialMessage)) {
                // Send initial message
                $messageId = $messagingService->sendMessage($conversationId, $userId, $initialMessage);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Conversation started successfully',
                    'conversation_id' => $conversationId,
                    'message_id' => $messageId
                ]);
            } elseif ($conversationId) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Conversation ready',
                    'conversation_id' => $conversationId
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to start conversation']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * Handle PUT requests - update messages and conversations
 */
function handlePutMessages($messagingService, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = sanitizeInput($input['action'] ?? '');
    
    switch ($action) {
        case 'flag_message':
            $messageId = sanitizeInput($input['message_id'] ?? '');
            $reason = sanitizeInput($input['reason'] ?? '');
            
            if (empty($messageId) || empty($reason)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Message ID and reason required']);
                return;
            }
            
            $success = $messagingService->flagMessage($messageId, $userId, $reason);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Message flagged for moderation'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to flag message']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * Handle DELETE requests - delete messages
 */
function handleDeleteMessages($messagingService, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $messageId = sanitizeInput($input['message_id'] ?? '');
    
    if (empty($messageId)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Message ID required']);
        return;
    }
    
    $success = $messagingService->deleteMessage($messageId, $userId);
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to delete message']);
    }
}
?>
