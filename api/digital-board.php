<?php
/**
 * Digital Beer Board API
 * Handles digital board management operations
 */

require_once '../config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$user = getCurrentUser();
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGet($conn, $action, $user);
            break;
        case 'POST':
            handlePost($conn, $action, $user);
            break;
        case 'PUT':
            handlePut($conn, $action, $user);
            break;
        case 'DELETE':
            handleDelete($conn, $action, $user);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Digital board API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGet($conn, $action, $user) {
    switch ($action) {
        case 'board_info':
            getBoardInfo($conn, $user);
            break;
        case 'menu_data':
            getMenuData($conn, $user);
            break;
        case 'board_stats':
            getBoardStats($conn, $user);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
}

function handlePost($conn, $action, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'create_board':
            createBoard($conn, $input, $user);
            break;
        case 'toggle_board':
            toggleBoard($conn, $input, $user);
            break;
        case 'update_settings':
            updateSettings($conn, $input, $user);
            break;
        case 'toggle_beer':
            toggleBeerAvailability($conn, $input, $user);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
}

function getBoardInfo($conn, $user) {
    $breweryId = $_GET['brewery_id'] ?? $user['brewery_id'];
    
    if (!$breweryId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Brewery ID required']);
        return;
    }
    
    // Check permissions
    if ($user['role'] !== 'admin' && $user['brewery_id'] !== $breweryId) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        return;
    }
    
    $query = "
        SELECT db.*, b.name as brewery_name
        FROM digital_boards db
        JOIN breweries b ON db.brewery_id = b.id
        WHERE db.brewery_id = ?
    ";
    $stmt = $conn->prepare($query);
    $stmt->execute([$breweryId]);
    $board = $stmt->fetch();
    
    if ($board) {
        $board['settings'] = json_decode($board['settings'], true);
    }
    
    echo json_encode([
        'success' => true,
        'board' => $board
    ]);
}

function getMenuData($conn, $user) {
    $breweryId = $_GET['brewery_id'] ?? $user['brewery_id'];
    
    if (!$breweryId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Brewery ID required']);
        return;
    }
    
    $query = "
        SELECT bm.*, bs.name as style_name 
        FROM beer_menu bm
        LEFT JOIN beer_styles bs ON bm.style_id = bs.id
        WHERE bm.brewery_id = ?
        ORDER BY bm.tap_number ASC, bm.name ASC
    ";
    $stmt = $conn->prepare($query);
    $stmt->execute([$breweryId]);
    $menu = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'menu' => $menu,
        'last_updated' => date('c')
    ]);
}

function getBoardStats($conn, $user) {
    $breweryId = $_GET['brewery_id'] ?? $user['brewery_id'];
    
    if (!$breweryId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Brewery ID required']);
        return;
    }
    
    $statsQuery = "
        SELECT 
            COUNT(*) as total_beers,
            COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_beers,
            COUNT(CASE WHEN tap_number IS NOT NULL THEN 1 END) as on_tap,
            AVG(abv) as avg_abv,
            AVG(ibu) as avg_ibu,
            MIN(price) as min_price,
            MAX(price) as max_price
        FROM beer_menu 
        WHERE brewery_id = ?
    ";
    $stmt = $conn->prepare($statsQuery);
    $stmt->execute([$breweryId]);
    $stats = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'stats' => $stats
    ]);
}

function createBoard($conn, $input, $user) {
    $breweryId = $input['brewery_id'] ?? $user['brewery_id'];
    
    if (!$breweryId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Brewery ID required']);
        return;
    }
    
    // Check permissions
    if ($user['role'] !== 'admin' && $user['brewery_id'] !== $breweryId) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        return;
    }
    
    // Generate unique board ID
    $boardId = bin2hex(random_bytes(16));
    
    // Default settings
    $defaultSettings = [
        'theme' => 'dark',
        'layout' => 'grid',
        'refresh_interval' => 300,
        'show_prices' => true,
        'show_descriptions' => true,
        'ticker_message' => 'Welcome to our brewery! Enjoy our fresh craft beers.',
        'auto_rotate' => false,
        'display_time' => 10
    ];
    
    $query = "
        INSERT INTO digital_boards (brewery_id, board_id, settings, is_active, created_at, updated_at)
        VALUES (?, ?, ?, 1, NOW(), NOW())
    ";
    $stmt = $conn->prepare($query);
    $stmt->execute([$breweryId, $boardId, json_encode($defaultSettings)]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Digital board created successfully',
        'board_id' => $boardId,
        'board_url' => getBaseUrl() . "/business/digital-board/display.php?brewery_id={$breweryId}&board_id={$boardId}"
    ]);
}

function toggleBoard($conn, $input, $user) {
    $boardId = $input['board_id'] ?? null;
    $isActive = $input['is_active'] ?? false;
    
    if (!$boardId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Board ID required']);
        return;
    }
    
    // Check permissions
    if ($user['role'] !== 'admin') {
        $checkQuery = "SELECT brewery_id FROM digital_boards WHERE id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->execute([$boardId]);
        $board = $stmt->fetch();
        
        if (!$board || $board['brewery_id'] !== $user['brewery_id']) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }
    }
    
    $query = "UPDATE digital_boards SET is_active = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$isActive ? 1 : 0, $boardId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Board status updated successfully'
    ]);
}

function updateSettings($conn, $input, $user) {
    $boardId = $input['board_id'] ?? null;
    $settings = $input['settings'] ?? [];
    
    if (!$boardId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Board ID required']);
        return;
    }
    
    // Check permissions
    if ($user['role'] !== 'admin') {
        $checkQuery = "SELECT brewery_id FROM digital_boards WHERE id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->execute([$boardId]);
        $board = $stmt->fetch();
        
        if (!$board || $board['brewery_id'] !== $user['brewery_id']) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }
    }
    
    // Validate settings
    $validatedSettings = [
        'theme' => in_array($settings['theme'] ?? 'dark', ['dark', 'light']) ? $settings['theme'] : 'dark',
        'layout' => in_array($settings['layout'] ?? 'grid', ['grid', 'list']) ? $settings['layout'] : 'grid',
        'refresh_interval' => max(60, min(3600, intval($settings['refresh_interval'] ?? 300))),
        'show_prices' => (bool)($settings['show_prices'] ?? true),
        'show_descriptions' => (bool)($settings['show_descriptions'] ?? true),
        'ticker_message' => substr($settings['ticker_message'] ?? '', 0, 200),
        'auto_rotate' => (bool)($settings['auto_rotate'] ?? false),
        'display_time' => max(5, min(60, intval($settings['display_time'] ?? 10)))
    ];
    
    $query = "UPDATE digital_boards SET settings = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([json_encode($validatedSettings), $boardId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Settings updated successfully',
        'settings' => $validatedSettings
    ]);
}

function toggleBeerAvailability($conn, $input, $user) {
    $beerId = $input['beer_id'] ?? null;
    $isAvailable = $input['is_available'] ?? false;
    
    if (!$beerId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Beer ID required']);
        return;
    }
    
    // Check permissions
    if ($user['role'] !== 'admin') {
        $checkQuery = "SELECT brewery_id FROM beer_menu WHERE id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->execute([$beerId]);
        $beer = $stmt->fetch();
        
        if (!$beer || $beer['brewery_id'] !== $user['brewery_id']) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Access denied']);
            return;
        }
    }
    
    $query = "UPDATE beer_menu SET is_available = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$isAvailable ? 1 : 0, $beerId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Beer availability updated successfully'
    ]);
}

function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname(dirname($_SERVER['SCRIPT_NAME']));
    return $protocol . '://' . $host . $path;
}
?>
