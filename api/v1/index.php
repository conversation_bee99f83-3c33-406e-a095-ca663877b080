<?php
/**
 * Beersty Public API v1
 * Phase 10: Advanced Features & API Development
 * Main API router and handler
 */

require_once '../../config/config.php';
require_once '../../includes/ApiService.php';

// Set JSON content type
header('Content-Type: application/json');

// Initialize API service
$apiService = new ApiService((new Database())->getConnection());

// Handle CORS
$apiService->setCorsHeaders();

// Get request details
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Remove 'beersty', 'api', 'v1' from path
$pathParts = array_slice($pathParts, 3);
$endpoint = implode('/', $pathParts);

// Get headers
$headers = getallheaders() ?: [];

// Start timing
$startTime = microtime(true);

try {
    // Authenticate request
    $auth = $apiService->authenticateRequest($headers);
    
    if (!$auth['success']) {
        http_response_code($auth['code']);
        echo json_encode($apiService->formatResponse($auth['error'], false));
        exit;
    }
    
    // Route the request
    $response = routeRequest($endpoint, $method, $auth, $apiService);
    
    // Calculate response time
    $responseTime = round((microtime(true) - $startTime) * 1000, 2);
    
    // Log the request
    $apiService->logRequest(
        $auth['api_key_id'],
        $endpoint,
        $method,
        http_response_code(),
        $responseTime
    );
    
    // Send response
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode($apiService->formatResponse('Internal server error', false));
}

/**
 * Route API requests to appropriate handlers
 */
function routeRequest($endpoint, $method, $auth, $apiService) {
    global $conn;
    
    // Split endpoint into parts
    $parts = explode('/', $endpoint);
    $resource = $parts[0] ?? '';
    $id = $parts[1] ?? null;
    $subResource = $parts[2] ?? null;
    
    switch ($resource) {
        case 'beers':
            return handleBeersEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'breweries':
            return handleBreweriesEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'users':
            return handleUsersEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'checkins':
            return handleCheckinsEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'ratings':
            return handleRatingsEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'search':
            return handleSearchEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'stats':
            return handleStatsEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case 'export':
            return handleExportEndpoint($method, $id, $subResource, $auth, $apiService);
            
        case '':
            return handleRootEndpoint($method, $auth, $apiService);
            
        default:
            http_response_code(404);
            return $apiService->formatResponse('Endpoint not found', false);
    }
}

/**
 * Handle root endpoint - API information
 */
function handleRootEndpoint($method, $auth, $apiService) {
    if ($method !== 'GET') {
        http_response_code(405);
        return $apiService->formatResponse('Method not allowed', false);
    }
    
    return $apiService->formatResponse([
        'name' => 'Beersty API',
        'version' => 'v1',
        'description' => 'Social beer platform API',
        'documentation' => '/beersty/api/docs',
        'endpoints' => [
            'beers' => 'Beer information and management',
            'breweries' => 'Brewery data and profiles',
            'users' => 'User profiles and social features',
            'checkins' => 'Beer check-ins and activities',
            'ratings' => 'Beer ratings and reviews',
            'search' => 'Search across all content',
            'stats' => 'Platform statistics',
            'export' => 'Data export functionality'
        ],
        'authentication' => 'API key required in X-API-Key header',
        'rate_limits' => [
            'public' => '100 requests/hour',
            'authenticated' => '1000 requests/hour',
            'premium' => '5000 requests/hour'
        ]
    ]);
}

/**
 * Handle beers endpoint
 */
function handleBeersEndpoint($method, $id, $subResource, $auth, $apiService) {
    global $conn;
    
    switch ($method) {
        case 'GET':
            if ($id) {
                if ($subResource === 'ratings') {
                    return getBeerRatings($id, $apiService);
                } elseif ($subResource === 'checkins') {
                    return getBeerCheckins($id, $apiService);
                } else {
                    return getBeer($id, $apiService);
                }
            } else {
                return getBeers($apiService);
            }
            
        case 'POST':
            if (!$apiService->hasPermission($auth['permissions'], 'beers:create')) {
                http_response_code(403);
                return $apiService->formatResponse('Insufficient permissions', false);
            }
            return createBeer($auth, $apiService);
            
        case 'PUT':
            if (!$apiService->hasPermission($auth['permissions'], 'beers:update')) {
                http_response_code(403);
                return $apiService->formatResponse('Insufficient permissions', false);
            }
            return updateBeer($id, $auth, $apiService);
            
        case 'DELETE':
            if (!$apiService->hasPermission($auth['permissions'], 'beers:delete')) {
                http_response_code(403);
                return $apiService->formatResponse('Insufficient permissions', false);
            }
            return deleteBeer($id, $auth, $apiService);
            
        default:
            http_response_code(405);
            return $apiService->formatResponse('Method not allowed', false);
    }
}

/**
 * Get all beers with pagination and filtering
 */
function getBeers($apiService) {
    global $conn;
    
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = min((int)($_GET['limit'] ?? 20), 100); // Max 100 per page
        $offset = ($page - 1) * $limit;
        
        $style = $_GET['style'] ?? null;
        $brewery = $_GET['brewery'] ?? null;
        $minAbv = $_GET['min_abv'] ?? null;
        $maxAbv = $_GET['max_abv'] ?? null;
        $search = $_GET['search'] ?? null;
        
        // Build query
        $where = ['1=1'];
        $params = [];
        
        if ($style) {
            $where[] = 'bs.name LIKE ?';
            $params[] = "%$style%";
        }
        
        if ($brewery) {
            $where[] = 'b.name LIKE ?';
            $params[] = "%$brewery%";
        }
        
        if ($minAbv) {
            $where[] = 'bm.abv >= ?';
            $params[] = $minAbv;
        }
        
        if ($maxAbv) {
            $where[] = 'bm.abv <= ?';
            $params[] = $maxAbv;
        }
        
        if ($search) {
            $where[] = '(bm.name LIKE ? OR bm.description LIKE ?)';
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $countStmt = $conn->prepare("
            SELECT COUNT(*) as total
            FROM beer_menu bm
            JOIN breweries b ON bm.brewery_id = b.id
            LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Get beers
        $stmt = $conn->prepare("
            SELECT 
                bm.id,
                bm.name,
                bm.description,
                bm.abv,
                bm.ibu,
                bm.srm,
                bm.price,
                bm.images,
                b.name as brewery_name,
                b.id as brewery_id,
                bs.name as style_name,
                bs.id as style_id,
                COALESCE(AVG(br.overall_rating), 0) as avg_rating,
                COUNT(br.id) as rating_count
            FROM beer_menu bm
            JOIN breweries b ON bm.brewery_id = b.id
            LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
            LEFT JOIN beer_ratings br ON bm.id = br.beer_id
            WHERE $whereClause
            GROUP BY bm.id
            ORDER BY bm.name
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $beers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format images
        foreach ($beers as &$beer) {
            $beer['images'] = json_decode($beer['images'], true) ?? [];
            $beer['avg_rating'] = (float)$beer['avg_rating'];
            $beer['rating_count'] = (int)$beer['rating_count'];
            $beer['abv'] = (float)$beer['abv'];
            $beer['ibu'] = (int)$beer['ibu'];
            $beer['srm'] = (int)$beer['srm'];
            $beer['price'] = (float)$beer['price'];
        }
        
        $meta = [
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ],
            'filters' => array_filter([
                'style' => $style,
                'brewery' => $brewery,
                'min_abv' => $minAbv,
                'max_abv' => $maxAbv,
                'search' => $search
            ])
        ];
        
        return $apiService->formatResponse($beers, true, null, $meta);
        
    } catch (Exception $e) {
        error_log("Get beers error: " . $e->getMessage());
        http_response_code(500);
        return $apiService->formatResponse('Failed to fetch beers', false);
    }
}

/**
 * Get single beer by ID
 */
function getBeer($id, $apiService) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT 
                bm.*,
                b.name as brewery_name,
                b.id as brewery_id,
                b.address as brewery_address,
                bs.name as style_name,
                bs.description as style_description,
                COALESCE(AVG(br.overall_rating), 0) as avg_rating,
                COUNT(br.id) as rating_count,
                COUNT(bc.id) as checkin_count
            FROM beer_menu bm
            JOIN breweries b ON bm.brewery_id = b.id
            LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
            LEFT JOIN beer_ratings br ON bm.id = br.beer_id
            LEFT JOIN beer_checkins bc ON bm.id = bc.beer_id
            WHERE bm.id = ?
            GROUP BY bm.id
        ");
        
        $stmt->execute([$id]);
        $beer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$beer) {
            http_response_code(404);
            return $apiService->formatResponse('Beer not found', false);
        }
        
        // Format data
        $beer['images'] = json_decode($beer['images'], true) ?? [];
        $beer['avg_rating'] = (float)$beer['avg_rating'];
        $beer['rating_count'] = (int)$beer['rating_count'];
        $beer['checkin_count'] = (int)$beer['checkin_count'];
        $beer['abv'] = (float)$beer['abv'];
        $beer['ibu'] = (int)$beer['ibu'];
        $beer['srm'] = (int)$beer['srm'];
        $beer['price'] = (float)$beer['price'];
        
        return $apiService->formatResponse($beer);
        
    } catch (Exception $e) {
        error_log("Get beer error: " . $e->getMessage());
        http_response_code(500);
        return $apiService->formatResponse('Failed to fetch beer', false);
    }
}

/**
 * Get beer ratings
 */
function getBeerRatings($beerId, $apiService) {
    global $conn;
    
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = min((int)($_GET['limit'] ?? 20), 50);
        $offset = ($page - 1) * $limit;
        
        $stmt = $conn->prepare("
            SELECT 
                br.*,
                CONCAT(p.first_name, ' ', p.last_name) as user_name,
                u.id as user_id
            FROM beer_ratings br
            JOIN users u ON br.user_id = u.id
            JOIN profiles p ON u.id = p.id
            WHERE br.beer_id = ?
            ORDER BY br.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$beerId, $limit, $offset]);
        $ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get total count
        $countStmt = $conn->prepare("SELECT COUNT(*) as total FROM beer_ratings WHERE beer_id = ?");
        $countStmt->execute([$beerId]);
        $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $meta = [
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ];
        
        return $apiService->formatResponse($ratings, true, null, $meta);
        
    } catch (Exception $e) {
        error_log("Get beer ratings error: " . $e->getMessage());
        http_response_code(500);
        return $apiService->formatResponse('Failed to fetch ratings', false);
    }
}

// Additional endpoint handlers would be implemented here...
// For brevity, I'm showing the pattern with beers endpoint

/**
 * Handle breweries endpoint
 */
function handleBreweriesEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation similar to beers endpoint
    return $apiService->formatResponse('Breweries endpoint - implementation in progress', false);
}

/**
 * Handle users endpoint
 */
function handleUsersEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for user profiles and social features
    return $apiService->formatResponse('Users endpoint - implementation in progress', false);
}

/**
 * Handle checkins endpoint
 */
function handleCheckinsEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for check-ins
    return $apiService->formatResponse('Checkins endpoint - implementation in progress', false);
}

/**
 * Handle ratings endpoint
 */
function handleRatingsEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for ratings
    return $apiService->formatResponse('Ratings endpoint - implementation in progress', false);
}

/**
 * Handle search endpoint
 */
function handleSearchEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for search
    return $apiService->formatResponse('Search endpoint - implementation in progress', false);
}

/**
 * Handle stats endpoint
 */
function handleStatsEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for statistics
    return $apiService->formatResponse('Stats endpoint - implementation in progress', false);
}

/**
 * Handle export endpoint
 */
function handleExportEndpoint($method, $id, $subResource, $auth, $apiService) {
    // Implementation for data export
    return $apiService->formatResponse('Export endpoint - implementation in progress', false);
}
?>
