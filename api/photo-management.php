<?php
/**
 * Photo Management API
 * Handle photo uploads, CRUD operations, and album management
 */

require_once '../config/config.php';
requireLogin();

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'upload':
            handleUpload();
            break;
        case 'get':
            handleGet();
            break;
        case 'update':
            handleUpdate();
            break;
        case 'delete':
            handleDelete();
            break;
        case 'bulk_delete':
            handleBulkDelete();
            break;
        case 'get_albums':
            handleGetAlbums();
            break;
        case 'create_album':
            handleCreateAlbum();
            break;
        case 'set_profile_photo':
            handleSetProfilePhoto();
            break;
        case 'set_cover_photo':
            handleSetCoverPhoto();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleUpload() {
    global $pdo;
    
    if (!isset($_FILES['photo']) || $_FILES['photo']['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
        return;
    }
    
    $file = $_FILES['photo'];
    $owner_type = $_POST['owner_type'] ?? 'user';
    $owner_id = $_POST['owner_id'] ?? $_SESSION['user_id'];
    $album_id = $_POST['album_id'] ?? null;
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $alt_text = $_POST['alt_text'] ?? '';
    
    // Validate file
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.']);
        return;
    }
    
    if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'File size exceeds 10MB limit']);
        return;
    }
    
    // Create uploads directory if it doesn't exist
    $upload_dir = '../uploads/photos/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
        return;
    }
    
    // Get image dimensions
    $image_info = getimagesize($filepath);
    $width = $image_info[0] ?? null;
    $height = $image_info[1] ?? null;
    
    // Save to database
    try {
        $stmt = $pdo->prepare("
            INSERT INTO photos (
                id, album_id, owner_type, owner_id, filename, original_filename,
                title, description, alt_text, file_size, mime_type, width, height,
                uploaded_by, created_at
            ) VALUES (
                UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ");
        
        $stmt->execute([
            $album_id,
            $owner_type,
            $owner_id,
            $filename,
            $file['name'],
            $title,
            $description,
            $alt_text,
            $file['size'],
            $file['type'],
            $width,
            $height,
            $_SESSION['user_id']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Photo uploaded successfully',
            'photo_id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        // Delete uploaded file if database insert fails
        unlink($filepath);
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleGet() {
    global $pdo;
    
    $photo_id = $_GET['id'] ?? null;
    
    if (!$photo_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Photo ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                p.*,
                pa.name as album_name,
                u.email as uploaded_by_email,
                CASE 
                    WHEN p.owner_type = 'user' THEN (SELECT email FROM users WHERE id = p.owner_id)
                    WHEN p.owner_type = 'place' THEN (SELECT name FROM breweries WHERE id = p.owner_id)
                END as owner_name
            FROM photos p
            LEFT JOIN photo_albums pa ON p.album_id = pa.id
            LEFT JOIN users u ON p.uploaded_by = u.id
            WHERE p.id = ?
        ");
        
        $stmt->execute([$photo_id]);
        $photo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($photo) {
            echo json_encode(['success' => true, 'photo' => $photo]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Photo not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $photo_id = $input['id'] ?? null;
    
    if (!$photo_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Photo ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowed_fields = ['title', 'description', 'alt_text', 'album_id', 'is_public', 'sort_order'];
        
        foreach ($allowed_fields as $field) {
            if (isset($input[$field])) {
                $fields[] = "$field = ?";
                $values[] = $input[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $photo_id;
        
        $sql = "UPDATE photos SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Photo updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $photo_id = $input['id'] ?? null;
    
    if (!$photo_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Photo ID is required']);
        return;
    }
    
    try {
        // Get photo info before deleting
        $stmt = $pdo->prepare("SELECT filename FROM photos WHERE id = ?");
        $stmt->execute([$photo_id]);
        $photo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$photo) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Photo not found']);
            return;
        }
        
        // Delete from database
        $stmt = $pdo->prepare("DELETE FROM photos WHERE id = ?");
        $stmt->execute([$photo_id]);
        
        // Delete file
        $filepath = '../uploads/photos/' . $photo['filename'];
        if (file_exists($filepath)) {
            unlink($filepath);
        }
        
        echo json_encode(['success' => true, 'message' => 'Photo deleted successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleBulkDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $photo_ids = $input['photo_ids'] ?? [];
    
    if (empty($photo_ids)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No photo IDs provided']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Get filenames before deleting
        $placeholders = str_repeat('?,', count($photo_ids) - 1) . '?';
        $stmt = $pdo->prepare("SELECT filename FROM photos WHERE id IN ($placeholders)");
        $stmt->execute($photo_ids);
        $photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Delete from database
        $stmt = $pdo->prepare("DELETE FROM photos WHERE id IN ($placeholders)");
        $stmt->execute($photo_ids);
        $deleted_count = $stmt->rowCount();
        
        // Delete files
        foreach ($photos as $photo) {
            $filepath = '../uploads/photos/' . $photo['filename'];
            if (file_exists($filepath)) {
                unlink($filepath);
            }
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully deleted $deleted_count photos",
            'deleted_count' => $deleted_count
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

function handleGetAlbums() {
    global $pdo;
    
    $owner_id = $_GET['owner_id'] ?? null;
    
    if (!$owner_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Owner ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, name, description 
            FROM photo_albums 
            WHERE owner_id = ? 
            ORDER BY sort_order, name
        ");
        $stmt->execute([$owner_id]);
        $albums = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'albums' => $albums]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleCreateAlbum() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $name = $input['name'] ?? null;
    $description = $input['description'] ?? '';
    $owner_type = $input['owner_type'] ?? 'user';
    $owner_id = $input['owner_id'] ?? $_SESSION['user_id'];
    
    if (!$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album name is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO photo_albums (id, owner_type, owner_id, name, description, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$owner_type, $owner_id, $name, $description]);
        
        echo json_encode(['success' => true, 'message' => 'Album created successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleSetProfilePhoto() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $photo_id = $input['photo_id'] ?? null;
    $owner_type = $input['owner_type'] ?? null;
    $owner_id = $input['owner_id'] ?? null;
    
    if (!$photo_id || !$owner_type || !$owner_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Photo ID, owner type, and owner ID are required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Clear existing profile photos for this owner
        $stmt = $pdo->prepare("UPDATE photos SET is_profile_photo = 0 WHERE owner_type = ? AND owner_id = ?");
        $stmt->execute([$owner_type, $owner_id]);
        
        // Set new profile photo
        $stmt = $pdo->prepare("UPDATE photos SET is_profile_photo = 1 WHERE id = ?");
        $stmt->execute([$photo_id]);
        
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => 'Profile photo updated successfully']);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

function handleSetCoverPhoto() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $photo_id = $input['photo_id'] ?? null;
    $owner_type = $input['owner_type'] ?? null;
    $owner_id = $input['owner_id'] ?? null;
    
    if (!$photo_id || !$owner_type || !$owner_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Photo ID, owner type, and owner ID are required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Clear existing cover photos for this owner
        $stmt = $pdo->prepare("UPDATE photos SET is_cover_photo = 0 WHERE owner_type = ? AND owner_id = ?");
        $stmt->execute([$owner_type, $owner_id]);
        
        // Set new cover photo
        $stmt = $pdo->prepare("UPDATE photos SET is_cover_photo = 1 WHERE id = ?");
        $stmt->execute([$photo_id]);
        
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => 'Cover photo updated successfully']);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}
?>
