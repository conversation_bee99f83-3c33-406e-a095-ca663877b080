<?php
require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Get search query
$query = sanitizeInput($_GET['q'] ?? '');

if (empty($query) || strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Search beers by name, brewery name, or style
    $searchTerm = "%$query%";
    
    $stmt = $conn->prepare("
        SELECT 
            bm.id,
            bm.name,
            bm.thumbnail,
            bm.abv,
            bm.ibu,
            b.name as brewery_name,
            b.city,
            b.state,
            bs.name as style_name,
            bs.category as style_category
        FROM beer_menu bm
        LEFT JOIN breweries b ON bm.brewery_id = b.id
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
        WHERE bm.available = 1 
        AND (
            bm.name LIKE ? 
            OR b.name LIKE ? 
            OR bs.name LIKE ?
            OR bm.description LIKE ?
        )
        ORDER BY 
            CASE 
                WHEN bm.name LIKE ? THEN 1
                WHEN b.name LIKE ? THEN 2
                WHEN bs.name LIKE ? THEN 3
                ELSE 4
            END,
            bm.average_rating DESC,
            bm.name ASC
        LIMIT 10
    ");
    
    $stmt->execute([
        $searchTerm, $searchTerm, $searchTerm, $searchTerm,
        $searchTerm, $searchTerm, $searchTerm
    ]);
    
    $beers = $stmt->fetchAll();
    
    // Format results
    $results = [];
    foreach ($beers as $beer) {
        $results[] = [
            'id' => $beer['id'],
            'name' => $beer['name'],
            'brewery_name' => $beer['brewery_name'],
            'brewery_location' => trim($beer['city'] . ($beer['city'] && $beer['state'] ? ', ' : '') . $beer['state']),
            'style_name' => $beer['style_name'],
            'style_category' => $beer['style_category'],
            'abv' => $beer['abv'] ? number_format($beer['abv'], 1) : null,
            'ibu' => $beer['ibu'],
            'thumbnail' => $beer['thumbnail']
        ];
    }
    
    echo json_encode($results);
    
} catch (Exception $e) {
    error_log("Beer search API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Search failed']);
}
?>
