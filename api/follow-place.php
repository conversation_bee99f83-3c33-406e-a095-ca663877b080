<?php
/**
 * Follow/Unfollow Place API
 * Handles following and unfollowing places
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Get current user
$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['place_id']) || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

$placeId = intval($input['place_id']);
$action = $input['action'];

if (!in_array($action, ['follow', 'unfollow'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

try {
    $conn->beginTransaction();
    
    // Check if place exists
    $stmt = $conn->prepare("SELECT id, name FROM places WHERE id = ?");
    $stmt->execute([$placeId]);
    $place = $stmt->fetch();
    
    if (!$place) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Place not found']);
        exit;
    }
    
    // Check current follow status
    $stmt = $conn->prepare("SELECT id FROM place_follows WHERE user_id = ? AND place_id = ?");
    $stmt->execute([$currentUser['id'], $placeId]);
    $isFollowing = $stmt->fetch() !== false;
    
    $message = '';
    
    if ($action === 'follow') {
        if ($isFollowing) {
            echo json_encode(['success' => false, 'message' => 'Already following this place']);
            exit;
        }
        
        // Create follow relationship
        $stmt = $conn->prepare("INSERT INTO place_follows (user_id, place_id, created_at) VALUES (?, ?, NOW())");
        $stmt->execute([$currentUser['id'], $placeId]);
        
        // Update follower count
        $stmt = $conn->prepare("UPDATE places SET followers_count = followers_count + 1 WHERE id = ?");
        $stmt->execute([$placeId]);
        
        // Log activity
        $stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata, created_at) 
            VALUES (?, 'place_follow', 'place', ?, ?, NOW())
        ");
        $stmt->execute([
            $currentUser['id'], 
            $placeId, 
            json_encode([
                'place_name' => $place['name']
            ])
        ]);
        
        $message = 'Successfully followed place';
        
    } else { // unfollow
        if (!$isFollowing) {
            echo json_encode(['success' => false, 'message' => 'Not following this place']);
            exit;
        }
        
        // Remove follow relationship
        $stmt = $conn->prepare("DELETE FROM place_follows WHERE user_id = ? AND place_id = ?");
        $stmt->execute([$currentUser['id'], $placeId]);
        
        // Update follower count
        $stmt = $conn->prepare("UPDATE places SET followers_count = GREATEST(0, followers_count - 1) WHERE id = ?");
        $stmt->execute([$placeId]);
        
        $message = 'Successfully unfollowed place';
    }
    
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'action' => $action
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    error_log("Follow place error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
