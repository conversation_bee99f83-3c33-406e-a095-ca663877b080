<?php
/**
 * Coupon Management API
 * Handle coupon CRUD operations and redemptions
 */

require_once '../config/config.php';
requireLogin();

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'create':
            handleCreate();
            break;
        case 'get':
            handleGet();
            break;
        case 'update':
            handleUpdate();
            break;
        case 'delete':
            handleDelete();
            break;
        case 'approve':
            handleApprove();
            break;
        case 'activate':
        case 'deactivate':
            handleToggleStatus();
            break;
        case 'redeem':
            handleRedeem();
            break;
        case 'get_categories':
            handleGetCategories();
            break;
        case 'analytics':
            handleAnalytics();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleCreate() {
    global $pdo;
    
    $place_id = $_POST['place_id'] ?? null;
    $category_id = $_POST['category_id'] ?? null;
    $code = strtoupper(trim($_POST['code'] ?? ''));
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $discount_type = $_POST['discount_type'] ?? null;
    $discount_value = $_POST['discount_value'] ?? null;
    $minimum_purchase = $_POST['minimum_purchase'] ?? 0;
    $maximum_discount = $_POST['maximum_discount'] ?? null;
    $usage_limit = $_POST['usage_limit'] ?? null;
    $usage_limit_per_user = $_POST['usage_limit_per_user'] ?? 1;
    $start_date = $_POST['start_date'] ?? null;
    $end_date = $_POST['end_date'] ?? null;
    $days_of_week = $_POST['days_of_week'] ?? null;
    $start_time = $_POST['start_time'] ?? null;
    $end_time = $_POST['end_time'] ?? null;
    $terms_conditions = trim($_POST['terms_conditions'] ?? '');
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $requires_approval = isset($_POST['requires_approval']) ? 1 : 0;
    
    // Validation
    if (!$place_id || !$code || !$title || !$discount_type || !$discount_value || !$start_date || !$end_date) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Required fields are missing']);
        return;
    }
    
    // Validate discount value
    if ($discount_value <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Discount value must be greater than 0']);
        return;
    }
    
    // Validate percentage discount
    if ($discount_type === 'percentage' && $discount_value > 100) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Percentage discount cannot exceed 100%']);
        return;
    }
    
    // Validate dates
    if (strtotime($end_date) <= strtotime($start_date)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'End date must be after start date']);
        return;
    }
    
    // Check for duplicate coupon code for this place
    try {
        $stmt = $pdo->prepare("SELECT id FROM coupons WHERE place_id = ? AND code = ?");
        $stmt->execute([$place_id, $code]);
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Coupon code already exists for this place']);
            return;
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error checking duplicate code']);
        return;
    }
    
    // Create coupon
    try {
        $stmt = $pdo->prepare("
            INSERT INTO coupons (
                id, place_id, category_id, code, title, description, discount_type, 
                discount_value, minimum_purchase, maximum_discount, usage_limit, 
                usage_limit_per_user, start_date, end_date, days_of_week, start_time, 
                end_time, terms_conditions, is_featured, requires_approval, created_by, 
                created_at
            ) VALUES (
                UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ");
        
        $stmt->execute([
            $place_id, $category_id, $code, $title, $description, $discount_type,
            $discount_value, $minimum_purchase, $maximum_discount, $usage_limit,
            $usage_limit_per_user, $start_date, $end_date, $days_of_week, $start_time,
            $end_time, $terms_conditions, $is_featured, $requires_approval, $_SESSION['user_id']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Coupon created successfully',
            'coupon_id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleGet() {
    global $pdo;
    
    $coupon_id = $_GET['id'] ?? null;
    
    if (!$coupon_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                c.*,
                cc.name as category_name,
                cc.color as category_color,
                b.name as place_name,
                u.email as created_by_email,
                a.email as approved_by_email
            FROM coupons c
            LEFT JOIN coupon_categories cc ON c.category_id = cc.id
            LEFT JOIN breweries b ON c.place_id = b.id
            LEFT JOIN users u ON c.created_by = u.id
            LEFT JOIN users a ON c.approved_by = a.id
            WHERE c.id = ?
        ");
        
        $stmt->execute([$coupon_id]);
        $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($coupon) {
            echo json_encode(['success' => true, 'coupon' => $coupon]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Coupon not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $coupon_id = $_POST['coupon_id'] ?? null;
    
    if (!$coupon_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowed_fields = [
            'title', 'description', 'discount_type', 'discount_value', 'minimum_purchase',
            'maximum_discount', 'usage_limit', 'usage_limit_per_user', 'start_date', 
            'end_date', 'days_of_week', 'start_time', 'end_time', 'terms_conditions',
            'is_active', 'is_featured', 'requires_approval'
        ];
        
        foreach ($allowed_fields as $field) {
            if (isset($_POST[$field])) {
                $fields[] = "$field = ?";
                $values[] = $_POST[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $coupon_id;
        
        $sql = "UPDATE coupons SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Coupon updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $coupon_id = $input['id'] ?? null;
    
    if (!$coupon_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM coupons WHERE id = ?");
        $stmt->execute([$coupon_id]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Coupon deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Coupon not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleApprove() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $coupon_id = $input['id'] ?? null;
    
    if (!$coupon_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            UPDATE coupons 
            SET approved_by = ?, approved_at = NOW(), updated_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $coupon_id]);
        
        echo json_encode(['success' => true, 'message' => 'Coupon approved successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleToggleStatus() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $coupon_id = $input['id'] ?? null;
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (!$coupon_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon ID is required']);
        return;
    }
    
    $is_active = ($action === 'activate') ? 1 : 0;
    
    try {
        $stmt = $pdo->prepare("UPDATE coupons SET is_active = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$is_active, $coupon_id]);
        
        $status = $is_active ? 'activated' : 'deactivated';
        echo json_encode(['success' => true, 'message' => "Coupon $status successfully"]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleRedeem() {
    global $pdo;
    
    $coupon_code = $_POST['coupon_code'] ?? null;
    $place_id = $_POST['place_id'] ?? null;
    $user_id = $_POST['user_id'] ?? $_SESSION['user_id'];
    $order_amount = $_POST['order_amount'] ?? 0;
    
    if (!$coupon_code || !$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Coupon code and place ID are required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Get coupon details
        $stmt = $pdo->prepare("
            SELECT * FROM coupons 
            WHERE code = ? AND place_id = ? AND is_active = 1 
            AND start_date <= CURDATE() AND end_date >= CURDATE()
        ");
        $stmt->execute([$coupon_code, $place_id]);
        $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$coupon) {
            $pdo->rollBack();
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Invalid or expired coupon']);
            return;
        }
        
        // Check usage limits
        if ($coupon['usage_limit']) {
            if ($coupon['used_count'] >= $coupon['usage_limit']) {
                $pdo->rollBack();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Coupon usage limit reached']);
                return;
            }
        }
        
        // Check per-user limit
        if ($coupon['usage_limit_per_user']) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM coupon_redemptions WHERE coupon_id = ? AND user_id = ?");
            $stmt->execute([$coupon['id'], $user_id]);
            $user_usage = $stmt->fetchColumn();
            
            if ($user_usage >= $coupon['usage_limit_per_user']) {
                $pdo->rollBack();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'You have already used this coupon the maximum number of times']);
                return;
            }
        }
        
        // Check minimum purchase
        if ($coupon['minimum_purchase'] > 0 && $order_amount < $coupon['minimum_purchase']) {
            $pdo->rollBack();
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Minimum purchase of $" . number_format($coupon['minimum_purchase'], 2) . " required"]);
            return;
        }
        
        // Calculate discount
        $discount_amount = calculateDiscount($coupon, $order_amount);
        
        // Record redemption
        $stmt = $pdo->prepare("
            INSERT INTO coupon_redemptions (id, coupon_id, user_id, redeemed_at, order_amount, discount_amount, status) 
            VALUES (UUID(), ?, ?, NOW(), ?, ?, 'completed')
        ");
        $stmt->execute([$coupon['id'], $user_id, $order_amount, $discount_amount]);
        
        // Update usage count
        $stmt = $pdo->prepare("UPDATE coupons SET used_count = used_count + 1 WHERE id = ?");
        $stmt->execute([$coupon['id']]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Coupon redeemed successfully',
            'discount_amount' => $discount_amount,
            'final_amount' => max(0, $order_amount - $discount_amount)
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error processing redemption: ' . $e->getMessage()]);
    }
}

function calculateDiscount($coupon, $order_amount) {
    switch ($coupon['discount_type']) {
        case 'percentage':
            $discount = ($order_amount * $coupon['discount_value']) / 100;
            break;
        case 'fixed_amount':
            $discount = $coupon['discount_value'];
            break;
        case 'buy_x_get_y':
            // Simplified BOGO - 50% off
            $discount = $order_amount * 0.5;
            break;
        case 'free_item':
            // Free item value (could be configurable)
            $discount = min($order_amount, $coupon['discount_value'] ?: 10);
            break;
        default:
            $discount = 0;
    }
    
    // Apply maximum discount limit
    if ($coupon['maximum_discount'] && $discount > $coupon['maximum_discount']) {
        $discount = $coupon['maximum_discount'];
    }
    
    // Don't exceed order amount
    return min($discount, $order_amount);
}

function handleGetCategories() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("SELECT * FROM coupon_categories WHERE is_active = 1 ORDER BY sort_order, name");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'categories' => $categories]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleAnalytics() {
    global $pdo;
    
    $place_id = $_GET['place_id'] ?? null;
    $date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
    $date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
    
    try {
        $where_clause = "WHERE cr.redeemed_at BETWEEN ? AND ?";
        $params = [$date_from, $date_to];
        
        if ($place_id) {
            $where_clause .= " AND c.place_id = ?";
            $params[] = $place_id;
        }
        
        // Get redemption analytics
        $sql = "
            SELECT 
                COUNT(*) as total_redemptions,
                SUM(cr.discount_amount) as total_savings,
                AVG(cr.discount_amount) as avg_discount,
                COUNT(DISTINCT cr.user_id) as unique_users,
                COUNT(DISTINCT c.id) as coupons_used
            FROM coupon_redemptions cr
            JOIN coupons c ON cr.coupon_id = c.id
            $where_clause
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $analytics = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'analytics' => $analytics]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
