<?php
require_once '../config/config.php';
require_once '../includes/BadgeService.php';

// Set JSON header
header('Content-Type: application/json');

// Require login
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit;
}

$targetUserId = sanitizeInput($input['user_id'] ?? '');
$action = sanitizeInput($input['action'] ?? '');

// Validation
if (empty($targetUserId)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

if (!in_array($action, ['follow', 'unfollow'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit;
}

$currentUser = getCurrentUser();

// Can't follow yourself
if ($targetUserId === $currentUser['id']) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'You cannot follow yourself']);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if target user exists and has public profile
    $stmt = $conn->prepare("SELECT id, profile_visibility FROM profiles WHERE id = ?");
    $stmt->execute([$targetUserId]);
    $targetUser = $stmt->fetch();
    
    if (!$targetUser) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    if ($targetUser['profile_visibility'] === 'private') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Cannot follow private profiles']);
        exit;
    }
    
    $conn->beginTransaction();
    
    if ($action === 'follow') {
        // Check if already following
        $stmt = $conn->prepare("SELECT id FROM user_follows WHERE follower_id = ? AND following_id = ?");
        $stmt->execute([$currentUser['id'], $targetUserId]);
        
        if ($stmt->fetch()) {
            $conn->rollBack();
            echo json_encode(['success' => false, 'message' => 'Already following this user']);
            exit;
        }
        
        // Create follow relationship
        $stmt = $conn->prepare("INSERT INTO user_follows (follower_id, following_id) VALUES (?, ?)");
        $stmt->execute([$currentUser['id'], $targetUserId]);
        
        // Update follower count
        $stmt = $conn->prepare("UPDATE profiles SET follower_count = follower_count + 1 WHERE id = ?");
        $stmt->execute([$targetUserId]);
        
        // Update following count
        $stmt = $conn->prepare("UPDATE profiles SET following_count = following_count + 1 WHERE id = ?");
        $stmt->execute([$currentUser['id']]);
        
        // Log activity
        $stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata) 
            VALUES (?, 'user_follow', 'user', ?, ?)
        ");
        $stmt->execute([
            $currentUser['id'], 
            $targetUserId, 
            json_encode([
                'target_user_name' => $targetUser['first_name'] . ' ' . $targetUser['last_name']
            ])
        ]);

        // Check for new badges
        $badgeService = new BadgeService($conn);
        $newBadges = $badgeService->checkAndAwardBadges($currentUser['id'], 'user_follow', [
            'target_user_id' => $targetUserId
        ]);

        // Create notification for followed user (Phase 7)
        require_once '../includes/NotificationService.php';
        $notificationService = new NotificationService($conn);
        $followerName = $currentUser['first_name'] . ' ' . $currentUser['last_name'];

        $notificationService->createNotification(
            $targetUserId,
            'new_follower',
            'New Follower',
            "{$followerName} started following you!",
            [
                'follower_id' => $currentUser['id'],
                'follower_name' => $followerName
            ],
            'user',
            $currentUser['id']
        );

        $message = 'Successfully followed user';
        if (!empty($newBadges)) {
            $badgeNames = array_column($newBadges, 'name');
            $message .= ' 🏆 Badge' . (count($newBadges) > 1 ? 's' : '') . ' earned: ' . implode(', ', $badgeNames);
        }
        
    } else { // unfollow
        // Check if currently following
        $stmt = $conn->prepare("SELECT id FROM user_follows WHERE follower_id = ? AND following_id = ?");
        $stmt->execute([$currentUser['id'], $targetUserId]);
        
        if (!$stmt->fetch()) {
            $conn->rollBack();
            echo json_encode(['success' => false, 'message' => 'Not following this user']);
            exit;
        }
        
        // Remove follow relationship
        $stmt = $conn->prepare("DELETE FROM user_follows WHERE follower_id = ? AND following_id = ?");
        $stmt->execute([$currentUser['id'], $targetUserId]);
        
        // Update follower count
        $stmt = $conn->prepare("UPDATE profiles SET follower_count = GREATEST(0, follower_count - 1) WHERE id = ?");
        $stmt->execute([$targetUserId]);
        
        // Update following count
        $stmt = $conn->prepare("UPDATE profiles SET following_count = GREATEST(0, following_count - 1) WHERE id = ?");
        $stmt->execute([$currentUser['id']]);
        
        $message = 'Successfully unfollowed user';
    }
    
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'action' => $action
    ]);
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    error_log("Follow/unfollow error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'An error occurred while processing your request'
    ]);
}
?>
