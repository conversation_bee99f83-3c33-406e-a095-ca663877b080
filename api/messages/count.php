<?php
/**
 * Messages Count API
 * Returns the count of unread messages for the current user
 */

require_once '../../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized', 'count' => 0]);
    exit;
}

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $user_id = getCurrentUser()['id'];
    
    // For now, return a mock count since messages table might not exist yet
    // In a real implementation, you would query the messages table
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM messages 
        WHERE recipient_id = ? AND read_at IS NULL
    ");
    
    try {
        $stmt->execute([$user_id]);
        $result = $stmt->fetch();
        $count = $result['count'] ?? 0;
    } catch (PDOException $e) {
        // If messages table doesn't exist, return 0
        $count = 0;
    }
    
    echo json_encode([
        'success' => true,
        'count' => (int)$count
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'count' => 0
    ]);
}
?>
