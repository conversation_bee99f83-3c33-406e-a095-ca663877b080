<?php
/**
 * Claim Listing API
 * Handles business listing claim requests
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Get current user
$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['place_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing place_id']);
    exit;
}

$placeId = intval($input['place_id']);

try {
    $conn->beginTransaction();
    
    // Check if place exists
    $stmt = $conn->prepare("SELECT id, name, email, is_claimed FROM places WHERE id = ?");
    $stmt->execute([$placeId]);
    $place = $stmt->fetch();
    
    if (!$place) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Place not found']);
        exit;
    }
    
    if ($place['is_claimed']) {
        echo json_encode(['success' => false, 'message' => 'This place has already been claimed']);
        exit;
    }
    
    // Check if user has already submitted a claim for this place
    $stmt = $conn->prepare("SELECT id FROM listing_claims WHERE user_id = ? AND place_id = ? AND status IN ('pending', 'under_review')");
    $stmt->execute([$currentUser['id'], $placeId]);
    $existingClaim = $stmt->fetch();
    
    if ($existingClaim) {
        echo json_encode(['success' => false, 'message' => 'You have already submitted a claim for this place']);
        exit;
    }
    
    // Create claim request
    $claimToken = bin2hex(random_bytes(32));
    
    $stmt = $conn->prepare("
        INSERT INTO listing_claims (
            user_id, place_id, claim_token, status, 
            submitted_at, user_email, user_name
        ) VALUES (?, ?, ?, 'pending', NOW(), ?, ?)
    ");
    
    $userName = trim(($currentUser['first_name'] ?? '') . ' ' . ($currentUser['last_name'] ?? ''));
    if (empty($userName)) {
        $userName = $currentUser['email'];
    }
    
    $stmt->execute([
        $currentUser['id'],
        $placeId,
        $claimToken,
        $currentUser['email'],
        $userName
    ]);
    
    $claimId = $conn->lastInsertId();
    
    // Log activity
    $stmt = $conn->prepare("
        INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata, created_at) 
        VALUES (?, 'listing_claim', 'place', ?, ?, NOW())
    ");
    $stmt->execute([
        $currentUser['id'], 
        $placeId, 
        json_encode([
            'place_name' => $place['name'],
            'claim_id' => $claimId
        ])
    ]);
    
    // Send notification email to admin (you can implement this)
    // sendClaimNotificationEmail($claimId, $place, $currentUser);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Claim request submitted successfully',
        'claim_id' => $claimId
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    error_log("Claim listing error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}

/**
 * Send claim notification email to admin
 */
function sendClaimNotificationEmail($claimId, $place, $user) {
    // Implementation would go here
    // This would send an email to admin about the new claim request
    // Include verification instructions and claim details
}
?>
