<?php
/**
 * CSV Templates Generator
 * Generate CSV templates for beer and food menu imports
 */

require_once '../config/config.php';
requireLogin();

$type = $_GET['type'] ?? '';

if (!in_array($type, ['beer', 'food'])) {
    http_response_code(400);
    echo "Invalid template type";
    exit;
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $type . '_menu_template.csv"');

$output = fopen('php://output', 'w');

if ($type === 'beer') {
    // Beer menu template
    fputcsv($output, [
        'name',
        'beer_style_name',
        'description',
        'abv',
        'ibu',
        'price',
        'availability'
    ]);
    
    // Sample data
    fputcsv($output, [
        'House IPA',
        'American IPA',
        'Hoppy and citrusy with notes of grapefruit and pine',
        '6.5',
        '65',
        '7.99',
        'year_round'
    ]);
    
    fputcsv($output, [
        'Chocolate Stout',
        'Imperial Stout',
        'Rich and creamy with chocolate and coffee notes',
        '8.2',
        '35',
        '9.99',
        'seasonal'
    ]);
    
    fputcsv($output, [
        'Summer Wheat',
        'American Wheat',
        'Light and refreshing wheat beer perfect for summer',
        '4.8',
        '15',
        '6.99',
        'seasonal'
    ]);
    
} else {
    // Food menu template
    fputcsv($output, [
        'name',
        'food_category_name',
        'description',
        'price',
        'ingredients',
        'allergens',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free'
    ]);
    
    // Sample data
    fputcsv($output, [
        'Margherita Pizza',
        'Pizza',
        'Fresh mozzarella, tomato sauce, and basil',
        '14.99',
        'Pizza dough, tomato sauce, mozzarella cheese, fresh basil',
        'Gluten, Dairy',
        '1',
        '0',
        '0'
    ]);
    
    fputcsv($output, [
        'Caesar Salad',
        'Salads',
        'Crisp romaine lettuce with parmesan and croutons',
        '12.99',
        'Romaine lettuce, parmesan cheese, croutons, caesar dressing',
        'Gluten, Dairy, Eggs',
        '1',
        '0',
        '0'
    ]);
    
    fputcsv($output, [
        'Veggie Burger',
        'Entrees',
        'Plant-based burger with lettuce, tomato, and vegan mayo',
        '13.99',
        'Plant-based patty, bun, lettuce, tomato, vegan mayo',
        'Gluten',
        '1',
        '1',
        '0'
    ]);
    
    fputcsv($output, [
        'Buffalo Wings',
        'Appetizers',
        'Spicy buffalo wings served with celery and blue cheese',
        '11.99',
        'Chicken wings, buffalo sauce, celery, blue cheese dressing',
        'Dairy',
        '0',
        '0',
        '1'
    ]);
}

fclose($output);
exit;
?>
