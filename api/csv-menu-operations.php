<?php
/**
 * CSV Menu Operations API
 * Handle CSV import/export for beer and food menus
 */

require_once '../config/config.php';
requireLogin();

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'import':
            handleImport();
            break;
        case 'export':
            handleExport();
            break;
        case 'validate':
            handleValidation();
            break;
        case 'preview':
            handlePreview();
            break;
        case 'history':
            handleHistory();
            break;
        case 'stats':
            handleExportStats();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleImport() {
    global $pdo;
    
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
        return;
    }
    
    $place_id = $_POST['place_id'] ?? null;
    $menu_type = $_POST['menu_type'] ?? null;
    $replace_existing = isset($_POST['replace_existing']);
    $validate_only = isset($_POST['validate_only']);
    
    if (!$place_id || !$menu_type) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID and menu type are required']);
        return;
    }
    
    $file = $_FILES['csv_file'];
    
    // Validate file
    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'File size exceeds 5MB limit']);
        return;
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($file_extension !== 'csv') {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Only CSV files are allowed']);
        return;
    }
    
    // Process CSV
    $csv_data = [];
    $errors = [];
    $warnings = [];
    
    if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
        $header = fgetcsv($handle);
        $row_number = 1;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_number++;
            
            if (count($data) !== count($header)) {
                $errors[] = "Row $row_number: Column count mismatch";
                continue;
            }
            
            $row_data = array_combine($header, $data);
            
            // Validate required fields
            if (empty($row_data['name'])) {
                $errors[] = "Row $row_number: Name is required";
                continue;
            }
            
            // Type-specific validation
            if ($menu_type === 'beer') {
                $validated_row = validateBeerRow($row_data, $row_number, $errors, $warnings);
            } else {
                $validated_row = validateFoodRow($row_data, $row_number, $errors, $warnings);
            }
            
            if ($validated_row) {
                $csv_data[] = $validated_row;
            }
        }
        fclose($handle);
    }
    
    $result = [
        'success' => empty($errors),
        'total_rows' => count($csv_data),
        'errors' => $errors,
        'warnings' => $warnings,
        'validate_only' => $validate_only
    ];
    
    if (!$validate_only && empty($errors)) {
        // Perform actual import
        try {
            $pdo->beginTransaction();
            
            if ($replace_existing) {
                if ($menu_type === 'beer') {
                    $stmt = $pdo->prepare("DELETE FROM place_beers WHERE place_id = ?");
                } else {
                    $stmt = $pdo->prepare("DELETE FROM place_food WHERE place_id = ?");
                }
                $stmt->execute([$place_id]);
            }
            
            $imported_count = 0;
            foreach ($csv_data as $row) {
                if ($menu_type === 'beer') {
                    $imported_count += importBeerRow($pdo, $place_id, $row);
                } else {
                    $imported_count += importFoodRow($pdo, $place_id, $row);
                }
            }
            
            // Log import
            logImport($pdo, $place_id, $menu_type, $imported_count, count($errors), count($warnings));
            
            $pdo->commit();
            $result['imported_count'] = $imported_count;
            $result['message'] = "Successfully imported $imported_count items";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $result['success'] = false;
            $result['message'] = 'Import failed: ' . $e->getMessage();
        }
    }
    
    echo json_encode($result);
}

function validateBeerRow($row, $row_number, &$errors, &$warnings) {
    global $pdo;
    
    $validated = [
        'name' => trim($row['name']),
        'beer_style_id' => null,
        'description' => trim($row['description'] ?? ''),
        'abv' => null,
        'ibu' => null,
        'price' => null,
        'availability' => 'year_round'
    ];
    
    // Validate beer style
    if (!empty($row['beer_style_name'])) {
        $stmt = $pdo->prepare("SELECT id FROM beer_styles WHERE name = ? AND is_active = 1");
        $stmt->execute([trim($row['beer_style_name'])]);
        $style = $stmt->fetch();
        
        if ($style) {
            $validated['beer_style_id'] = $style['id'];
        } else {
            $warnings[] = "Row $row_number: Beer style '{$row['beer_style_name']}' not found";
        }
    }
    
    // Validate numeric fields
    if (!empty($row['abv'])) {
        if (is_numeric($row['abv']) && $row['abv'] >= 0 && $row['abv'] <= 20) {
            $validated['abv'] = floatval($row['abv']);
        } else {
            $errors[] = "Row $row_number: Invalid ABV value";
            return null;
        }
    }
    
    if (!empty($row['ibu'])) {
        if (is_numeric($row['ibu']) && $row['ibu'] >= 0 && $row['ibu'] <= 120) {
            $validated['ibu'] = intval($row['ibu']);
        } else {
            $errors[] = "Row $row_number: Invalid IBU value";
            return null;
        }
    }
    
    if (!empty($row['price'])) {
        if (is_numeric($row['price']) && $row['price'] >= 0) {
            $validated['price'] = floatval($row['price']);
        } else {
            $errors[] = "Row $row_number: Invalid price value";
            return null;
        }
    }
    
    // Validate availability
    if (!empty($row['availability'])) {
        $valid_availability = ['year_round', 'seasonal', 'limited', 'one_off'];
        if (in_array($row['availability'], $valid_availability)) {
            $validated['availability'] = $row['availability'];
        } else {
            $warnings[] = "Row $row_number: Invalid availability, using 'year_round'";
        }
    }
    
    return $validated;
}

function validateFoodRow($row, $row_number, &$errors, &$warnings) {
    global $pdo;
    
    $validated = [
        'name' => trim($row['name']),
        'food_category_id' => null,
        'description' => trim($row['description'] ?? ''),
        'price' => null,
        'ingredients' => trim($row['ingredients'] ?? ''),
        'allergens' => trim($row['allergens'] ?? ''),
        'is_vegetarian' => 0,
        'is_vegan' => 0,
        'is_gluten_free' => 0
    ];
    
    // Validate food category
    if (!empty($row['food_category_name'])) {
        $stmt = $pdo->prepare("SELECT id FROM food_categories WHERE name = ? AND is_active = 1");
        $stmt->execute([trim($row['food_category_name'])]);
        $category = $stmt->fetch();
        
        if ($category) {
            $validated['food_category_id'] = $category['id'];
        } else {
            $warnings[] = "Row $row_number: Food category '{$row['food_category_name']}' not found";
        }
    }
    
    // Validate price
    if (!empty($row['price'])) {
        if (is_numeric($row['price']) && $row['price'] >= 0) {
            $validated['price'] = floatval($row['price']);
        } else {
            $errors[] = "Row $row_number: Invalid price value";
            return null;
        }
    }
    
    // Validate boolean fields
    $boolean_fields = ['is_vegetarian', 'is_vegan', 'is_gluten_free'];
    foreach ($boolean_fields as $field) {
        if (isset($row[$field])) {
            $validated[$field] = in_array($row[$field], ['1', 'true', 'yes', 'y']) ? 1 : 0;
        }
    }
    
    return $validated;
}

function importBeerRow($pdo, $place_id, $row) {
    $stmt = $pdo->prepare("
        INSERT INTO place_beers (
            id, place_id, beer_style_id, name, description, abv, ibu, price, 
            availability, is_active, created_at
        ) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
    ");
    
    $stmt->execute([
        $place_id,
        $row['beer_style_id'],
        $row['name'],
        $row['description'],
        $row['abv'],
        $row['ibu'],
        $row['price'],
        $row['availability']
    ]);
    
    return $stmt->rowCount();
}

function importFoodRow($pdo, $place_id, $row) {
    $stmt = $pdo->prepare("
        INSERT INTO place_food (
            id, place_id, food_category_id, name, description, price, 
            ingredients, allergens, is_vegetarian, is_vegan, is_gluten_free, 
            is_active, created_at
        ) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
    ");
    
    $stmt->execute([
        $place_id,
        $row['food_category_id'],
        $row['name'],
        $row['description'],
        $row['price'],
        $row['ingredients'],
        $row['allergens'],
        $row['is_vegetarian'],
        $row['is_vegan'],
        $row['is_gluten_free']
    ]);
    
    return $stmt->rowCount();
}

function logImport($pdo, $place_id, $menu_type, $imported_count, $error_count, $warning_count) {
    $stmt = $pdo->prepare("
        INSERT INTO csv_import_log (
            id, place_id, menu_type, imported_count, error_count, warning_count, 
            imported_by, created_at
        ) VALUES (UUID(), ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $place_id,
        $menu_type,
        $imported_count,
        $error_count,
        $warning_count,
        $_SESSION['user_id']
    ]);
}

function handleExport() {
    global $pdo;
    
    $place_id = $_GET['place_id'] ?? null;
    $menu_type = $_GET['menu_type'] ?? '';
    $include_inactive = $_GET['include_inactive'] ?? '0';
    $date_format = $_GET['date_format'] ?? 'Y-m-d';
    
    if (!$menu_type) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Menu type is required']);
        return;
    }
    
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . generateExportFilename($menu_type, $place_id) . '"');
    
    $output = fopen('php://output', 'w');
    
    if ($menu_type === 'beer' || $menu_type === 'both') {
        exportBeerMenu($pdo, $output, $place_id, $include_inactive, $date_format);
    }
    
    if ($menu_type === 'food' || $menu_type === 'both') {
        exportFoodMenu($pdo, $output, $place_id, $include_inactive, $date_format);
    }
    
    fclose($output);
    exit;
}

function exportBeerMenu($pdo, $output, $place_id, $include_inactive, $date_format) {
    // Write header
    fputcsv($output, [
        'place_name', 'name', 'beer_style_name', 'description', 'abv', 'ibu', 
        'price', 'availability', 'is_active', 'created_at'
    ]);
    
    // Build query
    $where_clause = $include_inactive ? '' : 'AND pb.is_active = 1';
    if ($place_id) {
        $where_clause .= ' AND pb.place_id = ?';
    }
    
    $sql = "
        SELECT 
            b.name as place_name,
            pb.name,
            bs.name as beer_style_name,
            pb.description,
            pb.abv,
            pb.ibu,
            pb.price,
            pb.availability,
            pb.is_active,
            pb.created_at
        FROM place_beers pb
        LEFT JOIN breweries b ON pb.place_id = b.id
        LEFT JOIN beer_styles bs ON pb.beer_style_id = bs.id
        WHERE 1=1 $where_clause
        ORDER BY b.name, pb.name
    ";
    
    $stmt = $pdo->prepare($sql);
    if ($place_id) {
        $stmt->execute([$place_id]);
    } else {
        $stmt->execute();
    }
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $row['created_at'] = date($date_format, strtotime($row['created_at']));
        fputcsv($output, $row);
    }
}

function exportFoodMenu($pdo, $output, $place_id, $include_inactive, $date_format) {
    // Write header
    fputcsv($output, [
        'place_name', 'name', 'food_category_name', 'description', 'price', 
        'ingredients', 'allergens', 'is_vegetarian', 'is_vegan', 'is_gluten_free', 
        'is_active', 'created_at'
    ]);
    
    // Build query
    $where_clause = $include_inactive ? '' : 'AND pf.is_active = 1';
    if ($place_id) {
        $where_clause .= ' AND pf.place_id = ?';
    }
    
    $sql = "
        SELECT 
            b.name as place_name,
            pf.name,
            fc.name as food_category_name,
            pf.description,
            pf.price,
            pf.ingredients,
            pf.allergens,
            pf.is_vegetarian,
            pf.is_vegan,
            pf.is_gluten_free,
            pf.is_active,
            pf.created_at
        FROM place_food pf
        LEFT JOIN breweries b ON pf.place_id = b.id
        LEFT JOIN food_categories fc ON pf.food_category_id = fc.id
        WHERE 1=1 $where_clause
        ORDER BY b.name, fc.sort_order, pf.name
    ";
    
    $stmt = $pdo->prepare($sql);
    if ($place_id) {
        $stmt->execute([$place_id]);
    } else {
        $stmt->execute();
    }
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $row['created_at'] = date($date_format, strtotime($row['created_at']));
        fputcsv($output, $row);
    }
}

function generateExportFilename($menu_type, $place_id) {
    $timestamp = date('Y-m-d_H-i-s');
    $place_suffix = $place_id ? '_single_place' : '_all_places';
    return "menu_export_{$menu_type}{$place_suffix}_{$timestamp}.csv";
}

function handleHistory() {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT 
            cil.*,
            b.name as place_name,
            u.email as imported_by_email
        FROM csv_import_log cil
        LEFT JOIN breweries b ON cil.place_id = b.id
        LEFT JOIN users u ON cil.imported_by = u.id
        ORDER BY cil.created_at DESC
        LIMIT 50
    ");
    $stmt->execute();
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'data' => $history]);
}

function handleExportStats() {
    global $pdo;

    $place_id = $_GET['place_id'] ?? null;
    $menu_type = $_GET['menu_type'] ?? '';

    $stats = [];

    if ($menu_type === 'beer' || $menu_type === 'both') {
        $where = $place_id ? 'WHERE place_id = ?' : '';
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM place_beers $where");
        if ($place_id) {
            $stmt->execute([$place_id]);
        } else {
            $stmt->execute();
        }
        $stats['beer_count'] = $stmt->fetchColumn();
    }

    if ($menu_type === 'food' || $menu_type === 'both') {
        $where = $place_id ? 'WHERE place_id = ?' : '';
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM place_food $where");
        if ($place_id) {
            $stmt->execute([$place_id]);
        } else {
            $stmt->execute();
        }
        $stats['food_count'] = $stmt->fetchColumn();
    }

    echo json_encode(['success' => true, 'data' => $stats]);
}

function handleValidation() {
    // Same as import but with validate_only = true
    $_POST['validate_only'] = true;
    handleImport();
}

function handlePreview() {
    global $pdo;

    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No file uploaded']);
        return;
    }

    $file = $_FILES['csv_file'];
    $preview_data = [];

    if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
        $header = fgetcsv($handle);
        $row_count = 0;

        while (($data = fgetcsv($handle)) !== FALSE && $row_count < 5) {
            $preview_data[] = array_combine($header, $data);
            $row_count++;
        }
        fclose($handle);
    }

    echo json_encode([
        'success' => true,
        'header' => $header,
        'preview_data' => $preview_data,
        'total_rows' => $row_count
    ]);
}
?>
