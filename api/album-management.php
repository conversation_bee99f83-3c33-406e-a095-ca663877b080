<?php
/**
 * Album Management API
 * Handle album CRUD operations and management
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get':
            handleGet();
            break;
        case 'create':
            handleCreate();
            break;
        case 'update':
            handleUpdate();
            break;
        case 'delete':
            handleDelete();
            break;
        case 'bulk_delete':
            handleBulkDelete();
            break;
        case 'get_owners':
            handleGetOwners();
            break;
        case 'set_cover_photo':
            handleSetCoverPhoto();
            break;
        case 'duplicate':
            handleDuplicate();
            break;
        case 'export':
            handleExport();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    $album_id = $_GET['id'] ?? null;
    
    if (!$album_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                pa.*,
                CASE 
                    WHEN pa.owner_type = 'user' THEN (SELECT email FROM users WHERE id = pa.owner_id)
                    WHEN pa.owner_type = 'place' THEN (SELECT name FROM breweries WHERE id = pa.owner_id)
                END as owner_name
            FROM photo_albums pa
            WHERE pa.id = ?
        ");
        
        $stmt->execute([$album_id]);
        $album = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($album) {
            echo json_encode(['success' => true, 'album' => $album]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Album not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleCreate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $owner_type = $input['owner_type'] ?? null;
    $owner_id = $input['owner_id'] ?? null;
    $name = trim($input['name'] ?? '');
    $description = trim($input['description'] ?? '');
    $is_public = isset($input['is_public']) ? 1 : 0;
    $sort_order = $input['sort_order'] ?? 0;
    
    // Validation
    if (!$owner_type || !$owner_id || !$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Owner type, owner ID, and name are required']);
        return;
    }
    
    if (!in_array($owner_type, ['user', 'place'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid owner type']);
        return;
    }
    
    // Verify owner exists
    try {
        if ($owner_type === 'user') {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        } else {
            $stmt = $pdo->prepare("SELECT id FROM breweries WHERE id = ?");
        }
        $stmt->execute([$owner_id]);
        
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Owner not found']);
            return;
        }
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error verifying owner']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO photo_albums (id, owner_type, owner_id, name, description, is_public, sort_order, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$owner_type, $owner_id, $name, $description, $is_public, $sort_order]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Album created successfully',
            'album_id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $album_id = $input['album_id'] ?? null;
    
    if (!$album_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowed_fields = ['name', 'description', 'is_public', 'sort_order'];
        
        foreach ($allowed_fields as $field) {
            if (isset($input[$field])) {
                $fields[] = "$field = ?";
                $values[] = $input[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $album_id;
        
        $sql = "UPDATE photo_albums SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Album updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $album_id = $input['album_id'] ?? null;
    
    if (!$album_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album ID is required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Move photos to no album (set album_id to NULL)
        $stmt = $pdo->prepare("UPDATE photos SET album_id = NULL WHERE album_id = ?");
        $stmt->execute([$album_id]);
        $photos_moved = $stmt->rowCount();
        
        // Delete the album
        $stmt = $pdo->prepare("DELETE FROM photo_albums WHERE id = ?");
        $stmt->execute([$album_id]);
        
        if ($stmt->rowCount() > 0) {
            $pdo->commit();
            echo json_encode([
                'success' => true, 
                'message' => 'Album deleted successfully',
                'photos_moved' => $photos_moved
            ]);
        } else {
            $pdo->rollBack();
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Album not found']);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error deleting album: ' . $e->getMessage()]);
    }
}

function handleBulkDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $album_ids = $input['album_ids'] ?? [];
    
    if (empty($album_ids)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No album IDs provided']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        $placeholders = str_repeat('?,', count($album_ids) - 1) . '?';
        
        // Move photos to no album
        $stmt = $pdo->prepare("UPDATE photos SET album_id = NULL WHERE album_id IN ($placeholders)");
        $stmt->execute($album_ids);
        $photos_moved = $stmt->rowCount();
        
        // Delete albums
        $stmt = $pdo->prepare("DELETE FROM photo_albums WHERE id IN ($placeholders)");
        $stmt->execute($album_ids);
        $deleted_count = $stmt->rowCount();
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully deleted $deleted_count albums",
            'deleted_count' => $deleted_count,
            'photos_moved' => $photos_moved
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

function handleGetOwners() {
    global $pdo;
    
    $owner_type = $_GET['type'] ?? null;
    
    if (!$owner_type || !in_array($owner_type, ['user', 'place'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Valid owner type is required']);
        return;
    }
    
    try {
        if ($owner_type === 'user') {
            $stmt = $pdo->query("SELECT id, email, username FROM users ORDER BY email LIMIT 100");
        } else {
            $stmt = $pdo->query("SELECT id, name, city, state FROM breweries ORDER BY name LIMIT 100");
        }
        
        $owners = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'owners' => $owners]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleSetCoverPhoto() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $album_id = $input['album_id'] ?? null;
    $photo_id = $input['photo_id'] ?? null;
    
    if (!$album_id || !$photo_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album ID and photo ID are required']);
        return;
    }
    
    try {
        // Verify photo belongs to album
        $stmt = $pdo->prepare("SELECT id FROM photos WHERE id = ? AND album_id = ?");
        $stmt->execute([$photo_id, $album_id]);
        
        if (!$stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Photo does not belong to this album']);
            return;
        }
        
        // Update album cover photo
        $stmt = $pdo->prepare("UPDATE photo_albums SET cover_photo_id = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$photo_id, $album_id]);
        
        echo json_encode(['success' => true, 'message' => 'Cover photo updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDuplicate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $album_id = $input['album_id'] ?? null;
    
    if (!$album_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Album ID is required']);
        return;
    }
    
    try {
        // Get original album
        $stmt = $pdo->prepare("SELECT * FROM photo_albums WHERE id = ?");
        $stmt->execute([$album_id]);
        $album = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$album) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Album not found']);
            return;
        }
        
        // Create duplicate
        $stmt = $pdo->prepare("
            INSERT INTO photo_albums (id, owner_type, owner_id, name, description, is_public, sort_order, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $album['owner_type'],
            $album['owner_id'],
            $album['name'] . ' (Copy)',
            $album['description'],
            $album['is_public'],
            $album['sort_order'] + 1
        ]);
        
        echo json_encode(['success' => true, 'message' => 'Album duplicated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleExport() {
    global $pdo;
    
    $format = $_GET['format'] ?? 'csv';
    
    try {
        $stmt = $pdo->query("
            SELECT 
                pa.name, pa.description, pa.owner_type, pa.is_public, pa.sort_order, pa.created_at,
                CASE 
                    WHEN pa.owner_type = 'user' THEN (SELECT email FROM users WHERE id = pa.owner_id)
                    WHEN pa.owner_type = 'place' THEN (SELECT name FROM breweries WHERE id = pa.owner_id)
                END as owner_name,
                (SELECT COUNT(*) FROM photos WHERE album_id = pa.id) as photo_count
            FROM photo_albums pa
            ORDER BY pa.created_at DESC
        ");
        $albums = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($format === 'csv') {
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="albums_export_' . date('Y-m-d') . '.csv"');
            
            $output = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($output, ['Name', 'Description', 'Owner Type', 'Owner Name', 'Public', 'Sort Order', 'Photo Count', 'Created At']);
            
            // CSV data
            foreach ($albums as $album) {
                fputcsv($output, [
                    $album['name'],
                    $album['description'],
                    $album['owner_type'],
                    $album['owner_name'],
                    $album['is_public'] ? 'Yes' : 'No',
                    $album['sort_order'],
                    $album['photo_count'],
                    $album['created_at']
                ]);
            }
            
            fclose($output);
        } else {
            echo json_encode(['success' => true, 'albums' => $albums]);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Export error: ' . $e->getMessage()]);
    }
}
?>
