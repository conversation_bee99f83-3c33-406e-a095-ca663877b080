<?php
/**
 * Digital Board API Documentation
 * Phase 4 11.0 - API Development
 * 
 * Interactive API documentation for the Digital Board system
 */

require_once '../../includes/auth.php';

// Check if user is logged in for protected endpoints
$user = getCurrentUser();
$isAuthenticated = $user !== null;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Board API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        body { background: #1a1a1a; color: #f5f5dc; font-family: 'Inter', sans-serif; }
        .container { max-width: 1200px; }
        .card { background: #2c1810; border: 1px solid #d69a6b; }
        .card-header { background: #6f4c3e; color: #f5f5dc; }
        .nav-pills .nav-link.active { background: #ffc107; color: #1a1a1a; }
        .nav-pills .nav-link { color: #f5f5dc; }
        .nav-pills .nav-link:hover { background: #d69a6b; color: #1a1a1a; }
        .endpoint { margin-bottom: 30px; padding: 20px; border-radius: 8px; background: rgba(108, 76, 62, 0.1); }
        .method-badge { font-weight: bold; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: black; }
        .method-delete { background: #dc3545; color: white; }
        .code-block { background: #2d2d2d; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .response-example { background: #1e1e1e; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .auth-required { color: #ffc107; }
        .auth-optional { color: #28a745; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-code"></i>
                            Digital Board API Documentation
                        </h1>
                        <p class="mb-0 mt-2">Phase 4 11.0 - Enhanced API Endpoints</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Authentication Status -->
                        <div class="alert <?= $isAuthenticated ? 'alert-success' : 'alert-warning' ?> mb-4">
                            <h5>
                                <i class="fas <?= $isAuthenticated ? 'fa-check-circle' : 'fa-exclamation-triangle' ?>"></i>
                                Authentication Status
                            </h5>
                            <?php if ($isAuthenticated): ?>
                                <p class="mb-0">✅ You are authenticated as: <strong><?= htmlspecialchars($user['email']) ?></strong></p>
                                <p class="mb-0">Role: <strong><?= htmlspecialchars($user['role']) ?></strong></p>
                            <?php else: ?>
                                <p class="mb-0">⚠️ You are not authenticated. Some endpoints require authentication.</p>
                                <a href="../../auth/login.php" class="btn btn-warning btn-sm mt-2">Login to Test Protected Endpoints</a>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Navigation -->
                        <ul class="nav nav-pills mb-4" id="apiTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button">Overview</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="boards-tab" data-bs-toggle="pill" data-bs-target="#boards" type="button">Boards</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="templates-tab" data-bs-toggle="pill" data-bs-target="#templates" type="button">Templates</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="slideshows-tab" data-bs-toggle="pill" data-bs-target="#slideshows" type="button">Slideshows</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="slides-tab" data-bs-toggle="pill" data-bs-target="#slides" type="button">Slides</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="media-tab" data-bs-toggle="pill" data-bs-target="#media" type="button">Media</button>
                            </li>
                        </ul>
                        
                        <!-- Tab Content -->
                        <div class="tab-content" id="apiTabsContent">
                            
                            <!-- Overview Tab -->
                            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                                <h3>API Overview</h3>
                                <p>The Digital Board API provides comprehensive management capabilities for digital signage systems in breweries, restaurants, and bars.</p>
                                
                                <h4>Base URL</h4>
                                <div class="code-block">
                                    <code><?= $_SERVER['HTTP_HOST'] ?>/api/digital-board/</code>
                                </div>
                                
                                <h4>Authentication</h4>
                                <p>Most endpoints require user authentication. Authentication is handled through session-based login.</p>
                                
                                <h4>Rate Limiting</h4>
                                <ul>
                                    <li><strong>Templates:</strong> 200 requests/hour</li>
                                    <li><strong>Slideshows:</strong> 150 requests/hour</li>
                                    <li><strong>Slides:</strong> 200 requests/hour</li>
                                    <li><strong>Boards:</strong> 100 requests/hour</li>
                                    <li><strong>Media:</strong> 50 requests/hour</li>
                                </ul>
                                
                                <h4>Response Format</h4>
                                <p>All responses are in JSON format with the following structure:</p>
                                <div class="response-example">
<pre><code class="language-json">{
  "success": true,
  "timestamp": "2024-12-16T10:30:00Z",
  "data": { ... },
  "message": "Operation completed successfully"
}</code></pre>
                                </div>
                                
                                <h4>Error Handling</h4>
                                <p>Error responses include appropriate HTTP status codes and error details:</p>
                                <div class="response-example">
<pre><code class="language-json">{
  "success": false,
  "timestamp": "2024-12-16T10:30:00Z",
  "data": { "error": "Resource not found" },
  "errors": ["Detailed error messages"]
}</code></pre>
                                </div>
                            </div>
                            
                            <!-- Boards Tab -->
                            <div class="tab-pane fade" id="boards" role="tabpanel">
                                <h3>Digital Boards API</h3>
                                <p>Manage enhanced digital boards with full configuration options.</p>
                                
                                <!-- GET Boards -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-get">GET</span>
                                        /api/digital-board/boards
                                        <span class="auth-required"><i class="fas fa-lock"></i> Auth Required</span>
                                    </h4>
                                    <p>Retrieve digital boards with filtering and pagination.</p>
                                    
                                    <h5>Query Parameters</h5>
                                    <ul>
                                        <li><code>brewery_id</code> (string, optional) - Filter by brewery</li>
                                        <li><code>id</code> (string, optional) - Get specific board</li>
                                        <li><code>page</code> (int, optional) - Page number (default: 1)</li>
                                        <li><code>limit</code> (int, optional) - Items per page (default: 20)</li>
                                        <li><code>include_stats</code> (boolean, optional) - Include board statistics</li>
                                    </ul>
                                    
                                    <h5>Example Request</h5>
                                    <div class="code-block">
                                        <code>GET /api/digital-board/boards?brewery_id=demo-brewery-1&include_stats=true</code>
                                    </div>
                                    
                                    <h5>Example Response</h5>
                                    <div class="response-example">
<pre><code class="language-json">{
  "success": true,
  "data": {
    "id": "board_abc123",
    "brewery_id": "demo-brewery-1",
    "board_id": "main-board",
    "name": "Main Digital Board",
    "display_mode": "static",
    "theme": "beersty-professional",
    "show_prices": true,
    "is_active": true,
    "stats": {
      "view_count": 1250,
      "beer_stats": {
        "total_beers": 12,
        "available_beers": 10
      }
    }
  }
}</code></pre>
                                    </div>
                                </div>
                                
                                <!-- POST Boards -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-post">POST</span>
                                        /api/digital-board/boards
                                        <span class="auth-required"><i class="fas fa-lock"></i> Auth Required</span>
                                    </h4>
                                    <p>Create a new digital board with enhanced features.</p>
                                    
                                    <h5>Required Fields</h5>
                                    <ul>
                                        <li><code>brewery_id</code> (string) - Brewery identifier</li>
                                        <li><code>board_id</code> (string) - Unique board identifier</li>
                                        <li><code>name</code> (string) - Board display name</li>
                                    </ul>
                                    
                                    <h5>Example Request</h5>
                                    <div class="code-block">
                                        <code>POST /api/digital-board/boards</code>
                                    </div>
                                    <div class="response-example">
<pre><code class="language-json">{
  "brewery_id": "demo-brewery-1",
  "board_id": "taproom-main",
  "name": "Taproom Main Board",
  "description": "Primary digital board for the taproom",
  "display_mode": "slideshow",
  "theme": "beersty-professional",
  "show_prices": true,
  "show_descriptions": true,
  "ticker_enabled": true,
  "ticker_message": "Welcome to our brewery!"
}</code></pre>
                                    </div>
                                </div>
                                
                                <!-- PUT Boards -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-put">PUT</span>
                                        /api/digital-board/boards?id={board_id}
                                        <span class="auth-required"><i class="fas fa-lock"></i> Auth Required</span>
                                    </h4>
                                    <p>Update an existing digital board. All fields are optional.</p>
                                </div>
                                
                                <!-- DELETE Boards -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-delete">DELETE</span>
                                        /api/digital-board/boards?id={board_id}
                                        <span class="auth-required"><i class="fas fa-lock"></i> Auth Required</span>
                                    </h4>
                                    <p>Soft delete a digital board (sets is_active to false).</p>
                                </div>
                            </div>
                            
                            <!-- Templates Tab -->
                            <div class="tab-pane fade" id="templates" role="tabpanel">
                                <h3>Templates API</h3>
                                <p>Manage custom templates for digital boards and slides.</p>
                                
                                <!-- GET Templates -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-get">GET</span>
                                        /api/digital-board/templates
                                        <span class="auth-optional"><i class="fas fa-unlock"></i> Public/Auth</span>
                                    </h4>
                                    <p>Retrieve templates with filtering. Public templates are available without authentication.</p>
                                    
                                    <h5>Query Parameters</h5>
                                    <ul>
                                        <li><code>brewery_id</code> (string, optional) - Filter by brewery (requires auth)</li>
                                        <li><code>category</code> (string, optional) - system, custom, community</li>
                                        <li><code>type</code> (string, optional) - beer_board, slide, layout</li>
                                        <li><code>id</code> (string, optional) - Get specific template</li>
                                    </ul>
                                </div>
                                
                                <!-- POST Templates -->
                                <div class="endpoint">
                                    <h4>
                                        <span class="method-badge method-post">POST</span>
                                        /api/digital-board/templates
                                        <span class="auth-required"><i class="fas fa-lock"></i> Auth Required</span>
                                    </h4>
                                    <p>Create a new custom template.</p>
                                    
                                    <h5>Required Fields</h5>
                                    <ul>
                                        <li><code>brewery_id</code> (string) - Brewery identifier</li>
                                        <li><code>name</code> (string) - Template name</li>
                                        <li><code>category</code> (string) - custom (system only for admins)</li>
                                        <li><code>template_type</code> (string) - beer_board, slide, layout</li>
                                        <li><code>theme_settings</code> (object) - Theme configuration</li>
                                        <li><code>layout_settings</code> (object) - Layout configuration</li>
                                        <li><code>color_scheme</code> (object) - Color scheme</li>
                                        <li><code>typography_settings</code> (object) - Typography settings</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Additional tabs would continue here... -->
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
