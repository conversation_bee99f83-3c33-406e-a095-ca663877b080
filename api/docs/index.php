<?php
/**
 * API Documentation
 * Phase 10: Advanced Features & API Development
 * Interactive API documentation with examples
 */

require_once '../../config/config.php';

$pageTitle = 'Beersty API Documentation';
$additionalCSS = ['/beersty/assets/css/api-docs.css'];
$additionalJS = ['/beersty/assets/js/api-docs.js'];

include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h5 class="sidebar-heading">
                    <i class="fas fa-code me-2"></i>API Documentation
                </h5>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#getting-started">
                            <i class="fas fa-play-circle me-2"></i>Getting Started
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#authentication">
                            <i class="fas fa-key me-2"></i>Authentication
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#rate-limits">
                            <i class="fas fa-tachometer-alt me-2"></i>Rate Limits
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#endpoints">
                            <i class="fas fa-sitemap me-2"></i>Endpoints
                        </a>
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link" href="#beers-api">Beers</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#breweries-api">Breweries</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#users-api">Users</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#checkins-api">Check-ins</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#search-api">Search</a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#webhooks">
                            <i class="fas fa-webhook me-2"></i>Webhooks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#sdks">
                            <i class="fas fa-code-branch me-2"></i>SDKs & Libraries
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="api-docs-content">
                
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-beer text-warning me-2"></i>
                        Beersty API Documentation
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="api-tester-btn">
                                <i class="fas fa-play me-1"></i>API Tester
                            </button>
                        </div>
                        <div class="btn-group">
                            <span class="badge bg-success">v1.0</span>
                        </div>
                    </div>
                </div>

                <!-- Getting Started -->
                <section id="getting-started" class="mb-5">
                    <h2><i class="fas fa-play-circle text-primary me-2"></i>Getting Started</h2>
                    <p class="lead">Welcome to the Beersty API! Our RESTful API allows you to access beer data, brewery information, user profiles, and social features programmatically.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle me-2"></i>Base URL</h5>
                                </div>
                                <div class="card-body">
                                    <code class="api-url">https://your-domain.com/beersty/api/v1/</code>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-file-code me-2"></i>Response Format</h5>
                                </div>
                                <div class="card-body">
                                    <code>application/json</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt-4">Quick Example</h4>
                    <div class="code-example">
                        <div class="code-tabs">
                            <button class="tab-btn active" data-tab="curl">cURL</button>
                            <button class="tab-btn" data-tab="javascript">JavaScript</button>
                            <button class="tab-btn" data-tab="php">PHP</button>
                            <button class="tab-btn" data-tab="python">Python</button>
                        </div>
                        
                        <div class="tab-content active" id="curl">
<pre><code>curl -X GET "https://your-domain.com/beersty/api/v1/beers" \
  -H "X-API-Key: your_api_key_here" \
  -H "Content-Type: application/json"</code></pre>
                        </div>
                        
                        <div class="tab-content" id="javascript">
<pre><code>const response = await fetch('https://your-domain.com/beersty/api/v1/beers', {
  headers: {
    'X-API-Key': 'your_api_key_here',
    'Content-Type': 'application/json'
  }
});
const data = await response.json();</code></pre>
                        </div>
                        
                        <div class="tab-content" id="php">
<pre><code>$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://your-domain.com/beersty/api/v1/beers');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-API-Key: your_api_key_here',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$data = json_decode($response, true);</code></pre>
                        </div>
                        
                        <div class="tab-content" id="python">
<pre><code>import requests

headers = {
    'X-API-Key': 'your_api_key_here',
    'Content-Type': 'application/json'
}

response = requests.get('https://your-domain.com/beersty/api/v1/beers', headers=headers)
data = response.json()</code></pre>
                        </div>
                    </div>
                </section>

                <!-- Authentication -->
                <section id="authentication" class="mb-5">
                    <h2><i class="fas fa-key text-warning me-2"></i>Authentication</h2>
                    <p>The Beersty API uses API keys for authentication. Include your API key in the <code>X-API-Key</code> header with every request.</p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Getting an API Key:</strong> You can generate API keys from your account settings page. Different subscription tiers have different rate limits and permissions.
                    </div>

                    <h4>API Key Tiers</h4>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Tier</th>
                                    <th>Rate Limit</th>
                                    <th>Permissions</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-secondary">Public</span></td>
                                    <td>100 requests/hour</td>
                                    <td>Read-only access to public data</td>
                                    <td>Free</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">Authenticated</span></td>
                                    <td>1,000 requests/hour</td>
                                    <td>Read/write access to user data</td>
                                    <td>$9.99/month</td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">Premium</span></td>
                                    <td>5,000 requests/hour</td>
                                    <td>Full API access + webhooks</td>
                                    <td>$19.99/month</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Rate Limits -->
                <section id="rate-limits" class="mb-5">
                    <h2><i class="fas fa-tachometer-alt text-danger me-2"></i>Rate Limits</h2>
                    <p>API requests are rate-limited based on your subscription tier. When you exceed the rate limit, you'll receive a <code>429 Too Many Requests</code> response.</p>
                    
                    <h4>Rate Limit Headers</h4>
                    <p>Every API response includes rate limit information in the headers:</p>
                    
                    <div class="code-example">
<pre><code>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200</code></pre>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Best Practices:</strong> Implement exponential backoff when you receive rate limit errors, and cache responses when possible to reduce API calls.
                    </div>
                </section>

                <!-- Endpoints -->
                <section id="endpoints" class="mb-5">
                    <h2><i class="fas fa-sitemap text-success me-2"></i>API Endpoints</h2>
                    
                    <!-- Beers API -->
                    <div id="beers-api" class="endpoint-section">
                        <h3><i class="fas fa-beer me-2"></i>Beers API</h3>
                        <p>Access beer information, ratings, and reviews.</p>
                        
                        <div class="endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/beers</span>
                                <span class="description">Get all beers with pagination and filtering</span>
                            </div>
                            
                            <div class="endpoint-details">
                                <h5>Query Parameters</h5>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Parameter</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Example</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>page</code></td>
                                            <td>integer</td>
                                            <td>Page number (default: 1)</td>
                                            <td>1</td>
                                        </tr>
                                        <tr>
                                            <td><code>limit</code></td>
                                            <td>integer</td>
                                            <td>Items per page (max: 100)</td>
                                            <td>20</td>
                                        </tr>
                                        <tr>
                                            <td><code>style</code></td>
                                            <td>string</td>
                                            <td>Filter by beer style</td>
                                            <td>IPA</td>
                                        </tr>
                                        <tr>
                                            <td><code>brewery</code></td>
                                            <td>string</td>
                                            <td>Filter by brewery name</td>
                                            <td>Stone Brewing</td>
                                        </tr>
                                        <tr>
                                            <td><code>min_abv</code></td>
                                            <td>float</td>
                                            <td>Minimum ABV percentage</td>
                                            <td>5.0</td>
                                        </tr>
                                        <tr>
                                            <td><code>max_abv</code></td>
                                            <td>float</td>
                                            <td>Maximum ABV percentage</td>
                                            <td>10.0</td>
                                        </tr>
                                        <tr>
                                            <td><code>search</code></td>
                                            <td>string</td>
                                            <td>Search in name and description</td>
                                            <td>hoppy</td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <h5>Example Response</h5>
                                <div class="code-example">
<pre><code>{
  "success": true,
  "version": "v1",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": [
    {
      "id": "beer-123",
      "name": "Stone IPA",
      "description": "A bold, hoppy India Pale Ale",
      "abv": 6.9,
      "ibu": 77,
      "srm": 8,
      "price": 12.99,
      "brewery_name": "Stone Brewing",
      "brewery_id": "brewery-456",
      "style_name": "India Pale Ale",
      "style_id": "style-789",
      "avg_rating": 4.2,
      "rating_count": 1547,
      "images": ["beer-123-1.jpg", "beer-123-2.jpg"]
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1250,
      "pages": 63
    },
    "filters": {
      "style": "IPA",
      "min_abv": 5.0
    }
  }
}</code></pre>
                                </div>
                            </div>
                        </div>

                        <div class="endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/beers/{id}</span>
                                <span class="description">Get detailed information about a specific beer</span>
                            </div>
                        </div>

                        <div class="endpoint">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/beers/{id}/ratings</span>
                                <span class="description">Get ratings and reviews for a specific beer</span>
                            </div>
                        </div>

                        <div class="endpoint">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/beers</span>
                                <span class="description">Create a new beer (requires permissions)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Tester Modal -->
                <div class="modal fade" id="apiTesterModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-play me-2"></i>API Tester
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="api-test-form">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">Method</label>
                                            <select class="form-select" id="test-method">
                                                <option value="GET">GET</option>
                                                <option value="POST">POST</option>
                                                <option value="PUT">PUT</option>
                                                <option value="DELETE">DELETE</option>
                                            </select>
                                        </div>
                                        <div class="col-md-9">
                                            <label class="form-label">Endpoint</label>
                                            <input type="text" class="form-control" id="test-endpoint" placeholder="/beers" value="/beers">
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <label class="form-label">API Key</label>
                                        <input type="text" class="form-control" id="test-api-key" placeholder="your_api_key_here">
                                    </div>
                                    
                                    <div class="mt-3">
                                        <label class="form-label">Request Body (JSON)</label>
                                        <textarea class="form-control" id="test-body" rows="4" placeholder='{"key": "value"}'></textarea>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-play me-2"></i>Send Request
                                        </button>
                                    </div>
                                </form>
                                
                                <div id="test-response" class="mt-4" style="display: none;">
                                    <h6>Response:</h6>
                                    <pre><code id="response-content"></code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
