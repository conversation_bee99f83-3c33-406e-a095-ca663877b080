<?php
require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Require login
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Login required']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$user = getCurrentUser();
$userId = $user['id'];

// Get form data
$name = sanitizeInput($_POST['name'] ?? '');
$description = sanitizeInput($_POST['description'] ?? '');
$isPublic = isset($_POST['is_public']) ? 1 : 0;

// Validate input
if (empty($name)) {
    echo json_encode(['success' => false, 'message' => 'List name is required']);
    exit;
}

if (strlen($name) > 100) {
    echo json_encode(['success' => false, 'message' => 'List name must be 100 characters or less']);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if user already has a list with this name
    $stmt = $conn->prepare("
        SELECT id FROM user_lists 
        WHERE user_id = ? AND name = ?
    ");
    $stmt->execute([$userId, $name]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'You already have a list with this name']);
        exit;
    }
    
    // Create the list
    $stmt = $conn->prepare("
        INSERT INTO user_lists (user_id, name, description, is_public, list_type)
        VALUES (?, ?, ?, ?, 'custom')
    ");
    
    $stmt->execute([$userId, $name, $description, $isPublic]);
    $listId = $conn->lastInsertId();
    
    // Log activity
    $stmt = $conn->prepare("
        INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata)
        VALUES (?, 'list_created', 'list', ?, ?)
    ");
    $stmt->execute([
        $userId,
        $listId,
        json_encode([
            'list_name' => $name,
            'is_public' => $isPublic
        ])
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'List created successfully',
        'list_id' => $listId
    ]);
    
} catch (Exception $e) {
    error_log("Create list API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to create list']);
}
?>
