<?php
/**
 * Category Management API
 * Handle CRUD operations for beer styles and food categories
 */

require_once '../config/config.php';
requireLogin();

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            handleCreate();
            break;
        case 'PUT':
            handleUpdate();
            break;
        case 'DELETE':
            handleDelete();
            break;
        case 'GET':
            handleRead();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleCreate() {
    global $pdo;
    
    $type = $_POST['type'] ?? '';
    
    if ($type === 'beer_style') {
        createBeerStyle();
    } elseif ($type === 'food_category') {
        createFoodCategory();
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid type specified']);
    }
}

function createBeerStyle() {
    global $pdo;
    
    $name = $_POST['name'] ?? null;
    $category = $_POST['category'] ?? null;
    $description = $_POST['description'] ?? null;
    $abv_min = $_POST['abv_min'] ?? null;
    $abv_max = $_POST['abv_max'] ?? null;
    $ibu_min = $_POST['ibu_min'] ?? null;
    $ibu_max = $_POST['ibu_max'] ?? null;
    
    if (!$name || !$category) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Name and category are required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO beer_styles (id, name, category, description, abv_min, abv_max, ibu_min, ibu_max, is_active, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 1, NOW())
        ");
        
        $stmt->execute([$name, $category, $description, $abv_min, $abv_max, $ibu_min, $ibu_max]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Beer style added successfully'
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function createFoodCategory() {
    global $pdo;
    
    $name = $_POST['name'] ?? null;
    $description = $_POST['description'] ?? null;
    $sort_order = $_POST['sort_order'] ?? 0;
    
    if (!$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Name is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO food_categories (id, name, description, sort_order, is_active, created_at) 
            VALUES (UUID(), ?, ?, ?, 1, NOW())
        ");
        
        $stmt->execute([$name, $description, $sort_order]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Food category added successfully'
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $type = $input['type'] ?? '';
    
    if ($type === 'beer_style') {
        updateBeerStyle($input);
    } elseif ($type === 'food_category') {
        updateFoodCategory($input);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid type specified']);
    }
}

function updateBeerStyle($data) {
    global $pdo;
    
    $id = $data['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowedFields = ['name', 'category', 'description', 'abv_min', 'abv_max', 'ibu_min', 'ibu_max', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE beer_styles SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Beer style updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function updateFoodCategory($data) {
    global $pdo;
    
    $id = $data['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowedFields = ['name', 'description', 'sort_order', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE food_categories SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Food category updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $input['id'] ?? null;
    $type = $input['type'] ?? '';
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        if ($type === 'beer_style') {
            $stmt = $pdo->prepare("DELETE FROM beer_styles WHERE id = ?");
            $itemType = 'Beer style';
        } elseif ($type === 'food_category') {
            $stmt = $pdo->prepare("DELETE FROM food_categories WHERE id = ?");
            $itemType = 'Food category';
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid type specified']);
            return;
        }
        
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => $itemType . ' deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => $itemType . ' not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleRead() {
    global $pdo;
    
    $type = $_GET['type'] ?? 'both';
    
    try {
        $result = [];
        
        if ($type === 'beer_styles' || $type === 'both') {
            $stmt = $pdo->query("SELECT * FROM beer_styles WHERE is_active = 1 ORDER BY category, name");
            $result['beer_styles'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        if ($type === 'food_categories' || $type === 'both') {
            $stmt = $pdo->query("SELECT * FROM food_categories WHERE is_active = 1 ORDER BY sort_order, name");
            $result['food_categories'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        echo json_encode(['success' => true, 'data' => $result]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
