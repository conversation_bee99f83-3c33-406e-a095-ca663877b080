<?php
require_once '../config/config.php';
require_once '../includes/PhotoManager.php';

// Set JSON header
header('Content-Type: application/json');

// Require login
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Login required']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$user = getCurrentUser();
$userId = $user['id'];

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$photoId = sanitizeInput($input['photo_id'] ?? '');

// Validate input
if (empty($photoId)) {
    echo json_encode(['success' => false, 'message' => 'Photo ID is required']);
    exit;
}

try {
    $photoManager = new PhotoManager();
    
    // Delete the photo (PhotoManager will check permissions)
    $result = $photoManager->deletePhoto($photoId, $userId);
    
    if ($result['success']) {
        // Log activity
        logPhotoDeleteActivity($userId, $photoId);
        
        echo json_encode([
            'success' => true,
            'message' => 'Photo deleted successfully'
        ]);
    } else {
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    error_log("Photo deletion API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to delete photo']);
}

/**
 * Log photo deletion activity
 */
function logPhotoDeleteActivity($userId, $photoId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata)
            VALUES (?, 'photo_delete', 'photo', ?, ?)
        ");
        $stmt->execute([
            $userId, 
            $photoId,
            json_encode(['photo_id' => $photoId])
        ]);
        
    } catch (Exception $e) {
        error_log("Photo delete activity logging error: " . $e->getMessage());
    }
}
?>
