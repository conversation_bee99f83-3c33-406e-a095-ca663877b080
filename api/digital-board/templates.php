<?php
/**
 * Digital Board Templates API
 * Phase 4 11.0 - API Development
 * 
 * Handles template management operations for digital boards
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAPI.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize API service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $api = new DigitalBoardAPI($pdo, $user);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE']) && !$user) {
    echo $api->formatResponse(['error' => 'Authentication required'], false, null, 401);
    exit;
}

// Check rate limiting
if (!$api->checkRateLimit('templates', 200, 3600)) {
    echo $api->formatResponse(['error' => 'Rate limit exceeded'], false, null, 429);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetTemplates($pdo, $api, $user);
            break;
        case 'POST':
            handleCreateTemplate($pdo, $api, $user);
            break;
        case 'PUT':
            handleUpdateTemplate($pdo, $api, $user);
            break;
        case 'DELETE':
            handleDeleteTemplate($pdo, $api, $user);
            break;
        default:
            echo $api->formatResponse(['error' => 'Method not allowed'], false, null, 405);
    }
} catch (Exception $e) {
    echo $api->handleDatabaseError($e, 'template operation');
}

/**
 * GET /api/digital-board/templates
 * Get templates with filtering and pagination
 */
function handleGetTemplates($pdo, $api, $user) {
    $breweryId = $_GET['brewery_id'] ?? null;
    $category = $_GET['category'] ?? null;
    $templateType = $_GET['type'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $templateId = $_GET['id'] ?? null;
    
    // Build query
    $where = ['1=1'];
    $params = [];
    
    // Single template by ID
    if ($templateId) {
        $where[] = 'id = ?';
        $params[] = $templateId;
    } else {
        // Filter by category
        if ($category) {
            $where[] = 'category = ?';
            $params[] = $category;
        }
        
        // Filter by type
        if ($templateType) {
            $where[] = 'template_type = ?';
            $params[] = $templateType;
        }
        
        // Filter by brewery (for custom templates)
        if ($breweryId) {
            // Validate brewery access
            $access = $api->validateBreweryAccess($breweryId, 'read');
            if (!$access['success']) {
                echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
                return;
            }
            
            $where[] = '(brewery_id = ? OR is_public = 1)';
            $params[] = $breweryId;
        } else {
            // Only show public templates if no brewery specified
            $where[] = 'is_public = 1';
        }
        
        // Only active templates
        $where[] = 'is_active = 1';
    }
    
    $query = "
        SELECT 
            t.*,
            b.name as brewery_name,
            (SELECT COUNT(*) FROM digital_boards WHERE template_id = t.id) as usage_count_actual
        FROM template_library t
        LEFT JOIN breweries b ON t.brewery_id = b.id
        WHERE " . implode(' AND ', $where) . "
        ORDER BY 
            CASE WHEN t.category = 'system' THEN 0 ELSE 1 END,
            t.is_featured DESC,
            t.usage_count DESC,
            t.created_at DESC
    ";
    
    if ($templateId) {
        // Single template
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo $api->formatResponse(['error' => 'Template not found'], false, null, 404);
            return;
        }
        
        // Parse JSON fields
        $template['theme_settings'] = json_decode($template['theme_settings'], true);
        $template['layout_settings'] = json_decode($template['layout_settings'], true);
        $template['color_scheme'] = json_decode($template['color_scheme'], true);
        $template['typography_settings'] = json_decode($template['typography_settings'], true);
        $template['tags'] = json_decode($template['tags'], true);
        
        echo $api->formatResponse($template);
    } else {
        // Paginated list
        $result = $api->paginate($query, $params, $page, $limit);
        
        // Parse JSON fields for each template
        foreach ($result['data'] as &$template) {
            $template['theme_settings'] = json_decode($template['theme_settings'], true);
            $template['layout_settings'] = json_decode($template['layout_settings'], true);
            $template['color_scheme'] = json_decode($template['color_scheme'], true);
            $template['typography_settings'] = json_decode($template['typography_settings'], true);
            $template['tags'] = json_decode($template['tags'], true);
        }
        
        echo $api->formatResponse($result);
    }
    
    // Log activity
    $api->logActivity('view', 'templates', $templateId, ['filters' => $_GET]);
}

/**
 * POST /api/digital-board/templates
 * Create a new template
 */
function handleCreateTemplate($pdo, $api, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }
    
    // Validation rules
    $rules = [
        'brewery_id' => ['type' => 'string', 'required' => true],
        'name' => ['type' => 'string', 'required' => true, 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'category' => ['type' => 'enum', 'values' => ['system', 'custom', 'community'], 'required' => true],
        'template_type' => ['type' => 'enum', 'values' => ['beer_board', 'slide', 'layout'], 'required' => true],
        'base_template' => ['type' => 'string', 'max_length' => 50],
        'theme_settings' => ['type' => 'json', 'required' => true],
        'layout_settings' => ['type' => 'json', 'required' => true],
        'color_scheme' => ['type' => 'json', 'required' => true],
        'typography_settings' => ['type' => 'json', 'required' => true],
        'css_content' => ['type' => 'string'],
        'html_template' => ['type' => 'string'],
        'js_content' => ['type' => 'string'],
        'tags' => ['type' => 'json'],
        'is_public' => ['type' => 'boolean']
    ];
    
    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }
    
    $data = $validation['data'];
    
    // Validate brewery access
    $access = $api->validateBreweryAccess($data['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    // Only admins can create system templates
    if ($data['category'] === 'system' && $user['role'] !== 'admin') {
        echo $api->formatResponse(['error' => 'Only administrators can create system templates'], false, null, 403);
        return;
    }
    
    try {
        // Generate template ID
        $templateId = $api->generateId('template_');
        
        // Insert template
        $stmt = $pdo->prepare("
            INSERT INTO template_library (
                id, brewery_id, name, description, category, template_type, base_template,
                theme_settings, layout_settings, color_scheme, typography_settings,
                css_content, html_template, js_content, tags, is_public, author, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $templateId,
            $data['brewery_id'],
            $data['name'],
            $data['description'],
            $data['category'],
            $data['template_type'],
            $data['base_template'],
            json_encode($data['theme_settings']),
            json_encode($data['layout_settings']),
            json_encode($data['color_scheme']),
            json_encode($data['typography_settings']),
            $data['css_content'],
            $data['html_template'],
            $data['js_content'],
            json_encode($data['tags']),
            $data['is_public'] ?? false,
            $user['name'] ?? $user['email']
        ]);
        
        // Get the created template
        $stmt = $pdo->prepare("SELECT * FROM template_library WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Parse JSON fields
        $template['theme_settings'] = json_decode($template['theme_settings'], true);
        $template['layout_settings'] = json_decode($template['layout_settings'], true);
        $template['color_scheme'] = json_decode($template['color_scheme'], true);
        $template['typography_settings'] = json_decode($template['typography_settings'], true);
        $template['tags'] = json_decode($template['tags'], true);
        
        echo $api->formatResponse($template, true, 'Template created successfully', 201);
        
        // Log activity
        $api->logActivity('create', 'template', $templateId, ['name' => $data['name']]);
        
    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'template creation');
    }
}

/**
 * PUT /api/digital-board/templates
 * Update an existing template
 */
function handleUpdateTemplate($pdo, $api, $user) {
    $templateId = $_GET['id'] ?? null;

    if (!$templateId) {
        echo $api->formatResponse(['error' => 'Template ID required'], false, null, 400);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }

    // Get existing template
    $stmt = $pdo->prepare("SELECT * FROM template_library WHERE id = ?");
    $stmt->execute([$templateId]);
    $existingTemplate = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingTemplate) {
        echo $api->formatResponse(['error' => 'Template not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingTemplate['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // System templates can only be updated by admins
    if ($existingTemplate['category'] === 'system' && $user['role'] !== 'admin') {
        echo $api->formatResponse(['error' => 'Only administrators can update system templates'], false, null, 403);
        return;
    }

    // Validation rules (all optional for updates)
    $rules = [
        'name' => ['type' => 'string', 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'template_type' => ['type' => 'enum', 'values' => ['beer_board', 'slide', 'layout']],
        'base_template' => ['type' => 'string', 'max_length' => 50],
        'theme_settings' => ['type' => 'json'],
        'layout_settings' => ['type' => 'json'],
        'color_scheme' => ['type' => 'json'],
        'typography_settings' => ['type' => 'json'],
        'css_content' => ['type' => 'string'],
        'html_template' => ['type' => 'string'],
        'js_content' => ['type' => 'string'],
        'tags' => ['type' => 'json'],
        'is_public' => ['type' => 'boolean'],
        'is_active' => ['type' => 'boolean']
    ];

    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }

    $data = $validation['data'];

    try {
        // Build update query dynamically
        $updateFields = [];
        $updateParams = [];

        foreach ($data as $field => $value) {
            if ($value !== null) {
                if (in_array($field, ['theme_settings', 'layout_settings', 'color_scheme', 'typography_settings', 'tags'])) {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = json_encode($value);
                } else {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = $value;
                }
            }
        }

        if (empty($updateFields)) {
            echo $api->formatResponse(['error' => 'No valid fields to update'], false, null, 400);
            return;
        }

        // Add updated_at
        $updateFields[] = "updated_at = NOW()";
        $updateParams[] = $templateId;

        $updateQuery = "UPDATE template_library SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute($updateParams);

        // Get updated template
        $stmt = $pdo->prepare("SELECT * FROM template_library WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);

        // Parse JSON fields
        $template['theme_settings'] = json_decode($template['theme_settings'], true);
        $template['layout_settings'] = json_decode($template['layout_settings'], true);
        $template['color_scheme'] = json_decode($template['color_scheme'], true);
        $template['typography_settings'] = json_decode($template['typography_settings'], true);
        $template['tags'] = json_decode($template['tags'], true);

        echo $api->formatResponse($template, true, 'Template updated successfully');

        // Log activity
        $api->logActivity('update', 'template', $templateId, ['updated_fields' => array_keys($data)]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'template update');
    }
}

/**
 * DELETE /api/digital-board/templates
 * Delete a template
 */
function handleDeleteTemplate($pdo, $api, $user) {
    $templateId = $_GET['id'] ?? null;

    if (!$templateId) {
        echo $api->formatResponse(['error' => 'Template ID required'], false, null, 400);
        return;
    }

    // Get existing template
    $stmt = $pdo->prepare("SELECT * FROM template_library WHERE id = ?");
    $stmt->execute([$templateId]);
    $existingTemplate = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingTemplate) {
        echo $api->formatResponse(['error' => 'Template not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingTemplate['brewery_id'], 'delete');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // System templates can only be deleted by admins
    if ($existingTemplate['category'] === 'system' && $user['role'] !== 'admin') {
        echo $api->formatResponse(['error' => 'System templates cannot be deleted'], false, null, 403);
        return;
    }

    // Check if template is in use
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM digital_boards WHERE template_id = ?");
    $stmt->execute([$templateId]);
    $usageCount = $stmt->fetchColumn();

    if ($usageCount > 0) {
        echo $api->formatResponse([
            'error' => 'Template is currently in use by digital boards',
            'usage_count' => $usageCount
        ], false, null, 409);
        return;
    }

    try {
        // Soft delete by setting is_active to false
        $stmt = $pdo->prepare("UPDATE template_library SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$templateId]);

        echo $api->formatResponse([
            'message' => 'Template deleted successfully',
            'template_id' => $templateId
        ], true, 'Template deleted successfully');

        // Log activity
        $api->logActivity('delete', 'template', $templateId, ['name' => $existingTemplate['name']]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'template deletion');
    }
}
?>
