<?php
/**
 * Digital Board Media API
 * Phase 4 11.0 - API Development
 * 
 * Handles media upload and management for digital board content
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAPI.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize API service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $api = new DigitalBoardAPI($pdo, $user);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication
$method = $_SERVER['REQUEST_METHOD'];
if (!$user) {
    echo $api->formatResponse(['error' => 'Authentication required'], false, null, 401);
    exit;
}

// Check rate limiting
if (!$api->checkRateLimit('media', 50, 3600)) {
    echo $api->formatResponse(['error' => 'Rate limit exceeded'], false, null, 429);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetMedia($pdo, $api, $user);
            break;
        case 'POST':
            handleUploadMedia($pdo, $api, $user);
            break;
        case 'DELETE':
            handleDeleteMedia($pdo, $api, $user);
            break;
        default:
            echo $api->formatResponse(['error' => 'Method not allowed'], false, null, 405);
    }
} catch (Exception $e) {
    echo $api->handleDatabaseError($e, 'media operation');
}

/**
 * GET /api/digital-board/media
 * Get media files with filtering and pagination
 */
function handleGetMedia($pdo, $api, $user) {
    $breweryId = $_GET['brewery_id'] ?? null;
    $contentType = $_GET['content_type'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $mediaId = $_GET['id'] ?? null;
    
    // Build query
    $where = ['sc.is_active = 1'];
    $params = [];
    
    // Single media by ID
    if ($mediaId) {
        $where[] = 'sc.id = ?';
        $params[] = $mediaId;
    }
    
    // Filter by brewery
    if ($breweryId) {
        // Validate brewery access
        $access = $api->validateBreweryAccess($breweryId, 'read');
        if (!$access['success']) {
            echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
            return;
        }
        
        $where[] = 'sc.brewery_id = ?';
        $params[] = $breweryId;
    } elseif ($user['role'] !== 'admin') {
        // Non-admin users can only see their own brewery's media
        $where[] = 'sc.brewery_id = ?';
        $params[] = $user['brewery_id'];
    }
    
    // Filter by content type
    if ($contentType) {
        $where[] = 'sc.content_type = ?';
        $params[] = $contentType;
    }
    
    $query = "
        SELECT 
            sc.*,
            b.name as brewery_name,
            (SELECT COUNT(*) FROM slideshow_slides WHERE content_id = sc.id AND is_active = 1) as usage_count
        FROM slide_content sc
        LEFT JOIN breweries b ON sc.brewery_id = b.id
        WHERE " . implode(' AND ', $where) . "
        ORDER BY sc.created_at DESC
    ";
    
    if ($mediaId) {
        // Single media file
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $media = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$media) {
            echo $api->formatResponse(['error' => 'Media not found'], false, null, 404);
            return;
        }
        
        // Parse JSON fields
        $media['tags'] = json_decode($media['tags'], true);
        
        echo $api->formatResponse($media);
    } else {
        // Paginated list
        $result = $api->paginate($query, $params, $page, $limit);
        
        // Parse JSON fields for each media
        foreach ($result['data'] as &$media) {
            $media['tags'] = json_decode($media['tags'], true);
        }
        
        echo $api->formatResponse($result);
    }
    
    // Log activity
    $api->logActivity('view', 'media', $mediaId, ['filters' => $_GET]);
}

/**
 * POST /api/digital-board/media
 * Upload media files
 */
function handleUploadMedia($pdo, $api, $user) {
    $breweryId = $_POST['brewery_id'] ?? null;
    
    if (!$breweryId) {
        echo $api->formatResponse(['error' => 'brewery_id required'], false, null, 400);
        return;
    }
    
    // Validate brewery access
    $access = $api->validateBreweryAccess($breweryId, 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        echo $api->formatResponse(['error' => 'No file uploaded or upload error'], false, null, 400);
        return;
    }
    
    $file = $_FILES['file'];
    $originalFilename = $file['name'];
    $fileSize = $file['size'];
    $mimeType = $file['type'];
    $tmpPath = $file['tmp_name'];
    
    // Validate file type
    $allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/webm', 'video/ogg',
        'audio/mp3', 'audio/wav', 'audio/ogg'
    ];
    
    if (!in_array($mimeType, $allowedTypes)) {
        echo $api->formatResponse(['error' => 'File type not allowed'], false, null, 400);
        return;
    }
    
    // Validate file size (max 50MB)
    $maxSize = 50 * 1024 * 1024; // 50MB
    if ($fileSize > $maxSize) {
        echo $api->formatResponse(['error' => 'File size exceeds maximum limit (50MB)'], false, null, 400);
        return;
    }
    
    // Determine content type
    $contentType = 'document';
    if (strpos($mimeType, 'image/') === 0) {
        $contentType = 'image';
    } elseif (strpos($mimeType, 'video/') === 0) {
        $contentType = 'video';
    } elseif (strpos($mimeType, 'audio/') === 0) {
        $contentType = 'audio';
    }
    
    try {
        // Generate unique filename
        $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
        $filename = $api->generateId('media_') . '.' . $extension;
        
        // Create upload directory if it doesn't exist
        $uploadDir = '../../uploads/digital-board/' . $breweryId . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filePath = $uploadDir . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($tmpPath, $filePath)) {
            echo $api->formatResponse(['error' => 'Failed to save uploaded file'], false, null, 500);
            return;
        }
        
        // Get image/video dimensions if applicable
        $width = null;
        $height = null;
        $duration = null;
        
        if ($contentType === 'image') {
            $imageInfo = getimagesize($filePath);
            if ($imageInfo) {
                $width = $imageInfo[0];
                $height = $imageInfo[1];
            }
        }
        
        // Generate content ID
        $contentId = $api->generateId('content_');
        
        // Insert into database
        $stmt = $pdo->prepare("
            INSERT INTO slide_content (
                id, brewery_id, content_type, filename, original_filename, file_path,
                file_size, mime_type, width, height, duration, title, description,
                processing_status, storage_type, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed', 'local', 1, NOW(), NOW())
        ");
        
        $stmt->execute([
            $contentId,
            $breweryId,
            $contentType,
            $filename,
            $originalFilename,
            $filePath,
            $fileSize,
            $mimeType,
            $width,
            $height,
            $duration,
            $_POST['title'] ?? $originalFilename,
            $_POST['description'] ?? null
        ]);
        
        // Get the created media record
        $stmt = $pdo->prepare("
            SELECT sc.*, b.name as brewery_name
            FROM slide_content sc
            LEFT JOIN breweries b ON sc.brewery_id = b.id
            WHERE sc.id = ?
        ");
        $stmt->execute([$contentId]);
        $media = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo $api->formatResponse($media, true, 'Media uploaded successfully', 201);
        
        // Log activity
        $api->logActivity('upload', 'media', $contentId, [
            'filename' => $originalFilename,
            'type' => $contentType,
            'size' => $fileSize
        ]);
        
    } catch (Exception $e) {
        // Clean up uploaded file if database insert fails
        if (isset($filePath) && file_exists($filePath)) {
            unlink($filePath);
        }
        echo $api->handleDatabaseError($e, 'media upload');
    }
}

/**
 * DELETE /api/digital-board/media
 * Delete a media file
 */
function handleDeleteMedia($pdo, $api, $user) {
    $mediaId = $_GET['id'] ?? null;
    
    if (!$mediaId) {
        echo $api->formatResponse(['error' => 'Media ID required'], false, null, 400);
        return;
    }
    
    // Get existing media
    $stmt = $pdo->prepare("SELECT * FROM slide_content WHERE id = ?");
    $stmt->execute([$mediaId]);
    $existingMedia = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$existingMedia) {
        echo $api->formatResponse(['error' => 'Media not found'], false, null, 404);
        return;
    }
    
    // Validate brewery access
    $access = $api->validateBreweryAccess($existingMedia['brewery_id'], 'delete');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    // Check if media is in use
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM slideshow_slides WHERE content_id = ? AND is_active = 1");
    $stmt->execute([$mediaId]);
    $usageCount = $stmt->fetchColumn();
    
    if ($usageCount > 0) {
        echo $api->formatResponse([
            'error' => 'Media is currently in use by slides',
            'usage_count' => $usageCount
        ], false, null, 409);
        return;
    }
    
    try {
        // Soft delete media record
        $stmt = $pdo->prepare("UPDATE slide_content SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$mediaId]);
        
        // Optionally delete physical file (commented out for safety)
        // if (file_exists($existingMedia['file_path'])) {
        //     unlink($existingMedia['file_path']);
        // }
        
        echo $api->formatResponse([
            'message' => 'Media deleted successfully',
            'media_id' => $mediaId
        ], true, 'Media deleted successfully');
        
        // Log activity
        $api->logActivity('delete', 'media', $mediaId, ['filename' => $existingMedia['filename']]);
        
    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'media deletion');
    }
}
?>
