<?php
/**
 * Digital Board Slideshows API
 * Phase 4 11.0 - API Development
 * 
 * Handles slideshow management operations for digital boards
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAPI.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize API service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $api = new DigitalBoardAPI($pdo, $user);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE']) && !$user) {
    echo $api->formatResponse(['error' => 'Authentication required'], false, null, 401);
    exit;
}

// Check rate limiting
if (!$api->checkRateLimit('slideshows', 150, 3600)) {
    echo $api->formatResponse(['error' => 'Rate limit exceeded'], false, null, 429);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetSlideshows($pdo, $api, $user);
            break;
        case 'POST':
            handleCreateSlideshow($pdo, $api, $user);
            break;
        case 'PUT':
            handleUpdateSlideshow($pdo, $api, $user);
            break;
        case 'DELETE':
            handleDeleteSlideshow($pdo, $api, $user);
            break;
        default:
            echo $api->formatResponse(['error' => 'Method not allowed'], false, null, 405);
    }
} catch (Exception $e) {
    echo $api->handleDatabaseError($e, 'slideshow operation');
}

/**
 * GET /api/digital-board/slideshows
 * Get slideshows with filtering and pagination
 */
function handleGetSlideshows($pdo, $api, $user) {
    $breweryId = $_GET['brewery_id'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $slideshowId = $_GET['id'] ?? null;
    $includeSlides = $_GET['include_slides'] ?? false;
    
    // Build query
    $where = ['sp.is_active = 1'];
    $params = [];
    
    // Single slideshow by ID
    if ($slideshowId) {
        $where[] = 'sp.id = ?';
        $params[] = $slideshowId;
    }
    
    // Filter by brewery
    if ($breweryId) {
        // Validate brewery access
        $access = $api->validateBreweryAccess($breweryId, 'read');
        if (!$access['success']) {
            echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
            return;
        }
        
        $where[] = 'sp.brewery_id = ?';
        $params[] = $breweryId;
    } elseif ($user && $user['role'] !== 'admin') {
        // Non-admin users can only see their own brewery's slideshows
        $where[] = 'sp.brewery_id = ?';
        $params[] = $user['brewery_id'];
    }
    
    $query = "
        SELECT 
            sp.*,
            b.name as brewery_name,
            COUNT(ss.id) as slide_count
        FROM slideshow_presentations sp
        LEFT JOIN breweries b ON sp.brewery_id = b.id
        LEFT JOIN slideshow_slides ss ON sp.id = ss.slideshow_id AND ss.is_active = 1
        WHERE " . implode(' AND ', $where) . "
        GROUP BY sp.id, b.name
        ORDER BY sp.created_at DESC
    ";
    
    if ($slideshowId) {
        // Single slideshow
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$slideshow) {
            echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
            return;
        }
        
        // Parse JSON fields
        $slideshow['schedule_days'] = json_decode($slideshow['schedule_days'], true);
        
        // Include slides if requested
        if ($includeSlides) {
            $slidesStmt = $pdo->prepare("
                SELECT 
                    ss.*,
                    sc.filename as content_filename,
                    sc.file_path as content_file_path,
                    tl.name as template_name
                FROM slideshow_slides ss
                LEFT JOIN slide_content sc ON ss.content_id = sc.id
                LEFT JOIN template_library tl ON ss.template_id = tl.id
                WHERE ss.slideshow_id = ? AND ss.is_active = 1
                ORDER BY ss.slide_order ASC
            ");
            $slidesStmt->execute([$slideshowId]);
            $slides = $slidesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Parse JSON fields for slides
            foreach ($slides as &$slide) {
                $slide['settings'] = json_decode($slide['settings'], true);
                $slide['condition_value'] = json_decode($slide['condition_value'], true);
            }
            
            $slideshow['slides'] = $slides;
        }
        
        echo $api->formatResponse($slideshow);
    } else {
        // Paginated list
        $result = $api->paginate($query, $params, $page, $limit);
        
        // Parse JSON fields for each slideshow
        foreach ($result['data'] as &$slideshow) {
            $slideshow['schedule_days'] = json_decode($slideshow['schedule_days'], true);
        }
        
        echo $api->formatResponse($result);
    }
    
    // Log activity
    $api->logActivity('view', 'slideshows', $slideshowId, ['filters' => $_GET]);
}

/**
 * POST /api/digital-board/slideshows
 * Create a new slideshow
 */
function handleCreateSlideshow($pdo, $api, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }
    
    // Validation rules
    $rules = [
        'brewery_id' => ['type' => 'string', 'required' => true],
        'name' => ['type' => 'string', 'required' => true, 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'loop_enabled' => ['type' => 'boolean'],
        'auto_advance' => ['type' => 'boolean'],
        'global_duration' => ['type' => 'int'],
        'global_transition' => ['type' => 'string', 'max_length' => 50],
        'shuffle_slides' => ['type' => 'boolean'],
        'pause_on_hover' => ['type' => 'boolean'],
        'show_controls' => ['type' => 'boolean'],
        'show_indicators' => ['type' => 'boolean'],
        'show_progress' => ['type' => 'boolean'],
        'schedule_enabled' => ['type' => 'boolean'],
        'schedule_start_time' => ['type' => 'string'],
        'schedule_end_time' => ['type' => 'string'],
        'schedule_days' => ['type' => 'json'],
        'transition_duration' => ['type' => 'float'],
        'preload_slides' => ['type' => 'boolean'],
        'cache_duration' => ['type' => 'int']
    ];
    
    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }
    
    $data = $validation['data'];
    
    // Validate brewery access
    $access = $api->validateBreweryAccess($data['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    try {
        // Generate slideshow ID
        $slideshowId = $api->generateId('slideshow_');
        
        // Insert slideshow
        $stmt = $pdo->prepare("
            INSERT INTO slideshow_presentations (
                id, brewery_id, name, description, is_active, loop_enabled, auto_advance,
                global_duration, global_transition, shuffle_slides, pause_on_hover,
                show_controls, show_indicators, show_progress, schedule_enabled,
                schedule_start_time, schedule_end_time, schedule_days, transition_duration,
                preload_slides, cache_duration, created_at, updated_at
            ) VALUES (?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $slideshowId,
            $data['brewery_id'],
            $data['name'],
            $data['description'],
            $data['loop_enabled'] ?? true,
            $data['auto_advance'] ?? true,
            $data['global_duration'] ?? 10,
            $data['global_transition'] ?? 'fade',
            $data['shuffle_slides'] ?? false,
            $data['pause_on_hover'] ?? false,
            $data['show_controls'] ?? true,
            $data['show_indicators'] ?? true,
            $data['show_progress'] ?? true,
            $data['schedule_enabled'] ?? false,
            $data['schedule_start_time'],
            $data['schedule_end_time'],
            json_encode($data['schedule_days']),
            $data['transition_duration'] ?? 1.0,
            $data['preload_slides'] ?? true,
            $data['cache_duration'] ?? 3600
        ]);
        
        // Get the created slideshow
        $stmt = $pdo->prepare("
            SELECT sp.*, b.name as brewery_name
            FROM slideshow_presentations sp
            LEFT JOIN breweries b ON sp.brewery_id = b.id
            WHERE sp.id = ?
        ");
        $stmt->execute([$slideshowId]);
        $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Parse JSON fields
        $slideshow['schedule_days'] = json_decode($slideshow['schedule_days'], true);
        
        echo $api->formatResponse($slideshow, true, 'Slideshow created successfully', 201);
        
        // Log activity
        $api->logActivity('create', 'slideshow', $slideshowId, ['name' => $data['name']]);
        
    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'slideshow creation');
    }
}

/**
 * PUT /api/digital-board/slideshows
 * Update an existing slideshow
 */
function handleUpdateSlideshow($pdo, $api, $user) {
    $slideshowId = $_GET['id'] ?? null;

    if (!$slideshowId) {
        echo $api->formatResponse(['error' => 'Slideshow ID required'], false, null, 400);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }

    // Get existing slideshow
    $stmt = $pdo->prepare("SELECT * FROM slideshow_presentations WHERE id = ?");
    $stmt->execute([$slideshowId]);
    $existingSlideshow = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingSlideshow) {
        echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingSlideshow['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // Validation rules (all optional for updates)
    $rules = [
        'name' => ['type' => 'string', 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'is_active' => ['type' => 'boolean'],
        'loop_enabled' => ['type' => 'boolean'],
        'auto_advance' => ['type' => 'boolean'],
        'global_duration' => ['type' => 'int'],
        'global_transition' => ['type' => 'string', 'max_length' => 50],
        'shuffle_slides' => ['type' => 'boolean'],
        'pause_on_hover' => ['type' => 'boolean'],
        'show_controls' => ['type' => 'boolean'],
        'show_indicators' => ['type' => 'boolean'],
        'show_progress' => ['type' => 'boolean'],
        'schedule_enabled' => ['type' => 'boolean'],
        'schedule_start_time' => ['type' => 'string'],
        'schedule_end_time' => ['type' => 'string'],
        'schedule_days' => ['type' => 'json'],
        'transition_duration' => ['type' => 'float'],
        'preload_slides' => ['type' => 'boolean'],
        'cache_duration' => ['type' => 'int']
    ];

    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }

    $data = $validation['data'];

    try {
        // Build update query dynamically
        $updateFields = [];
        $updateParams = [];

        foreach ($data as $field => $value) {
            if ($value !== null) {
                if ($field === 'schedule_days') {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = json_encode($value);
                } else {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = $value;
                }
            }
        }

        if (empty($updateFields)) {
            echo $api->formatResponse(['error' => 'No valid fields to update'], false, null, 400);
            return;
        }

        // Add updated_at
        $updateFields[] = "updated_at = NOW()";
        $updateParams[] = $slideshowId;

        $updateQuery = "UPDATE slideshow_presentations SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute($updateParams);

        // Get updated slideshow
        $stmt = $pdo->prepare("
            SELECT sp.*, b.name as brewery_name
            FROM slideshow_presentations sp
            LEFT JOIN breweries b ON sp.brewery_id = b.id
            WHERE sp.id = ?
        ");
        $stmt->execute([$slideshowId]);
        $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);

        // Parse JSON fields
        $slideshow['schedule_days'] = json_decode($slideshow['schedule_days'], true);

        echo $api->formatResponse($slideshow, true, 'Slideshow updated successfully');

        // Log activity
        $api->logActivity('update', 'slideshow', $slideshowId, ['updated_fields' => array_keys($data)]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'slideshow update');
    }
}

/**
 * DELETE /api/digital-board/slideshows
 * Delete a slideshow
 */
function handleDeleteSlideshow($pdo, $api, $user) {
    $slideshowId = $_GET['id'] ?? null;

    if (!$slideshowId) {
        echo $api->formatResponse(['error' => 'Slideshow ID required'], false, null, 400);
        return;
    }

    // Get existing slideshow
    $stmt = $pdo->prepare("SELECT * FROM slideshow_presentations WHERE id = ?");
    $stmt->execute([$slideshowId]);
    $existingSlideshow = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingSlideshow) {
        echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingSlideshow['brewery_id'], 'delete');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // Check if slideshow is in use by digital boards
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM digital_boards WHERE current_slideshow_id = ?");
    $stmt->execute([$slideshowId]);
    $usageCount = $stmt->fetchColumn();

    if ($usageCount > 0) {
        echo $api->formatResponse([
            'error' => 'Slideshow is currently in use by digital boards',
            'usage_count' => $usageCount
        ], false, null, 409);
        return;
    }

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Soft delete slides first
        $stmt = $pdo->prepare("UPDATE slideshow_slides SET is_active = 0, updated_at = NOW() WHERE slideshow_id = ?");
        $stmt->execute([$slideshowId]);

        // Soft delete slideshow
        $stmt = $pdo->prepare("UPDATE slideshow_presentations SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$slideshowId]);

        $pdo->commit();

        echo $api->formatResponse([
            'message' => 'Slideshow deleted successfully',
            'slideshow_id' => $slideshowId
        ], true, 'Slideshow deleted successfully');

        // Log activity
        $api->logActivity('delete', 'slideshow', $slideshowId, ['name' => $existingSlideshow['name']]);

    } catch (Exception $e) {
        $pdo->rollBack();
        echo $api->handleDatabaseError($e, 'slideshow deletion');
    }
}
?>
