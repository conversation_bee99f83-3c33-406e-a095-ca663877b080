<?php
/**
 * Digital Board Slides API
 * Phase 4 11.0 - API Development
 * 
 * Handles individual slide management operations for slideshows
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAPI.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize API service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $api = new DigitalBoardAPI($pdo, $user);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE']) && !$user) {
    echo $api->formatResponse(['error' => 'Authentication required'], false, null, 401);
    exit;
}

// Check rate limiting
if (!$api->checkRateLimit('slides', 200, 3600)) {
    echo $api->formatResponse(['error' => 'Rate limit exceeded'], false, null, 429);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetSlides($pdo, $api, $user);
            break;
        case 'POST':
            if (isset($_GET['action']) && $_GET['action'] === 'reorder') {
                handleReorderSlides($pdo, $api, $user);
            } else {
                handleCreateSlide($pdo, $api, $user);
            }
            break;
        case 'PUT':
            handleUpdateSlide($pdo, $api, $user);
            break;
        case 'DELETE':
            handleDeleteSlide($pdo, $api, $user);
            break;
        default:
            echo $api->formatResponse(['error' => 'Method not allowed'], false, null, 405);
    }
} catch (Exception $e) {
    echo $api->handleDatabaseError($e, 'slide operation');
}

/**
 * GET /api/digital-board/slides
 * Get slides for a slideshow
 */
function handleGetSlides($pdo, $api, $user) {
    $slideshowId = $_GET['slideshow_id'] ?? null;
    $slideId = $_GET['id'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 50;
    
    if (!$slideshowId && !$slideId) {
        echo $api->formatResponse(['error' => 'Slideshow ID or Slide ID required'], false, null, 400);
        return;
    }
    
    // Validate slideshow access if slideshow_id provided
    if ($slideshowId) {
        $stmt = $pdo->prepare("SELECT brewery_id FROM slideshow_presentations WHERE id = ?");
        $stmt->execute([$slideshowId]);
        $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$slideshow) {
            echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
            return;
        }
        
        $access = $api->validateBreweryAccess($slideshow['brewery_id'], 'read');
        if (!$access['success']) {
            echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
            return;
        }
    }
    
    // Build query
    $where = ['ss.is_active = 1'];
    $params = [];
    
    if ($slideId) {
        $where[] = 'ss.id = ?';
        $params[] = $slideId;
    } else {
        $where[] = 'ss.slideshow_id = ?';
        $params[] = $slideshowId;
    }
    
    $query = "
        SELECT 
            ss.*,
            sc.filename as content_filename,
            sc.file_path as content_file_path,
            sc.mime_type as content_mime_type,
            tl.name as template_name,
            sp.name as slideshow_name
        FROM slideshow_slides ss
        LEFT JOIN slide_content sc ON ss.content_id = sc.id
        LEFT JOIN template_library tl ON ss.template_id = tl.id
        LEFT JOIN slideshow_presentations sp ON ss.slideshow_id = sp.id
        WHERE " . implode(' AND ', $where) . "
        ORDER BY ss.slide_order ASC, ss.created_at ASC
    ";
    
    if ($slideId) {
        // Single slide
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $slide = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$slide) {
            echo $api->formatResponse(['error' => 'Slide not found'], false, null, 404);
            return;
        }
        
        // Parse JSON fields
        $slide['settings'] = json_decode($slide['settings'], true);
        $slide['condition_value'] = json_decode($slide['condition_value'], true);
        
        echo $api->formatResponse($slide);
    } else {
        // Paginated list
        $result = $api->paginate($query, $params, $page, $limit);
        
        // Parse JSON fields for each slide
        foreach ($result['data'] as &$slide) {
            $slide['settings'] = json_decode($slide['settings'], true);
            $slide['condition_value'] = json_decode($slide['condition_value'], true);
        }
        
        echo $api->formatResponse($result);
    }
    
    // Log activity
    $api->logActivity('view', 'slides', $slideId, ['slideshow_id' => $slideshowId]);
}

/**
 * POST /api/digital-board/slides
 * Create a new slide
 */
function handleCreateSlide($pdo, $api, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }
    
    // Validation rules
    $rules = [
        'slideshow_id' => ['type' => 'string', 'required' => true],
        'title' => ['type' => 'string', 'required' => true, 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'slide_type' => ['type' => 'enum', 'values' => ['beer_board', 'image', 'video', 'html', 'events', 'social', 'weather', 'qr_code'], 'required' => true],
        'content_id' => ['type' => 'string'],
        'template_id' => ['type' => 'string'],
        'duration' => ['type' => 'int'],
        'transition' => ['type' => 'string', 'max_length' => 50],
        'background_color' => ['type' => 'string', 'max_length' => 7],
        'background_image' => ['type' => 'string', 'max_length' => 255],
        'settings' => ['type' => 'json'],
        'beer_board_template' => ['type' => 'string', 'max_length' => 50],
        'show_header' => ['type' => 'boolean'],
        'show_ticker' => ['type' => 'boolean'],
        'custom_message' => ['type' => 'string'],
        'media_url' => ['type' => 'string', 'max_length' => 500],
        'media_alt_text' => ['type' => 'string', 'max_length' => 255],
        'overlay_text' => ['type' => 'string'],
        'overlay_position' => ['type' => 'enum', 'values' => ['top', 'center', 'bottom']],
        'html_content' => ['type' => 'string'],
        'css_styles' => ['type' => 'string'],
        'clickable' => ['type' => 'boolean'],
        'click_action' => ['type' => 'enum', 'values' => ['none', 'url', 'next_slide', 'pause']],
        'click_url' => ['type' => 'string', 'max_length' => 500],
        'condition_enabled' => ['type' => 'boolean'],
        'condition_type' => ['type' => 'enum', 'values' => ['time', 'weather', 'inventory', 'custom']],
        'condition_value' => ['type' => 'json']
    ];
    
    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }
    
    $data = $validation['data'];
    
    // Validate slideshow access
    $stmt = $pdo->prepare("SELECT brewery_id FROM slideshow_presentations WHERE id = ?");
    $stmt->execute([$data['slideshow_id']]);
    $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$slideshow) {
        echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
        return;
    }
    
    $access = $api->validateBreweryAccess($slideshow['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    try {
        // Get next slide order
        $stmt = $pdo->prepare("SELECT COALESCE(MAX(slide_order), 0) + 1 as next_order FROM slideshow_slides WHERE slideshow_id = ?");
        $stmt->execute([$data['slideshow_id']]);
        $nextOrder = $stmt->fetchColumn();
        
        // Generate slide ID
        $slideId = $api->generateId('slide_');
        
        // Insert slide
        $stmt = $pdo->prepare("
            INSERT INTO slideshow_slides (
                id, slideshow_id, slide_order, title, description, slide_type, content_id, template_id,
                duration, transition, background_color, background_image, settings, beer_board_template,
                show_header, show_ticker, custom_message, media_url, media_alt_text, overlay_text,
                overlay_position, html_content, css_styles, clickable, click_action, click_url,
                condition_enabled, condition_type, condition_value, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        ");
        
        $stmt->execute([
            $slideId,
            $data['slideshow_id'],
            $nextOrder,
            $data['title'],
            $data['description'],
            $data['slide_type'],
            $data['content_id'],
            $data['template_id'],
            $data['duration'] ?? 10,
            $data['transition'] ?? 'fade',
            $data['background_color'],
            $data['background_image'],
            json_encode($data['settings'] ?? []),
            $data['beer_board_template'],
            $data['show_header'] ?? true,
            $data['show_ticker'] ?? false,
            $data['custom_message'],
            $data['media_url'],
            $data['media_alt_text'],
            $data['overlay_text'],
            $data['overlay_position'] ?? 'bottom',
            $data['html_content'],
            $data['css_styles'],
            $data['clickable'] ?? false,
            $data['click_action'] ?? 'none',
            $data['click_url'],
            $data['condition_enabled'] ?? false,
            $data['condition_type'],
            json_encode($data['condition_value'])
        ]);
        
        // Get the created slide
        $stmt = $pdo->prepare("
            SELECT ss.*, sp.name as slideshow_name
            FROM slideshow_slides ss
            LEFT JOIN slideshow_presentations sp ON ss.slideshow_id = sp.id
            WHERE ss.id = ?
        ");
        $stmt->execute([$slideId]);
        $slide = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Parse JSON fields
        $slide['settings'] = json_decode($slide['settings'], true);
        $slide['condition_value'] = json_decode($slide['condition_value'], true);
        
        echo $api->formatResponse($slide, true, 'Slide created successfully', 201);
        
        // Log activity
        $api->logActivity('create', 'slide', $slideId, ['title' => $data['title'], 'type' => $data['slide_type']]);
        
    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'slide creation');
    }
}

/**
 * POST /api/digital-board/slides?action=reorder
 * Reorder slides in a slideshow
 */
function handleReorderSlides($pdo, $api, $user) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['slideshow_id']) || !isset($input['slide_orders'])) {
        echo $api->formatResponse(['error' => 'slideshow_id and slide_orders required'], false, null, 400);
        return;
    }

    $slideshowId = $input['slideshow_id'];
    $slideOrders = $input['slide_orders']; // Array of ['slide_id' => order]

    // Validate slideshow access
    $stmt = $pdo->prepare("SELECT brewery_id FROM slideshow_presentations WHERE id = ?");
    $stmt->execute([$slideshowId]);
    $slideshow = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$slideshow) {
        echo $api->formatResponse(['error' => 'Slideshow not found'], false, null, 404);
        return;
    }

    $access = $api->validateBreweryAccess($slideshow['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // Update slide orders
        foreach ($slideOrders as $slideId => $order) {
            $stmt = $pdo->prepare("UPDATE slideshow_slides SET slide_order = ?, updated_at = NOW() WHERE id = ? AND slideshow_id = ?");
            $stmt->execute([$order, $slideId, $slideshowId]);
        }

        $pdo->commit();

        echo $api->formatResponse([
            'message' => 'Slides reordered successfully',
            'slideshow_id' => $slideshowId,
            'updated_slides' => count($slideOrders)
        ], true, 'Slides reordered successfully');

        // Log activity
        $api->logActivity('reorder', 'slides', null, ['slideshow_id' => $slideshowId, 'count' => count($slideOrders)]);

    } catch (Exception $e) {
        $pdo->rollBack();
        echo $api->handleDatabaseError($e, 'slide reordering');
    }
}

/**
 * PUT /api/digital-board/slides
 * Update an existing slide
 */
function handleUpdateSlide($pdo, $api, $user) {
    $slideId = $_GET['id'] ?? null;

    if (!$slideId) {
        echo $api->formatResponse(['error' => 'Slide ID required'], false, null, 400);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }

    // Get existing slide and validate access
    $stmt = $pdo->prepare("
        SELECT ss.*, sp.brewery_id
        FROM slideshow_slides ss
        JOIN slideshow_presentations sp ON ss.slideshow_id = sp.id
        WHERE ss.id = ?
    ");
    $stmt->execute([$slideId]);
    $existingSlide = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingSlide) {
        echo $api->formatResponse(['error' => 'Slide not found'], false, null, 404);
        return;
    }

    $access = $api->validateBreweryAccess($existingSlide['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // Validation rules (all optional for updates)
    $rules = [
        'title' => ['type' => 'string', 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'slide_type' => ['type' => 'enum', 'values' => ['beer_board', 'image', 'video', 'html', 'events', 'social', 'weather', 'qr_code']],
        'content_id' => ['type' => 'string'],
        'template_id' => ['type' => 'string'],
        'duration' => ['type' => 'int'],
        'transition' => ['type' => 'string', 'max_length' => 50],
        'background_color' => ['type' => 'string', 'max_length' => 7],
        'background_image' => ['type' => 'string', 'max_length' => 255],
        'settings' => ['type' => 'json'],
        'beer_board_template' => ['type' => 'string', 'max_length' => 50],
        'show_header' => ['type' => 'boolean'],
        'show_ticker' => ['type' => 'boolean'],
        'custom_message' => ['type' => 'string'],
        'media_url' => ['type' => 'string', 'max_length' => 500],
        'media_alt_text' => ['type' => 'string', 'max_length' => 255],
        'overlay_text' => ['type' => 'string'],
        'overlay_position' => ['type' => 'enum', 'values' => ['top', 'center', 'bottom']],
        'html_content' => ['type' => 'string'],
        'css_styles' => ['type' => 'string'],
        'clickable' => ['type' => 'boolean'],
        'click_action' => ['type' => 'enum', 'values' => ['none', 'url', 'next_slide', 'pause']],
        'click_url' => ['type' => 'string', 'max_length' => 500],
        'condition_enabled' => ['type' => 'boolean'],
        'condition_type' => ['type' => 'enum', 'values' => ['time', 'weather', 'inventory', 'custom']],
        'condition_value' => ['type' => 'json'],
        'is_active' => ['type' => 'boolean']
    ];

    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }

    $data = $validation['data'];

    try {
        // Build update query dynamically
        $updateFields = [];
        $updateParams = [];

        foreach ($data as $field => $value) {
            if ($value !== null) {
                if (in_array($field, ['settings', 'condition_value'])) {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = json_encode($value);
                } else {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = $value;
                }
            }
        }

        if (empty($updateFields)) {
            echo $api->formatResponse(['error' => 'No valid fields to update'], false, null, 400);
            return;
        }

        // Add updated_at
        $updateFields[] = "updated_at = NOW()";
        $updateParams[] = $slideId;

        $updateQuery = "UPDATE slideshow_slides SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute($updateParams);

        // Get updated slide
        $stmt = $pdo->prepare("
            SELECT ss.*, sp.name as slideshow_name
            FROM slideshow_slides ss
            LEFT JOIN slideshow_presentations sp ON ss.slideshow_id = sp.id
            WHERE ss.id = ?
        ");
        $stmt->execute([$slideId]);
        $slide = $stmt->fetch(PDO::FETCH_ASSOC);

        // Parse JSON fields
        $slide['settings'] = json_decode($slide['settings'], true);
        $slide['condition_value'] = json_decode($slide['condition_value'], true);

        echo $api->formatResponse($slide, true, 'Slide updated successfully');

        // Log activity
        $api->logActivity('update', 'slide', $slideId, ['updated_fields' => array_keys($data)]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'slide update');
    }
}

/**
 * DELETE /api/digital-board/slides
 * Delete a slide
 */
function handleDeleteSlide($pdo, $api, $user) {
    $slideId = $_GET['id'] ?? null;

    if (!$slideId) {
        echo $api->formatResponse(['error' => 'Slide ID required'], false, null, 400);
        return;
    }

    // Get existing slide and validate access
    $stmt = $pdo->prepare("
        SELECT ss.*, sp.brewery_id
        FROM slideshow_slides ss
        JOIN slideshow_presentations sp ON ss.slideshow_id = sp.id
        WHERE ss.id = ?
    ");
    $stmt->execute([$slideId]);
    $existingSlide = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingSlide) {
        echo $api->formatResponse(['error' => 'Slide not found'], false, null, 404);
        return;
    }

    $access = $api->validateBreweryAccess($existingSlide['brewery_id'], 'delete');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    try {
        // Soft delete slide
        $stmt = $pdo->prepare("UPDATE slideshow_slides SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$slideId]);

        echo $api->formatResponse([
            'message' => 'Slide deleted successfully',
            'slide_id' => $slideId
        ], true, 'Slide deleted successfully');

        // Log activity
        $api->logActivity('delete', 'slide', $slideId, ['title' => $existingSlide['title']]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'slide deletion');
    }
}
?>
