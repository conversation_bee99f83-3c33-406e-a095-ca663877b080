<?php
/**
 * Enhanced Digital Boards API
 * Phase 4 11.0 - API Development
 * 
 * Handles enhanced digital board management with new schema features
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAPI.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize API service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $api = new DigitalBoardAPI($pdo, $user);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication for write operations
$method = $_SERVER['REQUEST_METHOD'];
if (in_array($method, ['POST', 'PUT', 'DELETE']) && !$user) {
    echo $api->formatResponse(['error' => 'Authentication required'], false, null, 401);
    exit;
}

// Check rate limiting
if (!$api->checkRateLimit('boards', 100, 3600)) {
    echo $api->formatResponse(['error' => 'Rate limit exceeded'], false, null, 429);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetBoards($pdo, $api, $user);
            break;
        case 'POST':
            handleCreateBoard($pdo, $api, $user);
            break;
        case 'PUT':
            handleUpdateBoard($pdo, $api, $user);
            break;
        case 'DELETE':
            handleDeleteBoard($pdo, $api, $user);
            break;
        default:
            echo $api->formatResponse(['error' => 'Method not allowed'], false, null, 405);
    }
} catch (Exception $e) {
    echo $api->handleDatabaseError($e, 'board operation');
}

/**
 * GET /api/digital-board/boards
 * Get digital boards with enhanced features
 */
function handleGetBoards($pdo, $api, $user) {
    $breweryId = $_GET['brewery_id'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $boardId = $_GET['id'] ?? null;
    $includeStats = $_GET['include_stats'] ?? false;
    
    // Build query
    $where = ['db.is_active = 1'];
    $params = [];
    
    // Single board by ID
    if ($boardId) {
        $where[] = 'db.id = ?';
        $params[] = $boardId;
    }
    
    // Filter by brewery
    if ($breweryId) {
        // Validate brewery access
        $access = $api->validateBreweryAccess($breweryId, 'read');
        if (!$access['success']) {
            echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
            return;
        }
        
        $where[] = 'db.brewery_id = ?';
        $params[] = $breweryId;
    } elseif ($user && $user['role'] !== 'admin') {
        // Non-admin users can only see their own brewery's boards
        $where[] = 'db.brewery_id = ?';
        $params[] = $user['brewery_id'];
    }
    
    $query = "
        SELECT 
            db.*,
            b.name as brewery_name,
            tl.name as template_name,
            tl.category as template_category,
            sp.name as slideshow_name,
            sp.total_slides as slideshow_slide_count
        FROM digital_boards db
        LEFT JOIN breweries b ON db.brewery_id = b.id
        LEFT JOIN template_library tl ON db.template_id = tl.id
        LEFT JOIN slideshow_presentations sp ON db.current_slideshow_id = sp.id
        WHERE " . implode(' AND ', $where) . "
        ORDER BY db.created_at DESC
    ";
    
    if ($boardId) {
        // Single board
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $board = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$board) {
            echo $api->formatResponse(['error' => 'Board not found'], false, null, 404);
            return;
        }
        
        // Parse JSON fields
        $board['settings'] = json_decode($board['settings'], true);
        $board['allowed_ips'] = json_decode($board['allowed_ips'], true);
        
        // Include statistics if requested
        if ($includeStats) {
            $board['stats'] = getBoardStats($pdo, $boardId);
        }
        
        echo $api->formatResponse($board);
    } else {
        // Paginated list
        $result = $api->paginate($query, $params, $page, $limit);
        
        // Parse JSON fields for each board
        foreach ($result['data'] as &$board) {
            $board['settings'] = json_decode($board['settings'], true);
            $board['allowed_ips'] = json_decode($board['allowed_ips'], true);
        }
        
        echo $api->formatResponse($result);
    }
    
    // Log activity
    $api->logActivity('view', 'boards', $boardId, ['filters' => $_GET]);
}

/**
 * POST /api/digital-board/boards
 * Create a new enhanced digital board
 */
function handleCreateBoard($pdo, $api, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }
    
    // Validation rules
    $rules = [
        'brewery_id' => ['type' => 'string', 'required' => true],
        'board_id' => ['type' => 'string', 'required' => true, 'max_length' => 100],
        'name' => ['type' => 'string', 'required' => true, 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'template_id' => ['type' => 'string'],
        'current_slideshow_id' => ['type' => 'string'],
        'display_mode' => ['type' => 'enum', 'values' => ['static', 'slideshow', 'hybrid']],
        'theme' => ['type' => 'string', 'max_length' => 50],
        'layout' => ['type' => 'enum', 'values' => ['grid', 'list', 'cards']],
        'columns_count' => ['type' => 'int'],
        'show_prices' => ['type' => 'boolean'],
        'show_descriptions' => ['type' => 'boolean'],
        'show_abv' => ['type' => 'boolean'],
        'show_ibu' => ['type' => 'boolean'],
        'show_tap_numbers' => ['type' => 'boolean'],
        'show_availability' => ['type' => 'boolean'],
        'show_header' => ['type' => 'boolean'],
        'show_footer' => ['type' => 'boolean'],
        'show_ticker' => ['type' => 'boolean'],
        'ticker_message' => ['type' => 'string'],
        'ticker_speed' => ['type' => 'int'],
        'ticker_enabled' => ['type' => 'boolean'],
        'background_color' => ['type' => 'string', 'max_length' => 7],
        'background_image' => ['type' => 'string', 'max_length' => 255],
        'is_public' => ['type' => 'boolean'],
        'access_code' => ['type' => 'string', 'max_length' => 20],
        'settings' => ['type' => 'json']
    ];
    
    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }
    
    $data = $validation['data'];
    
    // Validate brewery access
    $access = $api->validateBreweryAccess($data['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }
    
    // Check if board_id is unique for this brewery
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM digital_boards WHERE brewery_id = ? AND board_id = ?");
    $stmt->execute([$data['brewery_id'], $data['board_id']]);
    if ($stmt->fetchColumn() > 0) {
        echo $api->formatResponse(['error' => 'Board ID already exists for this brewery'], false, null, 409);
        return;
    }
    
    try {
        // Generate board ID
        $id = $api->generateId('board_');
        
        // Insert board with all enhanced fields
        $stmt = $pdo->prepare("
            INSERT INTO digital_boards (
                id, brewery_id, board_id, name, description, settings, template_id, current_slideshow_id,
                display_mode, refresh_interval, auto_refresh, fullscreen_mode, theme, layout, columns_count,
                show_prices, show_descriptions, show_abv, show_ibu, show_tap_numbers, show_availability,
                show_header, show_footer, show_ticker, ticker_message, ticker_speed, ticker_enabled,
                background_color, background_image, is_public, access_code, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        ");
        
        $stmt->execute([
            $id,
            $data['brewery_id'],
            $data['board_id'],
            $data['name'],
            $data['description'],
            json_encode($data['settings'] ?? []),
            $data['template_id'],
            $data['current_slideshow_id'],
            $data['display_mode'] ?? 'static',
            300, // refresh_interval
            true, // auto_refresh
            false, // fullscreen_mode
            $data['theme'] ?? 'beersty-professional',
            $data['layout'] ?? 'grid',
            $data['columns_count'] ?? 3,
            $data['show_prices'] ?? true,
            $data['show_descriptions'] ?? true,
            $data['show_abv'] ?? true,
            $data['show_ibu'] ?? true,
            $data['show_tap_numbers'] ?? true,
            $data['show_availability'] ?? true,
            $data['show_header'] ?? true,
            $data['show_footer'] ?? true,
            $data['show_ticker'] ?? false,
            $data['ticker_message'],
            $data['ticker_speed'] ?? 50,
            $data['ticker_enabled'] ?? false,
            $data['background_color'] ?? '#1a1a1a',
            $data['background_image'],
            $data['is_public'] ?? false,
            $data['access_code']
        ]);
        
        // Get the created board
        $stmt = $pdo->prepare("
            SELECT db.*, b.name as brewery_name
            FROM digital_boards db
            LEFT JOIN breweries b ON db.brewery_id = b.id
            WHERE db.id = ?
        ");
        $stmt->execute([$id]);
        $board = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Parse JSON fields
        $board['settings'] = json_decode($board['settings'], true);
        
        echo $api->formatResponse($board, true, 'Digital board created successfully', 201);
        
        // Log activity
        $api->logActivity('create', 'board', $id, ['name' => $data['name'], 'board_id' => $data['board_id']]);
        
    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'board creation');
    }
}

/**
 * Get board statistics
 */
function getBoardStats($pdo, $boardId) {
    $stats = [];
    
    // Get view count and last accessed
    $stmt = $pdo->prepare("SELECT view_count, last_accessed FROM digital_boards WHERE id = ?");
    $stmt->execute([$boardId]);
    $boardData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stats['view_count'] = $boardData['view_count'] ?? 0;
    $stats['last_accessed'] = $boardData['last_accessed'];
    
    // Get beer menu stats
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_beers,
            COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_beers,
            COUNT(CASE WHEN tap_number IS NOT NULL THEN 1 END) as on_tap,
            AVG(abv) as avg_abv,
            AVG(ibu) as avg_ibu
        FROM beer_menu bm
        JOIN digital_boards db ON bm.brewery_id = db.brewery_id
        WHERE db.id = ?
    ");
    $stmt->execute([$boardId]);
    $beerStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stats['beer_stats'] = $beerStats;
    
    return $stats;
}

/**
 * PUT /api/digital-board/boards
 * Update an existing digital board
 */
function handleUpdateBoard($pdo, $api, $user) {
    $boardId = $_GET['id'] ?? null;

    if (!$boardId) {
        echo $api->formatResponse(['error' => 'Board ID required'], false, null, 400);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo $api->formatResponse(['error' => 'Invalid JSON input'], false, null, 400);
        return;
    }

    // Get existing board
    $stmt = $pdo->prepare("SELECT * FROM digital_boards WHERE id = ?");
    $stmt->execute([$boardId]);
    $existingBoard = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingBoard) {
        echo $api->formatResponse(['error' => 'Board not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingBoard['brewery_id'], 'write');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    // Validation rules (all optional for updates)
    $rules = [
        'name' => ['type' => 'string', 'max_length' => 255],
        'description' => ['type' => 'string', 'max_length' => 1000],
        'template_id' => ['type' => 'string'],
        'current_slideshow_id' => ['type' => 'string'],
        'display_mode' => ['type' => 'enum', 'values' => ['static', 'slideshow', 'hybrid']],
        'theme' => ['type' => 'string', 'max_length' => 50],
        'layout' => ['type' => 'enum', 'values' => ['grid', 'list', 'cards']],
        'columns_count' => ['type' => 'int'],
        'show_prices' => ['type' => 'boolean'],
        'show_descriptions' => ['type' => 'boolean'],
        'show_abv' => ['type' => 'boolean'],
        'show_ibu' => ['type' => 'boolean'],
        'show_tap_numbers' => ['type' => 'boolean'],
        'show_availability' => ['type' => 'boolean'],
        'show_header' => ['type' => 'boolean'],
        'show_footer' => ['type' => 'boolean'],
        'show_ticker' => ['type' => 'boolean'],
        'ticker_message' => ['type' => 'string'],
        'ticker_speed' => ['type' => 'int'],
        'ticker_enabled' => ['type' => 'boolean'],
        'background_color' => ['type' => 'string', 'max_length' => 7],
        'background_image' => ['type' => 'string', 'max_length' => 255],
        'is_public' => ['type' => 'boolean'],
        'access_code' => ['type' => 'string', 'max_length' => 20],
        'is_active' => ['type' => 'boolean'],
        'settings' => ['type' => 'json']
    ];

    $validation = $api->validateInput($input, $rules);
    if (!$validation['success']) {
        echo $api->formatResponse(['errors' => $validation['errors']], false, null, 400);
        return;
    }

    $data = $validation['data'];

    try {
        // Build update query dynamically
        $updateFields = [];
        $updateParams = [];

        foreach ($data as $field => $value) {
            if ($value !== null) {
                if ($field === 'settings') {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = json_encode($value);
                } else {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = $value;
                }
            }
        }

        if (empty($updateFields)) {
            echo $api->formatResponse(['error' => 'No valid fields to update'], false, null, 400);
            return;
        }

        // Add updated_at
        $updateFields[] = "updated_at = NOW()";
        $updateParams[] = $boardId;

        $updateQuery = "UPDATE digital_boards SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute($updateParams);

        // Get updated board
        $stmt = $pdo->prepare("
            SELECT db.*, b.name as brewery_name
            FROM digital_boards db
            LEFT JOIN breweries b ON db.brewery_id = b.id
            WHERE db.id = ?
        ");
        $stmt->execute([$boardId]);
        $board = $stmt->fetch(PDO::FETCH_ASSOC);

        // Parse JSON fields
        $board['settings'] = json_decode($board['settings'], true);
        $board['allowed_ips'] = json_decode($board['allowed_ips'], true);

        echo $api->formatResponse($board, true, 'Board updated successfully');

        // Log activity
        $api->logActivity('update', 'board', $boardId, ['updated_fields' => array_keys($data)]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'board update');
    }
}

/**
 * DELETE /api/digital-board/boards
 * Delete a digital board
 */
function handleDeleteBoard($pdo, $api, $user) {
    $boardId = $_GET['id'] ?? null;

    if (!$boardId) {
        echo $api->formatResponse(['error' => 'Board ID required'], false, null, 400);
        return;
    }

    // Get existing board
    $stmt = $pdo->prepare("SELECT * FROM digital_boards WHERE id = ?");
    $stmt->execute([$boardId]);
    $existingBoard = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$existingBoard) {
        echo $api->formatResponse(['error' => 'Board not found'], false, null, 404);
        return;
    }

    // Validate brewery access
    $access = $api->validateBreweryAccess($existingBoard['brewery_id'], 'delete');
    if (!$access['success']) {
        echo $api->formatResponse(['error' => $access['error']], false, null, $access['code']);
        return;
    }

    try {
        // Soft delete board
        $stmt = $pdo->prepare("UPDATE digital_boards SET is_active = 0, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$boardId]);

        echo $api->formatResponse([
            'message' => 'Board deleted successfully',
            'board_id' => $boardId
        ], true, 'Board deleted successfully');

        // Log activity
        $api->logActivity('delete', 'board', $boardId, ['name' => $existingBoard['name']]);

    } catch (Exception $e) {
        echo $api->handleDatabaseError($e, 'board deletion');
    }
}
?>
