<?php
require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Get search parameters
$query = sanitizeInput($_GET['q'] ?? '');
$type = sanitizeInput($_GET['type'] ?? 'all'); // all, beers, breweries, users
$limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));

if (empty($query) || strlen($query) < 2) {
    echo json_encode([
        'beers' => [],
        'breweries' => [],
        'users' => [],
        'total' => 0
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $results = [
        'beers' => [],
        'breweries' => [],
        'users' => [],
        'total' => 0
    ];
    
    $searchTerm = "%$query%";
    
    // Search Beers
    if ($type === 'all' || $type === 'beers') {
        $stmt = $conn->prepare("
            SELECT 
                bm.id,
                bm.name,
                bm.thumbnail,
                bm.abv,
                bm.ibu,
                bm.average_rating,
                bm.total_ratings,
                b.name as brewery_name,
                bs.name as style_name,
                'beer' as result_type
            FROM beer_menu bm
            LEFT JOIN breweries b ON bm.brewery_id = b.id
            LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
            WHERE bm.available = 1 
            AND (
                bm.name LIKE ? 
                OR bm.description LIKE ?
                OR b.name LIKE ?
                OR bs.name LIKE ?
            )
            ORDER BY 
                CASE 
                    WHEN bm.name LIKE ? THEN 1
                    WHEN b.name LIKE ? THEN 2
                    WHEN bs.name LIKE ? THEN 3
                    ELSE 4
                END,
                bm.average_rating DESC,
                bm.total_ratings DESC
            LIMIT ?
        ");
        
        $stmt->execute([
            $searchTerm, $searchTerm, $searchTerm, $searchTerm,
            $searchTerm, $searchTerm, $searchTerm,
            $limit
        ]);
        
        $results['beers'] = $stmt->fetchAll();
    }
    
    // Search Breweries
    if ($type === 'all' || $type === 'breweries') {
        $stmt = $conn->prepare("
            SELECT 
                b.id,
                b.name,
                b.city,
                b.state,
                b.description,
                b.logo,
                COUNT(DISTINCT bm.id) as beer_count,
                AVG(br.overall_rating) as avg_rating,
                COUNT(DISTINCT bc.id) as checkin_count,
                'brewery' as result_type
            FROM breweries b
            LEFT JOIN beer_menu bm ON b.id = bm.brewery_id AND bm.available = 1
            LEFT JOIN beer_ratings br ON bm.id = br.beer_id AND br.is_public = 1
            LEFT JOIN beer_checkins bc ON b.id = bc.brewery_id
            WHERE (
                b.name LIKE ? 
                OR b.description LIKE ?
                OR b.city LIKE ?
                OR b.state LIKE ?
            )
            GROUP BY b.id
            ORDER BY 
                CASE 
                    WHEN b.name LIKE ? THEN 1
                    WHEN b.city LIKE ? THEN 2
                    ELSE 3
                END,
                checkin_count DESC,
                beer_count DESC
            LIMIT ?
        ");
        
        $stmt->execute([
            $searchTerm, $searchTerm, $searchTerm, $searchTerm,
            $searchTerm, $searchTerm,
            $limit
        ]);
        
        $results['breweries'] = $stmt->fetchAll();
    }
    
    // Search Users (only if logged in)
    if (isLoggedIn() && ($type === 'all' || $type === 'users')) {
        $stmt = $conn->prepare("
            SELECT 
                p.id,
                p.first_name,
                p.last_name,
                p.username,
                p.bio,
                p.avatar,
                p.role,
                p.total_checkins,
                p.total_reviews,
                p.follower_count,
                p.following_count,
                'user' as result_type
            FROM profiles p
            WHERE p.profile_visibility = 'public'
            AND (
                p.first_name LIKE ? 
                OR p.last_name LIKE ?
                OR p.username LIKE ?
                OR p.bio LIKE ?
            )
            ORDER BY 
                CASE 
                    WHEN p.username LIKE ? THEN 1
                    WHEN CONCAT(p.first_name, ' ', p.last_name) LIKE ? THEN 2
                    ELSE 3
                END,
                p.follower_count DESC,
                p.total_checkins DESC
            LIMIT ?
        ");
        
        $stmt->execute([
            $searchTerm, $searchTerm, $searchTerm, $searchTerm,
            $searchTerm, $searchTerm,
            $limit
        ]);
        
        $results['users'] = $stmt->fetchAll();
    }
    
    // Calculate total results
    $results['total'] = count($results['beers']) + count($results['breweries']) + count($results['users']);
    
    // Format results for better frontend consumption
    foreach ($results['beers'] as &$beer) {
        $beer['url'] = '/beers/detail.php?id=' . $beer['id'];
        $beer['display_name'] = $beer['name'];
        $beer['subtitle'] = $beer['brewery_name'];
        if ($beer['style_name']) {
            $beer['subtitle'] .= ' • ' . $beer['style_name'];
        }
        if ($beer['abv']) {
            $beer['subtitle'] .= ' • ' . number_format($beer['abv'], 1) . '% ABV';
        }
    }
    
    foreach ($results['breweries'] as &$brewery) {
        $brewery['url'] = '/breweries/detail.php?id=' . $brewery['id'];
        $brewery['display_name'] = $brewery['name'];
        $brewery['subtitle'] = '';
        if ($brewery['city']) {
            $brewery['subtitle'] = $brewery['city'];
            if ($brewery['state']) {
                $brewery['subtitle'] .= ', ' . $brewery['state'];
            }
        }
        if ($brewery['beer_count']) {
            $brewery['subtitle'] .= ' • ' . number_format($brewery['beer_count']) . ' beers';
        }
    }
    
    foreach ($results['users'] as &$user) {
        $user['url'] = '/user/profile.php?id=' . $user['id'];
        $user['display_name'] = trim($user['first_name'] . ' ' . $user['last_name']);
        if (empty($user['display_name'])) {
            $user['display_name'] = $user['username'] ?: 'Beer Enthusiast';
        }
        $user['subtitle'] = '';
        if ($user['username']) {
            $user['subtitle'] = '@' . $user['username'];
        }
        if ($user['total_checkins']) {
            $user['subtitle'] .= ' • ' . number_format($user['total_checkins']) . ' check-ins';
        }
        
        // Role labels
        $roleLabels = [
            'beer_enthusiast' => '🍺 Enthusiast',
            'beer_expert' => '🎯 Expert',
            'customer' => '👤 Customer'
        ];
        $user['role_label'] = $roleLabels[$user['role']] ?? ucfirst($user['role']);
    }
    
    echo json_encode($results);
    
} catch (Exception $e) {
    error_log("Global search API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Search failed',
        'beers' => [],
        'breweries' => [],
        'users' => [],
        'total' => 0
    ]);
}
?>
