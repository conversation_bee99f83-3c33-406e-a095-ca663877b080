<?php
/**
 * Smart Location Search API
 * Provides intelligent location suggestions based on user input and geolocation
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Get search parameters
$query = sanitizeInput($_GET['q'] ?? '');
$userLat = floatval($_GET['lat'] ?? 0);
$userLng = floatval($_GET['lng'] ?? 0);
$limit = min(10, max(1, intval($_GET['limit'] ?? 8)));

if (empty($query) || strlen($query) < 2) {
    echo json_encode([
        'success' => true,
        'suggestions' => [],
        'message' => 'Query too short'
    ]);
    exit;
}

try {
    // Initialize database connection
    $db = new Database();
    $pdo = $db->getConnection();

    $suggestions = [];

    // 1. Search local database for cities with breweries (only if database is available)
    if ($pdo) {
        $localSuggestions = searchLocalCities($query, $userLat, $userLng, $limit, $pdo);
        $suggestions = array_merge($suggestions, $localSuggestions);
    }

    // 2. Add popular US cities that match the query
    $popularCities = searchPopularCities($query, $userLat, $userLng);
    $suggestions = array_merge($suggestions, $popularCities);

    // 3. If user has geolocation, add nearby suggestions
    if ($userLat && $userLng) {
        $nearbySuggestions = searchNearbyCities($query, $userLat, $userLng);
        $suggestions = array_merge($suggestions, $nearbySuggestions);
    }

    // Remove duplicates and sort by relevance
    $suggestions = removeDuplicates($suggestions);
    $suggestions = sortByRelevance($suggestions, $query, $userLat, $userLng);

    // Limit results
    $suggestions = array_slice($suggestions, 0, $limit);

    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'user_location' => [
            'lat' => $userLat,
            'lng' => $userLng
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Search error: ' . $e->getMessage()
    ]);
}

/**
 * Search local database for cities with breweries
 */
function searchLocalCities($query, $userLat, $userLng, $limit, $pdo) {
    
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT 
                city, 
                state, 
                COUNT(*) as brewery_count,
                AVG(latitude) as avg_lat,
                AVG(longitude) as avg_lng
            FROM breweries 
            WHERE (city LIKE ? OR state LIKE ?)
            AND latitude IS NOT NULL 
            AND longitude IS NOT NULL
            GROUP BY city, state
            ORDER BY brewery_count DESC, city ASC
            LIMIT ?
        ");
        
        $searchParam = "%$query%";
        $stmt->execute([$searchParam, $searchParam, $limit]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $suggestions = [];
        foreach ($results as $result) {
            $distance = null;
            if ($userLat && $userLng && $result['avg_lat'] && $result['avg_lng']) {
                $distance = calculateDistance(
                    $userLat, $userLng, 
                    $result['avg_lat'], $result['avg_lng']
                );
            }
            
            $suggestions[] = [
                'name' => $result['city'] . ', ' . $result['state'],
                'type' => 'local',
                'city' => $result['city'],
                'state' => $result['state'],
                'lat' => floatval($result['avg_lat']),
                'lng' => floatval($result['avg_lng']),
                'brewery_count' => intval($result['brewery_count']),
                'distance' => $distance,
                'icon' => 'fas fa-beer',
                'priority' => 1
            ];
        }
        
        return $suggestions;
        
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Search popular US cities
 */
function searchPopularCities($query, $userLat, $userLng) {
    $popularCities = [
        ['name' => 'New York, NY', 'lat' => 40.7128, 'lng' => -74.0060],
        ['name' => 'Los Angeles, CA', 'lat' => 34.0522, 'lng' => -118.2437],
        ['name' => 'Chicago, IL', 'lat' => 41.8781, 'lng' => -87.6298],
        ['name' => 'Houston, TX', 'lat' => 29.7604, 'lng' => -95.3698],
        ['name' => 'Phoenix, AZ', 'lat' => 33.4484, 'lng' => -112.0740],
        ['name' => 'Philadelphia, PA', 'lat' => 39.9526, 'lng' => -75.1652],
        ['name' => 'San Antonio, TX', 'lat' => 29.4241, 'lng' => -98.4936],
        ['name' => 'San Diego, CA', 'lat' => 32.7157, 'lng' => -117.1611],
        ['name' => 'Dallas, TX', 'lat' => 32.7767, 'lng' => -96.7970],
        ['name' => 'San Jose, CA', 'lat' => 37.3382, 'lng' => -121.8863],
        ['name' => 'Austin, TX', 'lat' => 30.2672, 'lng' => -97.7431],
        ['name' => 'Jacksonville, FL', 'lat' => 30.3322, 'lng' => -81.6557],
        ['name' => 'Fort Worth, TX', 'lat' => 32.7555, 'lng' => -97.3308],
        ['name' => 'Columbus, OH', 'lat' => 39.9612, 'lng' => -82.9988],
        ['name' => 'Charlotte, NC', 'lat' => 35.2271, 'lng' => -80.8431],
        ['name' => 'San Francisco, CA', 'lat' => 37.7749, 'lng' => -122.4194],
        ['name' => 'Indianapolis, IN', 'lat' => 39.7684, 'lng' => -86.1581],
        ['name' => 'Seattle, WA', 'lat' => 47.6062, 'lng' => -122.3321],
        ['name' => 'Denver, CO', 'lat' => 39.7392, 'lng' => -104.9903],
        ['name' => 'Boston, MA', 'lat' => 42.3601, 'lng' => -71.0589],
        ['name' => 'Nashville, TN', 'lat' => 36.1627, 'lng' => -86.7816],
        ['name' => 'Portland, OR', 'lat' => 45.5152, 'lng' => -122.6784],
        ['name' => 'Las Vegas, NV', 'lat' => 36.1699, 'lng' => -115.1398],
        ['name' => 'Detroit, MI', 'lat' => 42.3314, 'lng' => -83.0458],
        ['name' => 'Memphis, TN', 'lat' => 35.1495, 'lng' => -90.0490],
        ['name' => 'Louisville, KY', 'lat' => 38.2527, 'lng' => -85.7585],
        ['name' => 'Baltimore, MD', 'lat' => 39.2904, 'lng' => -76.6122],
        ['name' => 'Milwaukee, WI', 'lat' => 43.0389, 'lng' => -87.9065],
        ['name' => 'Albuquerque, NM', 'lat' => 35.0844, 'lng' => -106.6504],
        ['name' => 'Tucson, AZ', 'lat' => 32.2226, 'lng' => -110.9747]
    ];
    
    $suggestions = [];
    $queryLower = strtolower($query);
    
    foreach ($popularCities as $city) {
        if (stripos($city['name'], $query) !== false) {
            $distance = null;
            if ($userLat && $userLng) {
                $distance = calculateDistance($userLat, $userLng, $city['lat'], $city['lng']);
            }
            
            $suggestions[] = [
                'name' => $city['name'],
                'type' => 'popular',
                'lat' => $city['lat'],
                'lng' => $city['lng'],
                'distance' => $distance,
                'icon' => 'fas fa-city',
                'priority' => 2
            ];
        }
    }
    
    return $suggestions;
}

/**
 * Search nearby cities based on user location
 */
function searchNearbyCities($query, $userLat, $userLng) {
    // This would typically use a more comprehensive database or API
    // For now, return a simple nearby suggestion
    return [
        [
            'name' => "Near me: $query",
            'type' => 'nearby',
            'lat' => $userLat,
            'lng' => $userLng,
            'distance' => 0,
            'icon' => 'fas fa-location-arrow',
            'priority' => 0
        ]
    ];
}

/**
 * Remove duplicate suggestions
 */
function removeDuplicates($suggestions) {
    $seen = [];
    $unique = [];
    
    foreach ($suggestions as $suggestion) {
        $key = strtolower($suggestion['name']);
        if (!isset($seen[$key])) {
            $seen[$key] = true;
            $unique[] = $suggestion;
        }
    }
    
    return $unique;
}

/**
 * Sort suggestions by relevance
 */
function sortByRelevance($suggestions, $query, $userLat, $userLng) {
    usort($suggestions, function($a, $b) use ($query, $userLat, $userLng) {
        // First sort by priority
        if ($a['priority'] !== $b['priority']) {
            return $a['priority'] - $b['priority'];
        }
        
        // Then by exact match
        $aExact = stripos($a['name'], $query) === 0 ? 1 : 0;
        $bExact = stripos($b['name'], $query) === 0 ? 1 : 0;
        if ($aExact !== $bExact) {
            return $bExact - $aExact;
        }
        
        // Then by distance if available
        if ($userLat && $userLng && isset($a['distance']) && isset($b['distance'])) {
            if ($a['distance'] !== null && $b['distance'] !== null) {
                return $a['distance'] - $b['distance'];
            }
        }
        
        // Finally by name
        return strcmp($a['name'], $b['name']);
    });
    
    return $suggestions;
}

/**
 * Calculate distance between two points in miles
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $earthRadius = 3959; // miles
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earthRadius * $c;
}
?>
