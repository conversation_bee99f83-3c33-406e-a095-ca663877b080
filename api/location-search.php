<?php
/**
 * Smart Location Search API
 * Provides intelligent location suggestions based on user input and geolocation
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Get search parameters
$query = sanitizeInput($_GET['q'] ?? '');
$userLat = floatval($_GET['lat'] ?? 0);
$userLng = floatval($_GET['lng'] ?? 0);
$limit = min(10, max(1, intval($_GET['limit'] ?? 8)));

if (empty($query) || strlen($query) < 2) {
    echo json_encode([
        'success' => true,
        'suggestions' => [],
        'message' => 'Query too short'
    ]);
    exit;
}

try {
    // Initialize database connection
    $db = new Database();
    $pdo = $db->getConnection();

    $suggestions = [];

    // 1. Search local database for cities with breweries (only if database is available)
    if ($pdo) {
        $localSuggestions = searchLocalCities($query, $userLat, $userLng, $limit, $pdo);
        $suggestions = array_merge($suggestions, $localSuggestions);
    }

    // 2. Add popular US cities that match the query
    $popularCities = searchPopularCities($query, $userLat, $userLng);
    $suggestions = array_merge($suggestions, $popularCities);

    // 3. If user has geolocation, add nearby suggestions
    if ($userLat && $userLng) {
        $nearbySuggestions = searchNearbyCities($query, $userLat, $userLng);
        $suggestions = array_merge($suggestions, $nearbySuggestions);
    }

    // Remove duplicates and sort by relevance
    $suggestions = removeDuplicates($suggestions);
    $suggestions = sortByRelevance($suggestions, $query, $userLat, $userLng);

    // Limit results
    $suggestions = array_slice($suggestions, 0, $limit);

    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'user_location' => [
            'lat' => $userLat,
            'lng' => $userLng
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Search error: ' . $e->getMessage()
    ]);
}

/**
 * Search local database for cities with breweries
 */
function searchLocalCities($query, $userLat, $userLng, $limit, $pdo) {
    
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT 
                city, 
                state, 
                COUNT(*) as brewery_count,
                AVG(latitude) as avg_lat,
                AVG(longitude) as avg_lng
            FROM breweries 
            WHERE (city LIKE ? OR state LIKE ?)
            AND latitude IS NOT NULL 
            AND longitude IS NOT NULL
            GROUP BY city, state
            ORDER BY brewery_count DESC, city ASC
            LIMIT ?
        ");
        
        $searchParam = "%$query%";
        $stmt->execute([$searchParam, $searchParam, $limit]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $suggestions = [];
        foreach ($results as $result) {
            $distance = null;
            if ($userLat && $userLng && $result['avg_lat'] && $result['avg_lng']) {
                $distance = calculateDistance(
                    $userLat, $userLng, 
                    $result['avg_lat'], $result['avg_lng']
                );
            }
            
            $suggestions[] = [
                'name' => $result['city'] . ', ' . $result['state'],
                'type' => 'local',
                'city' => $result['city'],
                'state' => $result['state'],
                'lat' => floatval($result['avg_lat']),
                'lng' => floatval($result['avg_lng']),
                'brewery_count' => intval($result['brewery_count']),
                'distance' => $distance,
                'icon' => 'fas fa-beer',
                'priority' => 1
            ];
        }
        
        return $suggestions;
        
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Search comprehensive US cities database
 */
function searchPopularCities($query, $userLat, $userLng) {
    $usCities = [
        // Major cities
        ['name' => 'New York, NY', 'lat' => 40.7128, 'lng' => -74.0060, 'pop' => 8336817],
        ['name' => 'Los Angeles, CA', 'lat' => 34.0522, 'lng' => -118.2437, 'pop' => 3979576],
        ['name' => 'Chicago, IL', 'lat' => 41.8781, 'lng' => -87.6298, 'pop' => 2693976],
        ['name' => 'Houston, TX', 'lat' => 29.7604, 'lng' => -95.3698, 'pop' => 2320268],
        ['name' => 'Phoenix, AZ', 'lat' => 33.4484, 'lng' => -112.0740, 'pop' => 1680992],
        ['name' => 'Philadelphia, PA', 'lat' => 39.9526, 'lng' => -75.1652, 'pop' => 1584064],
        ['name' => 'San Antonio, TX', 'lat' => 29.4241, 'lng' => -98.4936, 'pop' => 1547253],
        ['name' => 'San Diego, CA', 'lat' => 32.7157, 'lng' => -117.1611, 'pop' => 1423851],
        ['name' => 'Dallas, TX', 'lat' => 32.7767, 'lng' => -96.7970, 'pop' => 1343573],
        ['name' => 'San Jose, CA', 'lat' => 37.3382, 'lng' => -121.8863, 'pop' => 1021795],
        ['name' => 'Austin, TX', 'lat' => 30.2672, 'lng' => -97.7431, 'pop' => 978908],
        ['name' => 'Jacksonville, FL', 'lat' => 30.3322, 'lng' => -81.6557, 'pop' => 911507],
        ['name' => 'Fort Worth, TX', 'lat' => 32.7555, 'lng' => -97.3308, 'pop' => 909585],
        ['name' => 'Columbus, OH', 'lat' => 39.9612, 'lng' => -82.9988, 'pop' => 898553],
        ['name' => 'Charlotte, NC', 'lat' => 35.2271, 'lng' => -80.8431, 'pop' => 885708],
        ['name' => 'San Francisco, CA', 'lat' => 37.7749, 'lng' => -122.4194, 'pop' => 881549],
        ['name' => 'Indianapolis, IN', 'lat' => 39.7684, 'lng' => -86.1581, 'pop' => 876384],
        ['name' => 'Seattle, WA', 'lat' => 47.6062, 'lng' => -122.3321, 'pop' => 753675],
        ['name' => 'Denver, CO', 'lat' => 39.7392, 'lng' => -104.9903, 'pop' => 715522],
        ['name' => 'Boston, MA', 'lat' => 42.3601, 'lng' => -71.0589, 'pop' => 685094],

        // Medium cities
        ['name' => 'Nashville, TN', 'lat' => 36.1627, 'lng' => -86.7816, 'pop' => 670820],
        ['name' => 'Portland, OR', 'lat' => 45.5152, 'lng' => -122.6784, 'pop' => 652503],
        ['name' => 'Las Vegas, NV', 'lat' => 36.1699, 'lng' => -115.1398, 'pop' => 641903],
        ['name' => 'Detroit, MI', 'lat' => 42.3314, 'lng' => -83.0458, 'pop' => 639111],
        ['name' => 'Memphis, TN', 'lat' => 35.1495, 'lng' => -90.0490, 'pop' => 633104],
        ['name' => 'Louisville, KY', 'lat' => 38.2527, 'lng' => -85.7585, 'pop' => 617638],
        ['name' => 'Baltimore, MD', 'lat' => 39.2904, 'lng' => -76.6122, 'pop' => 576498],
        ['name' => 'Milwaukee, WI', 'lat' => 43.0389, 'lng' => -87.9065, 'pop' => 577222],
        ['name' => 'Albuquerque, NM', 'lat' => 35.0844, 'lng' => -106.6504, 'pop' => 560513],
        ['name' => 'Tucson, AZ', 'lat' => 32.2226, 'lng' => -110.9747, 'pop' => 548073],

        // Smaller cities and towns
        ['name' => 'Fresno, CA', 'lat' => 36.7378, 'lng' => -119.7871, 'pop' => 542107],
        ['name' => 'Sacramento, CA', 'lat' => 38.5816, 'lng' => -121.4944, 'pop' => 524943],
        ['name' => 'Kansas City, MO', 'lat' => 39.0997, 'lng' => -94.5786, 'pop' => 508090],
        ['name' => 'Mesa, AZ', 'lat' => 33.4152, 'lng' => -111.8315, 'pop' => 504258],
        ['name' => 'Atlanta, GA', 'lat' => 33.7490, 'lng' => -84.3880, 'pop' => 498715],
        ['name' => 'Colorado Springs, CO', 'lat' => 38.8339, 'lng' => -104.8214, 'pop' => 478961],
        ['name' => 'Raleigh, NC', 'lat' => 35.7796, 'lng' => -78.6382, 'pop' => 474069],
        ['name' => 'Omaha, NE', 'lat' => 41.2565, 'lng' => -95.9345, 'pop' => 486051],
        ['name' => 'Miami, FL', 'lat' => 25.7617, 'lng' => -80.1918, 'pop' => 442241],
        ['name' => 'Long Beach, CA', 'lat' => 33.7701, 'lng' => -118.1937, 'pop' => 466742],
        ['name' => 'Virginia Beach, VA', 'lat' => 36.8529, 'lng' => -75.9780, 'pop' => 459470],
        ['name' => 'Oakland, CA', 'lat' => 37.8044, 'lng' => -122.2712, 'pop' => 433031],
        ['name' => 'Minneapolis, MN', 'lat' => 44.9778, 'lng' => -93.2650, 'pop' => 429954],
        ['name' => 'Tulsa, OK', 'lat' => 36.1540, 'lng' => -95.9928, 'pop' => 413066],
        ['name' => 'Tampa, FL', 'lat' => 27.9506, 'lng' => -82.4572, 'pop' => 399700],
        ['name' => 'Arlington, TX', 'lat' => 32.7357, 'lng' => -97.1081, 'pop' => 398854],
        ['name' => 'New Orleans, LA', 'lat' => 29.9511, 'lng' => -90.0715, 'pop' => 383997],
        ['name' => 'Wichita, KS', 'lat' => 37.6872, 'lng' => -97.3301, 'pop' => 389938],
        ['name' => 'Cleveland, OH', 'lat' => 41.4993, 'lng' => -81.6944, 'pop' => 383793],
        ['name' => 'Bakersfield, CA', 'lat' => 35.3733, 'lng' => -119.0187, 'pop' => 380874],

        // Additional smaller cities and towns
        ['name' => 'Aurora, CO', 'lat' => 39.7294, 'lng' => -104.8319, 'pop' => 379289],

        // Small towns and cities (under 100k population)
        ['name' => 'Taylor, MI', 'lat' => 42.2409, 'lng' => -83.2696, 'pop' => 63131],
        ['name' => 'Taylor, TX', 'lat' => 30.5705, 'lng' => -97.4097, 'pop' => 16267],
        ['name' => 'Taylorville, IL', 'lat' => 39.5489, 'lng' => -89.2945, 'pop' => 11427],
        ['name' => 'Dearborn, MI', 'lat' => 42.3223, 'lng' => -83.1763, 'pop' => 109976],
        ['name' => 'Dearborn Heights, MI', 'lat' => 42.3370, 'lng' => -83.2732, 'pop' => 67204],
        ['name' => 'Livonia, MI', 'lat' => 42.3684, 'lng' => -83.3527, 'pop' => 93971],
        ['name' => 'Westland, MI', 'lat' => 42.3242, 'lng' => -83.4002, 'pop' => 84037],
        ['name' => 'Canton, MI', 'lat' => 42.3087, 'lng' => -83.4816, 'pop' => 98659],
        ['name' => 'Plymouth, MI', 'lat' => 42.3714, 'lng' => -83.4702, 'pop' => 9132],
        ['name' => 'Farmington Hills, MI', 'lat' => 42.4989, 'lng' => -83.3677, 'pop' => 83986],
        ['name' => 'Novi, MI', 'lat' => 42.4806, 'lng' => -83.4755, 'pop' => 66243],
        ['name' => 'Troy, MI', 'lat' => 42.6064, 'lng' => -83.1498, 'pop' => 87294],
        ['name' => 'Royal Oak, MI', 'lat' => 42.4895, 'lng' => -83.1446, 'pop' => 59256],
        ['name' => 'Southfield, MI', 'lat' => 42.4734, 'lng' => -83.2219, 'pop' => 73208],
        ['name' => 'Pontiac, MI', 'lat' => 42.6389, 'lng' => -83.2910, 'pop' => 61606],
        ['name' => 'Flint, MI', 'lat' => 43.0125, 'lng' => -83.6875, 'pop' => 95943],
        ['name' => 'Kalamazoo, MI', 'lat' => 42.2917, 'lng' => -85.5872, 'pop' => 75092],
        ['name' => 'Lansing, MI', 'lat' => 42.3314, 'lng' => -84.5467, 'pop' => 118427],
        ['name' => 'Battle Creek, MI', 'lat' => 42.3211, 'lng' => -85.1797, 'pop' => 51084],
        ['name' => 'Jackson, MI', 'lat' => 42.2459, 'lng' => -84.4013, 'pop' => 32715],
        ['name' => 'Saginaw, MI', 'lat' => 43.4194, 'lng' => -83.9508, 'pop' => 46360],
        ['name' => 'Bay City, MI', 'lat' => 43.5944, 'lng' => -83.8888, 'pop' => 32661],
        ['name' => 'Midland, MI', 'lat' => 43.6156, 'lng' => -84.2472, 'pop' => 42547],
        ['name' => 'Muskegon, MI', 'lat' => 43.2342, 'lng' => -86.2484, 'pop' => 37213],
        ['name' => 'Holland, MI', 'lat' => 42.7876, 'lng' => -86.1090, 'pop' => 34378],
        ['name' => 'Portage, MI', 'lat' => 42.2011, 'lng' => -85.5800, 'pop' => 49224],
        ['name' => 'Wyandotte, MI', 'lat' => 42.2142, 'lng' => -83.1499, 'pop' => 25058],
        ['name' => 'Lincoln Park, MI', 'lat' => 42.2506, 'lng' => -83.1785, 'pop' => 36836],
        ['name' => 'Southgate, MI', 'lat' => 42.2139, 'lng' => -83.1946, 'pop' => 29404],
        ['name' => 'Roseville, MI', 'lat' => 42.4973, 'lng' => -82.9371, 'pop' => 47710],
        ['name' => 'St. Clair Shores, MI', 'lat' => 42.4973, 'lng' => -82.8885, 'pop' => 59749],
        ['name' => 'Eastpointe, MI', 'lat' => 42.4684, 'lng' => -82.9557, 'pop' => 32442],
        ['name' => 'Harper Woods, MI', 'lat' => 42.4370, 'lng' => -82.9224, 'pop' => 14236],
        ['name' => 'Grosse Pointe, MI', 'lat' => 42.3831, 'lng' => -82.9115, 'pop' => 5421],
        ['name' => 'Ferndale, MI', 'lat' => 42.4606, 'lng' => -83.1346, 'pop' => 19900],
        ['name' => 'Hazel Park, MI', 'lat' => 42.4625, 'lng' => -83.1041, 'pop' => 16422],
        ['name' => 'Oak Park, MI', 'lat' => 42.4595, 'lng' => -83.1827, 'pop' => 29560],
        ['name' => 'Madison Heights, MI', 'lat' => 42.4859, 'lng' => -83.1052, 'pop' => 31101],
        ['name' => 'Clawson, MI', 'lat' => 42.5334, 'lng' => -83.1457, 'pop' => 11825],
        ['name' => 'Birmingham, MI', 'lat' => 42.5467, 'lng' => -83.2113, 'pop' => 21564],
        ['name' => 'Bloomfield Hills, MI', 'lat' => 42.5834, 'lng' => -83.2455, 'pop' => 3995],
        ['name' => 'Rochester, MI', 'lat' => 42.6803, 'lng' => -83.1338, 'pop' => 13035],
        ['name' => 'Rochester Hills, MI', 'lat' => 42.6583, 'lng' => -83.1499, 'pop' => 76300],
        ['name' => 'Auburn Hills, MI', 'lat' => 42.6875, 'lng' => -83.2341, 'pop' => 23058],
        ['name' => 'Lake Orion, MI', 'lat' => 42.7848, 'lng' => -83.2399, 'pop' => 3240],
        ['name' => 'Clarkston, MI', 'lat' => 42.7370, 'lng' => -83.4196, 'pop' => 882],
        ['name' => 'Waterford, MI', 'lat' => 42.6645, 'lng' => -83.3499, 'pop' => 71707],
        ['name' => 'White Lake, MI', 'lat' => 42.6645, 'lng' => -83.5341, 'pop' => 30019],
        ['name' => 'Commerce, MI', 'lat' => 42.5931, 'lng' => -83.4888, 'pop' => 40186],
        ['name' => 'Wixom, MI', 'lat' => 42.5248, 'lng' => -83.5360, 'pop' => 17193],
        ['name' => 'Milford, MI', 'lat' => 42.5917, 'lng' => -83.6004, 'pop' => 16685],
        ['name' => 'Highland, MI', 'lat' => 42.6370, 'lng' => -83.6138, 'pop' => 19329],
        ['name' => 'Howell, MI', 'lat' => 42.6073, 'lng' => -83.9294, 'pop' => 9489],
        ['name' => 'Brighton, MI', 'lat' => 42.5295, 'lng' => -83.7802, 'pop' => 7444],
        ['name' => 'South Lyon, MI', 'lat' => 42.4606, 'lng' => -83.6516, 'pop' => 11818],
        ['name' => 'Northville, MI', 'lat' => 42.4317, 'lng' => -83.4830, 'pop' => 6119],
        ['name' => 'Garden City, MI', 'lat' => 42.3256, 'lng' => -83.3307, 'pop' => 26565],
        ['name' => 'Redford, MI', 'lat' => 42.3837, 'lng' => -83.2966, 'pop' => 46109],
        ['name' => 'Inkster, MI', 'lat' => 42.2942, 'lng' => -83.3099, 'pop' => 24857],
        ['name' => 'Wayne, MI', 'lat' => 42.2814, 'lng' => -83.3863, 'pop' => 16894],
        ['name' => 'Romulus, MI', 'lat' => 42.2223, 'lng' => -83.3966, 'pop' => 23989],
        ['name' => 'Belleville, MI', 'lat' => 42.2045, 'lng' => -83.4855, 'pop' => 3991],
        ['name' => 'Ypsilanti, MI', 'lat' => 42.2411, 'lng' => -83.6130, 'pop' => 21018],
        ['name' => 'Saline, MI', 'lat' => 42.1667, 'lng' => -83.7816, 'pop' => 9723],
        ['name' => 'Chelsea, MI', 'lat' => 42.3178, 'lng' => -84.0202, 'pop' => 5467],
        ['name' => 'Dexter, MI', 'lat' => 42.3378, 'lng' => -83.8891, 'pop' => 4067],
        ['name' => 'Manchester, MI', 'lat' => 42.1501, 'lng' => -84.0372, 'pop' => 2091],
        ['name' => 'Anaheim, CA', 'lat' => 33.8366, 'lng' => -117.9143, 'pop' => 352497],
        ['name' => 'Honolulu, HI', 'lat' => 21.3099, 'lng' => -157.8581, 'pop' => 347397],
        ['name' => 'Santa Ana, CA', 'lat' => 33.7455, 'lng' => -117.8677, 'pop' => 334136],
        ['name' => 'Riverside, CA', 'lat' => 33.9533, 'lng' => -117.3962, 'pop' => 331360],
        ['name' => 'Corpus Christi, TX', 'lat' => 27.8006, 'lng' => -97.3964, 'pop' => 326586],
        ['name' => 'Lexington, KY', 'lat' => 38.0406, 'lng' => -84.5037, 'pop' => 323152],
        ['name' => 'Stockton, CA', 'lat' => 37.9577, 'lng' => -121.2908, 'pop' => 320804],
        ['name' => 'Henderson, NV', 'lat' => 36.0397, 'lng' => -114.9817, 'pop' => 320189],
        ['name' => 'Saint Paul, MN', 'lat' => 44.9537, 'lng' => -93.0900, 'pop' => 308096],
        ['name' => 'St. Louis, MO', 'lat' => 38.6270, 'lng' => -90.1994, 'pop' => 301578],
        ['name' => 'Cincinnati, OH', 'lat' => 39.1031, 'lng' => -84.5120, 'pop' => 309317],
        ['name' => 'Pittsburgh, PA', 'lat' => 40.4406, 'lng' => -79.9959, 'pop' => 302971],
        ['name' => 'Greensboro, NC', 'lat' => 36.0726, 'lng' => -79.7920, 'pop' => 296710],
        ['name' => 'Lincoln, NE', 'lat' => 40.8136, 'lng' => -96.7026, 'pop' => 295178],
        ['name' => 'Plano, TX', 'lat' => 33.0198, 'lng' => -96.6989, 'pop' => 288061],
        ['name' => 'Anchorage, AK', 'lat' => 61.2181, 'lng' => -149.9003, 'pop' => 291247],
        ['name' => 'Orlando, FL', 'lat' => 28.5383, 'lng' => -81.3792, 'pop' => 287442],
        ['name' => 'Irvine, CA', 'lat' => 33.6846, 'lng' => -117.8265, 'pop' => 287401],
        ['name' => 'Newark, NJ', 'lat' => 40.7357, 'lng' => -74.1724, 'pop' => 282011],
        ['name' => 'Durham, NC', 'lat' => 35.9940, 'lng' => -78.8986, 'pop' => 283506],
        ['name' => 'Chula Vista, CA', 'lat' => 32.6401, 'lng' => -117.0842, 'pop' => 275487],
        ['name' => 'Toledo, OH', 'lat' => 41.6528, 'lng' => -83.5379, 'pop' => 272779],
        ['name' => 'Fort Wayne, IN', 'lat' => 41.0793, 'lng' => -85.1394, 'pop' => 270402],
        ['name' => 'St. Petersburg, FL', 'lat' => 27.7676, 'lng' => -82.6403, 'pop' => 265351],
        ['name' => 'Laredo, TX', 'lat' => 27.5306, 'lng' => -99.4803, 'pop' => 261639],
        ['name' => 'Jersey City, NJ', 'lat' => 40.7178, 'lng' => -74.0431, 'pop' => 262075],
        ['name' => 'Chandler, AZ', 'lat' => 33.3062, 'lng' => -111.8413, 'pop' => 261165],
        ['name' => 'Madison, WI', 'lat' => 43.0731, 'lng' => -89.4012, 'pop' => 259680],
        ['name' => 'Lubbock, TX', 'lat' => 33.5779, 'lng' => -101.8552, 'pop' => 258862],
        ['name' => 'Norfolk, VA', 'lat' => 36.8468, 'lng' => -76.2852, 'pop' => 238005],
        ['name' => 'Buffalo, NY', 'lat' => 42.8864, 'lng' => -78.8784, 'pop' => 255284],
        ['name' => 'Winston-Salem, NC', 'lat' => 36.0999, 'lng' => -80.2442, 'pop' => 247945],
        ['name' => 'Glendale, AZ', 'lat' => 33.5387, 'lng' => -112.1860, 'pop' => 248325],
        ['name' => 'Reno, NV', 'lat' => 39.5296, 'lng' => -119.8138, 'pop' => 250998],
        ['name' => 'Scottsdale, AZ', 'lat' => 33.4942, 'lng' => -111.9261, 'pop' => 258069],
        ['name' => 'North Las Vegas, NV', 'lat' => 36.1989, 'lng' => -115.1175, 'pop' => 251974],
        ['name' => 'Irving, TX', 'lat' => 32.8140, 'lng' => -96.9489, 'pop' => 249610],
        ['name' => 'Fremont, CA', 'lat' => 37.5485, 'lng' => -121.9886, 'pop' => 230504],
        ['name' => 'Gilbert, AZ', 'lat' => 33.3528, 'lng' => -111.7890, 'pop' => 254114],
        ['name' => 'San Bernardino, CA', 'lat' => 34.1083, 'lng' => -117.2898, 'pop' => 222101],
        ['name' => 'Baton Rouge, LA', 'lat' => 30.4515, 'lng' => -91.1871, 'pop' => 220236],
        ['name' => 'Hialeah, FL', 'lat' => 25.8576, 'lng' => -80.2781, 'pop' => 223109],
        ['name' => 'Garland, TX', 'lat' => 32.9126, 'lng' => -96.6389, 'pop' => 246018],
        ['name' => 'Rochester, NY', 'lat' => 43.1566, 'lng' => -77.6088, 'pop' => 206284],
        ['name' => 'Chesapeake, VA', 'lat' => 36.7682, 'lng' => -76.2875, 'pop' => 249422],
        ['name' => 'Spokane, WA', 'lat' => 47.6587, 'lng' => -117.4260, 'pop' => 219190],
        ['name' => 'Des Moines, IA', 'lat' => 41.5868, 'lng' => -93.6250, 'pop' => 214133],
        ['name' => 'Modesto, CA', 'lat' => 37.6391, 'lng' => -120.9969, 'pop' => 218464],
        ['name' => 'Fayetteville, NC', 'lat' => 35.0527, 'lng' => -78.8784, 'pop' => 211657],
        ['name' => 'Tacoma, WA', 'lat' => 47.2529, 'lng' => -122.4443, 'pop' => 219346],
        ['name' => 'Oxnard, CA', 'lat' => 34.1975, 'lng' => -119.1771, 'pop' => 207906],
        ['name' => 'Fontana, CA', 'lat' => 34.0922, 'lng' => -117.4350, 'pop' => 213739],
        ['name' => 'Columbus, GA', 'lat' => 32.4609, 'lng' => -84.9877, 'pop' => 206922],
        ['name' => 'Montgomery, AL', 'lat' => 32.3668, 'lng' => -86.3000, 'pop' => 200603],
        ['name' => 'Moreno Valley, CA', 'lat' => 33.9425, 'lng' => -117.2297, 'pop' => 208634],
        ['name' => 'Shreveport, LA', 'lat' => 32.5252, 'lng' => -93.7502, 'pop' => 187593],
        ['name' => 'Aurora, IL', 'lat' => 41.7606, 'lng' => -88.3201, 'pop' => 200661],
        ['name' => 'Yonkers, NY', 'lat' => 40.9312, 'lng' => -73.8988, 'pop' => 200370],
        ['name' => 'Akron, OH', 'lat' => 41.0814, 'lng' => -81.5190, 'pop' => 197597],
        ['name' => 'Huntington Beach, CA', 'lat' => 33.6603, 'lng' => -117.9992, 'pop' => 198711],
        ['name' => 'Little Rock, AR', 'lat' => 34.7465, 'lng' => -92.2896, 'pop' => 197312],
        ['name' => 'Augusta, GA', 'lat' => 33.4735, 'lng' => -82.0105, 'pop' => 197350],
        ['name' => 'Amarillo, TX', 'lat' => 35.2220, 'lng' => -101.8313, 'pop' => 200393],
        ['name' => 'Glendale, CA', 'lat' => 34.1425, 'lng' => -118.2551, 'pop' => 199303],
        ['name' => 'Mobile, AL', 'lat' => 30.6954, 'lng' => -88.0399, 'pop' => 187041],
        ['name' => 'Grand Rapids, MI', 'lat' => 42.9634, 'lng' => -85.6681, 'pop' => 198917],
        ['name' => 'Salt Lake City, UT', 'lat' => 40.7608, 'lng' => -111.8910, 'pop' => 200567],
        ['name' => 'Tallahassee, FL', 'lat' => 30.4518, 'lng' => -84.27277, 'pop' => 194500],
        ['name' => 'Huntsville, AL', 'lat' => 34.7304, 'lng' => -86.5861, 'pop' => 215006],
        ['name' => 'Grand Prairie, TX', 'lat' => 32.7460, 'lng' => -96.9978, 'pop' => 196100],
        ['name' => 'Knoxville, TN', 'lat' => 35.9606, 'lng' => -83.9207, 'pop' => 190740],
        ['name' => 'Worcester, MA', 'lat' => 42.2626, 'lng' => -71.8023, 'pop' => 185877],
        ['name' => 'Newport News, VA', 'lat' => 37.0871, 'lng' => -76.4730, 'pop' => 179225],
        ['name' => 'Brownsville, TX', 'lat' => 25.9018, 'lng' => -97.4975, 'pop' => 186738],
        ['name' => 'Santa Clarita, CA', 'lat' => 34.3917, 'lng' => -118.5426, 'pop' => 228673],
        ['name' => 'Providence, RI', 'lat' => 41.8240, 'lng' => -71.4128, 'pop' => 190934],
        ['name' => 'Overland Park, KS', 'lat' => 38.9822, 'lng' => -94.6708, 'pop' => 195494],
        ['name' => 'Garden Grove, CA', 'lat' => 33.7739, 'lng' => -117.9415, 'pop' => 171949],
        ['name' => 'Chattanooga, TN', 'lat' => 35.0456, 'lng' => -85.3097, 'pop' => 181099],
        ['name' => 'Oceanside, CA', 'lat' => 33.1959, 'lng' => -117.3795, 'pop' => 174648],
        ['name' => 'Jackson, MS', 'lat' => 32.2988, 'lng' => -90.1848, 'pop' => 153701],
        ['name' => 'Fort Lauderdale, FL', 'lat' => 26.1224, 'lng' => -80.1373, 'pop' => 182760],
        ['name' => 'Santa Rosa, CA', 'lat' => 38.4404, 'lng' => -122.7144, 'pop' => 178127],
        ['name' => 'Rancho Cucamonga, CA', 'lat' => 34.1064, 'lng' => -117.5931, 'pop' => 177751],
        ['name' => 'Port St. Lucie, FL', 'lat' => 27.2730, 'lng' => -80.3582, 'pop' => 204851],
        ['name' => 'Tempe, AZ', 'lat' => 33.4255, 'lng' => -111.9400, 'pop' => 195805],
        ['name' => 'Ontario, CA', 'lat' => 34.0633, 'lng' => -117.6509, 'pop' => 175265],
        ['name' => 'Vancouver, WA', 'lat' => 45.6387, 'lng' => -122.6615, 'pop' => 183741],
        ['name' => 'Cape Coral, FL', 'lat' => 26.5629, 'lng' => -81.9495, 'pop' => 194016],
        ['name' => 'Sioux Falls, SD', 'lat' => 43.5446, 'lng' => -96.7311, 'pop' => 192517],
        ['name' => 'Springfield, MO', 'lat' => 37.2153, 'lng' => -93.2982, 'pop' => 169176],
        ['name' => 'Peoria, AZ', 'lat' => 33.5806, 'lng' => -112.2374, 'pop' => 190985],
        ['name' => 'Pembroke Pines, FL', 'lat' => 26.0070, 'lng' => -80.2962, 'pop' => 171178],
        ['name' => 'Elk Grove, CA', 'lat' => 38.4088, 'lng' => -121.3716, 'pop' => 176124],
        ['name' => 'Salem, OR', 'lat' => 44.9429, 'lng' => -123.0351, 'pop' => 177723],
        ['name' => 'Lancaster, CA', 'lat' => 34.6868, 'lng' => -118.1542, 'pop' => 173516],
        ['name' => 'Corona, CA', 'lat' => 33.8753, 'lng' => -117.5664, 'pop' => 169868],
        ['name' => 'Eugene, OR', 'lat' => 44.0521, 'lng' => -123.0868, 'pop' => 176654],
        ['name' => 'Palmdale, CA', 'lat' => 34.5794, 'lng' => -118.1165, 'pop' => 169450],
        ['name' => 'Salinas, CA', 'lat' => 36.6777, 'lng' => -121.6555, 'pop' => 157380],
        ['name' => 'Springfield, MA', 'lat' => 42.1015, 'lng' => -72.5898, 'pop' => 155929],
        ['name' => 'Pasadena, CA', 'lat' => 34.1478, 'lng' => -118.1445, 'pop' => 141029],
        ['name' => 'Fort Collins, CO', 'lat' => 40.5853, 'lng' => -105.0844, 'pop' => 169810],
        ['name' => 'Hayward, CA', 'lat' => 37.6688, 'lng' => -122.0808, 'pop' => 162954],
        ['name' => 'Pomona, CA', 'lat' => 34.0552, 'lng' => -117.7500, 'pop' => 151713],
        ['name' => 'Cary, NC', 'lat' => 35.7915, 'lng' => -78.7811, 'pop' => 174721],
        ['name' => 'Rockford, IL', 'lat' => 42.2711, 'lng' => -89.0940, 'pop' => 145609],
        ['name' => 'Alexandria, VA', 'lat' => 38.8048, 'lng' => -77.0469, 'pop' => 159467],
        ['name' => 'Escondido, CA', 'lat' => 33.1192, 'lng' => -117.0864, 'pop' => 151613],
        ['name' => 'McKinney, TX', 'lat' => 33.1972, 'lng' => -96.6397, 'pop' => 199177],
        ['name' => 'Kansas City, KS', 'lat' => 39.1142, 'lng' => -94.6275, 'pop' => 156607],
        ['name' => 'Joliet, IL', 'lat' => 41.5250, 'lng' => -88.0817, 'pop' => 150362],
        ['name' => 'Sunnyvale, CA', 'lat' => 37.3688, 'lng' => -122.0363, 'pop' => 155805],
        ['name' => 'Torrance, CA', 'lat' => 33.8358, 'lng' => -118.3406, 'pop' => 147067],
        ['name' => 'Bridgeport, CT', 'lat' => 41.1865, 'lng' => -73.1952, 'pop' => 148654],
        ['name' => 'Lakewood, CO', 'lat' => 39.7047, 'lng' => -105.0814, 'pop' => 155984],
        ['name' => 'Hollywood, FL', 'lat' => 26.0112, 'lng' => -80.1495, 'pop' => 154817],
        ['name' => 'Paterson, NJ', 'lat' => 40.9168, 'lng' => -74.1718, 'pop' => 145233],
        ['name' => 'Naperville, IL', 'lat' => 41.7508, 'lng' => -88.1535, 'pop' => 148449],
        ['name' => 'Syracuse, NY', 'lat' => 43.0481, 'lng' => -76.1474, 'pop' => 142749],
        ['name' => 'Mesquite, TX', 'lat' => 32.7668, 'lng' => -96.5991, 'pop' => 150108],
        ['name' => 'Dayton, OH', 'lat' => 39.7589, 'lng' => -84.1916, 'pop' => 140407],
        ['name' => 'Savannah, GA', 'lat' => 32.0835, 'lng' => -81.0998, 'pop' => 147780],
        ['name' => 'Clarksville, TN', 'lat' => 36.5298, 'lng' => -87.3595, 'pop' => 166722],
        ['name' => 'Orange, CA', 'lat' => 33.7879, 'lng' => -117.8531, 'pop' => 139911],
        ['name' => 'Pasadena, TX', 'lat' => 29.6911, 'lng' => -95.2091, 'pop' => 151950],
        ['name' => 'Fullerton, CA', 'lat' => 33.8704, 'lng' => -117.9242, 'pop' => 143617],
        ['name' => 'Killeen, TX', 'lat' => 31.1171, 'lng' => -97.7278, 'pop' => 153095],
        ['name' => 'Frisco, TX', 'lat' => 33.1507, 'lng' => -96.8236, 'pop' => 200509],
        ['name' => 'Hampton, VA', 'lat' => 37.0299, 'lng' => -76.3452, 'pop' => 135410],
        ['name' => 'McAllen, TX', 'lat' => 26.2034, 'lng' => -98.2300, 'pop' => 143268],
        ['name' => 'Warren, MI', 'lat' => 42.5144, 'lng' => -83.0146, 'pop' => 139387],
        ['name' => 'Bellevue, WA', 'lat' => 47.6101, 'lng' => -122.2015, 'pop' => 151854],
        ['name' => 'West Valley City, UT', 'lat' => 40.6916, 'lng' => -112.0011, 'pop' => 140230],
        ['name' => 'Columbia, MO', 'lat' => 38.9517, 'lng' => -92.3341, 'pop' => 126254],
        ['name' => 'Olathe, KS', 'lat' => 38.8814, 'lng' => -94.8191, 'pop' => 140545],
        ['name' => 'Sterling Heights, MI', 'lat' => 42.5803, 'lng' => -83.0302, 'pop' => 134346],
        ['name' => 'New Haven, CT', 'lat' => 41.3083, 'lng' => -72.9279, 'pop' => 130250],
        ['name' => 'Miramar, FL', 'lat' => 25.9873, 'lng' => -80.2322, 'pop' => 141191],
        ['name' => 'Waco, TX', 'lat' => 31.5494, 'lng' => -97.1467, 'pop' => 138486],
        ['name' => 'Thousand Oaks, CA', 'lat' => 34.1706, 'lng' => -118.8376, 'pop' => 126966],
        ['name' => 'Cedar Rapids, IA', 'lat' => 41.9778, 'lng' => -91.6656, 'pop' => 133562],
        ['name' => 'Charleston, SC', 'lat' => 32.7765, 'lng' => -79.9311, 'pop' => 150227],
        ['name' => 'Visalia, CA', 'lat' => 36.3302, 'lng' => -119.2921, 'pop' => 141384],
        ['name' => 'Topeka, KS', 'lat' => 39.0473, 'lng' => -95.6890, 'pop' => 125310],
        ['name' => 'Elizabeth, NJ', 'lat' => 40.6640, 'lng' => -74.2107, 'pop' => 137298],
        ['name' => 'Gainesville, FL', 'lat' => 29.6516, 'lng' => -82.3248, 'pop' => 141085],
        ['name' => 'Thornton, CO', 'lat' => 39.8681, 'lng' => -104.9719, 'pop' => 141867],
        ['name' => 'Roseville, CA', 'lat' => 38.7521, 'lng' => -121.2880, 'pop' => 147773],
        ['name' => 'Carrollton, TX', 'lat' => 32.9537, 'lng' => -96.8903, 'pop' => 133434],
        ['name' => 'Coral Springs, FL', 'lat' => 26.2710, 'lng' => -80.2706, 'pop' => 134394],
        ['name' => 'Stamford, CT', 'lat' => 41.0534, 'lng' => -73.5387, 'pop' => 135470],
        ['name' => 'Simi Valley, CA', 'lat' => 34.2694, 'lng' => -118.7815, 'pop' => 124243],
        ['name' => 'Concord, CA', 'lat' => 37.9780, 'lng' => -122.0311, 'pop' => 129295],
        ['name' => 'Hartford, CT', 'lat' => 41.7658, 'lng' => -72.6734, 'pop' => 121054],
        ['name' => 'Kent, WA', 'lat' => 47.3809, 'lng' => -122.2348, 'pop' => 136588],
        ['name' => 'Lafayette, LA', 'lat' => 30.2241, 'lng' => -92.0198, 'pop' => 121374],
        ['name' => 'Midland, TX', 'lat' => 31.9973, 'lng' => -102.0779, 'pop' => 146038],
        ['name' => 'Surprise, AZ', 'lat' => 33.6292, 'lng' => -112.3679, 'pop' => 141664],
        ['name' => 'Denton, TX', 'lat' => 33.2148, 'lng' => -97.1331, 'pop' => 139734],
        ['name' => 'Victorville, CA', 'lat' => 34.5362, 'lng' => -117.2911, 'pop' => 122958],
        ['name' => 'Evansville, IN', 'lat' => 37.9716, 'lng' => -87.5710, 'pop' => 117298],
        ['name' => 'Santa Clara, CA', 'lat' => 37.3541, 'lng' => -121.9552, 'pop' => 127134],
        ['name' => 'Abilene, TX', 'lat' => 32.4487, 'lng' => -99.7331, 'pop' => 125182],
        ['name' => 'Athens, GA', 'lat' => 33.9519, 'lng' => -83.3576, 'pop' => 127315],
        ['name' => 'Vallejo, CA', 'lat' => 38.1041, 'lng' => -122.2566, 'pop' => 122105],
        ['name' => 'Allentown, PA', 'lat' => 40.6084, 'lng' => -75.4902, 'pop' => 125845],
        ['name' => 'Norman, OK', 'lat' => 35.2226, 'lng' => -97.4395, 'pop' => 128026],
        ['name' => 'Beaumont, TX', 'lat' => 30.0803, 'lng' => -94.1266, 'pop' => 115282],
        ['name' => 'Independence, MO', 'lat' => 39.0911, 'lng' => -94.4155, 'pop' => 123011],
        ['name' => 'Murfreesboro, TN', 'lat' => 35.8456, 'lng' => -86.3903, 'pop' => 152769],
        ['name' => 'Ann Arbor, MI', 'lat' => 42.2808, 'lng' => -83.7430, 'pop' => 123851],
        ['name' => 'Springfield, IL', 'lat' => 39.7817, 'lng' => -89.6501, 'pop' => 114230],
        ['name' => 'Berkeley, CA', 'lat' => 37.8715, 'lng' => -122.2730, 'pop' => 124321],
        ['name' => 'Peoria, IL', 'lat' => 40.6936, 'lng' => -89.5890, 'pop' => 112936],
        ['name' => 'Provo, UT', 'lat' => 40.2338, 'lng' => -111.6585, 'pop' => 117335],
        ['name' => 'El Monte, CA', 'lat' => 34.0686, 'lng' => -118.0276, 'pop' => 115965],
        ['name' => 'Columbia, SC', 'lat' => 34.0007, 'lng' => -81.0348, 'pop' => 137300],
        ['name' => 'Lansing, MI', 'lat' => 42.3314, 'lng' => -84.5467, 'pop' => 118427],
        ['name' => 'Fargo, ND', 'lat' => 46.8772, 'lng' => -96.7898, 'pop' => 125990],
        ['name' => 'Downey, CA', 'lat' => 33.9401, 'lng' => -118.1326, 'pop' => 111263],
        ['name' => 'Costa Mesa, CA', 'lat' => 33.6411, 'lng' => -117.9187, 'pop' => 112174],
        ['name' => 'Wilmington, NC', 'lat' => 34.2257, 'lng' => -77.9447, 'pop' => 123744],
        ['name' => 'Arvada, CO', 'lat' => 39.8028, 'lng' => -105.0875, 'pop' => 121510],
        ['name' => 'Inglewood, CA', 'lat' => 33.9617, 'lng' => -118.3531, 'pop' => 109398],
        ['name' => 'Miami Gardens, FL', 'lat' => 25.9420, 'lng' => -80.2456, 'pop' => 113750],
        ['name' => 'Carlsbad, CA', 'lat' => 33.1581, 'lng' => -117.3506, 'pop' => 115382],
        ['name' => 'Westminster, CO', 'lat' => 39.8367, 'lng' => -105.0372, 'pop' => 114832],
        ['name' => 'Rochester, MN', 'lat' => 44.0121, 'lng' => -92.4802, 'pop' => 121395],
        ['name' => 'Odessa, TX', 'lat' => 31.8457, 'lng' => -102.3676, 'pop' => 123334],
        ['name' => 'Manchester, NH', 'lat' => 42.9956, 'lng' => -71.4548, 'pop' => 115644],
        ['name' => 'Elgin, IL', 'lat' => 42.0354, 'lng' => -88.2826, 'pop' => 114797],
        ['name' => 'West Jordan, UT', 'lat' => 40.6097, 'lng' => -111.9391, 'pop' => 116961],
        ['name' => 'Round Rock, TX', 'lat' => 30.5083, 'lng' => -97.6789, 'pop' => 133372],
        ['name' => 'Clearwater, FL', 'lat' => 27.9659, 'lng' => -82.8001, 'pop' => 117292],
        ['name' => 'Waterbury, CT', 'lat' => 41.5581, 'lng' => -73.0515, 'pop' => 108143],
        ['name' => 'Gresham, OR', 'lat' => 45.5001, 'lng' => -122.4302, 'pop' => 114247],
        ['name' => 'Fairfield, CA', 'lat' => 38.2494, 'lng' => -122.0399, 'pop' => 119881],
        ['name' => 'Billings, MT', 'lat' => 45.7833, 'lng' => -108.5007, 'pop' => 117116],
        ['name' => 'Lowell, MA', 'lat' => 42.6334, 'lng' => -71.3162, 'pop' => 115554],
        ['name' => 'San Buenaventura, CA', 'lat' => 34.2746, 'lng' => -119.2290, 'pop' => 110763],
        ['name' => 'Pueblo, CO', 'lat' => 38.2544, 'lng' => -104.6091, 'pop' => 111876],
        ['name' => 'High Point, NC', 'lat' => 35.9557, 'lng' => -80.0053, 'pop' => 114059],
        ['name' => 'West Covina, CA', 'lat' => 34.0686, 'lng' => -117.9390, 'pop' => 106098],
        ['name' => 'Richmond, VA', 'lat' => 37.5407, 'lng' => -77.4360, 'pop' => 230436],
        ['name' => 'Murrieta, CA', 'lat' => 33.5539, 'lng' => -117.2139, 'pop' => 114066],
        ['name' => 'Cambridge, MA', 'lat' => 42.3736, 'lng' => -71.1097, 'pop' => 118403],
        ['name' => 'Antioch, CA', 'lat' => 38.0049, 'lng' => -121.8058, 'pop' => 115291],
        ['name' => 'Temecula, CA', 'lat' => 33.4936, 'lng' => -117.1484, 'pop' => 114761],
        ['name' => 'Norwalk, CA', 'lat' => 33.9022, 'lng' => -118.0817, 'pop' => 105549],
        ['name' => 'Centennial, CO', 'lat' => 39.5807, 'lng' => -104.8756, 'pop' => 108418],
        ['name' => 'Richardson, TX', 'lat' => 32.9483, 'lng' => -96.7299, 'pop' => 119469],
        ['name' => 'Lansing, MI', 'lat' => 42.3314, 'lng' => -84.5467, 'pop' => 118427],
        ['name' => 'College Station, TX', 'lat' => 30.6280, 'lng' => -96.3344, 'pop' => 120511],
        ['name' => 'Fairfield, CA', 'lat' => 38.2494, 'lng' => -122.0399, 'pop' => 119881],
        ['name' => 'Pearland, TX', 'lat' => 29.5638, 'lng' => -95.2861, 'pop' => 125410]
    ];
    
    $suggestions = [];
    $queryLower = strtolower($query);

    foreach ($usCities as $city) {
        $cityName = $city['name'];
        $cityNameLower = strtolower($cityName);

        // Check if query matches city name or state
        $matches = false;
        $exactMatch = false;
        $startsWithMatch = false;

        // Extract city and state parts
        $parts = explode(', ', $cityName);
        $cityPart = isset($parts[0]) ? strtolower($parts[0]) : '';
        $statePart = isset($parts[1]) ? strtolower($parts[1]) : '';

        // Check for exact matches (highest priority)
        if ($cityPart === $queryLower || $statePart === $queryLower) {
            $matches = true;
            $exactMatch = true;
        }
        // Check for starts with matches (high priority)
        else if (strpos($cityPart, $queryLower) === 0 || strpos($statePart, $queryLower) === 0) {
            $matches = true;
            $startsWithMatch = true;
        }
        // Check for contains matches (lower priority)
        else if (stripos($cityNameLower, $queryLower) !== false) {
            $matches = true;
        }

        if ($matches) {
            $distance = null;
            if ($userLat && $userLng) {
                $distance = calculateDistance($userLat, $userLng, $city['lat'], $city['lng']);
            }

            // Determine priority based on match type and population
            $priority = 3; // Default priority
            if ($exactMatch) {
                $priority = 1; // Highest priority for exact matches
            } else if ($startsWithMatch) {
                $priority = 2; // High priority for starts-with matches
            }

            // Adjust priority based on population (smaller cities get slightly higher priority for local feel)
            $populationBonus = 0;
            if (isset($city['pop'])) {
                if ($city['pop'] < 100000) {
                    $populationBonus = -0.1; // Slight boost for smaller cities
                } else if ($city['pop'] > 1000000) {
                    $populationBonus = 0.2; // Slight penalty for very large cities
                }
            }

            $suggestions[] = [
                'name' => $city['name'],
                'type' => 'city',
                'lat' => $city['lat'],
                'lng' => $city['lng'],
                'distance' => $distance,
                'population' => $city['pop'] ?? null,
                'icon' => $city['pop'] > 500000 ? 'fas fa-city' : 'fas fa-home',
                'priority' => $priority + $populationBonus,
                'exactMatch' => $exactMatch,
                'startsWithMatch' => $startsWithMatch
            ];
        }
    }

    // Sort by priority, then by population (smaller cities first within same priority)
    usort($suggestions, function($a, $b) {
        if ($a['priority'] !== $b['priority']) {
            return $a['priority'] - $b['priority'];
        }

        // Within same priority, prefer smaller cities for local feel
        $aPop = $a['population'] ?? 0;
        $bPop = $b['population'] ?? 0;

        if ($aPop !== $bPop) {
            return $aPop - $bPop; // Smaller population first
        }

        return strcmp($a['name'], $b['name']);
    });

    return $suggestions;
}

/**
 * Search nearby cities based on user location
 */
function searchNearbyCities($query, $userLat, $userLng) {
    // This would typically use a more comprehensive database or API
    // For now, return a simple nearby suggestion
    return [
        [
            'name' => "Near me: $query",
            'type' => 'nearby',
            'lat' => $userLat,
            'lng' => $userLng,
            'distance' => 0,
            'icon' => 'fas fa-location-arrow',
            'priority' => 0
        ]
    ];
}

/**
 * Remove duplicate suggestions
 */
function removeDuplicates($suggestions) {
    $seen = [];
    $unique = [];
    
    foreach ($suggestions as $suggestion) {
        $key = strtolower($suggestion['name']);
        if (!isset($seen[$key])) {
            $seen[$key] = true;
            $unique[] = $suggestion;
        }
    }
    
    return $unique;
}

/**
 * Sort suggestions by relevance
 */
function sortByRelevance($suggestions, $query, $userLat, $userLng) {
    usort($suggestions, function($a, $b) use ($query, $userLat, $userLng) {
        // First sort by priority
        if ($a['priority'] !== $b['priority']) {
            return $a['priority'] - $b['priority'];
        }
        
        // Then by exact match
        $aExact = stripos($a['name'], $query) === 0 ? 1 : 0;
        $bExact = stripos($b['name'], $query) === 0 ? 1 : 0;
        if ($aExact !== $bExact) {
            return $bExact - $aExact;
        }
        
        // Then by distance if available
        if ($userLat && $userLng && isset($a['distance']) && isset($b['distance'])) {
            if ($a['distance'] !== null && $b['distance'] !== null) {
                return $a['distance'] - $b['distance'];
            }
        }
        
        // Finally by name
        return strcmp($a['name'], $b['name']);
    });
    
    return $suggestions;
}

/**
 * Calculate distance between two points in miles
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $earthRadius = 3959; // miles
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earthRadius * $c;
}
?>
