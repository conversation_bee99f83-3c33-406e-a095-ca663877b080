<?php
/**
 * Analytics API
 * Phase 7 - Advanced Features
 * 
 * API endpoints for analytics data and interaction tracking
 */

require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/DigitalBoardAnalytics.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize services
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $analytics = new DigitalBoardAnalytics($pdo);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Get current user (optional for some endpoints)
$user = getCurrentUser();
$method = $_SERVER['REQUEST_METHOD'];

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetAnalytics($pdo, $analytics, $user);
            break;
        case 'POST':
            handleTrackInteraction($pdo, $analytics, $user);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Analytics API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

/**
 * GET /api/analytics
 * Get analytics data
 */
function handleGetAnalytics($pdo, $analytics, $user) {
    $action = $_GET['action'] ?? 'board_analytics';
    $boardId = $_GET['board_id'] ?? null;
    $timeframe = $_GET['timeframe'] ?? '30_days';
    $export = $_GET['export'] ?? null;
    
    // Check permissions for authenticated endpoints
    if ($user && !canAccessBoard($pdo, $user, $boardId)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        return;
    }
    
    switch ($action) {
        case 'board_analytics':
            if (!$boardId) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Board ID required']);
                return;
            }
            
            $data = $analytics->getBoardAnalytics($boardId, $timeframe);
            
            if ($export) {
                handleExport($data, $export, "board_{$boardId}_analytics");
            } else {
                echo json_encode([
                    'success' => true,
                    'analytics' => $data,
                    'board_id' => $boardId,
                    'timeframe' => $timeframe,
                    'timestamp' => date('c')
                ]);
            }
            break;
            
        case 'brewery_analytics':
            if (!$user) {
                http_response_code(401);
                echo json_encode(['success' => false, 'error' => 'Authentication required']);
                return;
            }
            
            $breweryId = $user['brewery_id'];
            $data = $analytics->getBreweryAnalytics($breweryId, $timeframe);
            
            if ($export) {
                handleExport($data, $export, "brewery_{$breweryId}_analytics");
            } else {
                echo json_encode([
                    'success' => true,
                    'analytics' => $data,
                    'brewery_id' => $breweryId,
                    'timeframe' => $timeframe,
                    'timestamp' => date('c')
                ]);
            }
            break;
            
        case 'real_time_stats':
            $stats = getRealTimeStats($pdo, $boardId);
            echo json_encode([
                'success' => true,
                'stats' => $stats,
                'timestamp' => date('c')
            ]);
            break;
            
        case 'interaction_heatmap':
            if (!$boardId) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Board ID required']);
                return;
            }
            
            $heatmapData = getInteractionHeatmap($pdo, $boardId, $timeframe);
            echo json_encode([
                'success' => true,
                'heatmap_data' => $heatmapData,
                'board_id' => $boardId,
                'timeframe' => $timeframe
            ]);
            break;
            
        case 'performance_metrics':
            $metrics = getPerformanceMetrics($pdo, $boardId, $timeframe);
            echo json_encode([
                'success' => true,
                'metrics' => $metrics,
                'timestamp' => date('c')
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
}

/**
 * POST /api/analytics
 * Track interactions and events
 */
function handleTrackInteraction($pdo, $analytics, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid input data']);
        return;
    }
    
    $action = $input['action'] ?? 'track_interaction';
    
    switch ($action) {
        case 'track_interaction':
            $result = trackSingleInteraction($pdo, $analytics, $input);
            echo json_encode($result);
            break;
            
        case 'track_batch':
            $result = trackBatchInteractions($pdo, $analytics, $input);
            echo json_encode($result);
            break;
            
        case 'track_view':
            $result = trackBoardView($pdo, $analytics, $input);
            echo json_encode($result);
            break;
            
        case 'track_session':
            $result = trackUserSession($pdo, $input);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
}

/**
 * Track single interaction
 */
function trackSingleInteraction($pdo, $analytics, $data) {
    try {
        $required = ['board_id', 'session_id', 'interaction_type'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                return ['success' => false, 'error' => "Missing required field: {$field}"];
            }
        }
        
        $interactionData = [
            'type' => $data['interaction_type'],
            'element_id' => $data['element_id'] ?? null,
            'x' => $data['x_coordinate'] ?? null,
            'y' => $data['y_coordinate'] ?? null,
            'duration' => $data['interaction_duration'] ?? 0,
            'metadata' => $data['data'] ?? []
        ];
        
        $interactionId = $analytics->trackInteraction(
            $data['board_id'],
            $data['session_id'],
            $interactionData
        );
        
        if ($interactionId) {
            return [
                'success' => true,
                'interaction_id' => $interactionId,
                'timestamp' => date('c')
            ];
        } else {
            return ['success' => false, 'error' => 'Failed to track interaction'];
        }
        
    } catch (Exception $e) {
        error_log("Track interaction error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Internal error'];
    }
}

/**
 * Track batch interactions
 */
function trackBatchInteractions($pdo, $analytics, $data) {
    try {
        if (!isset($data['interactions']) || !is_array($data['interactions'])) {
            return ['success' => false, 'error' => 'Invalid interactions data'];
        }
        
        $successCount = 0;
        $errors = [];
        
        foreach ($data['interactions'] as $interaction) {
            $result = trackSingleInteraction($pdo, $analytics, $interaction);
            if ($result['success']) {
                $successCount++;
            } else {
                $errors[] = $result['error'];
            }
        }
        
        return [
            'success' => true,
            'processed' => count($data['interactions']),
            'successful' => $successCount,
            'errors' => $errors,
            'timestamp' => date('c')
        ];
        
    } catch (Exception $e) {
        error_log("Track batch interactions error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Internal error'];
    }
}

/**
 * Track board view
 */
function trackBoardView($pdo, $analytics, $data) {
    try {
        $required = ['board_id', 'session_id'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                return ['success' => false, 'error' => "Missing required field: {$field}"];
            }
        }
        
        $deviceInfo = [
            'device_type' => $data['device_type'] ?? 'unknown',
            'browser' => $data['browser'] ?? 'unknown',
            'screen_resolution' => $data['screen_resolution'] ?? 'unknown'
        ];
        
        $viewId = $analytics->trackBoardView(
            $data['board_id'],
            $data['session_id'],
            $deviceInfo
        );
        
        if ($viewId) {
            return [
                'success' => true,
                'view_id' => $viewId,
                'timestamp' => date('c')
            ];
        } else {
            return ['success' => false, 'error' => 'Failed to track view'];
        }
        
    } catch (Exception $e) {
        error_log("Track view error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Internal error'];
    }
}

/**
 * Track user session
 */
function trackUserSession($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_sessions (
                session_id, board_id, ip_address, user_agent, 
                device_info, started_at, last_activity
            ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            last_activity = NOW(),
            device_info = VALUES(device_info)
        ");
        
        $stmt->execute([
            $data['session_id'],
            $data['board_id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            json_encode($data['device_info'] ?? [])
        ]);
        
        return [
            'success' => true,
            'session_id' => $data['session_id'],
            'timestamp' => date('c')
        ];
        
    } catch (Exception $e) {
        error_log("Track session error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Internal error'];
    }
}

/**
 * Get real-time statistics
 */
function getRealTimeStats($pdo, $boardId = null) {
    try {
        $whereClause = $boardId ? "WHERE board_id = ?" : "";
        $params = $boardId ? [$boardId] : [];
        
        // Active sessions in last 5 minutes
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT session_id) as active_sessions
            FROM board_views 
            {$whereClause} 
            AND viewed_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ");
        $stmt->execute($params);
        $activeSessions = $stmt->fetchColumn();
        
        // Interactions in last hour
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as recent_interactions
            FROM board_interactions 
            {$whereClause} 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute($params);
        $recentInteractions = $stmt->fetchColumn();
        
        // Views today
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as views_today
            FROM board_views 
            {$whereClause} 
            AND DATE(viewed_at) = CURDATE()
        ");
        $stmt->execute($params);
        $viewsToday = $stmt->fetchColumn();
        
        return [
            'active_sessions' => (int)$activeSessions,
            'recent_interactions' => (int)$recentInteractions,
            'views_today' => (int)$viewsToday,
            'last_updated' => date('c')
        ];
        
    } catch (Exception $e) {
        error_log("Real-time stats error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get interaction heatmap data
 */
function getInteractionHeatmap($pdo, $boardId, $timeframe) {
    try {
        $interval = getIntervalFromTimeframe($timeframe);
        
        $stmt = $pdo->prepare("
            SELECT 
                x_coordinate,
                y_coordinate,
                COUNT(*) as interaction_count,
                interaction_type
            FROM board_interactions 
            WHERE board_id = ? 
            AND created_at >= DATE_SUB(NOW(), INTERVAL {$interval})
            AND x_coordinate IS NOT NULL 
            AND y_coordinate IS NOT NULL
            GROUP BY x_coordinate, y_coordinate, interaction_type
            ORDER BY interaction_count DESC
        ");
        $stmt->execute([$boardId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Heatmap data error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get performance metrics
 */
function getPerformanceMetrics($pdo, $boardId, $timeframe) {
    try {
        $interval = getIntervalFromTimeframe($timeframe);
        $whereClause = $boardId ? "WHERE board_id = ?" : "";
        $params = $boardId ? [$boardId] : [];
        
        // Page load times
        $stmt = $pdo->prepare("
            SELECT 
                AVG(load_time) as avg_load_time,
                MIN(load_time) as min_load_time,
                MAX(load_time) as max_load_time
            FROM page_performance 
            {$whereClause} 
            AND created_at >= DATE_SUB(NOW(), INTERVAL {$interval})
        ");
        $stmt->execute($params);
        $loadTimes = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Error rates
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(CASE WHEN error_type IS NOT NULL THEN 1 END) as error_count,
                COUNT(*) as total_requests,
                (COUNT(CASE WHEN error_type IS NOT NULL THEN 1 END) / COUNT(*)) * 100 as error_rate
            FROM api_requests 
            {$whereClause} 
            AND created_at >= DATE_SUB(NOW(), INTERVAL {$interval})
        ");
        $stmt->execute($params);
        $errorStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'load_times' => $loadTimes,
            'error_stats' => $errorStats,
            'timestamp' => date('c')
        ];
        
    } catch (Exception $e) {
        error_log("Performance metrics error: " . $e->getMessage());
        return [];
    }
}

/**
 * Handle data export
 */
function handleExport($data, $format, $filename) {
    switch ($format) {
        case 'csv':
            exportToCSV($data, $filename);
            break;
        case 'pdf':
            exportToPDF($data, $filename);
            break;
        case 'json':
            exportToJSON($data, $filename);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid export format']);
    }
}

/**
 * Export to CSV
 */
function exportToCSV($data, $filename) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // Flatten data for CSV export
    $flatData = flattenArrayForCSV($data);
    
    if (!empty($flatData)) {
        fputcsv($output, array_keys($flatData[0]));
        foreach ($flatData as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
}

/**
 * Export to JSON
 */
function exportToJSON($data, $filename) {
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    
    echo json_encode($data, JSON_PRETTY_PRINT);
}

/**
 * Helper functions
 */
function canAccessBoard($pdo, $user, $boardId) {
    if (!$boardId || !$user) return false;
    
    if ($user['role'] === 'admin') return true;
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM digital_boards 
        WHERE id = ? AND brewery_id = ?
    ");
    $stmt->execute([$boardId, $user['brewery_id']]);
    
    return $stmt->fetchColumn() > 0;
}

function getIntervalFromTimeframe($timeframe) {
    $intervals = [
        '7_days' => '7 DAY',
        '30_days' => '30 DAY',
        '90_days' => '90 DAY',
        '1_year' => '1 YEAR'
    ];
    
    return $intervals[$timeframe] ?? '30 DAY';
}

function flattenArrayForCSV($data, $prefix = '') {
    $result = [];
    
    foreach ($data as $key => $value) {
        $newKey = $prefix ? $prefix . '_' . $key : $key;
        
        if (is_array($value)) {
            $result = array_merge($result, flattenArrayForCSV($value, $newKey));
        } else {
            $result[$newKey] = $value;
        }
    }
    
    return [$result];
}
?>
