<?php
/**
 * Notification Preferences API
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';
require_once '../includes/auth_functions.php';
require_once '../includes/NotificationService.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$user = getCurrentUser();
$userId = $user['id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    $notificationService = new NotificationService($conn);
    
    switch ($method) {
        case 'GET':
            handleGetPreferences($notificationService, $userId);
            break;
            
        case 'POST':
        case 'PUT':
            handleUpdatePreferences($conn, $userId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Notification Preferences API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Handle GET requests - fetch notification preferences
 */
function handleGetPreferences($notificationService, $userId) {
    $preferences = $notificationService->getUserNotificationPreferences($userId);
    
    echo json_encode([
        'success' => true,
        'preferences' => $preferences
    ]);
}

/**
 * Handle POST/PUT requests - update notification preferences
 */
function handleUpdatePreferences($conn, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Define allowed preference fields
    $allowedFields = [
        'email_new_follower',
        'email_friend_checkin',
        'email_beer_release',
        'email_brewery_event',
        'email_achievement_unlocked',
        'email_message_received',
        'email_rating_liked',
        'email_comment_received',
        'push_new_follower',
        'push_friend_checkin',
        'push_beer_release',
        'push_brewery_event',
        'push_achievement_unlocked',
        'push_message_received',
        'push_rating_liked',
        'push_comment_received',
        'digest_frequency',
        'quiet_hours_start',
        'quiet_hours_end'
    ];
    
    // Build update query
    $updateFields = [];
    $updateValues = [];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $value = $input[$field];
            
            // Validate boolean fields
            if (strpos($field, 'email_') === 0 || strpos($field, 'push_') === 0) {
                $value = (bool)$value;
            }
            
            // Validate digest frequency
            if ($field === 'digest_frequency') {
                $value = sanitizeInput($value);
                if (!in_array($value, ['none', 'daily', 'weekly'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Invalid digest frequency']);
                    return;
                }
            }
            
            // Validate time fields
            if ($field === 'quiet_hours_start' || $field === 'quiet_hours_end') {
                $value = sanitizeInput($value);
                if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $value)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Invalid time format']);
                    return;
                }
            }
            
            $updateFields[] = "$field = ?";
            $updateValues[] = $value;
        }
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
        return;
    }
    
    // Add user ID for WHERE clause
    $updateValues[] = $userId;
    
    try {
        // Check if preferences exist
        $stmt = $conn->prepare("SELECT id FROM notification_preferences WHERE user_id = ?");
        $stmt->execute([$userId]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            // Update existing preferences
            $sql = "UPDATE notification_preferences SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE user_id = ?";
            $stmt = $conn->prepare($sql);
            $success = $stmt->execute($updateValues);
        } else {
            // Create new preferences with defaults
            $notificationService = new NotificationService($conn);
            $notificationService->createDefaultNotificationPreferences($userId);
            
            // Now update with provided values
            $sql = "UPDATE notification_preferences SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE user_id = ?";
            $stmt = $conn->prepare($sql);
            $success = $stmt->execute($updateValues);
        }
        
        if ($success) {
            // Return updated preferences
            $stmt = $conn->prepare("SELECT * FROM notification_preferences WHERE user_id = ?");
            $stmt->execute([$userId]);
            $preferences = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'message' => 'Notification preferences updated successfully',
                'preferences' => $preferences
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update preferences']);
        }
        
    } catch (Exception $e) {
        error_log("Update notification preferences error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}
?>
