<?php
/**
 * Menu Management API
 * Handle CRUD operations for beer and food menus for places (breweries, restaurants, pubs, party stores, production facilities)
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is admin
if (!isLoggedIn() || getCurrentUser()['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            handleCreate();
            break;
        case 'PUT':
            handleUpdate();
            break;
        case 'DELETE':
            handleDelete();
            break;
        case 'GET':
            handleRead();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleCreate() {
    global $pdo;
    
    $type = $_POST['type'] ?? 'beer'; // Default to beer if not specified
    
    if ($type === 'food') {
        createFoodItem();
    } else {
        createBeerItem();
    }
}

function createBeerItem() {
    global $pdo;

    $place_id = $_POST['place_id'] ?? $_POST['brewery_id'] ?? null; // Support both for backward compatibility
    $name = $_POST['name'] ?? null;
    $beer_style_id = $_POST['beer_style_id'] ?? null;
    $description = $_POST['description'] ?? null;
    $abv = $_POST['abv'] ?? null;
    $ibu = $_POST['ibu'] ?? null;
    $price = $_POST['price'] ?? null;

    if (!$place_id || !$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID and name are required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO place_beers (
                id, place_id, beer_style_id, name, description, abv, ibu, price,
                availability, is_active, created_at
            ) VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 'year_round', 1, NOW())
        ");

        $stmt->execute([
            $place_id, $beer_style_id, $name, $description,
            $abv, $ibu, $price
        ]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Beer added successfully',
            'id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function createFoodItem() {
    global $pdo;

    $place_id = $_POST['place_id'] ?? $_POST['brewery_id'] ?? null; // Support both for backward compatibility
    $name = $_POST['name'] ?? null;
    $category = $_POST['category'] ?? null;
    $description = $_POST['description'] ?? null;
    $price = $_POST['price'] ?? null;

    if (!$place_id || !$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID and name are required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO place_food (
                id, place_id, food_category_id, name, description, price, is_active, created_at
            ) VALUES (UUID(), ?, ?, ?, ?, ?, 1, NOW())
        ");

        $stmt->execute([$place_id, $category, $name, $description, $price]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Food item added successfully',
            'id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $type = $input['type'] ?? 'beer';
    
    if ($type === 'food') {
        updateFoodItem($input);
    } else {
        updateBeerItem($input);
    }
}

function updateBeerItem($data) {
    global $pdo;
    
    $id = $data['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowedFields = ['name', 'beer_style_id', 'description', 'abv', 'ibu', 'price', 'available', 'featured'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE place_beers SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Beer updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function updateFoodItem($data) {
    global $pdo;
    
    $id = $data['id'] ?? null;
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowedFields = ['name', 'category', 'description', 'price', 'available'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE place_food SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Food item updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $id = $input['id'] ?? null;
    $type = $input['type'] ?? 'beer';
    
    if (!$id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    try {
        if ($type === 'food') {
            $stmt = $pdo->prepare("DELETE FROM place_food WHERE id = ?");
            $itemType = 'Food item';
        } else {
            $stmt = $pdo->prepare("DELETE FROM place_beers WHERE id = ?");
            $itemType = 'Beer';
        }
        
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => $itemType . ' deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => $itemType . ' not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleRead() {
    global $pdo;

    $place_id = $_GET['place_id'] ?? $_GET['brewery_id'] ?? null; // Support both for backward compatibility
    $type = $_GET['type'] ?? 'both';

    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    try {
        $result = [];
        
        if ($type === 'beer' || $type === 'both') {
            $stmt = $pdo->prepare("
                SELECT pb.*, bs.name as style_name
                FROM place_beers pb
                LEFT JOIN beer_styles bs ON pb.beer_style_id = bs.id
                WHERE pb.place_id = ? AND pb.is_active = 1
                ORDER BY pb.name
            ");
            $stmt->execute([$place_id]);
            $result['beers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        if ($type === 'food' || $type === 'both') {
            $stmt = $pdo->prepare("
                SELECT pf.*, fc.name as category_name
                FROM place_food pf
                LEFT JOIN food_categories fc ON pf.food_category_id = fc.id
                WHERE pf.place_id = ? AND pf.is_active = 1
                ORDER BY fc.sort_order, pf.name
            ");
            $stmt->execute([$place_id]);
            $result['food'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        echo json_encode(['success' => true, 'data' => $result]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
