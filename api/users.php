<?php
/**
 * Users API
 * Phase 5 - User Management & Authentication
 * 
 * Handles user management operations with enhanced permissions
 */

require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/UserManager.php';
require_once '../includes/PermissionManager.php';

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Initialize services
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $user = getCurrentUser();
    $userManager = new UserManager($pdo);
    $permissionManager = new PermissionManager($pdo);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit;
}

// Check authentication
$method = $_SERVER['REQUEST_METHOD'];
if (!$user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit;
}

// Check permissions
$canManageUsers = $permissionManager->hasPermission($user['id'], 'manage_brewery_users') || $user['role'] === 'admin';
if (!$canManageUsers && !in_array($method, ['GET'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Insufficient permissions']);
    exit;
}

// Route the request
try {
    switch ($method) {
        case 'GET':
            handleGetUsers($pdo, $userManager, $permissionManager, $user);
            break;
        case 'POST':
            handleCreateUser($pdo, $userManager, $permissionManager, $user);
            break;
        case 'PUT':
            handleUpdateUser($pdo, $userManager, $permissionManager, $user);
            break;
        case 'DELETE':
            handleDeleteUser($pdo, $userManager, $permissionManager, $user);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Users API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

/**
 * GET /api/users
 * Get users with filtering and pagination
 */
function handleGetUsers($pdo, $userManager, $permissionManager, $currentUser) {
    $userId = $_GET['id'] ?? null;
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    
    // Build filters
    $filters = [];
    
    if (!empty($_GET['role'])) {
        $filters['role'] = $_GET['role'];
    }
    
    if (!empty($_GET['status'])) {
        $filters['status'] = $_GET['status'];
    }
    
    if (!empty($_GET['search'])) {
        $filters['search'] = $_GET['search'];
    }
    
    // Brewery filtering based on user permissions
    if (!empty($_GET['brewery_id'])) {
        $breweryId = $_GET['brewery_id'];
        
        // Check if user can access this brewery
        if ($currentUser['role'] !== 'admin' && !$permissionManager->canAccessBrewery($currentUser['id'], $breweryId)) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Access denied to this brewery']);
            return;
        }
        
        $filters['brewery_id'] = $breweryId;
    } elseif ($currentUser['role'] !== 'admin') {
        // Non-admin users can only see users from their accessible breweries
        $userBreweries = $permissionManager->getUserBreweries($currentUser['id']);
        if (!empty($userBreweries)) {
            $filters['brewery_id'] = $userBreweries[0]['id']; // Default to first accessible brewery
        }
    }
    
    if ($userId) {
        // Get single user
        $userData = $userManager->getUserById($userId);
        
        if (!$userData) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'User not found']);
            return;
        }
        
        // Check access permissions
        if ($currentUser['role'] !== 'admin' && $userData['brewery_id'] && 
            !$permissionManager->canAccessBrewery($currentUser['id'], $userData['brewery_id'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Access denied to this user']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $userData,
            'timestamp' => date('c')
        ]);
    } else {
        // Get paginated list
        $result = $userManager->getUsers($filters, $page, $limit);
        
        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'data' => $result['data'],
                'pagination' => $result['pagination'],
                'timestamp' => date('c')
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $result['error']
            ]);
        }
    }
}

/**
 * POST /api/users
 * Create a new user
 */
function handleCreateUser($pdo, $userManager, $permissionManager, $currentUser) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // If form data, use $_POST instead
    if (empty($input) && !empty($_POST)) {
        $input = $_POST;
    }
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid input data']);
        return;
    }
    
    // Validate brewery access if specified
    if (!empty($input['brewery_id'])) {
        if ($currentUser['role'] !== 'admin' && !$permissionManager->canAccessBrewery($currentUser['id'], $input['brewery_id'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Access denied to this brewery']);
            return;
        }
    }
    
    // Validate role permissions
    if (!empty($input['role'])) {
        $allowedRoles = ['user', 'digital_board_operator', 'business_manager'];
        
        if ($currentUser['role'] === 'admin') {
            $allowedRoles = array_keys(UserManager::ROLES);
        } elseif ($currentUser['role'] === 'business_owner') {
            $allowedRoles[] = 'business_owner';
        }
        
        if (!in_array($input['role'], $allowedRoles)) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Insufficient permissions to create user with this role']);
            return;
        }
    }
    
    $result = $userManager->createUser($input);
    
    if ($result['success']) {
        http_response_code(201);
        echo json_encode([
            'success' => true,
            'data' => ['user_id' => $result['user_id']],
            'message' => $result['message'],
            'timestamp' => date('c')
        ]);
        
        // Log activity
        logUserActivity($pdo, $currentUser['id'], 'user_created', [
            'created_user_id' => $result['user_id'],
            'role' => $input['role'] ?? 'user'
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'errors' => $result['errors'] ?? []
        ]);
    }
}

/**
 * PUT /api/users
 * Update an existing user
 */
function handleUpdateUser($pdo, $userManager, $permissionManager, $currentUser) {
    $userId = $_GET['id'] ?? null;
    $input = json_decode(file_get_contents('php://input'), true);
    
    // If form data, use $_POST instead
    if (empty($input) && !empty($_POST)) {
        $input = $_POST;
        $userId = $input['user_id'] ?? $userId;
    }
    
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'User ID required']);
        return;
    }
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid input data']);
        return;
    }
    
    // Get existing user data
    $existingUser = $userManager->getUserById($userId);
    if (!$existingUser) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'User not found']);
        return;
    }
    
    // Check access permissions
    if ($currentUser['role'] !== 'admin' && $existingUser['brewery_id'] && 
        !$permissionManager->canAccessBrewery($currentUser['id'], $existingUser['brewery_id'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied to this user']);
        return;
    }
    
    // Validate role change permissions
    if (!empty($input['role']) && $input['role'] !== $existingUser['role']) {
        $allowedRoles = ['user', 'digital_board_operator', 'business_manager'];
        
        if ($currentUser['role'] === 'admin') {
            $allowedRoles = array_keys(UserManager::ROLES);
        } elseif ($currentUser['role'] === 'business_owner') {
            $allowedRoles[] = 'business_owner';
        }
        
        if (!in_array($input['role'], $allowedRoles)) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Insufficient permissions to change user role']);
            return;
        }
    }
    
    $result = $userManager->updateUser($userId, $input);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'timestamp' => date('c')
        ]);
        
        // Log activity
        logUserActivity($pdo, $currentUser['id'], 'user_updated', [
            'updated_user_id' => $userId,
            'changes' => array_keys($input)
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $result['error'],
            'errors' => $result['errors'] ?? []
        ]);
    }
}

/**
 * DELETE /api/users
 * Soft delete a user (set status to inactive)
 */
function handleDeleteUser($pdo, $userManager, $permissionManager, $currentUser) {
    $userId = $_GET['id'] ?? null;
    
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'User ID required']);
        return;
    }
    
    // Get existing user data
    $existingUser = $userManager->getUserById($userId);
    if (!$existingUser) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'User not found']);
        return;
    }
    
    // Check access permissions
    if ($currentUser['role'] !== 'admin' && $existingUser['brewery_id'] && 
        !$permissionManager->canAccessBrewery($currentUser['id'], $existingUser['brewery_id'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied to this user']);
        return;
    }
    
    // Prevent self-deletion
    if ($userId === $currentUser['id']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Cannot delete your own account']);
        return;
    }
    
    // Soft delete by setting status to inactive
    $result = $userManager->updateUser($userId, ['status' => 'inactive']);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'User deactivated successfully',
            'timestamp' => date('c')
        ]);
        
        // Log activity
        logUserActivity($pdo, $currentUser['id'], 'user_deleted', [
            'deleted_user_id' => $userId,
            'user_email' => $existingUser['email']
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $result['error']
        ]);
    }
}

/**
 * Log user activity
 */
function logUserActivity($pdo, $userId, $action, $details = []) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_activity_log (user_id, action, details, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $userId,
            $action,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("Error logging user activity: " . $e->getMessage());
    }
}
?>
