<?php
/**
 * Notifications API
 * Phase 7: Notifications & Communication
 */

require_once '../config/config.php';
require_once '../includes/auth_functions.php';
require_once '../includes/NotificationService.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$user = getCurrentUser();
$userId = $user['id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    $notificationService = new NotificationService($conn);
    
    switch ($method) {
        case 'GET':
            handleGetNotifications($notificationService, $userId);
            break;
            
        case 'POST':
            handlePostNotifications($notificationService, $userId);
            break;
            
        case 'PUT':
            handlePutNotifications($notificationService, $userId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

/**
 * Handle GET requests - fetch notifications
 */
function handleGetNotifications($notificationService, $userId) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            $limit = min((int)($_GET['limit'] ?? 20), 50);
            $offset = max((int)($_GET['offset'] ?? 0), 0);
            
            $notifications = $notificationService->getUserNotifications($userId, $limit, $offset);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications
            ]);
            break;
            
        case 'unread':
            $notifications = $notificationService->getUnreadNotifications($userId);
            $count = $notificationService->getUnreadCount($userId);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'unread_count' => $count
            ]);
            break;
            
        case 'count':
            $count = $notificationService->getUnreadCount($userId);
            
            echo json_encode([
                'success' => true,
                'unread_count' => $count
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * Handle POST requests - create notifications (admin only)
 */
function handlePostNotifications($notificationService, $userId) {
    // Only allow admins to create notifications manually
    $user = getCurrentUser();
    if ($user['role'] !== 'admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Admin access required']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $targetUserId = sanitizeInput($input['user_id'] ?? '');
    $type = sanitizeInput($input['type'] ?? '');
    $title = sanitizeInput($input['title'] ?? '');
    $message = sanitizeInput($input['message'] ?? '');
    $data = $input['data'] ?? null;
    $relatedType = sanitizeInput($input['related_type'] ?? '');
    $relatedId = sanitizeInput($input['related_id'] ?? '');
    
    if (empty($targetUserId) || empty($type) || empty($title) || empty($message)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        return;
    }
    
    $notificationId = $notificationService->createNotification(
        $targetUserId,
        $type,
        $title,
        $message,
        $data,
        $relatedType ?: null,
        $relatedId ?: null
    );
    
    if ($notificationId) {
        echo json_encode([
            'success' => true,
            'message' => 'Notification created successfully',
            'notification_id' => $notificationId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to create notification']);
    }
}

/**
 * Handle PUT requests - update notifications
 */
function handlePutNotifications($notificationService, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = sanitizeInput($input['action'] ?? '');
    
    switch ($action) {
        case 'mark_read':
            $notificationId = sanitizeInput($input['notification_id'] ?? '');
            
            if (empty($notificationId)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Notification ID required']);
                return;
            }
            
            $success = $notificationService->markAsRead($notificationId, $userId);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to mark notification as read']);
            }
            break;
            
        case 'mark_all_read':
            $success = $notificationService->markAllAsRead($userId);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'All notifications marked as read'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to mark all notifications as read']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * Trigger notification helpers (for testing and integration)
 */

/**
 * Create new follower notification
 */
function createNewFollowerNotification($followerId, $followedUserId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $notificationService = new NotificationService($conn);
        
        // Get follower info
        $stmt = $conn->prepare("
            SELECT first_name, last_name FROM profiles WHERE id = ?
        ");
        $stmt->execute([$followerId]);
        $follower = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($follower) {
            $followerName = $follower['first_name'] . ' ' . $follower['last_name'];
            
            $notificationService->createNotification(
                $followedUserId,
                'new_follower',
                'New Follower',
                "{$followerName} started following you!",
                ['follower_id' => $followerId, 'follower_name' => $followerName],
                'user',
                $followerId
            );
        }
        
    } catch (Exception $e) {
        error_log("Create new follower notification error: " . $e->getMessage());
    }
}

/**
 * Create friend check-in notification
 */
function createFriendCheckinNotification($checkinUserId, $beerName, $breweryName) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $notificationService = new NotificationService($conn);
        
        // Get user info
        $stmt = $conn->prepare("
            SELECT first_name, last_name FROM profiles WHERE id = ?
        ");
        $stmt->execute([$checkinUserId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) return;
        
        $userName = $user['first_name'] . ' ' . $user['last_name'];
        
        // Get followers
        $stmt = $conn->prepare("
            SELECT follower_id FROM user_follows WHERE followed_id = ?
        ");
        $stmt->execute([$checkinUserId]);
        $followers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($followers as $follower) {
            $notificationService->createNotification(
                $follower['follower_id'],
                'friend_checkin',
                'Friend Check-in',
                "{$userName} checked in to {$beerName} at {$breweryName}",
                [
                    'user_id' => $checkinUserId,
                    'user_name' => $userName,
                    'beer_name' => $beerName,
                    'brewery_name' => $breweryName
                ],
                'user',
                $checkinUserId
            );
        }
        
    } catch (Exception $e) {
        error_log("Create friend checkin notification error: " . $e->getMessage());
    }
}

/**
 * Create achievement unlocked notification
 */
function createAchievementNotification($userId, $badgeName, $badgeDescription) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $notificationService = new NotificationService($conn);
        
        $notificationService->createNotification(
            $userId,
            'achievement_unlocked',
            'Achievement Unlocked! 🏆',
            "Congratulations! You've earned the '{$badgeName}' badge. {$badgeDescription}",
            [
                'badge_name' => $badgeName,
                'badge_description' => $badgeDescription
            ],
            'badge',
            null
        );
        
    } catch (Exception $e) {
        error_log("Create achievement notification error: " . $e->getMessage());
    }
}
?>
