<?php
/**
 * Save Place API
 * Handles saving places to user's saved places list
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Get current user
$currentUser = getCurrentUser();
if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['place_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing place_id']);
    exit;
}

$placeId = intval($input['place_id']);

try {
    $conn->beginTransaction();
    
    // Check if place exists
    $stmt = $conn->prepare("SELECT id, name FROM places WHERE id = ?");
    $stmt->execute([$placeId]);
    $place = $stmt->fetch();
    
    if (!$place) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Place not found']);
        exit;
    }
    
    // Check if user has already saved this place
    $stmt = $conn->prepare("SELECT id FROM saved_places WHERE user_id = ? AND place_id = ?");
    $stmt->execute([$currentUser['id'], $placeId]);
    $existingSave = $stmt->fetch();
    
    if ($existingSave) {
        echo json_encode(['success' => false, 'message' => 'Place already saved']);
        exit;
    }
    
    // Save the place
    $stmt = $conn->prepare("
        INSERT INTO saved_places (user_id, place_id, saved_at) 
        VALUES (?, ?, NOW())
    ");
    $stmt->execute([$currentUser['id'], $placeId]);
    
    // Log activity
    $stmt = $conn->prepare("
        INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata, created_at) 
        VALUES (?, 'place_save', 'place', ?, ?, NOW())
    ");
    $stmt->execute([
        $currentUser['id'], 
        $placeId, 
        json_encode([
            'place_name' => $place['name']
        ])
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Place saved successfully'
    ]);
    
} catch (Exception $e) {
    $conn->rollback();
    error_log("Save place error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
