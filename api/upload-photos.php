<?php
require_once '../config/config.php';
require_once '../includes/PhotoManager.php';

// Set JSON header
header('Content-Type: application/json');

// Require login
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Login required']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$user = getCurrentUser();
$userId = $user['id'];

// Get form data
$type = sanitizeInput($_POST['type'] ?? '');
$targetId = sanitizeInput($_POST['target_id'] ?? '');
$title = sanitizeInput($_POST['title'] ?? '');
$description = sanitizeInput($_POST['description'] ?? '');

// Validate input
$allowedTypes = ['checkin', 'beer', 'brewery', 'user', 'review', 'general'];
if (!in_array($type, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid photo type']);
    exit;
}

// Check if files were uploaded
if (!isset($_FILES['photos']) || empty($_FILES['photos']['name'][0])) {
    echo json_encode(['success' => false, 'message' => 'No photos uploaded']);
    exit;
}

try {
    $photoManager = new PhotoManager();
    
    // Upload photos
    $results = $photoManager->uploadMultiplePhotos($_FILES['photos'], $type, $targetId, $userId);
    
    $successCount = 0;
    $errors = [];
    $uploadedPhotos = [];
    
    foreach ($results as $result) {
        if ($result['success']) {
            $successCount++;
            
            // Update photo metadata if provided
            if (!empty($title) || !empty($description)) {
                updatePhotoMetadata($result['photo_id'], $title, $description);
            }
            
            $uploadedPhotos[] = [
                'id' => $result['photo_id'],
                'filename' => $result['filename'],
                'url' => $result['url'],
                'thumbnail_url' => $result['thumbnail_url'],
                'width' => $result['width'],
                'height' => $result['height']
            ];
        } else {
            $errors[] = $result['error'] . ' (' . $result['filename'] . ')';
        }
    }
    
    // Log activity for successful uploads
    if ($successCount > 0) {
        logPhotoActivity($userId, $type, $targetId, $successCount);
    }
    
    // Return results
    if ($successCount > 0) {
        echo json_encode([
            'success' => true,
            'message' => "Successfully uploaded {$successCount} photo(s)",
            'uploaded_count' => $successCount,
            'total_count' => count($results),
            'photos' => $uploadedPhotos,
            'errors' => $errors
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No photos were uploaded successfully',
            'errors' => $errors
        ]);
    }
    
} catch (Exception $e) {
    error_log("Photo upload API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Upload failed']);
}

/**
 * Update photo metadata
 */
function updatePhotoMetadata($photoId, $title, $description) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            UPDATE photos 
            SET title = ?, description = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$title, $description, $photoId]);
        
    } catch (Exception $e) {
        error_log("Photo metadata update error: " . $e->getMessage());
    }
}

/**
 * Log photo upload activity
 */
function logPhotoActivity($userId, $type, $targetId, $count) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $activityType = 'photo_upload';
        $metadata = json_encode([
            'photo_type' => $type,
            'photo_count' => $count,
            'target_id' => $targetId
        ]);
        
        $stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $activityType, $type, $targetId, $metadata]);
        
    } catch (Exception $e) {
        error_log("Photo activity logging error: " . $e->getMessage());
    }
}
?>
