<?php
/**
 * Places Management API
 * Handle place CRUD operations and management
 */

require_once '../config/config.php';
requireLogin();
requireRole('admin');

header('Content-Type: application/json');

// Initialize database connection
try {
    $db = new Database();
    $pdo = $db->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get':
            handleGet();
            break;
        case 'create':
            handleCreate();
            break;
        case 'update':
            handleUpdate();
            break;
        case 'delete':
            handleDelete();
            break;
        case 'verify':
        case 'unverify':
            handleToggleVerification();
            break;
        case 'claim':
        case 'unclaim':
            handleToggleClaim();
            break;
        case 'bulk_verify':
        case 'bulk_unverify':
        case 'bulk_delete':
            handleBulkAction();
            break;
        case 'export':
            handleExport();
            break;
        case 'search':
            handleSearch();
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleGet() {
    global $pdo;
    
    $place_id = $_GET['id'] ?? null;
    
    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM breweries WHERE id = ?
        ");
        
        $stmt->execute([$place_id]);
        $place = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($place) {
            echo json_encode(['success' => true, 'place' => $place]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Place not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleCreate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $name = trim($input['name'] ?? '');
    $brewery_type = $input['brewery_type'] ?? 'brewery';
    $description = trim($input['description'] ?? '');
    $phone = trim($input['phone'] ?? '');
    $email = trim($input['email'] ?? '');
    $website_url = trim($input['website_url'] ?? '');
    $address_1 = trim($input['address_1'] ?? '');
    $address_2 = trim($input['address_2'] ?? '');
    $city = trim($input['city'] ?? '');
    $state = trim($input['state'] ?? '');
    $postal_code = trim($input['postal_code'] ?? '');
    $country = trim($input['country'] ?? 'United States');
    $latitude = $input['latitude'] ?? null;
    $longitude = $input['longitude'] ?? null;
    $verified = isset($input['verified']) ? 1 : 0;
    $claimed = isset($input['claimed']) ? 1 : 0;
    
    // Validation
    if (!$name) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place name is required']);
        return;
    }
    
    // Validate email if provided
    if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid email address']);
        return;
    }
    
    // Validate URL if provided
    if ($website_url && !filter_var($website_url, FILTER_VALIDATE_URL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid website URL']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO breweries (
                id, name, brewery_type, description, phone, email, website_url,
                address_1, address_2, city, state, postal_code, country,
                latitude, longitude, verified, claimed, created_at
            ) VALUES (
                UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ");
        
        $stmt->execute([
            $name, $brewery_type, $description, $phone, $email, $website_url,
            $address_1, $address_2, $city, $state, $postal_code, $country,
            $latitude, $longitude, $verified, $claimed
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Place created successfully',
            'place_id' => $pdo->lastInsertId()
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdate() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $place_id = $input['place_id'] ?? null;
    
    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    try {
        $fields = [];
        $values = [];
        
        $allowed_fields = [
            'name', 'brewery_type', 'description', 'phone', 'email', 'website_url',
            'address_1', 'address_2', 'city', 'state', 'postal_code', 'country',
            'latitude', 'longitude', 'verified', 'claimed'
        ];
        
        foreach ($allowed_fields as $field) {
            if (isset($input[$field])) {
                $fields[] = "$field = ?";
                $values[] = $input[$field];
            }
        }
        
        if (empty($fields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $place_id;
        
        $sql = "UPDATE breweries SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        echo json_encode(['success' => true, 'message' => 'Place updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDelete() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $place_id = $input['id'] ?? null;
    
    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // Delete related data first (photos, coupons, etc.)
        $stmt = $pdo->prepare("DELETE FROM photos WHERE owner_type = 'place' AND owner_id = ?");
        $stmt->execute([$place_id]);
        
        $stmt = $pdo->prepare("DELETE FROM coupons WHERE place_id = ?");
        $stmt->execute([$place_id]);
        
        // Delete the place
        $stmt = $pdo->prepare("DELETE FROM breweries WHERE id = ?");
        $stmt->execute([$place_id]);
        
        if ($stmt->rowCount() > 0) {
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'Place deleted successfully']);
        } else {
            $pdo->rollBack();
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Place not found']);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error deleting place: ' . $e->getMessage()]);
    }
}

function handleToggleVerification() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $place_id = $input['id'] ?? null;
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    $verified = ($action === 'verify') ? 1 : 0;
    
    try {
        $stmt = $pdo->prepare("UPDATE breweries SET verified = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$verified, $place_id]);
        
        $status = $verified ? 'verified' : 'unverified';
        echo json_encode(['success' => true, 'message' => "Place $status successfully"]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleToggleClaim() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $place_id = $input['id'] ?? null;
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (!$place_id) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Place ID is required']);
        return;
    }
    
    $claimed = ($action === 'claim') ? 1 : 0;
    
    try {
        $stmt = $pdo->prepare("UPDATE breweries SET claimed = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$claimed, $place_id]);
        
        $status = $claimed ? 'claimed' : 'unclaimed';
        echo json_encode(['success' => true, 'message' => "Place $status successfully"]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleBulkAction() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $place_ids = $input['place_ids'] ?? [];
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (empty($place_ids)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'No place IDs provided']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        $placeholders = str_repeat('?,', count($place_ids) - 1) . '?';
        $affected_count = 0;
        
        switch ($action) {
            case 'bulk_verify':
                $stmt = $pdo->prepare("UPDATE breweries SET verified = 1, updated_at = NOW() WHERE id IN ($placeholders)");
                $stmt->execute($place_ids);
                $affected_count = $stmt->rowCount();
                break;
                
            case 'bulk_unverify':
                $stmt = $pdo->prepare("UPDATE breweries SET verified = 0, updated_at = NOW() WHERE id IN ($placeholders)");
                $stmt->execute($place_ids);
                $affected_count = $stmt->rowCount();
                break;
                
            case 'bulk_delete':
                // Delete related data first
                $stmt = $pdo->prepare("DELETE FROM photos WHERE owner_type = 'place' AND owner_id IN ($placeholders)");
                $stmt->execute($place_ids);
                
                $stmt = $pdo->prepare("DELETE FROM coupons WHERE place_id IN ($placeholders)");
                $stmt->execute($place_ids);
                
                // Delete places
                $stmt = $pdo->prepare("DELETE FROM breweries WHERE id IN ($placeholders)");
                $stmt->execute($place_ids);
                $affected_count = $stmt->rowCount();
                break;
                
            default:
                $pdo->rollBack();
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid bulk action']);
                return;
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "Bulk action completed successfully",
            'affected_count' => $affected_count
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error performing bulk action: ' . $e->getMessage()]);
    }
}

function handleExport() {
    global $pdo;
    
    $format = $_GET['format'] ?? 'csv';
    
    try {
        $stmt = $pdo->query("
            SELECT name, brewery_type, description, phone, email, website_url,
                   address_1, address_2, city, state, postal_code, country,
                   latitude, longitude, verified, claimed, created_at
            FROM breweries 
            ORDER BY name
        ");
        $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($format === 'csv') {
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="places_export_' . date('Y-m-d') . '.csv"');
            
            $output = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($output, array_keys($places[0]));
            
            // CSV data
            foreach ($places as $place) {
                fputcsv($output, $place);
            }
            
            fclose($output);
        } else {
            echo json_encode(['success' => true, 'places' => $places]);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Export error: ' . $e->getMessage()]);
    }
}

function handleSearch() {
    global $pdo;
    
    $query = $_GET['q'] ?? '';
    $limit = min(50, max(1, intval($_GET['limit'] ?? 10)));
    
    if (strlen($query) < 2) {
        echo json_encode(['success' => true, 'places' => []]);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT id, name, city, state, brewery_type
            FROM breweries 
            WHERE name LIKE ? OR city LIKE ? OR state LIKE ?
            ORDER BY name
            LIMIT ?
        ");
        
        $search_param = "%$query%";
        $stmt->execute([$search_param, $search_param, $search_param, $limit]);
        $places = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'places' => $places]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Search error: ' . $e->getMessage()]);
    }
}
?>
