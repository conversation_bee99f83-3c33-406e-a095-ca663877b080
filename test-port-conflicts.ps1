# PORT CONFLICT DETECTION AND TESTING
# Check for conflicts on common web server ports

Write-Host "🔍 PORT CONFLICT DETECTION" -ForegroundColor Yellow
Write-Host "==========================" -ForegroundColor Yellow

# Common ports to check
$PortsToCheck = @(
    @{Port=80; Service="HTTP (Apache)"},
    @{Port=8080; Service="HTTP Alternative"},
    @{Port=443; Service="HTTPS"},
    @{Port=3306; Service="MySQL"},
    @{Port=3307; Service="MySQL Alternative"},
    @{Port=8000; Service="Development Server"},
    @{Port=8888; Service="XAMPP Alternative"}
)

Write-Host ""
Write-Host "1. CHECKING PORT USAGE:" -ForegroundColor Cyan

foreach ($portInfo in $PortsToCheck) {
    $port = $portInfo.Port
    $service = $portInfo.Service
    
    Write-Host ""
    Write-Host "Port $port ($service):" -ForegroundColor Yellow
    
    try {
        $netstatResult = netstat -an | findstr ":$port "
        if ($netstatResult) {
            Write-Host "  ⚠️ PORT $port IS IN USE:" -ForegroundColor Red
            $netstatResult | ForEach-Object { 
                Write-Host "    $_" -ForegroundColor White 
                
                # Try to identify the process
                if ($_ -match "LISTENING") {
                    Write-Host "    🔴 Something is listening on port $port" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "  ✅ Port $port is FREE" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ❌ Could not check port $port" -ForegroundColor Red
    }
}

# Check what processes are using these ports
Write-Host ""
Write-Host "2. IDENTIFYING PROCESSES USING PORTS:" -ForegroundColor Cyan

try {
    Write-Host ""
    Write-Host "Detailed port usage (with process IDs):" -ForegroundColor Yellow
    $netstatDetailed = netstat -ano | findstr ":80 :8080 :443 :3306"
    if ($netstatDetailed) {
        $netstatDetailed | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "  No processes found on common ports" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not get detailed port information" -ForegroundColor Red
}

# Test actual connectivity
Write-Host ""
Write-Host "3. TESTING ACTUAL CONNECTIVITY:" -ForegroundColor Cyan

$TestUrls = @(
    "http://localhost",
    "http://localhost:80",
    "http://localhost:8080",
    "http://127.0.0.1",
    "http://127.0.0.1:80",
    "http://127.0.0.1:8080"
)

$WorkingUrls = @()

foreach ($url in $TestUrls) {
    Write-Host ""
    Write-Host "Testing $url..." -ForegroundColor Gray
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 3 -UseBasicParsing
        Write-Host "  ✅ $url WORKS! (Status: $($response.StatusCode))" -ForegroundColor Green
        $WorkingUrls += $url
        
        # Test if it's actually Apache/XAMPP
        if ($response.Headers.Server) {
            Write-Host "    Server: $($response.Headers.Server)" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "  ❌ $url FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Check for IIS conflicts
Write-Host ""
Write-Host "4. CHECKING FOR IIS CONFLICTS:" -ForegroundColor Cyan

try {
    $iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
    if ($iisService) {
        Write-Host "IIS Service found:" -ForegroundColor Yellow
        Write-Host "  Status: $($iisService.Status)" -ForegroundColor White
        Write-Host "  Start Type: $($iisService.StartType)" -ForegroundColor White
        
        if ($iisService.Status -eq "Running") {
            Write-Host "  🔴 IIS IS RUNNING - This conflicts with Apache on port 80!" -ForegroundColor Red
            Write-Host "  Solution: Stop IIS or use port 8080 for Apache" -ForegroundColor Yellow
        } else {
            Write-Host "  ✅ IIS is stopped - no conflict" -ForegroundColor Green
        }
    } else {
        Write-Host "✅ IIS not installed - no conflict" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not check IIS status" -ForegroundColor Gray
}

# Check for Skype conflicts
Write-Host ""
Write-Host "5. CHECKING FOR SKYPE CONFLICTS:" -ForegroundColor Cyan

$skypeProcess = Get-Process -Name "Skype" -ErrorAction SilentlyContinue
if ($skypeProcess) {
    Write-Host "⚠️ Skype is running - may conflict with port 80/443" -ForegroundColor Yellow
    Write-Host "  Consider closing Skype or changing its port settings" -ForegroundColor White
} else {
    Write-Host "✅ Skype not running - no conflict" -ForegroundColor Green
}

# Check XAMPP processes
Write-Host ""
Write-Host "6. CHECKING XAMPP PROCESSES:" -ForegroundColor Cyan

$apacheProcess = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apacheProcess) {
    Write-Host "✅ Apache (httpd) is running:" -ForegroundColor Green
    $apacheProcess | Format-Table ProcessName, Id, CPU -AutoSize | Out-String | Write-Host -ForegroundColor White
} else {
    Write-Host "❌ Apache (httpd) is NOT running" -ForegroundColor Red
}

$mysqlProcess = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysqlProcess) {
    Write-Host "✅ MySQL (mysqld) is running:" -ForegroundColor Green
    $mysqlProcess | Format-Table ProcessName, Id, CPU -AutoSize | Out-String | Write-Host -ForegroundColor White
} else {
    Write-Host "❌ MySQL (mysqld) is NOT running" -ForegroundColor Red
}

# Summary and recommendations
Write-Host ""
Write-Host "7. SUMMARY AND RECOMMENDATIONS:" -ForegroundColor Red
Write-Host "===============================" -ForegroundColor Red

if ($WorkingUrls.Count -gt 0) {
    Write-Host ""
    Write-Host "🎉 GOOD NEWS: Found working URLs!" -ForegroundColor Green
    foreach ($url in $WorkingUrls) {
        Write-Host "  ✅ $url" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🧪 TEST YOUR PROJECT:" -ForegroundColor Yellow
    $projectUrl = $WorkingUrls[0] + "/beersty-lovable"
    Write-Host "  Main: $projectUrl" -ForegroundColor White
    Write-Host "  Admin: $projectUrl/admin/user-management.php" -ForegroundColor White
    Write-Host "  PDO Test: $projectUrl/test-pdo-simple.php" -ForegroundColor White
    
    # Open the working URLs
    Write-Host ""
    Write-Host "Opening working URLs..." -ForegroundColor Cyan
    Start-Process $WorkingUrls[0]
    Start-Sleep 1
    Start-Process "$($WorkingUrls[0])/beersty-lovable"
    Start-Sleep 1
    Start-Process "$($WorkingUrls[0])/beersty-lovable/admin/user-management.php"
    
} else {
    Write-Host ""
    Write-Host "❌ NO WORKING URLs FOUND!" -ForegroundColor Red
    Write-Host ""
    Write-Host "TROUBLESHOOTING STEPS:" -ForegroundColor Yellow
    Write-Host "1. Check if Apache is actually running in XAMPP Control Panel" -ForegroundColor White
    Write-Host "2. If port 80 is blocked, configure Apache to use port 8080" -ForegroundColor White
    Write-Host "3. Stop IIS if it's running: net stop w3svc" -ForegroundColor White
    Write-Host "4. Run XAMPP as Administrator" -ForegroundColor White
    Write-Host "5. Check Windows Firewall settings" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
