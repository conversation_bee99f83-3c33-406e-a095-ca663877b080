<?php
/**
 * Setup Breweries Data
 * Ensure breweries table exists and has sample data
 */

require_once 'config/config.php';

echo "<h1>🍺 Breweries Setup</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Setting up breweries data...</h2>";
    
    // Check if breweries table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'breweries'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Breweries table does not exist. Please run the main database setup first.</p>";
        echo "<p><a href='setup.php' class='btn btn-primary'>Run Database Setup</a></p>";
        exit;
    }
    
    echo "<p>✅ Breweries table exists</p>";
    
    // Check current brewery count
    $stmt = $conn->query("SELECT COUNT(*) as count FROM breweries");
    $currentCount = $stmt->fetch()['count'];
    
    echo "<p>Current breweries in database: <strong>$currentCount</strong></p>";
    
    if ($currentCount < 10) {
        echo "<h3>Adding sample breweries...</h3>";
        
        $sampleBreweries = [
            [
                'name' => 'Stone Brewing',
                'address' => '1999 Citracado Pkwy',
                'city' => 'Escondido',
                'state' => 'CA',
                'zip' => '92029',
                'phone' => '(*************',
                'website' => 'https://stonebrewing.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'Known for bold, hoppy beers and innovative brewing techniques. Stone Brewing has been a pioneer in craft beer since 1996.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 1250,
                'like_count' => 890,
                'latitude' => 33.1192,
                'longitude' => -117.0931
            ],
            [
                'name' => 'Dogfish Head Brewery',
                'address' => '6 Cannery Village Center',
                'city' => 'Milton',
                'state' => 'DE',
                'zip' => '19968',
                'phone' => '(*************',
                'website' => 'https://dogfish.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'Off-centered ales for off-centered people. Dogfish Head has been brewing since 1995.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 980,
                'like_count' => 756,
                'latitude' => 38.7773,
                'longitude' => -75.3082
            ],
            [
                'name' => 'Bell\'s Brewery',
                'address' => '355 E Kalamazoo Ave',
                'city' => 'Kalamazoo',
                'state' => 'MI',
                'zip' => '49007',
                'phone' => '(*************',
                'website' => 'https://bellsbeer.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'Brewing exceptional beers since 1985. Home of Two Hearted Ale.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 1100,
                'like_count' => 823,
                'latitude' => 42.2917,
                'longitude' => -85.5872
            ],
            [
                'name' => 'Russian River Brewing',
                'address' => '725 4th St',
                'city' => 'Santa Rosa',
                'state' => 'CA',
                'zip' => '95404',
                'phone' => '(*************',
                'website' => 'https://russianriverbrewing.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'micro',
                'description' => 'Famous for Pliny the Elder and exceptional sour beers.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 1450,
                'like_count' => 1200,
                'latitude' => 38.4404,
                'longitude' => -122.7144
            ],
            [
                'name' => 'Founders Brewing',
                'address' => '235 Grandville Ave SW',
                'city' => 'Grand Rapids',
                'state' => 'MI',
                'zip' => '49503',
                'phone' => '(*************',
                'website' => 'https://foundersbrewing.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'We don\'t brew beer for everyone. Established in 1997.',
                'verified' => 1,
                'claimed' => 0,
                'follower_count' => 890,
                'like_count' => 654,
                'latitude' => 42.9634,
                'longitude' => -85.6681
            ],
            [
                'name' => 'Trillium Brewing',
                'address' => '369 Congress St',
                'city' => 'Boston',
                'state' => 'MA',
                'zip' => '02210',
                'phone' => '(*************',
                'website' => 'https://trilliumbrewing.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'micro',
                'description' => 'New England style IPAs and farmhouse ales.',
                'verified' => 0,
                'claimed' => 0,
                'follower_count' => 567,
                'like_count' => 432,
                'latitude' => 42.3505,
                'longitude' => -71.0395
            ],
            [
                'name' => 'Tree House Brewing',
                'address' => '129 Sturbridge Rd',
                'city' => 'Charlton',
                'state' => 'MA',
                'zip' => '01507',
                'phone' => '(*************',
                'website' => 'https://treehousebrew.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'micro',
                'description' => 'Very good beer. No shortcuts. Exceptional New England IPAs.',
                'verified' => 0,
                'claimed' => 0,
                'follower_count' => 789,
                'like_count' => 623,
                'latitude' => 42.1370,
                'longitude' => -71.9703
            ],
            [
                'name' => 'The Alchemist',
                'address' => '35 Crossroad',
                'city' => 'Stowe',
                'state' => 'VT',
                'zip' => '05672',
                'phone' => '(*************',
                'website' => 'https://alchemistbeer.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'micro',
                'description' => 'Home of Heady Topper. Brewing exceptional Vermont beer.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 1023,
                'like_count' => 876,
                'latitude' => 44.4654,
                'longitude' => -72.6874
            ],
            [
                'name' => 'Cigar City Brewing',
                'address' => '3924 W Spruce St',
                'city' => 'Tampa',
                'state' => 'FL',
                'zip' => '33607',
                'phone' => '(*************',
                'website' => 'https://cigarcitybrewing.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'Tampa Bay\'s premier craft brewery since 2009.',
                'verified' => 1,
                'claimed' => 1,
                'follower_count' => 756,
                'like_count' => 543,
                'latitude' => 27.9506,
                'longitude' => -82.5540
            ],
            [
                'name' => 'Ballast Point Brewing',
                'address' => '2215 India St',
                'city' => 'San Diego',
                'state' => 'CA',
                'zip' => '92101',
                'phone' => '(*************',
                'website' => 'https://ballastpoint.com',
                'email' => '<EMAIL>',
                'brewery_type' => 'regional',
                'description' => 'Award-winning craft beer from San Diego since 1996.',
                'verified' => 1,
                'claimed' => 0,
                'follower_count' => 634,
                'like_count' => 478,
                'latitude' => 32.7280,
                'longitude' => -117.1695
            ]
        ];
        
        $insertStmt = $conn->prepare("
            INSERT INTO breweries (
                name, address, city, state, zip, phone, website, email, 
                brewery_type, description, verified, claimed, 
                follower_count, like_count, latitude, longitude
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $inserted = 0;
        foreach ($sampleBreweries as $brewery) {
            try {
                // Check if brewery already exists
                $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE name = ?");
                $checkStmt->execute([$brewery['name']]);
                
                if ($checkStmt->rowCount() == 0) {
                    $insertStmt->execute([
                        $brewery['name'],
                        $brewery['address'],
                        $brewery['city'],
                        $brewery['state'],
                        $brewery['zip'],
                        $brewery['phone'],
                        $brewery['website'],
                        $brewery['email'],
                        $brewery['brewery_type'],
                        $brewery['description'],
                        $brewery['verified'],
                        $brewery['claimed'],
                        $brewery['follower_count'],
                        $brewery['like_count'],
                        $brewery['latitude'],
                        $brewery['longitude']
                    ]);
                    $inserted++;
                    echo "<p>✅ Added: {$brewery['name']}</p>";
                } else {
                    echo "<p>⏭️ Skipped: {$brewery['name']} (already exists)</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Failed to add {$brewery['name']}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>✅ Breweries Setup Complete!</h3>";
        echo "<p>Successfully added <strong>$inserted</strong> new breweries to the database.</p>";
        echo "</div>";
        
    } else {
        echo "<p>✅ Database already has sufficient brewery data ($currentCount breweries)</p>";
    }
    
    // Final count
    $stmt = $conn->query("SELECT COUNT(*) as count FROM breweries");
    $finalCount = $stmt->fetch()['count'];
    
    echo "<h3>📊 Final Statistics:</h3>";
    echo "<ul>";
    echo "<li><strong>Total Breweries:</strong> $finalCount</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM breweries WHERE verified = 1");
    $verifiedCount = $stmt->fetch()['count'];
    echo "<li><strong>Verified Breweries:</strong> $verifiedCount</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM breweries WHERE claimed = 1");
    $claimedCount = $stmt->fetch()['count'];
    echo "<li><strong>Claimed Breweries:</strong> $claimedCount</li>";
    
    $stmt = $conn->query("SELECT COUNT(DISTINCT state) as count FROM breweries");
    $stateCount = $stmt->fetch()['count'];
    echo "<li><strong>States Represented:</strong> $stateCount</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='breweries/listing.php'>View Breweries →</a> | <a href='test-breweries.php'>Test Breweries →</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
