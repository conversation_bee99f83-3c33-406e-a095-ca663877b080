# Fix Localhost Connection Issues
# This script diagnoses and fixes common localhost problems

Write-Host "LOCALHOST CONNECTION TROUBLESHOOTER" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow

# Step 1: Check what's using port 80
Write-Host ""
Write-Host "STEP 1: Checking what's using port 80..." -ForegroundColor Cyan
try {
    $port80 = netstat -an | findstr ":80 "
    if ($port80) {
        Write-Host "Port 80 usage:" -ForegroundColor Yellow
        $port80 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "Port 80 appears to be free" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not check port 80" -ForegroundColor Red
}

# Step 2: Check for common port conflicts
Write-Host ""
Write-Host "STEP 2: Checking for common conflicts..." -ForegroundColor Cyan

# Check if IIS is running
$iis = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
if ($iis -and $iis.Status -eq "Running") {
    Write-Host "WARNING: IIS (Internet Information Services) is running!" -ForegroundColor Red
    Write-Host "IIS conflicts with Apache on port 80" -ForegroundColor Yellow
    $stopIIS = Read-Host "Stop IIS now? (y/n)"
    if ($stopIIS -eq "y" -or $stopIIS -eq "Y") {
        Stop-Service -Name "W3SVC" -Force
        Write-Host "IIS stopped" -ForegroundColor Green
    }
}

# Check if Skype is using port 80
$skype = Get-Process -Name "Skype" -ErrorAction SilentlyContinue
if ($skype) {
    Write-Host "WARNING: Skype is running and may conflict with port 80" -ForegroundColor Yellow
    Write-Host "Consider closing Skype or changing its port settings" -ForegroundColor Yellow
}

# Step 3: Find and start XAMPP
Write-Host ""
Write-Host "STEP 3: Starting XAMPP services..." -ForegroundColor Cyan

$xamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$xamppPath = $xamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $xamppPath) {
    Write-Host "ERROR: XAMPP not found!" -ForegroundColor Red
    Write-Host "Please install XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found XAMPP at: $xamppPath" -ForegroundColor Green

# Stop any existing processes
Write-Host "Stopping existing processes..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 3

# Start XAMPP Control Panel
$xamppControl = Join-Path $xamppPath "xampp-control.exe"
if (Test-Path $xamppControl) {
    Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
    Start-Process $xamppControl -WindowStyle Normal
    Start-Sleep 3
}

# Step 4: Try alternative ports
Write-Host ""
Write-Host "STEP 4: Testing alternative solutions..." -ForegroundColor Cyan

Write-Host "If port 80 is blocked, we can try port 8080" -ForegroundColor Yellow
$useAltPort = Read-Host "Try Apache on port 8080? (y/n)"

if ($useAltPort -eq "y" -or $useAltPort -eq "Y") {
    # Modify Apache config for port 8080
    $httpdConf = Join-Path $xamppPath "apache\conf\httpd.conf"
    if (Test-Path $httpdConf) {
        Write-Host "Configuring Apache for port 8080..." -ForegroundColor Yellow
        
        # Backup original config
        $backupConf = $httpdConf + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
        Copy-Item $httpdConf $backupConf
        
        # Read and modify config
        $content = Get-Content $httpdConf
        for ($i = 0; $i -lt $content.Length; $i++) {
            if ($content[$i] -match "^Listen 80$") {
                $content[$i] = "Listen 8080"
                Write-Host "Changed Listen port to 8080" -ForegroundColor Green
            }
            if ($content[$i] -match "^ServerName localhost:80$") {
                $content[$i] = "ServerName localhost:8080"
                Write-Host "Changed ServerName to port 8080" -ForegroundColor Green
            }
        }
        
        # Write changes
        $content | Set-Content $httpdConf -Encoding UTF8
        Write-Host "Apache configured for port 8080" -ForegroundColor Green
        Write-Host "You'll access your site at: http://localhost:8080" -ForegroundColor Yellow
    }
}

# Step 5: Manual instructions
Write-Host ""
Write-Host "STEP 5: Manual startup required..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "In the XAMPP Control Panel:" -ForegroundColor White
Write-Host "1. Click START next to Apache" -ForegroundColor White
Write-Host "2. Click START next to MySQL" -ForegroundColor White
Write-Host "3. If Apache shows an error, check the logs" -ForegroundColor White
Write-Host ""

if ($useAltPort -eq "y" -or $useAltPort -eq "Y") {
    Write-Host "After starting Apache, test these URLs:" -ForegroundColor Yellow
    Write-Host "  http://localhost:8080" -ForegroundColor White
    Write-Host "  http://localhost:8080/beersty-lovable" -ForegroundColor White
} else {
    Write-Host "After starting Apache, test these URLs:" -ForegroundColor Yellow
    Write-Host "  http://localhost" -ForegroundColor White
    Write-Host "  http://localhost/beersty-lovable" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter AFTER starting Apache and MySQL in XAMPP Control Panel"

# Step 6: Test connection
Write-Host ""
Write-Host "STEP 6: Testing connection..." -ForegroundColor Cyan

$testUrls = @("http://localhost", "http://localhost:8080")
$working = $false

foreach ($url in $testUrls) {
    try {
        Write-Host "Testing $url..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        Write-Host "SUCCESS: $url is working! (Status: $($response.StatusCode))" -ForegroundColor Green
        $working = $true
        
        # Test project
        $projectUrl = $url + "/beersty-lovable"
        try {
            $projectResponse = Invoke-WebRequest -Uri $projectUrl -TimeoutSec 5 -UseBasicParsing
            Write-Host "SUCCESS: Project accessible at $projectUrl" -ForegroundColor Green
            
            # Open working URLs
            Start-Process $url
            Start-Process $projectUrl
            Start-Process ($projectUrl + "/admin/user-management.php")
            
        } catch {
            Write-Host "Project not found at $projectUrl" -ForegroundColor Yellow
        }
        break
        
    } catch {
        Write-Host "$url not responding" -ForegroundColor Red
    }
}

if (-not $working) {
    Write-Host ""
    Write-Host "TROUBLESHOOTING STEPS:" -ForegroundColor Red
    Write-Host "1. Check XAMPP Control Panel - is Apache green/running?" -ForegroundColor White
    Write-Host "2. Click 'Logs' next to Apache to see error messages" -ForegroundColor White
    Write-Host "3. Try running XAMPP as Administrator" -ForegroundColor White
    Write-Host "4. Check Windows Firewall settings" -ForegroundColor White
    Write-Host "5. Restart your computer and try again" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
