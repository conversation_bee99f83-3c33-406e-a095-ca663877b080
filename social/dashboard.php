<?php
/**
 * Social Dashboard
 * Central hub for all social networking features
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Social Dashboard - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css'];
$additionalJS = ['../assets/js/social-dashboard.js'];

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    // Get user's social statistics
    $socialStatsQuery = "
        SELECT 
            us.*,
            COUNT(DISTINCT f1.id) as following_count,
            COUNT(DISTINCT f2.id) as followers_count,
            COUNT(DISTINCT cm.id) as clubs_count,
            COUNT(DISTINCT er.id) as events_attending,
            COUNT(DISTINCT cp.id) as active_challenges,
            COUNT(DISTINCT bt.id) as active_trades
        FROM user_statistics us
        LEFT JOIN user_follows f1 ON us.user_id = f1.follower_id AND f1.status = 'accepted'
        LEFT JOIN user_follows f2 ON us.user_id = f2.following_id AND f2.status = 'accepted'
        LEFT JOIN club_memberships cm ON us.user_id = cm.user_id AND cm.status = 'active'
        LEFT JOIN event_rsvps er ON us.user_id = er.user_id AND er.rsvp_status = 'going'
        LEFT JOIN challenge_participants cp ON us.user_id = cp.user_id AND cp.completed = 0
        LEFT JOIN beer_trades bt ON us.user_id = bt.initiator_id AND bt.status = 'open'
        WHERE us.user_id = ?
        GROUP BY us.user_id
    ";
    $stmt = $conn->prepare($socialStatsQuery);
    $stmt->execute([$user['id']]);
    $socialStats = $stmt->fetch();
    
    // Get recent activity feed
    $activityFeedQuery = "
        SELECT 
            ua.*,
            p.first_name, p.last_name, p.username, p.avatar,
            CASE 
                WHEN ua.activity_type = 'beer_rating' THEN CONCAT('rated ', ua.activity_data)
                WHEN ua.activity_type = 'beer_checkin' THEN CONCAT('checked in at ', ua.activity_data)
                WHEN ua.activity_type = 'user_follow' THEN 'started following someone'
                WHEN ua.activity_type = 'event_created' THEN CONCAT('created event: ', ua.activity_data)
                WHEN ua.activity_type = 'challenge_completed' THEN CONCAT('completed challenge: ', ua.activity_data)
                WHEN ua.activity_type = 'club_joined' THEN CONCAT('joined club: ', ua.activity_data)
                WHEN ua.activity_type = 'trade_posted' THEN CONCAT('posted trade: ', ua.activity_data)
                ELSE ua.activity_data
            END as activity_description
        FROM user_activities ua
        JOIN profiles p ON ua.user_id = p.id
        WHERE ua.user_id IN (
            SELECT following_id FROM user_follows 
            WHERE follower_id = ? AND status = 'accepted'
            UNION
            SELECT ?
        )
        ORDER BY ua.created_at DESC
        LIMIT 20
    ";
    $stmt = $conn->prepare($activityFeedQuery);
    $stmt->execute([$user['id'], $user['id']]);
    $activityFeed = $stmt->fetchAll();
    
    // Get upcoming events
    $upcomingEventsQuery = "
        SELECT e.*, er.rsvp_status, b.name as brewery_name
        FROM events e
        JOIN event_rsvps er ON e.id = er.event_id
        LEFT JOIN breweries b ON e.brewery_id = b.id
        WHERE er.user_id = ? AND e.start_datetime >= NOW() AND e.status = 'published'
        ORDER BY e.start_datetime ASC
        LIMIT 5
    ";
    $stmt = $conn->prepare($upcomingEventsQuery);
    $stmt->execute([$user['id']]);
    $upcomingEvents = $stmt->fetchAll();
    
    // Get active challenges
    $activeChallengesQuery = "
        SELECT sc.*, cp.progress, cp.completed
        FROM social_challenges sc
        JOIN challenge_participants cp ON sc.id = cp.challenge_id
        WHERE cp.user_id = ? AND sc.status = 'active' AND cp.completed = 0
        ORDER BY sc.end_date ASC
        LIMIT 5
    ";
    $stmt = $conn->prepare($activeChallengesQuery);
    $stmt->execute([$user['id']]);
    $activeChallenges = $stmt->fetchAll();
    
    // Get recent check-ins from friends
    $friendsCheckinsQuery = "
        SELECT 
            bc.*,
            p.first_name, p.last_name, p.username, p.avatar,
            br.name as brewery_name,
            bi.name as beer_name
        FROM beer_checkins bc
        JOIN profiles p ON bc.user_id = p.id
        LEFT JOIN breweries br ON bc.brewery_id = br.id
        LEFT JOIN beer_items bi ON bc.beer_id = bi.id
        WHERE bc.user_id IN (
            SELECT following_id FROM user_follows 
            WHERE follower_id = ? AND status = 'accepted'
        )
        ORDER BY bc.created_at DESC
        LIMIT 10
    ";
    $stmt = $conn->prepare($friendsCheckinsQuery);
    $stmt->execute([$user['id']]);
    $friendsCheckins = $stmt->fetchAll();
    
    // Get trending beers
    $trendingBeersQuery = "
        SELECT 
            bi.*,
            COUNT(bc.id) as checkin_count,
            AVG(br.rating) as avg_rating
        FROM beer_items bi
        LEFT JOIN beer_checkins bc ON bi.id = bc.beer_id AND bc.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        LEFT JOIN beer_reviews br ON bi.id = br.beer_id
        GROUP BY bi.id
        HAVING checkin_count > 0
        ORDER BY checkin_count DESC, avg_rating DESC
        LIMIT 5
    ";
    $stmt = $conn->prepare($trendingBeersQuery);
    $stmt->execute();
    $trendingBeers = $stmt->fetchAll();
    
    // Get recent badges earned
    $recentBadgesQuery = "
        SELECT ub.*, b.name, b.description, b.icon
        FROM user_badges ub
        JOIN badges b ON ub.badge_id = b.id
        WHERE ub.user_id = ?
        ORDER BY ub.earned_at DESC
        LIMIT 3
    ";
    $stmt = $conn->prepare($recentBadgesQuery);
    $stmt->execute([$user['id']]);
    $recentBadges = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Social dashboard error: " . $e->getMessage());
    $socialStats = [];
    $activityFeed = [];
    $upcomingEvents = [];
    $activeChallenges = [];
    $friendsCheckins = [];
    $trendingBeers = [];
    $recentBadges = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Welcome Header -->
            <div class="welcome-header mb-4">
                <div class="d-flex align-items-center">
                    <div class="avatar-lg me-3">
                        <?php if ($user['avatar']): ?>
                            <img src="<?php echo htmlspecialchars($user['avatar']); ?>" 
                                 class="rounded-circle" width="60" height="60" alt="Avatar">
                        <?php else: ?>
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-user fa-2x text-white"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h2 class="mb-0">Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</h2>
                        <p class="text-muted mb-0">Here's what's happening in your beer community</p>
                    </div>
                </div>
            </div>
            
            <!-- Social Stats Overview -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-beer fa-2x text-primary mb-2"></i>
                            <h4 class="mb-0"><?php echo $socialStats['total_checkins'] ?? 0; ?></h4>
                            <small class="text-muted">Check-ins</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-success mb-2"></i>
                            <h4 class="mb-0"><?php echo $socialStats['followers_count'] ?? 0; ?></h4>
                            <small class="text-muted">Followers</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                            <h4 class="mb-0"><?php echo $socialStats['total_badges'] ?? 0; ?></h4>
                            <small class="text-muted">Badges</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-2x text-info mb-2"></i>
                            <h4 class="mb-0"><?php echo $socialStats['social_score'] ?? 0; ?></h4>
                            <small class="text-muted">Social Score</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="../check-in.php" class="btn btn-primary w-100">
                                <i class="fas fa-map-pin me-2"></i>Check In
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="events.php" class="btn btn-success w-100">
                                <i class="fas fa-calendar me-2"></i>Events
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="challenges.php" class="btn btn-warning w-100">
                                <i class="fas fa-trophy me-2"></i>Challenges
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="trading.php" class="btn btn-info w-100">
                                <i class="fas fa-exchange-alt me-2"></i>Trading
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Activity Feed -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-stream me-2"></i>Activity Feed
                    </h5>
                    <a href="activity-feed.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($activityFeed)): ?>
                        <?php foreach ($activityFeed as $activity): ?>
                            <div class="activity-item d-flex p-3 border-bottom">
                                <div class="flex-shrink-0 me-3">
                                    <?php if ($activity['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($activity['avatar']); ?>" 
                                             class="rounded-circle" width="40" height="40" alt="Avatar">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-content">
                                        <strong><?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?></strong>
                                        <?php echo htmlspecialchars($activity['activity_description']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo timeAgo($activity['created_at']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-stream fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No recent activity. Follow some friends to see their updates!</p>
                            <a href="../discover-users.php" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Find Friends
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Upcoming Events -->
            <?php if (!empty($upcomingEvents)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>Upcoming Events
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <?php foreach ($upcomingEvents as $event): ?>
                            <div class="d-flex p-3 border-bottom">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-primary text-white rounded text-center p-2" style="min-width: 50px;">
                                        <div class="small"><?php echo date('M', strtotime($event['start_datetime'])); ?></div>
                                        <div class="fw-bold"><?php echo date('j', strtotime($event['start_datetime'])); ?></div>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="event-detail.php?id=<?php echo $event['id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($event['title']); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo date('g:i A', strtotime($event['start_datetime'])); ?>
                                        <span class="badge bg-<?php echo $event['rsvp_status'] === 'going' ? 'success' : 'secondary'; ?> ms-2">
                                            <?php echo ucfirst($event['rsvp_status']); ?>
                                        </span>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="p-3">
                            <a href="events.php" class="btn btn-outline-primary w-100">View All Events</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Active Challenges -->
            <?php if (!empty($activeChallenges)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>Active Challenges
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($activeChallenges as $challenge): ?>
                            <div class="challenge-item mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">
                                        <a href="challenge-detail.php?id=<?php echo $challenge['id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($challenge['title']); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo date('M j', strtotime($challenge['end_date'])); ?>
                                    </small>
                                </div>
                                <div class="progress mb-2">
                                    <?php 
                                    $progress = json_decode($challenge['progress'], true);
                                    $rules = json_decode($challenge['rules'], true);
                                    $progressPercent = calculateChallengeProgress($progress, $rules);
                                    ?>
                                    <div class="progress-bar" style="width: <?php echo $progressPercent; ?>%">
                                        <?php echo $progressPercent; ?>%
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <a href="challenges.php" class="btn btn-outline-warning w-100">View All Challenges</a>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Recent Badges -->
            <?php if (!empty($recentBadges)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-medal me-2"></i>Recent Badges
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($recentBadges as $badge): ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="badge-icon me-3">
                                    <i class="<?php echo $badge['icon']; ?> fa-2x text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($badge['name']); ?></h6>
                                    <small class="text-muted">
                                        Earned <?php echo timeAgo($badge['earned_at']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <a href="../badges.php" class="btn btn-outline-warning w-100">View All Badges</a>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Trending Beers -->
            <?php if (!empty($trendingBeers)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-fire me-2"></i>Trending This Week
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($trendingBeers as $beer): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <a href="../beer-detail.php?id=<?php echo $beer['id']; ?>" class="text-decoration-none">
                                        <strong><?php echo htmlspecialchars($beer['name']); ?></strong>
                                    </a>
                                    <div class="small text-muted">
                                        <?php echo $beer['checkin_count']; ?> check-ins
                                        <?php if ($beer['avg_rating']): ?>
                                            • <?php echo number_format($beer['avg_rating'], 1); ?> ⭐
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <span class="badge bg-danger">🔥</span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Friends' Recent Check-ins -->
            <?php if (!empty($friendsCheckins)): ?>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>Friends' Check-ins
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <?php foreach (array_slice($friendsCheckins, 0, 5) as $checkin): ?>
                            <div class="d-flex p-3 border-bottom">
                                <div class="flex-shrink-0 me-3">
                                    <?php if ($checkin['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($checkin['avatar']); ?>" 
                                             class="rounded-circle" width="30" height="30" alt="Avatar">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 30px; height: 30px;">
                                            <i class="fas fa-user text-white small"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="small">
                                        <strong><?php echo htmlspecialchars($checkin['first_name']); ?></strong>
                                        checked in
                                        <?php if ($checkin['beer_name']): ?>
                                            <strong><?php echo htmlspecialchars($checkin['beer_name']); ?></strong>
                                        <?php endif; ?>
                                        <?php if ($checkin['brewery_name']): ?>
                                            at <strong><?php echo htmlspecialchars($checkin['brewery_name']); ?></strong>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo timeAgo($checkin['created_at']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php 
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

function calculateChallengeProgress($progress, $rules) {
    if (!$progress || !$rules) return 0;
    
    $target = $rules['target_count'] ?? 100;
    $current = $progress['current_count'] ?? 0;
    
    return min(100, ($current / $target) * 100);
}

require_once '../includes/footer.php'; 
?>
