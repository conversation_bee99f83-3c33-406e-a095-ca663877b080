<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'Discover Beer Enthusiasts - ' . APP_NAME;
$additionalCSS = ['/assets/css/social.css', '/assets/css/beersty-layouts.css'];

$user = getCurrentUser();
$search = sanitizeInput($_GET['search'] ?? '');
$role = $_GET['role'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

$users = [];
$totalUsers = 0;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Build search query
    $whereConditions = ["p.id != ?"];
    $params = [$user['id']];
    
    if (!empty($search)) {
        $whereConditions[] = "(p.first_name LIKE ? OR p.last_name LIKE ? OR p.username LIKE ? OR p.bio LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($role) && in_array($role, ['beer_enthusiast', 'beer_expert', 'customer'])) {
        $whereConditions[] = "p.role = ?";
        $params[] = $role;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get total count
    $countQuery = "
        SELECT COUNT(*) 
        FROM profiles p 
        WHERE $whereClause AND p.profile_visibility = 'public'
    ";
    $stmt = $conn->prepare($countQuery);
    $stmt->execute($params);
    $totalUsers = $stmt->fetchColumn();
    
    // Get users with pagination
    $query = "
        SELECT p.*, 
               (SELECT COUNT(*) FROM user_follows WHERE following_id = p.id) as follower_count,
               (SELECT COUNT(*) FROM user_follows WHERE follower_id = p.id) as following_count,
               (SELECT COUNT(*) FROM user_follows WHERE follower_id = ? AND following_id = p.id) as is_following
        FROM profiles p 
        WHERE $whereClause AND p.profile_visibility = 'public'
        ORDER BY p.total_reviews DESC, p.total_checkins DESC, p.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute(array_merge([$user['id']], $params));
    $users = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("User discovery error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading users.';
}

$totalPages = ceil($totalUsers / $limit);

include '../includes/header.php';
?>

<!-- Apply brewery theme background -->
<style>
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}
</style>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold mb-2" style="color: #F5F5DC;">
                <i class="fas fa-users me-3" style="color: #FFC107;"></i>Discover Beer Enthusiasts
            </h1>
            <p class="lead" style="color: #D69A6B;">
                Connect with fellow beer lovers and discover new perspectives
            </p>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card shadow-sm mb-4" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- Search -->
                <div class="col-md-6">
                    <label for="search" class="form-label" style="color: #F5F5DC;">
                        <i class="fas fa-search me-1" style="color: #FFC107;"></i>Search Users
                    </label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Name, username, or bio..."
                           style="background-color: #3B2A2A; border-color: #D69A6B; color: #F5F5DC;">
                </div>

                <!-- Role Filter -->
                <div class="col-md-4">
                    <label for="role" class="form-label" style="color: #F5F5DC;">
                        <i class="fas fa-user-tag me-1" style="color: #FFC107;"></i>User Type
                    </label>
                    <select class="form-select" id="role" name="role"
                            style="background-color: #3B2A2A; border-color: #D69A6B; color: #F5F5DC;">
                        <option value="">All Users</option>
                        <option value="beer_enthusiast" <?php echo $role === 'beer_enthusiast' ? 'selected' : ''; ?>>
                            🍺 Beer Enthusiasts
                        </option>
                        <option value="beer_expert" <?php echo $role === 'beer_expert' ? 'selected' : ''; ?>>
                            🎯 Beer Experts
                        </option>
                        <option value="customer" <?php echo $role === 'customer' ? 'selected' : ''; ?>>
                            👤 Customers
                        </option>
                    </select>
                </div>

                <!-- Search Button -->
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn w-100" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0" style="color: #F5F5DC;">
                    <?php echo number_format($totalUsers); ?> user<?php echo $totalUsers !== 1 ? 's' : ''; ?> found
                </h5>
                <?php if ($totalPages > 1): ?>
                    <span style="color: #D69A6B;">
                        Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- User Grid -->
    <?php if (empty($users)): ?>
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x mb-3" style="color: #D69A6B;"></i>
            <h4 style="color: #F5F5DC;">No users found</h4>
            <p style="color: #D69A6B;">Try adjusting your search criteria</p>
        </div>
    <?php else: ?>
        <div class="row g-4 mb-4">
            <?php foreach ($users as $discoveredUser): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm user-card" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
                        <div class="card-body text-center">
                            <!-- Avatar -->
                            <div class="user-avatar mb-3">
                                <?php if (!empty($discoveredUser['avatar'])): ?>
                                    <img src="<?php echo htmlspecialchars($discoveredUser['avatar']); ?>"
                                         alt="Profile Picture" class="rounded-circle" width="80" height="80"
                                         style="border: 2px solid #FFC107;">
                                <?php else: ?>
                                    <div class="avatar-placeholder rounded-circle mx-auto d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 80px; background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%); border: 2px solid #D69A6B;">
                                        <i class="fas fa-user fa-2x" style="color: #D69A6B;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- User Info -->
                            <h5 class="card-title mb-1" style="color: #F5F5DC;">
                                <?php
                                $displayName = trim($discoveredUser['first_name'] . ' ' . $discoveredUser['last_name']);
                                if (empty($displayName)) {
                                    $displayName = $discoveredUser['username'] ?: 'Beer Enthusiast';
                                }
                                echo htmlspecialchars($displayName);
                                ?>
                            </h5>

                            <?php if (!empty($discoveredUser['username'])): ?>
                                <p class="mb-2" style="color: #D69A6B;">@<?php echo htmlspecialchars($discoveredUser['username']); ?></p>
                            <?php endif; ?>

                            <!-- Role Badge -->
                            <div class="mb-3">
                                <span class="badge" style="background-color: #FFC107; color: #3B2A2A;">
                                    <?php
                                    $roleLabels = [
                                        'beer_enthusiast' => '🍺 Beer Enthusiast',
                                        'beer_expert' => '🎯 Beer Expert',
                                        'customer' => '👤 Customer'
                                    ];
                                    echo $roleLabels[$discoveredUser['role']] ?? ucfirst($discoveredUser['role']);
                                    ?>
                                </span>
                            </div>
                            
                            <!-- Bio -->
                            <?php if (!empty($discoveredUser['bio'])): ?>
                                <p class="small mb-3" style="color: #D69A6B;">
                                    <?php echo htmlspecialchars(substr($discoveredUser['bio'], 0, 100)); ?>
                                    <?php if (strlen($discoveredUser['bio']) > 100): ?>...<?php endif; ?>
                                </p>
                            <?php endif; ?>

                            <!-- Stats -->
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold" style="color: #FFC107;"><?php echo number_format($discoveredUser['total_reviews'] ?? 0); ?></div>
                                    <small style="color: #D69A6B;">Reviews</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold" style="color: #FFC107;"><?php echo number_format($discoveredUser['total_checkins'] ?? 0); ?></div>
                                    <small style="color: #D69A6B;">Check-ins</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold" style="color: #FFC107;"><?php echo number_format($discoveredUser['follower_count'] ?? 0); ?></div>
                                    <small style="color: #D69A6B;">Followers</small>
                                </div>
                            </div>

                            <!-- Location -->
                            <?php if (!empty($discoveredUser['location']) && $discoveredUser['show_location']): ?>
                                <p class="small mb-3" style="color: #D69A6B;">
                                    <i class="fas fa-map-marker-alt me-1" style="color: #FFC107;"></i>
                                    <?php echo htmlspecialchars($discoveredUser['location']); ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <!-- Actions -->
                        <div class="card-footer" style="background-color: #3B2A2A; border-top: 1px solid #D69A6B;">
                            <div class="d-flex gap-2">
                                <a href="/user/profile.php?id=<?php echo $discoveredUser['id']; ?>"
                                   class="btn btn-sm flex-fill" style="border: 1px solid #FFC107; color: #F5F5DC; background-color: transparent;">
                                    <i class="fas fa-eye me-1"></i>View Profile
                                </a>

                                <?php if ($discoveredUser['is_following']): ?>
                                    <button class="btn btn-sm follow-btn"
                                            style="background-color: #28a745; border-color: #28a745; color: white;"
                                            data-user-id="<?php echo $discoveredUser['id']; ?>"
                                            data-action="unfollow">
                                        <i class="fas fa-user-check me-1"></i>Following
                                    </button>
                                <?php else: ?>
                                    <button class="btn btn-sm follow-btn"
                                            style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;"
                                            data-user-id="<?php echo $discoveredUser['id']; ?>"
                                            data-action="follow">
                                        <i class="fas fa-user-plus me-1"></i>Follow
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="User pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                               style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                               style="<?php echo $i === $page ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;'; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                               style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
/* Brewery-themed hover effects for user cards */
.user-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.3) !important;
    border-color: #FFC107 !important;
}

/* Form styling enhancements */
.form-control:focus,
.form-select:focus {
    border-color: #FFC107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}

.form-control::placeholder {
    color: #D69A6B !important;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.card-footer .btn:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

/* Pagination hover effects */
.page-link:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

/* Avatar image styling */
.user-avatar img {
    transition: transform 0.2s ease;
}

.user-card:hover .user-avatar img {
    transform: scale(1.05);
}

/* Follow button specific styling */
.follow-btn[data-action="follow"]:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.follow-btn[data-action="unfollow"]:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle follow/unfollow buttons
    document.querySelectorAll('.follow-btn').forEach(button => {
        button.addEventListener('click', async function() {
            const userId = this.dataset.userId;
            const action = this.dataset.action;
            
            try {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
                
                const response = await fetch('/api/follow-user.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        action: action
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    if (action === 'follow') {
                        this.className = 'btn btn-sm follow-btn';
                        this.style.cssText = 'background-color: #28a745; border-color: #28a745; color: white;';
                        this.innerHTML = '<i class="fas fa-user-check me-1"></i>Following';
                        this.dataset.action = 'unfollow';
                    } else {
                        this.className = 'btn btn-sm follow-btn';
                        this.style.cssText = 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;';
                        this.innerHTML = '<i class="fas fa-user-plus me-1"></i>Follow';
                        this.dataset.action = 'follow';
                    }
                } else {
                    alert(result.message || 'An error occurred');
                    // Reset button
                    if (action === 'follow') {
                        this.innerHTML = '<i class="fas fa-user-plus me-1"></i>Follow';
                    } else {
                        this.innerHTML = '<i class="fas fa-user-check me-1"></i>Following';
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
                // Reset button
                if (action === 'follow') {
                    this.innerHTML = '<i class="fas fa-user-plus me-1"></i>Follow';
                } else {
                    this.innerHTML = '<i class="fas fa-user-check me-1"></i>Following';
                }
            } finally {
                this.disabled = false;
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
