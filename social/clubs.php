<?php
/**
 * Beer Clubs & Groups
 * Community groups for beer enthusiasts
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Beer Clubs - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css'];
$additionalJS = ['../assets/js/clubs.js'];

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);
$currentTab = $_GET['tab'] ?? 'discover';

// Get filter parameters
$focusArea = $_GET['focus'] ?? '';
$clubType = $_GET['type'] ?? '';
$location = $_GET['location'] ?? '';
$searchQuery = $_GET['search'] ?? '';

try {
    // Build WHERE clause for filters
    $whereConditions = ["bc.is_active = 1"];
    $params = [];
    
    if (!empty($focusArea)) {
        $whereConditions[] = "bc.focus_area = ?";
        $params[] = $focusArea;
    }
    
    if (!empty($clubType)) {
        $whereConditions[] = "bc.club_type = ?";
        $params[] = $clubType;
    }
    
    if (!empty($location)) {
        $whereConditions[] = "bc.location LIKE ?";
        $params[] = "%$location%";
    }
    
    if (!empty($searchQuery)) {
        $whereConditions[] = "(bc.name LIKE ? OR bc.description LIKE ?)";
        $params[] = "%$searchQuery%";
        $params[] = "%$searchQuery%";
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get all clubs for discovery
    $clubsQuery = "
        SELECT 
            bc.*,
            p.first_name, p.last_name, p.username, p.avatar,
            COUNT(DISTINCT cm.id) as member_count,
            MAX(CASE WHEN cm.user_id = ? THEN cm.status END) as user_membership_status,
            MAX(CASE WHEN cm.user_id = ? THEN cm.role END) as user_role
        FROM beer_clubs bc
        JOIN profiles p ON bc.creator_id = p.id
        LEFT JOIN club_memberships cm ON bc.id = cm.club_id AND cm.status = 'active'
        WHERE $whereClause
        GROUP BY bc.id
        ORDER BY bc.featured DESC, bc.created_at DESC
    ";
    $stmt = $conn->prepare($clubsQuery);
    $stmt->execute(array_merge([$user['id'], $user['id']], $params));
    $allClubs = $stmt->fetchAll();
    
    // Get user's clubs
    $userClubsQuery = "
        SELECT 
            bc.*,
            cm.role, cm.status, cm.joined_at,
            p.first_name, p.last_name, p.username,
            COUNT(DISTINCT cm2.id) as member_count
        FROM beer_clubs bc
        JOIN club_memberships cm ON bc.id = cm.club_id
        JOIN profiles p ON bc.creator_id = p.id
        LEFT JOIN club_memberships cm2 ON bc.id = cm2.club_id AND cm2.status = 'active'
        WHERE cm.user_id = ? AND cm.status IN ('active', 'pending')
        GROUP BY bc.id
        ORDER BY cm.joined_at DESC
    ";
    $stmt = $conn->prepare($userClubsQuery);
    $stmt->execute([$user['id']]);
    $userClubs = $stmt->fetchAll();
    
    // Get featured clubs
    $featuredClubsQuery = "
        SELECT 
            bc.*,
            p.first_name, p.last_name, p.username,
            COUNT(DISTINCT cm.id) as member_count
        FROM beer_clubs bc
        JOIN profiles p ON bc.creator_id = p.id
        LEFT JOIN club_memberships cm ON bc.id = cm.club_id AND cm.status = 'active'
        WHERE bc.featured = 1 AND bc.is_active = 1
        GROUP BY bc.id
        ORDER BY bc.created_at DESC
        LIMIT 6
    ";
    $stmt = $conn->prepare($featuredClubsQuery);
    $stmt->execute();
    $featuredClubs = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Clubs page error: " . $e->getMessage());
    $allClubs = [];
    $userClubs = [];
    $featuredClubs = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users me-2"></i>Beer Clubs
                    </h1>
                    <p class="text-muted mb-0">Join communities of beer enthusiasts</p>
                </div>
                <div>
                    <a href="create-club.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Club
                    </a>
                </div>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'discover' ? 'active' : ''; ?>" 
                       href="?tab=discover">
                        <i class="fas fa-search me-2"></i>Discover Clubs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'my' ? 'active' : ''; ?>" 
                       href="?tab=my">
                        <i class="fas fa-user me-2"></i>My Clubs
                        <span class="badge bg-primary ms-2"><?php echo count($userClubs); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'featured' ? 'active' : ''; ?>" 
                       href="?tab=featured">
                        <i class="fas fa-star me-2"></i>Featured
                    </a>
                </li>
            </ul>
            
            <!-- Discover Clubs Tab -->
            <?php if ($currentTab === 'discover'): ?>
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <input type="hidden" name="tab" value="discover">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search Clubs</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                       placeholder="Club name or description">
                            </div>
                            <div class="col-md-2">
                                <label for="focus" class="form-label">Focus Area</label>
                                <select class="form-select" id="focus" name="focus">
                                    <option value="">All Areas</option>
                                    <option value="general" <?php echo $focusArea === 'general' ? 'selected' : ''; ?>>General</option>
                                    <option value="style_specific" <?php echo $focusArea === 'style_specific' ? 'selected' : ''; ?>>Style Specific</option>
                                    <option value="local" <?php echo $focusArea === 'local' ? 'selected' : ''; ?>>Local</option>
                                    <option value="professional" <?php echo $focusArea === 'professional' ? 'selected' : ''; ?>>Professional</option>
                                    <option value="educational" <?php echo $focusArea === 'educational' ? 'selected' : ''; ?>>Educational</option>
                                    <option value="social" <?php echo $focusArea === 'social' ? 'selected' : ''; ?>>Social</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="type" class="form-label">Club Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">All Types</option>
                                    <option value="public" <?php echo $clubType === 'public' ? 'selected' : ''; ?>>Public</option>
                                    <option value="private" <?php echo $clubType === 'private' ? 'selected' : ''; ?>>Private</option>
                                    <option value="invite_only" <?php echo $clubType === 'invite_only' ? 'selected' : ''; ?>>Invite Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="<?php echo htmlspecialchars($location); ?>" 
                                       placeholder="City or region">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Clubs Grid -->
                <div class="row">
                    <?php foreach ($allClubs as $club): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card club-card h-100">
                                <?php if ($club['club_image']): ?>
                                    <img src="<?php echo htmlspecialchars($club['club_image']); ?>" 
                                         class="card-img-top" alt="Club Image" style="height: 150px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                         style="height: 150px;">
                                        <i class="fas fa-users fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body d-flex flex-column">
                                    <div class="mb-2">
                                        <span class="badge bg-<?php echo getClubTypeColor($club['club_type']); ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $club['club_type'])); ?>
                                        </span>
                                        <span class="badge bg-secondary">
                                            <?php echo ucwords(str_replace('_', ' ', $club['focus_area'])); ?>
                                        </span>
                                        <?php if ($club['featured']): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <h5 class="card-title"><?php echo htmlspecialchars($club['name']); ?></h5>
                                    
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($club['description'], 0, 100)); ?>
                                        <?php if (strlen($club['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                    
                                    <div class="club-info mb-3">
                                        <div class="d-flex justify-content-between text-muted small">
                                            <span>
                                                <i class="fas fa-users me-1"></i>
                                                <?php echo $club['member_count']; ?> members
                                            </span>
                                            <?php if ($club['location']): ?>
                                                <span>
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?php echo htmlspecialchars($club['location']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($club['meeting_frequency']): ?>
                                            <div class="text-muted small mt-1">
                                                <i class="fas fa-calendar me-1"></i>
                                                Meets <?php echo str_replace('_', ' ', $club['meeting_frequency']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <div class="d-flex gap-2">
                                            <a href="club-detail.php?id=<?php echo $club['id']; ?>" 
                                               class="btn btn-outline-primary flex-grow-1">
                                                View Club
                                            </a>
                                            
                                            <?php if ($club['user_membership_status']): ?>
                                                <button class="btn btn-sm btn-<?php echo $club['user_membership_status'] === 'active' ? 'success' : 'warning'; ?>" disabled>
                                                    <?php echo ucfirst($club['user_membership_status']); ?>
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success" 
                                                        onclick="joinClub('<?php echo $club['id']; ?>')">
                                                    Join
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($allClubs)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h4>No Clubs Found</h4>
                        <p class="text-muted">Try adjusting your filters or create a new club!</p>
                        <a href="create-club.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Club
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- My Clubs Tab -->
            <?php if ($currentTab === 'my'): ?>
                <div class="row">
                    <?php foreach ($userClubs as $club): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card club-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getClubRoleColor($club['role']); ?>">
                                        <?php echo ucfirst($club['role']); ?>
                                    </span>
                                    <span class="badge bg-<?php echo $club['status'] === 'active' ? 'success' : 'warning'; ?>">
                                        <?php echo ucfirst($club['status']); ?>
                                    </span>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($club['name']); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($club['description'], 0, 80)); ?>...
                                    </p>
                                    
                                    <div class="club-info mb-3">
                                        <div class="text-muted small">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $club['member_count']; ?> members
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar me-1"></i>
                                            Joined <?php echo date('M j, Y', strtotime($club['joined_at'])); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="club-detail.php?id=<?php echo $club['id']; ?>" 
                                           class="btn btn-primary w-100">
                                            Manage Club
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($userClubs)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                        <h4>No Clubs Joined</h4>
                        <p class="text-muted">Join some clubs to connect with fellow beer enthusiasts!</p>
                        <a href="?tab=discover" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Discover Clubs
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- Featured Clubs Tab -->
            <?php if ($currentTab === 'featured'): ?>
                <div class="row">
                    <?php foreach ($featuredClubs as $club): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card club-card h-100 border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <i class="fas fa-star me-2"></i>Featured Club
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($club['name']); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars($club['description']); ?>
                                    </p>
                                    
                                    <div class="club-info mb-3">
                                        <div class="text-muted small">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $club['member_count']; ?> members
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-user me-1"></i>
                                            Created by <?php echo htmlspecialchars($club['first_name'] . ' ' . $club['last_name']); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="club-detail.php?id=<?php echo $club['id']; ?>" 
                                           class="btn btn-warning w-100">
                                            <i class="fas fa-star me-2"></i>View Featured Club
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Club Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-primary"><?php echo count($allClubs); ?></div>
                                <small class="text-muted">Total Clubs</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-success"><?php echo count($userClubs); ?></div>
                                <small class="text-muted">Your Clubs</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Focus Areas -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bullseye me-2"></i>Focus Areas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="?tab=discover&focus=general" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-beer me-2"></i>General</span>
                            <span class="badge bg-primary rounded-pill">8</span>
                        </a>
                        <a href="?tab=discover&focus=style_specific" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-wine-glass me-2"></i>Style Specific</span>
                            <span class="badge bg-primary rounded-pill">5</span>
                        </a>
                        <a href="?tab=discover&focus=local" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-map-marker-alt me-2"></i>Local</span>
                            <span class="badge bg-primary rounded-pill">12</span>
                        </a>
                        <a href="?tab=discover&focus=educational" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-graduation-cap me-2"></i>Educational</span>
                            <span class="badge bg-primary rounded-pill">3</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="create-club.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Club
                        </a>
                        <a href="events.php" class="btn btn-outline-info">
                            <i class="fas fa-calendar me-2"></i>Events
                        </a>
                        <a href="challenges.php" class="btn btn-outline-success">
                            <i class="fas fa-trophy me-2"></i>Challenges
                        </a>
                        <a href="trading.php" class="btn btn-outline-warning">
                            <i class="fas fa-exchange-alt me-2"></i>Beer Trading
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function joinClub(clubId) {
    fetch('/api/club-membership.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            club_id: clubId,
            action: 'join'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error joining club: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error joining club');
    });
}
</script>

<?php 
// Helper functions
function getClubTypeColor($type) {
    $colors = [
        'public' => 'success',
        'private' => 'warning',
        'invite_only' => 'danger'
    ];
    return $colors[$type] ?? 'secondary';
}

function getClubRoleColor($role) {
    $colors = [
        'owner' => 'danger',
        'admin' => 'warning',
        'moderator' => 'info',
        'member' => 'success'
    ];
    return $colors[$role] ?? 'secondary';
}

require_once '../includes/footer.php'; 
?>
