<?php
/**
 * Live Activity Feed
 * Real-time social activity stream with live updates
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Live Feed - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css', '../assets/css/live-feed.css'];
$additionalJS = ['../assets/js/live-feed.js'];

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.* 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get user's following list for activity filtering
    $followingQuery = "
        SELECT following_id 
        FROM user_follows 
        WHERE follower_id = ? AND status = 'accepted'
    ";
    $stmt = $conn->prepare($followingQuery);
    $stmt->execute([$user['id']]);
    $following = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Include user's own activities
    $following[] = $user['id'];
    $followingPlaceholders = str_repeat('?,', count($following) - 1) . '?';
    
    // Get recent activities with enhanced data
    $activitiesQuery = "
        SELECT 
            ua.*,
            p.first_name, p.last_name, p.username, p.avatar,
            CASE 
                WHEN ua.activity_type = 'beer_checkin' THEN (
                    SELECT JSON_OBJECT(
                        'beer_name', bi.name,
                        'brewery_name', br.name,
                        'rating', bc.rating,
                        'location', bc.location,
                        'photo', bc.photo_url
                    )
                    FROM beer_checkins bc
                    LEFT JOIN beer_items bi ON bc.beer_id = bi.id
                    LEFT JOIN breweries br ON bc.brewery_id = br.id
                    WHERE bc.id = ua.related_id
                )
                WHEN ua.activity_type = 'beer_rating' THEN (
                    SELECT JSON_OBJECT(
                        'beer_name', bi.name,
                        'brewery_name', br.name,
                        'rating', br2.rating,
                        'review', br2.review
                    )
                    FROM beer_reviews br2
                    LEFT JOIN beer_items bi ON br2.beer_id = bi.id
                    LEFT JOIN breweries br ON bi.brewery_id = br.id
                    WHERE br2.id = ua.related_id
                )
                WHEN ua.activity_type = 'user_follow' THEN (
                    SELECT JSON_OBJECT(
                        'followed_user', CONCAT(p2.first_name, ' ', p2.last_name),
                        'followed_username', p2.username,
                        'followed_avatar', p2.avatar
                    )
                    FROM profiles p2
                    WHERE p2.id = ua.related_id
                )
                WHEN ua.activity_type = 'badge_earned' THEN (
                    SELECT JSON_OBJECT(
                        'badge_name', b.name,
                        'badge_description', b.description,
                        'badge_icon', b.icon
                    )
                    FROM badges b
                    WHERE b.id = ua.related_id
                )
                ELSE NULL
            END as enhanced_data,
            COUNT(DISTINCT al.id) as like_count,
            COUNT(DISTINCT ac.id) as comment_count,
            MAX(CASE WHEN al.user_id = ? THEN 1 ELSE 0 END) as user_liked
        FROM user_activities ua
        JOIN profiles p ON ua.user_id = p.id
        LEFT JOIN activity_likes al ON ua.id = al.activity_id
        LEFT JOIN activity_comments ac ON ua.id = ac.activity_id
        WHERE ua.user_id IN ($followingPlaceholders)
        AND ua.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY ua.id
        ORDER BY ua.created_at DESC
        LIMIT 50
    ";
    
    $params = array_merge([$user['id']], $following);
    $stmt = $conn->prepare($activitiesQuery);
    $stmt->execute($params);
    $activities = $stmt->fetchAll();
    
    // Get trending hashtags
    $trendingQuery = "
        SELECT 
            hashtag,
            COUNT(*) as usage_count
        FROM (
            SELECT 
                SUBSTRING_INDEX(SUBSTRING_INDEX(activity_data, '#', -1), ' ', 1) as hashtag
            FROM user_activities 
            WHERE activity_data LIKE '%#%' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ) hashtags
        WHERE LENGTH(hashtag) > 2
        GROUP BY hashtag
        ORDER BY usage_count DESC
        LIMIT 10
    ";
    $stmt = $conn->prepare($trendingQuery);
    $stmt->execute();
    $trendingHashtags = $stmt->fetchAll();
    
    // Get online friends (active in last 15 minutes)
    $onlineFriendsQuery = "
        SELECT 
            p.id, p.first_name, p.last_name, p.username, p.avatar,
            ua.created_at as last_activity
        FROM profiles p
        JOIN user_follows uf ON p.id = uf.following_id
        LEFT JOIN user_activities ua ON p.id = ua.user_id
        WHERE uf.follower_id = ? 
        AND uf.status = 'accepted'
        AND ua.created_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        GROUP BY p.id
        ORDER BY ua.created_at DESC
        LIMIT 20
    ";
    $stmt = $conn->prepare($onlineFriendsQuery);
    $stmt->execute([$user['id']]);
    $onlineFriends = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Live feed error: " . $e->getMessage());
    $activities = [];
    $trendingHashtags = [];
    $onlineFriends = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Feed -->
        <div class="col-lg-8">
            <!-- Live Feed Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-broadcast-tower me-2 text-danger"></i>Live Feed
                        <span class="badge bg-danger ms-2 pulse">LIVE</span>
                    </h1>
                    <p class="text-muted mb-0">Real-time activities from your beer community</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" id="refreshFeed">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
            
            <!-- Quick Post -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-shrink-0 me-3">
                            <?php if ($userProfile['avatar']): ?>
                                <img src="<?php echo htmlspecialchars($userProfile['avatar']); ?>" 
                                     class="rounded-circle" width="40" height="40" alt="Avatar">
                            <?php else: ?>
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-grow-1">
                            <textarea class="form-control" id="quickPostContent" 
                                      placeholder="What's brewing? Share your beer thoughts... #craftbeer" 
                                      rows="2"></textarea>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" onclick="addEmoji('🍺')">🍺</button>
                                    <button class="btn btn-outline-secondary" onclick="addEmoji('🍻')">🍻</button>
                                    <button class="btn btn-outline-secondary" onclick="addEmoji('🎉')">🎉</button>
                                    <button class="btn btn-outline-secondary" onclick="addEmoji('👍')">👍</button>
                                </div>
                                <button class="btn btn-primary btn-sm" id="postQuickUpdate">
                                    <i class="fas fa-paper-plane me-1"></i>Post
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Live Activity Stream -->
            <div id="activityStream">
                <?php foreach ($activities as $activity): ?>
                    <div class="activity-item card mb-3" data-activity-id="<?php echo $activity['id']; ?>">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <?php if ($activity['avatar']): ?>
                                        <img src="<?php echo htmlspecialchars($activity['avatar']); ?>" 
                                             class="rounded-circle" width="40" height="40" alt="Avatar">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="activity-header mb-2">
                                        <strong><?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?></strong>
                                        <span class="text-muted">@<?php echo htmlspecialchars($activity['username']); ?></span>
                                        <span class="activity-type badge bg-<?php echo getActivityTypeColor($activity['activity_type']); ?> ms-2">
                                            <?php echo getActivityTypeLabel($activity['activity_type']); ?>
                                        </span>
                                        <small class="text-muted ms-2">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo timeAgo($activity['created_at']); ?>
                                        </small>
                                    </div>
                                    
                                    <div class="activity-content mb-3">
                                        <?php echo renderActivityContent($activity); ?>
                                    </div>
                                    
                                    <!-- Enhanced Activity Data -->
                                    <?php if ($activity['enhanced_data']): ?>
                                        <div class="enhanced-content mb-3">
                                            <?php echo renderEnhancedContent($activity['enhanced_data'], $activity['activity_type']); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Activity Actions -->
                                    <div class="activity-actions d-flex justify-content-between align-items-center">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-danger like-btn <?php echo $activity['user_liked'] ? 'active' : ''; ?>" 
                                                    data-activity-id="<?php echo $activity['id']; ?>">
                                                <i class="fas fa-heart me-1"></i>
                                                <span class="like-count"><?php echo $activity['like_count']; ?></span>
                                            </button>
                                            <button class="btn btn-outline-primary comment-btn" 
                                                    data-activity-id="<?php echo $activity['id']; ?>">
                                                <i class="fas fa-comment me-1"></i>
                                                <span class="comment-count"><?php echo $activity['comment_count']; ?></span>
                                            </button>
                                            <button class="btn btn-outline-success share-btn" 
                                                    data-activity-id="<?php echo $activity['id']; ?>">
                                                <i class="fas fa-share me-1"></i>Share
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <?php if ($activity['created_at'] > date('Y-m-d H:i:s', strtotime('-5 minutes'))): ?>
                                                <span class="badge bg-success">NEW</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    
                                    <!-- Comments Section (Initially Hidden) -->
                                    <div class="comments-section mt-3" id="comments-<?php echo $activity['id']; ?>" style="display: none;">
                                        <div class="comments-list"></div>
                                        <div class="comment-form mt-2">
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control comment-input" 
                                                       placeholder="Add a comment..." 
                                                       data-activity-id="<?php echo $activity['id']; ?>">
                                                <button class="btn btn-primary submit-comment" 
                                                        data-activity-id="<?php echo $activity['id']; ?>">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Load More -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" id="loadMoreActivities">
                    <i class="fas fa-chevron-down me-2"></i>Load More Activities
                </button>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Online Friends -->
            <?php if (!empty($onlineFriends)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-circle text-success me-2"></i>Online Now
                            <span class="badge bg-success ms-2"><?php echo count($onlineFriends); ?></span>
                        </h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="row g-2">
                            <?php foreach (array_slice($onlineFriends, 0, 8) as $friend): ?>
                                <div class="col-3">
                                    <div class="text-center">
                                        <div class="position-relative d-inline-block">
                                            <?php if ($friend['avatar']): ?>
                                                <img src="<?php echo htmlspecialchars($friend['avatar']); ?>" 
                                                     class="rounded-circle" width="40" height="40" alt="Avatar">
                                            <?php else: ?>
                                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white small"></i>
                                                </div>
                                            <?php endif; ?>
                                            <span class="position-absolute top-0 start-100 translate-middle p-1 bg-success border border-light rounded-circle">
                                                <span class="visually-hidden">Online</span>
                                            </span>
                                        </div>
                                        <div class="small mt-1"><?php echo htmlspecialchars($friend['first_name']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Trending Hashtags -->
            <?php if (!empty($trendingHashtags)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-hashtag me-2"></i>Trending Now
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($trendingHashtags as $hashtag): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <a href="#" class="text-decoration-none hashtag-link" 
                                   data-hashtag="<?php echo htmlspecialchars($hashtag['hashtag']); ?>">
                                    #<?php echo htmlspecialchars($hashtag['hashtag']); ?>
                                </a>
                                <span class="badge bg-primary"><?php echo $hashtag['usage_count']; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="card mb-4 quick-actions">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="../check-in.php" class="btn btn-primary">
                            <i class="fas fa-map-pin me-2"></i>Check In Beer
                        </a>
                        <a href="events.php" class="btn btn-success">
                            <i class="fas fa-calendar me-2"></i>Find Events
                        </a>
                        <a href="challenges.php" class="btn btn-warning">
                            <i class="fas fa-trophy me-2"></i>Join Challenge
                        </a>
                        <a href="trading.php" class="btn btn-info">
                            <i class="fas fa-exchange-alt me-2"></i>Trade Beers
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Live Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Live Community Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h5 mb-0 text-primary" id="liveCheckins">0</div>
                                <small class="text-muted">Check-ins Today</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h5 mb-0 text-success" id="liveUsers">0</div>
                                <small class="text-muted">Active Users</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Live Update Indicator -->
<div id="liveUpdateIndicator" class="position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 1050; display: none;">
    <div class="alert alert-info alert-dismissible fade show">
        <i class="fas fa-sync-alt fa-spin me-2"></i>
        <span id="updateMessage">Loading new activities...</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<style>
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.activity-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.activity-item.new-activity {
    border-left-color: #28a745;
    background-color: #f8fff9;
}

.hashtag-link:hover {
    color: #0d6efd !important;
}

.like-btn.active {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}
</style>

<?php 
// Helper functions
function getActivityTypeColor($type) {
    $colors = [
        'beer_checkin' => 'primary',
        'beer_rating' => 'warning',
        'user_follow' => 'success',
        'badge_earned' => 'info',
        'event_created' => 'secondary',
        'challenge_joined' => 'danger'
    ];
    return $colors[$type] ?? 'secondary';
}

function getActivityTypeLabel($type) {
    $labels = [
        'beer_checkin' => 'Check-in',
        'beer_rating' => 'Review',
        'user_follow' => 'Follow',
        'badge_earned' => 'Badge',
        'event_created' => 'Event',
        'challenge_joined' => 'Challenge'
    ];
    return $labels[$type] ?? ucfirst(str_replace('_', ' ', $type));
}

function renderActivityContent($activity) {
    return htmlspecialchars($activity['activity_data']);
}

function renderEnhancedContent($enhancedData, $activityType) {
    $data = json_decode($enhancedData, true);
    if (!$data) return '';
    
    switch ($activityType) {
        case 'beer_checkin':
            return "
                <div class='enhanced-checkin p-2 bg-light rounded'>
                    <strong>{$data['beer_name']}</strong> at <strong>{$data['brewery_name']}</strong>
                    " . ($data['rating'] ? "<br>Rating: " . str_repeat('⭐', $data['rating']) : '') . "
                    " . ($data['location'] ? "<br>📍 {$data['location']}" : '') . "
                </div>
            ";
        case 'beer_rating':
            return "
                <div class='enhanced-rating p-2 bg-light rounded'>
                    <strong>{$data['beer_name']}</strong> by <strong>{$data['brewery_name']}</strong>
                    <br>Rating: " . str_repeat('⭐', $data['rating']) . "
                    " . ($data['review'] ? "<br><em>\"{$data['review']}\"</em>" : '') . "
                </div>
            ";
        case 'badge_earned':
            return "
                <div class='enhanced-badge p-2 bg-warning bg-opacity-10 rounded'>
                    <i class='{$data['badge_icon']} fa-2x text-warning me-2'></i>
                    <strong>{$data['badge_name']}</strong>
                    <br><small>{$data['badge_description']}</small>
                </div>
            ";
    }
    
    return '';
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    return date('M j', strtotime($datetime));
}

require_once '../includes/footer.php'; 
?>
