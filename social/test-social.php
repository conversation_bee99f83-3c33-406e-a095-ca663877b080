<?php
/**
 * Test Social Features
 * Simple test page to verify social features work
 */

require_once '../config/config.php';

$pageTitle = 'Test Social Features - ' . APP_NAME;

// Test database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
    $dbStatus = "✅ Database connection successful";
} catch (Exception $e) {
    $dbStatus = "❌ Database connection failed: " . $e->getMessage();
}

// Test if social tables exist
$socialTables = [
    'events',
    'event_rsvps', 
    'event_comments',
    'social_challenges',
    'challenge_participants',
    'beer_clubs',
    'club_memberships',
    'beer_trades',
    'trade_offers'
];

$tableStatus = [];
foreach ($socialTables as $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch() !== false;
        $tableStatus[$table] = $exists ? "✅ Exists" : "❌ Missing";
    } catch (Exception $e) {
        $tableStatus[$table] = "❌ Error: " . $e->getMessage();
    }
}

// Test if user system is working
$userSystemStatus = "❌ Not tested";
if (function_exists('getCurrentUser')) {
    $userSystemStatus = "✅ getCurrentUser function exists";
} else {
    $userSystemStatus = "❌ getCurrentUser function missing";
}

// Test if profiles table exists
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'profiles'");
    $stmt->execute();
    $profilesExist = $stmt->fetch() !== false;
    $profilesStatus = $profilesExist ? "✅ Profiles table exists" : "❌ Profiles table missing";
} catch (Exception $e) {
    $profilesStatus = "❌ Error checking profiles: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .feature-card { transition: transform 0.2s; }
        .feature-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h1 class="display-4">🍺 Beersty Social Features Test</h1>
                    <p class="lead">Testing the social networking platform components</p>
                </div>
                
                <!-- System Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Database:</strong> <?php echo $dbStatus; ?></p>
                                <p><strong>User System:</strong> <?php echo $userSystemStatus; ?></p>
                                <p><strong>Profiles:</strong> <?php echo $profilesStatus; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                                <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                                <p><strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Database Tables Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>Social Database Tables</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($tableStatus as $table => $status): ?>
                                <div class="col-md-6 mb-2">
                                    <strong><?php echo $table; ?>:</strong> <?php echo $status; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Social Features -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt fa-3x text-primary mb-3"></i>
                                <h5>Events & Meetups</h5>
                                <p class="text-muted">Create and join beer events</p>
                                <a href="events.php" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>View Events
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-trophy fa-3x text-warning mb-3"></i>
                                <h5>Challenges</h5>
                                <p class="text-muted">Compete in beer challenges</p>
                                <a href="challenges.php" class="btn btn-warning">
                                    <i class="fas fa-arrow-right me-2"></i>View Challenges
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x text-success mb-3"></i>
                                <h5>Beer Clubs</h5>
                                <p class="text-muted">Join communities of enthusiasts</p>
                                <a href="clubs.php" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>View Clubs
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-exchange-alt fa-3x text-info mb-3"></i>
                                <h5>Beer Trading</h5>
                                <p class="text-muted">Trade and share rare beers</p>
                                <a href="trading.php" class="btn btn-info">
                                    <i class="fas fa-arrow-right me-2"></i>View Trading
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Social Dashboard -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <i class="fas fa-tachometer-alt fa-3x text-dark mb-3"></i>
                        <h5>Social Dashboard</h5>
                        <p class="text-muted">Central hub for all social activities</p>
                        <a href="dashboard.php" class="btn btn-dark btn-lg">
                            <i class="fas fa-rocket me-2"></i>Launch Social Dashboard
                        </a>
                    </div>
                </div>
                
                <!-- Setup Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-wrench me-2"></i>Setup Instructions</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Database Setup:</strong> Run the social database setup script</li>
                            <li><strong>User Account:</strong> Create or login to a user account</li>
                            <li><strong>Profile:</strong> Complete your user profile</li>
                            <li><strong>Explore:</strong> Start using the social features</li>
                        </ol>
                        
                        <div class="mt-3">
                            <h6>Quick Setup Commands:</h6>
                            <code>.\setup-social-features.ps1</code>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Manual Database Setup:</h6>
                            <p>Import the SQL file: <code>database/social_events_system.sql</code></p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="../index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Back to Homepage
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
