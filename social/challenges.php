<?php
/**
 * Social Challenges & Competitions
 * Gamified challenges for beer enthusiasts
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Beer Challenges - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css'];
$additionalJS = ['../assets/js/challenges.js'];

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);
$currentTab = $_GET['tab'] ?? 'active';

try {
    // Get active challenges
    $activeChallengesQuery = "
        SELECT 
            sc.*,
            p.first_name, p.last_name, p.username,
            COUNT(DISTINCT cp.id) as participant_count,
            MAX(CASE WHEN cp.user_id = ? THEN cp.id END) as user_participation_id,
            MAX(CASE WHEN cp.user_id = ? THEN cp.progress END) as user_progress,
            MAX(CASE WHEN cp.user_id = ? THEN cp.completed END) as user_completed
        FROM social_challenges sc
        JOIN profiles p ON sc.creator_id = p.id
        LEFT JOIN challenge_participants cp ON sc.id = cp.challenge_id
        WHERE sc.status = 'active' AND sc.is_public = 1
        GROUP BY sc.id
        ORDER BY sc.start_date ASC
    ";
    $stmt = $conn->prepare($activeChallengesQuery);
    $stmt->execute([$user['id'], $user['id'], $user['id']]);
    $activeChallenges = $stmt->fetchAll();
    
    // Get user's challenges
    $userChallengesQuery = "
        SELECT 
            sc.*,
            cp.progress, cp.completed, cp.completion_date, cp.final_score, cp.rank_position,
            p.first_name, p.last_name, p.username
        FROM social_challenges sc
        JOIN challenge_participants cp ON sc.id = cp.challenge_id
        JOIN profiles p ON sc.creator_id = p.id
        WHERE cp.user_id = ?
        ORDER BY cp.joined_at DESC
    ";
    $stmt = $conn->prepare($userChallengesQuery);
    $stmt->execute([$user['id']]);
    $userChallenges = $stmt->fetchAll();
    
    // Get completed challenges
    $completedChallengesQuery = "
        SELECT 
            sc.*,
            p.first_name, p.last_name, p.username,
            COUNT(DISTINCT cp.id) as participant_count
        FROM social_challenges sc
        JOIN profiles p ON sc.creator_id = p.id
        LEFT JOIN challenge_participants cp ON sc.id = cp.challenge_id
        WHERE sc.status = 'completed' AND sc.is_public = 1
        GROUP BY sc.id
        ORDER BY sc.end_date DESC
        LIMIT 10
    ";
    $stmt = $conn->prepare($completedChallengesQuery);
    $stmt->execute();
    $completedChallenges = $stmt->fetchAll();
    
    // Get user's challenge statistics
    $userStatsQuery = "
        SELECT 
            COUNT(*) as total_joined,
            COUNT(CASE WHEN completed = 1 THEN 1 END) as total_completed,
            AVG(final_score) as avg_score,
            COUNT(CASE WHEN rank_position = 1 THEN 1 END) as first_places
        FROM challenge_participants 
        WHERE user_id = ?
    ";
    $stmt = $conn->prepare($userStatsQuery);
    $stmt->execute([$user['id']]);
    $userStats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Challenges page error: " . $e->getMessage());
    $activeChallenges = [];
    $userChallenges = [];
    $completedChallenges = [];
    $userStats = ['total_joined' => 0, 'total_completed' => 0, 'avg_score' => 0, 'first_places' => 0];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-trophy me-2"></i>Beer Challenges
                    </h1>
                    <p class="text-muted mb-0">Join challenges and compete with fellow beer enthusiasts</p>
                </div>
                <div>
                    <a href="create-challenge.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Challenge
                    </a>
                </div>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'active' ? 'active' : ''; ?>" 
                       href="?tab=active">
                        <i class="fas fa-play me-2"></i>Active Challenges
                        <span class="badge bg-primary ms-2"><?php echo count($activeChallenges); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'my' ? 'active' : ''; ?>" 
                       href="?tab=my">
                        <i class="fas fa-user me-2"></i>My Challenges
                        <span class="badge bg-success ms-2"><?php echo count($userChallenges); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'completed' ? 'active' : ''; ?>" 
                       href="?tab=completed">
                        <i class="fas fa-check me-2"></i>Completed
                    </a>
                </li>
            </ul>
            
            <!-- Active Challenges Tab -->
            <?php if ($currentTab === 'active'): ?>
                <div class="row">
                    <?php foreach ($activeChallenges as $challenge): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card challenge-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getChallengeTypeColor($challenge['challenge_type']); ?>">
                                        <?php echo ucwords(str_replace('_', ' ', $challenge['challenge_type'])); ?>
                                    </span>
                                    <small class="text-muted">
                                        <?php echo $challenge['participant_count']; ?> participants
                                    </small>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($challenge['title']); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($challenge['description'], 0, 100)); ?>
                                        <?php if (strlen($challenge['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                    
                                    <div class="challenge-dates mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('M j', strtotime($challenge['start_date'])); ?> - 
                                            <?php echo date('M j, Y', strtotime($challenge['end_date'])); ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($challenge['prize_description']): ?>
                                        <div class="prize-info mb-3">
                                            <small class="text-success">
                                                <i class="fas fa-gift me-1"></i>
                                                <?php echo htmlspecialchars($challenge['prize_description']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Progress for joined challenges -->
                                    <?php if ($challenge['user_participation_id']): ?>
                                        <div class="progress mb-3">
                                            <?php 
                                            $progress = json_decode($challenge['user_progress'], true);
                                            $rules = json_decode($challenge['rules'], true);
                                            $progressPercent = calculateChallengeProgress($progress, $rules);
                                            ?>
                                            <div class="progress-bar" style="width: <?php echo $progressPercent; ?>%">
                                                <?php echo $progressPercent; ?>%
                                            </div>
                                        </div>
                                        
                                        <?php if ($challenge['user_completed']): ?>
                                            <div class="alert alert-success py-2">
                                                <i class="fas fa-check-circle me-2"></i>Completed!
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    
                                    <div class="mt-auto">
                                        <?php if ($challenge['user_participation_id']): ?>
                                            <a href="challenge-detail.php?id=<?php echo $challenge['id']; ?>" 
                                               class="btn btn-outline-primary w-100">
                                                View Progress
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-success w-100" 
                                                    onclick="joinChallenge('<?php echo $challenge['id']; ?>')">
                                                <i class="fas fa-play me-2"></i>Join Challenge
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($activeChallenges)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h4>No Active Challenges</h4>
                        <p class="text-muted">Be the first to create a challenge!</p>
                        <a href="create-challenge.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Challenge
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- My Challenges Tab -->
            <?php if ($currentTab === 'my'): ?>
                <div class="row">
                    <?php foreach ($userChallenges as $challenge): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card challenge-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getChallengeStatusColor($challenge['status']); ?>">
                                        <?php echo ucfirst($challenge['status']); ?>
                                    </span>
                                    <?php if ($challenge['completed']): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-trophy me-1"></i>Completed
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($challenge['title']); ?></h5>
                                    <p class="card-text">
                                        <?php echo htmlspecialchars(substr($challenge['description'], 0, 80)); ?>...
                                    </p>
                                    
                                    <?php if ($challenge['completed']): ?>
                                        <div class="completion-info mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>Final Score:</span>
                                                <strong><?php echo $challenge['final_score']; ?></strong>
                                            </div>
                                            <?php if ($challenge['rank_position']): ?>
                                                <div class="d-flex justify-content-between">
                                                    <span>Rank:</span>
                                                    <strong>#<?php echo $challenge['rank_position']; ?></strong>
                                                </div>
                                            <?php endif; ?>
                                            <small class="text-muted">
                                                Completed: <?php echo date('M j, Y', strtotime($challenge['completion_date'])); ?>
                                            </small>
                                        </div>
                                    <?php else: ?>
                                        <div class="progress mb-3">
                                            <?php 
                                            $progress = json_decode($challenge['progress'], true);
                                            $rules = json_decode($challenge['rules'], true);
                                            $progressPercent = calculateChallengeProgress($progress, $rules);
                                            ?>
                                            <div class="progress-bar" style="width: <?php echo $progressPercent; ?>%">
                                                <?php echo $progressPercent; ?>%
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-auto">
                                        <a href="challenge-detail.php?id=<?php echo $challenge['id']; ?>" 
                                           class="btn btn-outline-primary w-100">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($userChallenges)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                        <h4>No Challenges Joined</h4>
                        <p class="text-muted">Join some challenges to start competing!</p>
                        <a href="?tab=active" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Challenges
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- Completed Challenges Tab -->
            <?php if ($currentTab === 'completed'): ?>
                <div class="row">
                    <?php foreach ($completedChallenges as $challenge): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card challenge-card h-100">
                                <div class="card-header">
                                    <span class="badge bg-secondary">Completed</span>
                                    <small class="text-muted ms-2">
                                        <?php echo $challenge['participant_count']; ?> participants
                                    </small>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($challenge['title']); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($challenge['description'], 0, 100)); ?>...
                                    </p>
                                    
                                    <div class="challenge-dates mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Ended: <?php echo date('M j, Y', strtotime($challenge['end_date'])); ?>
                                        </small>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="challenge-detail.php?id=<?php echo $challenge['id']; ?>" 
                                           class="btn btn-outline-secondary w-100">
                                            View Results
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- User Challenge Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Your Challenge Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-primary"><?php echo $userStats['total_joined']; ?></div>
                                <small class="text-muted">Joined</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-success"><?php echo $userStats['total_completed']; ?></div>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-warning"><?php echo $userStats['first_places']; ?></div>
                                <small class="text-muted">1st Places</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-info"><?php echo number_format($userStats['avg_score'], 1); ?></div>
                                <small class="text-muted">Avg Score</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Challenge Types -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Challenge Types
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between">
                            <span><i class="fas fa-beer me-2"></i>Beer Styles</span>
                            <span class="badge bg-primary rounded-pill">5</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span><i class="fas fa-building me-2"></i>Brewery Visits</span>
                            <span class="badge bg-primary rounded-pill">3</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span><i class="fas fa-map-pin me-2"></i>Check-ins</span>
                            <span class="badge bg-primary rounded-pill">4</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span><i class="fas fa-star me-2"></i>Reviews</span>
                            <span class="badge bg-primary rounded-pill">2</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between">
                            <span><i class="fas fa-users me-2"></i>Social</span>
                            <span class="badge bg-primary rounded-pill">6</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="create-challenge.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Challenge
                        </a>
                        <a href="leaderboard.php" class="btn btn-outline-warning">
                            <i class="fas fa-crown me-2"></i>Leaderboard
                        </a>
                        <a href="events.php" class="btn btn-outline-info">
                            <i class="fas fa-calendar me-2"></i>Events
                        </a>
                        <a href="clubs.php" class="btn btn-outline-success">
                            <i class="fas fa-users me-2"></i>Beer Clubs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function joinChallenge(challengeId) {
    fetch('/api/challenge-participation.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            challenge_id: challengeId,
            action: 'join'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error joining challenge: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error joining challenge');
    });
}
</script>

<?php 
// Helper functions
function getChallengeTypeColor($type) {
    $colors = [
        'beer_styles' => 'primary',
        'brewery_visits' => 'success',
        'check_ins' => 'info',
        'reviews' => 'warning',
        'social' => 'secondary',
        'seasonal' => 'danger',
        'custom' => 'dark'
    ];
    return $colors[$type] ?? 'primary';
}

function getChallengeStatusColor($status) {
    $colors = [
        'upcoming' => 'secondary',
        'active' => 'success',
        'completed' => 'primary',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

function calculateChallengeProgress($progress, $rules) {
    if (!$progress || !$rules) return 0;
    
    $target = $rules['target_count'] ?? 100;
    $current = $progress['current_count'] ?? 0;
    
    return min(100, ($current / $target) * 100);
}

require_once '../includes/footer.php'; 
?>
