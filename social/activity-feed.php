<?php
/**
 * Enhanced Social Activity Feed - Fun User Frontend
 * Beersty Platform
 */

require_once '../config/config.php';

$pageTitle = 'Activity Feed - ' . APP_NAME;
$additionalCSS = ['/assets/css/social.css', '/assets/css/social-dark.css'];

// Redirect if not logged in
if (!isLoggedIn()) {
    header('Location: /account/login/');
    exit;
}

$user = getCurrentUser();

// Get recent activities
$activities = [];
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get recent check-ins and activities
    $stmt = $conn->prepare("
        SELECT 
            'checkin' as activity_type,
            bc.id as activity_id,
            bc.created_at,
            u.id as user_id,
            p.first_name,
            p.last_name,
            p.username,
            bm.name as beer_name,
            b.name as brewery_name,
            bc.checkin_location,
            br.overall_rating,
            br.review_text,
            bc.checkin_comment
        FROM beer_checkins bc
        JOIN users u ON bc.user_id = u.id
        LEFT JOIN profiles p ON u.id = p.user_id
        LEFT JOIN beer_menu bm ON bc.beer_id = bm.id
        LEFT JOIN breweries b ON bc.brewery_id = b.id
        LEFT JOIN beer_ratings br ON bc.rating_id = br.id
        WHERE bc.is_public = 1
        ORDER BY bc.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $activities = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Error fetching activities: " . $e->getMessage());
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Feed -->
        <div class="col-lg-8">
            <!-- Feed Header -->
            <div class="feed-header mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">
                        <i class="fas fa-stream me-2 text-beer-gold"></i>
                        Activity Feed
                    </h2>
                    <div class="feed-controls">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="feedFilter" id="allActivity" checked>
                            <label class="btn btn-outline-primary" for="allActivity">All</label>
                            
                            <input type="radio" class="btn-check" name="feedFilter" id="friendsActivity">
                            <label class="btn btn-outline-primary" for="friendsActivity">Friends</label>
                            
                            <input type="radio" class="btn-check" name="feedFilter" id="nearbyActivity">
                            <label class="btn btn-outline-primary" for="nearbyActivity">Nearby</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Check-in Prompt -->
            <div class="card quick-checkin-card quick-actions mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <a href="/social/checkin/" class="btn btn-outline-primary btn-lg w-100 text-start">
                                <i class="fas fa-check-circle me-2"></i>
                                What are you drinking, <?php echo htmlspecialchars($user['first_name'] ?: 'Beer Lover'); ?>?
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Feed -->
            <div class="activity-feed">
                <?php if (empty($activities)): ?>
                    <div class="card text-center py-5">
                        <div class="card-body">
                            <i class="fas fa-beer fa-4x text-muted mb-3"></i>
                            <h4>No activity yet!</h4>
                            <p class="text-muted mb-4">Be the first to check in and start the conversation.</p>
                            <a href="/social/checkin/" class="btn btn-primary btn-lg">
                                <i class="fas fa-check-circle me-2"></i>Make Your First Check-in
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($activities as $activity): ?>
                        <div class="card activity-card mb-3">
                            <div class="card-body">
                                <!-- Activity Header -->
                                <div class="activity-header d-flex align-items-center mb-3">
                                    <div class="user-avatar me-3">
                                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                                             style="width: 45px; height: 45px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="user-name fw-bold">
                                            <?php 
                                            $displayName = trim($activity['first_name'] . ' ' . $activity['last_name']);
                                            if (empty($displayName)) {
                                                $displayName = $activity['username'] ?: 'Beer Enthusiast';
                                            }
                                            echo htmlspecialchars($displayName);
                                            ?>
                                        </div>
                                        <div class="activity-meta text-muted small">
                                            is drinking a <strong><?php echo htmlspecialchars($activity['beer_name']); ?></strong>
                                            <?php if ($activity['brewery_name']): ?>
                                                by <?php echo htmlspecialchars($activity['brewery_name']); ?>
                                            <?php endif; ?>
                                            <?php if ($activity['checkin_location']): ?>
                                                at <?php echo htmlspecialchars($activity['checkin_location']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="activity-time text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo formatDateTime($activity['created_at']); ?>
                                        </div>
                                    </div>
                                    <?php if ($activity['overall_rating']): ?>
                                        <div class="activity-rating">
                                            <div class="rating-display">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <span class="star <?php echo $i <= $activity['overall_rating'] ? 'filled' : ''; ?>">⭐</span>
                                                <?php endfor; ?>
                                            </div>
                                            <div class="rating-value text-center">
                                                <?php echo number_format($activity['overall_rating'], 1); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Activity Content -->
                                <?php if ($activity['review_text'] || $activity['checkin_comment']): ?>
                                    <div class="activity-content mb-3">
                                        <?php if ($activity['review_text']): ?>
                                            <p class="review-text mb-2"><?php echo nl2br(htmlspecialchars($activity['review_text'])); ?></p>
                                        <?php endif; ?>
                                        <?php if ($activity['checkin_comment']): ?>
                                            <p class="checkin-comment text-muted"><?php echo nl2br(htmlspecialchars($activity['checkin_comment'])); ?></p>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Social Actions -->
                                <div class="social-actions">
                                    <button class="social-btn like-btn" data-activity-id="<?php echo $activity['activity_id']; ?>">
                                        <i class="fas fa-heart me-1"></i>
                                        <span class="like-count">0</span> Likes
                                    </button>
                                    <button class="social-btn comment-btn" data-activity-id="<?php echo $activity['activity_id']; ?>">
                                        <i class="fas fa-comment me-1"></i>
                                        <span class="comment-count">0</span> Comments
                                    </button>
                                    <button class="social-btn share-btn" data-activity-id="<?php echo $activity['activity_id']; ?>">
                                        <i class="fas fa-share me-1"></i>
                                        Share
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Load More -->
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn">
                    <i class="fas fa-plus me-2"></i>Load More Activities
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- User Stats Card -->
            <div class="card user-stats-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-success"></i>
                        Your Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="user-stats">
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Check-ins</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Reviews</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Photos</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Badges</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="/profile/edit/" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-user me-2"></i>View Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Trending Beers -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2 text-danger"></i>
                        Trending Beers
                    </h5>
                </div>
                <div class="card-body">
                    <div class="trending-beer mb-3">
                        <div class="d-flex align-items-center">
                            <div class="beer-icon me-3">🍺</div>
                            <div class="flex-grow-1">
                                <div class="beer-name fw-bold">Hazy IPA</div>
                                <div class="beer-brewery text-muted small">Local Brewery</div>
                                <div class="beer-rating">⭐⭐⭐⭐⭐ 4.8</div>
                            </div>
                        </div>
                    </div>
                    <div class="trending-beer mb-3">
                        <div class="d-flex align-items-center">
                            <div class="beer-icon me-3">🍻</div>
                            <div class="flex-grow-1">
                                <div class="beer-name fw-bold">Imperial Stout</div>
                                <div class="beer-brewery text-muted small">Craft Brewery</div>
                                <div class="beer-rating">⭐⭐⭐⭐ 4.5</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="/beers/trending/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-fire me-2"></i>View All Trending
                        </a>
                    </div>
                </div>
            </div>

            <!-- Nearby Breweries -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2 text-info"></i>
                        Nearby Breweries
                    </h5>
                </div>
                <div class="card-body">
                    <div class="nearby-brewery mb-3">
                        <div class="d-flex align-items-center">
                            <div class="brewery-icon me-3">🏭</div>
                            <div class="flex-grow-1">
                                <div class="brewery-name fw-bold">Local Craft Brewery</div>
                                <div class="brewery-distance text-muted small">0.5 miles away</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="/breweries/discover/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-compass me-2"></i>Explore Breweries
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/js/activity-feed.js"></script>

<?php require_once '../includes/footer.php'; ?>
