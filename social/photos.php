<?php
/**
 * Photo Gallery & Sharing System
 * Advanced photo management for beer experiences
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Photo Gallery - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css', '../assets/css/photo-gallery.css'];
$additionalJS = ['../assets/js/photo-gallery.js'];

$user = getCurrentUser();
$currentTab = $_GET['tab'] ?? 'my_photos';
$albumId = $_GET['album'] ?? null;

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.* 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get user's photo albums
    $albumsQuery = "
        SELECT 
            pa.*,
            COUNT(p.id) as photo_count,
            MAX(p.created_at) as last_updated
        FROM photo_albums pa
        LEFT JOIN photos p ON pa.id = p.album_id
        WHERE pa.user_id = ?
        GROUP BY pa.id
        ORDER BY pa.created_at DESC
    ";
    $stmt = $conn->prepare($albumsQuery);
    $stmt->execute([$user['id']]);
    $userAlbums = $stmt->fetchAll();
    
    // Get photos based on current view
    if ($albumId) {
        // Photos from specific album
        $photosQuery = "
            SELECT 
                p.*,
                pa.name as album_name,
                bi.name as beer_name,
                br.name as brewery_name,
                bc.rating as checkin_rating
            FROM photos p
            LEFT JOIN photo_albums pa ON p.album_id = pa.id
            LEFT JOIN beer_checkins bc ON p.checkin_id = bc.id
            LEFT JOIN beer_items bi ON bc.beer_id = bi.id
            LEFT JOIN breweries br ON bc.brewery_id = br.id
            WHERE p.album_id = ? AND pa.user_id = ?
            ORDER BY p.created_at DESC
        ";
        $stmt = $conn->prepare($photosQuery);
        $stmt->execute([$albumId, $user['id']]);
        $photos = $stmt->fetchAll();
    } elseif ($currentTab === 'my_photos') {
        // All user's photos
        $photosQuery = "
            SELECT 
                p.*,
                pa.name as album_name,
                bi.name as beer_name,
                br.name as brewery_name,
                bc.rating as checkin_rating,
                COUNT(DISTINCT pl.id) as like_count,
                COUNT(DISTINCT pc.id) as comment_count
            FROM photos p
            LEFT JOIN photo_albums pa ON p.album_id = pa.id
            LEFT JOIN beer_checkins bc ON p.checkin_id = bc.id
            LEFT JOIN beer_items bi ON bc.beer_id = bi.id
            LEFT JOIN breweries br ON bc.brewery_id = br.id
            LEFT JOIN photo_likes pl ON p.id = pl.photo_id
            LEFT JOIN photo_comments pc ON p.id = pc.photo_id
            WHERE p.user_id = ?
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT 50
        ";
        $stmt = $conn->prepare($photosQuery);
        $stmt->execute([$user['id']]);
        $photos = $stmt->fetchAll();
    } else {
        // Community photos (friends and public)
        $photosQuery = "
            SELECT 
                p.*,
                pa.name as album_name,
                bi.name as beer_name,
                br.name as brewery_name,
                bc.rating as checkin_rating,
                prof.first_name, prof.last_name, prof.username, prof.avatar,
                COUNT(DISTINCT pl.id) as like_count,
                COUNT(DISTINCT pc.id) as comment_count,
                MAX(CASE WHEN pl.user_id = ? THEN 1 ELSE 0 END) as user_liked
            FROM photos p
            LEFT JOIN photo_albums pa ON p.album_id = pa.id
            LEFT JOIN beer_checkins bc ON p.checkin_id = bc.id
            LEFT JOIN beer_items bi ON bc.beer_id = bi.id
            LEFT JOIN breweries br ON bc.brewery_id = br.id
            LEFT JOIN profiles prof ON p.user_id = prof.id
            LEFT JOIN photo_likes pl ON p.id = pl.photo_id
            LEFT JOIN photo_comments pc ON p.id = pc.photo_id
            WHERE (
                p.visibility = 'public' 
                OR p.user_id IN (
                    SELECT following_id FROM user_follows 
                    WHERE follower_id = ? AND status = 'accepted'
                )
                OR p.user_id = ?
            )
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT 50
        ";
        $stmt = $conn->prepare($photosQuery);
        $stmt->execute([$user['id'], $user['id'], $user['id']]);
        $photos = $stmt->fetchAll();
    }
    
    // Get photo statistics
    $statsQuery = "
        SELECT 
            COUNT(DISTINCT p.id) as total_photos,
            COUNT(DISTINCT pa.id) as total_albums,
            COUNT(DISTINCT pl.id) as total_likes,
            COUNT(DISTINCT pc.id) as total_comments
        FROM photos p
        LEFT JOIN photo_albums pa ON p.album_id = pa.id AND pa.user_id = ?
        LEFT JOIN photo_likes pl ON p.id = pl.photo_id
        LEFT JOIN photo_comments pc ON p.id = pc.photo_id
        WHERE p.user_id = ?
    ";
    $stmt = $conn->prepare($statsQuery);
    $stmt->execute([$user['id'], $user['id']]);
    $photoStats = $stmt->fetch();
    
    // Get trending photos
    $trendingQuery = "
        SELECT 
            p.*,
            prof.first_name, prof.last_name, prof.username, prof.avatar,
            bi.name as beer_name,
            br.name as brewery_name,
            COUNT(DISTINCT pl.id) as like_count,
            COUNT(DISTINCT pc.id) as comment_count
        FROM photos p
        LEFT JOIN profiles prof ON p.user_id = prof.id
        LEFT JOIN beer_checkins bc ON p.checkin_id = bc.id
        LEFT JOIN beer_items bi ON bc.beer_id = bi.id
        LEFT JOIN breweries br ON bc.brewery_id = br.id
        LEFT JOIN photo_likes pl ON p.id = pl.photo_id AND pl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        LEFT JOIN photo_comments pc ON p.id = pc.photo_id
        WHERE p.visibility = 'public'
        AND p.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY p.id
        HAVING like_count > 0
        ORDER BY like_count DESC, comment_count DESC
        LIMIT 20
    ";
    $stmt = $conn->prepare($trendingQuery);
    $stmt->execute();
    $trendingPhotos = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Photo gallery error: " . $e->getMessage());
    $userAlbums = [];
    $photos = [];
    $photoStats = ['total_photos' => 0, 'total_albums' => 0, 'total_likes' => 0, 'total_comments' => 0];
    $trendingPhotos = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-camera me-2 text-primary"></i>Photo Gallery
                    </h1>
                    <p class="text-muted mb-0">Share and discover amazing beer photography</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-plus me-2"></i>Upload Photos
                    </button>
                </div>
            </div>
            
            <!-- Photo Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-images text-primary"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $photoStats['total_photos']; ?></div>
                            <div class="stat-label">Photos</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-folder text-success"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $photoStats['total_albums']; ?></div>
                            <div class="stat-label">Albums</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-heart text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $photoStats['total_likes']; ?></div>
                            <div class="stat-label">Likes</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-comments text-info"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $photoStats['total_comments']; ?></div>
                            <div class="stat-label">Comments</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'my_photos' ? 'active' : ''; ?>" 
                       href="?tab=my_photos">
                        <i class="fas fa-user me-2"></i>My Photos
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'community' ? 'active' : ''; ?>" 
                       href="?tab=community">
                        <i class="fas fa-users me-2"></i>Community
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'trending' ? 'active' : ''; ?>" 
                       href="?tab=trending">
                        <i class="fas fa-fire me-2"></i>Trending
                    </a>
                </li>
            </ul>
            
            <!-- Album Navigation (for My Photos) -->
            <?php if ($currentTab === 'my_photos' && !empty($userAlbums)): ?>
                <div class="album-navigation mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <h5 class="mb-0 me-3">Albums:</h5>
                        <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createAlbumModal">
                            <i class="fas fa-plus me-1"></i>Create Album
                        </button>
                    </div>
                    <div class="album-list">
                        <a href="?tab=my_photos" class="album-item <?php echo !$albumId ? 'active' : ''; ?>">
                            <i class="fas fa-images me-2"></i>All Photos
                        </a>
                        <?php foreach ($userAlbums as $album): ?>
                            <a href="?tab=my_photos&album=<?php echo $album['id']; ?>" 
                               class="album-item <?php echo $albumId == $album['id'] ? 'active' : ''; ?>">
                                <i class="fas fa-folder me-2"></i>
                                <?php echo htmlspecialchars($album['name']); ?>
                                <span class="badge bg-secondary ms-2"><?php echo $album['photo_count']; ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Photo Grid -->
            <div class="photo-grid">
                <?php if ($currentTab === 'trending'): ?>
                    <!-- Trending Photos -->
                    <div class="row">
                        <?php foreach ($trendingPhotos as $photo): ?>
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="photo-card trending-photo" data-photo-id="<?php echo $photo['id']; ?>">
                                    <div class="photo-image">
                                        <img src="<?php echo htmlspecialchars($photo['file_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($photo['caption'] ?? 'Beer photo'); ?>"
                                             class="img-fluid" onclick="openPhotoModal('<?php echo $photo['id']; ?>')">
                                        <div class="photo-overlay">
                                            <div class="photo-stats">
                                                <span class="stat-item">
                                                    <i class="fas fa-heart"></i> <?php echo $photo['like_count']; ?>
                                                </span>
                                                <span class="stat-item">
                                                    <i class="fas fa-comment"></i> <?php echo $photo['comment_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="photo-info">
                                        <div class="photo-user">
                                            <div class="d-flex align-items-center">
                                                <?php if ($photo['avatar']): ?>
                                                    <img src="<?php echo htmlspecialchars($photo['avatar']); ?>" 
                                                         class="rounded-circle me-2" width="30" height="30">
                                                <?php else: ?>
                                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                         style="width: 30px; height: 30px;">
                                                        <i class="fas fa-user text-white small"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($photo['first_name'] . ' ' . $photo['last_name']); ?></strong>
                                                    <div class="small text-muted">@<?php echo htmlspecialchars($photo['username']); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if ($photo['beer_name']): ?>
                                            <div class="photo-beer mt-2">
                                                <small class="text-muted">
                                                    <strong><?php echo htmlspecialchars($photo['beer_name']); ?></strong>
                                                    <?php if ($photo['brewery_name']): ?>
                                                        by <?php echo htmlspecialchars($photo['brewery_name']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($photo['caption']): ?>
                                            <div class="photo-caption mt-2">
                                                <small><?php echo htmlspecialchars($photo['caption']); ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <!-- Regular Photo Grid -->
                    <div class="masonry-grid">
                        <?php foreach ($photos as $photo): ?>
                            <div class="photo-item" data-photo-id="<?php echo $photo['id']; ?>">
                                <div class="photo-card">
                                    <div class="photo-image">
                                        <img src="<?php echo htmlspecialchars($photo['file_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($photo['caption'] ?? 'Beer photo'); ?>"
                                             class="img-fluid" onclick="openPhotoModal('<?php echo $photo['id']; ?>')">
                                        <div class="photo-overlay">
                                            <div class="photo-actions">
                                                <?php if ($photo['user_id'] === $user['id']): ?>
                                                    <button class="btn btn-sm btn-outline-light" onclick="editPhoto('<?php echo $photo['id']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deletePhoto('<?php echo $photo['id']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-outline-light like-btn <?php echo ($photo['user_liked'] ?? 0) ? 'active' : ''; ?>" 
                                                            onclick="togglePhotoLike('<?php echo $photo['id']; ?>')">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                            <div class="photo-stats">
                                                <span class="stat-item">
                                                    <i class="fas fa-heart"></i> <?php echo $photo['like_count'] ?? 0; ?>
                                                </span>
                                                <span class="stat-item">
                                                    <i class="fas fa-comment"></i> <?php echo $photo['comment_count'] ?? 0; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="photo-info">
                                        <?php if ($currentTab === 'community' && isset($photo['first_name'])): ?>
                                            <div class="photo-user mb-2">
                                                <small class="text-muted">
                                                    by <strong><?php echo htmlspecialchars($photo['first_name'] . ' ' . $photo['last_name']); ?></strong>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($photo['beer_name']): ?>
                                            <div class="photo-beer mb-2">
                                                <small class="text-primary">
                                                    <i class="fas fa-beer me-1"></i>
                                                    <strong><?php echo htmlspecialchars($photo['beer_name']); ?></strong>
                                                    <?php if ($photo['brewery_name']): ?>
                                                        <br><span class="text-muted">by <?php echo htmlspecialchars($photo['brewery_name']); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($photo['album_name']): ?>
                                            <div class="photo-album mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-folder me-1"></i>
                                                    <?php echo htmlspecialchars($photo['album_name']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($photo['caption']): ?>
                                            <div class="photo-caption">
                                                <small><?php echo htmlspecialchars($photo['caption']); ?></small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="photo-meta mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo timeAgo($photo['created_at']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($photos)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h4>No Photos Found</h4>
                        <p class="text-muted">
                            <?php if ($currentTab === 'my_photos'): ?>
                                Start building your beer photo collection!
                            <?php else: ?>
                                Follow some friends to see their photos here.
                            <?php endif; ?>
                        </p>
                        <?php if ($currentTab === 'my_photos'): ?>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                <i class="fas fa-plus me-2"></i>Upload Your First Photo
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Photo Tools -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Photo Tools
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-2"></i>Upload Photos
                        </button>
                        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#createAlbumModal">
                            <i class="fas fa-folder-plus me-2"></i>Create Album
                        </button>
                        <button class="btn btn-outline-info" onclick="bulkActions()">
                            <i class="fas fa-tasks me-2"></i>Bulk Actions
                        </button>
                        <a href="../check-in.php" class="btn btn-outline-warning">
                            <i class="fas fa-camera me-2"></i>Photo Check-in
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Albums -->
            <?php if (!empty($userAlbums)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-folder me-2"></i>Your Albums
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <?php foreach (array_slice($userAlbums, 0, 5) as $album): ?>
                            <div class="album-preview">
                                <a href="?tab=my_photos&album=<?php echo $album['id']; ?>"
                                   class="d-flex align-items-center p-3 text-decoration-none">
                                    <div class="album-icon me-3">
                                        <i class="fas fa-folder fa-2x text-primary"></i>
                                    </div>
                                    <div class="album-details">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($album['name']); ?></h6>
                                        <small class="text-muted">
                                            <?php echo $album['photo_count']; ?> photos
                                            <?php if ($album['last_updated']): ?>
                                                • Updated <?php echo timeAgo($album['last_updated']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Photo Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Photography Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="tip-item mb-3">
                        <h6 class="small">📸 Perfect Beer Shot</h6>
                        <p class="small text-muted">Use natural light and show the beer's color and foam</p>
                    </div>
                    <div class="tip-item mb-3">
                        <h6 class="small">🍺 Include Context</h6>
                        <p class="small text-muted">Show the brewery, food pairing, or setting</p>
                    </div>
                    <div class="tip-item mb-3">
                        <h6 class="small">📱 Mobile Tips</h6>
                        <p class="small text-muted">Clean your lens and use the rule of thirds</p>
                    </div>
                    <div class="tip-item">
                        <h6 class="small">🏷️ Tag Your Photos</h6>
                        <p class="small text-muted">Add beer names and locations for better discovery</p>
                    </div>
                </div>
            </div>

            <!-- Community Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Community Photos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-2">
                        <div class="d-flex justify-content-between">
                            <span>Photos Today</span>
                            <strong>47</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-2">
                        <div class="d-flex justify-content-between">
                            <span>Most Liked</span>
                            <strong>IPA Photos</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-2">
                        <div class="d-flex justify-content-between">
                            <span>Trending Tag</span>
                            <strong>#craftbeer</strong>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="d-flex justify-content-between">
                            <span>Your Rank</span>
                            <strong class="text-success">#12</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    return date('M j', strtotime($datetime));
}

require_once '../includes/footer.php';
?>
