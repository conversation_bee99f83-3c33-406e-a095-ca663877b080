<?php
/**
 * Beer Trading & Sharing System
 * Platform for beer enthusiasts to trade, share, and exchange beers
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Beer Trading - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css'];
$additionalJS = ['../assets/js/trading.js'];

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);
$currentTab = $_GET['tab'] ?? 'browse';

// Get filter parameters
$tradeType = $_GET['type'] ?? '';
$location = $_GET['location'] ?? '';
$searchQuery = $_GET['search'] ?? '';

try {
    // Build WHERE clause for filters
    $whereConditions = ["bt.status = 'open'"];
    $params = [];
    
    if (!empty($tradeType)) {
        $whereConditions[] = "bt.trade_type = ?";
        $params[] = $tradeType;
    }
    
    if (!empty($location)) {
        $whereConditions[] = "bt.location LIKE ?";
        $params[] = "%$location%";
    }
    
    if (!empty($searchQuery)) {
        $whereConditions[] = "(bt.title LIKE ? OR bt.description LIKE ?)";
        $params[] = "%$searchQuery%";
        $params[] = "%$searchQuery%";
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get all active trades
    $tradesQuery = "
        SELECT 
            bt.*,
            p.first_name, p.last_name, p.username, p.avatar,
            COUNT(DISTINCT to1.id) as offer_count
        FROM beer_trades bt
        JOIN profiles p ON bt.initiator_id = p.id
        LEFT JOIN trade_offers to1 ON bt.id = to1.trade_id
        WHERE $whereClause
        GROUP BY bt.id
        ORDER BY bt.created_at DESC
    ";
    $stmt = $conn->prepare($tradesQuery);
    $stmt->execute($params);
    $allTrades = $stmt->fetchAll();
    
    // Get user's trades
    $userTradesQuery = "
        SELECT 
            bt.*,
            COUNT(DISTINCT to1.id) as offer_count,
            COUNT(DISTINCT CASE WHEN to1.status = 'pending' THEN to1.id END) as pending_offers
        FROM beer_trades bt
        LEFT JOIN trade_offers to1 ON bt.id = to1.trade_id
        WHERE bt.initiator_id = ?
        GROUP BY bt.id
        ORDER BY bt.created_at DESC
    ";
    $stmt = $conn->prepare($userTradesQuery);
    $stmt->execute([$user['id']]);
    $userTrades = $stmt->fetchAll();
    
    // Get user's offers
    $userOffersQuery = "
        SELECT 
            to1.*,
            bt.title as trade_title,
            bt.trade_type,
            p.first_name, p.last_name, p.username
        FROM trade_offers to1
        JOIN beer_trades bt ON to1.trade_id = bt.id
        JOIN profiles p ON bt.initiator_id = p.id
        WHERE to1.offerer_id = ?
        ORDER BY to1.created_at DESC
    ";
    $stmt = $conn->prepare($userOffersQuery);
    $stmt->execute([$user['id']]);
    $userOffers = $stmt->fetchAll();
    
    // Get user's trading statistics
    $userStatsQuery = "
        SELECT 
            COUNT(CASE WHEN bt.status = 'completed' AND bt.initiator_id = ? THEN 1 END) as completed_trades,
            COUNT(CASE WHEN bt.status = 'open' AND bt.initiator_id = ? THEN 1 END) as active_trades,
            COUNT(CASE WHEN to1.status = 'accepted' AND to1.offerer_id = ? THEN 1 END) as successful_offers,
            COUNT(CASE WHEN to1.status = 'pending' AND to1.offerer_id = ? THEN 1 END) as pending_offers
        FROM beer_trades bt
        LEFT JOIN trade_offers to1 ON bt.id = to1.trade_id
        WHERE bt.initiator_id = ? OR to1.offerer_id = ?
    ";
    $stmt = $conn->prepare($userStatsQuery);
    $stmt->execute([$user['id'], $user['id'], $user['id'], $user['id'], $user['id'], $user['id']]);
    $userStats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Trading page error: " . $e->getMessage());
    $allTrades = [];
    $userTrades = [];
    $userOffers = [];
    $userStats = ['completed_trades' => 0, 'active_trades' => 0, 'successful_offers' => 0, 'pending_offers' => 0];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>Beer Trading
                    </h1>
                    <p class="text-muted mb-0">Trade, share, and exchange beers with fellow enthusiasts</p>
                </div>
                <div>
                    <a href="create-trade.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Post Trade
                    </a>
                </div>
            </div>
            
            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'browse' ? 'active' : ''; ?>" 
                       href="?tab=browse">
                        <i class="fas fa-search me-2"></i>Browse Trades
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'my_trades' ? 'active' : ''; ?>" 
                       href="?tab=my_trades">
                        <i class="fas fa-handshake me-2"></i>My Trades
                        <span class="badge bg-primary ms-2"><?php echo count($userTrades); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'my_offers' ? 'active' : ''; ?>" 
                       href="?tab=my_offers">
                        <i class="fas fa-hand-paper me-2"></i>My Offers
                        <span class="badge bg-success ms-2"><?php echo count($userOffers); ?></span>
                    </a>
                </li>
            </ul>
            
            <!-- Browse Trades Tab -->
            <?php if ($currentTab === 'browse'): ?>
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <input type="hidden" name="tab" value="browse">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Trades</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                       placeholder="Beer name or description">
                            </div>
                            <div class="col-md-3">
                                <label for="type" class="form-label">Trade Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">All Types</option>
                                    <option value="trade" <?php echo $tradeType === 'trade' ? 'selected' : ''; ?>>Trade</option>
                                    <option value="share" <?php echo $tradeType === 'share' ? 'selected' : ''; ?>>Share</option>
                                    <option value="sell" <?php echo $tradeType === 'sell' ? 'selected' : ''; ?>>Sell</option>
                                    <option value="request" <?php echo $tradeType === 'request' ? 'selected' : ''; ?>>Request</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       value="<?php echo htmlspecialchars($location); ?>" 
                                       placeholder="City or region">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Trades Grid -->
                <div class="row">
                    <?php foreach ($allTrades as $trade): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card trade-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getTradeTypeColor($trade['trade_type']); ?>">
                                        <?php echo ucfirst($trade['trade_type']); ?>
                                    </span>
                                    <small class="text-muted">
                                        <?php echo $trade['offer_count']; ?> offers
                                    </small>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($trade['title']); ?></h5>
                                    
                                    <div class="trader-info mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($trade['first_name'] . ' ' . $trade['last_name']); ?>
                                        </small>
                                    </div>
                                    
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($trade['description'], 0, 100)); ?>
                                        <?php if (strlen($trade['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                    
                                    <!-- Offering Beers -->
                                    <div class="offering-beers mb-3">
                                        <h6 class="small text-muted mb-2">Offering:</h6>
                                        <?php 
                                        $offeringBeers = json_decode($trade['offering_beers'], true);
                                        if ($offeringBeers):
                                        ?>
                                            <div class="beer-list">
                                                <?php foreach (array_slice($offeringBeers, 0, 3) as $beer): ?>
                                                    <span class="badge bg-light text-dark me-1 mb-1">
                                                        <?php echo htmlspecialchars($beer['name'] ?? $beer); ?>
                                                    </span>
                                                <?php endforeach; ?>
                                                <?php if (count($offeringBeers) > 3): ?>
                                                    <span class="badge bg-secondary">+<?php echo count($offeringBeers) - 3; ?> more</span>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Seeking Beers -->
                                    <?php if ($trade['seeking_beers']): ?>
                                        <div class="seeking-beers mb-3">
                                            <h6 class="small text-muted mb-2">Seeking:</h6>
                                            <?php 
                                            $seekingBeers = json_decode($trade['seeking_beers'], true);
                                            if ($seekingBeers):
                                            ?>
                                                <div class="beer-list">
                                                    <?php foreach (array_slice($seekingBeers, 0, 3) as $beer): ?>
                                                        <span class="badge bg-outline-primary me-1 mb-1">
                                                            <?php echo htmlspecialchars($beer['name'] ?? $beer); ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                    <?php if (count($seekingBeers) > 3): ?>
                                                        <span class="badge bg-primary">+<?php echo count($seekingBeers) - 3; ?> more</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Trade Info -->
                                    <div class="trade-info mb-3">
                                        <?php if ($trade['location']): ?>
                                            <div class="text-muted small">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?php echo htmlspecialchars($trade['location']); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($trade['trade_value']): ?>
                                            <div class="text-success small">
                                                <i class="fas fa-dollar-sign me-1"></i>
                                                Value: $<?php echo number_format($trade['trade_value'], 2); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            Posted <?php echo timeAgo($trade['created_at']); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <?php if ($trade['initiator_id'] !== $user['id']): ?>
                                            <div class="d-flex gap-2">
                                                <a href="trade-detail.php?id=<?php echo $trade['id']; ?>" 
                                                   class="btn btn-outline-primary flex-grow-1">
                                                    View Details
                                                </a>
                                                <button class="btn btn-success" 
                                                        onclick="makeOffer('<?php echo $trade['id']; ?>')">
                                                    Make Offer
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <a href="trade-detail.php?id=<?php echo $trade['id']; ?>" 
                                               class="btn btn-primary w-100">
                                                Manage Trade
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($allTrades)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h4>No Trades Available</h4>
                        <p class="text-muted">Be the first to post a trade!</p>
                        <a href="create-trade.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Post Trade
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- My Trades Tab -->
            <?php if ($currentTab === 'my_trades'): ?>
                <div class="row">
                    <?php foreach ($userTrades as $trade): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card trade-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getTradeStatusColor($trade['status']); ?>">
                                        <?php echo ucfirst($trade['status']); ?>
                                    </span>
                                    <div>
                                        <span class="badge bg-primary"><?php echo $trade['offer_count']; ?> offers</span>
                                        <?php if ($trade['pending_offers'] > 0): ?>
                                            <span class="badge bg-warning"><?php echo $trade['pending_offers']; ?> pending</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($trade['title']); ?></h5>
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($trade['description'], 0, 80)); ?>...
                                    </p>
                                    
                                    <div class="trade-stats mb-3">
                                        <div class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            Posted <?php echo timeAgo($trade['created_at']); ?>
                                        </div>
                                        <?php if ($trade['expires_at']): ?>
                                            <div class="text-warning small">
                                                <i class="fas fa-hourglass-half me-1"></i>
                                                Expires <?php echo timeAgo($trade['expires_at']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-auto">
                                        <a href="trade-detail.php?id=<?php echo $trade['id']; ?>" 
                                           class="btn btn-primary w-100">
                                            Manage Trade
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($userTrades)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                        <h4>No Trades Posted</h4>
                        <p class="text-muted">Start trading by posting your first trade!</p>
                        <a href="create-trade.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Post Trade
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <!-- My Offers Tab -->
            <?php if ($currentTab === 'my_offers'): ?>
                <div class="row">
                    <?php foreach ($userOffers as $offer): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card offer-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?php echo getOfferStatusColor($offer['status']); ?>">
                                        <?php echo ucfirst($offer['status']); ?>
                                    </span>
                                    <small class="text-muted">
                                        <?php echo timeAgo($offer['created_at']); ?>
                                    </small>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($offer['trade_title']); ?></h5>
                                    
                                    <div class="trader-info mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            Trade by <?php echo htmlspecialchars($offer['first_name'] . ' ' . $offer['last_name']); ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($offer['message']): ?>
                                        <p class="card-text">
                                            <em>"<?php echo htmlspecialchars($offer['message']); ?>"</em>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($offer['offer_value']): ?>
                                        <div class="offer-value mb-3">
                                            <div class="text-success">
                                                <i class="fas fa-dollar-sign me-1"></i>
                                                Offer Value: $<?php echo number_format($offer['offer_value'], 2); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-auto">
                                        <a href="trade-detail.php?id=<?php echo $offer['trade_id']; ?>&offer=<?php echo $offer['id']; ?>" 
                                           class="btn btn-outline-primary w-100">
                                            View Offer
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($userOffers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-hand-paper fa-3x text-muted mb-3"></i>
                        <h4>No Offers Made</h4>
                        <p class="text-muted">Browse trades and make your first offer!</p>
                        <a href="?tab=browse" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Trades
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Trading Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Your Trading Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-success"><?php echo $userStats['completed_trades']; ?></div>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-primary"><?php echo $userStats['active_trades']; ?></div>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-info"><?php echo $userStats['successful_offers']; ?></div>
                                <small class="text-muted">Successful Offers</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="stat-item">
                                <div class="h4 mb-0 text-warning"><?php echo $userStats['pending_offers']; ?></div>
                                <small class="text-muted">Pending Offers</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Trade Types -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Trade Types
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="?tab=browse&type=trade" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-exchange-alt me-2"></i>Trade</span>
                            <span class="badge bg-primary rounded-pill">15</span>
                        </a>
                        <a href="?tab=browse&type=share" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-heart me-2"></i>Share</span>
                            <span class="badge bg-primary rounded-pill">8</span>
                        </a>
                        <a href="?tab=browse&type=sell" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-dollar-sign me-2"></i>Sell</span>
                            <span class="badge bg-primary rounded-pill">5</span>
                        </a>
                        <a href="?tab=browse&type=request" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-search me-2"></i>Request</span>
                            <span class="badge bg-primary rounded-pill">12</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="create-trade.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Post Trade
                        </a>
                        <a href="trading-guidelines.php" class="btn btn-outline-info">
                            <i class="fas fa-info-circle me-2"></i>Trading Guidelines
                        </a>
                        <a href="events.php" class="btn btn-outline-success">
                            <i class="fas fa-calendar me-2"></i>Events
                        </a>
                        <a href="clubs.php" class="btn btn-outline-warning">
                            <i class="fas fa-users me-2"></i>Beer Clubs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function makeOffer(tradeId) {
    window.location.href = `make-offer.php?trade_id=${tradeId}`;
}

function timeAgo(dateString) {
    // Simple time ago function - you can enhance this
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
    return Math.floor(diffInSeconds / 86400) + ' days ago';
}
</script>

<?php 
// Helper functions
function getTradeTypeColor($type) {
    $colors = [
        'trade' => 'primary',
        'share' => 'success',
        'sell' => 'warning',
        'request' => 'info'
    ];
    return $colors[$type] ?? 'secondary';
}

function getTradeStatusColor($status) {
    $colors = [
        'open' => 'success',
        'pending' => 'warning',
        'completed' => 'primary',
        'cancelled' => 'danger',
        'expired' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}

function getOfferStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'accepted' => 'success',
        'declined' => 'danger',
        'countered' => 'info',
        'withdrawn' => 'secondary'
    ];
    return $colors[$status] ?? 'secondary';
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

require_once '../includes/footer.php'; 
?>
