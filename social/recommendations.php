<?php
/**
 * Beer Recommendations Engine
 * AI-powered beer discovery and personalized recommendations
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Beer Recommendations - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css', '../assets/css/recommendations.css'];
$additionalJS = ['../assets/js/recommendations.js'];

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.* 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get user's taste profile
    $tasteProfileQuery = "
        SELECT 
            AVG(br.rating) as avg_rating,
            COUNT(DISTINCT br.beer_id) as beers_rated,
            COUNT(DISTINCT bc.beer_id) as beers_tried,
            GROUP_CONCAT(DISTINCT bs.name) as preferred_styles,
            AVG(bi.abv) as preferred_abv,
            AVG(bi.ibu) as preferred_ibu,
            COUNT(DISTINCT bi.brewery_id) as breweries_tried
        FROM beer_reviews br
        LEFT JOIN beer_checkins bc ON br.user_id = bc.user_id
        LEFT JOIN beer_items bi ON br.beer_id = bi.id
        LEFT JOIN beer_styles bs ON bi.style_id = bs.id
        WHERE br.user_id = ? AND br.rating >= 4
        GROUP BY br.user_id
    ";
    $stmt = $conn->prepare($tasteProfileQuery);
    $stmt->execute([$user['id']]);
    $tasteProfile = $stmt->fetch();
    
    // Get personalized recommendations based on user's ratings and preferences
    $personalizedQuery = "
        SELECT DISTINCT
            bi.*,
            br_brewery.name as brewery_name,
            bs.name as style_name,
            AVG(br_all.rating) as avg_rating,
            COUNT(br_all.id) as review_count,
            COUNT(bc_all.id) as checkin_count,
            CASE 
                WHEN br_user.rating IS NOT NULL THEN 'rated'
                WHEN bc_user.id IS NOT NULL THEN 'tried'
                ELSE 'new'
            END as user_status,
            (
                -- Similarity score based on user preferences
                (CASE WHEN bi.abv BETWEEN ? - 1 AND ? + 1 THEN 2 ELSE 0 END) +
                (CASE WHEN bi.ibu BETWEEN ? - 10 AND ? + 10 THEN 2 ELSE 0 END) +
                (CASE WHEN bs.name IN (?) THEN 3 ELSE 0 END) +
                (CASE WHEN AVG(br_all.rating) >= ? THEN 2 ELSE 0 END)
            ) as similarity_score
        FROM beer_items bi
        JOIN breweries br_brewery ON bi.brewery_id = br_brewery.id
        JOIN beer_styles bs ON bi.style_id = bs.id
        LEFT JOIN beer_reviews br_all ON bi.id = br_all.beer_id
        LEFT JOIN beer_checkins bc_all ON bi.id = bc_all.beer_id
        LEFT JOIN beer_reviews br_user ON bi.id = br_user.beer_id AND br_user.user_id = ?
        LEFT JOIN beer_checkins bc_user ON bi.id = bc_user.beer_id AND bc_user.user_id = ?
        WHERE br_user.id IS NULL  -- User hasn't rated this beer
        AND bc_user.id IS NULL    -- User hasn't tried this beer
        GROUP BY bi.id
        HAVING similarity_score > 3 AND review_count >= 3
        ORDER BY similarity_score DESC, avg_rating DESC
        LIMIT 20
    ";
    
    $preferredAbv = $tasteProfile['preferred_abv'] ?? 5.0;
    $preferredIbu = $tasteProfile['preferred_ibu'] ?? 30;
    $preferredStyles = $tasteProfile['preferred_styles'] ?? 'IPA,Lager,Stout';
    $minRating = $tasteProfile['avg_rating'] ?? 3.5;
    
    $stmt = $conn->prepare($personalizedQuery);
    $stmt->execute([
        $preferredAbv, $preferredAbv,
        $preferredIbu, $preferredIbu,
        $preferredStyles,
        $minRating,
        $user['id'], $user['id']
    ]);
    $personalizedRecommendations = $stmt->fetchAll();
    
    // Get trending beers (popular this week)
    $trendingQuery = "
        SELECT 
            bi.*,
            br.name as brewery_name,
            bs.name as style_name,
            AVG(br_all.rating) as avg_rating,
            COUNT(DISTINCT bc.id) as checkin_count,
            COUNT(DISTINCT br_all.id) as review_count
        FROM beer_items bi
        JOIN breweries br ON bi.brewery_id = br.id
        JOIN beer_styles bs ON bi.style_id = bs.id
        LEFT JOIN beer_checkins bc ON bi.id = bc.beer_id AND bc.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        LEFT JOIN beer_reviews br_all ON bi.id = br_all.beer_id
        GROUP BY bi.id
        HAVING checkin_count > 0
        ORDER BY checkin_count DESC, avg_rating DESC
        LIMIT 15
    ";
    $stmt = $conn->prepare($trendingQuery);
    $stmt->execute();
    $trendingBeers = $stmt->fetchAll();
    
    // Get similar users' favorites
    $similarUsersQuery = "
        SELECT 
            bi.*,
            br.name as brewery_name,
            bs.name as style_name,
            AVG(br_similar.rating) as avg_rating,
            COUNT(br_similar.id) as similar_user_ratings
        FROM beer_reviews br_similar
        JOIN beer_items bi ON br_similar.beer_id = bi.id
        JOIN breweries br ON bi.brewery_id = br.id
        JOIN beer_styles bs ON bi.style_id = bs.id
        WHERE br_similar.user_id IN (
            -- Find users with similar taste
            SELECT DISTINCT br2.user_id
            FROM beer_reviews br1
            JOIN beer_reviews br2 ON br1.beer_id = br2.beer_id
            WHERE br1.user_id = ? 
            AND br2.user_id != ?
            AND ABS(br1.rating - br2.rating) <= 1
            GROUP BY br2.user_id
            HAVING COUNT(*) >= 3
        )
        AND br_similar.rating >= 4
        AND bi.id NOT IN (
            SELECT beer_id FROM beer_reviews WHERE user_id = ?
            UNION
            SELECT beer_id FROM beer_checkins WHERE user_id = ?
        )
        GROUP BY bi.id
        HAVING similar_user_ratings >= 2
        ORDER BY avg_rating DESC, similar_user_ratings DESC
        LIMIT 15
    ";
    $stmt = $conn->prepare($similarUsersQuery);
    $stmt->execute([$user['id'], $user['id'], $user['id'], $user['id']]);
    $similarUsersRecommendations = $stmt->fetchAll();
    
    // Get local brewery recommendations
    $localBreweriesQuery = "
        SELECT 
            bi.*,
            br.name as brewery_name,
            bs.name as style_name,
            AVG(br_all.rating) as avg_rating,
            COUNT(br_all.id) as review_count,
            br.location
        FROM beer_items bi
        JOIN breweries br ON bi.brewery_id = br.id
        JOIN beer_styles bs ON bi.style_id = bs.id
        LEFT JOIN beer_reviews br_all ON bi.id = br_all.beer_id
        WHERE br.location LIKE ? OR br.location LIKE ?
        AND bi.id NOT IN (
            SELECT beer_id FROM beer_reviews WHERE user_id = ?
            UNION
            SELECT beer_id FROM beer_checkins WHERE user_id = ?
        )
        GROUP BY bi.id
        HAVING review_count >= 1
        ORDER BY avg_rating DESC, review_count DESC
        LIMIT 12
    ";
    
    $userLocation = $userProfile['location'] ?? '';
    $locationPattern = '%' . explode(',', $userLocation)[0] . '%';
    $stmt = $conn->prepare($localBreweriesQuery);
    $stmt->execute([$locationPattern, $locationPattern, $user['id'], $user['id']]);
    $localRecommendations = $stmt->fetchAll();
    
    // Get seasonal recommendations
    $currentMonth = date('n');
    $seasonalStyles = [
        'winter' => ['Stout', 'Porter', 'Imperial Stout', 'Winter Warmer'],
        'spring' => ['Wheat Beer', 'Hefeweizen', 'Saison', 'Pilsner'],
        'summer' => ['Lager', 'Light Ale', 'Wheat Beer', 'Sour'],
        'fall' => ['Oktoberfest', 'Brown Ale', 'Pumpkin Ale', 'Amber Ale']
    ];
    
    $season = $currentMonth >= 12 || $currentMonth <= 2 ? 'winter' :
              ($currentMonth >= 3 && $currentMonth <= 5 ? 'spring' :
              ($currentMonth >= 6 && $currentMonth <= 8 ? 'summer' : 'fall'));
    
    $seasonalStylesList = "'" . implode("','", $seasonalStyles[$season]) . "'";
    
    $seasonalQuery = "
        SELECT 
            bi.*,
            br.name as brewery_name,
            bs.name as style_name,
            AVG(br_all.rating) as avg_rating,
            COUNT(br_all.id) as review_count
        FROM beer_items bi
        JOIN breweries br ON bi.brewery_id = br.id
        JOIN beer_styles bs ON bi.style_id = bs.id
        LEFT JOIN beer_reviews br_all ON bi.id = br_all.beer_id
        WHERE bs.name IN ($seasonalStylesList)
        AND bi.id NOT IN (
            SELECT beer_id FROM beer_reviews WHERE user_id = ?
            UNION
            SELECT beer_id FROM beer_checkins WHERE user_id = ?
        )
        GROUP BY bi.id
        HAVING review_count >= 2
        ORDER BY avg_rating DESC
        LIMIT 10
    ";
    $stmt = $conn->prepare($seasonalQuery);
    $stmt->execute([$user['id'], $user['id']]);
    $seasonalRecommendations = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Recommendations error: " . $e->getMessage());
    $tasteProfile = [];
    $personalizedRecommendations = [];
    $trendingBeers = [];
    $similarUsersRecommendations = [];
    $localRecommendations = [];
    $seasonalRecommendations = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-magic me-2 text-primary"></i>Beer Recommendations
                    </h1>
                    <p class="text-muted mb-0">Discover your next favorite beer with AI-powered recommendations</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" id="refreshRecommendations">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
            
            <!-- Taste Profile Summary -->
            <?php if ($tasteProfile): ?>
                <div class="card mb-4 taste-profile-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-circle me-2"></i>Your Taste Profile
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-item text-center">
                                    <div class="h4 mb-0 text-primary"><?php echo number_format($tasteProfile['avg_rating'], 1); ?></div>
                                    <small class="text-muted">Avg Rating</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item text-center">
                                    <div class="h4 mb-0 text-success"><?php echo $tasteProfile['beers_tried']; ?></div>
                                    <small class="text-muted">Beers Tried</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item text-center">
                                    <div class="h4 mb-0 text-warning"><?php echo number_format($tasteProfile['preferred_abv'], 1); ?>%</div>
                                    <small class="text-muted">Preferred ABV</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item text-center">
                                    <div class="h4 mb-0 text-info"><?php echo $tasteProfile['breweries_tried']; ?></div>
                                    <small class="text-muted">Breweries</small>
                                </div>
                            </div>
                        </div>
                        <?php if ($tasteProfile['preferred_styles']): ?>
                            <div class="mt-3">
                                <strong>Preferred Styles:</strong>
                                <?php foreach (explode(',', $tasteProfile['preferred_styles']) as $style): ?>
                                    <span class="badge bg-primary me-1"><?php echo htmlspecialchars($style); ?></span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Personalized Recommendations -->
            <?php if (!empty($personalizedRecommendations)): ?>
                <div class="recommendation-section mb-5">
                    <div class="section-header mb-3">
                        <h4>
                            <i class="fas fa-star text-warning me-2"></i>Personalized For You
                            <span class="badge bg-primary ms-2"><?php echo count($personalizedRecommendations); ?></span>
                        </h4>
                        <p class="text-muted">Based on your taste preferences and rating history</p>
                    </div>
                    <div class="row">
                        <?php foreach ($personalizedRecommendations as $beer): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <?php echo renderBeerCard($beer, 'personalized'); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Trending This Week -->
            <?php if (!empty($trendingBeers)): ?>
                <div class="recommendation-section mb-5">
                    <div class="section-header mb-3">
                        <h4>
                            <i class="fas fa-fire text-danger me-2"></i>Trending This Week
                            <span class="badge bg-danger ms-2">HOT</span>
                        </h4>
                        <p class="text-muted">Most popular beers in the community right now</p>
                    </div>
                    <div class="row">
                        <?php foreach (array_slice($trendingBeers, 0, 8) as $beer): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <?php echo renderBeerCard($beer, 'trending'); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Similar Users Love -->
            <?php if (!empty($similarUsersRecommendations)): ?>
                <div class="recommendation-section mb-5">
                    <div class="section-header mb-3">
                        <h4>
                            <i class="fas fa-users text-success me-2"></i>People Like You Love
                        </h4>
                        <p class="text-muted">Favorites from users with similar taste</p>
                    </div>
                    <div class="row">
                        <?php foreach (array_slice($similarUsersRecommendations, 0, 8) as $beer): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <?php echo renderBeerCard($beer, 'similar'); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Local Discoveries -->
            <?php if (!empty($localRecommendations)): ?>
                <div class="recommendation-section mb-5">
                    <div class="section-header mb-3">
                        <h4>
                            <i class="fas fa-map-marker-alt text-info me-2"></i>Local Discoveries
                        </h4>
                        <p class="text-muted">Great beers from breweries near you</p>
                    </div>
                    <div class="row">
                        <?php foreach (array_slice($localRecommendations, 0, 6) as $beer): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <?php echo renderBeerCard($beer, 'local'); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Seasonal Picks -->
            <?php if (!empty($seasonalRecommendations)): ?>
                <div class="recommendation-section mb-5">
                    <div class="section-header mb-3">
                        <h4>
                            <i class="fas fa-leaf text-warning me-2"></i>Perfect for <?php echo ucfirst($season); ?>
                        </h4>
                        <p class="text-muted">Seasonal styles that match the current weather</p>
                    </div>
                    <div class="row">
                        <?php foreach ($seasonalRecommendations as $beer): ?>
                            <div class="col-md-4 col-lg-3 mb-4">
                                <?php echo renderBeerCard($beer, 'seasonal'); ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Recommendation Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Refine Recommendations
                    </h6>
                </div>
                <div class="card-body">
                    <form id="recommendationFilters">
                        <div class="mb-3">
                            <label class="form-label">ABV Range</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="minAbv" placeholder="Min" step="0.1" min="0" max="20">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           id="maxAbv" placeholder="Max" step="0.1" min="0" max="20">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Beer Style</label>
                            <select class="form-select form-select-sm" id="styleFilter">
                                <option value="">All Styles</option>
                                <option value="IPA">IPA</option>
                                <option value="Lager">Lager</option>
                                <option value="Stout">Stout</option>
                                <option value="Wheat Beer">Wheat Beer</option>
                                <option value="Sour">Sour</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Minimum Rating</label>
                            <select class="form-select form-select-sm" id="ratingFilter">
                                <option value="0">Any Rating</option>
                                <option value="3">3+ Stars</option>
                                <option value="4">4+ Stars</option>
                                <option value="4.5">4.5+ Stars</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Apply Filters
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Discovery Tools -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-compass me-2"></i>Discovery Tools
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="surpriseMe()">
                            <i class="fas fa-random me-2"></i>Surprise Me!
                        </button>
                        <button class="btn btn-outline-success" onclick="findSimilar()">
                            <i class="fas fa-search-plus me-2"></i>Find Similar
                        </button>
                        <button class="btn btn-outline-warning" onclick="exploreStyle()">
                            <i class="fas fa-map me-2"></i>Explore Style
                        </button>
                        <a href="beer-quiz.php" class="btn btn-outline-info">
                            <i class="fas fa-question-circle me-2"></i>Take Beer Quiz
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Recommendation Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Your Discovery Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Recommendations Tried</span>
                            <strong>12</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Success Rate</span>
                            <strong>83%</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>New Favorites Found</span>
                            <strong>5</strong>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 83%"></div>
                    </div>
                    <small class="text-muted">Recommendation accuracy</small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
function renderBeerCard($beer, $type) {
    $typeColors = [
        'personalized' => 'warning',
        'trending' => 'danger',
        'similar' => 'success',
        'local' => 'info',
        'seasonal' => 'warning'
    ];
    
    $typeIcons = [
        'personalized' => 'star',
        'trending' => 'fire',
        'similar' => 'users',
        'local' => 'map-marker-alt',
        'seasonal' => 'leaf'
    ];
    
    $color = $typeColors[$type] ?? 'primary';
    $icon = $typeIcons[$type] ?? 'beer';
    
    return "
        <div class='card beer-card h-100' data-beer-id='{$beer['id']}'>
            <div class='card-header p-2'>
                <div class='d-flex justify-content-between align-items-center'>
                    <span class='badge bg-{$color}'>
                        <i class='fas fa-{$icon} me-1'></i>" . ucfirst($type) . "
                    </span>
                    " . ($beer['avg_rating'] ? "<span class='rating'>" . number_format($beer['avg_rating'], 1) . " ⭐</span>" : '') . "
                </div>
            </div>
            <div class='card-body p-3'>
                <h6 class='card-title mb-2'>" . htmlspecialchars($beer['name']) . "</h6>
                <p class='card-text small text-muted mb-2'>
                    <strong>" . htmlspecialchars($beer['brewery_name']) . "</strong><br>
                    " . htmlspecialchars($beer['style_name']) . "
                </p>
                " . ($beer['abv'] ? "<div class='beer-stats mb-2'>
                    <small class='text-muted'>
                        ABV: {$beer['abv']}%" . ($beer['ibu'] ? " • IBU: {$beer['ibu']}" : '') . "
                    </small>
                </div>" : '') . "
                <div class='d-flex justify-content-between align-items-center'>
                    <small class='text-muted'>
                        " . ($beer['review_count'] ?? 0) . " reviews
                    </small>
                    <div class='btn-group btn-group-sm'>
                        <button class='btn btn-outline-primary' onclick='addToWishlist({$beer['id']})'>
                            <i class='fas fa-heart'></i>
                        </button>
                        <button class='btn btn-primary' onclick='viewBeer({$beer['id']})'>
                            View
                        </button>
                    </div>
                </div>
            </div>
        </div>
    ";
}

require_once '../includes/footer.php'; 
?>
