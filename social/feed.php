<?php
require_once '../config/config.php';

// Require login
requireLogin();

$pageTitle = 'Activity Feed - ' . APP_NAME;
$additionalCSS = ['/assets/css/social.css', '/assets/css/beersty-layouts.css'];

$user = getCurrentUser();
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$activities = [];
$checkins = [];
$totalItems = 0;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get activities from followed users and own activities
    $activitiesQuery = "
        SELECT 
            ua.*,
            p.first_name, p.last_name, p.username, p.role, p.avatar,
            'activity' as item_type
        FROM user_activities ua
        JOIN profiles p ON ua.user_id = p.id
        WHERE ua.is_public = 1 
        AND (
            ua.user_id = ? 
            OR ua.user_id IN (
                SELECT following_id 
                FROM user_follows 
                WHERE follower_id = ?
            )
        )
        AND ua.activity_type IN ('beer_rating', 'beer_review', 'beer_checkin', 'user_follow', 'joined')
    ";
    
    // Get check-ins from followed users and own check-ins
    $checkinsQuery = "
        SELECT 
            bc.*,
            p.first_name, p.last_name, p.username, p.role, p.avatar,
            bm.name as beer_name, bm.thumbnail as beer_thumbnail,
            b.name as brewery_name, b.city as brewery_city, b.state as brewery_state,
            bs.name as style_name,
            br.overall_rating,
            'checkin' as item_type
        FROM beer_checkins bc
        JOIN profiles p ON bc.user_id = p.id
        JOIN beer_menu bm ON bc.beer_id = bm.id
        LEFT JOIN breweries b ON bc.brewery_id = b.id
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
        LEFT JOIN beer_ratings br ON bc.rating_id = br.id
        WHERE bc.is_public = 1 
        AND (
            bc.user_id = ? 
            OR bc.user_id IN (
                SELECT following_id 
                FROM user_follows 
                WHERE follower_id = ?
            )
        )
    ";
    
    // Combine and order by created_at
    $combinedQuery = "
        ($activitiesQuery)
        UNION ALL
        ($checkinsQuery)
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $conn->prepare($combinedQuery);
    $stmt->execute([$user['id'], $user['id'], $user['id'], $user['id']]);
    $feedItems = $stmt->fetchAll();
    
    // Get total count for pagination
    $countQuery = "
        SELECT COUNT(*) FROM (
            ($activitiesQuery)
            UNION ALL
            ($checkinsQuery)
        ) as combined
    ";
    $stmt = $conn->prepare($countQuery);
    $stmt->execute([$user['id'], $user['id'], $user['id'], $user['id']]);
    $totalItems = $stmt->fetchColumn();
    
} catch (Exception $e) {
    error_log("Activity feed error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading activity feed.';
}

$totalPages = ceil($totalItems / $limit);

include '../includes/header.php';
?>

<!-- Apply brewery theme background -->
<style>
body {
    background-color: #3B2A2A !important;
    color: #F5F5DC !important;
}
</style>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold mb-2" style="color: #F5F5DC;">
                <i class="fas fa-stream me-3" style="color: #FFC107;"></i>Activity Feed
            </h1>
            <p class="lead" style="color: #D69A6B;">
                See what's happening in the beer community
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="/social/checkin.php" class="btn" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;">
                <i class="fas fa-map-marker-alt me-2"></i>Check In
            </a>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4 quick-actions">
        <div class="col-12">
            <div class="card shadow-sm" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 col-md-3 mb-2">
                            <a href="/social/checkin.php" class="btn w-100" style="border: 2px solid #FFC107; color: #F5F5DC; background-color: transparent; font-weight: bold;">
                                <i class="fas fa-map-marker-alt d-block mb-1" style="color: #FFC107;"></i>
                                <small>Check In</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3 mb-2">
                            <a href="/beers/discover.php" class="btn w-100" style="border: 2px solid #D69A6B; color: #F5F5DC; background-color: transparent; font-weight: bold;">
                                <i class="fas fa-search d-block mb-1" style="color: #D69A6B;"></i>
                                <small>Discover Beers</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3 mb-2">
                            <a href="/social/discover-users.php" class="btn w-100" style="border: 2px solid #F5F5DC; color: #F5F5DC; background-color: transparent; font-weight: bold;">
                                <i class="fas fa-users d-block mb-1" style="color: #F5F5DC;"></i>
                                <small>Find Friends</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3 mb-2">
                            <a href="/user/profile.php" class="btn w-100" style="border: 2px solid #F5F5DC; color: #F5F5DC; background-color: transparent; font-weight: bold;">
                                <i class="fas fa-user d-block mb-1" style="color: #F5F5DC;"></i>
                                <small>My Profile</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Feed -->
    <?php if (empty($feedItems)): ?>
        <div class="text-center py-5">
            <i class="fas fa-stream fa-3x mb-3" style="color: #D69A6B;"></i>
            <h4 style="color: #F5F5DC;">Your feed is empty</h4>
            <p style="color: #D69A6B;" class="mb-4">
                Follow some beer enthusiasts or check in to some beers to see activity here!
            </p>
            <div class="d-flex gap-2 justify-content-center">
                <a href="/social/discover-users.php" class="btn" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;">
                    <i class="fas fa-users me-2"></i>Find People to Follow
                </a>
                <a href="/social/checkin.php" class="btn" style="border: 2px solid #FFC107; color: #F5F5DC; background-color: transparent; font-weight: bold;">
                    <i class="fas fa-map-marker-alt me-2"></i>Make Your First Check-in
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="feed-container">
            <?php foreach ($feedItems as $item): ?>
                <div class="card shadow-sm mb-4 feed-item" style="background-color: #6F4C3E; border: 1px solid #D69A6B;">
                    <div class="card-body">
                        <!-- User Info -->
                        <div class="d-flex align-items-center mb-3">
                            <div class="user-avatar me-3">
                                <?php if (!empty($item['avatar'])): ?>
                                    <img src="<?php echo htmlspecialchars($item['avatar']); ?>" 
                                         alt="Profile Picture" class="rounded-circle" width="40" height="40">
                                <?php else: ?>
                                    <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px; background: #f8f9fa; border: 1px solid #dee2e6;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold" style="color: #F5F5DC;">
                                    <?php
                                    $displayName = trim($item['first_name'] . ' ' . $item['last_name']);
                                    if (empty($displayName)) {
                                        $displayName = $item['username'] ?: 'Beer Enthusiast';
                                    }
                                    echo htmlspecialchars($displayName);
                                    ?>
                                    <span class="badge ms-2" style="background-color: #FFC107; color: #3B2A2A;">
                                        <?php
                                        $roleLabels = [
                                            'beer_enthusiast' => '🍺 Enthusiast',
                                            'beer_expert' => '🎯 Expert',
                                            'customer' => '👤 Customer'
                                        ];
                                        echo $roleLabels[$item['role']] ?? ucfirst($item['role']);
                                        ?>
                                    </span>
                                </div>
                                <small style="color: #D69A6B;">
                                    <?php echo formatDateTime($item['created_at']); ?>
                                </small>
                            </div>
                        </div>
                        
                        <!-- Activity Content -->
                        <?php if ($item['item_type'] === 'checkin'): ?>
                            <!-- Check-in -->
                            <div class="checkin-content">
                                <div class="row">
                                    <div class="col-md-2 text-center mb-3">
                                        <?php if (!empty($item['beer_thumbnail'])): ?>
                                            <img src="<?php echo htmlspecialchars($item['beer_thumbnail']); ?>" 
                                                 class="img-fluid beer-thumb" alt="<?php echo htmlspecialchars($item['beer_name']); ?>">
                                        <?php else: ?>
                                            <div class="beer-thumb-placeholder">
                                                <i class="fas fa-beer fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="checkin-header mb-2">
                                            <h6 class="mb-1" style="color: #F5F5DC;">
                                                <i class="fas fa-map-marker-alt me-1" style="color: #FFC107;"></i>
                                                is drinking <strong><?php echo htmlspecialchars($item['beer_name']); ?></strong>
                                            </h6>
                                            <p class="mb-1" style="color: #D69A6B;">
                                                by <?php echo htmlspecialchars($item['brewery_name']); ?>
                                                <?php if (!empty($item['brewery_city'])): ?>
                                                    • <?php echo htmlspecialchars($item['brewery_city']); ?><?php if (!empty($item['brewery_state'])): ?>, <?php echo htmlspecialchars($item['brewery_state']); ?><?php endif; ?>
                                                <?php endif; ?>
                                            </p>
                                            <?php if (!empty($item['style_name'])): ?>
                                                <span class="badge" style="background-color: #FFC107; color: #3B2A2A;"><?php echo htmlspecialchars($item['style_name']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if (!empty($item['overall_rating'])): ?>
                                            <div class="rating mb-2">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star" style="color: <?php echo $i <= $item['overall_rating'] ? '#FFC107' : '#D69A6B'; ?>;"></i>
                                                <?php endfor; ?>
                                                <span class="ms-2" style="color: #6F4C3E; font-weight: bold;"><?php echo number_format($item['overall_rating'], 1); ?>/5</span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($item['checkin_comment'])): ?>
                                            <p class="checkin-comment" style="color: #F5F5DC;"><?php echo nl2br(htmlspecialchars($item['checkin_comment'])); ?></p>
                                        <?php endif; ?>

                                        <?php if (!empty($item['checkin_location'])): ?>
                                            <p class="small" style="color: #D69A6B;">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?php echo htmlspecialchars($item['checkin_location']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Regular Activity -->
                            <div class="activity-content">
                                <?php
                                $metadata = json_decode($item['metadata'] ?? '{}', true);
                                switch ($item['activity_type']) {
                                    case 'beer_rating':
                                    case 'beer_review':
                                        echo '<p class="mb-0">';
                                        echo '<i class="fas fa-star text-warning me-1"></i>';
                                        echo 'rated <strong>' . htmlspecialchars($metadata['beer_name'] ?? 'a beer') . '</strong>';
                                        if (!empty($metadata['rating'])) {
                                            echo ' ' . number_format($metadata['rating'], 1) . '/5 stars';
                                        }
                                        if (!empty($metadata['brewery_name'])) {
                                            echo ' by ' . htmlspecialchars($metadata['brewery_name']);
                                        }
                                        echo '</p>';
                                        break;
                                        
                                    case 'user_follow':
                                        echo '<p class="mb-0">';
                                        echo '<i class="fas fa-user-plus text-success me-1"></i>';
                                        echo 'started following <strong>' . htmlspecialchars($metadata['target_user_name'] ?? 'someone') . '</strong>';
                                        echo '</p>';
                                        break;
                                        
                                    case 'joined':
                                        echo '<p class="mb-0">';
                                        echo '<i class="fas fa-user-check text-primary me-1"></i>';
                                        echo 'joined the Beersty community!';
                                        echo '</p>';
                                        break;
                                        
                                    default:
                                        echo '<p class="mb-0">';
                                        echo '<i class="fas fa-activity text-info me-1"></i>';
                                        echo 'had some activity';
                                        echo '</p>';
                                }
                                ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Social Actions -->
                        <div class="social-actions mt-3 pt-3" style="border-top: 1px solid #D69A6B;">
                            <div class="d-flex gap-3">
                                <button class="btn btn-sm like-btn"
                                        style="border: 1px solid #FFC107; color: #F5F5DC; background-color: transparent;"
                                        data-type="<?php echo $item['item_type']; ?>"
                                        data-id="<?php echo $item['id']; ?>">
                                    <i class="fas fa-heart me-1" style="color: #FFC107;"></i>
                                    <span class="like-count"><?php echo $item['like_count'] ?? 0; ?></span>
                                </button>

                                <button class="btn btn-sm comment-btn"
                                        style="border: 1px solid #D69A6B; color: #F5F5DC; background-color: transparent;"
                                        data-type="<?php echo $item['item_type']; ?>"
                                        data-id="<?php echo $item['id']; ?>">
                                    <i class="fas fa-comment me-1" style="color: #D69A6B;"></i>
                                    <span class="comment-count"><?php echo $item['comment_count'] ?? 0; ?></span>
                                </button>

                                <button class="btn btn-sm share-btn" style="border: 1px solid #F5F5DC; color: #F5F5DC; background-color: transparent;">
                                    <i class="fas fa-share me-1" style="color: #F5F5DC;"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="Feed pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>" style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>"
                               style="<?php echo $i === $page ? 'background-color: #FFC107; border-color: #FFC107; color: #3B2A2A;' : 'color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;'; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>" style="color: #F5F5DC; border-color: #D69A6B; background-color: #6F4C3E;">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
/* Brewery-themed hover effects */
.quick-actions .btn:hover {
    background-color: #FFC107 !important;
    color: #3B2A2A !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.social-actions .btn:hover {
    background-color: #FFC107 !important;
    color: #3B2A2A !important;
    border-color: #FFC107 !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.feed-item {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feed-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 42, 42, 0.3) !important;
    border-color: #FFC107 !important;
}

.page-link:hover {
    background-color: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.beer-thumb {
    border-radius: 8px;
    border: 2px solid #D69A6B;
    max-width: 80px;
    max-height: 80px;
}

.beer-thumb-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%);
    border-radius: 8px;
    border: 2px solid #D69A6B;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #D69A6B;
}

/* Avatar placeholder styling */
.avatar-placeholder {
    background: linear-gradient(135deg, #3B2A2A 0%, #6F4C3E 100%) !important;
    border: 1px solid #D69A6B !important;
    color: #D69A6B !important;
}

/* Activity content styling */
.activity-content p {
    color: #F5F5DC !important;
}

.activity-content i {
    color: #FFC107 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Like functionality
    document.querySelectorAll('.like-btn').forEach(button => {
        button.addEventListener('click', async function() {
            const type = this.dataset.type;
            const id = this.dataset.id;
            
            try {
                const response = await fetch('/api/like-activity.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: type,
                        id: id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const likeCount = this.querySelector('.like-count');
                    likeCount.textContent = result.like_count;
                    
                    if (result.liked) {
                        this.classList.remove('btn-outline-primary');
                        this.classList.add('btn-primary');
                    } else {
                        this.classList.remove('btn-primary');
                        this.classList.add('btn-outline-primary');
                    }
                }
            } catch (error) {
                console.error('Like error:', error);
            }
        });
    });
    
    // Share functionality
    document.querySelectorAll('.share-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: 'Check out this beer activity on Beersty!',
                    url: window.location.href
                });
            } else {
                // Fallback to copying URL
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
