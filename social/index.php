<?php
/**
 * Social Hub - Main Landing Page
 * Central navigation for all social features
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Social Hub - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css', '../assets/css/social-hub.css'];
$additionalJS = ['../assets/js/social-hub.js'];

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.* 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get quick stats for the user
    $statsQuery = "
        SELECT 
            COUNT(DISTINCT bc.id) as total_checkins,
            COUNT(DISTINCT br.id) as total_reviews,
            COUNT(DISTINCT ub.id) as total_badges,
            COUNT(DISTINCT uf1.id) as followers_count,
            COUNT(DISTINCT uf2.id) as following_count
        FROM profiles p
        LEFT JOIN beer_checkins bc ON p.id = bc.user_id
        LEFT JOIN beer_reviews br ON p.id = br.user_id
        LEFT JOIN user_badges ub ON p.id = ub.user_id
        LEFT JOIN user_follows uf1 ON p.id = uf1.following_id AND uf1.status = 'accepted'
        LEFT JOIN user_follows uf2 ON p.id = uf2.follower_id AND uf2.status = 'accepted'
        WHERE p.id = ?
    ";
    $stmt = $conn->prepare($statsQuery);
    $stmt->execute([$user['id']]);
    $userStats = $stmt->fetch();
    
    // Get recent activity count
    $recentActivityQuery = "
        SELECT COUNT(*) as recent_activities
        FROM user_activities 
        WHERE user_id IN (
            SELECT following_id FROM user_follows 
            WHERE follower_id = ? AND status = 'accepted'
            UNION SELECT ?
        )
        AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ";
    $stmt = $conn->prepare($recentActivityQuery);
    $stmt->execute([$user['id'], $user['id']]);
    $recentActivity = $stmt->fetch();
    
    // Get upcoming events count
    $upcomingEventsQuery = "
        SELECT COUNT(*) as upcoming_events
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id AND er.user_id = ?
        WHERE e.start_datetime >= NOW() 
        AND e.status = 'published'
        AND (e.visibility = 'public' OR er.rsvp_status IN ('going', 'maybe'))
    ";
    $stmt = $conn->prepare($upcomingEventsQuery);
    $stmt->execute([$user['id']]);
    $upcomingEvents = $stmt->fetch();
    
    // Get active challenges count
    $activeChallengesQuery = "
        SELECT COUNT(*) as active_challenges
        FROM social_challenges sc
        LEFT JOIN challenge_participants cp ON sc.id = cp.challenge_id AND cp.user_id = ?
        WHERE sc.status = 'active'
        AND (cp.completed = 0 OR cp.id IS NULL)
    ";
    $stmt = $conn->prepare($activeChallengesQuery);
    $stmt->execute([$user['id']]);
    $activeChallenges = $stmt->fetch();
    
    // Get active trades count
    $activeTradesQuery = "
        SELECT COUNT(*) as active_trades
        FROM beer_trades bt
        WHERE bt.initiator_id = ? AND bt.status = 'open'
    ";
    $stmt = $conn->prepare($activeTradesQuery);
    $stmt->execute([$user['id']]);
    $activeTrades = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Social hub error: " . $e->getMessage());
    $userStats = ['total_checkins' => 0, 'total_reviews' => 0, 'total_badges' => 0, 'followers_count' => 0, 'following_count' => 0];
    $recentActivity = ['recent_activities' => 0];
    $upcomingEvents = ['upcoming_events' => 0];
    $activeChallenges = ['active_challenges' => 0];
    $activeTrades = ['active_trades' => 0];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Hero Section -->
    <div class="hero-section mb-5">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <h1 class="display-4 mb-3">
                        Welcome to Your Beer Community, <?php echo htmlspecialchars($userProfile['first_name'] ?? 'Friend'); ?>! 🍺
                    </h1>
                    <p class="lead mb-4">
                        Connect with fellow beer enthusiasts, discover new brews, compete in challenges, 
                        and share your beer journey with the world.
                    </p>
                    <div class="hero-stats">
                        <div class="row">
                            <div class="col-auto">
                                <div class="stat-badge">
                                    <div class="stat-number"><?php echo $userStats['total_checkins']; ?></div>
                                    <div class="stat-label">Check-ins</div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="stat-badge">
                                    <div class="stat-number"><?php echo $userStats['total_reviews']; ?></div>
                                    <div class="stat-label">Reviews</div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="stat-badge">
                                    <div class="stat-number"><?php echo $userStats['total_badges']; ?></div>
                                    <div class="stat-label">Badges</div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="stat-badge">
                                    <div class="stat-number"><?php echo $userStats['followers_count']; ?></div>
                                    <div class="stat-label">Followers</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-image text-center">
                    <i class="fas fa-users-cog fa-10x text-primary opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions mb-5">
        <h3 class="mb-4">
            <i class="fas fa-bolt me-2"></i>Quick Actions
        </h3>
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="../check-in.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-map-pin"></i>
                    </div>
                    <div class="action-content">
                        <h5>Check In Beer</h5>
                        <p>Share your current beer experience</p>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="live-feed.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <div class="action-content">
                        <h5>Live Feed</h5>
                        <p>See what's happening now</p>
                        <?php if ($recentActivity['recent_activities'] > 0): ?>
                            <span class="badge bg-danger"><?php echo $recentActivity['recent_activities']; ?> new</span>
                        <?php endif; ?>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="recommendations.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="action-content">
                        <h5>Recommendations</h5>
                        <p>Discover your next favorite beer</p>
                    </div>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="../user/messages.php" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="action-content">
                        <h5>Messages</h5>
                        <p>Chat with friends</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Main Features Grid -->
    <div class="features-grid mb-5">
        <h3 class="mb-4">
            <i class="fas fa-th-large me-2"></i>Explore Features
        </h3>
        <div class="row">
            <!-- Social Dashboard -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-tachometer-alt fa-2x text-primary"></i>
                        <h4>Social Dashboard</h4>
                    </div>
                    <div class="feature-body">
                        <p>Your personalized hub for all social activities, friend updates, and community insights.</p>
                        <ul class="feature-list">
                            <li>Activity feed from friends</li>
                            <li>Social statistics</li>
                            <li>Quick access to all features</li>
                            <li>Trending content</li>
                        </ul>
                    </div>
                    <div class="feature-footer">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-rocket me-2"></i>Launch Dashboard
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Events & Meetups -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-calendar-alt fa-2x text-success"></i>
                        <h4>Events & Meetups</h4>
                    </div>
                    <div class="feature-body">
                        <p>Discover and join beer events, brewery tours, tastings, and community meetups.</p>
                        <ul class="feature-list">
                            <li>Brewery tours & tastings</li>
                            <li>Beer festivals</li>
                            <li>Community meetups</li>
                            <li>RSVP and attendance tracking</li>
                        </ul>
                        <?php if ($upcomingEvents['upcoming_events'] > 0): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                You have <?php echo $upcomingEvents['upcoming_events']; ?> upcoming events
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="feature-footer">
                        <a href="events.php" class="btn btn-success">
                            <i class="fas fa-calendar me-2"></i>Explore Events
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Challenges & Competitions -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-trophy fa-2x text-warning"></i>
                        <h4>Challenges</h4>
                    </div>
                    <div class="feature-body">
                        <p>Compete in beer challenges, earn badges, and climb the leaderboards.</p>
                        <ul class="feature-list">
                            <li>Style exploration challenges</li>
                            <li>Check-in competitions</li>
                            <li>Badge achievements</li>
                            <li>Leaderboard rankings</li>
                        </ul>
                        <?php if ($activeChallenges['active_challenges'] > 0): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-fire me-2"></i>
                                <?php echo $activeChallenges['active_challenges']; ?> active challenges available
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="feature-footer">
                        <a href="challenges.php" class="btn btn-warning">
                            <i class="fas fa-trophy me-2"></i>Join Challenges
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Beer Clubs -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-users fa-2x text-info"></i>
                        <h4>Beer Clubs</h4>
                    </div>
                    <div class="feature-body">
                        <p>Join communities of beer enthusiasts with similar interests and preferences.</p>
                        <ul class="feature-list">
                            <li>Style-specific clubs</li>
                            <li>Local beer communities</li>
                            <li>Professional networks</li>
                            <li>Educational groups</li>
                        </ul>
                    </div>
                    <div class="feature-footer">
                        <a href="clubs.php" class="btn btn-info">
                            <i class="fas fa-users me-2"></i>Find Clubs
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Beer Trading -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-exchange-alt fa-2x text-danger"></i>
                        <h4>Beer Trading</h4>
                    </div>
                    <div class="feature-body">
                        <p>Trade, share, and exchange rare beers with fellow collectors and enthusiasts.</p>
                        <ul class="feature-list">
                            <li>Trade rare beers</li>
                            <li>Share with friends</li>
                            <li>Buy/sell marketplace</li>
                            <li>Request specific beers</li>
                        </ul>
                        <?php if ($activeTrades['active_trades'] > 0): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-handshake me-2"></i>
                                You have <?php echo $activeTrades['active_trades']; ?> active trades
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="feature-footer">
                        <a href="trading.php" class="btn btn-danger">
                            <i class="fas fa-exchange-alt me-2"></i>Start Trading
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Leaderboards -->
            <div class="col-lg-4 mb-4">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-medal fa-2x text-secondary"></i>
                        <h4>Leaderboards</h4>
                    </div>
                    <div class="feature-body">
                        <p>See how you rank against other beer enthusiasts in various categories.</p>
                        <ul class="feature-list">
                            <li>Overall rankings</li>
                            <li>Check-in leaderboards</li>
                            <li>Review competitions</li>
                            <li>Social influence scores</li>
                        </ul>
                    </div>
                    <div class="feature-footer">
                        <a href="leaderboards.php" class="btn btn-secondary">
                            <i class="fas fa-medal me-2"></i>View Rankings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Getting Started -->
    <div class="getting-started">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>Getting Started with Beersty Social
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <h6>Complete Your Profile</h6>
                            <p>Add your photo, bio, and beer preferences</p>
                            <a href="../user/profile.php" class="btn btn-sm btn-outline-primary">Edit Profile</a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <h6>Make Your First Check-in</h6>
                            <p>Share your current beer experience</p>
                            <a href="../check-in.php" class="btn btn-sm btn-outline-success">Check In</a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <h6>Find Friends</h6>
                            <p>Connect with other beer enthusiasts</p>
                            <a href="../discover-users.php" class="btn btn-sm btn-outline-info">Find Friends</a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <h6>Join a Challenge</h6>
                            <p>Start competing and earning badges</p>
                            <a href="challenges.php" class="btn btn-sm btn-outline-warning">Join Challenge</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 3rem;
    color: white;
    margin-bottom: 2rem;
}

.stat-badge {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.action-card {
    display: block;
    text-decoration: none;
    color: inherit;
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.feature-header {
    padding: 1.5rem;
    text-align: center;
    background: #f8f9fa;
}

.feature-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.feature-footer {
    padding: 1.5rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.step-item {
    text-align: center;
    padding: 1rem;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 1rem;
}
</style>

<?php require_once '../includes/footer.php'; ?>
