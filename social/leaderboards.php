<?php
/**
 * Social Leaderboards & Competitions
 * Gamified ranking system for beer enthusiasts
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Leaderboards - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css', '../assets/css/leaderboards.css'];
$additionalJS = ['../assets/js/leaderboards.js'];

$user = getCurrentUser();
$currentTab = $_GET['tab'] ?? 'overall';
$timeframe = $_GET['timeframe'] ?? 'all_time';

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Build time filter
    $timeFilter = '';
    $timeParams = [];

    switch ($timeframe) {
        case 'week':
            $timeFilter = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)';
            break;
        case 'month':
            $timeFilter = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)';
            break;
        case 'year':
            $timeFilter = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)';
            break;
        default:
            $timeFilter = '';
    }

    // Overall Leaderboard (Social Score)
    $overallQuery = "
        SELECT
            p.*,
            us.social_score,
            us.total_checkins,
            us.total_reviews,
            us.total_badges,
            us.unique_beers_tried,
            us.breweries_visited,
            COUNT(DISTINCT uf.follower_id) as followers_count,
            ROW_NUMBER() OVER (ORDER BY us.social_score DESC) as rank
        FROM profiles p
        JOIN user_statistics us ON p.id = us.user_id
        LEFT JOIN user_follows uf ON p.id = uf.following_id AND uf.status = 'accepted'
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        ORDER BY us.social_score DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($overallQuery);
    $stmt->execute();
    $overallLeaderboard = $stmt->fetchAll();

    // Check-ins Leaderboard
    $checkinsQuery = "
        SELECT
            p.*,
            COUNT(bc.id) as checkin_count,
            COUNT(DISTINCT bc.beer_id) as unique_beers,
            COUNT(DISTINCT bc.brewery_id) as unique_breweries,
            ROW_NUMBER() OVER (ORDER BY COUNT(bc.id) DESC) as rank
        FROM profiles p
        LEFT JOIN beer_checkins bc ON p.id = bc.user_id $timeFilter
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        HAVING checkin_count > 0
        ORDER BY checkin_count DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($checkinsQuery);
    $stmt->execute();
    $checkinsLeaderboard = $stmt->fetchAll();

    // Reviews Leaderboard
    $reviewsQuery = "
        SELECT
            p.*,
            COUNT(br.id) as review_count,
            AVG(br.rating) as avg_rating,
            COUNT(DISTINCT br.beer_id) as beers_reviewed,
            ROW_NUMBER() OVER (ORDER BY COUNT(br.id) DESC) as rank
        FROM profiles p
        LEFT JOIN beer_reviews br ON p.id = br.user_id $timeFilter
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        HAVING review_count > 0
        ORDER BY review_count DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($reviewsQuery);
    $stmt->execute();
    $reviewsLeaderboard = $stmt->fetchAll();

    // Badges Leaderboard
    $badgesQuery = "
        SELECT
            p.*,
            COUNT(ub.id) as badge_count,
            COUNT(DISTINCT b.category) as badge_categories,
            MAX(ub.earned_at) as latest_badge,
            ROW_NUMBER() OVER (ORDER BY COUNT(ub.id) DESC) as rank
        FROM profiles p
        LEFT JOIN user_badges ub ON p.id = ub.user_id $timeFilter
        LEFT JOIN badges b ON ub.badge_id = b.id
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        HAVING badge_count > 0
        ORDER BY badge_count DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($badgesQuery);
    $stmt->execute();
    $badgesLeaderboard = $stmt->fetchAll();

    // Social Leaderboard (Followers)
    $socialQuery = "
        SELECT
            p.*,
            COUNT(DISTINCT uf1.follower_id) as followers_count,
            COUNT(DISTINCT uf2.following_id) as following_count,
            COUNT(DISTINCT ua.id) as activities_count,
            ROW_NUMBER() OVER (ORDER BY COUNT(DISTINCT uf1.follower_id) DESC) as rank
        FROM profiles p
        LEFT JOIN user_follows uf1 ON p.id = uf1.following_id AND uf1.status = 'accepted'
        LEFT JOIN user_follows uf2 ON p.id = uf2.follower_id AND uf2.status = 'accepted'
        LEFT JOIN user_activities ua ON p.id = ua.user_id $timeFilter
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        HAVING followers_count > 0
        ORDER BY followers_count DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($socialQuery);
    $stmt->execute();
    $socialLeaderboard = $stmt->fetchAll();

    // Explorer Leaderboard (Unique Breweries)
    $explorerQuery = "
        SELECT
            p.*,
            COUNT(DISTINCT bc.brewery_id) as breweries_visited,
            COUNT(DISTINCT bc.beer_id) as unique_beers,
            COUNT(bc.id) as total_checkins,
            ROW_NUMBER() OVER (ORDER BY COUNT(DISTINCT bc.brewery_id) DESC) as rank
        FROM profiles p
        LEFT JOIN beer_checkins bc ON p.id = bc.user_id $timeFilter
        WHERE p.profile_visibility = 'public'
        GROUP BY p.id
        HAVING breweries_visited > 0
        ORDER BY breweries_visited DESC
        LIMIT 50
    ";
    $stmt = $conn->prepare($explorerQuery);
    $stmt->execute();
    $explorerLeaderboard = $stmt->fetchAll();

    // Get user's position in each leaderboard
    $userPositions = [];
    foreach (['overall', 'checkins', 'reviews', 'badges', 'social', 'explorer'] as $board) {
        $leaderboard = ${$board . 'Leaderboard'};
        foreach ($leaderboard as $index => $entry) {
            if ($entry['id'] === $user['id']) {
                $userPositions[$board] = $index + 1;
                break;
            }
        }
    }

} catch (Exception $e) {
    error_log("Leaderboards error: " . $e->getMessage());
    $overallLeaderboard = [];
    $checkinsLeaderboard = [];
    $reviewsLeaderboard = [];
    $badgesLeaderboard = [];
    $socialLeaderboard = [];
    $explorerLeaderboard = [];
    $userPositions = [];
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-trophy me-2 text-warning"></i>Leaderboards
                    </h1>
                    <p class="text-muted mb-0">Compete with fellow beer enthusiasts and climb the rankings</p>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button"
                            data-bs-toggle="dropdown">
                        <i class="fas fa-clock me-2"></i>
                        <?php
                        $timeLabels = [
                            'all_time' => 'All Time',
                            'year' => 'This Year',
                            'month' => 'This Month',
                            'week' => 'This Week'
                        ];
                        echo $timeLabels[$timeframe];
                        ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?tab=<?php echo $currentTab; ?>&timeframe=all_time">All Time</a></li>
                        <li><a class="dropdown-item" href="?tab=<?php echo $currentTab; ?>&timeframe=year">This Year</a></li>
                        <li><a class="dropdown-item" href="?tab=<?php echo $currentTab; ?>&timeframe=month">This Month</a></li>
                        <li><a class="dropdown-item" href="?tab=<?php echo $currentTab; ?>&timeframe=week">This Week</a></li>
                    </ul>
                </div>
            </div>

            <!-- User's Current Position -->
            <div class="card mb-4 user-position-card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-warning">
                                    #<?php echo $userPositions['overall'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Overall</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-primary">
                                    #<?php echo $userPositions['checkins'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Check-ins</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-success">
                                    #<?php echo $userPositions['reviews'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Reviews</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-info">
                                    #<?php echo $userPositions['badges'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Badges</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-danger">
                                    #<?php echo $userPositions['social'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Social</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="position-stat">
                                <div class="h4 mb-0 text-secondary">
                                    #<?php echo $userPositions['explorer'] ?? '—'; ?>
                                </div>
                                <small class="text-muted">Explorer</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leaderboard Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'overall' ? 'active' : ''; ?>"
                       href="?tab=overall&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-crown me-2"></i>Overall
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'checkins' ? 'active' : ''; ?>"
                       href="?tab=checkins&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-map-pin me-2"></i>Check-ins
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'reviews' ? 'active' : ''; ?>"
                       href="?tab=reviews&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-star me-2"></i>Reviews
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'badges' ? 'active' : ''; ?>"
                       href="?tab=badges&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-medal me-2"></i>Badges
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'social' ? 'active' : ''; ?>"
                       href="?tab=social&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-users me-2"></i>Social
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentTab === 'explorer' ? 'active' : ''; ?>"
                       href="?tab=explorer&timeframe=<?php echo $timeframe; ?>">
                        <i class="fas fa-compass me-2"></i>Explorer
                    </a>
                </li>
            </ul>

            <!-- Leaderboard Content -->
            <div class="leaderboard-content">
                <?php
                $currentLeaderboard = ${$currentTab . 'Leaderboard'};
                if (!empty($currentLeaderboard)):
                ?>
                    <div class="leaderboard-list">
                        <?php foreach ($currentLeaderboard as $index => $entry): ?>
                            <div class="leaderboard-item <?php echo $entry['id'] === $user['id'] ? 'current-user' : ''; ?>"
                                 data-rank="<?php echo $index + 1; ?>">
                                <div class="rank-badge">
                                    <?php if ($index < 3): ?>
                                        <i class="fas fa-trophy rank-<?php echo $index + 1; ?>"></i>
                                    <?php else: ?>
                                        <span class="rank-number">#<?php echo $index + 1; ?></span>
                                    <?php endif; ?>
                                </div>

                                <div class="user-info">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3">
                                            <?php if ($entry['avatar']): ?>
                                                <img src="<?php echo htmlspecialchars($entry['avatar']); ?>"
                                                     class="rounded-circle" width="50" height="50" alt="Avatar">
                                            <?php else: ?>
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="user-details">
                                            <h6 class="mb-0">
                                                <?php echo htmlspecialchars($entry['first_name'] . ' ' . $entry['last_name']); ?>
                                                <?php if ($entry['id'] === $user['id']): ?>
                                                    <span class="badge bg-primary ms-2">You</span>
                                                <?php endif; ?>
                                            </h6>
                                            <small class="text-muted">@<?php echo htmlspecialchars($entry['username']); ?></small>
                                        </div>
                                    </div>
                                </div>

                                <div class="stats">
                                    <?php echo renderLeaderboardStats($entry, $currentTab); ?>
                                </div>

                                <div class="actions">
                                    <?php if ($entry['id'] !== $user['id']): ?>
                                        <button class="btn btn-sm btn-outline-primary"
                                                onclick="viewProfile('<?php echo $entry['id']; ?>')">
                                            <i class="fas fa-eye me-1"></i>View
                                        </button>
                                        <button class="btn btn-sm btn-outline-success"
                                                onclick="followUser('<?php echo $entry['id']; ?>')">
                                            <i class="fas fa-user-plus me-1"></i>Follow
                                        </button>
                                    <?php else: ?>
                                        <a href="../user/profile.php" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit me-1"></i>Edit Profile
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h4>No Rankings Available</h4>
                        <p class="text-muted">Be the first to start competing in this category!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Competition Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-fire me-2"></i>Active Competitions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="competition-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Monthly Check-in Challenge</strong>
                                <div class="small text-muted">50 check-ins to win</div>
                            </div>
                            <span class="badge bg-warning">5 days left</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                        <small class="text-muted">30/50 check-ins</small>
                    </div>

                    <div class="competition-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Style Explorer</strong>
                                <div class="small text-muted">Try 10 new styles</div>
                            </div>
                            <span class="badge bg-success">12 days left</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar bg-success" style="width: 70%"></div>
                        </div>
                        <small class="text-muted">7/10 styles</small>
                    </div>

                    <a href="challenges.php" class="btn btn-outline-primary w-100">
                        <i class="fas fa-trophy me-2"></i>View All Challenges
                    </a>
                </div>
            </div>

            <!-- Achievement Progress -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-target me-2"></i>Next Achievements
                    </h6>
                </div>
                <div class="card-body">
                    <div class="achievement-item mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-beer fa-2x text-warning me-3"></i>
                            <div>
                                <strong>Beer Connoisseur</strong>
                                <div class="small text-muted">Rate 100 beers</div>
                            </div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 85%"></div>
                        </div>
                        <small class="text-muted">85/100 ratings</small>
                    </div>

                    <div class="achievement-item mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-map-marked-alt fa-2x text-info me-3"></i>
                            <div>
                                <strong>Brewery Explorer</strong>
                                <div class="small text-muted">Visit 25 breweries</div>
                            </div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 64%"></div>
                        </div>
                        <small class="text-muted">16/25 breweries</small>
                    </div>

                    <a href="../badges.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-medal me-2"></i>View All Badges
                    </a>
                </div>
            </div>

            <!-- Leaderboard Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Community Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Competitors</span>
                            <strong><?php echo count($overallLeaderboard); ?></strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Active This Week</span>
                            <strong>127</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>New Records Set</span>
                            <strong>8</strong>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="d-flex justify-content-between">
                            <span>Your Rank Change</span>
                            <strong class="text-success">+3 ↗</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function renderLeaderboardStats($entry, $tab) {
    switch ($tab) {
        case 'overall':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['social_score']) . "</div>
                        <div class='stat-label'>Social Score</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['total_checkins'] ?? 0) . "</div>
                        <div class='stat-label'>Check-ins</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['total_badges'] ?? 0) . "</div>
                        <div class='stat-label'>Badges</div>
                    </div>
                </div>
            ";
        case 'checkins':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['checkin_count']) . "</div>
                        <div class='stat-label'>Check-ins</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['unique_beers'] ?? 0) . "</div>
                        <div class='stat-label'>Unique Beers</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['unique_breweries'] ?? 0) . "</div>
                        <div class='stat-label'>Breweries</div>
                    </div>
                </div>
            ";
        case 'reviews':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['review_count']) . "</div>
                        <div class='stat-label'>Reviews</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['avg_rating'], 1) . "</div>
                        <div class='stat-label'>Avg Rating</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['beers_reviewed'] ?? 0) . "</div>
                        <div class='stat-label'>Beers Reviewed</div>
                    </div>
                </div>
            ";
        case 'badges':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['badge_count']) . "</div>
                        <div class='stat-label'>Total Badges</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['badge_categories'] ?? 0) . "</div>
                        <div class='stat-label'>Categories</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['latest_badge'] ? timeAgo($entry['latest_badge']) : 'None') . "</div>
                        <div class='stat-label'>Latest</div>
                    </div>
                </div>
            ";
        case 'social':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['followers_count']) . "</div>
                        <div class='stat-label'>Followers</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['following_count'] ?? 0) . "</div>
                        <div class='stat-label'>Following</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['activities_count'] ?? 0) . "</div>
                        <div class='stat-label'>Activities</div>
                    </div>
                </div>
            ";
        case 'explorer':
            return "
                <div class='stat-group'>
                    <div class='stat-item'>
                        <div class='stat-value'>" . number_format($entry['breweries_visited']) . "</div>
                        <div class='stat-label'>Breweries</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['unique_beers'] ?? 0) . "</div>
                        <div class='stat-label'>Unique Beers</div>
                    </div>
                    <div class='stat-item'>
                        <div class='stat-value'>" . ($entry['total_checkins'] ?? 0) . "</div>
                        <div class='stat-label'>Check-ins</div>
                    </div>
                </div>
            ";
        default:
            return '';
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    return date('M j', strtotime($datetime));
}

require_once '../includes/footer.php';
?>