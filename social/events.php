<?php
/**
 * Events & Meetups Management
 * Social events system for beer enthusiasts
 */

require_once '../config/config.php';
requireLogin();

$pageTitle = 'Beer Events & Meetups - ' . APP_NAME;
$additionalCSS = ['../assets/css/social.css'];
$additionalJS = ['../assets/js/events.js'];

// Initialize database connection
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$user = getCurrentUser();

// Get full user profile
function getUserFullProfile($userId) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("
            SELECT u.*, p.*
            FROM users u
            JOIN profiles p ON u.id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

$userProfile = getUserFullProfile($user['id']);
$currentPage = $_GET['page'] ?? 1;
$eventsPerPage = 12;
$offset = ($currentPage - 1) * $eventsPerPage;

// Get filter parameters
$eventType = $_GET['type'] ?? '';
$location = $_GET['location'] ?? '';
$dateFilter = $_GET['date'] ?? 'upcoming';
$searchQuery = $_GET['search'] ?? '';

// Build WHERE clause for filters
$whereConditions = ["e.status = 'published'"];
$params = [];

if (!empty($eventType)) {
    $whereConditions[] = "e.event_type = ?";
    $params[] = $eventType;
}

if (!empty($location)) {
    $whereConditions[] = "(e.location_name LIKE ? OR e.address LIKE ?)";
    $params[] = "%$location%";
    $params[] = "%$location%";
}

if (!empty($searchQuery)) {
    $whereConditions[] = "(e.title LIKE ? OR e.description LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

// Date filtering
switch ($dateFilter) {
    case 'today':
        $whereConditions[] = "DATE(e.start_datetime) = CURDATE()";
        break;
    case 'this_week':
        $whereConditions[] = "YEARWEEK(e.start_datetime) = YEARWEEK(CURDATE())";
        break;
    case 'this_month':
        $whereConditions[] = "YEAR(e.start_datetime) = YEAR(CURDATE()) AND MONTH(e.start_datetime) = MONTH(CURDATE())";
        break;
    case 'upcoming':
    default:
        $whereConditions[] = "e.start_datetime >= NOW()";
        break;
}

$whereClause = implode(' AND ', $whereConditions);

try {
    // Get events with creator and RSVP info
    $eventsQuery = "
        SELECT 
            e.*,
            p.first_name, p.last_name, p.username, p.avatar,
            b.name as brewery_name,
            COUNT(DISTINCT er.id) as rsvp_count,
            COUNT(DISTINCT CASE WHEN er.rsvp_status = 'going' THEN er.id END) as going_count,
            MAX(CASE WHEN er.user_id = ? THEN er.rsvp_status END) as user_rsvp_status
        FROM events e
        JOIN profiles p ON e.creator_id = p.id
        LEFT JOIN breweries b ON e.brewery_id = b.id
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE $whereClause
        GROUP BY e.id
        ORDER BY e.start_datetime ASC
        LIMIT $eventsPerPage OFFSET $offset
    ";
    
    $stmt = $conn->prepare($eventsQuery);
    $stmt->execute(array_merge([$user['id']], $params));
    $events = $stmt->fetchAll();
    
    // Get total count for pagination
    $countQuery = "
        SELECT COUNT(DISTINCT e.id)
        FROM events e
        WHERE $whereClause
    ";
    $stmt = $conn->prepare($countQuery);
    $stmt->execute($params);
    $totalEvents = $stmt->fetchColumn();
    $totalPages = ceil($totalEvents / $eventsPerPage);
    
    // Get user's upcoming events
    $userEventsQuery = "
        SELECT e.*, er.rsvp_status
        FROM events e
        JOIN event_rsvps er ON e.id = er.event_id
        WHERE er.user_id = ? AND e.start_datetime >= NOW() AND e.status = 'published'
        ORDER BY e.start_datetime ASC
        LIMIT 5
    ";
    $stmt = $conn->prepare($userEventsQuery);
    $stmt->execute([$user['id']]);
    $userEvents = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Events page error: " . $e->getMessage());
    $events = [];
    $userEvents = [];
    $totalEvents = 0;
    $totalPages = 1;
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Beer Events & Meetups
                    </h1>
                    <p class="text-muted mb-0">Discover and join beer events in your area</p>
                </div>
                <div>
                    <a href="create-event.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Event
                    </a>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search Events</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="Event name or description">
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">Event Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="brewery_tour" <?php echo $eventType === 'brewery_tour' ? 'selected' : ''; ?>>Brewery Tour</option>
                                <option value="beer_tasting" <?php echo $eventType === 'beer_tasting' ? 'selected' : ''; ?>>Beer Tasting</option>
                                <option value="meetup" <?php echo $eventType === 'meetup' ? 'selected' : ''; ?>>Meetup</option>
                                <option value="festival" <?php echo $eventType === 'festival' ? 'selected' : ''; ?>>Festival</option>
                                <option value="competition" <?php echo $eventType === 'competition' ? 'selected' : ''; ?>>Competition</option>
                                <option value="educational" <?php echo $eventType === 'educational' ? 'selected' : ''; ?>>Educational</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date" class="form-label">When</label>
                            <select class="form-select" id="date" name="date">
                                <option value="upcoming" <?php echo $dateFilter === 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                                <option value="today" <?php echo $dateFilter === 'today' ? 'selected' : ''; ?>>Today</option>
                                <option value="this_week" <?php echo $dateFilter === 'this_week' ? 'selected' : ''; ?>>This Week</option>
                                <option value="this_month" <?php echo $dateFilter === 'this_month' ? 'selected' : ''; ?>>This Month</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="<?php echo htmlspecialchars($location); ?>" 
                                   placeholder="City or venue">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Events Grid -->
            <?php if (!empty($events)): ?>
                <div class="row">
                    <?php foreach ($events as $event): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card event-card h-100">
                                <?php if ($event['event_image']): ?>
                                    <img src="<?php echo htmlspecialchars($event['event_image']); ?>" 
                                         class="card-img-top" alt="Event Image" style="height: 200px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                         style="height: 200px;">
                                        <i class="fas fa-calendar-alt fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body d-flex flex-column">
                                    <div class="mb-2">
                                        <span class="badge bg-primary"><?php echo ucwords(str_replace('_', ' ', $event['event_type'])); ?></span>
                                        <?php if ($event['price'] > 0): ?>
                                            <span class="badge bg-success">$<?php echo number_format($event['price'], 2); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Free</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <h5 class="card-title"><?php echo htmlspecialchars($event['title']); ?></h5>
                                    
                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('M j, Y g:i A', strtotime($event['start_datetime'])); ?>
                                    </div>
                                    
                                    <?php if ($event['location_name']): ?>
                                        <div class="text-muted small mb-2">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($event['location_name']); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($event['brewery_name']): ?>
                                        <div class="text-muted small mb-2">
                                            <i class="fas fa-building me-1"></i>
                                            <?php echo htmlspecialchars($event['brewery_name']); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <p class="card-text flex-grow-1">
                                        <?php echo htmlspecialchars(substr($event['description'], 0, 100)); ?>
                                        <?php if (strlen($event['description']) > 100): ?>...<?php endif; ?>
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <div class="small text-muted">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $event['going_count']; ?> going
                                        </div>
                                        
                                        <div class="btn-group">
                                            <a href="event-detail.php?id=<?php echo $event['id']; ?>" 
                                               class="btn btn-outline-primary btn-sm">View</a>
                                            
                                            <?php if ($event['user_rsvp_status']): ?>
                                                <button class="btn btn-sm <?php echo $event['user_rsvp_status'] === 'going' ? 'btn-success' : 'btn-outline-secondary'; ?>" 
                                                        onclick="toggleRSVP('<?php echo $event['id']; ?>')">
                                                    <?php echo ucfirst($event['user_rsvp_status']); ?>
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-outline-success btn-sm" 
                                                        onclick="toggleRSVP('<?php echo $event['id']; ?>')">
                                                    RSVP
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Events pagination">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4>No Events Found</h4>
                    <p class="text-muted">Try adjusting your filters or create a new event!</p>
                    <a href="create-event.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Event
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Your Events -->
            <?php if (!empty($userEvents)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>Your Upcoming Events
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <?php foreach ($userEvents as $userEvent): ?>
                            <div class="d-flex p-3 border-bottom">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-primary text-white rounded text-center p-2" style="min-width: 50px;">
                                        <div class="small"><?php echo date('M', strtotime($userEvent['start_datetime'])); ?></div>
                                        <div class="fw-bold"><?php echo date('j', strtotime($userEvent['start_datetime'])); ?></div>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="event-detail.php?id=<?php echo $userEvent['id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($userEvent['title']); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo date('g:i A', strtotime($userEvent['start_datetime'])); ?>
                                        <span class="badge bg-<?php echo $userEvent['rsvp_status'] === 'going' ? 'success' : 'secondary'; ?> ms-2">
                                            <?php echo ucfirst($userEvent['rsvp_status']); ?>
                                        </span>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="create-event.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Event
                        </a>
                        <a href="my-events.php" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt me-2"></i>My Events
                        </a>
                        <a href="challenges.php" class="btn btn-outline-success">
                            <i class="fas fa-trophy me-2"></i>Challenges
                        </a>
                        <a href="clubs.php" class="btn btn-outline-info">
                            <i class="fas fa-users me-2"></i>Beer Clubs
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Event Types -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>Event Types
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="?type=brewery_tour" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-route me-2"></i>Brewery Tours</span>
                            <span class="badge bg-primary rounded-pill">12</span>
                        </a>
                        <a href="?type=beer_tasting" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-wine-glass me-2"></i>Beer Tastings</span>
                            <span class="badge bg-primary rounded-pill">8</span>
                        </a>
                        <a href="?type=meetup" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-handshake me-2"></i>Meetups</span>
                            <span class="badge bg-primary rounded-pill">15</span>
                        </a>
                        <a href="?type=festival" class="list-group-item list-group-item-action d-flex justify-content-between">
                            <span><i class="fas fa-music me-2"></i>Festivals</span>
                            <span class="badge bg-primary rounded-pill">3</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRSVP(eventId) {
    fetch('/api/event-rsvp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            event_id: eventId,
            action: 'toggle_rsvp'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating RSVP: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating RSVP');
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
