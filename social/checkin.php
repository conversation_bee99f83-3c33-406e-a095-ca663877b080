<?php
require_once '../config/config.php';
require_once '../includes/BadgeService.php';

// Require login
requireLogin();

$pageTitle = 'Check In - ' . APP_NAME;
$additionalCSS = ['/assets/css/checkin.css', '/assets/css/social.css', '/assets/css/lists.css'];
$additionalJS = ['/assets/js/photo-gallery.js'];

$user = getCurrentUser();
$beerId = sanitizeInput($_GET['beer_id'] ?? '');
$beer = null;
$errors = [];

// If beer ID provided, get beer details
if (!empty($beerId)) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            SELECT bm.*, b.name as brewery_name, b.city, b.state,
                   bs.name as style_name, bs.category as style_category
            FROM beer_menu bm 
            LEFT JOIN breweries b ON bm.brewery_id = b.id 
            LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
            WHERE bm.id = ?
        ");
        $stmt->execute([$beerId]);
        $beer = $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Beer lookup error: " . $e->getMessage());
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selectedBeerId = sanitizeInput($_POST['beer_id'] ?? '');
    $checkinLocation = sanitizeInput($_POST['checkin_location'] ?? '');
    $checkinLatitude = $_POST['checkin_latitude'] ?? null;
    $checkinLongitude = $_POST['checkin_longitude'] ?? null;
    $servingStyle = $_POST['serving_style'] ?? null;
    $checkinComment = sanitizeInput($_POST['checkin_comment'] ?? '');
    $isPublic = isset($_POST['is_public']) ? 1 : 0;
    
    // Rating fields (optional)
    $overallRating = !empty($_POST['overall_rating']) ? (float)$_POST['overall_rating'] : null;
    $reviewText = sanitizeInput($_POST['review_text'] ?? '');
    
    // Validation
    if (empty($selectedBeerId)) {
        $errors[] = 'Please select a beer to check in.';
    }
    
    if ($overallRating !== null && ($overallRating < 0.5 || $overallRating > 5.0)) {
        $errors[] = 'Rating must be between 0.5 and 5.0.';
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // Get beer details
            $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE id = ?");
            $stmt->execute([$selectedBeerId]);
            $selectedBeer = $stmt->fetch();
            
            if (!$selectedBeer) {
                $errors[] = 'Selected beer not found.';
            } else {
                $conn->beginTransaction();
                
                $ratingId = null;
                
                // If rating provided, create/update rating
                if ($overallRating !== null) {
                    // Check for existing rating
                    $stmt = $conn->prepare("SELECT id FROM beer_ratings WHERE user_id = ? AND beer_id = ?");
                    $stmt->execute([$user['id'], $selectedBeerId]);
                    $existingRating = $stmt->fetch();
                    
                    if ($existingRating) {
                        // Update existing rating
                        $stmt = $conn->prepare("
                            UPDATE beer_ratings SET 
                                overall_rating = ?, review_text = ?, 
                                checkin_location = ?, serving_style = ?,
                                is_public = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->execute([
                            $overallRating, $reviewText, $checkinLocation, 
                            $servingStyle, $isPublic, $existingRating['id']
                        ]);
                        $ratingId = $existingRating['id'];
                    } else {
                        // Create new rating
                        $stmt = $conn->prepare("
                            INSERT INTO beer_ratings 
                            (user_id, beer_id, brewery_id, overall_rating, review_text, 
                             checkin_location, serving_style, is_public) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $user['id'], $selectedBeerId, $selectedBeer['brewery_id'],
                            $overallRating, $reviewText, $checkinLocation, 
                            $servingStyle, $isPublic
                        ]);
                        $ratingId = $conn->lastInsertId();
                    }
                    
                    // Update beer statistics
                    $stmt = $conn->prepare("
                        UPDATE beer_menu SET 
                            average_rating = (
                                SELECT AVG(overall_rating) 
                                FROM beer_ratings 
                                WHERE beer_id = ? AND is_public = 1
                            ),
                            total_ratings = (
                                SELECT COUNT(*) 
                                FROM beer_ratings 
                                WHERE beer_id = ? AND is_public = 1
                            )
                        WHERE id = ?
                    ");
                    $stmt->execute([$selectedBeerId, $selectedBeerId, $selectedBeerId]);
                }
                
                // Create check-in
                $stmt = $conn->prepare("
                    INSERT INTO beer_checkins 
                    (user_id, beer_id, brewery_id, rating_id, checkin_location, 
                     checkin_latitude, checkin_longitude, serving_style, 
                     checkin_comment, is_public) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $user['id'], $selectedBeerId, $selectedBeer['brewery_id'], $ratingId,
                    $checkinLocation, $checkinLatitude, $checkinLongitude, 
                    $servingStyle, $checkinComment, $isPublic
                ]);
                
                $checkinId = $conn->lastInsertId();
                
                // Update user stats
                $stmt = $conn->prepare("UPDATE profiles SET total_checkins = total_checkins + 1 WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                // Update beer check-in count
                $stmt = $conn->prepare("UPDATE beer_menu SET total_checkins = total_checkins + 1 WHERE id = ?");
                $stmt->execute([$selectedBeerId]);
                
                // Log activity
                if ($isPublic) {
                    $stmt = $conn->prepare("
                        INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata) 
                        VALUES (?, 'beer_checkin', 'beer', ?, ?)
                    ");
                    $stmt->execute([
                        $user['id'], $selectedBeerId,
                        json_encode([
                            'beer_name' => $selectedBeer['name'],
                            'brewery_name' => $selectedBeer['brewery_name'] ?? '',
                            'location' => $checkinLocation,
                            'rating' => $overallRating,
                            'checkin_id' => $checkinId
                        ])
                    ]);
                }
                
                // Check for new badges
                $badgeService = new BadgeService($conn);
                $newBadges = $badgeService->checkAndAwardBadges($user['id'], 'beer_checkin', [
                    'beer_id' => $selectedBeerId,
                    'brewery_id' => $selectedBeer['brewery_id'],
                    'rating' => $overallRating,
                    'location' => $checkinLocation
                ]);

                $conn->commit();

                $successMessage = 'Check-in successful!';
                if (!empty($newBadges)) {
                    $badgeNames = array_column($newBadges, 'name');
                    $successMessage .= ' 🏆 New badge' . (count($newBadges) > 1 ? 's' : '') . ' earned: ' . implode(', ', $badgeNames);
                }

                $_SESSION['success_message'] = $successMessage;
                redirect('/social/feed.php');
            }
            
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            error_log("Check-in error: " . $e->getMessage());
            $errors[] = 'An error occurred while processing your check-in.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

include '../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>Check In
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="checkinForm">
                        <!-- Beer Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-beer me-2"></i>Select Beer *
                            </label>
                            
                            <?php if ($beer): ?>
                                <!-- Pre-selected beer -->
                                <div class="selected-beer p-3 bg-light rounded">
                                    <input type="hidden" name="beer_id" value="<?php echo $beer['id']; ?>">
                                    <div class="row align-items-center">
                                        <div class="col-md-2 text-center">
                                            <?php if (!empty($beer['thumbnail'])): ?>
                                                <img src="<?php echo htmlspecialchars($beer['thumbnail']); ?>" 
                                                     class="img-fluid beer-thumb" alt="<?php echo htmlspecialchars($beer['name']); ?>">
                                            <?php else: ?>
                                                <div class="beer-thumb-placeholder">
                                                    <i class="fas fa-beer fa-2x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-10">
                                            <h5 class="mb-1"><?php echo htmlspecialchars($beer['name']); ?></h5>
                                            <p class="text-muted mb-1">
                                                <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                                <?php if (!empty($beer['city'])): ?>
                                                    • <?php echo htmlspecialchars($beer['city']); ?><?php if (!empty($beer['state'])): ?>, <?php echo htmlspecialchars($beer['state']); ?><?php endif; ?>
                                                <?php endif; ?>
                                            </p>
                                            <?php if (!empty($beer['style_name'])): ?>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($beer['style_name']); ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($beer['abv'])): ?>
                                                <span class="badge bg-secondary ms-1"><?php echo number_format($beer['abv'], 1); ?>% ABV</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <a href="/social/checkin.php" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-search me-1"></i>Choose Different Beer
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Beer search -->
                                <div class="beer-search">
                                    <input type="text" class="form-control" id="beerSearch" 
                                           placeholder="Search for a beer to check in...">
                                    <input type="hidden" name="beer_id" id="selectedBeerId">
                                    <div id="beerSearchResults" class="search-results"></div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Location -->
                        <div class="mb-3">
                            <label for="checkin_location" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>Location
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="checkin_location" name="checkin_location" 
                                       placeholder="Where are you drinking this beer?">
                                <button type="button" class="btn btn-outline-secondary" id="detectLocation">
                                    <i class="fas fa-crosshairs"></i>
                                </button>
                            </div>
                            <input type="hidden" name="checkin_latitude" id="checkin_latitude">
                            <input type="hidden" name="checkin_longitude" id="checkin_longitude">
                        </div>
                        
                        <!-- Serving Style -->
                        <div class="mb-3">
                            <label for="serving_style" class="form-label">Serving Style</label>
                            <select class="form-select" id="serving_style" name="serving_style">
                                <option value="">Select serving style...</option>
                                <option value="draft">🍺 Draft</option>
                                <option value="bottle">🍾 Bottle</option>
                                <option value="can">🥫 Can</option>
                                <option value="growler">🍺 Growler</option>
                                <option value="other">❓ Other</option>
                            </select>
                        </div>
                        
                        <!-- Comment -->
                        <div class="mb-4">
                            <label for="checkin_comment" class="form-label">Comment</label>
                            <textarea class="form-control" id="checkin_comment" name="checkin_comment" rows="3"
                                      placeholder="How's this beer? Share your thoughts..."></textarea>
                        </div>
                        
                        <!-- Optional Rating -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Rate This Beer (Optional)</h6>
                            <div class="rating-section p-3 bg-light rounded">
                                <div class="rating-input text-center mb-3">
                                    <div class="stars">
                                        <i class="fas fa-star" data-value="1"></i>
                                        <i class="fas fa-star" data-value="2"></i>
                                        <i class="fas fa-star" data-value="3"></i>
                                        <i class="fas fa-star" data-value="4"></i>
                                        <i class="fas fa-star" data-value="5"></i>
                                    </div>
                                    <input type="hidden" name="overall_rating" id="overall_rating">
                                    <div class="rating-text">Click to rate</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="review_text" class="form-label">Review (Optional)</label>
                                    <textarea class="form-control" id="review_text" name="review_text" rows="2"
                                              placeholder="Detailed review of this beer..."></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Photos -->
                        <div class="mb-4">
                            <h6 class="fw-bold">
                                <i class="fas fa-camera me-2"></i>Add Photos (Optional)
                            </h6>
                            <div class="photo-upload-section">
                                <div class="upload-area">
                                    <div class="upload-dropzone" onclick="document.getElementById('checkin-photos').click()">
                                        <i class="fas fa-camera fa-2x text-muted mb-3"></i>
                                        <h6>Add photos to your check-in</h6>
                                        <p class="text-muted">
                                            Share photos of your beer, the location, or the experience
                                        </p>
                                        <p class="text-muted small">Click to browse or drag photos here</p>
                                    </div>

                                    <input type="file"
                                           id="checkin-photos"
                                           name="photos[]"
                                           accept="image/*"
                                           multiple
                                           style="display: none;"
                                           onchange="handleCheckinPhotoSelection()">
                                </div>

                                <!-- Photo Preview -->
                                <div class="file-preview" id="checkin-preview" style="display: none;">
                                    <h6>Selected Photos:</h6>
                                    <div class="preview-grid" id="checkin-preview-grid"></div>
                                    <button type="button" class="btn btn-outline-secondary btn-sm mt-2" onclick="clearCheckinPhotos()">
                                        <i class="fas fa-times me-1"></i>Clear Photos
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Privacy -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" checked>
                                <label class="form-check-label" for="is_public">
                                    <i class="fas fa-globe me-1"></i>Share this check-in publicly
                                </label>
                                <div class="form-text">
                                    Public check-ins appear in your activity feed and help friends discover great beers!
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit -->
                        <div class="d-flex justify-content-between">
                            <a href="/beers/discover.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>Check In
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Beer search functionality
    const beerSearch = document.getElementById('beerSearch');
    const searchResults = document.getElementById('beerSearchResults');
    const selectedBeerId = document.getElementById('selectedBeerId');
    
    if (beerSearch) {
        let searchTimeout;
        
        beerSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            searchTimeout = setTimeout(() => {
                searchBeers(query);
            }, 300);
        });
    }
    
    async function searchBeers(query) {
        try {
            const response = await fetch(`/api/search-beers.php?q=${encodeURIComponent(query)}`);
            const beers = await response.json();
            
            if (beers.length === 0) {
                searchResults.innerHTML = '<div class="p-3 text-muted">No beers found</div>';
                return;
            }
            
            let html = '';
            beers.forEach(beer => {
                html += `
                    <div class="search-result-item p-3 border-bottom" data-beer-id="${beer.id}">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                ${beer.thumbnail ? 
                                    `<img src="${beer.thumbnail}" class="beer-thumb-small" alt="${beer.name}">` :
                                    '<div class="beer-thumb-small-placeholder"><i class="fas fa-beer"></i></div>'
                                }
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">${beer.name}</div>
                                <div class="text-muted small">${beer.brewery_name}</div>
                                ${beer.style_name ? `<span class="badge bg-primary badge-sm">${beer.style_name}</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            searchResults.innerHTML = html;
            
            // Add click handlers
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', function() {
                    const beerId = this.dataset.beerId;
                    const beerName = this.querySelector('.fw-bold').textContent;
                    
                    selectedBeerId.value = beerId;
                    beerSearch.value = beerName;
                    searchResults.innerHTML = '';
                });
            });
            
        } catch (error) {
            console.error('Search error:', error);
            searchResults.innerHTML = '<div class="p-3 text-danger">Search error occurred</div>';
        }
    }
    
    // Location detection
    document.getElementById('detectLocation')?.addEventListener('click', function() {
        if (navigator.geolocation) {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            navigator.geolocation.getCurrentPosition(
                position => {
                    document.getElementById('checkin_latitude').value = position.coords.latitude;
                    document.getElementById('checkin_longitude').value = position.coords.longitude;
                    
                    // Reverse geocoding (simplified)
                    document.getElementById('checkin_location').value = 
                        `${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`;
                    
                    this.innerHTML = '<i class="fas fa-check text-success"></i>';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    }, 2000);
                },
                error => {
                    alert('Unable to detect location');
                    this.innerHTML = '<i class="fas fa-crosshairs"></i>';
                }
            );
        } else {
            alert('Geolocation is not supported by this browser');
        }
    });
    
    // Rating functionality
    const stars = document.querySelectorAll('.stars i');
    const ratingInput = document.getElementById('overall_rating');
    const ratingText = document.querySelector('.rating-text');
    
    stars.forEach((star, index) => {
        star.addEventListener('click', () => {
            const value = index + 1;
            ratingInput.value = value;
            updateStars(value);
            updateRatingText(value);
        });
        
        star.addEventListener('mouseenter', () => {
            updateStars(index + 1, true);
        });
    });
    
    document.querySelector('.rating-input').addEventListener('mouseleave', () => {
        const currentValue = parseInt(ratingInput.value) || 0;
        updateStars(currentValue);
    });
    
    function updateStars(value, isHover = false) {
        stars.forEach((star, index) => {
            if (index < value) {
                star.classList.add('active');
                if (isHover) star.classList.add('hover');
            } else {
                star.classList.remove('active', 'hover');
            }
        });
    }
    
    function updateRatingText(value) {
        const labels = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
        ratingText.textContent = value > 0 ? `${value}/5 - ${labels[value]}` : 'Click to rate';
    }

    // Photo upload functionality
    window.handleCheckinPhotoSelection = function() {
        const fileInput = document.getElementById('checkin-photos');
        const files = fileInput.files;

        if (files.length > 0) {
            displayFilePreview(files, 'checkin-preview-grid');
            document.getElementById('checkin-preview').style.display = 'block';
        } else {
            clearCheckinPhotos();
        }
    };

    window.clearCheckinPhotos = function() {
        const fileInput = document.getElementById('checkin-photos');
        fileInput.value = '';
        document.getElementById('checkin-preview').style.display = 'none';
        document.getElementById('checkin-preview-grid').innerHTML = '';
    };

    // Display file preview function
    function displayFilePreview(files, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    previewItem.innerHTML = `
                        <img src="${e.target.result}" alt="${file.name}" class="preview-image">
                        <button type="button" class="preview-remove" onclick="removePreviewItem(this, ${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    container.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Remove preview item function
    window.removePreviewItem = function(button, index) {
        button.parentElement.remove();
    };
});
</script>

<?php include '../includes/footer.php'; ?>
