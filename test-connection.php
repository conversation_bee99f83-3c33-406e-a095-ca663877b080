<?php
/**
 * Test Database Connection
 * Quick test to verify XAMPP and database setup
 */

echo "<h1>Beersty - Connection Test</h1>";

// Test PHP version
echo "<h2>PHP Version</h2>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";

if (version_compare(PHP_VERSION, '8.0.0', '>=')) {
    echo "<p style='color: green;'>✓ PHP version is compatible</p>";
} else {
    echo "<p style='color: red;'>✗ PHP version too old (need 8.0+)</p>";
}

// Test required extensions
echo "<h2>Required Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'session'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext extension loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext extension missing</p>";
    }
}

// Test database connection
echo "<h2>Database Connection</h2>";

try {
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'beersty_db'");
    $stmt->execute();
    
    if ($stmt->fetch()) {
        echo "<p style='color: green;'>✓ beersty_db database exists</p>";
        
        // Test table access
        $pdo->exec("USE beersty_db");
        $stmt = $pdo->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ Database tables found: " . implode(', ', $tables) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Database exists but no tables found</p>";
            echo "<p>Please run the SQL script to create tables:</p>";
            echo "<ol>";
            echo "<li>Open <a href='http://localhost/phpmyadmin/' target='_blank'>phpMyAdmin</a></li>";
            echo "<li>Import the create-database.sql file</li>";
            echo "<li>Or copy and paste the SQL from database/schema.sql</li>";
            echo "</ol>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ beersty_db database not found</p>";
        echo "<p>Creating database...</p>";
        
        try {
            $pdo->exec("CREATE DATABASE beersty_db");
            echo "<p style='color: green;'>✓ Database created successfully</p>";
            echo "<p>Now please import the schema from database/schema.sql</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Could not create database: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Make sure MySQL is running in XAMPP Control Panel</p>";
}

// Test file permissions
echo "<h2>File Permissions</h2>";

$dirs = ['uploads', 'config'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p style='color: green;'>✓ $dir directory is writable</p>";
        } else {
            echo "<p style='color: orange;'>⚠ $dir directory is not writable</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ $dir directory does not exist</p>";
        if (mkdir($dir, 0777, true)) {
            echo "<p style='color: green;'>✓ Created $dir directory</p>";
        }
    }
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>If all tests pass, visit <a href='index.php'>the main application</a></li>";
echo "<li>If database setup is needed, run the SQL script in phpMyAdmin</li>";
echo "<li>Login with: <EMAIL> / admin123</li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Application</a> | <a href='phpinfo.php'>View PHP Info</a></p>";
?>
