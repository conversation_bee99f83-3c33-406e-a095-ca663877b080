-- Badge Data for Beersty Gamification System
-- Comprehensive badge system with various categories and earning criteria

-- Explorer Badges (Brewery and Location-based)
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, rarity, points_value) VALUES
('badge-first-checkin', 'First Check-in', 'Welcome to Beersty! You\'ve made your first beer check-in.', 'explorer', '🍺', '#28a745', 'checkin_count', 1, 'common', 10),
('badge-regular-drinker', 'Regular Drinker', 'You\'ve checked in 10 different beers. Keep exploring!', 'explorer', '🍻', '#007bff', 'checkin_count', 10, 'common', 25),
('badge-beer-explorer', 'Beer Explorer', 'Impressive! You\'ve tried 50 different beers.', 'explorer', '🗺️', '#17a2b8', 'beer_count', 50, 'uncommon', 50),
('badge-beer-adventurer', 'Beer Adventurer', 'Amazing! You\'ve sampled 100 unique beers.', 'explorer', '🧭', '#fd7e14', 'beer_count', 100, 'rare', 100),
('badge-beer-master', 'Beer Master', 'Legendary! You\'ve experienced 250 different beers.', 'explorer', '👑', '#6f42c1', 'beer_count', 250, 'epic', 250),
('badge-brewery-hopper', 'Brewery Hopper', 'You\'ve visited 5 different breweries. Nice exploration!', 'explorer', '🏭', '#20c997', 'brewery_count', 5, 'common', 30),
('badge-brewery-tourist', 'Brewery Tourist', 'You\'ve been to 15 breweries. You love discovering new places!', 'explorer', '🚌', '#ffc107', 'brewery_count', 15, 'uncommon', 75),
('badge-brewery-pilgrim', 'Brewery Pilgrim', 'Incredible! You\'ve visited 30 breweries across your journey.', 'explorer', '⛩️', '#e83e8c', 'brewery_count', 30, 'rare', 150);

-- Connoisseur Badges (Beer Style and Quality-based)
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, criteria_metadata, rarity, points_value) VALUES
('badge-ipa-lover', 'IPA Enthusiast', 'You\'ve tried 10 different IPA styles. Hoppy adventures await!', 'connoisseur', '🌿', '#28a745', 'special', 10, '{"style_category": "IPA"}', 'common', 40),
('badge-stout-aficionado', 'Stout Aficionado', 'Dark and rich! You\'ve explored 10 different stout varieties.', 'connoisseur', '⚫', '#343a40', 'special', 10, '{"style_category": "Stout"}', 'common', 40),
('badge-lager-specialist', 'Lager Specialist', 'Crisp and clean! You\'ve sampled 10 different lager styles.', 'connoisseur', '🌾', '#ffc107', 'special', 10, '{"style_category": "Lager"}', 'common', 40),
('badge-sour-seeker', 'Sour Seeker', 'Pucker up! You\'ve tried 5 different sour beer styles.', 'connoisseur', '🍋', '#dc3545', 'special', 5, '{"style_category": "Sour"}', 'uncommon', 60),
('badge-style-explorer', 'Style Explorer', 'Diverse palate! You\'ve tried beers from 10 different style categories.', 'connoisseur', '🎨', '#6610f2', 'style_count', 10, 'uncommon', 80),
('badge-style-master', 'Style Master', 'Exceptional diversity! You\'ve experienced 20 different beer style categories.', 'connoisseur', '🎭', '#6f42c1', 'style_count', 20, 'rare', 200),
('badge-critic', 'Beer Critic', 'Your opinion matters! You\'ve written 25 detailed beer reviews.', 'connoisseur', '📝', '#17a2b8', 'review_count', 25, 'uncommon', 100),
('badge-expert-reviewer', 'Expert Reviewer', 'Prolific reviewer! You\'ve shared 100 beer reviews with the community.', 'connoisseur', '🏆', '#fd7e14', 'review_count', 100, 'rare', 300);

-- Social Badges (Community and Interaction-based)
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, rarity, points_value) VALUES
('badge-social-butterfly', 'Social Butterfly', 'You\'re building your network! You have 10 followers.', 'social', '🦋', '#e83e8c', 'follower_count', 10, 'common', 50),
('badge-influencer', 'Beer Influencer', 'People love your taste! You have 50 followers.', 'social', '📢', '#fd7e14', 'follower_count', 50, 'uncommon', 150),
('badge-celebrity', 'Beer Celebrity', 'You\'re famous in the beer community! You have 100 followers.', 'social', '⭐', '#ffc107', 'follower_count', 100, 'rare', 400),
('badge-first-friend', 'First Friend', 'Welcome to the community! You\'ve made your first connection.', 'social', '👋', '#28a745', 'special', 1, '{"action": "first_follow"}', 'common', 15),
('badge-networker', 'Networker', 'You\'re well connected! You\'re following 25 beer enthusiasts.', 'social', '🤝', '#17a2b8', 'special', 25, '{"action": "following_count"}', 'common', 35),
('badge-community-helper', 'Community Helper', 'Helpful member! Your reviews have received 50 likes.', 'social', '❤️', '#dc3545', 'special', 50, '{"action": "likes_received"}', 'uncommon', 75);

-- Seasonal Badges (Time and Event-based)
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, criteria_metadata, rarity, points_value) VALUES
('badge-oktoberfest', 'Oktoberfest Celebrant', 'Prost! You checked in a beer during Oktoberfest season.', 'seasonal', '🍻', '#ffc107', 'special', 1, '{"season": "oktoberfest", "months": [9, 10]}', 'uncommon', 50),
('badge-summer-sipper', 'Summer Sipper', 'Beat the heat! You\'ve checked in 10 beers during summer months.', 'seasonal', '☀️', '#fd7e14', 'special', 10, '{"season": "summer", "months": [6, 7, 8]}', 'common', 30),
('badge-winter-warmer', 'Winter Warmer', 'Cozy up! You\'ve enjoyed 10 beers during the winter season.', 'seasonal', '❄️', '#17a2b8', 'special', 10, '{"season": "winter", "months": [12, 1, 2]}', 'common', 30),
('badge-new-year-toaster', 'New Year Toaster', 'Cheers to new beginnings! You checked in a beer on New Year\'s Day.', 'seasonal', '🎊', '#6610f2', 'special', 1, '{"holiday": "new_year", "date": "01-01"}', 'rare', 100),
('badge-weekend-warrior', 'Weekend Warrior', 'You know how to unwind! You\'ve checked in 20 beers on weekends.', 'seasonal', '🎉', '#e83e8c', 'special', 20, '{"days": ["saturday", "sunday"]}', 'common', 40);

-- Location Badges (Geographic-based)
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, criteria_metadata, rarity, points_value) VALUES
('badge-local-supporter', 'Local Supporter', 'Supporting your community! You\'ve visited 3 local breweries.', 'location', '🏠', '#28a745', 'special', 3, '{"type": "local_breweries"}', 'common', 40),
('badge-road-tripper', 'Road Tripper', 'Adventure seeker! You\'ve checked in beers from 5 different cities.', 'location', '🚗', '#17a2b8', 'special', 5, '{"type": "different_cities"}', 'uncommon', 80),
('badge-globe-trotter', 'Globe Trotter', 'World traveler! You\'ve tried beers from 3 different countries.', 'location', '🌍', '#6610f2', 'special', 3, '{"type": "different_countries"}', 'epic', 300),
('badge-state-explorer', 'State Explorer', 'Regional explorer! You\'ve visited breweries in 5 different states.', 'location', '🗺️', '#fd7e14', 'special', 5, '{"type": "different_states"}', 'rare', 200);

-- Special Achievement Badges
INSERT INTO badges (id, name, description, category, icon, color, criteria_type, criteria_value, criteria_metadata, rarity, points_value) VALUES
('badge-early-adopter', 'Early Adopter', 'Welcome pioneer! You were among the first to join Beersty.', 'special', '🚀', '#6610f2', 'special', 1, '{"type": "early_user"}', 'legendary', 500),
('badge-perfect-rating', 'Perfect Palate', 'Discerning taste! You\'ve given a perfect 5-star rating.', 'special', '⭐', '#ffc107', 'special', 1, '{"rating": 5.0}', 'common', 20),
('badge-harsh-critic', 'Harsh Critic', 'Honest reviewer! You\'ve given a 1-star rating (we all have bad beers).', 'special', '💔', '#dc3545', 'special', 1, '{"rating": 1.0}', 'uncommon', 25),
('badge-streak-starter', 'Streak Starter', 'Consistency! You\'ve checked in beers for 7 consecutive days.', 'special', '🔥', '#fd7e14', 'special', 7, '{"type": "daily_streak"}', 'uncommon', 70),
('badge-streak-master', 'Streak Master', 'Dedication! You\'ve maintained a 30-day check-in streak.', 'special', '🏅', '#e83e8c', 'special', 30, '{"type": "daily_streak"}', 'epic', 400),
('badge-midnight-drinker', 'Midnight Drinker', 'Night owl! You\'ve checked in a beer after midnight.', 'special', '🌙', '#6c757d', 'special', 1, '{"hour_range": [0, 5]}', 'uncommon', 30),
('badge-early-bird', 'Early Bird', 'Morning person! You\'ve checked in a beer before 8 AM.', 'special', '🐦', '#28a745', 'special', 1, '{"hour_range": [5, 8]}', 'uncommon', 30),
('badge-photo-enthusiast', 'Photo Enthusiast', 'Picture perfect! You\'ve shared 25 photos with your check-ins.', 'special', '📸', '#17a2b8', 'special', 25, '{"type": "photos_shared"}', 'common', 50);
