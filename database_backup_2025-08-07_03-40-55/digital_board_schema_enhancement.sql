-- Digital Board System - Enhanced Database Schema
-- Phase 4 10.0 - Comprehensive Database Schema Enhancement
-- Created: December 2024

-- Use the existing beersty_db database
USE beersty_db;

-- =====================================================
-- ENHANCED DIGITAL BOARDS TABLE
-- =====================================================

-- Drop existing digital_boards table if it exists (for clean setup)
-- In production, use ALTER TABLE instead
DROP TABLE IF EXISTS digital_boards;

CREATE TABLE digital_boards (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    board_id VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL DEFAULT 'Digital Board',
    description TEXT NULL,
    
    -- Display Settings
    settings JSON NOT NULL DEFAULT '{}',
    template_id VARCHAR(36) NULL,
    current_slideshow_id VARCHAR(36) NULL,
    
    -- Board Configuration
    display_mode ENUM('static', 'slideshow', 'hybrid') DEFAULT 'static',
    refresh_interval INT DEFAULT 300, -- seconds
    auto_refresh BOOLEAN DEFAULT TRUE,
    fullscreen_mode BOOLEAN DEFAULT FALSE,
    
    -- Theme and Layout
    theme VARCHAR(50) DEFAULT 'beersty-professional',
    layout ENUM('grid', 'list', 'cards') DEFAULT 'grid',
    columns_count INT DEFAULT 3,
    
    -- Display Options
    show_prices BOOLEAN DEFAULT TRUE,
    show_descriptions BOOLEAN DEFAULT TRUE,
    show_abv BOOLEAN DEFAULT TRUE,
    show_ibu BOOLEAN DEFAULT TRUE,
    show_tap_numbers BOOLEAN DEFAULT TRUE,
    show_availability BOOLEAN DEFAULT TRUE,
    show_header BOOLEAN DEFAULT TRUE,
    show_footer BOOLEAN DEFAULT TRUE,
    show_ticker BOOLEAN DEFAULT FALSE,
    
    -- Ticker Configuration
    ticker_message TEXT NULL,
    ticker_speed INT DEFAULT 50, -- pixels per second
    ticker_enabled BOOLEAN DEFAULT FALSE,
    
    -- Advanced Settings
    custom_css TEXT NULL,
    custom_js TEXT NULL,
    background_image VARCHAR(255) NULL,
    background_color VARCHAR(7) DEFAULT '#1a1a1a',
    
    -- Access Control
    is_public BOOLEAN DEFAULT FALSE,
    access_code VARCHAR(20) NULL,
    allowed_ips JSON NULL,
    
    -- Status and Metadata
    is_active BOOLEAN DEFAULT TRUE,
    last_accessed TIMESTAMP NULL,
    view_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys and Constraints
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_brewery_board (brewery_id, board_id),
    INDEX idx_digital_boards_brewery (brewery_id),
    INDEX idx_digital_boards_active (is_active),
    INDEX idx_digital_boards_mode (display_mode),
    INDEX idx_digital_boards_template (template_id)
);

-- =====================================================
-- SLIDESHOW PRESENTATIONS TABLE
-- =====================================================

CREATE TABLE slideshow_presentations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    
    -- Slideshow Configuration
    is_active BOOLEAN DEFAULT TRUE,
    loop_enabled BOOLEAN DEFAULT TRUE,
    auto_advance BOOLEAN DEFAULT TRUE,
    global_duration INT DEFAULT 10, -- seconds per slide
    global_transition VARCHAR(50) DEFAULT 'fade',
    
    -- Playback Settings
    shuffle_slides BOOLEAN DEFAULT FALSE,
    pause_on_hover BOOLEAN DEFAULT FALSE,
    show_controls BOOLEAN DEFAULT TRUE,
    show_indicators BOOLEAN DEFAULT TRUE,
    show_progress BOOLEAN DEFAULT TRUE,
    
    -- Scheduling
    schedule_enabled BOOLEAN DEFAULT FALSE,
    schedule_start_time TIME NULL,
    schedule_end_time TIME NULL,
    schedule_days JSON NULL, -- array of day numbers (0=Sunday)
    
    -- Advanced Settings
    transition_duration DECIMAL(4,2) DEFAULT 1.0, -- seconds
    preload_slides BOOLEAN DEFAULT TRUE,
    cache_duration INT DEFAULT 3600, -- seconds
    
    -- Metadata
    total_slides INT DEFAULT 0,
    total_duration INT DEFAULT 0, -- calculated total duration
    last_played TIMESTAMP NULL,
    play_count INT DEFAULT 0,
    
    -- Status
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys and Constraints
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_slideshow_brewery (brewery_id),
    INDEX idx_slideshow_active (is_active),
    INDEX idx_slideshow_schedule (schedule_enabled, schedule_start_time, schedule_end_time)
);

-- =====================================================
-- SLIDESHOW SLIDES TABLE
-- =====================================================

CREATE TABLE slideshow_slides (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    slideshow_id VARCHAR(36) NOT NULL,
    slide_order INT NOT NULL DEFAULT 0,
    
    -- Slide Basic Info
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    slide_type ENUM('beer_board', 'image', 'video', 'html', 'events', 'social', 'weather', 'qr_code') NOT NULL,
    
    -- Content Configuration
    content_id VARCHAR(36) NULL, -- references slide_content table
    template_id VARCHAR(36) NULL, -- for beer_board slides
    
    -- Display Settings
    duration INT DEFAULT 10, -- seconds
    transition VARCHAR(50) DEFAULT 'fade',
    background_color VARCHAR(7) NULL,
    background_image VARCHAR(255) NULL,
    
    -- Slide-specific Settings
    settings JSON NULL DEFAULT '{}',
    
    -- Beer Board Specific (when slide_type = 'beer_board')
    beer_board_template VARCHAR(50) NULL,
    show_header BOOLEAN DEFAULT TRUE,
    show_ticker BOOLEAN DEFAULT FALSE,
    custom_message TEXT NULL,
    
    -- Media Specific (when slide_type = 'image' or 'video')
    media_url VARCHAR(500) NULL,
    media_alt_text VARCHAR(255) NULL,
    overlay_text TEXT NULL,
    overlay_position ENUM('top', 'center', 'bottom') DEFAULT 'bottom',
    
    -- HTML Specific (when slide_type = 'html')
    html_content TEXT NULL,
    css_styles TEXT NULL,
    
    -- Interactive Elements
    clickable BOOLEAN DEFAULT FALSE,
    click_action ENUM('none', 'url', 'next_slide', 'pause') DEFAULT 'none',
    click_url VARCHAR(500) NULL,
    
    -- Conditional Display
    condition_enabled BOOLEAN DEFAULT FALSE,
    condition_type ENUM('time', 'weather', 'inventory', 'custom') NULL,
    condition_value JSON NULL,
    
    -- Status and Metadata
    is_active BOOLEAN DEFAULT TRUE,
    view_count INT DEFAULT 0,
    last_displayed TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys and Constraints
    FOREIGN KEY (slideshow_id) REFERENCES slideshow_presentations(id) ON DELETE CASCADE,
    INDEX idx_slides_slideshow (slideshow_id),
    INDEX idx_slides_order (slideshow_id, slide_order),
    INDEX idx_slides_type (slide_type),
    INDEX idx_slides_active (is_active),
    UNIQUE KEY unique_slideshow_order (slideshow_id, slide_order)
);

-- =====================================================
-- TEMPLATE LIBRARY TABLE
-- =====================================================

CREATE TABLE template_library (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NULL, -- NULL for system templates
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    category ENUM('system', 'custom', 'community') DEFAULT 'custom',

    -- Template Configuration
    template_type ENUM('beer_board', 'slide', 'layout') DEFAULT 'beer_board',
    base_template VARCHAR(50) NULL, -- parent template if customized

    -- Design Settings
    theme_settings JSON NOT NULL DEFAULT '{}',
    layout_settings JSON NOT NULL DEFAULT '{}',
    color_scheme JSON NOT NULL DEFAULT '{}',
    typography_settings JSON NOT NULL DEFAULT '{}',

    -- Template Assets
    preview_image VARCHAR(255) NULL,
    css_content TEXT NULL,
    html_template TEXT NULL,
    js_content TEXT NULL,

    -- Template Metadata
    version VARCHAR(20) DEFAULT '1.0',
    author VARCHAR(255) NULL,
    tags JSON NULL, -- array of tags for categorization

    -- Usage Statistics
    usage_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,

    -- Sharing and Access
    is_public BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,

    -- Status
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys and Constraints
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_template_brewery (brewery_id),
    INDEX idx_template_category (category),
    INDEX idx_template_type (template_type),
    INDEX idx_template_public (is_public),
    INDEX idx_template_featured (is_featured)
);

-- =====================================================
-- SLIDE CONTENT TABLE
-- =====================================================

CREATE TABLE slide_content (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    content_type ENUM('image', 'video', 'audio', 'document', 'html', 'data') NOT NULL,

    -- File Information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL, -- bytes
    mime_type VARCHAR(100) NOT NULL,

    -- Media Properties
    width INT NULL,
    height INT NULL,
    duration DECIMAL(10,2) NULL, -- seconds for video/audio

    -- Content Metadata
    title VARCHAR(255) NULL,
    description TEXT NULL,
    alt_text VARCHAR(255) NULL,
    tags JSON NULL,

    -- Processing Status
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    thumbnail_path VARCHAR(500) NULL,
    compressed_path VARCHAR(500) NULL,

    -- Usage and Analytics
    usage_count INT DEFAULT 0,
    last_used TIMESTAMP NULL,

    -- Storage Configuration
    storage_type ENUM('local', 'cloud', 'cdn') DEFAULT 'local',
    cloud_url VARCHAR(500) NULL,
    cdn_url VARCHAR(500) NULL,

    -- Access Control
    is_public BOOLEAN DEFAULT FALSE,
    access_level ENUM('private', 'brewery', 'public') DEFAULT 'brewery',

    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys and Constraints
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_content_brewery (brewery_id),
    INDEX idx_content_type (content_type),
    INDEX idx_content_status (processing_status),
    INDEX idx_content_public (is_public),
    INDEX idx_content_storage (storage_type)
);

-- =====================================================
-- ENHANCED BEER MENU TABLE
-- =====================================================

-- Drop existing beer_menu table if it exists (for clean setup)
DROP TABLE IF EXISTS beer_menu;

CREATE TABLE beer_menu (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,

    -- Beer Classification
    style_id VARCHAR(36) NULL,
    category VARCHAR(100) NULL, -- IPA, Stout, Lager, etc.
    subcategory VARCHAR(100) NULL,

    -- Beer Properties
    abv DECIMAL(4,2) NULL,
    ibu INT NULL,
    srm INT NULL, -- Standard Reference Method (color)
    og DECIMAL(4,3) NULL, -- Original Gravity
    fg DECIMAL(4,3) NULL, -- Final Gravity

    -- Pricing and Availability
    price DECIMAL(8,2) NULL,
    price_half DECIMAL(8,2) NULL, -- half pint price
    price_flight DECIMAL(8,2) NULL, -- flight price
    tap_number INT NULL,
    keg_size DECIMAL(6,2) NULL, -- gallons
    remaining_volume DECIMAL(6,2) NULL, -- gallons remaining

    -- Status and Availability
    is_available BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_seasonal BOOLEAN DEFAULT FALSE,
    availability_status ENUM('available', 'low', 'out', 'coming_soon') DEFAULT 'available',

    -- Display Settings
    display_order INT DEFAULT 0,
    show_on_board BOOLEAN DEFAULT TRUE,
    highlight_color VARCHAR(7) NULL,

    -- Additional Information
    ingredients TEXT NULL,
    allergens JSON NULL,
    awards JSON NULL, -- array of awards/medals
    tasting_notes TEXT NULL,
    food_pairings TEXT NULL,

    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys and Constraints
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    FOREIGN KEY (style_id) REFERENCES beer_styles(id) ON DELETE SET NULL,
    INDEX idx_beer_menu_brewery (brewery_id),
    INDEX idx_beer_menu_style (style_id),
    INDEX idx_beer_menu_available (is_available),
    INDEX idx_beer_menu_featured (is_featured),
    INDEX idx_beer_menu_tap (tap_number),
    INDEX idx_beer_menu_order (display_order)
);

-- =====================================================
-- FOREIGN KEY RELATIONSHIPS AND CONSTRAINTS
-- =====================================================

-- Add foreign key relationships between new tables
ALTER TABLE digital_boards
ADD CONSTRAINT fk_digital_boards_template
FOREIGN KEY (template_id) REFERENCES template_library(id) ON DELETE SET NULL;

ALTER TABLE digital_boards
ADD CONSTRAINT fk_digital_boards_slideshow
FOREIGN KEY (current_slideshow_id) REFERENCES slideshow_presentations(id) ON DELETE SET NULL;

ALTER TABLE slideshow_slides
ADD CONSTRAINT fk_slides_template
FOREIGN KEY (template_id) REFERENCES template_library(id) ON DELETE SET NULL;

ALTER TABLE slideshow_slides
ADD CONSTRAINT fk_slides_content
FOREIGN KEY (content_id) REFERENCES slide_content(id) ON DELETE SET NULL;

-- =====================================================
-- SYSTEM TEMPLATES DATA
-- =====================================================

-- Insert default system templates
INSERT INTO template_library (id, brewery_id, name, description, category, template_type, theme_settings, layout_settings, color_scheme, typography_settings, is_public, is_featured, is_active) VALUES
('template-classic-dark', NULL, 'Classic Dark Theme', 'Professional dark theme with elegant typography', 'system', 'beer_board',
JSON_OBJECT('background', '#1a1a1a', 'accent', '#d4af37'),
JSON_OBJECT('layout', 'grid', 'columns', 3),
JSON_OBJECT('primary', '#ffffff', 'secondary', '#cccccc', 'accent', '#d4af37'),
JSON_OBJECT('font_family', 'Inter', 'font_size', '16px'),
TRUE, TRUE, TRUE),

('template-brewery-wood', NULL, 'Brewery Wood Theme', 'Warm wood-themed design perfect for traditional breweries', 'system', 'beer_board',
JSON_OBJECT('background', '#2c1810', 'accent', '#d69a6b'),
JSON_OBJECT('layout', 'grid', 'columns', 2),
JSON_OBJECT('primary', '#f5f5dc', 'secondary', '#d69a6b', 'accent', '#ffc107'),
JSON_OBJECT('font_family', 'Playfair Display', 'font_size', '18px'),
TRUE, TRUE, TRUE),

('template-modern-light', NULL, 'Modern Light Theme', 'Clean and modern light theme with excellent readability', 'system', 'beer_board',
JSON_OBJECT('background', '#ffffff', 'accent', '#007bff'),
JSON_OBJECT('layout', 'list', 'columns', 1),
JSON_OBJECT('primary', '#333333', 'secondary', '#666666', 'accent', '#007bff'),
JSON_OBJECT('font_family', 'Roboto', 'font_size', '16px'),
TRUE, TRUE, TRUE),

('template-beersty-professional', NULL, 'Beersty Professional', 'Official Beersty theme with brewery-inspired colors', 'system', 'beer_board',
JSON_OBJECT('background', '#6f4c3e', 'accent', '#ffc107'),
JSON_OBJECT('layout', 'grid', 'columns', 3),
JSON_OBJECT('primary', '#f5f5dc', 'secondary', '#d69a6b', 'accent', '#ffc107'),
JSON_OBJECT('font_family', 'Inter', 'font_size', '16px'),
TRUE, TRUE, TRUE);

-- =====================================================
-- DEMO DATA FOR TESTING
-- =====================================================

-- Insert demo brewery if it doesn't exist
INSERT IGNORE INTO breweries (id, name, address, city, state, zip, phone, website, email, description) VALUES
('demo-brewery-1', 'Demo Craft Brewery', '123 Brewery Lane', 'Craftville', 'CA', '90210', '(*************', 'https://democraftbrewery.com', '<EMAIL>', 'A demo brewery for testing the digital board system');

-- Insert demo digital board
INSERT INTO digital_boards (id, brewery_id, board_id, name, description, display_mode, theme, template_id, settings) VALUES
('demo-board-1', 'demo-brewery-1', 'main-board', 'Main Digital Board', 'Primary digital beer board for the taproom', 'static', 'beersty-professional', 'template-beersty-professional',
JSON_OBJECT(
    'refresh_interval', 300,
    'show_prices', true,
    'show_descriptions', true,
    'show_abv', true,
    'show_ibu', true,
    'ticker_message', 'Welcome to Demo Craft Brewery! Fresh craft beers on tap daily!',
    'ticker_enabled', true
));

-- Insert demo slideshow presentation
INSERT INTO slideshow_presentations (id, brewery_id, name, description, global_duration, global_transition) VALUES
('demo-slideshow-1', 'demo-brewery-1', 'Main Slideshow', 'Primary slideshow for digital displays', 15, 'fade');

-- Insert demo slides
INSERT INTO slideshow_slides (id, slideshow_id, slide_order, title, slide_type, duration, transition, beer_board_template, show_header, custom_message) VALUES
('demo-slide-1', 'demo-slideshow-1', 1, 'Current Beer Menu', 'beer_board', 20, 'fade', 'beersty-professional', TRUE, 'Fresh Craft Beers on Tap!'),
('demo-slide-2', 'demo-slideshow-1', 2, 'Happy Hour Special', 'image', 10, 'slide_left', NULL, FALSE, NULL),
('demo-slide-3', 'demo-slideshow-1', 3, 'Brewery Events', 'events', 15, 'fade', NULL, TRUE, 'Join us for upcoming events!');

-- =====================================================
-- INDEXES AND PERFORMANCE OPTIMIZATION
-- =====================================================

-- Additional indexes for performance
CREATE INDEX idx_digital_boards_last_accessed ON digital_boards(last_accessed);
CREATE INDEX idx_slideshow_last_played ON slideshow_presentations(last_played);
CREATE INDEX idx_slides_last_displayed ON slideshow_slides(last_displayed);
CREATE INDEX idx_content_last_used ON slide_content(last_used);
CREATE INDEX idx_template_usage ON template_library(usage_count);

-- Composite indexes for common queries
CREATE INDEX idx_slides_slideshow_active_order ON slideshow_slides(slideshow_id, is_active, slide_order);
CREATE INDEX idx_content_brewery_type ON slide_content(brewery_id, content_type);
CREATE INDEX idx_beer_menu_brewery_available ON beer_menu(brewery_id, is_available);

-- =====================================================
-- TRIGGERS FOR DATA INTEGRITY
-- =====================================================

-- Update slideshow metadata when slides are added/removed
DELIMITER //
CREATE TRIGGER update_slideshow_metadata_insert
AFTER INSERT ON slideshow_slides
FOR EACH ROW
BEGIN
    UPDATE slideshow_presentations
    SET total_slides = (SELECT COUNT(*) FROM slideshow_slides WHERE slideshow_id = NEW.slideshow_id AND is_active = TRUE),
        total_duration = (SELECT SUM(duration) FROM slideshow_slides WHERE slideshow_id = NEW.slideshow_id AND is_active = TRUE),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.slideshow_id;
END//

CREATE TRIGGER update_slideshow_metadata_update
AFTER UPDATE ON slideshow_slides
FOR EACH ROW
BEGIN
    UPDATE slideshow_presentations
    SET total_slides = (SELECT COUNT(*) FROM slideshow_slides WHERE slideshow_id = NEW.slideshow_id AND is_active = TRUE),
        total_duration = (SELECT SUM(duration) FROM slideshow_slides WHERE slideshow_id = NEW.slideshow_id AND is_active = TRUE),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.slideshow_id;
END//

CREATE TRIGGER update_slideshow_metadata_delete
AFTER DELETE ON slideshow_slides
FOR EACH ROW
BEGIN
    UPDATE slideshow_presentations
    SET total_slides = (SELECT COUNT(*) FROM slideshow_slides WHERE slideshow_id = OLD.slideshow_id AND is_active = TRUE),
        total_duration = (SELECT SUM(duration) FROM slideshow_slides WHERE slideshow_id = OLD.slideshow_id AND is_active = TRUE),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.slideshow_id;
END//

-- Update template usage count
CREATE TRIGGER update_template_usage
AFTER INSERT ON digital_boards
FOR EACH ROW
BEGIN
    IF NEW.template_id IS NOT NULL THEN
        UPDATE template_library
        SET usage_count = usage_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.template_id;
    END IF;
END//

DELIMITER ;

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for active digital boards with template information
CREATE VIEW v_active_digital_boards AS
SELECT
    db.id,
    db.brewery_id,
    db.board_id,
    db.name,
    db.description,
    db.display_mode,
    db.theme,
    db.is_active,
    db.last_accessed,
    db.view_count,
    tl.name as template_name,
    tl.category as template_category,
    b.name as brewery_name
FROM digital_boards db
LEFT JOIN template_library tl ON db.template_id = tl.id
LEFT JOIN breweries b ON db.brewery_id = b.id
WHERE db.is_active = TRUE;

-- View for slideshow presentations with slide counts
CREATE VIEW v_slideshow_summary AS
SELECT
    sp.id,
    sp.brewery_id,
    sp.name,
    sp.description,
    sp.is_active,
    sp.total_slides,
    sp.total_duration,
    sp.last_played,
    sp.play_count,
    b.name as brewery_name,
    COUNT(ss.id) as active_slides_count
FROM slideshow_presentations sp
LEFT JOIN breweries b ON sp.brewery_id = b.id
LEFT JOIN slideshow_slides ss ON sp.id = ss.slideshow_id AND ss.is_active = TRUE
GROUP BY sp.id, sp.brewery_id, sp.name, sp.description, sp.is_active, sp.total_slides, sp.total_duration, sp.last_played, sp.play_count, b.name;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Digital Board Schema Enhancement Complete!' as status,
       'Phase 4 10.0 - Database Schema Enhancement has been successfully implemented.' as message,
       '5 new tables created with full relationships and constraints.' as details;
