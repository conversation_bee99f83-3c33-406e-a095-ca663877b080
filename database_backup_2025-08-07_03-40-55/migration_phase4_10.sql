-- Digital Board System - Migration Script
-- Phase 4 10.0 - Database Schema Enhancement Migration
-- This script safely migrates existing databases to the new enhanced schema

USE beersty_db;

-- =====================================================
-- BACKUP EXISTING DATA
-- =====================================================

-- Create backup tables for existing data
CREATE TABLE IF NOT EXISTS digital_boards_backup AS SELECT * FROM digital_boards WHERE 1=0;
CREATE TABLE IF NOT EXISTS beer_menu_backup AS SELECT * FROM beer_menu WHERE 1=0;

-- Backup existing data if tables exist
INSERT IGNORE INTO digital_boards_backup SELECT * FROM digital_boards;
INSERT IGNORE INTO beer_menu_backup SELECT * FROM beer_menu;

-- =====================================================
-- MIGRATION STEP 1: CREATE NEW TABLES
-- =====================================================

-- Create slideshow_presentations table
CREATE TABLE IF NOT EXISTS slideshow_presentations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    loop_enabled BOOLEAN DEFAULT TRUE,
    auto_advance BOOLEAN DEFAULT TRUE,
    global_duration INT DEFAULT 10,
    global_transition VARCHAR(50) DEFAULT 'fade',
    shuffle_slides BOOLEAN DEFAULT FALSE,
    pause_on_hover BOOLEAN DEFAULT FALSE,
    show_controls BOOLEAN DEFAULT TRUE,
    show_indicators BOOLEAN DEFAULT TRUE,
    show_progress BOOLEAN DEFAULT TRUE,
    schedule_enabled BOOLEAN DEFAULT FALSE,
    schedule_start_time TIME NULL,
    schedule_end_time TIME NULL,
    schedule_days JSON NULL,
    transition_duration DECIMAL(4,2) DEFAULT 1.0,
    preload_slides BOOLEAN DEFAULT TRUE,
    cache_duration INT DEFAULT 3600,
    total_slides INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    last_played TIMESTAMP NULL,
    play_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_slideshow_brewery (brewery_id),
    INDEX idx_slideshow_active (is_active),
    INDEX idx_slideshow_schedule (schedule_enabled, schedule_start_time, schedule_end_time)
);

-- Create slideshow_slides table
CREATE TABLE IF NOT EXISTS slideshow_slides (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    slideshow_id VARCHAR(36) NOT NULL,
    slide_order INT NOT NULL DEFAULT 0,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    slide_type ENUM('beer_board', 'image', 'video', 'html', 'events', 'social', 'weather', 'qr_code') NOT NULL,
    content_id VARCHAR(36) NULL,
    template_id VARCHAR(36) NULL,
    duration INT DEFAULT 10,
    transition VARCHAR(50) DEFAULT 'fade',
    background_color VARCHAR(7) NULL,
    background_image VARCHAR(255) NULL,
    settings JSON NULL DEFAULT '{}',
    beer_board_template VARCHAR(50) NULL,
    show_header BOOLEAN DEFAULT TRUE,
    show_ticker BOOLEAN DEFAULT FALSE,
    custom_message TEXT NULL,
    media_url VARCHAR(500) NULL,
    media_alt_text VARCHAR(255) NULL,
    overlay_text TEXT NULL,
    overlay_position ENUM('top', 'center', 'bottom') DEFAULT 'bottom',
    html_content TEXT NULL,
    css_styles TEXT NULL,
    clickable BOOLEAN DEFAULT FALSE,
    click_action ENUM('none', 'url', 'next_slide', 'pause') DEFAULT 'none',
    click_url VARCHAR(500) NULL,
    condition_enabled BOOLEAN DEFAULT FALSE,
    condition_type ENUM('time', 'weather', 'inventory', 'custom') NULL,
    condition_value JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    view_count INT DEFAULT 0,
    last_displayed TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (slideshow_id) REFERENCES slideshow_presentations(id) ON DELETE CASCADE,
    INDEX idx_slides_slideshow (slideshow_id),
    INDEX idx_slides_order (slideshow_id, slide_order),
    INDEX idx_slides_type (slide_type),
    INDEX idx_slides_active (is_active),
    UNIQUE KEY unique_slideshow_order (slideshow_id, slide_order)
);

-- Create template_library table
CREATE TABLE IF NOT EXISTS template_library (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    category ENUM('system', 'custom', 'community') DEFAULT 'custom',
    template_type ENUM('beer_board', 'slide', 'layout') DEFAULT 'beer_board',
    base_template VARCHAR(50) NULL,
    theme_settings JSON NOT NULL DEFAULT '{}',
    layout_settings JSON NOT NULL DEFAULT '{}',
    color_scheme JSON NOT NULL DEFAULT '{}',
    typography_settings JSON NOT NULL DEFAULT '{}',
    preview_image VARCHAR(255) NULL,
    css_content TEXT NULL,
    html_template TEXT NULL,
    js_content TEXT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    author VARCHAR(255) NULL,
    tags JSON NULL,
    usage_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_template_brewery (brewery_id),
    INDEX idx_template_category (category),
    INDEX idx_template_type (template_type),
    INDEX idx_template_public (is_public),
    INDEX idx_template_featured (is_featured)
);

-- Create slide_content table
CREATE TABLE IF NOT EXISTS slide_content (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    content_type ENUM('image', 'video', 'audio', 'document', 'html', 'data') NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    width INT NULL,
    height INT NULL,
    duration DECIMAL(10,2) NULL,
    title VARCHAR(255) NULL,
    description TEXT NULL,
    alt_text VARCHAR(255) NULL,
    tags JSON NULL,
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    thumbnail_path VARCHAR(500) NULL,
    compressed_path VARCHAR(500) NULL,
    usage_count INT DEFAULT 0,
    last_used TIMESTAMP NULL,
    storage_type ENUM('local', 'cloud', 'cdn') DEFAULT 'local',
    cloud_url VARCHAR(500) NULL,
    cdn_url VARCHAR(500) NULL,
    is_public BOOLEAN DEFAULT FALSE,
    access_level ENUM('private', 'brewery', 'public') DEFAULT 'brewery',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    INDEX idx_content_brewery (brewery_id),
    INDEX idx_content_type (content_type),
    INDEX idx_content_status (processing_status),
    INDEX idx_content_public (is_public),
    INDEX idx_content_storage (storage_type)
);

-- =====================================================
-- MIGRATION STEP 2: ALTER EXISTING TABLES
-- =====================================================

-- Add new columns to digital_boards table
ALTER TABLE digital_boards
ADD COLUMN IF NOT EXISTS name VARCHAR(255) NOT NULL DEFAULT 'Digital Board' AFTER board_id,
ADD COLUMN IF NOT EXISTS description TEXT NULL AFTER name,
ADD COLUMN IF NOT EXISTS template_id VARCHAR(36) NULL AFTER settings,
ADD COLUMN IF NOT EXISTS current_slideshow_id VARCHAR(36) NULL AFTER template_id,
ADD COLUMN IF NOT EXISTS display_mode ENUM('static', 'slideshow', 'hybrid') DEFAULT 'static' AFTER current_slideshow_id,
ADD COLUMN IF NOT EXISTS refresh_interval INT DEFAULT 300 AFTER display_mode,
ADD COLUMN IF NOT EXISTS auto_refresh BOOLEAN DEFAULT TRUE AFTER refresh_interval,
ADD COLUMN IF NOT EXISTS fullscreen_mode BOOLEAN DEFAULT FALSE AFTER auto_refresh,
ADD COLUMN IF NOT EXISTS theme VARCHAR(50) DEFAULT 'beersty-professional' AFTER fullscreen_mode,
ADD COLUMN IF NOT EXISTS layout ENUM('grid', 'list', 'cards') DEFAULT 'grid' AFTER theme,
ADD COLUMN IF NOT EXISTS columns_count INT DEFAULT 3 AFTER layout,
ADD COLUMN IF NOT EXISTS show_prices BOOLEAN DEFAULT TRUE AFTER columns_count,
ADD COLUMN IF NOT EXISTS show_descriptions BOOLEAN DEFAULT TRUE AFTER show_prices,
ADD COLUMN IF NOT EXISTS show_abv BOOLEAN DEFAULT TRUE AFTER show_descriptions,
ADD COLUMN IF NOT EXISTS show_ibu BOOLEAN DEFAULT TRUE AFTER show_abv,
ADD COLUMN IF NOT EXISTS show_tap_numbers BOOLEAN DEFAULT TRUE AFTER show_ibu,
ADD COLUMN IF NOT EXISTS show_availability BOOLEAN DEFAULT TRUE AFTER show_tap_numbers,
ADD COLUMN IF NOT EXISTS show_header BOOLEAN DEFAULT TRUE AFTER show_availability,
ADD COLUMN IF NOT EXISTS show_footer BOOLEAN DEFAULT TRUE AFTER show_header,
ADD COLUMN IF NOT EXISTS show_ticker BOOLEAN DEFAULT FALSE AFTER show_footer,
ADD COLUMN IF NOT EXISTS ticker_message TEXT NULL AFTER show_ticker,
ADD COLUMN IF NOT EXISTS ticker_speed INT DEFAULT 50 AFTER ticker_message,
ADD COLUMN IF NOT EXISTS ticker_enabled BOOLEAN DEFAULT FALSE AFTER ticker_speed,
ADD COLUMN IF NOT EXISTS custom_css TEXT NULL AFTER ticker_enabled,
ADD COLUMN IF NOT EXISTS custom_js TEXT NULL AFTER custom_css,
ADD COLUMN IF NOT EXISTS background_image VARCHAR(255) NULL AFTER custom_js,
ADD COLUMN IF NOT EXISTS background_color VARCHAR(7) DEFAULT '#1a1a1a' AFTER background_image,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE AFTER background_color,
ADD COLUMN IF NOT EXISTS access_code VARCHAR(20) NULL AFTER is_public,
ADD COLUMN IF NOT EXISTS allowed_ips JSON NULL AFTER access_code,
ADD COLUMN IF NOT EXISTS last_accessed TIMESTAMP NULL AFTER allowed_ips,
ADD COLUMN IF NOT EXISTS view_count INT DEFAULT 0 AFTER last_accessed;

-- Add new indexes to digital_boards
ALTER TABLE digital_boards
ADD INDEX IF NOT EXISTS idx_digital_boards_active (is_active),
ADD INDEX IF NOT EXISTS idx_digital_boards_mode (display_mode),
ADD INDEX IF NOT EXISTS idx_digital_boards_template (template_id),
ADD INDEX IF NOT EXISTS idx_digital_boards_last_accessed (last_accessed);

-- Enhance beer_menu table
ALTER TABLE beer_menu
ADD COLUMN IF NOT EXISTS category VARCHAR(100) NULL AFTER style_id,
ADD COLUMN IF NOT EXISTS subcategory VARCHAR(100) NULL AFTER category,
ADD COLUMN IF NOT EXISTS og DECIMAL(4,3) NULL AFTER srm,
ADD COLUMN IF NOT EXISTS fg DECIMAL(4,3) NULL AFTER og,
ADD COLUMN IF NOT EXISTS price_half DECIMAL(8,2) NULL AFTER price,
ADD COLUMN IF NOT EXISTS price_flight DECIMAL(8,2) NULL AFTER price_half,
ADD COLUMN IF NOT EXISTS keg_size DECIMAL(6,2) NULL AFTER tap_number,
ADD COLUMN IF NOT EXISTS remaining_volume DECIMAL(6,2) NULL AFTER keg_size,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE AFTER is_available,
ADD COLUMN IF NOT EXISTS is_seasonal BOOLEAN DEFAULT FALSE AFTER is_featured,
ADD COLUMN IF NOT EXISTS availability_status ENUM('available', 'low', 'out', 'coming_soon') DEFAULT 'available' AFTER is_seasonal,
ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0 AFTER availability_status,
ADD COLUMN IF NOT EXISTS show_on_board BOOLEAN DEFAULT TRUE AFTER display_order,
ADD COLUMN IF NOT EXISTS highlight_color VARCHAR(7) NULL AFTER show_on_board,
ADD COLUMN IF NOT EXISTS ingredients TEXT NULL AFTER highlight_color,
ADD COLUMN IF NOT EXISTS allergens JSON NULL AFTER ingredients,
ADD COLUMN IF NOT EXISTS awards JSON NULL AFTER allergens,
ADD COLUMN IF NOT EXISTS tasting_notes TEXT NULL AFTER awards,
ADD COLUMN IF NOT EXISTS food_pairings TEXT NULL AFTER tasting_notes;

-- Add new indexes to beer_menu
ALTER TABLE beer_menu
ADD INDEX IF NOT EXISTS idx_beer_menu_featured (is_featured),
ADD INDEX IF NOT EXISTS idx_beer_menu_tap (tap_number),
ADD INDEX IF NOT EXISTS idx_beer_menu_order (display_order),
ADD INDEX IF NOT EXISTS idx_beer_menu_brewery_available (brewery_id, is_available);

-- =====================================================
-- MIGRATION STEP 3: ADD FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Add foreign key constraints (ignore if they already exist)
SET foreign_key_checks = 0;

ALTER TABLE digital_boards
ADD CONSTRAINT IF NOT EXISTS fk_digital_boards_template
FOREIGN KEY (template_id) REFERENCES template_library(id) ON DELETE SET NULL;

ALTER TABLE digital_boards
ADD CONSTRAINT IF NOT EXISTS fk_digital_boards_slideshow
FOREIGN KEY (current_slideshow_id) REFERENCES slideshow_presentations(id) ON DELETE SET NULL;

ALTER TABLE slideshow_slides
ADD CONSTRAINT IF NOT EXISTS fk_slides_template
FOREIGN KEY (template_id) REFERENCES template_library(id) ON DELETE SET NULL;

ALTER TABLE slideshow_slides
ADD CONSTRAINT IF NOT EXISTS fk_slides_content
FOREIGN KEY (content_id) REFERENCES slide_content(id) ON DELETE SET NULL;

SET foreign_key_checks = 1;

-- =====================================================
-- MIGRATION STEP 4: INSERT SYSTEM TEMPLATES
-- =====================================================

-- Insert system templates (ignore duplicates)
INSERT IGNORE INTO template_library (id, brewery_id, name, description, category, template_type, theme_settings, layout_settings, color_scheme, typography_settings, is_public, is_featured, is_active) VALUES
('template-classic-dark', NULL, 'Classic Dark Theme', 'Professional dark theme with elegant typography', 'system', 'beer_board',
JSON_OBJECT('background', '#1a1a1a', 'accent', '#d4af37'),
JSON_OBJECT('layout', 'grid', 'columns', 3),
JSON_OBJECT('primary', '#ffffff', 'secondary', '#cccccc', 'accent', '#d4af37'),
JSON_OBJECT('font_family', 'Inter', 'font_size', '16px'),
TRUE, TRUE, TRUE),

('template-brewery-wood', NULL, 'Brewery Wood Theme', 'Warm wood-themed design perfect for traditional breweries', 'system', 'beer_board',
JSON_OBJECT('background', '#2c1810', 'accent', '#d69a6b'),
JSON_OBJECT('layout', 'grid', 'columns', 2),
JSON_OBJECT('primary', '#f5f5dc', 'secondary', '#d69a6b', 'accent', '#ffc107'),
JSON_OBJECT('font_family', 'Playfair Display', 'font_size', '18px'),
TRUE, TRUE, TRUE),

('template-modern-light', NULL, 'Modern Light Theme', 'Clean and modern light theme with excellent readability', 'system', 'beer_board',
JSON_OBJECT('background', '#ffffff', 'accent', '#007bff'),
JSON_OBJECT('layout', 'list', 'columns', 1),
JSON_OBJECT('primary', '#333333', 'secondary', '#666666', 'accent', '#007bff'),
JSON_OBJECT('font_family', 'Roboto', 'font_size', '16px'),
TRUE, TRUE, TRUE),

('template-beersty-professional', NULL, 'Beersty Professional', 'Official Beersty theme with brewery-inspired colors', 'system', 'beer_board',
JSON_OBJECT('background', '#6f4c3e', 'accent', '#ffc107'),
JSON_OBJECT('layout', 'grid', 'columns', 3),
JSON_OBJECT('primary', '#f5f5dc', 'secondary', '#d69a6b', 'accent', '#ffc107'),
JSON_OBJECT('font_family', 'Inter', 'font_size', '16px'),
TRUE, TRUE, TRUE);

-- =====================================================
-- MIGRATION STEP 5: MIGRATE EXISTING DATA
-- =====================================================

-- Update existing digital_boards with default template
UPDATE digital_boards
SET template_id = 'template-beersty-professional',
    theme = 'beersty-professional'
WHERE template_id IS NULL;

-- Extract settings from JSON and populate new columns
UPDATE digital_boards
SET
    show_prices = COALESCE(JSON_EXTRACT(settings, '$.show_prices'), TRUE),
    show_descriptions = COALESCE(JSON_EXTRACT(settings, '$.show_descriptions'), TRUE),
    ticker_message = JSON_UNQUOTE(JSON_EXTRACT(settings, '$.ticker_message')),
    ticker_enabled = COALESCE(JSON_EXTRACT(settings, '$.ticker_enabled'), FALSE),
    refresh_interval = COALESCE(JSON_EXTRACT(settings, '$.refresh_interval'), 300)
WHERE settings IS NOT NULL AND JSON_VALID(settings);

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

SELECT 'Migration Complete!' as status,
       'Phase 4 10.0 - Database Schema Enhancement migration has been successfully completed.' as message,
       'All existing data has been preserved and new tables created.' as details;
