-- Places Enhancement Database Schema
-- Additional tables for places system enhancements

-- Places table (main places table - may already exist)
CREATE TABLE IF NOT EXISTS places (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    type ENUM('brewery', 'bar', 'restaurant', 'beer_garden', 'taproom') NOT NULL,
    description TEXT,
    address VARCHAR(500),
    city VARCHAR(100),
    state VARCHAR(50),
    zip VARCHAR(20),
    country VARCHAR(50) DEFAULT 'US',
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    rating DECIMAL(3, 2) DEFAULT 0.00,
    reviews_count INT DEFAULT 0,
    followers_count INT DEFAULT 0,
    price_range ENUM('$', '$$', '$$$', '$$$$') DEFAULT '$$',
    is_claimed BOOLEAN DEFAULT FALSE,
    claim_contact VARCHAR(255),
    has_360_tour BOOLEAN DEFAULT FALSE,
    tour_url VARCHAR(500),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_location (latitude, longitude),
    INDEX idx_type (type),
    INDEX idx_rating (rating),
    INDEX idx_status (status)
);

-- Place follows table
CREATE TABLE IF NOT EXISTS place_follows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    place_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_follow (user_id, place_id),
    INDEX idx_user_follows (user_id),
    INDEX idx_place_followers (place_id)
);

-- Deals table
CREATE TABLE IF NOT EXISTS deals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    discount_type ENUM('percentage', 'fixed_amount', 'bogo', 'free_item') NOT NULL,
    discount_value DECIMAL(8, 2) DEFAULT 0,
    qr_code VARCHAR(50),
    terms TEXT,
    valid_from DATE,
    valid_until DATE,
    usage_limit INT,
    usage_count INT DEFAULT 0,
    save_count INT DEFAULT 0,
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    INDEX idx_place_deals (place_id),
    INDEX idx_status (status),
    INDEX idx_valid_dates (valid_from, valid_until)
);

-- Saved deals table
CREATE TABLE IF NOT EXISTS saved_deals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    deal_id INT NOT NULL,
    saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (deal_id) REFERENCES deals(id) ON DELETE CASCADE,
    UNIQUE KEY unique_saved_deal (user_id, deal_id),
    INDEX idx_user_saved_deals (user_id),
    INDEX idx_deal_saves (deal_id)
);

-- Saved places table
CREATE TABLE IF NOT EXISTS saved_places (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    place_id INT NOT NULL,
    saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_saved_place (user_id, place_id),
    INDEX idx_user_saved_places (user_id),
    INDEX idx_place_saves (place_id)
);

-- Deal notifications table
CREATE TABLE IF NOT EXISTS deal_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    place_id INT NOT NULL,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_notification_subscription (user_id, place_id),
    INDEX idx_user_notifications (user_id),
    INDEX idx_place_subscribers (place_id)
);

-- Listing claims table
CREATE TABLE IF NOT EXISTS listing_claims (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    place_id INT NOT NULL,
    claim_token VARCHAR(64) UNIQUE NOT NULL,
    status ENUM('pending', 'under_review', 'approved', 'rejected') DEFAULT 'pending',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    reviewed_by INT NULL,
    user_email VARCHAR(255),
    user_name VARCHAR(255),
    verification_documents TEXT,
    admin_notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_claims (user_id),
    INDEX idx_place_claims (place_id),
    INDEX idx_status (status),
    INDEX idx_claim_token (claim_token)
);

-- Place photos table
CREATE TABLE IF NOT EXISTS place_photos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    user_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    category ENUM('food', 'drinks', 'interior', 'exterior', 'events', 'other') DEFAULT 'other',
    caption TEXT,
    likes_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'pending', 'rejected') DEFAULT 'active',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_place_photos (place_id),
    INDEX idx_user_photos (user_id),
    INDEX idx_category (category),
    INDEX idx_status (status)
);

-- Photo likes table
CREATE TABLE IF NOT EXISTS photo_likes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    photo_id INT NOT NULL,
    user_id INT NOT NULL,
    liked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES place_photos(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_photo_like (photo_id, user_id),
    INDEX idx_photo_likes (photo_id),
    INDEX idx_user_likes (user_id)
);

-- Place features table
CREATE TABLE IF NOT EXISTS place_features (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_value VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    INDEX idx_place_features (place_id),
    INDEX idx_feature_name (feature_name)
);

-- Place hours table
CREATE TABLE IF NOT EXISTS place_hours (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    day_of_week TINYINT NOT NULL, -- 0=Sunday, 1=Monday, etc.
    open_time TIME,
    close_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_place_day (place_id, day_of_week),
    INDEX idx_place_hours (place_id)
);

-- Insert sample features for common amenities
INSERT IGNORE INTO place_features (place_id, feature_name) VALUES
(1, 'Outdoor Seating'),
(1, 'Live Music'),
(1, 'Food Truck'),
(1, 'Pet Friendly'),
(1, 'Parking Available'),
(1, 'WiFi'),
(1, 'Happy Hour'),
(1, 'Sports TV');

-- Insert sample hours for place ID 1
INSERT IGNORE INTO place_hours (place_id, day_of_week, open_time, close_time, is_closed) VALUES
(1, 0, '12:00:00', '21:00:00', FALSE), -- Sunday
(1, 1, NULL, NULL, TRUE),              -- Monday (Closed)
(1, 2, '16:00:00', '22:00:00', FALSE), -- Tuesday
(1, 3, '16:00:00', '22:00:00', FALSE), -- Wednesday
(1, 4, '16:00:00', '23:00:00', FALSE), -- Thursday
(1, 5, '14:00:00', '00:00:00', FALSE), -- Friday
(1, 6, '12:00:00', '00:00:00', FALSE); -- Saturday

-- Insert sample deals
INSERT IGNORE INTO deals (place_id, title, description, discount_type, discount_value, qr_code, terms, valid_from, valid_until) VALUES
(1, 'Happy Hour Special', '50% off all draft beers', 'percentage', 50, 'HAPPY50', 'Valid Monday-Friday 3-6 PM only', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(1, 'Buy 2 Get 1 Free', 'Buy any 2 craft beers and get the 3rd one free', 'bogo', 0, 'BOGO3', 'Applies to beers of equal or lesser value', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 14 DAY)),
(1, 'Free Appetizer', 'Free appetizer with purchase of any entree', 'free_item', 0, 'FREEAPP', 'One per table, dine-in only', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY));
