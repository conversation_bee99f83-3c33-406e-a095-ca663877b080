-- Beersty Database Schema
-- MySQL Database Schema for Brewery Management System

CREATE DATABASE IF NOT EXISTS beersty_db;
USE beersty_db;

-- Users table (replaces Supabase auth.users)
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL
);

-- Profiles table (Enhanced for Phase 1)
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    role ENUM('admin', 'brewery', 'customer', 'beer_enthusiast', 'beer_expert') NOT NULL DEFAULT 'customer',
    brewery_id VARCHAR(36) NULL,

    -- Enhanced Profile Fields (Phase 1)
    first_name VA<PERSON>HA<PERSON>(100) NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NULL,
    username VARCHA<PERSON>(50) <PERSON>IQUE NULL,
    bio TEXT NULL,
    avatar VARCHAR(255) NULL,
    location VARCHAR(255) NULL,
    hometown VARCHAR(255) NULL,
    date_of_birth DATE NULL,

    -- Social Media Links
    website VARCHAR(255) NULL,
    instagram VARCHAR(100) NULL,
    twitter VARCHAR(100) NULL,
    facebook VARCHAR(100) NULL,

    -- Privacy Settings
    profile_visibility ENUM('public', 'friends', 'private') DEFAULT 'public',
    show_location BOOLEAN DEFAULT TRUE,
    show_age BOOLEAN DEFAULT FALSE,
    allow_messages BOOLEAN DEFAULT TRUE,

    -- Stats (will be calculated)
    total_checkins INT DEFAULT 0,
    total_reviews INT DEFAULT 0,
    total_photos INT DEFAULT 0,
    follower_count INT DEFAULT 0,
    following_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
);

-- Breweries table
CREATE TABLE breweries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    address TEXT NULL,
    city VARCHAR(100) NULL,
    state VARCHAR(50) NULL,
    zip VARCHAR(20) NULL,
    phone VARCHAR(20) NULL,
    website VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    description TEXT NULL,
    logo VARCHAR(255) NULL,
    feature_image VARCHAR(255) NULL,
    avatar VARCHAR(255) NULL,
    brewery_type VARCHAR(50) NOT NULL DEFAULT 'micro',
    social_links JSON NULL,
    claimable BOOLEAN DEFAULT TRUE,
    claimed BOOLEAN DEFAULT FALSE,
    verification_open BOOLEAN DEFAULT TRUE,
    verified BOOLEAN DEFAULT FALSE,
    follower_count INT DEFAULT 0,
    like_count INT DEFAULT 0,

    -- Location data (Phase 5)
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Beer Styles table (Phase 2)
CREATE TABLE beer_styles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL, -- IPA, Stout, Lager, etc.
    description TEXT NULL,
    characteristics JSON NULL, -- flavor profile, aroma, appearance
    abv_min DECIMAL(4,2) NULL,
    abv_max DECIMAL(4,2) NULL,
    ibu_min INT NULL,
    ibu_max INT NULL,
    srm_min INT NULL, -- Standard Reference Method (color)
    srm_max INT NULL,
    parent_style_id VARCHAR(36) NULL, -- for style families
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
);

-- Enhanced Beer Menu table (Phase 2)
CREATE TABLE beer_menu (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    beer_style_id VARCHAR(36) NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NULL, -- keeping for backward compatibility
    description TEXT NULL,
    abv DECIMAL(4,2) NULL,
    ibu INT NULL,
    srm INT NULL, -- color rating
    price DECIMAL(8,2) NULL,
    origin VARCHAR(100) NULL,

    -- Enhanced beer information (Phase 2)
    hops TEXT NULL, -- hop varieties used
    malts TEXT NULL, -- malt varieties used
    yeast VARCHAR(100) NULL, -- yeast strain
    brewing_process TEXT NULL, -- special brewing notes
    serving_temp_min INT NULL, -- serving temperature range
    serving_temp_max INT NULL,
    food_pairings TEXT NULL,

    -- Media and availability
    thumbnail VARCHAR(255) NULL,
    images JSON NULL, -- array of image URLs
    featured BOOLEAN DEFAULT FALSE,
    seasonal BOOLEAN DEFAULT FALSE,
    limited_edition BOOLEAN DEFAULT FALSE,
    available BOOLEAN DEFAULT TRUE,
    availability_start DATE NULL,
    availability_end DATE NULL,
    added_date DATE NULL,

    -- Statistics (calculated fields)
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    total_checkins INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    FOREIGN KEY (beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
);

-- Food Menu table
CREATE TABLE food_menu (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NULL,
    description TEXT NULL,
    price DECIMAL(8,2) NULL,
    image VARCHAR(255) NULL,
    available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
);

-- Brewery Coupons table
CREATE TABLE brewery_coupons (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    discount_value VARCHAR(50) NOT NULL,
    expiry_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    qr_code_url VARCHAR(255) NULL,
    redemption_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_brewery_code (brewery_id, code)
);

-- Digital Boards table
CREATE TABLE digital_boards (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    board_id VARCHAR(100) NOT NULL,
    settings JSON NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_brewery_board (brewery_id, board_id)
);

-- Gallery Images table
CREATE TABLE gallery_images (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    title VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
);

-- Posts table
CREATE TABLE posts (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    content TEXT NOT NULL,
    image_url VARCHAR(255) NULL,
    likes INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
);

-- Brewery Followers table
CREATE TABLE brewery_followers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    brewery_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_brewery_follow (user_id, brewery_id)
);

-- Brewery Likes table
CREATE TABLE brewery_likes (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    brewery_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_brewery_like (user_id, brewery_id)
);

-- User Beer Preferences table (Phase 1)
CREATE TABLE user_beer_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    favorite_styles JSON NULL, -- Array of beer style preferences
    favorite_breweries JSON NULL, -- Array of favorite brewery IDs
    preferred_abv_min DECIMAL(4,2) NULL,
    preferred_abv_max DECIMAL(4,2) NULL,
    preferred_ibu_min INT NULL,
    preferred_ibu_max INT NULL,
    avoid_styles JSON NULL, -- Array of styles to avoid
    dietary_restrictions JSON NULL, -- gluten-free, vegan, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
);

-- User Follows table (Phase 1 - Social Features Foundation)
CREATE TABLE user_follows (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    follower_id VARCHAR(36) NOT NULL,
    following_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_follow (follower_id, following_id)
);

-- User Activities table (Phase 1 - Activity Feed Foundation)
CREATE TABLE user_activities (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    activity_type ENUM('profile_update', 'brewery_follow', 'brewery_like', 'joined', 'beer_rating', 'beer_review', 'beer_checkin', 'user_follow') NOT NULL,
    target_type ENUM('user', 'brewery', 'beer') NULL,
    target_id VARCHAR(36) NULL,
    metadata JSON NULL, -- Additional activity data
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Beer Check-ins table (Phase 3)
CREATE TABLE beer_checkins (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    beer_id VARCHAR(36) NOT NULL,
    brewery_id VARCHAR(36) NOT NULL,
    rating_id VARCHAR(36) NULL, -- Optional link to rating if user rates during check-in

    -- Check-in location
    checkin_location VARCHAR(255) NULL,
    checkin_latitude DECIMAL(10, 8) NULL,
    checkin_longitude DECIMAL(11, 8) NULL,

    -- Check-in details
    serving_style ENUM('draft', 'bottle', 'can', 'growler', 'other') NULL,
    checkin_comment TEXT NULL,
    photos JSON NULL, -- Array of photo URLs

    -- Social features
    is_public BOOLEAN DEFAULT TRUE,
    like_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,

    -- Metadata
    device_info VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE SET NULL
);

-- Activity Likes table (Phase 3 - Social Interactions)
CREATE TABLE activity_likes (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    activity_id VARCHAR(36) NULL,
    checkin_id VARCHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (activity_id) REFERENCES user_activities(id) ON DELETE CASCADE,
    FOREIGN KEY (checkin_id) REFERENCES beer_checkins(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_activity_like (user_id, activity_id),
    UNIQUE KEY unique_user_checkin_like (user_id, checkin_id),
    CHECK ((activity_id IS NOT NULL AND checkin_id IS NULL) OR (activity_id IS NULL AND checkin_id IS NOT NULL))
);

-- Activity Comments table (Phase 3 - Social Interactions)
CREATE TABLE activity_comments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    activity_id VARCHAR(36) NULL,
    checkin_id VARCHAR(36) NULL,
    comment_text TEXT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (activity_id) REFERENCES user_activities(id) ON DELETE CASCADE,
    FOREIGN KEY (checkin_id) REFERENCES beer_checkins(id) ON DELETE CASCADE,
    CHECK ((activity_id IS NOT NULL AND checkin_id IS NULL) OR (activity_id IS NULL AND checkin_id IS NOT NULL))
);

-- Badges table (Phase 4 - Gamification)
CREATE TABLE badges (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category ENUM('explorer', 'connoisseur', 'social', 'seasonal', 'location', 'special') NOT NULL,
    icon VARCHAR(255) NULL, -- Badge icon/image URL
    color VARCHAR(7) DEFAULT '#007bff', -- Hex color code

    -- Badge earning criteria
    criteria_type ENUM('checkin_count', 'beer_count', 'brewery_count', 'style_count', 'rating_count', 'follower_count', 'review_count', 'special') NOT NULL,
    criteria_value INT NULL, -- Threshold value for earning
    criteria_metadata JSON NULL, -- Additional criteria data

    -- Badge properties
    is_active BOOLEAN DEFAULT TRUE,
    is_hidden BOOLEAN DEFAULT FALSE, -- Hidden until earned
    rarity ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') DEFAULT 'common',
    points_value INT DEFAULT 10, -- Points awarded for earning this badge

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User Badges table (Phase 4 - User Badge Tracking)
CREATE TABLE user_badges (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    badge_id VARCHAR(36) NOT NULL,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress_data JSON NULL, -- Track progress towards badge
    is_featured BOOLEAN DEFAULT FALSE, -- Featured on profile

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (badge_id) REFERENCES badges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_badge (user_id, badge_id)
);

-- User Statistics table (Phase 4 - Detailed User Stats)
CREATE TABLE user_statistics (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,

    -- Check-in statistics
    total_checkins INT DEFAULT 0,
    unique_beers_tried INT DEFAULT 0,
    unique_breweries_visited INT DEFAULT 0,
    unique_styles_tried INT DEFAULT 0,

    -- Rating statistics
    total_ratings INT DEFAULT 0,
    average_rating_given DECIMAL(3,2) DEFAULT 0.00,
    highest_rating_given DECIMAL(2,1) DEFAULT 0.0,
    lowest_rating_given DECIMAL(2,1) DEFAULT 5.0,

    -- Social statistics
    total_followers INT DEFAULT 0,
    total_following INT DEFAULT 0,
    total_likes_received INT DEFAULT 0,
    total_likes_given INT DEFAULT 0,

    -- Achievement statistics
    total_badges_earned INT DEFAULT 0,
    total_points_earned INT DEFAULT 0,
    current_streak_days INT DEFAULT 0,
    longest_streak_days INT DEFAULT 0,

    -- Activity statistics
    most_active_day_of_week TINYINT NULL, -- 0=Sunday, 6=Saturday
    most_active_hour_of_day TINYINT NULL, -- 0-23
    favorite_beer_style_id VARCHAR(36) NULL,

    -- Dates
    first_checkin_date DATE NULL,
    last_checkin_date DATE NULL,
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (favorite_beer_style_id) REFERENCES beer_styles(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_stats (user_id)
);

-- User Lists table (Phase 6 - Lists & Collections)
CREATE TABLE user_lists (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    list_type ENUM('custom', 'want_to_try', 'favorites', 'tried', 'wishlist') DEFAULT 'custom',
    beer_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_lists_user (user_id),
    INDEX idx_user_lists_public (is_public),
    INDEX idx_user_lists_type (list_type)
);

-- Notifications table (Phase 7 - Notification System)
CREATE TABLE notifications (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    type ENUM('new_follower', 'friend_checkin', 'beer_release', 'brewery_event', 'achievement_unlocked', 'message_received', 'rating_liked', 'comment_received', 'badge_earned', 'friend_joined') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON NULL, -- Additional notification data

    -- Related objects
    related_type ENUM('user', 'brewery', 'beer', 'checkin', 'rating', 'badge', 'message') NULL,
    related_id VARCHAR(36) NULL,

    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,

    -- Delivery preferences
    send_email BOOLEAN DEFAULT FALSE,
    send_push BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notifications_user (user_id),
    INDEX idx_notifications_unread (user_id, is_read),
    INDEX idx_notifications_type (type),
    INDEX idx_notifications_created (created_at)
);

-- Notification Preferences table (Phase 7 - User Notification Settings)
CREATE TABLE notification_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,

    -- Email notification preferences
    email_new_follower BOOLEAN DEFAULT TRUE,
    email_friend_checkin BOOLEAN DEFAULT TRUE,
    email_beer_release BOOLEAN DEFAULT FALSE,
    email_brewery_event BOOLEAN DEFAULT FALSE,
    email_achievement_unlocked BOOLEAN DEFAULT TRUE,
    email_message_received BOOLEAN DEFAULT TRUE,
    email_rating_liked BOOLEAN DEFAULT FALSE,
    email_comment_received BOOLEAN DEFAULT TRUE,

    -- Push notification preferences (for future implementation)
    push_new_follower BOOLEAN DEFAULT TRUE,
    push_friend_checkin BOOLEAN DEFAULT FALSE,
    push_beer_release BOOLEAN DEFAULT FALSE,
    push_brewery_event BOOLEAN DEFAULT FALSE,
    push_achievement_unlocked BOOLEAN DEFAULT TRUE,
    push_message_received BOOLEAN DEFAULT TRUE,
    push_rating_liked BOOLEAN DEFAULT FALSE,
    push_comment_received BOOLEAN DEFAULT TRUE,

    -- General preferences
    digest_frequency ENUM('none', 'daily', 'weekly') DEFAULT 'weekly',
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '08:00:00',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notification_prefs (user_id)
);

-- Conversations table (Phase 7 - Messaging System)
CREATE TABLE conversations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    type ENUM('direct', 'group') DEFAULT 'direct',
    title VARCHAR(255) NULL, -- For group conversations
    description TEXT NULL,
    created_by VARCHAR(36) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP NULL,
    participant_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_conversations_type (type),
    INDEX idx_conversations_active (is_active),
    INDEX idx_conversations_last_message (last_message_at)
);

-- Conversation Participants table (Phase 7 - Messaging System)
CREATE TABLE conversation_participants (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('member', 'admin') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP NULL,
    is_muted BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,

    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_conversation_participant (conversation_id, user_id),
    INDEX idx_conversation_participants_user (user_id),
    INDEX idx_conversation_participants_active (conversation_id, is_active)
);

-- Messages table (Phase 7 - Messaging System)
CREATE TABLE messages (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message_type ENUM('text', 'image', 'system') DEFAULT 'text',
    content TEXT NOT NULL,

    -- Attachments and media
    attachments JSON NULL, -- Array of attachment objects

    -- Message status
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL,

    -- Moderation
    is_flagged BOOLEAN DEFAULT FALSE,
    flagged_reason VARCHAR(255) NULL,
    flagged_by VARCHAR(36) NULL,
    moderated_by VARCHAR(36) NULL,
    moderated_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flagged_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_messages_conversation (conversation_id),
    INDEX idx_messages_sender (sender_id),
    INDEX idx_messages_created (created_at),
    INDEX idx_messages_flagged (is_flagged)
);

-- Message Read Status table (Phase 7 - Messaging System)
CREATE TABLE message_read_status (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    message_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_read (message_id, user_id),
    INDEX idx_message_read_user (user_id)
);

-- User List Items table (Phase 6 - List Items)
CREATE TABLE user_list_items (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    list_id VARCHAR(36) NOT NULL,
    beer_id VARCHAR(36) NOT NULL,
    notes TEXT NULL,
    priority INT DEFAULT 0,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (list_id) REFERENCES user_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
    UNIQUE KEY unique_list_beer (list_id, beer_id),
    INDEX idx_list_items_list (list_id),
    INDEX idx_list_items_beer (beer_id),
    INDEX idx_list_items_priority (priority)
);

-- Photos table (Phase 6.3 - Photo & Media Features)
CREATE TABLE photos (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    thumbnail_path VARCHAR(500) NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    width INT NULL,
    height INT NULL,

    -- Photo categorization
    type ENUM('checkin', 'beer', 'brewery', 'user', 'review', 'general') NOT NULL,
    target_id VARCHAR(36) NULL,
    user_id VARCHAR(36) NOT NULL,

    -- Photo metadata
    title VARCHAR(255) NULL,
    description TEXT NULL,
    alt_text VARCHAR(255) NULL,

    -- Photo properties
    is_featured BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,

    -- Engagement
    like_count INT DEFAULT 0,
    view_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_photos_type_target (type, target_id),
    INDEX idx_photos_user (user_id),
    INDEX idx_photos_created (created_at DESC),
    INDEX idx_photos_featured (is_featured),
    INDEX idx_photos_public (is_public)
);

-- Photo Tags table (Phase 6.3 - Photo Tagging)
CREATE TABLE photo_tags (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    photo_id VARCHAR(36) NOT NULL,
    tag_type ENUM('user', 'beer', 'brewery', 'location', 'custom') NOT NULL,
    tag_value VARCHAR(255) NOT NULL,
    tag_id VARCHAR(36) NULL, -- Reference to tagged entity
    x_position DECIMAL(5,2) NULL, -- For positioning tags on image (0-100%)
    y_position DECIMAL(5,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
    INDEX idx_photo_tags_photo (photo_id),
    INDEX idx_photo_tags_type (tag_type),
    INDEX idx_photo_tags_value (tag_value)
);

-- Beer Ratings table (Phase 2)
CREATE TABLE beer_ratings (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    beer_id VARCHAR(36) NOT NULL,
    brewery_id VARCHAR(36) NOT NULL,

    -- Overall rating and detailed ratings
    overall_rating DECIMAL(2,1) NOT NULL CHECK (overall_rating >= 0.5 AND overall_rating <= 5.0),
    taste_rating DECIMAL(2,1) NULL CHECK (taste_rating >= 0.5 AND taste_rating <= 5.0),
    aroma_rating DECIMAL(2,1) NULL CHECK (aroma_rating >= 0.5 AND aroma_rating <= 5.0),
    appearance_rating DECIMAL(2,1) NULL CHECK (appearance_rating >= 0.5 AND appearance_rating <= 5.0),
    mouthfeel_rating DECIMAL(2,1) NULL CHECK (mouthfeel_rating >= 0.5 AND mouthfeel_rating <= 5.0),

    -- Review content
    review_text TEXT NULL,
    review_title VARCHAR(255) NULL,

    -- Check-in information
    checkin_location VARCHAR(255) NULL,
    checkin_latitude DECIMAL(10, 8) NULL,
    checkin_longitude DECIMAL(11, 8) NULL,
    serving_style ENUM('draft', 'bottle', 'can', 'growler', 'other') NULL,

    -- Media
    photos JSON NULL, -- array of photo URLs

    -- Metadata
    device_info VARCHAR(255) NULL,
    is_public BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE, -- for expert reviews
    helpful_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_beer_rating (user_id, beer_id)
);

-- Beer Rating Helpfulness table (Phase 2)
CREATE TABLE beer_rating_helpfulness (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    rating_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_rating_helpfulness (rating_id, user_id)
);

-- Beer Rating Reports table (Phase 2 - Moderation)
CREATE TABLE beer_rating_reports (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    rating_id VARCHAR(36) NOT NULL,
    reporter_user_id VARCHAR(36) NOT NULL,
    reason ENUM('spam', 'inappropriate', 'fake', 'offensive', 'other') NOT NULL,
    description TEXT NULL,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (rating_id) REFERENCES beer_ratings(id) ON DELETE CASCADE,
    FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_brewery_id ON profiles(brewery_id);
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_visibility ON profiles(profile_visibility);
CREATE INDEX idx_breweries_city_state ON breweries(city, state);
CREATE INDEX idx_breweries_claimed ON breweries(claimed);
CREATE INDEX idx_breweries_location ON breweries(latitude, longitude);

-- Beer and style indexes
CREATE INDEX idx_beer_styles_category ON beer_styles(category);
CREATE INDEX idx_beer_styles_active ON beer_styles(is_active);
CREATE INDEX idx_beer_menu_brewery_id ON beer_menu(brewery_id);
CREATE INDEX idx_beer_menu_style_id ON beer_menu(beer_style_id);
CREATE INDEX idx_beer_menu_available ON beer_menu(available);
CREATE INDEX idx_beer_menu_featured ON beer_menu(featured);
CREATE INDEX idx_beer_menu_seasonal ON beer_menu(seasonal);
CREATE INDEX idx_beer_menu_rating ON beer_menu(average_rating DESC);
CREATE INDEX idx_beer_menu_abv ON beer_menu(abv);
CREATE INDEX idx_beer_menu_ibu ON beer_menu(ibu);

-- Rating system indexes
CREATE INDEX idx_beer_ratings_user ON beer_ratings(user_id);
CREATE INDEX idx_beer_ratings_beer ON beer_ratings(beer_id);
CREATE INDEX idx_beer_ratings_brewery ON beer_ratings(brewery_id);
CREATE INDEX idx_beer_ratings_overall ON beer_ratings(overall_rating DESC);
CREATE INDEX idx_beer_ratings_created ON beer_ratings(created_at DESC);
CREATE INDEX idx_beer_ratings_public ON beer_ratings(is_public);
CREATE INDEX idx_beer_ratings_location ON beer_ratings(checkin_latitude, checkin_longitude);
CREATE INDEX idx_beer_rating_helpfulness_rating ON beer_rating_helpfulness(rating_id);
CREATE INDEX idx_beer_rating_reports_status ON beer_rating_reports(status);

-- Other indexes
CREATE INDEX idx_food_menu_brewery_id ON food_menu(brewery_id);
CREATE INDEX idx_posts_brewery_id ON posts(brewery_id);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX idx_user_follows_follower ON user_follows(follower_id);
CREATE INDEX idx_user_follows_following ON user_follows(following_id);
CREATE INDEX idx_user_activities_user ON user_activities(user_id);
CREATE INDEX idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX idx_user_activities_created ON user_activities(created_at DESC);

-- Check-in system indexes
CREATE INDEX idx_beer_checkins_user ON beer_checkins(user_id);
CREATE INDEX idx_beer_checkins_beer ON beer_checkins(beer_id);
CREATE INDEX idx_beer_checkins_brewery ON beer_checkins(brewery_id);
CREATE INDEX idx_beer_checkins_created ON beer_checkins(created_at DESC);
CREATE INDEX idx_beer_checkins_public ON beer_checkins(is_public);
CREATE INDEX idx_beer_checkins_location ON beer_checkins(checkin_latitude, checkin_longitude);

-- Social interaction indexes
CREATE INDEX idx_activity_likes_user ON activity_likes(user_id);
CREATE INDEX idx_activity_likes_activity ON activity_likes(activity_id);
CREATE INDEX idx_activity_likes_checkin ON activity_likes(checkin_id);
CREATE INDEX idx_activity_comments_user ON activity_comments(user_id);
CREATE INDEX idx_activity_comments_activity ON activity_comments(activity_id);
CREATE INDEX idx_activity_comments_checkin ON activity_comments(checkin_id);
CREATE INDEX idx_activity_comments_created ON activity_comments(created_at DESC);

-- Badge system indexes (Phase 4)
CREATE INDEX idx_badges_category ON badges(category);
CREATE INDEX idx_badges_criteria_type ON badges(criteria_type);
CREATE INDEX idx_badges_active ON badges(is_active);
CREATE INDEX idx_badges_rarity ON badges(rarity);
CREATE INDEX idx_user_badges_user ON user_badges(user_id);
CREATE INDEX idx_user_badges_badge ON user_badges(badge_id);
CREATE INDEX idx_user_badges_earned ON user_badges(earned_at DESC);
CREATE INDEX idx_user_badges_featured ON user_badges(is_featured);
CREATE INDEX idx_user_statistics_user ON user_statistics(user_id);
CREATE INDEX idx_user_statistics_calculated ON user_statistics(last_calculated_at);

-- Insert default admin user (password: admin123)
INSERT INTO users (id, email, password_hash) VALUES 
('admin-user-id', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

INSERT INTO profiles (id, email, role) VALUES 
('admin-user-id', '<EMAIL>', 'admin');
