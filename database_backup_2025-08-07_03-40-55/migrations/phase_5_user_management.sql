-- Phase 5 User Management & Authentication
-- Database Migration Script
-- Created: December 16, 2024

-- =====================================================
-- User Brewery Permissions Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_brewery_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    brewery_id VARCHAR(50) NOT NULL,
    permission VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    granted_by VARCHAR(50),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_brewery (user_id, brewery_id),
    INDEX idx_permission (permission),
    INDEX idx_active (is_active),
    UNIQUE KEY unique_user_brewery_permission (user_id, brewery_id, permission)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Activity Log Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_user_action (user_id, action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Sessions Table (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    login_method VARCHAR(50) DEFAULT 'password',
    two_factor_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_active (is_active),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Login Attempts Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(255),
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_ip_address (ip_address),
    INDEX idx_attempted_at (attempted_at),
    INDEX idx_success (success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Password Reset Tokens Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User Two-Factor Authentication Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_two_factor_auth (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE,
    secret VARCHAR(255) NOT NULL,
    backup_codes JSON,
    is_enabled BOOLEAN DEFAULT FALSE,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Update existing users table with enhanced fields
-- =====================================================
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS login_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS failed_login_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255),
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS must_change_password BOOLEAN DEFAULT FALSE;

-- Add indexes for performance
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_last_login (last_login),
ADD INDEX IF NOT EXISTS idx_email_verified (email_verified),
ADD INDEX IF NOT EXISTS idx_two_factor_enabled (two_factor_enabled),
ADD INDEX IF NOT EXISTS idx_account_locked (account_locked_until);

-- =====================================================
-- Update existing profiles table with enhanced fields
-- =====================================================
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'UTC',
ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'en',
ADD COLUMN IF NOT EXISTS notification_preferences JSON,
ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255),
ADD COLUMN IF NOT EXISTS last_profile_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Add indexes
ALTER TABLE profiles 
ADD INDEX IF NOT EXISTS idx_timezone (timezone),
ADD INDEX IF NOT EXISTS idx_language (language);

-- =====================================================
-- User Notification Preferences Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    frequency VARCHAR(50) DEFAULT 'immediate',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_notification_type (notification_type),
    UNIQUE KEY unique_user_notification (user_id, notification_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- User API Keys Table
-- =====================================================
CREATE TABLE IF NOT EXISTS user_api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    key_name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL UNIQUE,
    permissions JSON,
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_api_key (api_key),
    INDEX idx_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Insert default notification preferences for existing users
-- =====================================================
INSERT IGNORE INTO user_notification_preferences (user_id, notification_type, email_enabled, push_enabled)
SELECT id, 'user_created', TRUE, TRUE FROM users
UNION ALL
SELECT id, 'user_updated', TRUE, FALSE FROM users
UNION ALL
SELECT id, 'password_changed', TRUE, TRUE FROM users
UNION ALL
SELECT id, 'login_alert', FALSE, FALSE FROM users
UNION ALL
SELECT id, 'digital_board_alert', TRUE, TRUE FROM users
UNION ALL
SELECT id, 'system_maintenance', TRUE, FALSE FROM users;

-- =====================================================
-- Create default permissions for existing business users
-- =====================================================
INSERT IGNORE INTO user_brewery_permissions (user_id, brewery_id, permission, granted_by)
SELECT 
    p.id,
    p.brewery_id,
    CASE 
        WHEN p.role = 'business_owner' THEN 'manage_brewery_users'
        WHEN p.role = 'business_manager' THEN 'view_analytics'
        WHEN p.role = 'digital_board_operator' THEN 'manage_slides'
        ELSE 'view_boards'
    END,
    'system'
FROM profiles p
WHERE p.brewery_id IS NOT NULL 
AND p.role IN ('business_owner', 'business_manager', 'digital_board_operator');

-- =====================================================
-- Create views for easier user management
-- =====================================================
CREATE OR REPLACE VIEW user_summary AS
SELECT 
    u.id,
    u.email,
    u.first_name,
    u.last_name,
    u.role,
    u.status,
    u.last_login,
    u.login_count,
    u.email_verified,
    u.two_factor_enabled,
    p.brewery_id,
    b.name as brewery_name,
    p.phone,
    p.city,
    p.state,
    (SELECT COUNT(*) FROM user_activity_log WHERE user_id = u.id) as activity_count,
    (SELECT COUNT(*) FROM user_brewery_permissions WHERE user_id = u.id AND is_active = TRUE) as permission_count,
    u.created_at,
    u.updated_at
FROM users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN breweries b ON p.brewery_id = b.id;

-- =====================================================
-- Create view for user permissions summary
-- =====================================================
CREATE OR REPLACE VIEW user_permissions_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.role,
    p.brewery_id,
    b.name as brewery_name,
    GROUP_CONCAT(DISTINCT ubp.permission ORDER BY ubp.permission) as permissions,
    COUNT(DISTINCT ubp.permission) as permission_count
FROM users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN breweries b ON p.brewery_id = b.id
LEFT JOIN user_brewery_permissions ubp ON u.id = ubp.user_id AND ubp.is_active = TRUE
GROUP BY u.id, u.email, u.role, p.brewery_id, b.name;

-- =====================================================
-- Update schema version
-- =====================================================
INSERT INTO schema_versions (version, description, applied_at) 
VALUES ('5.0.0', 'Phase 5 - Enhanced User Management & Authentication', NOW())
ON DUPLICATE KEY UPDATE applied_at = NOW();

-- =====================================================
-- Performance optimization indexes
-- =====================================================
-- Optimize user lookup queries
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_email_status (email, status);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_role_status (role, status);

-- Optimize activity log queries
ALTER TABLE user_activity_log ADD INDEX IF NOT EXISTS idx_user_created (user_id, created_at);

-- Optimize permission queries
ALTER TABLE user_brewery_permissions ADD INDEX IF NOT EXISTS idx_brewery_permission (brewery_id, permission, is_active);

-- =====================================================
-- Data cleanup and maintenance
-- =====================================================
-- Clean up old login attempts (keep last 30 days)
DELETE FROM user_login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean up expired password reset tokens
DELETE FROM user_password_reset_tokens WHERE expires_at < NOW();

-- Clean up old activity logs (keep last 90 days for non-admin users)
DELETE FROM user_activity_log 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
AND user_id NOT IN (SELECT id FROM users WHERE role = 'admin');

COMMIT;
