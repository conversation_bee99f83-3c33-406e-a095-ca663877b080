-- Phase 7 Database Update: Notifications & Communication
-- Run this script to add Phase 7 tables to existing database

USE beersty_db;

-- Notifications table (Phase 7 - Notification System)
CREATE TABLE IF NOT EXISTS notifications (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    type ENUM('new_follower', 'friend_checkin', 'beer_release', 'brewery_event', 'achievement_unlocked', 'message_received', 'rating_liked', 'comment_received', 'badge_earned', 'friend_joined') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON NULL, -- Additional notification data
    
    -- Related objects
    related_type ENUM('user', 'brewery', 'beer', 'checkin', 'rating', 'badge', 'message') NULL,
    related_id VARCHAR(36) NULL,
    
    -- Status
    is_read BOOLEAN DEFAULT FALSE,
    is_sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP NULL,
    
    -- Delivery preferences
    send_email BOOLEAN DEFAULT FALSE,
    send_push BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notifications_user (user_id),
    INDEX idx_notifications_unread (user_id, is_read),
    INDEX idx_notifications_type (type),
    INDEX idx_notifications_created (created_at)
);

-- Notification Preferences table (Phase 7 - User Notification Settings)
CREATE TABLE IF NOT EXISTS notification_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Email notification preferences
    email_new_follower BOOLEAN DEFAULT TRUE,
    email_friend_checkin BOOLEAN DEFAULT TRUE,
    email_beer_release BOOLEAN DEFAULT FALSE,
    email_brewery_event BOOLEAN DEFAULT FALSE,
    email_achievement_unlocked BOOLEAN DEFAULT TRUE,
    email_message_received BOOLEAN DEFAULT TRUE,
    email_rating_liked BOOLEAN DEFAULT FALSE,
    email_comment_received BOOLEAN DEFAULT TRUE,
    
    -- Push notification preferences (for future implementation)
    push_new_follower BOOLEAN DEFAULT TRUE,
    push_friend_checkin BOOLEAN DEFAULT FALSE,
    push_beer_release BOOLEAN DEFAULT FALSE,
    push_brewery_event BOOLEAN DEFAULT FALSE,
    push_achievement_unlocked BOOLEAN DEFAULT TRUE,
    push_message_received BOOLEAN DEFAULT TRUE,
    push_rating_liked BOOLEAN DEFAULT FALSE,
    push_comment_received BOOLEAN DEFAULT TRUE,
    
    -- General preferences
    digest_frequency ENUM('none', 'daily', 'weekly') DEFAULT 'weekly',
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '08:00:00',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notification_prefs (user_id)
);

-- Conversations table (Phase 7 - Messaging System)
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    type ENUM('direct', 'group') DEFAULT 'direct',
    title VARCHAR(255) NULL, -- For group conversations
    description TEXT NULL,
    created_by VARCHAR(36) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP NULL,
    participant_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_conversations_type (type),
    INDEX idx_conversations_active (is_active),
    INDEX idx_conversations_last_message (last_message_at)
);

-- Conversation Participants table (Phase 7 - Messaging System)
CREATE TABLE IF NOT EXISTS conversation_participants (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('member', 'admin') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP NULL,
    is_muted BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_conversation_participant (conversation_id, user_id),
    INDEX idx_conversation_participants_user (user_id),
    INDEX idx_conversation_participants_active (conversation_id, is_active)
);

-- Messages table (Phase 7 - Messaging System)
CREATE TABLE IF NOT EXISTS messages (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message_type ENUM('text', 'image', 'system') DEFAULT 'text',
    content TEXT NOT NULL,
    
    -- Attachments and media
    attachments JSON NULL, -- Array of attachment objects
    
    -- Message status
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL,
    
    -- Moderation
    is_flagged BOOLEAN DEFAULT FALSE,
    flagged_reason VARCHAR(255) NULL,
    flagged_by VARCHAR(36) NULL,
    moderated_by VARCHAR(36) NULL,
    moderated_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flagged_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_messages_conversation (conversation_id),
    INDEX idx_messages_sender (sender_id),
    INDEX idx_messages_created (created_at),
    INDEX idx_messages_flagged (is_flagged)
);

-- Message Read Status table (Phase 7 - Messaging System)
CREATE TABLE IF NOT EXISTS message_read_status (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    message_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_read (message_id, user_id),
    INDEX idx_message_read_user (user_id)
);

-- Create default notification preferences for existing users
INSERT IGNORE INTO notification_preferences (user_id)
SELECT id FROM users WHERE id NOT IN (SELECT user_id FROM notification_preferences);

-- Update existing activity types to include new notification types
ALTER TABLE user_activities 
MODIFY COLUMN activity_type ENUM(
    'profile_update', 
    'brewery_follow', 
    'brewery_like', 
    'joined', 
    'beer_rating', 
    'beer_review', 
    'beer_checkin', 
    'user_follow',
    'badge_earned',
    'message_sent',
    'notification_sent'
) NOT NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_activities_type_user ON user_activities(activity_type, user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_target ON user_activities(target_type, target_id);

-- Show completion message
SELECT 'Phase 7 database update completed successfully!' as status;
