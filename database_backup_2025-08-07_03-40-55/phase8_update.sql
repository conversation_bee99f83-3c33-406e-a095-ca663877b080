-- Phase 8 Database Update: Analytics & Business Intelligence
-- Run this script to add Phase 8 enhancements to existing database

USE beersty_db;

-- Add analytics-specific indexes for better performance
CREATE INDEX IF NOT EXISTS idx_beer_checkins_year ON beer_checkins(user_id, YEAR(created_at));
CREATE INDEX IF NOT EXISTS idx_beer_checkins_month ON beer_checkins(user_id, YEAR(created_at), MONTH(created_at));
CREATE INDEX IF NOT EXISTS idx_beer_checkins_rating ON beer_checkins(user_id, rating);
CREATE INDEX IF NOT EXISTS idx_beer_checkins_beer_rating ON beer_checkins(beer_id, rating);

CREATE INDEX IF NOT EXISTS idx_beer_ratings_year ON beer_ratings(user_id, YEAR(created_at));
CREATE INDEX IF NOT EXISTS idx_beer_ratings_overall ON beer_ratings(overall_rating);
CREATE INDEX IF NOT EXISTS idx_beer_ratings_beer ON beer_ratings(beer_id, overall_rating);

CREATE INDEX IF NOT EXISTS idx_user_activities_year ON user_activities(user_id, YEAR(created_at));
CREATE INDEX IF NOT EXISTS idx_user_activities_type_year ON user_activities(activity_type, YEAR(created_at));
CREATE INDEX IF NOT EXISTS idx_user_activities_daily ON user_activities(DATE(created_at));

CREATE INDEX IF NOT EXISTS idx_user_badges_year ON user_badges(user_id, YEAR(earned_at));
CREATE INDEX IF NOT EXISTS idx_user_follows_created ON user_follows(followed_id, created_at);

-- Add analytics tracking table for admin insights
CREATE TABLE IF NOT EXISTS analytics_events (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    event_type ENUM('page_view', 'user_action', 'feature_usage', 'error', 'performance') NOT NULL,
    event_name VARCHAR(100) NOT NULL,
    user_id VARCHAR(36) NULL,
    session_id VARCHAR(100) NULL,
    
    -- Event data
    page_url VARCHAR(500) NULL,
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    
    -- Custom event data
    event_data JSON NULL,
    
    -- Performance metrics
    load_time_ms INT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_analytics_events_type (event_type),
    INDEX idx_analytics_events_name (event_name),
    INDEX idx_analytics_events_user (user_id),
    INDEX idx_analytics_events_created (created_at),
    INDEX idx_analytics_events_session (session_id)
);

-- Add user session tracking for better analytics
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NULL,
    session_id VARCHAR(100) NOT NULL,
    
    -- Session info
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
    browser VARCHAR(50) NULL,
    os VARCHAR(50) NULL,
    
    -- Session metrics
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    duration_seconds INT NULL,
    page_views INT DEFAULT 0,
    
    -- Geographic data (if available)
    country VARCHAR(2) NULL,
    region VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_sessions_user (user_id),
    INDEX idx_user_sessions_session (session_id),
    INDEX idx_user_sessions_started (started_at),
    INDEX idx_user_sessions_device (device_type)
);

-- Add analytics summary tables for faster reporting
CREATE TABLE IF NOT EXISTS daily_analytics_summary (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    date DATE NOT NULL,
    
    -- User metrics
    total_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    returning_users INT DEFAULT 0,
    
    -- Activity metrics
    total_checkins INT DEFAULT 0,
    total_ratings INT DEFAULT 0,
    total_follows INT DEFAULT 0,
    total_badges_earned INT DEFAULT 0,
    
    -- Engagement metrics
    avg_session_duration_seconds INT DEFAULT 0,
    total_page_views INT DEFAULT 0,
    bounce_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Content metrics
    new_beers_added INT DEFAULT 0,
    new_breweries_added INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date (date),
    INDEX idx_daily_analytics_date (date)
);

-- Add monthly analytics summary
CREATE TABLE IF NOT EXISTS monthly_analytics_summary (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    year INT NOT NULL,
    month INT NOT NULL,
    
    -- User metrics
    total_users INT DEFAULT 0,
    new_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    user_retention_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Activity metrics
    total_checkins INT DEFAULT 0,
    total_ratings INT DEFAULT 0,
    total_follows INT DEFAULT 0,
    total_badges_earned INT DEFAULT 0,
    
    -- Popular content
    top_beer_style VARCHAR(100) NULL,
    top_brewery VARCHAR(100) NULL,
    avg_rating DECIMAL(3,2) DEFAULT 0.00,
    
    -- Geographic insights
    top_city VARCHAR(100) NULL,
    top_state VARCHAR(100) NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_year_month (year, month),
    INDEX idx_monthly_analytics_year_month (year, month)
);

-- Add user analytics preferences
CREATE TABLE IF NOT EXISTS user_analytics_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Privacy settings
    allow_analytics_tracking BOOLEAN DEFAULT TRUE,
    allow_performance_tracking BOOLEAN DEFAULT TRUE,
    allow_location_tracking BOOLEAN DEFAULT FALSE,
    
    -- Sharing preferences
    share_statistics_publicly BOOLEAN DEFAULT FALSE,
    share_year_in_review BOOLEAN DEFAULT TRUE,
    share_achievements BOOLEAN DEFAULT TRUE,
    
    -- Notification preferences for insights
    notify_monthly_summary BOOLEAN DEFAULT TRUE,
    notify_year_in_review BOOLEAN DEFAULT TRUE,
    notify_milestone_achievements BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_analytics_prefs (user_id)
);

-- Create default analytics preferences for existing users
INSERT IGNORE INTO user_analytics_preferences (user_id)
SELECT id FROM users WHERE id NOT IN (SELECT user_id FROM user_analytics_preferences);

-- Add stored procedures for analytics calculations (MySQL 5.7+)
DELIMITER //

-- Procedure to calculate daily analytics summary
CREATE PROCEDURE IF NOT EXISTS CalculateDailyAnalytics(IN target_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO daily_analytics_summary (
        date, total_users, new_users, active_users, total_checkins, 
        total_ratings, total_follows, total_badges_earned
    )
    SELECT 
        target_date,
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) <= target_date),
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = target_date),
        (SELECT COUNT(DISTINCT user_id) FROM user_activities WHERE DATE(created_at) = target_date),
        (SELECT COUNT(*) FROM beer_checkins WHERE DATE(created_at) = target_date),
        (SELECT COUNT(*) FROM beer_ratings WHERE DATE(created_at) = target_date),
        (SELECT COUNT(*) FROM user_follows WHERE DATE(created_at) = target_date),
        (SELECT COUNT(*) FROM user_badges WHERE DATE(earned_at) = target_date)
    ON DUPLICATE KEY UPDATE
        total_users = VALUES(total_users),
        new_users = VALUES(new_users),
        active_users = VALUES(active_users),
        total_checkins = VALUES(total_checkins),
        total_ratings = VALUES(total_ratings),
        total_follows = VALUES(total_follows),
        total_badges_earned = VALUES(total_badges_earned),
        updated_at = NOW();
    
    COMMIT;
END //

-- Procedure to calculate monthly analytics summary
CREATE PROCEDURE IF NOT EXISTS CalculateMonthlyAnalytics(IN target_year INT, IN target_month INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO monthly_analytics_summary (
        year, month, total_users, new_users, active_users, 
        total_checkins, total_ratings, total_follows, total_badges_earned
    )
    SELECT 
        target_year,
        target_month,
        (SELECT COUNT(*) FROM users WHERE YEAR(created_at) <= target_year AND MONTH(created_at) <= target_month),
        (SELECT COUNT(*) FROM users WHERE YEAR(created_at) = target_year AND MONTH(created_at) = target_month),
        (SELECT COUNT(DISTINCT user_id) FROM user_activities WHERE YEAR(created_at) = target_year AND MONTH(created_at) = target_month),
        (SELECT COUNT(*) FROM beer_checkins WHERE YEAR(created_at) = target_year AND MONTH(created_at) = target_month),
        (SELECT COUNT(*) FROM beer_ratings WHERE YEAR(created_at) = target_year AND MONTH(created_at) = target_month),
        (SELECT COUNT(*) FROM user_follows WHERE YEAR(created_at) = target_year AND MONTH(created_at) = target_month),
        (SELECT COUNT(*) FROM user_badges WHERE YEAR(earned_at) = target_year AND MONTH(earned_at) = target_month)
    ON DUPLICATE KEY UPDATE
        total_users = VALUES(total_users),
        new_users = VALUES(new_users),
        active_users = VALUES(active_users),
        total_checkins = VALUES(total_checkins),
        total_ratings = VALUES(total_ratings),
        total_follows = VALUES(total_follows),
        total_badges_earned = VALUES(total_badges_earned),
        updated_at = NOW();
    
    COMMIT;
END //

DELIMITER ;

-- Create events for automatic analytics calculation (MySQL 5.7+ with event scheduler)
-- Note: Event scheduler must be enabled: SET GLOBAL event_scheduler = ON;

-- Daily analytics calculation event
CREATE EVENT IF NOT EXISTS daily_analytics_calculation
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '01:00:00')
DO
  CALL CalculateDailyAnalytics(CURRENT_DATE - INTERVAL 1 DAY);

-- Monthly analytics calculation event  
CREATE EVENT IF NOT EXISTS monthly_analytics_calculation
ON SCHEDULE EVERY 1 MONTH
STARTS TIMESTAMP(LAST_DAY(CURRENT_DATE) + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CalculateMonthlyAnalytics(YEAR(CURRENT_DATE - INTERVAL 1 MONTH), MONTH(CURRENT_DATE - INTERVAL 1 MONTH));

-- Add analytics views for common queries
CREATE OR REPLACE VIEW user_engagement_summary AS
SELECT 
    u.id as user_id,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    u.email,
    u.role,
    us.total_checkins,
    us.unique_beers_tried,
    us.unique_breweries_visited,
    us.total_ratings,
    us.average_rating_given,
    us.total_followers,
    us.total_following,
    us.total_badges_earned,
    us.total_points_earned,
    DATEDIFF(CURRENT_DATE, u.created_at) as days_since_joined,
    COALESCE(recent_activity.last_activity, u.created_at) as last_activity_date,
    CASE 
        WHEN recent_activity.last_activity >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY) THEN 'Active'
        WHEN recent_activity.last_activity >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY) THEN 'Inactive'
        ELSE 'Dormant'
    END as engagement_status
FROM users u
JOIN profiles p ON u.id = p.id
LEFT JOIN user_statistics us ON u.id = us.user_id
LEFT JOIN (
    SELECT user_id, MAX(created_at) as last_activity
    FROM user_activities
    GROUP BY user_id
) recent_activity ON u.id = recent_activity.user_id;

-- Show completion message
SELECT 'Phase 8 database update completed successfully!' as status,
       'Analytics & Business Intelligence features are now available!' as message;
