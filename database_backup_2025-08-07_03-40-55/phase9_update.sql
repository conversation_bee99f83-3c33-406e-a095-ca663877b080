-- Phase 9 Database Update: Design & Mobile Optimization
-- Run this script to add Phase 9 enhancements to existing database

USE beersty_db;

-- Add PWA and mobile-specific user preferences
CREATE TABLE IF NOT EXISTS user_pwa_preferences (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- PWA Installation
    pwa_installed BOOLEAN DEFAULT FALSE,
    install_prompt_dismissed_at TIMESTAMP NULL,
    install_source ENUM('browser_prompt', 'manual', 'share_target') NULL,
    
    -- Notification Preferences
    push_notifications_enabled BOOLEAN DEFAULT FALSE,
    push_subscription_data JSON NULL,
    notification_types JSON NULL, -- Array of enabled notification types
    
    -- Mobile Preferences
    mobile_bottom_nav_enabled BOOLEAN DEFAULT TRUE,
    mobile_gestures_enabled BOOLEAN DEFAULT TRUE,
    mobile_haptic_feedback BOOLEAN DEFAULT TRUE,
    mobile_auto_hide_nav BOOLEAN DEFAULT FALSE,
    
    -- Offline Preferences
    offline_mode_enabled BOOLEAN DEFAULT TRUE,
    offline_sync_wifi_only BOOLEAN DEFAULT FALSE,
    offline_cache_size_mb INT DEFAULT 50,
    
    -- Display Preferences
    theme_preference ENUM('light', 'dark', 'auto') DEFAULT 'auto',
    reduce_motion BOOLEAN DEFAULT FALSE,
    high_contrast BOOLEAN DEFAULT FALSE,
    font_size_scale DECIMAL(3,2) DEFAULT 1.00,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_pwa_prefs (user_id),
    INDEX idx_pwa_prefs_user (user_id),
    INDEX idx_pwa_prefs_installed (pwa_installed),
    INDEX idx_pwa_prefs_push (push_notifications_enabled)
);

-- Add mobile device tracking
CREATE TABLE IF NOT EXISTS user_devices (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Device Information
    device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
    device_name VARCHAR(255) NULL,
    operating_system VARCHAR(100) NULL,
    browser VARCHAR(100) NULL,
    browser_version VARCHAR(50) NULL,
    
    -- Screen Information
    screen_width INT NULL,
    screen_height INT NULL,
    pixel_ratio DECIMAL(3,2) NULL,
    color_depth INT NULL,
    
    -- PWA Information
    is_pwa_installed BOOLEAN DEFAULT FALSE,
    pwa_display_mode ENUM('browser', 'standalone', 'minimal-ui', 'fullscreen') DEFAULT 'browser',
    supports_service_worker BOOLEAN DEFAULT FALSE,
    supports_push_notifications BOOLEAN DEFAULT FALSE,
    
    -- Capabilities
    supports_touch BOOLEAN DEFAULT FALSE,
    supports_geolocation BOOLEAN DEFAULT FALSE,
    supports_camera BOOLEAN DEFAULT FALSE,
    supports_vibration BOOLEAN DEFAULT FALSE,
    supports_web_share BOOLEAN DEFAULT FALSE,
    
    -- Network Information
    connection_type ENUM('wifi', 'cellular', 'ethernet', 'unknown') DEFAULT 'unknown',
    connection_speed ENUM('slow-2g', '2g', '3g', '4g', '5g', 'unknown') DEFAULT 'unknown',
    
    -- Usage Tracking
    first_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    total_sessions INT DEFAULT 1,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_devices_user (user_id),
    INDEX idx_user_devices_type (device_type),
    INDEX idx_user_devices_pwa (is_pwa_installed),
    INDEX idx_user_devices_last_seen (last_seen_at)
);

-- Add PWA analytics tracking
CREATE TABLE IF NOT EXISTS pwa_analytics (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NULL,
    session_id VARCHAR(100) NOT NULL,
    
    -- Event Information
    event_type ENUM('install_prompt_shown', 'install_prompt_accepted', 'install_prompt_dismissed', 
                   'app_installed', 'app_launched', 'offline_usage', 'push_notification_received',
                   'push_notification_clicked', 'share_used', 'gesture_used') NOT NULL,
    event_data JSON NULL,
    
    -- Context
    page_url VARCHAR(500) NULL,
    referrer VARCHAR(500) NULL,
    user_agent TEXT NULL,
    
    -- Device Context
    device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
    is_pwa_mode BOOLEAN DEFAULT FALSE,
    is_offline BOOLEAN DEFAULT FALSE,
    
    -- Timing
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_pwa_analytics_user (user_id),
    INDEX idx_pwa_analytics_type (event_type),
    INDEX idx_pwa_analytics_session (session_id),
    INDEX idx_pwa_analytics_timestamp (event_timestamp),
    INDEX idx_pwa_analytics_device (device_type)
);

-- Add mobile-specific user interactions
CREATE TABLE IF NOT EXISTS mobile_interactions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NULL,
    session_id VARCHAR(100) NOT NULL,
    
    -- Interaction Type
    interaction_type ENUM('swipe', 'pinch', 'tap', 'long_press', 'pull_to_refresh', 
                         'bottom_nav_tap', 'gesture_navigation') NOT NULL,
    
    -- Interaction Data
    element_type VARCHAR(100) NULL, -- button, card, list-item, etc.
    element_id VARCHAR(255) NULL,
    page_url VARCHAR(500) NULL,
    
    -- Gesture Details (for swipe, pinch, etc.)
    gesture_data JSON NULL, -- direction, distance, speed, etc.
    
    -- Performance Metrics
    response_time_ms INT NULL,
    was_successful BOOLEAN DEFAULT TRUE,
    
    -- Context
    device_orientation ENUM('portrait', 'landscape') DEFAULT 'portrait',
    viewport_width INT NULL,
    viewport_height INT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_mobile_interactions_user (user_id),
    INDEX idx_mobile_interactions_type (interaction_type),
    INDEX idx_mobile_interactions_session (session_id),
    INDEX idx_mobile_interactions_created (created_at)
);

-- Add offline data queue for background sync
CREATE TABLE IF NOT EXISTS offline_sync_queue (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Sync Information
    action_type ENUM('checkin', 'rating', 'review', 'follow', 'like', 'comment') NOT NULL,
    action_data JSON NOT NULL,
    
    -- Status
    sync_status ENUM('pending', 'syncing', 'completed', 'failed') DEFAULT 'pending',
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    
    -- Error Information
    last_error TEXT NULL,
    last_error_at TIMESTAMP NULL,
    
    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    synced_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_offline_sync_user (user_id),
    INDEX idx_offline_sync_status (sync_status),
    INDEX idx_offline_sync_type (action_type),
    INDEX idx_offline_sync_created (created_at)
);

-- Create default PWA preferences for existing users
INSERT IGNORE INTO user_pwa_preferences (user_id)
SELECT id FROM users WHERE id NOT IN (SELECT user_id FROM user_pwa_preferences);

-- Add indexes for mobile performance optimization
CREATE INDEX IF NOT EXISTS idx_beer_checkins_mobile ON beer_checkins(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_beer_ratings_mobile ON beer_ratings(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_activities_mobile ON user_activities(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_follows_mobile ON user_follows(follower_id, created_at DESC);

-- Add mobile-optimized views
CREATE OR REPLACE VIEW mobile_user_summary AS
SELECT 
    u.id as user_id,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    u.email,
    u.role,
    
    -- PWA Status
    upp.pwa_installed,
    upp.push_notifications_enabled,
    upp.theme_preference,
    
    -- Device Info
    ud.device_type,
    ud.is_pwa_installed as device_pwa_installed,
    ud.supports_touch,
    ud.last_seen_at as last_device_activity,
    
    -- Activity Summary
    us.total_checkins,
    us.unique_beers_tried,
    us.total_ratings,
    us.total_followers,
    us.total_following,
    
    -- Recent Activity
    (SELECT COUNT(*) FROM beer_checkins bc WHERE bc.user_id = u.id AND bc.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as checkins_last_week,
    (SELECT COUNT(*) FROM user_activities ua WHERE ua.user_id = u.id AND ua.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as activities_last_day
    
FROM users u
JOIN profiles p ON u.id = p.id
LEFT JOIN user_pwa_preferences upp ON u.id = upp.user_id
LEFT JOIN user_statistics us ON u.id = us.user_id
LEFT JOIN (
    SELECT user_id, device_type, is_pwa_installed, supports_touch, MAX(last_seen_at) as last_seen_at
    FROM user_devices
    GROUP BY user_id
) ud ON u.id = ud.user_id;

-- Add stored procedures for mobile analytics
DELIMITER //

-- Procedure to track PWA events
CREATE PROCEDURE IF NOT EXISTS TrackPWAEvent(
    IN p_user_id VARCHAR(36),
    IN p_session_id VARCHAR(100),
    IN p_event_type VARCHAR(50),
    IN p_event_data JSON,
    IN p_page_url VARCHAR(500),
    IN p_device_type VARCHAR(20),
    IN p_is_pwa_mode BOOLEAN,
    IN p_is_offline BOOLEAN
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO pwa_analytics (
        user_id, session_id, event_type, event_data, page_url,
        device_type, is_pwa_mode, is_offline
    ) VALUES (
        p_user_id, p_session_id, p_event_type, p_event_data, p_page_url,
        p_device_type, p_is_pwa_mode, p_is_offline
    );
    
    COMMIT;
END //

-- Procedure to update device information
CREATE PROCEDURE IF NOT EXISTS UpdateDeviceInfo(
    IN p_user_id VARCHAR(36),
    IN p_device_type VARCHAR(20),
    IN p_device_name VARCHAR(255),
    IN p_operating_system VARCHAR(100),
    IN p_browser VARCHAR(100),
    IN p_screen_width INT,
    IN p_screen_height INT,
    IN p_supports_touch BOOLEAN,
    IN p_is_pwa_installed BOOLEAN
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO user_devices (
        user_id, device_type, device_name, operating_system, browser,
        screen_width, screen_height, supports_touch, is_pwa_installed,
        total_sessions
    ) VALUES (
        p_user_id, p_device_type, p_device_name, p_operating_system, p_browser,
        p_screen_width, p_screen_height, p_supports_touch, p_is_pwa_installed,
        1
    )
    ON DUPLICATE KEY UPDATE
        device_name = VALUES(device_name),
        operating_system = VALUES(operating_system),
        browser = VALUES(browser),
        screen_width = VALUES(screen_width),
        screen_height = VALUES(screen_height),
        supports_touch = VALUES(supports_touch),
        is_pwa_installed = VALUES(is_pwa_installed),
        total_sessions = total_sessions + 1,
        last_seen_at = CURRENT_TIMESTAMP;
    
    COMMIT;
END //

DELIMITER ;

-- Add triggers for mobile analytics
DELIMITER //

-- Trigger to track mobile interactions
CREATE TRIGGER IF NOT EXISTS after_mobile_interaction_insert
AFTER INSERT ON mobile_interactions
FOR EACH ROW
BEGIN
    -- Update user activity
    INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
    VALUES (NEW.user_id, 'mobile_interaction', 
            JSON_OBJECT('interaction_type', NEW.interaction_type, 'element_type', NEW.element_type),
            NEW.created_at)
    ON DUPLICATE KEY UPDATE activity_data = VALUES(activity_data);
END //

DELIMITER ;

-- Show completion message
SELECT 'Phase 9 database update completed successfully!' as status,
       'Design & Mobile Optimization features are now available!' as message;
