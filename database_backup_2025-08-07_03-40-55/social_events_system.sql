-- Events & Meetups System for Beersty Social Platform
-- Comprehensive event management with RSVP, social features, and brewery integration

-- Events table for beer-related events and meetups
CREATE TABLE IF NOT EXISTS events (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    creator_id VARCHAR(36) NOT NULL,
    brewery_id VARCHAR(36) NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_type ENUM('brewery_tour', 'beer_tasting', 'meetup', 'festival', 'competition', 'educational', 'social', 'other') DEFAULT 'meetup',
    start_datetime DATETIME NOT NULL,
    end_datetime DATETIME NOT NULL,
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    location_name VARCHAR(255),
    address TEXT,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    max_attendees INT NULL,
    price DECIMAL(10, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    is_public BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    age_restriction INT DEFAULT 21,
    tags <PERSON><PERSON><PERSON> NULL,
    featured_beers JSON NULL,
    event_image VARCHAR(255) NULL,
    external_url VARCHAR(500) NULL,
    status ENUM('draft', 'published', 'cancelled', 'completed') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE SET NULL,
    INDEX idx_events_datetime (start_datetime, end_datetime),
    INDEX idx_events_location (latitude, longitude),
    INDEX idx_events_creator (creator_id),
    INDEX idx_events_brewery (brewery_id),
    INDEX idx_events_status (status),
    INDEX idx_events_type (event_type)
);

-- Event RSVPs and attendance tracking
CREATE TABLE IF NOT EXISTS event_rsvps (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    event_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    rsvp_status ENUM('going', 'maybe', 'not_going', 'pending') DEFAULT 'going',
    guest_count INT DEFAULT 0,
    dietary_restrictions TEXT NULL,
    special_requests TEXT NULL,
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_user (event_id, user_id),
    INDEX idx_rsvp_event (event_id),
    INDEX idx_rsvp_user (user_id),
    INDEX idx_rsvp_status (rsvp_status)
);

-- Event comments and discussions
CREATE TABLE IF NOT EXISTS event_comments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    event_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    parent_id VARCHAR(36) NULL,
    content TEXT NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES event_comments(id) ON DELETE CASCADE,
    INDEX idx_event_comments_event (event_id),
    INDEX idx_event_comments_user (user_id),
    INDEX idx_event_comments_parent (parent_id)
);

-- Social Challenges and Competitions
CREATE TABLE IF NOT EXISTS social_challenges (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    creator_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    challenge_type ENUM('beer_styles', 'brewery_visits', 'check_ins', 'reviews', 'social', 'seasonal', 'custom') DEFAULT 'custom',
    rules JSON NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    max_participants INT NULL,
    entry_fee DECIMAL(10, 2) DEFAULT 0.00,
    prize_description TEXT NULL,
    badge_reward VARCHAR(36) NULL,
    is_public BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    status ENUM('upcoming', 'active', 'completed', 'cancelled') DEFAULT 'upcoming',
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (badge_reward) REFERENCES badges(id) ON DELETE SET NULL,
    INDEX idx_challenges_dates (start_date, end_date),
    INDEX idx_challenges_creator (creator_id),
    INDEX idx_challenges_status (status),
    INDEX idx_challenges_type (challenge_type)
);

-- Challenge participation tracking
CREATE TABLE IF NOT EXISTS challenge_participants (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    challenge_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    progress JSON NULL,
    completed BOOLEAN DEFAULT FALSE,
    completion_date TIMESTAMP NULL,
    final_score INT DEFAULT 0,
    rank_position INT NULL,
    
    FOREIGN KEY (challenge_id) REFERENCES social_challenges(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_challenge_user (challenge_id, user_id),
    INDEX idx_participants_challenge (challenge_id),
    INDEX idx_participants_user (user_id),
    INDEX idx_participants_score (final_score DESC)
);

-- Beer Clubs and Groups
CREATE TABLE IF NOT EXISTS beer_clubs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    creator_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    club_type ENUM('public', 'private', 'invite_only') DEFAULT 'public',
    focus_area ENUM('general', 'style_specific', 'local', 'professional', 'educational', 'social') DEFAULT 'general',
    location VARCHAR(255) NULL,
    max_members INT NULL,
    membership_fee DECIMAL(10, 2) DEFAULT 0.00,
    club_image VARCHAR(255) NULL,
    rules TEXT NULL,
    meeting_frequency ENUM('weekly', 'biweekly', 'monthly', 'quarterly', 'irregular', 'online_only') DEFAULT 'monthly',
    tags JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_clubs_creator (creator_id),
    INDEX idx_clubs_type (club_type),
    INDEX idx_clubs_focus (focus_area),
    INDEX idx_clubs_active (is_active)
);

-- Club membership management
CREATE TABLE IF NOT EXISTS club_memberships (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    club_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    role ENUM('owner', 'admin', 'moderator', 'member') DEFAULT 'member',
    status ENUM('active', 'pending', 'suspended', 'banned') DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP NULL,
    membership_notes TEXT NULL,
    
    FOREIGN KEY (club_id) REFERENCES beer_clubs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_club_user (club_id, user_id),
    INDEX idx_memberships_club (club_id),
    INDEX idx_memberships_user (user_id),
    INDEX idx_memberships_role (role),
    INDEX idx_memberships_status (status)
);

-- Beer Trading and Sharing System
CREATE TABLE IF NOT EXISTS beer_trades (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    initiator_id VARCHAR(36) NOT NULL,
    recipient_id VARCHAR(36) NULL,
    trade_type ENUM('trade', 'share', 'sell', 'request') DEFAULT 'trade',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    offering_beers JSON NOT NULL,
    seeking_beers JSON NULL,
    trade_value DECIMAL(10, 2) NULL,
    location VARCHAR(255) NULL,
    shipping_allowed BOOLEAN DEFAULT FALSE,
    local_only BOOLEAN DEFAULT TRUE,
    status ENUM('open', 'pending', 'completed', 'cancelled', 'expired') DEFAULT 'open',
    expires_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (initiator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_trades_initiator (initiator_id),
    INDEX idx_trades_recipient (recipient_id),
    INDEX idx_trades_type (trade_type),
    INDEX idx_trades_status (status),
    INDEX idx_trades_expires (expires_at)
);

-- Trade offers and negotiations
CREATE TABLE IF NOT EXISTS trade_offers (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    trade_id VARCHAR(36) NOT NULL,
    offerer_id VARCHAR(36) NOT NULL,
    offered_beers JSON NOT NULL,
    requested_beers JSON NULL,
    message TEXT NULL,
    offer_value DECIMAL(10, 2) NULL,
    status ENUM('pending', 'accepted', 'declined', 'countered', 'withdrawn') DEFAULT 'pending',
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trade_id) REFERENCES beer_trades(id) ON DELETE CASCADE,
    FOREIGN KEY (offerer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_offers_trade (trade_id),
    INDEX idx_offers_offerer (offerer_id),
    INDEX idx_offers_status (status)
);

-- Enhanced user statistics for social features
ALTER TABLE user_statistics 
ADD COLUMN IF NOT EXISTS events_created INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS events_attended INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS challenges_completed INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS clubs_joined INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS trades_completed INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS social_score INT DEFAULT 0;

-- Add new activity types for events and social features
ALTER TABLE user_activities 
MODIFY COLUMN activity_type ENUM(
    'joined', 'profile_update', 'beer_rating', 'beer_review', 'beer_checkin', 
    'user_follow', 'list_created', 'badge_earned', 'brewery_follow',
    'event_created', 'event_rsvp', 'event_attended', 'challenge_joined', 
    'challenge_completed', 'club_joined', 'trade_posted', 'trade_completed'
) NOT NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_activities_social ON user_activities(activity_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_statistics_social ON user_statistics(social_score DESC);

-- Insert sample social challenges
INSERT IGNORE INTO social_challenges (id, creator_id, title, description, challenge_type, rules, start_date, end_date, status) VALUES
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'IPA Explorer Challenge', 'Try 10 different IPA styles this month', 'beer_styles', '{"target_count": 10, "beer_styles": ["IPA", "Double IPA", "Session IPA", "New England IPA"], "time_limit": 30}', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'active'),
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'Local Brewery Tour', 'Visit 5 different breweries in your area', 'brewery_visits', '{"target_count": 5, "location_radius": 50, "time_limit": 60}', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 60 DAY), 'active'),
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'Social Butterfly', 'Get 25 likes on your check-ins this week', 'social', '{"target_likes": 25, "time_limit": 7}', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'active');

-- Insert sample beer club
INSERT IGNORE INTO beer_clubs (id, creator_id, name, description, club_type, focus_area) VALUES
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'Craft Beer Enthusiasts', 'A community for craft beer lovers to share experiences and discover new brews', 'public', 'general'),
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'IPA Lovers Society', 'Dedicated to exploring the world of India Pale Ales', 'public', 'style_specific'),
(UUID(), (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 'Local Brewery Supporters', 'Supporting and promoting local breweries in our community', 'public', 'local');
