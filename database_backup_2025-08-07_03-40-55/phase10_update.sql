-- Phase 10 Database Update: Advanced Features & API Development
-- Run this script to add Phase 10 enhancements to existing database

USE beersty_db;

-- API Keys and Authentication
CREATE TABLE IF NOT EXISTS api_keys (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NULL,
    api_key VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    
    -- API Tier and Permissions
    tier ENUM('public', 'authenticated', 'premium') DEFAULT 'public',
    permissions JSON NULL, -- Array of permissions like ['beers:read', 'beers:write']
    
    -- Usage Tracking
    request_count INT DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    revoked_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_api_keys_key (api_key),
    INDEX idx_api_keys_user (user_id),
    INDEX idx_api_keys_active (is_active),
    INDEX idx_api_keys_tier (tier)
);

-- API Request Logging
CREATE TABLE IF NOT EXISTS api_requests (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    api_key_id VARCHAR(36) NULL,
    
    -- Request Details
    endpoint VARCHAR(500) NOT NULL,
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
    response_code INT NOT NULL,
    response_time_ms DECIMAL(10,2) NULL,
    
    -- Client Information
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
    INDEX idx_api_requests_key (api_key_id),
    INDEX idx_api_requests_endpoint (endpoint),
    INDEX idx_api_requests_created (created_at),
    INDEX idx_api_requests_response (response_code)
);

-- Social Media Sharing
CREATE TABLE IF NOT EXISTS social_shares (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Content Information
    content_type ENUM('beer', 'brewery', 'checkin', 'rating', 'user_profile', 'year_review') NOT NULL,
    content_id VARCHAR(36) NOT NULL,
    
    -- Platform and Tracking
    platform ENUM('facebook', 'twitter', 'instagram', 'linkedin', 'whatsapp', 'telegram', 'reddit', 'pinterest', 'email', 'copy_link') NOT NULL,
    shared_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_social_shares_user (user_id),
    INDEX idx_social_shares_content (content_type, content_id),
    INDEX idx_social_shares_platform (platform),
    INDEX idx_social_shares_shared (shared_at)
);

-- QR Codes
CREATE TABLE IF NOT EXISTS qr_codes (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    
    -- Content Information
    content_type ENUM('beer', 'brewery', 'user_profile', 'checkin', 'menu', 'event', 'custom') NOT NULL,
    content_id VARCHAR(36) NULL,
    
    -- QR Code Data
    data_url TEXT NOT NULL,
    qr_url TEXT NOT NULL,
    title VARCHAR(255) NOT NULL,
    
    -- Usage Tracking
    scan_count INT DEFAULT 0,
    last_scanned_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_qr_codes_content (content_type, content_id),
    INDEX idx_qr_codes_created (created_at)
);

-- QR Code Scans
CREATE TABLE IF NOT EXISTS qr_scans (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    qr_code_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NULL,
    
    -- Scan Information
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    metadata JSON NULL,
    
    scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (qr_code_id) REFERENCES qr_codes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_qr_scans_qr (qr_code_id),
    INDEX idx_qr_scans_user (user_id),
    INDEX idx_qr_scans_scanned (scanned_at)
);

-- User Subscriptions and Payments
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Subscription Details
    plan_id ENUM('basic', 'premium', 'pro') NOT NULL DEFAULT 'basic',
    stripe_subscription_id VARCHAR(255) NULL,
    
    -- Status and Timing
    status ENUM('active', 'cancelled', 'past_due', 'cancel_at_period_end') DEFAULT 'active',
    expires_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_subscription (user_id),
    INDEX idx_user_subscriptions_plan (plan_id),
    INDEX idx_user_subscriptions_status (status),
    INDEX idx_user_subscriptions_expires (expires_at)
);

-- Payments
CREATE TABLE IF NOT EXISTS payments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Payment Details
    stripe_payment_intent_id VARCHAR(255) NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'succeeded', 'failed', 'cancelled') DEFAULT 'pending',
    description TEXT NULL,
    
    -- Metadata
    metadata JSON NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_payments_user (user_id),
    INDEX idx_payments_status (status),
    INDEX idx_payments_created (created_at)
);

-- Subscription Events
CREATE TABLE IF NOT EXISTS subscription_events (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Event Information
    event_type ENUM('subscription_created', 'subscription_updated', 'subscription_cancelled', 'payment_succeeded', 'payment_failed') NOT NULL,
    event_data JSON NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_subscription_events_user (user_id),
    INDEX idx_subscription_events_type (event_type),
    INDEX idx_subscription_events_created (created_at)
);

-- Webhooks
CREATE TABLE IF NOT EXISTS webhooks (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Webhook Configuration
    url VARCHAR(500) NOT NULL,
    events JSON NOT NULL, -- Array of event types to listen for
    secret VARCHAR(255) NOT NULL,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    disabled_reason VARCHAR(255) NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_webhooks_user (user_id),
    INDEX idx_webhooks_active (is_active)
);

-- Webhook Deliveries
CREATE TABLE IF NOT EXISTS webhook_deliveries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    webhook_id VARCHAR(36) NOT NULL,
    
    -- Delivery Information
    event_type VARCHAR(100) NOT NULL,
    payload JSON NOT NULL,
    
    -- Status and Retry Logic
    status ENUM('pending', 'delivered', 'failed', 'retry') DEFAULT 'pending',
    retry_count INT DEFAULT 0,
    next_retry_at TIMESTAMP NULL,
    
    -- Response Information
    http_code INT NULL,
    response TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    
    FOREIGN KEY (webhook_id) REFERENCES webhooks(id) ON DELETE CASCADE,
    INDEX idx_webhook_deliveries_webhook (webhook_id),
    INDEX idx_webhook_deliveries_status (status),
    INDEX idx_webhook_deliveries_retry (next_retry_at),
    INDEX idx_webhook_deliveries_created (created_at)
);

-- Data Export Requests
CREATE TABLE IF NOT EXISTS data_exports (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Export Configuration
    export_type ENUM('full', 'checkins', 'ratings', 'profile', 'analytics') NOT NULL,
    format ENUM('json', 'csv', 'pdf') DEFAULT 'json',
    date_range_start DATE NULL,
    date_range_end DATE NULL,
    
    -- Status and File Information
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    file_path VARCHAR(500) NULL,
    file_size_bytes BIGINT NULL,
    download_count INT DEFAULT 0,
    
    -- Expiration
    expires_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_data_exports_user (user_id),
    INDEX idx_data_exports_status (status),
    INDEX idx_data_exports_expires (expires_at)
);

-- Third-party Integrations
CREATE TABLE IF NOT EXISTS user_integrations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    
    -- Integration Details
    provider ENUM('google_calendar', 'outlook', 'facebook', 'twitter', 'instagram', 'untappd') NOT NULL,
    provider_user_id VARCHAR(255) NULL,
    access_token TEXT NULL,
    refresh_token TEXT NULL,
    token_expires_at TIMESTAMP NULL,
    
    -- Configuration
    settings JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Sync Information
    last_sync_at TIMESTAMP NULL,
    sync_status ENUM('success', 'failed', 'pending') NULL,
    sync_error TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_provider (user_id, provider),
    INDEX idx_user_integrations_provider (provider),
    INDEX idx_user_integrations_active (is_active),
    INDEX idx_user_integrations_sync (last_sync_at)
);

-- Create default API key for existing users
INSERT IGNORE INTO api_keys (user_id, api_key, name, tier, permissions)
SELECT
    id,
    CONCAT('bst_', LOWER(HEX(RANDOM_BYTES(32)))),
    'Default API Key',
    CASE
        WHEN role = 'admin' THEN 'premium'
        WHEN role IN ('brewery_owner', 'restaurant_owner') THEN 'authenticated'
        ELSE 'public'
    END,
    CASE
        WHEN role = 'admin' THEN JSON_ARRAY('*')
        WHEN role IN ('brewery_owner', 'restaurant_owner') THEN JSON_ARRAY('beers:read', 'breweries:read', 'breweries:update')
        ELSE JSON_ARRAY('beers:read', 'breweries:read')
    END
FROM users
WHERE id NOT IN (SELECT DISTINCT user_id FROM api_keys WHERE user_id IS NOT NULL);

-- Create default subscriptions for existing users
INSERT IGNORE INTO user_subscriptions (user_id, plan_id, status)
SELECT id, 'basic', 'active'
FROM users
WHERE id NOT IN (SELECT user_id FROM user_subscriptions);

-- Add stored procedures for Phase 10
DELIMITER //

-- Procedure to generate API key
CREATE PROCEDURE IF NOT EXISTS GenerateApiKey(
    IN p_user_id VARCHAR(36),
    IN p_name VARCHAR(255),
    IN p_tier VARCHAR(20),
    IN p_permissions JSON
)
BEGIN
    DECLARE v_api_key VARCHAR(255);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    SET v_api_key = CONCAT('bst_', LOWER(HEX(RANDOM_BYTES(32))));

    INSERT INTO api_keys (user_id, api_key, name, tier, permissions, expires_at)
    VALUES (p_user_id, v_api_key, p_name, p_tier, p_permissions, DATE_ADD(NOW(), INTERVAL 1 YEAR));

    SELECT v_api_key as api_key, LAST_INSERT_ID() as api_key_id;

    COMMIT;
END //

-- Procedure to track API usage
CREATE PROCEDURE IF NOT EXISTS TrackApiUsage(
    IN p_api_key_id VARCHAR(36),
    IN p_endpoint VARCHAR(500),
    IN p_method VARCHAR(10),
    IN p_response_code INT,
    IN p_response_time DECIMAL(10,2),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Log the request
    INSERT INTO api_requests (
        api_key_id, endpoint, method, response_code, response_time_ms, ip_address, user_agent
    ) VALUES (
        p_api_key_id, p_endpoint, p_method, p_response_code, p_response_time, p_ip_address, p_user_agent
    );

    -- Update API key usage
    UPDATE api_keys
    SET request_count = request_count + 1, last_used_at = NOW()
    WHERE id = p_api_key_id;

    COMMIT;
END //

-- Procedure to process webhook deliveries
CREATE PROCEDURE IF NOT EXISTS ProcessWebhookDeliveries(
    IN p_limit INT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_delivery_id VARCHAR(36);
    DECLARE v_webhook_id VARCHAR(36);
    DECLARE v_url VARCHAR(500);
    DECLARE v_payload JSON;
    DECLARE v_secret VARCHAR(255);

    DECLARE delivery_cursor CURSOR FOR
        SELECT wd.id, wd.webhook_id, w.url, wd.payload, w.secret
        FROM webhook_deliveries wd
        JOIN webhooks w ON wd.webhook_id = w.id
        WHERE wd.status = 'pending' AND w.is_active = 1
        ORDER BY wd.created_at ASC
        LIMIT p_limit;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN delivery_cursor;

    delivery_loop: LOOP
        FETCH delivery_cursor INTO v_delivery_id, v_webhook_id, v_url, v_payload, v_secret;

        IF done THEN
            LEAVE delivery_loop;
        END IF;

        -- Mark as processing (would be handled by external process)
        UPDATE webhook_deliveries
        SET status = 'retry', retry_count = retry_count + 1
        WHERE id = v_delivery_id;

    END LOOP;

    CLOSE delivery_cursor;

    SELECT ROW_COUNT() as processed_deliveries;
END //

-- Procedure to clean up expired data
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- Clean up expired API keys
    UPDATE api_keys
    SET is_active = 0, revoked_at = NOW()
    WHERE expires_at < NOW() AND is_active = 1;

    -- Clean up old API request logs (keep 90 days)
    DELETE FROM api_requests
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Clean up expired data exports
    DELETE FROM data_exports
    WHERE expires_at < NOW() AND status = 'completed';

    -- Clean up old webhook deliveries (keep 30 days)
    DELETE FROM webhook_deliveries
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) AND status IN ('delivered', 'failed');

    -- Clean up old QR scans (keep 1 year)
    DELETE FROM qr_scans
    WHERE scanned_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

    COMMIT;

    SELECT 'Cleanup completed successfully' as status;
END //

-- Procedure to get API analytics
CREATE PROCEDURE IF NOT EXISTS GetApiAnalytics(
    IN p_api_key_id VARCHAR(36),
    IN p_timeframe VARCHAR(10)
)
BEGIN
    DECLARE v_interval VARCHAR(50);

    SET v_interval = CASE p_timeframe
        WHEN '1h' THEN 'INTERVAL 1 HOUR'
        WHEN '24h' THEN 'INTERVAL 24 HOUR'
        WHEN '7d' THEN 'INTERVAL 7 DAY'
        WHEN '30d' THEN 'INTERVAL 30 DAY'
        ELSE 'INTERVAL 24 HOUR'
    END;

    SET @sql = CONCAT('
        SELECT
            COUNT(*) as total_requests,
            COUNT(CASE WHEN response_code = 200 THEN 1 END) as successful_requests,
            COUNT(CASE WHEN response_code >= 400 THEN 1 END) as error_requests,
            AVG(response_time_ms) as avg_response_time,
            COUNT(DISTINCT endpoint) as unique_endpoints,
            COUNT(DISTINCT ip_address) as unique_ips
        FROM api_requests
        WHERE api_key_id = ? AND created_at > DATE_SUB(NOW(), ', v_interval, ')
    ');

    PREPARE stmt FROM @sql;
    EXECUTE stmt USING p_api_key_id;
    DEALLOCATE PREPARE stmt;
END //

DELIMITER ;

-- Create API analytics view
CREATE OR REPLACE VIEW api_usage_summary AS
SELECT
    ak.id as api_key_id,
    ak.name as api_key_name,
    ak.tier,
    u.email as user_email,
    COUNT(ar.id) as total_requests,
    COUNT(CASE WHEN ar.response_code = 200 THEN 1 END) as successful_requests,
    COUNT(CASE WHEN ar.response_code >= 400 THEN 1 END) as error_requests,
    AVG(ar.response_time_ms) as avg_response_time,
    MAX(ar.created_at) as last_request_at
FROM api_keys ak
LEFT JOIN users u ON ak.user_id = u.id
LEFT JOIN api_requests ar ON ak.id = ar.api_key_id
    AND ar.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY ak.id;

-- Create webhook analytics view
CREATE OR REPLACE VIEW webhook_analytics AS
SELECT
    w.id as webhook_id,
    w.url,
    w.is_active,
    u.email as user_email,
    COUNT(wd.id) as total_deliveries,
    COUNT(CASE WHEN wd.status = 'delivered' THEN 1 END) as successful_deliveries,
    COUNT(CASE WHEN wd.status = 'failed' THEN 1 END) as failed_deliveries,
    CASE
        WHEN COUNT(wd.id) > 0 THEN (COUNT(CASE WHEN wd.status = 'delivered' THEN 1 END) / COUNT(wd.id)) * 100
        ELSE 0
    END as success_rate,
    MAX(wd.delivered_at) as last_delivery_at
FROM webhooks w
LEFT JOIN users u ON w.user_id = u.id
LEFT JOIN webhook_deliveries wd ON w.id = wd.webhook_id
    AND wd.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY w.id;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_requests_performance ON api_requests(api_key_id, created_at, response_code);
CREATE INDEX IF NOT EXISTS idx_webhook_deliveries_performance ON webhook_deliveries(webhook_id, status, created_at);
CREATE INDEX IF NOT EXISTS idx_social_shares_analytics ON social_shares(user_id, platform, shared_at);
CREATE INDEX IF NOT EXISTS idx_qr_codes_analytics ON qr_codes(content_type, created_at, scan_count);

-- Show completion message
SELECT 'Phase 10 database update completed successfully!' as status,
       'Advanced Features & API Development infrastructure is now available!' as message;
