<?php
// Test Menu Management API
require_once 'config/config.php';

echo "=== Testing Menu Management API ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Get first brewery for testing
    $stmt = $pdo->query("SELECT id, name FROM breweries LIMIT 1");
    $brewery = $stmt->fetch();
    
    if (!$brewery) {
        echo "❌ No breweries found for testing" . PHP_EOL;
        exit;
    }
    
    $breweryId = $brewery['id'];
    $breweryName = $brewery['name'];
    echo "Testing with brewery: $breweryName (ID: $breweryId)" . PHP_EOL;
    
    // Get first beer style
    $stmt = $pdo->query("SELECT id, name FROM beer_styles LIMIT 1");
    $beerStyle = $stmt->fetch();
    
    if ($beerStyle) {
        $styleId = $beerStyle['id'];
        $styleName = $beerStyle['name'];
        echo "Using beer style: $styleName (ID: $styleId)" . PHP_EOL;
        
        // Test beer insertion directly
        echo "\nTesting beer insertion..." . PHP_EOL;
        $stmt = $pdo->prepare("
            INSERT INTO brewery_beers (id, brewery_id, beer_style_id, name, description, abv, ibu, price, availability, is_active, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 'year_round', 1, NOW())
        ");
        
        $result = $stmt->execute([
            $breweryId,
            $styleId,
            'Test API Beer',
            'A test beer for API validation',
            5.5,
            45,
            7.99
        ]);
        
        if ($result) {
            echo "✓ Beer insertion successful" . PHP_EOL;
            
            // Get the inserted beer
            $stmt = $pdo->prepare("SELECT * FROM brewery_beers WHERE name = 'Test API Beer' AND brewery_id = ?");
            $stmt->execute([$breweryId]);
            $testBeer = $stmt->fetch();
            
            if ($testBeer) {
                echo "✓ Beer found in database: " . $testBeer['name'] . PHP_EOL;
                echo "  ABV: " . $testBeer['abv'] . "%" . PHP_EOL;
                echo "  IBU: " . $testBeer['ibu'] . PHP_EOL;
                echo "  Price: $" . $testBeer['price'] . PHP_EOL;
            }
        } else {
            echo "❌ Beer insertion failed" . PHP_EOL;
        }
    }
    
    // Get first food category
    $stmt = $pdo->query("SELECT id, name FROM food_categories LIMIT 1");
    $foodCategory = $stmt->fetch();
    
    if ($foodCategory) {
        $categoryId = $foodCategory['id'];
        $categoryName = $foodCategory['name'];
        echo "\nUsing food category: $categoryName (ID: $categoryId)" . PHP_EOL;
        
        // Test food insertion directly
        echo "Testing food insertion..." . PHP_EOL;
        $stmt = $pdo->prepare("
            INSERT INTO brewery_food (id, brewery_id, food_category_id, name, description, price, is_active, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, 1, NOW())
        ");
        
        $result = $stmt->execute([
            $breweryId,
            $categoryId,
            'Test API Food Item',
            'A test food item for API validation',
            12.99
        ]);
        
        if ($result) {
            echo "✓ Food insertion successful" . PHP_EOL;
            
            // Get the inserted food
            $stmt = $pdo->prepare("SELECT * FROM brewery_food WHERE name = 'Test API Food Item' AND brewery_id = ?");
            $stmt->execute([$breweryId]);
            $testFood = $stmt->fetch();
            
            if ($testFood) {
                echo "✓ Food found in database: " . $testFood['name'] . PHP_EOL;
                echo "  Price: $" . $testFood['price'] . PHP_EOL;
                echo "  Category ID: " . $testFood['food_category_id'] . PHP_EOL;
            }
        } else {
            echo "❌ Food insertion failed" . PHP_EOL;
        }
    }
    
    // Test reading menu items
    echo "\nTesting menu retrieval..." . PHP_EOL;
    
    // Get beers for this brewery
    $stmt = $pdo->prepare("
        SELECT bb.*, bs.name as style_name 
        FROM brewery_beers bb 
        LEFT JOIN beer_styles bs ON bb.beer_style_id = bs.id 
        WHERE bb.brewery_id = ? AND bb.is_active = 1
        ORDER BY bb.name
    ");
    $stmt->execute([$breweryId]);
    $beers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($beers) . " beers for this brewery:" . PHP_EOL;
    foreach ($beers as $beer) {
        echo "  • " . $beer['name'] . " (" . ($beer['style_name'] ?? 'No style') . ")" . PHP_EOL;
    }
    
    // Get food for this brewery
    $stmt = $pdo->prepare("
        SELECT bf.*, fc.name as category_name 
        FROM brewery_food bf 
        LEFT JOIN food_categories fc ON bf.food_category_id = fc.id 
        WHERE bf.brewery_id = ? AND bf.is_active = 1
        ORDER BY fc.sort_order, bf.name
    ");
    $stmt->execute([$breweryId]);
    $foods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nFound " . count($foods) . " food items for this brewery:" . PHP_EOL;
    foreach ($foods as $food) {
        echo "  • " . $food['name'] . " (" . ($food['category_name'] ?? 'No category') . ")" . PHP_EOL;
    }
    
    // Clean up test data
    echo "\nCleaning up test data..." . PHP_EOL;
    $pdo->exec("DELETE FROM brewery_beers WHERE name = 'Test API Beer'");
    $pdo->exec("DELETE FROM brewery_food WHERE name = 'Test API Food Item'");
    echo "✓ Test data cleaned up" . PHP_EOL;
    
    echo "\n=== API Testing Complete ===" . PHP_EOL;
    echo "✓ Database schema is correct" . PHP_EOL;
    echo "✓ Beer insertion works" . PHP_EOL;
    echo "✓ Food insertion works" . PHP_EOL;
    echo "✓ Menu retrieval works" . PHP_EOL;
    echo "\nMenu management should now work properly!" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
