<?php
/**
 * Import Michigan Breweries CSV
 * Automatically import the Michigan breweries data
 */

require_once 'config/config.php';

echo "<h1>🍺 Importing Michigan Breweries</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $csvPath = 'csv-data/michigan_breweries.csv';
    
    if (!file_exists($csvPath)) {
        throw new Exception("CSV file not found: $csvPath");
    }
    
    echo "<h2>📁 Reading CSV File</h2>";
    echo "<p>File: $csvPath</p>";
    
    // Read CSV file
    $csvData = file_get_contents($csvPath);
    if ($csvData === false) {
        throw new Exception("Failed to read CSV file");
    }
    
    $lines = str_getcsv($csvData, "\n");
    echo "<p>✅ Found " . (count($lines) - 1) . " brewery records</p>";
    
    // Parse header
    $headers = str_getcsv($lines[0]);
    echo "<p>✅ Headers: " . implode(', ', $headers) . "</p>";
    
    // Connect to database
    echo "<h2>🗄️ Connecting to Database</h2>";
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connection successful</p>";
    
    // Prepare statements
    $insertStmt = $conn->prepare("
        INSERT INTO breweries (
            name, address, city, state, brewery_type, website, 
            verified, claimed, follower_count, like_count,
            external_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 1, 0, 0, 0, ?, NOW(), NOW())
    ");
    
    $updateStmt = $conn->prepare("
        UPDATE breweries SET
            address = ?, city = ?, state = ?, brewery_type = ?, website = ?,
            external_id = ?, updated_at = NOW()
        WHERE name = ?
    ");
    
    $checkStmt = $conn->prepare("SELECT id FROM breweries WHERE name = ? OR external_id = ?");
    
    $stats = [
        'total_rows' => count($lines) - 1,
        'processed' => 0,
        'inserted' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => []
    ];
    
    echo "<h2>⚙️ Processing Breweries</h2>";
    
    // Process data rows
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i]);
        $stats['processed']++;
        
        if (empty($row) || count($row) < count($headers)) {
            $stats['skipped']++;
            continue;
        }
        
        // Map row data
        $data = array_combine($headers, $row);
        
        // Skip if no name
        if (empty(trim($data['name']))) {
            $stats['skipped']++;
            continue;
        }
        
        // Clean and prepare data
        $breweryName = trim($data['name']);
        $address = trim($data['street'] ?? '');
        $city = trim($data['city'] ?? '');
        $state = trim($data['state'] ?? '');
        $breweryType = cleanBreweryType(trim($data['brewery_type'] ?? ''));
        $website = cleanWebsite(trim($data['website_url'] ?? ''));
        $externalId = trim($data['openbrewerydb_id'] ?? '');
        
        try {
            // Check if brewery exists
            $checkStmt->execute([$breweryName, $externalId]);
            $exists = $checkStmt->fetch();
            
            if ($exists) {
                // Update existing brewery
                $updateStmt->execute([
                    $address, $city, $state, $breweryType, $website, $externalId, $breweryName
                ]);
                $stats['updated']++;
                echo "<p>🔄 Updated: " . htmlspecialchars($breweryName) . "</p>";
            } else {
                // Insert new brewery
                $insertStmt->execute([
                    $breweryName, $address, $city, $state, $breweryType, $website, $externalId
                ]);
                $stats['inserted']++;
                echo "<p>✅ Inserted: " . htmlspecialchars($breweryName) . "</p>";
            }
            
        } catch (Exception $e) {
            $stats['errors'][] = "Row " . ($i + 1) . " ($breweryName): " . $e->getMessage();
            echo "<p>❌ Error with " . htmlspecialchars($breweryName) . ": " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Show progress every 50 records
        if ($stats['processed'] % 50 === 0) {
            echo "<p><strong>Progress:</strong> {$stats['processed']}/{$stats['total_rows']} processed...</p>";
            flush();
        }
    }
    
    echo "<h2>📊 Import Complete!</h2>";
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Import Statistics</h3>";
    echo "<ul>";
    echo "<li><strong>Total Rows:</strong> {$stats['total_rows']}</li>";
    echo "<li><strong>Processed:</strong> {$stats['processed']}</li>";
    echo "<li><strong>Inserted:</strong> {$stats['inserted']}</li>";
    echo "<li><strong>Updated:</strong> {$stats['updated']}</li>";
    echo "<li><strong>Skipped:</strong> {$stats['skipped']}</li>";
    echo "<li><strong>Errors:</strong> " . count($stats['errors']) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!empty($stats['errors'])) {
        echo "<h3>❌ Errors Encountered:</h3>";
        echo "<ul>";
        foreach (array_slice($stats['errors'], 0, 10) as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        if (count($stats['errors']) > 10) {
            echo "<li><em>... and " . (count($stats['errors']) - 10) . " more errors</em></li>";
        }
        echo "</ul>";
    }
    
    // Show sample of imported breweries
    echo "<h2>🍺 Sample Imported Breweries</h2>";
    $stmt = $conn->query("SELECT name, city, state, brewery_type FROM breweries ORDER BY created_at DESC LIMIT 10");
    $sampleBreweries = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($sampleBreweries as $brewery) {
        echo "<li><strong>" . htmlspecialchars($brewery['name']) . "</strong> - " . 
             htmlspecialchars($brewery['city']) . ", " . htmlspecialchars($brewery['state']) . 
             " (" . htmlspecialchars($brewery['brewery_type']) . ")</li>";
    }
    echo "</ul>";
    
    // Get total brewery count
    $stmt = $conn->query("SELECT COUNT(*) as total FROM breweries");
    $totalBreweries = $stmt->fetch()['total'];
    echo "<p><strong>Total Breweries in Database:</strong> " . number_format($totalBreweries) . "</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Import Failed!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

function cleanBreweryType($type) {
    $type = strtolower(trim($type));
    $validTypes = ['micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor', 'brewpub'];
    
    // Map common variations
    $typeMap = [
        'brewpub' => 'brewpub',
        'micro' => 'micro',
        'nano' => 'nano',
        'regional' => 'regional',
        'large' => 'large',
        'planning' => 'planning',
        'bar' => 'bar',
        'contract' => 'contract',
        'proprietor' => 'proprietor',
        'closed' => 'planning' // Map closed to planning
    ];
    
    return $typeMap[$type] ?? 'micro';
}

function cleanWebsite($url) {
    if (empty($url)) return '';
    
    // Add protocol if missing
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'https://' . $url;
    }
    
    return filter_var($url, FILTER_VALIDATE_URL) ? $url : '';
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='breweries/listing.php' class='btn btn-primary'>Browse Imported Breweries</a></li>";
echo "<li><a href='admin/dashboard.php' class='btn btn-info'>Go to Admin Dashboard</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
