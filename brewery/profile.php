<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Brewery Profile - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

// Debug: Check if brewery ID exists
if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with this account.';
    redirect('../dashboard.php');
}

// Verify brewery exists in database
try {
    $db = new Database();
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);

    if ($stmt->rowCount() == 0) {
        $_SESSION['error_message'] = 'Brewery not found in database.';
        redirect('../dashboard.php');
    }
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Database error: ' . $e->getMessage();
    redirect('../dashboard.php');
}

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Handle image deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_image'])) {
    $imageType = $_POST['delete_image']; // 'logo' or 'feature_image'

    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Get current image path
        $stmt = $conn->prepare("SELECT {$imageType} FROM breweries WHERE id = ?");
        $stmt->execute([$breweryId]);
        $result = $stmt->fetch();

        if ($result && $result[$imageType]) {
            $imagePath = '../' . $result[$imageType];

            // Delete file if it exists
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            // Update database
            $stmt = $conn->prepare("UPDATE breweries SET {$imageType} = NULL, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$breweryId]);

            $_SESSION['success_message'] = ucfirst(str_replace('_', ' ', $imageType)) . ' deleted successfully!';
        } else {
            $_SESSION['error_message'] = 'No image found to delete.';
        }

        redirect('profile.php');

    } catch (Exception $e) {
        error_log("Error deleting {$imageType}: " . $e->getMessage());
        $_SESSION['error_message'] = 'Error deleting image: ' . $e->getMessage();
    }
}

// Handle file uploads
elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_type'])) {
    $uploadType = $_POST['upload_type']; // 'logo' or 'feature_image'
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxFileSize = 5 * 1024 * 1024; // 5MB

    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['image_file'];

        // Validate file type
        if (!in_array($file['type'], $allowedTypes)) {
            $_SESSION['error_message'] = 'Please upload a valid image file (JPEG, PNG, GIF, or WebP).';
        }
        // Validate file size
        elseif ($file['size'] > $maxFileSize) {
            $_SESSION['error_message'] = 'File size must be less than 5MB.';
        }
        else {
            // Create upload directory if it doesn't exist
            $baseDir = dirname(__DIR__); // Go up one level from brewery/ to root
            $uploadDir = $baseDir . '/uploads/breweries/' . $breweryId . '/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = $uploadType . '_' . time() . '.' . $extension;
            $uploadPath = $uploadDir . $filename;
            $relativePath = 'uploads/breweries/' . $breweryId . '/' . $filename;

            // Debug logging
            error_log("Upload attempt - Brewery ID: {$breweryId}, Upload Type: {$uploadType}, File: {$filename}");

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                try {
                    $db = new Database();
                    $conn = $db->getConnection();

                    // Check if column exists first
                    $checkStmt = $conn->prepare("SHOW COLUMNS FROM breweries LIKE ?");
                    $checkStmt->execute([$uploadType]);

                    if ($checkStmt->rowCount() == 0) {
                        // Column doesn't exist, add it
                        $alterSql = "ALTER TABLE breweries ADD COLUMN {$uploadType} VARCHAR(255) NULL";
                        $conn->exec($alterSql);
                    }

                    // Update database
                    $stmt = $conn->prepare("UPDATE breweries SET {$uploadType} = ?, updated_at = NOW() WHERE id = ?");
                    $result = $stmt->execute([$relativePath, $breweryId]);

                    if ($result && $stmt->rowCount() > 0) {
                        $_SESSION['success_message'] = ucfirst(str_replace('_', ' ', $uploadType)) . ' uploaded successfully!';
                        // Refresh page to show new image
                        redirect('profile.php');
                    } else {
                        throw new Exception("No rows updated. Brewery ID: {$breweryId}");
                    }

                } catch (Exception $e) {
                    error_log("Error updating {$uploadType}: " . $e->getMessage());
                    $_SESSION['error_message'] = 'Error saving image to database: ' . $e->getMessage();

                    // Clean up uploaded file on database error
                    if (file_exists($uploadPath)) {
                        unlink($uploadPath);
                    }
                }
            } else {
                $_SESSION['error_message'] = 'Error uploading file. Please try again.';
            }
        }
    } else {
        $_SESSION['error_message'] = 'Please select a file to upload.';
    }
}

// Handle photo gallery uploads
elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload_photos') {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxFileSize = 5 * 1024 * 1024; // 5MB
    $uploadedPhotos = [];
    $errors = [];

    if (isset($_FILES['photos']) && is_array($_FILES['photos']['name'])) {
        $category = sanitizeInput($_POST['category'] ?? 'general');
        $description = sanitizeInput($_POST['description'] ?? '');

        // Create upload directory
        $baseDir = dirname(__DIR__);
        $uploadDir = $baseDir . '/uploads/breweries/' . $breweryId . '/gallery/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Process each uploaded file
        for ($i = 0; $i < count($_FILES['photos']['name']); $i++) {
            if ($_FILES['photos']['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $_FILES['photos']['name'][$i],
                    'type' => $_FILES['photos']['type'][$i],
                    'tmp_name' => $_FILES['photos']['tmp_name'][$i],
                    'size' => $_FILES['photos']['size'][$i]
                ];

                // Validate file
                if (!in_array($file['type'], $allowedTypes)) {
                    $errors[] = "Invalid file type for {$file['name']}";
                    continue;
                }

                if ($file['size'] > $maxFileSize) {
                    $errors[] = "File {$file['name']} is too large (max 5MB)";
                    continue;
                }

                // Generate unique filename
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $filename = 'gallery_' . time() . '_' . $i . '.' . $extension;
                $uploadPath = $uploadDir . $filename;
                $relativePath = 'uploads/breweries/' . $breweryId . '/gallery/' . $filename;

                // Move uploaded file
                if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                    try {
                        $db = new Database();
                        $conn = $db->getConnection();

                        // Create gallery table if it doesn't exist
                        $createTable = "
                            CREATE TABLE IF NOT EXISTS brewery_gallery (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                brewery_id VARCHAR(36) NOT NULL,
                                filename VARCHAR(255) NOT NULL,
                                original_filename VARCHAR(255) NOT NULL,
                                title VARCHAR(200),
                                description TEXT,
                                category VARCHAR(50) DEFAULT 'general',
                                file_size INT,
                                mime_type VARCHAR(100),
                                sort_order INT DEFAULT 0,
                                is_featured BOOLEAN DEFAULT FALSE,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX idx_brewery_id (brewery_id),
                                INDEX idx_category (category),
                                INDEX idx_sort_order (sort_order)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ";
                        $conn->exec($createTable);

                        // Insert photo record
                        $stmt = $conn->prepare("
                            INSERT INTO brewery_gallery (brewery_id, filename, original_filename, title, description, category, file_size, mime_type)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $breweryId,
                            $relativePath,
                            $file['name'],
                            pathinfo($file['name'], PATHINFO_FILENAME),
                            $description,
                            $category,
                            $file['size'],
                            $file['type']
                        ]);

                        $uploadedPhotos[] = $file['name'];

                    } catch (Exception $e) {
                        error_log("Error saving photo to database: " . $e->getMessage());
                        $errors[] = "Error saving {$file['name']} to database";

                        // Clean up uploaded file on database error
                        if (file_exists($uploadPath)) {
                            unlink($uploadPath);
                        }
                    }
                } else {
                    $errors[] = "Error uploading {$file['name']}";
                }
            }
        }
    }

    // Set response message
    if (!empty($uploadedPhotos)) {
        $count = count($uploadedPhotos);
        $_SESSION['success_message'] = "Successfully uploaded {$count} photo(s)!";
    }
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }

    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => !empty($uploadedPhotos),
            'uploaded' => count($uploadedPhotos),
            'errors' => $errors
        ]);
        exit;
    }

    redirect('profile.php#gallery');
}

// Handle photo management actions
elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['photo_action'])) {
    $action = $_POST['photo_action'];
    $photoId = intval($_POST['photo_id'] ?? 0);

    try {
        $db = new Database();
        $conn = $db->getConnection();

        switch ($action) {
            case 'delete':
                // Get photo info first
                $stmt = $conn->prepare("SELECT filename FROM brewery_gallery WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$photoId, $breweryId]);
                $photo = $stmt->fetch();

                if ($photo) {
                    // Delete file
                    $filePath = '../' . $photo['filename'];
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }

                    // Delete database record
                    $stmt = $conn->prepare("DELETE FROM brewery_gallery WHERE id = ? AND brewery_id = ?");
                    $stmt->execute([$photoId, $breweryId]);

                    $_SESSION['success_message'] = 'Photo deleted successfully!';
                } else {
                    $_SESSION['error_message'] = 'Photo not found.';
                }
                break;

            case 'update':
                $title = sanitizeInput($_POST['title'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $category = sanitizeInput($_POST['category'] ?? 'general');
                $isFeatured = isset($_POST['is_featured']) ? 1 : 0;

                $stmt = $conn->prepare("
                    UPDATE brewery_gallery
                    SET title = ?, description = ?, category = ?, is_featured = ?
                    WHERE id = ? AND brewery_id = ?
                ");
                $stmt->execute([$title, $description, $category, $isFeatured, $photoId, $breweryId]);

                $_SESSION['success_message'] = 'Photo updated successfully!';
                break;

            case 'set_featured':
                // Remove featured status from all photos
                $stmt = $conn->prepare("UPDATE brewery_gallery SET is_featured = 0 WHERE brewery_id = ?");
                $stmt->execute([$breweryId]);

                // Set this photo as featured
                $stmt = $conn->prepare("UPDATE brewery_gallery SET is_featured = 1 WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$photoId, $breweryId]);

                $_SESSION['success_message'] = 'Featured photo updated!';
                break;
        }

    } catch (Exception $e) {
        error_log("Photo management error: " . $e->getMessage());
        $_SESSION['error_message'] = 'Error managing photo.';
    }

    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => empty($_SESSION['error_message'])]);
        exit;
    }

    redirect('profile.php#gallery');
}

// Handle form submission
elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $name = sanitizeInput($_POST['name'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $city = sanitizeInput($_POST['city'] ?? '');
    $state = sanitizeInput($_POST['state'] ?? '');
    $zip = sanitizeInput($_POST['zip'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $website = sanitizeInput($_POST['website'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $brewery_type = sanitizeInput($_POST['brewery_type'] ?? '');
    
    // Social links
    $social_links = [
        'facebook' => sanitizeInput($_POST['facebook'] ?? ''),
        'instagram' => sanitizeInput($_POST['instagram'] ?? ''),
        'twitter' => sanitizeInput($_POST['twitter'] ?? '')
    ];
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Brewery name is required.';
    }
    
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Please enter a valid website URL.';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();

            // Check what columns exist in the breweries table
            $columnsStmt = $conn->query("DESCRIBE breweries");
            $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);


            // Add missing columns if they don't exist
            $requiredColumns = [
                'description' => 'TEXT',
                'address' => 'VARCHAR(255)',
                'city' => 'VARCHAR(100)',
                'state' => 'VARCHAR(50)',
                'zip' => 'VARCHAR(20)',
                'phone' => 'VARCHAR(20)',
                'website' => 'VARCHAR(255)',
                'email' => 'VARCHAR(255)',
                'brewery_type' => 'VARCHAR(50)',
                'social_links' => 'JSON'
            ];

            foreach ($requiredColumns as $column => $type) {
                if (!in_array($column, $columns)) {
                    try {
                        $alterSql = "ALTER TABLE breweries ADD COLUMN `$column` $type NULL";
                        $conn->exec($alterSql);

                        $columns[] = $column; // Add to our columns array
                    } catch (Exception $e) {
                        error_log("Error adding column $column: " . $e->getMessage());
                    }
                }
            }

            // Build dynamic update query based on existing columns
            $updateFields = [];
            $updateValues = [];

            // Always update name (should exist)
            $updateFields[] = "name = ?";
            $updateValues[] = $name;

            // Check and add other fields if they exist
            if (in_array('description', $columns)) {
                $updateFields[] = "description = ?";
                $updateValues[] = $description;
            }
            if (in_array('address', $columns)) {
                $updateFields[] = "address = ?";
                $updateValues[] = $address;
            }
            if (in_array('city', $columns)) {
                $updateFields[] = "city = ?";
                $updateValues[] = $city;
            }
            if (in_array('state', $columns)) {
                $updateFields[] = "state = ?";
                $updateValues[] = $state;
            }
            if (in_array('zip', $columns)) {
                $updateFields[] = "zip = ?";
                $updateValues[] = $zip;
            }
            if (in_array('phone', $columns)) {
                $updateFields[] = "phone = ?";
                $updateValues[] = $phone;
            }
            if (in_array('website', $columns)) {
                $updateFields[] = "website = ?";
                $updateValues[] = $website;
            }
            if (in_array('email', $columns)) {
                $updateFields[] = "email = ?";
                $updateValues[] = $email;
            }
            if (in_array('brewery_type', $columns)) {
                $updateFields[] = "brewery_type = ?";
                $updateValues[] = $brewery_type;
            }
            if (in_array('social_links', $columns)) {
                $updateFields[] = "social_links = ?";
                $updateValues[] = json_encode($social_links);
            }

            // Add brewery ID for WHERE clause
            $updateValues[] = $breweryId;

            $sql = "UPDATE breweries SET " . implode(', ', $updateFields) . " WHERE id = ?";


            $stmt = $conn->prepare($sql);
            $stmt->execute($updateValues);
            
            $_SESSION['success_message'] = 'Brewery profile updated successfully!';
            redirect('profile.php');
            
        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            $errors[] = 'An error occurred while updating your profile.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
    if ($brewery && isset($brewery['social_links']) && $brewery['social_links']) {
        $brewery['social_links'] = json_decode($brewery['social_links'], true);
    } else {
        $brewery['social_links'] = [];
    }
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

// Get gallery photos
$galleryPhotos = [];
try {
    $db = new Database();
    $conn = $db->getConnection();

    // Check if gallery table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'brewery_gallery'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("
            SELECT * FROM brewery_gallery
            WHERE brewery_id = ?
            ORDER BY is_featured DESC, sort_order ASC, created_at DESC
        ");
        $stmt->execute([$breweryId]);
        $galleryPhotos = $stmt->fetchAll();
    }
} catch (Exception $e) {
    error_log("Error fetching gallery photos: " . $e->getMessage());
}

include '../includes/header.php';
?>

<style>
.photo-item {
    transition: all 0.3s ease;
}

.photo-item:hover {
    transform: translateY(-5px);
}

.photo-item .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.photo-item .card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.photo-item .card-img-top {
    transition: all 0.3s ease;
}

.photo-item .card-img-top:hover {
    transform: scale(1.05);
}

.upload-dropzone {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-dropzone:hover,
.upload-dropzone.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.photo-stats .card {
    transition: all 0.3s ease;
}

.photo-stats .card:hover {
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .photo-item {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-wrap: wrap;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin: 0.25rem;
    }
}
</style>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-store me-2"></i>Brewery Profile
                </h1>
                <div>
                    <a href="menu.php" class="btn btn-primary me-2">
                        <i class="fas fa-utensils me-1"></i>Manage Menu
                    </a>
                    <a href="digital-board.php" class="btn btn-success">
                        <i class="fas fa-tv me-1"></i>Digital Board
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs nav-fill mb-4" id="breweryTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                        <i class="fas fa-edit me-2"></i>Profile Info
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="gallery-tab" data-bs-toggle="tab" data-bs-target="#gallery" type="button" role="tab">
                        <i class="fas fa-images me-2"></i>Photo Gallery
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="deals-tab" data-bs-toggle="tab" data-bs-target="#deals" type="button" role="tab">
                        <i class="fas fa-tags me-2"></i>Deals & Coupons
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                        <i class="fas fa-star me-2"></i>Reviews
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="breweryTabContent">
        <!-- Profile Information Tab -->
        <div class="tab-pane fade show active" id="profile" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>Edit Profile Information
                            </h5>
                        </div>
                <div class="card-body">
                    <form method="POST" id="profileForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-building me-1"></i>Brewery Name *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($brewery['name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="brewery_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Brewery Type
                                </label>
                                <select class="form-select" id="brewery_type" name="brewery_type">
                                    <option value="micro" <?php echo ($brewery['brewery_type'] ?? '') === 'micro' ? 'selected' : ''; ?>>Microbrewery</option>
                                    <option value="nano" <?php echo ($brewery['brewery_type'] ?? '') === 'nano' ? 'selected' : ''; ?>>Nanobrewery</option>
                                    <option value="regional" <?php echo ($brewery['brewery_type'] ?? '') === 'regional' ? 'selected' : ''; ?>>Regional</option>
                                    <option value="brewpub" <?php echo ($brewery['brewery_type'] ?? '') === 'brewpub' ? 'selected' : ''; ?>>Brewpub</option>
                                    <option value="large" <?php echo ($brewery['brewery_type'] ?? '') === 'large' ? 'selected' : ''; ?>>Large</option>
                                    <option value="planning" <?php echo ($brewery['brewery_type'] ?? '') === 'planning' ? 'selected' : ''; ?>>In Planning</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Tell customers about your brewery..."><?php echo htmlspecialchars($brewery['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>Address
                                </label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="<?php echo htmlspecialchars($brewery['address'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($brewery['city'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state" 
                                       value="<?php echo htmlspecialchars($brewery['state'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="zip" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="zip" name="zip" 
                                       value="<?php echo htmlspecialchars($brewery['zip'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>Phone
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($brewery['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($brewery['email'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="website" class="form-label">
                                <i class="fas fa-globe me-1"></i>Website
                            </label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="<?php echo htmlspecialchars($brewery['website'] ?? ''); ?>"
                                   placeholder="https://yourbrewery.com">
                        </div>
                        
                        <h6 class="mt-4 mb-3">
                            <i class="fas fa-share-alt me-2"></i>Social Media Links
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="facebook" class="form-label">
                                    <i class="fab fa-facebook me-1"></i>Facebook
                                </label>
                                <input type="url" class="form-control" id="facebook" name="facebook" 
                                       value="<?php echo htmlspecialchars($brewery['social_links']['facebook'] ?? ''); ?>"
                                       placeholder="https://facebook.com/yourbrewery">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="instagram" class="form-label">
                                    <i class="fab fa-instagram me-1"></i>Instagram
                                </label>
                                <input type="url" class="form-control" id="instagram" name="instagram" 
                                       value="<?php echo htmlspecialchars($brewery['social_links']['instagram'] ?? ''); ?>"
                                       placeholder="https://instagram.com/yourbrewery">
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="twitter" class="form-label">
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                </label>
                                <input type="url" class="form-control" id="twitter" name="twitter" 
                                       value="<?php echo htmlspecialchars($brewery['social_links']['twitter'] ?? ''); ?>"
                                       placeholder="https://twitter.com/yourbrewery">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Profile Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Claimed Status:</strong>
                        <span class="badge bg-<?php echo ($brewery['claimed'] ?? false) ? 'success' : 'warning'; ?> ms-2">
                            <?php echo ($brewery['claimed'] ?? false) ? 'Claimed' : 'Unclaimed'; ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Verification Status:</strong>
                        <span class="badge bg-<?php echo ($brewery['verified'] ?? false) ? 'success' : 'secondary'; ?> ms-2">
                            <?php echo ($brewery['verified'] ?? false) ? 'Verified' : 'Pending'; ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Profile Completion:</strong>
                        <?php
                        $fields = ['name', 'description', 'address', 'city', 'state', 'phone', 'email', 'website'];
                        $completed = 0;
                        foreach ($fields as $field) {
                            if (!empty($brewery[$field])) $completed++;
                        }
                        $percentage = round(($completed / count($fields)) * 100);
                        ?>
                        <div class="progress mt-2">
                            <div class="progress-bar" role="progressbar" style="width: <?php echo $percentage; ?>%">
                                <?php echo $percentage; ?>%
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted"><?php echo formatDateTime($brewery['updated_at'] ?? $brewery['created_at'] ?? 'Unknown'); ?></small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Followers:</strong>
                        <span class="badge bg-info ms-2"><?php echo number_format($brewery['follower_count'] ?? 0); ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Likes:</strong>
                        <span class="badge bg-danger ms-2"><?php echo number_format($brewery['like_count'] ?? 0); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-image me-2"></i>Images
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Logo</label>
                        <div class="text-center">
                            <?php if (isset($brewery['logo']) && $brewery['logo']): ?>
                                <div class="upload-preview mb-3">
                                    <img src="<?php echo '../' . htmlspecialchars($brewery['logo']); ?>"
                                         alt="Brewery Logo" class="img-thumbnail" style="max-width: 150px; max-height: 150px; cursor: pointer;"
                                         onclick="showImageModal(this.src, 'Brewery Logo')">
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="delete_image" value="logo">
                                        <button type="submit" class="remove-image" title="Delete Logo"
                                                onclick="return confirm('Are you sure you want to delete this logo?')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Current logo</small>
                                </div>
                            <?php else: ?>
                                <div class="bg-light p-4 rounded mb-3">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                    <p class="text-muted mb-0">No logo uploaded</p>
                                </div>
                            <?php endif; ?>

                            <form method="POST" enctype="multipart/form-data" class="d-inline" id="logo_form">
                                <input type="hidden" name="upload_type" value="logo">
                                <input type="file" name="image_file" id="logo_file" accept="image/*" style="display: none;" onchange="handleFileUpload(this, 'logo')">
                                <button type="button" class="btn btn-sm btn-outline-primary image-upload-btn" id="logo_btn" onclick="document.getElementById('logo_file').click()">
                                    <i class="fas fa-upload me-1"></i><?php echo (isset($brewery['logo']) && $brewery['logo']) ? 'Change Logo' : 'Upload Logo'; ?>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Feature Image</label>
                        <div class="text-center">
                            <?php if (isset($brewery['feature_image']) && $brewery['feature_image']): ?>
                                <div class="upload-preview mb-3">
                                    <img src="<?php echo '../' . htmlspecialchars($brewery['feature_image']); ?>"
                                         alt="Feature Image" class="img-thumbnail" style="max-width: 250px; max-height: 200px; cursor: pointer;"
                                         onclick="showImageModal(this.src, 'Feature Image')">
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="delete_image" value="feature_image">
                                        <button type="submit" class="remove-image" title="Delete Feature Image"
                                                onclick="return confirm('Are you sure you want to delete this feature image?')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Current feature image</small>
                                </div>
                            <?php else: ?>
                                <div class="bg-light p-4 rounded mb-3">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                    <p class="text-muted mb-0">No feature image uploaded</p>
                                </div>
                            <?php endif; ?>

                            <form method="POST" enctype="multipart/form-data" class="d-inline" id="feature_form">
                                <input type="hidden" name="upload_type" value="feature_image">
                                <input type="file" name="image_file" id="feature_file" accept="image/*" style="display: none;" onchange="handleFileUpload(this, 'feature')">
                                <button type="button" class="btn btn-sm btn-outline-primary image-upload-btn" id="feature_btn" onclick="document.getElementById('feature_file').click()">
                                    <i class="fas fa-upload me-1"></i><?php echo (isset($brewery['feature_image']) && $brewery['feature_image']) ? 'Change Image' : 'Upload Image'; ?>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Profile Tab -->

        <!-- Photo Gallery Tab -->
        <div class="tab-pane fade" id="gallery" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-images me-2"></i>Photo Gallery Management
                            </h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPhotoModal">
                                <i class="fas fa-plus me-1"></i>Add Photos
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (empty($galleryPhotos)): ?>
                                <div class="row" id="photoGallery">
                                    <div class="col-12 text-center py-5">
                                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                        <h5>No Photos Added Yet</h5>
                                        <p class="text-muted">Start building your photo gallery to showcase your brewery.</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPhotoModal">
                                            <i class="fas fa-plus me-2"></i>Add Your First Photo
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Gallery Stats -->
                                <div class="row mb-4 photo-stats">
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-images fa-2x mb-2"></i>
                                                <h3 class="mb-0"><?php echo count($galleryPhotos); ?></h3>
                                                <small>Total Photos</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-home fa-2x mb-2"></i>
                                                <h3 class="mb-0"><?php echo count(array_filter($galleryPhotos, function($p) { return $p['category'] === 'interior'; })); ?></h3>
                                                <small>Interior</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-building fa-2x mb-2"></i>
                                                <h3 class="mb-0"><?php echo count(array_filter($galleryPhotos, function($p) { return $p['category'] === 'exterior'; })); ?></h3>
                                                <small>Exterior</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-star fa-2x mb-2"></i>
                                                <h3 class="mb-0"><?php echo count(array_filter($galleryPhotos, function($p) { return $p['is_featured']; })); ?></h3>
                                                <small>Featured</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category Filter -->
                                <div class="mb-3">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary active" data-filter="all">All Photos</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="general">General</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="interior">Interior</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="exterior">Exterior</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="brewing">Brewing</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="events">Events</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="food">Food</button>
                                        <button type="button" class="btn btn-outline-primary" data-filter="beer">Beer</button>
                                    </div>
                                </div>

                                <!-- Photo Grid -->
                                <div class="row" id="photoGallery">
                                    <?php foreach ($galleryPhotos as $photo): ?>
                                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4 photo-item" data-category="<?php echo htmlspecialchars($photo['category']); ?>">
                                            <div class="card h-100">
                                                <div class="position-relative">
                                                    <img src="<?php echo '../' . htmlspecialchars($photo['filename']); ?>"
                                                         class="card-img-top"
                                                         style="height: 200px; object-fit: cover; cursor: pointer;"
                                                         alt="<?php echo htmlspecialchars($photo['title']); ?>"
                                                         onclick="showPhotoModal('<?php echo '../' . htmlspecialchars($photo['filename']); ?>', '<?php echo htmlspecialchars($photo['title']); ?>')">

                                                    <?php if ($photo['is_featured']): ?>
                                                        <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                                            <i class="fas fa-star"></i> Featured
                                                        </span>
                                                    <?php endif; ?>

                                                    <div class="position-absolute top-0 end-0 m-2">
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-dark btn-outline-light" type="button" data-bs-toggle="dropdown">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#" onclick="editPhoto(<?php echo $photo['id']; ?>)">
                                                                    <i class="fas fa-edit me-2"></i>Edit
                                                                </a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="setFeaturedPhoto(<?php echo $photo['id']; ?>)">
                                                                    <i class="fas fa-star me-2"></i>Set as Featured
                                                                </a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#" onclick="deletePhoto(<?php echo $photo['id']; ?>)">
                                                                    <i class="fas fa-trash me-2"></i>Delete
                                                                </a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="card-body">
                                                    <h6 class="card-title"><?php echo htmlspecialchars($photo['title']); ?></h6>
                                                    <?php if ($photo['description']): ?>
                                                        <p class="card-text small text-muted"><?php echo htmlspecialchars($photo['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <span class="badge bg-secondary"><?php echo ucfirst($photo['category']); ?></span>
                                                        </small>
                                                        <small class="text-muted"><?php echo formatDateTime($photo['created_at']); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Gallery Tab -->

        <!-- Deals & Coupons Tab -->
        <div class="tab-pane fade" id="deals" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tags me-2"></i>Deals & Coupons Management
                            </h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDealModal">
                                <i class="fas fa-plus me-1"></i>Create Deal
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Deal Title</th>
                                            <th>Type</th>
                                            <th>Discount</th>
                                            <th>Valid Until</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dealsTable">
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                                <h5>No Deals Created Yet</h5>
                                                <p class="text-muted">Create your first deal to attract customers.</p>
                                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDealModal">
                                                    <i class="fas fa-plus me-2"></i>Create Your First Deal
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Deals Tab -->

        <!-- Reviews Tab -->
        <div class="tab-pane fade" id="reviews" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i>Customer Reviews Management
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">4.5</h3>
                                            <small>Average Rating</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">127</h3>
                                            <small>Total Reviews</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">5</h3>
                                            <small>Pending Response</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h3 class="mb-0">98%</h3>
                                            <small>Response Rate</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Customer</th>
                                            <th>Rating</th>
                                            <th>Review</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="reviewsTable">
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                                <h5>No Reviews Yet</h5>
                                                <p class="text-muted">Customer reviews will appear here once they start rating your brewery.</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Reviews Tab -->
    </div>
    <!-- End Tab Content -->
</div>

<!-- Add Photo Modal -->
<div class="modal fade" id="addPhotoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-images me-2"></i>Add Photos to Gallery
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPhotoForm" enctype="multipart/form-data" method="POST">
                    <input type="hidden" name="action" value="upload_photos">
                    <div class="mb-3">
                        <label for="photoFiles" class="form-label">Select Photos *</label>
                        <input type="file" class="form-control" id="photoFiles" name="photos[]" multiple accept="image/*" required>
                        <div class="form-text">You can select multiple photos. Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB per photo.</div>
                    </div>
                    <div class="mb-3">
                        <label for="photoCategory" class="form-label">Category</label>
                        <select class="form-select" id="photoCategory" name="category">
                            <option value="general">General</option>
                            <option value="interior">Interior</option>
                            <option value="exterior">Exterior</option>
                            <option value="brewing">Brewing Process</option>
                            <option value="events">Events</option>
                            <option value="food">Food</option>
                            <option value="beer">Beer</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="photoDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="photoDescription" name="description" rows="3" placeholder="Optional description for these photos"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="uploadPhotosBtn" onclick="uploadPhotos()">
                    <i class="fas fa-upload me-1"></i>Upload Photos
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Photo Modal -->
<div class="modal fade" id="editPhotoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Photo
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPhotoForm">
                    <input type="hidden" id="editPhotoId" name="photo_id">
                    <input type="hidden" name="photo_action" value="update">

                    <div class="mb-3">
                        <label for="editPhotoTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="editPhotoTitle" name="title">
                    </div>

                    <div class="mb-3">
                        <label for="editPhotoCategory" class="form-label">Category</label>
                        <select class="form-select" id="editPhotoCategory" name="category">
                            <option value="general">General</option>
                            <option value="interior">Interior</option>
                            <option value="exterior">Exterior</option>
                            <option value="brewing">Brewing Process</option>
                            <option value="events">Events</option>
                            <option value="food">Food</option>
                            <option value="beer">Beer</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="editPhotoDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editPhotoDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editPhotoFeatured" name="is_featured">
                            <label class="form-check-label" for="editPhotoFeatured">
                                Set as featured photo
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updatePhoto()">
                    <i class="fas fa-save me-1"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Photo Preview Modal -->
<div class="modal fade" id="photoPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoPreviewTitle">Photo Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="photoPreviewImg" src="" alt="" class="img-fluid" style="max-height: 70vh;">
            </div>
        </div>
    </div>
</div>

<!-- Add Deal Modal -->
<div class="modal fade" id="addDealModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tags me-2"></i>Create New Deal
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDealForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealTitle" class="form-label">Deal Title *</label>
                                <input type="text" class="form-control" id="dealTitle" name="title" required placeholder="e.g., Happy Hour Special">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealType" class="form-label">Deal Type *</label>
                                <select class="form-select" id="dealType" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="percentage">Percentage Discount</option>
                                    <option value="fixed">Fixed Amount Off</option>
                                    <option value="bogo">Buy One Get One</option>
                                    <option value="special">Special Offer</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealValue" class="form-label">Discount Value</label>
                                <input type="number" class="form-control" id="dealValue" name="value" placeholder="e.g., 20 for 20% off">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealCode" class="form-label">Coupon Code</label>
                                <input type="text" class="form-control" id="dealCode" name="code" placeholder="Optional coupon code">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="dealDescription" class="form-label">Description *</label>
                        <textarea class="form-control" id="dealDescription" name="description" rows="3" required placeholder="Describe the deal details..."></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealStartDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="dealStartDate" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dealEndDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="dealEndDate" name="end_date">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dealActive" name="active" checked>
                            <label class="form-check-label" for="dealActive">
                                Active (visible to customers)
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="createDealBtn">
                    <i class="fas fa-plus me-1"></i>Create Deal
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" alt="" class="img-fluid" style="max-height: 70vh;">
            </div>
        </div>
    </div>
</div>

<script>
function handleFileUpload(input, type) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const fileType = file.type.toLowerCase();

    if (!allowedTypes.includes(fileType)) {
        alert('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
        input.value = '';
        return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        input.value = '';
        return;
    }

    // Show loading state
    const btn = document.getElementById(type + '_btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
    btn.disabled = true;

    // Submit form
    input.form.submit();
}

// Show image in modal
function showImageModal(src, title) {
    document.getElementById('imageModalImg').src = src;
    document.getElementById('imageModalTitle').textContent = title;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

// Show photo in preview modal
function showPhotoModal(src, title) {
    document.getElementById('photoPreviewImg').src = src;
    document.getElementById('photoPreviewTitle').textContent = title || 'Photo Preview';
    new bootstrap.Modal(document.getElementById('photoPreviewModal')).show();
}

// Upload photos function
function uploadPhotos() {
    const form = document.getElementById('addPhotoForm');
    const fileInput = document.getElementById('photoFiles');
    const uploadBtn = document.getElementById('uploadPhotosBtn');

    if (!fileInput.files.length) {
        showNotification('Please select at least one photo to upload.', 'error');
        return;
    }

    // Show loading state
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
    uploadBtn.disabled = true;

    // Create FormData and submit
    const formData = new FormData(form);

    fetch('profile.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully uploaded ${data.uploaded} photo(s)!`, 'success');
            // Close modal and reload page
            bootstrap.Modal.getInstance(document.getElementById('addPhotoModal')).hide();
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error uploading photos: ' + (data.errors ? data.errors.join(', ') : 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error uploading photos. Please try again.', 'error');
    })
    .finally(() => {
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
}

// Edit photo function
function editPhoto(photoId) {
    // Get photo data from the DOM (you could also fetch from server)
    const photoCard = document.querySelector(`[onclick*="${photoId}"]`).closest('.photo-item');
    const title = photoCard.querySelector('.card-title').textContent;
    const description = photoCard.querySelector('.card-text')?.textContent || '';
    const category = photoCard.dataset.category;
    const isFeatured = photoCard.querySelector('.badge.bg-warning') !== null;

    // Populate modal
    document.getElementById('editPhotoId').value = photoId;
    document.getElementById('editPhotoTitle').value = title;
    document.getElementById('editPhotoDescription').value = description;
    document.getElementById('editPhotoCategory').value = category;
    document.getElementById('editPhotoFeatured').checked = isFeatured;

    // Show modal
    new bootstrap.Modal(document.getElementById('editPhotoModal')).show();
}

// Update photo function
function updatePhoto() {
    const form = document.getElementById('editPhotoForm');
    const formData = new FormData(form);

    fetch('profile.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Photo updated successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editPhotoModal')).hide();
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error updating photo.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating photo. Please try again.', 'error');
    });
}

// Delete photo function
function deletePhoto(photoId) {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
        return;
    }

    const formData = new FormData();
    formData.append('photo_action', 'delete');
    formData.append('photo_id', photoId);

    fetch('profile.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Photo deleted successfully!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error deleting photo.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error deleting photo. Please try again.', 'error');
    });
}

// Set featured photo function
function setFeaturedPhoto(photoId) {
    const formData = new FormData();
    formData.append('photo_action', 'set_featured');
    formData.append('photo_id', photoId);

    fetch('profile.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Featured photo updated!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error setting featured photo.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error setting featured photo. Please try again.', 'error');
    });
}

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Apply dark theme for brewery admin pages
    document.documentElement.setAttribute('data-bs-theme', 'dark');

    // Notification function
    window.showNotification = function(message, type) {
        let alertClass = 'alert-primary';
        if (type === 'success') alertClass = 'alert-success';
        else if (type === 'error') alertClass = 'alert-danger';
        else if (type === 'info') alertClass = 'alert-info';
        else if (type === 'warning') alertClass = 'alert-warning';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    };

    // Profile Form Handler
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                // Show loading state
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving Changes...';
                submitBtn.disabled = true;

                // Re-enable button after a delay (in case of redirect)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    }

    // Photo gallery category filtering
    const filterButtons = document.querySelectorAll('[data-filter]');
    const photoItems = document.querySelectorAll('.photo-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter photos
            photoItems.forEach(item => {
                if (filter === 'all' || item.dataset.category === filter) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Show success/error messages
    <?php if (isset($_SESSION['success_message'])): ?>
        showNotification('<?php echo addslashes($_SESSION['success_message']); ?>', 'success');
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        showNotification('<?php echo addslashes($_SESSION['error_message']); ?>', 'error');
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    // Deal Creation Handler (placeholder for future implementation)
    const createDealBtn = document.getElementById('createDealBtn');
    if (createDealBtn) {
        createDealBtn.addEventListener('click', function() {
            showNotification('Deals & coupons feature coming soon!', 'info');
        });
    }
});

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 500);
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
