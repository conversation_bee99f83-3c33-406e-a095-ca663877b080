<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Menu Management - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/menu-management.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Debug: Log brewery ID
error_log("Current brewery ID: " . $breweryId);
error_log("User data: " . print_r($user, true));

// Handle CSV import
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    header('Content-Type: application/json');

    try {
        $db = new Database();
        $conn = $db->getConnection();

        $importType = $_POST['import_type'] ?? 'food';
        $file = $_FILES['csv_file'];

        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error.');
        }

        if ($file['type'] !== 'text/csv' && pathinfo($file['name'], PATHINFO_EXTENSION) !== 'csv') {
            throw new Exception('Please upload a valid CSV file.');
        }

        $handle = fopen($file['tmp_name'], 'r');
        if (!$handle) {
            throw new Exception('Could not read CSV file.');
        }

        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            throw new Exception('CSV file appears to be empty.');
        }

        // Validate headers for food import
        if ($importType === 'food') {
            $expectedHeaders = ['name', 'category', 'description', 'price', 'status'];
            $normalizedHeaders = array_map('strtolower', array_map('trim', $headers));

            $missingHeaders = array_diff($expectedHeaders, $normalizedHeaders);
            if (!empty($missingHeaders)) {
                throw new Exception('CSV missing required columns: ' . implode(', ', $missingHeaders) . '. Expected: Name, Category, Description, Price, Status');
            }
        }

        $imported = 0;
        $errors = [];

        if ($importType === 'food') {
            // Ensure food_menu table exists
            $stmt = $conn->query("SHOW TABLES LIKE 'food_menu'");
            if ($stmt->rowCount() == 0) {
                throw new Exception('Food menu table does not exist. Please add a food item first.');
            }

            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) < 2) continue; // Skip empty rows

                $name = trim($data[0] ?? '');
                $category = trim($data[1] ?? 'uncategorized');
                $description = trim($data[2] ?? '');
                $price = floatval($data[3] ?? 0);
                $status = trim($data[4] ?? 'available');

                if (empty($name)) {
                    $errors[] = "Row " . ($imported + 1) . ": Name is required";
                    continue;
                }

                try {
                    $stmt = $conn->prepare("
                        INSERT INTO food_menu (brewery_id, name, category, description, price, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$breweryId, $name, $category, $description, $price, $status]);
                    $imported++;
                } catch (Exception $e) {
                    $errors[] = "Row " . ($imported + 1) . ": " . $e->getMessage();
                }
            }
        } else {
            // Beer import logic would go here
            throw new Exception('Beer import not yet implemented.');
        }

        fclose($handle);

        $message = "Successfully imported $imported items.";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(', ', array_slice($errors, 0, 3));
            if (count($errors) > 3) {
                $message .= " and " . (count($errors) - 3) . " more.";
            }
        }

        echo json_encode(['success' => true, 'message' => $message]);
        exit;

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// Handle AJAX requests for menu items
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        $db = new Database();
        $conn = $db->getConnection();

        switch ($_POST['action']) {
            case 'add_food_item':
                $name = sanitizeInput($_POST['name']);
                $category = sanitizeInput($_POST['category']);
                $description = sanitizeInput($_POST['description'] ?? '');
                $price = floatval($_POST['price'] ?? 0);
                $status = sanitizeInput($_POST['status'] ?? 'available');

                if (empty($name) || empty($category)) {
                    throw new Exception('Name and category are required.');
                }

                // Check if food_menu table exists, create if not
                $stmt = $conn->query("SHOW TABLES LIKE 'food_menu'");
                if ($stmt->rowCount() == 0) {
                    $createTable = "
                        CREATE TABLE food_menu (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            brewery_id INT NOT NULL,
                            name VARCHAR(255) NOT NULL,
                            category VARCHAR(100) NOT NULL,
                            description TEXT,
                            price DECIMAL(10,2) DEFAULT 0.00,
                            status ENUM('available', 'unavailable') DEFAULT 'available',
                            featured BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
                        )
                    ";
                    $conn->exec($createTable);
                } else {
                    // Check if category column exists, add it if not
                    $stmt = $conn->query("SHOW COLUMNS FROM food_menu LIKE 'category'");
                    if ($stmt->rowCount() == 0) {
                        $conn->exec("ALTER TABLE food_menu ADD COLUMN category VARCHAR(100) NOT NULL DEFAULT 'uncategorized' AFTER name");
                    }

                    // Check if status column exists, add it if not
                    $stmt = $conn->query("SHOW COLUMNS FROM food_menu LIKE 'status'");
                    if ($stmt->rowCount() == 0) {
                        $conn->exec("ALTER TABLE food_menu ADD COLUMN status ENUM('available', 'unavailable') DEFAULT 'available' AFTER price");
                    }
                }

                $stmt = $conn->prepare("
                    INSERT INTO food_menu (brewery_id, name, category, description, price, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([$breweryId, $name, $category, $description, $price, $status]);

                echo json_encode(['success' => true, 'message' => 'Food item added successfully!']);
                exit;

            case 'delete_food_item':
                $itemId = intval($_POST['item_id']);
                $stmt = $conn->prepare("DELETE FROM food_menu WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$itemId, $breweryId]);

                echo json_encode(['success' => true, 'message' => 'Food item deleted successfully!']);
                exit;

            case 'update_food':
                $foodId = sanitizeInput($_POST['food_id']);
                $name = sanitizeInput($_POST['name']);
                $category = sanitizeInput($_POST['category']);
                $description = sanitizeInput($_POST['description']);
                $price = floatval($_POST['price'] ?? 0);
                $isAvailable = isset($_POST['is_available']) ? 'available' : 'unavailable';

                if (empty($name)) {
                    throw new Exception('Food item name is required.');
                }

                if (empty($foodId)) {
                    throw new Exception('Food item ID is required.');
                }

                // Check if food item exists first
                $checkStmt = $conn->prepare("SELECT * FROM food_menu WHERE id = ? AND brewery_id = ?");
                $checkStmt->execute([$foodId, $breweryId]);
                $existingFood = $checkStmt->fetch();

                if (!$existingFood) {
                    throw new Exception('Food item not found in database.');
                }

                $stmt = $conn->prepare("
                    UPDATE food_menu
                    SET name = ?, category = ?, description = ?, price = ?, status = ?
                    WHERE id = ? AND brewery_id = ?
                ");

                $stmt->execute([$name, $category, $description, $price, $isAvailable, $foodId, $breweryId]);

                if ($stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => 'Food item updated successfully!']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'No changes made to the food item.']);
                }
                exit;

            case 'add_category':
                // Test database connection first
                $testQuery = $conn->query("SELECT 1");
                if (!$testQuery) {
                    throw new Exception('Database connection failed.');
                }

                // Ensure autocommit is enabled
                $conn->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

                $name = sanitizeInput($_POST['name']);
                $type = sanitizeInput($_POST['type'] ?? 'food'); // 'food' or 'beer'
                $description = sanitizeInput($_POST['description'] ?? '');

                if (empty($name)) {
                    throw new Exception('Category name is required.');
                }

                // Verify brewery exists
                $breweryCheck = $conn->prepare("SELECT id FROM breweries WHERE id = ?");
                $breweryCheck->execute([$breweryId]);
                if ($breweryCheck->rowCount() == 0) {
                    throw new Exception('Invalid brewery ID: ' . $breweryId);
                }

                // Check if menu_categories table exists, create if not
                $stmt = $conn->query("SHOW TABLES LIKE 'menu_categories'");
                if ($stmt->rowCount() == 0) {
                    $createTable = "
                        CREATE TABLE menu_categories (
                            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                            brewery_id VARCHAR(36) NOT NULL,
                            name VARCHAR(100) NOT NULL,
                            type ENUM('food', 'beer') DEFAULT 'food',
                            description TEXT,
                            sort_order INT DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_brewery_id (brewery_id),
                            UNIQUE KEY unique_category (brewery_id, name, type)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($createTable);
                }

                // Check if category already exists
                $stmt = $conn->prepare("SELECT id FROM menu_categories WHERE brewery_id = ? AND name = ? AND type = ?");
                $stmt->execute([$breweryId, $name, $type]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception('Category already exists.');
                }

                // Simple insert with autocommit
                $stmt = $conn->prepare("
                    INSERT INTO menu_categories (brewery_id, name, type, description)
                    VALUES (?, ?, ?, ?)
                ");
                $result = $stmt->execute([$breweryId, $name, $type, $description]);

                if (!$result) {
                    throw new Exception('Failed to insert category into database.');
                }

                $insertedId = $conn->lastInsertId();
                if (!$insertedId) {
                    throw new Exception('Category insertion failed - no ID returned.');
                }

                // Verify the category was actually saved
                $verifyStmt = $conn->prepare("SELECT id, name, type, description FROM menu_categories WHERE id = ?");
                $verifyStmt->execute([$insertedId]);
                $savedCategory = $verifyStmt->fetch();

                if (!$savedCategory) {
                    throw new Exception('Category was not saved properly.');
                }

                // Double check by counting all categories for this brewery
                $countStmt = $conn->prepare("SELECT COUNT(*) as total FROM menu_categories WHERE brewery_id = ?");
                $countStmt->execute([$breweryId]);
                $totalCategories = $countStmt->fetch()['total'];

                echo json_encode([
                    'success' => true,
                    'message' => 'Category added successfully!',
                    'category_id' => $insertedId,
                    'debug' => [
                        'brewery_id' => $breweryId,
                        'name' => $name,
                        'type' => $type,
                        'description' => $description,
                        'saved_category' => $savedCategory,
                        'total_categories' => $totalCategories,
                        'insert_result' => $result,
                        'last_insert_id' => $insertedId
                    ]
                ]);
                exit;

            case 'edit_category':
                $categoryId = sanitizeInput($_POST['category_id']);
                $name = sanitizeInput($_POST['name']);
                $type = sanitizeInput($_POST['type'] ?? 'food');
                $description = sanitizeInput($_POST['description'] ?? '');

                if (empty($name)) {
                    throw new Exception('Category name is required.');
                }

                // Check if category exists and belongs to this brewery
                $stmt = $conn->prepare("SELECT id FROM menu_categories WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$categoryId, $breweryId]);
                if ($stmt->rowCount() == 0) {
                    throw new Exception('Category not found.');
                }

                // Check if new name conflicts with existing category
                $stmt = $conn->prepare("SELECT id FROM menu_categories WHERE brewery_id = ? AND name = ? AND type = ? AND id != ?");
                $stmt->execute([$breweryId, $name, $type, $categoryId]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception('Category with this name already exists.');
                }

                $stmt = $conn->prepare("
                    UPDATE menu_categories
                    SET name = ?, type = ?, description = ?, updated_at = NOW()
                    WHERE id = ? AND brewery_id = ?
                ");
                $stmt->execute([$name, $type, $description, $categoryId, $breweryId]);

                echo json_encode(['success' => true, 'message' => 'Category updated successfully!']);
                exit;

            case 'delete_category':
                $categoryId = sanitizeInput($_POST['category_id']);

                if (empty($categoryId)) {
                    throw new Exception('Category ID is required.');
                }

                // First get the category name
                $stmt = $conn->prepare("SELECT name FROM menu_categories WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$categoryId, $breweryId]);
                $category = $stmt->fetch();

                if (!$category) {
                    throw new Exception('Category not found.');
                }

                $categoryName = $category['name'];

                // Check if category is in use in food menu
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM food_menu WHERE brewery_id = ? AND category = ?");
                $stmt->execute([$breweryId, $categoryName]);
                $foodCount = $stmt->fetch()['count'];

                // Check if category is in use in beer menu
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu WHERE brewery_id = ? AND category = ?");
                $stmt->execute([$breweryId, $categoryName]);
                $beerCount = $stmt->fetch()['count'];

                $totalUsage = $foodCount + $beerCount;

                if ($totalUsage > 0) {
                    throw new Exception("Cannot delete category '$categoryName' - it is being used by $totalUsage menu item(s). Please reassign items first.");
                }

                // Delete the category
                $stmt = $conn->prepare("DELETE FROM menu_categories WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$categoryId, $breweryId]);

                if ($stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => "Category '$categoryName' deleted successfully!"]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to delete category - no rows affected.']);
                }
                exit;

            case 'add_beer':
                $name = sanitizeInput($_POST['name']);
                $style = sanitizeInput($_POST['style']);
                $description = sanitizeInput($_POST['description']);
                $abv = floatval($_POST['abv'] ?? 0);
                $ibu = intval($_POST['ibu'] ?? 0);
                $price = floatval($_POST['price'] ?? 0);
                $isAvailable = intval($_POST['is_available'] ?? 1);

                if (empty($name) || empty($style)) {
                    throw new Exception('Beer name and style are required.');
                }

                // Check if beer_menu table exists and create if needed
                $stmt = $conn->query("SHOW TABLES LIKE 'beer_menu'");
                if ($stmt->rowCount() == 0) {
                    // Create the table
                    $createTable = "
                        CREATE TABLE beer_menu (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            brewery_id VARCHAR(36) NOT NULL,
                            name VARCHAR(255) NOT NULL,
                            style VARCHAR(100),
                            description TEXT,
                            abv DECIMAL(4,2),
                            ibu INT,
                            price DECIMAL(6,2),
                            is_available BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_brewery_id (brewery_id),
                            INDEX idx_is_available (is_available)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($createTable);
                }

                $stmt = $conn->prepare("
                    INSERT INTO beer_menu (brewery_id, name, style, description, abv, ibu, price, is_available)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $result = $stmt->execute([$breweryId, $name, $style, $description, $abv, $ibu, $price, $isAvailable]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Beer added successfully!']);
                } else {
                    $errorInfo = $stmt->errorInfo();
                    throw new Exception('Failed to insert beer into database: ' . $errorInfo[2]);
                }
                exit;

            case 'import_beers':
                if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('Please select a valid CSV file.');
                }

                $csvFile = $_FILES['csv_file']['tmp_name'];
                $replaceExisting = isset($_POST['replace_existing']);

                // Read and parse CSV
                $handle = fopen($csvFile, 'r');
                if (!$handle) {
                    throw new Exception('Could not read CSV file.');
                }

                // Skip header row
                $headers = fgetcsv($handle);

                $importedCount = 0;
                $errors = [];

                // Clear existing beers if replace option is selected
                if ($replaceExisting) {
                    $stmt = $conn->prepare("DELETE FROM beer_menu WHERE brewery_id = ?");
                    $stmt->execute([$breweryId]);
                }

                // Prepare insert statement
                $stmt = $conn->prepare("
                    INSERT INTO beer_menu (brewery_id, name, style, description, abv, ibu, price, is_available)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    style = VALUES(style),
                    description = VALUES(description),
                    abv = VALUES(abv),
                    ibu = VALUES(ibu),
                    price = VALUES(price),
                    is_available = VALUES(is_available)
                ");

                while (($data = fgetcsv($handle)) !== FALSE) {
                    if (count($data) < 6) continue; // Skip incomplete rows

                    try {
                        $name = trim($data[0]);
                        $style = trim($data[1]);
                        $description = trim($data[2] ?? '');
                        $abv = floatval($data[3] ?? 0);
                        $ibu = intval($data[4] ?? 0);
                        $price = floatval($data[5] ?? 0);
                        $available = strtolower(trim($data[6] ?? 'yes')) === 'yes' ? 1 : 0;

                        if (empty($name)) continue; // Skip rows without name

                        $stmt->execute([$breweryId, $name, $style, $description, $abv, $ibu, $price, $available]);
                        $importedCount++;

                    } catch (Exception $e) {
                        $errors[] = "Row with beer '{$name}': " . $e->getMessage();
                    }
                }

                fclose($handle);

                if (!empty($errors) && $importedCount === 0) {
                    throw new Exception('Import failed: ' . implode(', ', array_slice($errors, 0, 3)));
                }

                $message = "Successfully imported {$importedCount} beers.";
                if (!empty($errors)) {
                    $message .= " " . count($errors) . " rows had errors.";
                }

                echo json_encode(['success' => true, 'message' => $message, 'count' => $importedCount]);
                exit;

            case 'get_beer':
                $beerId = sanitizeInput($_POST['beer_id']);

                $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$beerId, $breweryId]);
                $beer = $stmt->fetch();

                if ($beer) {
                    echo json_encode(['success' => true, 'beer' => $beer]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Beer not found']);
                }
                exit;

            case 'update_beer':
                $beerId = sanitizeInput($_POST['beer_id']);
                $name = sanitizeInput($_POST['name']);
                $style = sanitizeInput($_POST['style']);
                $description = sanitizeInput($_POST['description']);
                $abv = floatval($_POST['abv'] ?? 0);
                $ibu = intval($_POST['ibu'] ?? 0);
                $price = floatval($_POST['price'] ?? 0);
                $tapNumber = intval($_POST['tap_number'] ?? 0) ?: null;
                $category = sanitizeInput($_POST['category']);
                $isAvailable = isset($_POST['is_available']) ? 1 : 0;

                if (empty($name) || empty($style)) {
                    throw new Exception('Beer name and style are required.');
                }

                if (empty($beerId)) {
                    throw new Exception('Beer ID is required.');
                }

                // Check if beer exists first
                $checkStmt = $conn->prepare("SELECT * FROM beer_menu WHERE id = ? AND brewery_id = ?");
                $checkStmt->execute([$beerId, $breweryId]);
                $existingBeer = $checkStmt->fetch();

                if (!$existingBeer) {
                    throw new Exception('Beer not found in database.');
                }

                $stmt = $conn->prepare("
                    UPDATE beer_menu
                    SET name = ?, style = ?, description = ?, abv = ?, ibu = ?, price = ?,
                        tap_number = ?, category = ?, is_available = ?
                    WHERE id = ? AND brewery_id = ?
                ");

                $stmt->execute([$name, $style, $description, $abv, $ibu, $price, $tapNumber, $category, $isAvailable, $beerId, $breweryId]);

                if ($stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => 'Beer updated successfully!']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'No changes made to the beer.']);
                }
                exit;

            case 'duplicate_beer':
                $beerId = sanitizeInput($_POST['beer_id']);

                // Get original beer data
                $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$beerId, $breweryId]);
                $originalBeer = $stmt->fetch();

                if (!$originalBeer) {
                    throw new Exception('Original beer not found');
                }

                // Create duplicate with modified name
                $newName = $originalBeer['name'] . ' (Copy)';
                $stmt = $conn->prepare("
                    INSERT INTO beer_menu (brewery_id, name, style, description, abv, ibu, price, tap_number, category, is_available)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NULL, ?, ?)
                ");
                $stmt->execute([
                    $breweryId, $newName, $originalBeer['style'], $originalBeer['description'],
                    $originalBeer['abv'], $originalBeer['ibu'], $originalBeer['price'],
                    $originalBeer['category'], $originalBeer['is_available']
                ]);

                echo json_encode(['success' => true, 'message' => 'Beer duplicated successfully!']);
                exit;

            case 'toggle_beer_availability':
                $beerId = sanitizeInput($_POST['beer_id']);
                $available = $_POST['available'] === 'true' ? 1 : 0;

                $stmt = $conn->prepare("UPDATE beer_menu SET is_available = ?, updated_at = NOW() WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$available, $beerId, $breweryId]);

                if ($stmt->rowCount() > 0) {
                    $status = $available ? 'shown' : 'hidden';
                    echo json_encode(['success' => true, 'message' => "Beer $status successfully!"]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Beer not found']);
                }
                exit;

            case 'delete_beer':
                $beerId = sanitizeInput($_POST['beer_id']);

                $stmt = $conn->prepare("DELETE FROM beer_menu WHERE id = ? AND brewery_id = ?");
                $stmt->execute([$beerId, $breweryId]);

                if ($stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => 'Beer deleted successfully!']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Beer not found']);
                }
                exit;

            case 'export_csv':
                $type = sanitizeInput($_POST['export_type'] ?? 'food'); // 'food' or 'beer'

                if ($type === 'food') {
                    $stmt = $conn->prepare("SELECT * FROM food_menu WHERE brewery_id = ? ORDER BY category, name");
                    $stmt->execute([$breweryId]);
                    $items = $stmt->fetchAll();

                    $filename = 'food_menu_' . date('Y-m-d_H-i-s') . '.csv';
                    $headers = ['Name', 'Category', 'Description', 'Price', 'Status'];
                } else {
                    $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE brewery_id = ? ORDER BY name");
                    $stmt->execute([$breweryId]);
                    $items = $stmt->fetchAll();

                    $filename = 'beer_menu_' . date('Y-m-d_H-i-s') . '.csv';
                    $headers = ['Name', 'Style', 'Description', 'ABV', 'IBU', 'Price', 'Available'];
                }

                // Set headers for CSV download
                header('Content-Type: text/csv');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Expires: 0');

                $output = fopen('php://output', 'w');
                fputcsv($output, $headers);

                foreach ($items as $item) {
                    if ($type === 'food') {
                        fputcsv($output, [
                            $item['name'],
                            $item['category'],
                            $item['description'],
                            $item['price'],
                            $item['status']
                        ]);
                    } else {
                        fputcsv($output, [
                            $item['name'],
                            $item['style'] ?? '',
                            $item['description'],
                            $item['abv'],
                            $item['ibu'],
                            $item['price'],
                            $item['available'] ? 'Yes' : 'No'
                        ]);
                    }
                }

                fclose($output);
                exit;

            default:
                throw new Exception('Invalid action.');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

// Get beer menu data
$beerMenu = [];
$beerStats = ['total' => 0, 'available' => 0, 'low_stock' => 0, 'out_of_stock' => 0];

// Get food menu data
$foodMenu = [];
$foodStats = ['total' => 0, 'available' => 0, 'unavailable' => 0];
try {
    $stmt = $conn->prepare("
        SELECT bm.*, bs.name as style_name, bs.category as style_category
        FROM beer_menu bm
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
        WHERE bm.brewery_id = ?
        ORDER BY bm.tap_number ASC, bm.name ASC
    ");
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();

    // Calculate stats
    $beerStats['total'] = count($beerMenu);
    foreach ($beerMenu as $beer) {
        if ($beer['available']) {
            $beerStats['available']++;
        } else {
            $beerStats['out_of_stock']++;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching beer menu: " . $e->getMessage());
}

// Get food menu data (check if table exists first)
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'food_menu'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("
            SELECT * FROM food_menu
            WHERE brewery_id = ?
            ORDER BY category ASC, name ASC
        ");
        $stmt->execute([$breweryId]);
        $foodMenu = $stmt->fetchAll();

        $foodStats['total'] = count($foodMenu);
        foreach ($foodMenu as $food) {
            if (isset($food['status']) && $food['status'] === 'available') {
                $foodStats['available']++;
            } else {
                $foodStats['unavailable']++;
            }
        }
    }
} catch (Exception $e) {
    error_log("Error fetching food menu: " . $e->getMessage());
}

// Get categories data
$categories = [];
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'menu_categories'");
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("
            SELECT * FROM menu_categories
            WHERE brewery_id = ?
            ORDER BY type ASC, sort_order ASC, name ASC
        ");
        $stmt->execute([$breweryId]);
        $categories = $stmt->fetchAll();

        // Debug: Log what we found
        error_log("Categories found for brewery $breweryId: " . count($categories));
        error_log("Categories data: " . print_r($categories, true));
    } else {
        error_log("menu_categories table does not exist");
    }
} catch (Exception $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// Define comprehensive beer styles
$beerStyles = [
    // Ales
    ['category' => 'Pale Ales', 'name' => 'American Pale Ale'],
    ['category' => 'Pale Ales', 'name' => 'English Pale Ale'],
    ['category' => 'Pale Ales', 'name' => 'Belgian Pale Ale'],

    // IPAs
    ['category' => 'India Pale Ales', 'name' => 'American IPA'],
    ['category' => 'India Pale Ales', 'name' => 'English IPA'],
    ['category' => 'India Pale Ales', 'name' => 'Double/Imperial IPA'],
    ['category' => 'India Pale Ales', 'name' => 'New England IPA'],
    ['category' => 'India Pale Ales', 'name' => 'West Coast IPA'],
    ['category' => 'India Pale Ales', 'name' => 'Session IPA'],
    ['category' => 'India Pale Ales', 'name' => 'Belgian IPA'],
    ['category' => 'India Pale Ales', 'name' => 'Black IPA'],

    // Wheat Beers
    ['category' => 'Wheat Beers', 'name' => 'American Wheat'],
    ['category' => 'Wheat Beers', 'name' => 'Hefeweizen'],
    ['category' => 'Wheat Beers', 'name' => 'Witbier'],
    ['category' => 'Wheat Beers', 'name' => 'Wheat Wine'],

    // Lagers
    ['category' => 'Lagers', 'name' => 'American Lager'],
    ['category' => 'Lagers', 'name' => 'German Pilsner'],
    ['category' => 'Lagers', 'name' => 'Czech Pilsner'],
    ['category' => 'Lagers', 'name' => 'Vienna Lager'],
    ['category' => 'Lagers', 'name' => 'Märzen/Oktoberfest'],
    ['category' => 'Lagers', 'name' => 'Helles'],
    ['category' => 'Lagers', 'name' => 'Bock'],
    ['category' => 'Lagers', 'name' => 'Doppelbock'],

    // Stouts & Porters
    ['category' => 'Stouts & Porters', 'name' => 'Dry Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Sweet Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Imperial Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Chocolate Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Coffee Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Oatmeal Stout'],
    ['category' => 'Stouts & Porters', 'name' => 'Porter'],
    ['category' => 'Stouts & Porters', 'name' => 'Robust Porter'],

    // Sours
    ['category' => 'Sour Beers', 'name' => 'Berliner Weisse'],
    ['category' => 'Sour Beers', 'name' => 'Gose'],
    ['category' => 'Sour Beers', 'name' => 'Lambic'],
    ['category' => 'Sour Beers', 'name' => 'American Sour'],
    ['category' => 'Sour Beers', 'name' => 'Flanders Red'],

    // Belgian Styles
    ['category' => 'Belgian Styles', 'name' => 'Belgian Dubbel'],
    ['category' => 'Belgian Styles', 'name' => 'Belgian Tripel'],
    ['category' => 'Belgian Styles', 'name' => 'Belgian Quadrupel'],
    ['category' => 'Belgian Styles', 'name' => 'Saison'],
    ['category' => 'Belgian Styles', 'name' => 'Belgian Strong Ale'],

    // Brown Ales
    ['category' => 'Brown Ales', 'name' => 'American Brown Ale'],
    ['category' => 'Brown Ales', 'name' => 'English Brown Ale'],
    ['category' => 'Brown Ales', 'name' => 'Nut Brown Ale'],

    // Specialty
    ['category' => 'Specialty', 'name' => 'Fruit Beer'],
    ['category' => 'Specialty', 'name' => 'Spiced Beer'],
    ['category' => 'Specialty', 'name' => 'Smoked Beer'],
    ['category' => 'Specialty', 'name' => 'Barrel-Aged'],
    ['category' => 'Specialty', 'name' => 'Gluten-Free'],
    ['category' => 'Specialty', 'name' => 'Low Alcohol'],
    ['category' => 'Specialty', 'name' => 'Non-Alcoholic']
];

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-utensils me-2 text-primary"></i>Menu Management
                    </h1>
                    <p class="text-muted mb-0">Manage your beer and food menus</p>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-outline-secondary btn-sm" id="darkModeToggle" title="Toggle Dark Mode">
                        <i class="fas fa-moon" id="darkModeIcon"></i>
                    </button>
                    <a href="profile.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Profile
                    </a>
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeerModal">
                            <i class="fas fa-beer me-1"></i>Add Beer
                        </button>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFoodModal">
                            <i class="fas fa-utensils me-1"></i>Add Food
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="menuTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="beer-tab" data-bs-toggle="tab" data-bs-target="#beer-menu" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beer Menu
                        <span class="badge bg-primary ms-2"><?php echo $beerStats['total']; ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="food-tab" data-bs-toggle="tab" data-bs-target="#food-menu" type="button" role="tab">
                        <i class="fas fa-utensils me-2"></i>Food Menu
                        <span class="badge bg-success ms-2"><?php echo $foodStats['total']; ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">
                        <i class="fas fa-tags me-2"></i>Categories
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import-export" type="button" role="tab">
                        <i class="fas fa-file-import me-2"></i>Import/Export
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="menuTabContent">
        <!-- Beer Menu Tab -->
        <div class="tab-pane fade show active" id="beer-menu" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-beer me-2"></i>Beer Menu
                            </h5>
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm" style="width: auto;">
                                    <option>All Categories</option>
                                    <option>IPA</option>
                                    <option>Lager</option>
                                    <option>Stout</option>
                                    <option>Wheat</option>
                                </select>
                                <div class="btn-group btn-group-sm" role="group">
                                    <input type="radio" class="btn-check" name="beer-view" id="beer-grid" checked>
                                    <label class="btn btn-outline-secondary" for="beer-grid">
                                        <i class="fas fa-th-large"></i>
                                    </label>
                                    <input type="radio" class="btn-check" name="beer-view" id="beer-list">
                                    <label class="btn btn-outline-secondary" for="beer-list">
                                        <i class="fas fa-list"></i>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Dynamic Beer Items -->
                            <div class="row" id="beer-grid-view">
                                <?php if (empty($beerMenu)): ?>
                                    <div class="col-12">
                                        <div class="text-center py-5">
                                            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                                            <h5>No Beers Added Yet</h5>
                                            <p class="text-muted">Start building your beer menu by adding your first beer.</p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeerModal">
                                                <i class="fas fa-plus me-2"></i>Add Your First Beer
                                            </button>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($beerMenu as $beer): ?>
                                        <div class="col-md-6 mb-4">
                                            <div class="menu-item-card">
                                                <div class="menu-item-header">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h6 class="menu-item-name"><?php echo htmlspecialchars($beer['name']); ?></h6>
                                                            <?php if ($beer['style_category']): ?>
                                                                <span class="badge bg-warning"><?php echo htmlspecialchars($beer['style_category']); ?></span>
                                                            <?php endif; ?>
                                                            <?php if ($beer['tap_number']): ?>
                                                                <span class="badge bg-info ms-1">Tap <?php echo $beer['tap_number']; ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#" onclick="editBeer('<?php echo $beer['id']; ?>')"><i class="fas fa-edit me-2"></i>Edit</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="duplicateBeer('<?php echo $beer['id']; ?>')"><i class="fas fa-copy me-2"></i>Duplicate</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="toggleBeerAvailability('<?php echo $beer['id']; ?>', <?php echo $beer['available'] ? 'false' : 'true'; ?>)">
                                                                    <i class="fas fa-<?php echo $beer['available'] ? 'eye-slash' : 'eye'; ?> me-2"></i>
                                                                    <?php echo $beer['available'] ? 'Hide' : 'Show'; ?>
                                                                </a></li>
                                                                <li><hr class="dropdown-divider"></li>
                                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteBeer('<?php echo $beer['id']; ?>')"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="menu-item-body">
                                                    <?php if ($beer['description']): ?>
                                                        <p class="menu-item-description"><?php echo htmlspecialchars($beer['description']); ?></p>
                                                    <?php endif; ?>
                                                    <div class="menu-item-details">
                                                        <div class="row">
                                                            <div class="col-6">
                                                                <small class="text-muted">ABV:</small>
                                                                <span class="fw-bold"><?php echo $beer['abv'] ? $beer['abv'] . '%' : 'N/A'; ?></span>
                                                            </div>
                                                            <div class="col-6">
                                                                <small class="text-muted">IBU:</small>
                                                                <span class="fw-bold"><?php echo $beer['ibu'] ?? 'N/A'; ?></span>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-2">
                                                            <div class="col-6">
                                                                <small class="text-muted">Price:</small>
                                                                <span class="fw-bold text-primary">
                                                                    <?php echo $beer['price'] ? '$' . number_format($beer['price'], 2) : 'N/A'; ?>
                                                                </span>
                                                            </div>
                                                            <div class="col-6">
                                                                <span class="badge bg-<?php echo $beer['available'] ? 'success' : 'danger'; ?>">
                                                                    <?php echo $beer['available'] ? 'Available' : 'Out of Stock'; ?>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>


                            </div>
                        </div>
                    </div>
                </div>

                <!-- Beer Menu Sidebar -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Beer Menu Stats
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <div class="stat-label">Total Beers</div>
                                <div class="stat-value"><?php echo $beerStats['total']; ?></div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Available</div>
                                <div class="stat-value text-success"><?php echo $beerStats['available']; ?></div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Low Stock</div>
                                <div class="stat-value text-warning"><?php echo $beerStats['low_stock']; ?></div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Out of Stock</div>
                                <div class="stat-value text-danger"><?php echo $beerStats['out_of_stock']; ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBeerModal">
                                    <i class="fas fa-plus me-2"></i>Add New Beer
                                </button>
                                <button class="btn btn-outline-primary" onclick="switchToCategories()">
                                    <i class="fas fa-tags me-2"></i>Manage Categories
                                </button>
                                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#importBeersModal">
                                    <i class="fas fa-file-import me-2"></i>Import Beers
                                </button>
                                <a href="menu-preview.php" class="btn btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye me-2"></i>Preview Menu
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Food Menu Tab -->
        <div class="tab-pane fade" id="food-menu" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-utensils me-2"></i>Food Menu
                    </h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addFoodModal">
                        <i class="fas fa-plus me-1"></i>Add Food Item
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($foodMenu)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5>No Food Items Yet</h5>
                            <p class="text-muted">Start building your food menu by adding your first item.</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addFoodModal">
                                <i class="fas fa-plus me-2"></i>Add Your First Food Item
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="row" id="food-items-container">
                            <?php
                            $foodItemsByCategory = [];
                            foreach ($foodMenu as $item) {
                                $category = $item['category'] ?? 'uncategorized';
                                $foodItemsByCategory[$category][] = $item;
                            }

                            foreach ($foodItemsByCategory as $category => $items): ?>
                                <div class="col-12 mb-4">
                                    <h6 class="text-uppercase text-muted mb-3"><?php echo ucfirst($category); ?></h6>
                                    <div class="row">
                                        <?php foreach ($items as $item): ?>
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card h-100 food-item-card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="card-title mb-0"><?php echo htmlspecialchars($item['name'] ?? 'Unnamed Item'); ?></h6>
                                                            <div class="dropdown">
                                                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                                    <i class="fas fa-ellipsis-v"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li><a class="dropdown-item" href="#" onclick="editFood('<?php echo $item['id'] ?? ''; ?>', '<?php echo htmlspecialchars($item['name'] ?? ''); ?>', '<?php echo htmlspecialchars($item['category'] ?? ''); ?>', '<?php echo htmlspecialchars($item['description'] ?? ''); ?>', '<?php echo $item['price'] ?? ''; ?>', '<?php echo $item['is_available'] ?? 1; ?>')"><i class="fas fa-edit me-2"></i>Edit</a></li>
                                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteFoodItem(<?php echo $item['id'] ?? 0; ?>)"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <?php if (isset($item['description']) && $item['description']): ?>
                                                            <p class="card-text text-muted small"><?php echo htmlspecialchars($item['description']); ?></p>
                                                        <?php endif; ?>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="h6 mb-0 text-primary">$<?php echo number_format($item['price'] ?? 0, 2); ?></span>
                                                            <span class="badge bg-<?php echo (isset($item['status']) && $item['status'] === 'available') ? 'success' : 'secondary'; ?>">
                                                                <?php echo ucfirst($item['status'] ?? 'unavailable'); ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Categories Tab -->
        <div class="tab-pane fade" id="categories" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tags me-2"></i>Menu Categories
                            </h5>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus me-1"></i>Add Category
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (empty($categories)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                    <h5>No Categories Yet</h5>
                                    <p class="text-muted">Create categories to organize your menu items.</p>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                        <i class="fas fa-plus me-2"></i>Add Your First Category
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Type</th>
                                                <th>Description</th>
                                                <th>Items</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($categories as $category): ?>
                                                <?php
                                                // Count items in this category
                                                $itemCount = 0;
                                                $categoryType = $category['type'] ?? 'food';
                                                $categoryName = $category['name'] ?? '';

                                                if ($categoryType === 'food') {
                                                    foreach ($foodMenu as $item) {
                                                        if (($item['category'] ?? '') === $categoryName) {
                                                            $itemCount++;
                                                        }
                                                    }
                                                } else {
                                                    foreach ($beerMenu as $item) {
                                                        if (($item['category'] ?? '') === $categoryName) {
                                                            $itemCount++;
                                                        }
                                                    }
                                                }
                                                ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($categoryName); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $categoryType === 'food' ? 'success' : 'primary'; ?>">
                                                            <i class="fas fa-<?php echo $categoryType === 'food' ? 'utensils' : 'beer'; ?> me-1"></i>
                                                            <?php echo ucfirst($categoryType); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">
                                                            <?php echo htmlspecialchars($category['description'] ?? 'No description'); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?php echo $itemCount; ?> items</span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" title="Edit"
                                                                    onclick="editCategory('<?php echo htmlspecialchars($category['id'] ?? ''); ?>', '<?php echo htmlspecialchars($categoryName); ?>', '<?php echo htmlspecialchars($categoryType); ?>', '<?php echo htmlspecialchars($category['description'] ?? ''); ?>')">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger"
                                                                    onclick="deleteCategory('<?php echo htmlspecialchars($category['id'] ?? ''); ?>')"
                                                                    title="Delete"
                                                                    <?php echo $itemCount > 0 ? 'disabled' : ''; ?>>
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Category Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">Food Categories</h6>
                                <small class="text-muted">Examples: Appetizers, Entrees, Desserts, Beverages</small>
                            </div>
                            <div class="mb-3">
                                <h6 class="text-primary">Beer Categories</h6>
                                <small class="text-muted">Examples: IPA, Lager, Stout, Wheat, Seasonal</small>
                            </div>
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Categories help organize your menu and make it easier for customers to find what they're looking for.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import/Export Tab -->
        <div class="tab-pane fade" id="import-export" role="tabpanel">
            <div class="row">
                <div class="col-lg-6">
                    <!-- Import Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2 text-success"></i>Import Menu Items
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="importForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="importType" class="form-label">Import Type</label>
                                    <select class="form-select" id="importType" name="import_type" required>
                                        <option value="food">Food Menu</option>
                                        <option value="beer" disabled>Beer Menu (Coming Soon)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="csvFile" class="form-label">CSV File</label>
                                    <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                                    <div class="form-text">
                                        Upload a CSV file with your menu items. Maximum file size: 5MB.
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-upload me-2"></i>Import CSV
                                </button>
                            </form>

                            <div class="mt-4">
                                <h6 class="text-primary">Food CSV Format:</h6>
                                <div class="bg-light p-3 rounded">
                                    <code>Name, Category, Description, Price, Status</code><br>
                                    <small class="text-muted">
                                        Example: "Buffalo Wings, Appetizers, Crispy wings with buffalo sauce, 12.99, available"
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <!-- Export Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-download me-2 text-primary"></i>Export Menu Items
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Export Options</label>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success" onclick="exportCSV('food')">
                                        <i class="fas fa-utensils me-2"></i>Export Food Menu
                                        <span class="badge bg-success ms-2"><?php echo $foodStats['total']; ?></span>
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="exportCSV('beer')">
                                        <i class="fas fa-beer me-2"></i>Export Beer Menu
                                        <span class="badge bg-primary ms-2"><?php echo $beerStats['total']; ?></span>
                                    </button>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Export includes:</strong> All menu items with their details, categories, prices, and availability status.
                            </div>
                        </div>
                    </div>

                    <!-- Sample Templates -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-file-download me-2"></i>Sample Templates
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small mb-3">Download sample CSV templates to get started:</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-outline-secondary" onclick="downloadTemplate('food')">
                                    <i class="fas fa-file-csv me-1"></i>Food Menu Template
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="downloadTemplate('beer')">
                                    <i class="fas fa-file-csv me-1"></i>Beer Menu Template
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Beer Modal -->
<div class="modal fade" id="addBeerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-beer me-2"></i>Add New Beer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addBeerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="beerName" class="form-label">Beer Name *</label>
                                <input type="text" class="form-control" id="beerName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="beerStyle" class="form-label">Style *</label>
                                <select class="form-select" id="beerStyle" name="style" required>
                                    <option value="">Select Beer Style</option>
                                    <?php
                                    $currentCategory = '';
                                    foreach ($beerStyles as $style):
                                        if ($currentCategory !== $style['category']):
                                            if ($currentCategory !== '') echo '</optgroup>';
                                            echo '<optgroup label="' . htmlspecialchars($style['category']) . '">';
                                            $currentCategory = $style['category'];
                                        endif;
                                    ?>
                                        <option value="<?php echo htmlspecialchars($style['name']); ?>">
                                            <?php echo htmlspecialchars($style['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                                    <optgroup label="Other">
                                        <option value="custom">Custom Style (Enter Below)</option>
                                    </optgroup>
                                </select>
                                <input type="text" class="form-control mt-2" id="beerStyleCustom" name="style_custom" placeholder="Enter custom beer style" style="display: none;">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="beerDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="beerDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="beerABV" class="form-label">ABV (%)</label>
                                <input type="number" class="form-control" id="beerABV" step="0.1" min="0" max="20">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="beerIBU" class="form-label">IBU</label>
                                <input type="number" class="form-control" id="beerIBU" min="0" max="120">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="beerPrice" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="beerPrice" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="beerStatus" class="form-label">Status</label>
                                <select class="form-select" id="beerStatus">
                                    <option value="available">Available</option>
                                    <option value="low_stock">Low Stock</option>
                                    <option value="out_of_stock">Out of Stock</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addBeerBtn" onclick="addBeer()">
                    <i class="fas fa-plus me-1"></i>Add Beer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Beer Modal -->
<div class="modal fade" id="editBeerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Beer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editBeerForm">
                    <input type="hidden" id="editBeerId" name="beer_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBeerName" class="form-label">Beer Name *</label>
                                <input type="text" class="form-control" id="editBeerName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBeerStyle" class="form-label">Style *</label>
                                <select class="form-select" id="editBeerStyle" name="style" required>
                                    <option value="">Select Beer Style</option>
                                    <?php
                                    $currentCategory = '';
                                    foreach ($beerStyles as $style):
                                        if ($currentCategory !== $style['category']):
                                            if ($currentCategory !== '') echo '</optgroup>';
                                            echo '<optgroup label="' . htmlspecialchars($style['category']) . '">';
                                            $currentCategory = $style['category'];
                                        endif;
                                    ?>
                                        <option value="<?php echo htmlspecialchars($style['name']); ?>">
                                            <?php echo htmlspecialchars($style['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                                    <optgroup label="Other">
                                        <option value="custom">Custom Style (Enter Below)</option>
                                    </optgroup>
                                </select>
                                <input type="text" class="form-control mt-2" id="editBeerStyleCustom" name="style_custom" placeholder="Enter custom beer style" style="display: none;">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editBeerDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editBeerDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editBeerABV" class="form-label">ABV (%)</label>
                                <input type="number" class="form-control" id="editBeerABV" name="abv" step="0.1" min="0" max="20">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editBeerIBU" class="form-label">IBU</label>
                                <input type="number" class="form-control" id="editBeerIBU" name="ibu" min="0" max="120">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editBeerPrice" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="editBeerPrice" name="price" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBeerTapNumber" class="form-label">Tap Number</label>
                                <input type="number" class="form-control" id="editBeerTapNumber" name="tap_number" min="1" max="50">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editBeerCategory" class="form-label">Category</label>
                                <input type="text" class="form-control" id="editBeerCategory" name="category">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editBeerAvailable" name="is_available">
                            <label class="form-check-label" for="editBeerAvailable">
                                Available for serving
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateBeerBtn" onclick="updateBeer()">
                    <i class="fas fa-save me-1"></i>Update Beer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Food Modal -->
<div class="modal fade" id="addFoodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-utensils me-2"></i>Add Food Item
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addFoodForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="foodName" class="form-label">Item Name *</label>
                                <input type="text" class="form-control" id="foodName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="foodCategory" class="form-label">Category *</label>
                                <select class="form-select" id="foodCategory" required>
                                    <option value="">Select Category</option>
                                    <?php
                                    $foodCategories = array_filter($categories, function($cat) {
                                        return isset($cat['type']) && $cat['type'] === 'food';
                                    });
                                    if (empty($foodCategories)): ?>
                                        <option value="appetizers">Appetizers</option>
                                        <option value="entrees">Entrees</option>
                                        <option value="desserts">Desserts</option>
                                        <option value="sides">Sides</option>
                                    <?php else: ?>
                                        <?php foreach ($foodCategories as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category['name'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($category['name'] ?? 'Unnamed Category'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="foodDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="foodDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="foodPrice" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="foodPrice" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="foodStatus" class="form-label">Status</label>
                                <select class="form-select" id="foodStatus">
                                    <option value="available">Available</option>
                                    <option value="unavailable">Unavailable</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="addFoodBtn">
                    <i class="fas fa-plus me-1"></i>Add Food Item
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tags me-2"></i>Add Category
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCategoryForm">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryType" class="form-label">Category Type *</label>
                        <select class="form-select" id="categoryType" required>
                            <option value="">Select Type</option>
                            <option value="food">Food Category</option>
                            <option value="beer">Beer Category</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" rows="3"
                                  placeholder="Optional description for this category"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addCategoryBtn">
                    <i class="fas fa-plus me-1"></i>Add Category
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Food Modal -->
<div class="modal fade" id="editFoodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Food Item
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editFoodForm">
                    <input type="hidden" id="editFoodId" name="food_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFoodName" class="form-label">Item Name *</label>
                                <input type="text" class="form-control" id="editFoodName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFoodCategory" class="form-label">Category</label>
                                <input type="text" class="form-control" id="editFoodCategory" name="category">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editFoodDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editFoodDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editFoodPrice" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="editFoodPrice" name="price" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="editFoodAvailable" name="is_available">
                                    <label class="form-check-label" for="editFoodAvailable">
                                        Available for serving
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="updateFoodBtn" onclick="updateFood()">
                    <i class="fas fa-save me-1"></i>Update Food Item
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Beers Modal -->
<div class="modal fade" id="importBeersModal" tabindex="-1" aria-labelledby="importBeersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importBeersModalLabel">
                    <i class="fas fa-file-import me-2"></i>Import Beers
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Upload CSV File</h6>
                        <form id="importBeersForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="beerCsvFile" class="form-label">Choose CSV File</label>
                                <input type="file" class="form-control" id="beerCsvFile" name="csv_file" accept=".csv" required>
                                <div class="form-text">Upload a CSV file with your beer menu data</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="replaceExisting" name="replace_existing">
                                    <label class="form-check-label" for="replaceExisting">
                                        Replace existing beers
                                    </label>
                                    <div class="form-text">If checked, existing beers will be replaced. Otherwise, duplicates will be skipped.</div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <strong>CSV Format:</strong><br>
                                Name, Style, Description, ABV, IBU, Price, Available<br>
                                <small>Available should be "Yes" or "No"</small>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h6>Download Template</h6>
                        <p class="text-muted">Need a template? Download our sample CSV file to get started.</p>
                        <button type="button" class="btn btn-outline-primary mb-3" onclick="downloadTemplate('beer')">
                            <i class="fas fa-download me-2"></i>Download Beer Template
                        </button>

                        <h6>Import Instructions</h6>
                        <ol class="small text-muted">
                            <li>Download the template CSV file</li>
                            <li>Fill in your beer data</li>
                            <li>Save as CSV format</li>
                            <li>Upload the file using the form</li>
                            <li>Review and confirm the import</li>
                        </ol>
                    </div>
                </div>

                <!-- Import Preview (hidden initially) -->
                <div id="importPreview" style="display: none;">
                    <hr>
                    <h6>Import Preview</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Style</th>
                                    <th>ABV</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="previewTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="processImportBtn" onclick="processImport()" style="display: none;">
                    <i class="fas fa-upload me-1"></i>Import Beers
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if Bootstrap is available
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded!');
        return;
    }

    // Dark Mode Toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    const darkModeIcon = document.getElementById('darkModeIcon');
    const htmlElement = document.documentElement;

    // Check for saved dark mode preference or default to dark mode
    const savedTheme = localStorage.getItem('brewery-theme') || 'dark';
    htmlElement.setAttribute('data-bs-theme', savedTheme);
    updateDarkModeIcon(savedTheme);

    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            const currentTheme = htmlElement.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            htmlElement.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('brewery-theme', newTheme);
            updateDarkModeIcon(newTheme);
        });
    }

    function updateDarkModeIcon(theme) {
        if (darkModeIcon) {
            // Add a subtle rotation animation
            darkModeIcon.style.transform = 'rotate(180deg)';

            setTimeout(() => {
                if (theme === 'dark') {
                    darkModeIcon.className = 'fas fa-sun';
                    if (darkModeToggle) {
                        darkModeToggle.title = 'Switch to Light Mode';
                    }
                } else {
                    darkModeIcon.className = 'fas fa-moon';
                    if (darkModeToggle) {
                        darkModeToggle.title = 'Switch to Dark Mode';
                    }
                }
                darkModeIcon.style.transform = 'rotate(0deg)';
            }, 150);
        }
    }

    // Get modal elements
    const addBeerModalElement = document.getElementById('addBeerModal');
    const addFoodModalElement = document.getElementById('addFoodModal');
    const addCategoryModalElement = document.getElementById('addCategoryModal');

    // Get form elements
    const addBeerBtn = document.getElementById('addBeerBtn');
    const addBeerForm = document.getElementById('addBeerForm');
    const addFoodBtn = document.getElementById('addFoodBtn');
    const addFoodForm = document.getElementById('addFoodForm');
    const addCategoryBtn = document.getElementById('addCategoryBtn');
    const addCategoryForm = document.getElementById('addCategoryForm');

    // Initialize modals
    let addBeerModal, addFoodModal, addCategoryModal;

    if (addBeerModalElement) {
        addBeerModal = new bootstrap.Modal(addBeerModalElement);
    }

    if (addFoodModalElement) {
        addFoodModal = new bootstrap.Modal(addFoodModalElement);
    }

    if (addCategoryModalElement) {
        addCategoryModal = new bootstrap.Modal(addCategoryModalElement);
    }

    // Manual click handlers for quick action buttons (backup for Bootstrap data attributes)
    const quickAddBeerBtns = document.querySelectorAll('[data-bs-target="#addBeerModal"]');
    const quickAddFoodBtns = document.querySelectorAll('[data-bs-target="#addFoodModal"]');
    const quickAddCategoryBtns = document.querySelectorAll('[data-bs-target="#addCategoryModal"]');

    // Add click handlers for beer buttons
    quickAddBeerBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (addBeerModalElement) {
                const modal = bootstrap.Modal.getOrCreateInstance(addBeerModalElement);
                modal.show();
            }
        });
    });

    // Add click handlers for food buttons
    quickAddFoodBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (addFoodModalElement) {
                const modal = bootstrap.Modal.getOrCreateInstance(addFoodModalElement);
                modal.show();
            }
        });
    });

    // Add click handlers for category buttons
    quickAddCategoryBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (addCategoryModalElement) {
                const modal = bootstrap.Modal.getOrCreateInstance(addCategoryModalElement);
                modal.show();
            }
        });
    });

    // Initialize tabs (backup for Bootstrap data attributes)
    const tabElements = document.querySelectorAll('[data-bs-toggle="tab"]');

    tabElements.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const tabInstance = new bootstrap.Tab(this);
            tabInstance.show();
        });
    });

    // Restore active tab after page load
    const savedActiveTab = localStorage.getItem('brewery-active-tab');
    if (savedActiveTab) {
        const tabToActivate = document.querySelector(`[data-bs-target="${savedActiveTab}"]`);
        if (tabToActivate) {
            const tabInstance = new bootstrap.Tab(tabToActivate);
            tabInstance.show();
        }
        // Clear the saved tab so it doesn't interfere with normal navigation
        localStorage.removeItem('brewery-active-tab');
    }

    // Import Form Handler
    const importForm = document.getElementById('importForm');
    if (importForm) {
        importForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(importForm);
            const submitBtn = importForm.querySelector('button[type="submit"]');

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Importing...';
            submitBtn.disabled = true;

            fetch('menu.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    importForm.reset();
                    saveActiveTabAndReload(2000);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred during import. Please try again.', 'error');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Import CSV';
                submitBtn.disabled = false;
            });
        });
    }

    // Add Beer button now uses onclick handler

    // Add Food Item Handler
    if (addFoodBtn) {
        addFoodBtn.addEventListener('click', function() {
            const formData = new FormData();
            formData.append('action', 'add_food_item');
            formData.append('name', document.getElementById('foodName').value);
            formData.append('category', document.getElementById('foodCategory').value);
            formData.append('description', document.getElementById('foodDescription').value);
            formData.append('price', document.getElementById('foodPrice').value);
            formData.append('status', document.getElementById('foodStatus').value);

            // Validate required fields
            if (!formData.get('name') || !formData.get('category')) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Show loading state
            addFoodBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
            addFoodBtn.disabled = true;

            fetch('menu.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    addFoodModal.hide();
                    addFoodForm.reset();
                    saveActiveTabAndReload(1000);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred. Please try again.', 'error');
                console.error('Error:', error);
            })
            .finally(() => {
                addFoodBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Food Item';
                addFoodBtn.disabled = false;
            });
        });
    }

    // Add/Edit Category Handler
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const isEditMode = this.getAttribute('data-mode') === 'edit';
            const categoryId = this.getAttribute('data-category-id');

            const formData = new FormData();
            formData.append('action', isEditMode ? 'edit_category' : 'add_category');
            formData.append('name', document.getElementById('categoryName').value);
            formData.append('type', document.getElementById('categoryType').value);
            formData.append('description', document.getElementById('categoryDescription').value);

            if (isEditMode && categoryId) {
                formData.append('category_id', categoryId);
            }

            // Validate required fields
            if (!formData.get('name') || !formData.get('type')) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Show loading state
            const originalText = this.innerHTML;
            addCategoryBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>' + (isEditMode ? 'Updating...' : 'Adding...');
            addCategoryBtn.disabled = true;

            fetch('menu.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Server response:', data);

                if (data.success) {
                    showNotification(data.message, 'success');

                    if (data.debug) {
                        console.log('Debug info:', data.debug);
                    }

                    addCategoryModal.hide();
                    addCategoryForm.reset();

                    // Reset modal to add mode
                    document.querySelector('#addCategoryModal .modal-title').innerHTML = '<i class="fas fa-tags me-2"></i>Add Category';
                    addCategoryBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Category';
                    addCategoryBtn.removeAttribute('data-category-id');
                    addCategoryBtn.removeAttribute('data-mode');

                    // Reload immediately to check if data persists
                    saveActiveTabAndReload(100);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('An error occurred. Please try again.', 'error');
                console.error('Error:', error);
            })
            .finally(() => {
                const isEditMode = addCategoryBtn.getAttribute('data-mode') === 'edit';
                addCategoryBtn.innerHTML = '<i class="fas fa-' + (isEditMode ? 'save' : 'plus') + ' me-1"></i>' + (isEditMode ? 'Update Category' : 'Add Category');
                addCategoryBtn.disabled = false;
            });
        });
    }

    // Reset category modal when closed
    if (addCategoryModalElement) {
        addCategoryModalElement.addEventListener('hidden.bs.modal', function() {
            // Reset modal to add mode
            document.querySelector('#addCategoryModal .modal-title').innerHTML = '<i class="fas fa-tags me-2"></i>Add Category';
            if (addCategoryBtn) {
                addCategoryBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Category';
                addCategoryBtn.removeAttribute('data-category-id');
                addCategoryBtn.removeAttribute('data-mode');
            }
            if (addCategoryForm) {
                addCategoryForm.reset();
            }
        });
    }

    // Save active tab before reload
    function saveActiveTabAndReload(delay = 1000) {
        const activeTab = document.querySelector('.nav-tabs .nav-link.active');
        if (activeTab) {
            localStorage.setItem('brewery-active-tab', activeTab.getAttribute('data-bs-target'));
            // Add subtle visual feedback
            activeTab.style.transition = 'transform 0.2s ease';
            activeTab.style.transform = 'scale(1.02)';
            setTimeout(() => {
                if (activeTab) {
                    activeTab.style.transform = '';
                }
            }, 200);
        }
        setTimeout(() => location.reload(), delay);
    }

    // Notification function
    function showNotification(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
});

// Delete Food Item Function (global scope for onclick)
function deleteFoodItem(itemId) {
    if (!confirm('Are you sure you want to delete this food item?')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_food_item');
    formData.append('item_id', itemId);

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            saveActiveTabAndReload(1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('An error occurred. Please try again.', 'error');
        console.error('Error:', error);
    });
}

// Edit Category Function (global scope for onclick)
function editCategory(categoryId, name, type, description) {
    console.log('Edit category called with:', {categoryId, name, type, description});

    // Check if required elements exist
    const nameField = document.getElementById('categoryName');
    const typeField = document.getElementById('categoryType');
    const descField = document.getElementById('categoryDescription');
    const submitBtn = document.getElementById('addCategoryBtn');
    const modal = document.getElementById('addCategoryModal');

    if (!nameField || !typeField || !descField || !submitBtn || !modal) {
        console.error('Required form elements not found');
        showNotification('Error: Form elements not found', 'error');
        return;
    }

    // Populate the form with current values
    nameField.value = name || '';
    typeField.value = type || 'food';
    descField.value = description || '';

    // Change the modal title and button
    const titleElement = document.querySelector('#addCategoryModal .modal-title');
    if (titleElement) {
        titleElement.innerHTML = '<i class="fas fa-edit me-2"></i>Edit Category';
    }

    submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>Update Category';
    submitBtn.className = 'btn btn-warning'; // Change color for edit mode
    submitBtn.setAttribute('data-category-id', categoryId);
    submitBtn.setAttribute('data-mode', 'edit');

    // Show the modal
    try {
        const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);
        modalInstance.show();
        showNotification('Editing category: ' + name, 'info');
    } catch (error) {
        console.error('Error showing modal:', error);
        showNotification('Error opening edit modal', 'error');
    }
}

// Delete Category Function (global scope for onclick)
function deleteCategory(categoryId) {
    console.log('Delete category called with ID:', categoryId);

    if (!categoryId || categoryId === '' || categoryId === '0') {
        showNotification('Error: Invalid category ID', 'error');
        return;
    }

    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_category');
    formData.append('category_id', categoryId);

    // Show loading state
    showNotification('Deleting category...', 'info');

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('Delete response:', data);
        if (data.success) {
            showNotification(data.message || 'Category deleted successfully!', 'success');
            saveActiveTabAndReload(1500);
        } else {
            showNotification(data.message || 'Failed to delete category', 'error');
        }
    })
    .catch(error => {
        showNotification('An error occurred. Please try again.', 'error');
        console.error('Error:', error);
    });
}

// Export CSV Function (global scope for onclick)
function exportCSV(type) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'menu.php';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'export_csv';

    const typeInput = document.createElement('input');
    typeInput.type = 'hidden';
    typeInput.name = 'export_type';
    typeInput.value = type;

    form.appendChild(actionInput);
    form.appendChild(typeInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    showNotification(`Exporting ${type} menu...`, 'success');
}

// Download Template Function (global scope for onclick)
function downloadTemplate(type) {
    let csvContent = '';
    let filename = '';

    if (type === 'food') {
        csvContent = 'Name,Category,Description,Price,Status\n';
        csvContent += 'Buffalo Wings,Appetizers,Crispy wings with buffalo sauce,12.99,available\n';
        csvContent += 'Caesar Salad,Salads,Fresh romaine with caesar dressing,9.99,available\n';
        csvContent += 'Grilled Salmon,Entrees,Atlantic salmon with lemon herb butter,24.99,available\n';
        csvContent += 'Chocolate Cake,Desserts,Rich chocolate cake with vanilla ice cream,7.99,available\n';
        filename = 'food_menu_template.csv';
    } else {
        csvContent = 'Name,Style,Description,ABV,IBU,Price,Available\n';
        csvContent += 'House IPA,IPA,Hoppy and citrusy with tropical notes,6.5,65,7.99,Yes\n';
        csvContent += 'Wheat Beer,Wheat,Light and refreshing wheat beer,4.8,15,6.99,Yes\n';
        csvContent += 'Porter,Porter,Rich and dark with chocolate notes,5.2,35,8.99,Yes\n';
        csvContent += 'Lager,Lager,Crisp and clean traditional lager,4.5,20,6.99,Yes\n';
        filename = 'beer_menu_template.csv';
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showNotification(`Downloaded ${type} menu template`, 'success');
}

// Switch to Categories tab
function switchToCategories() {
    // Find and click the Categories tab
    const categoriesTab = document.querySelector('button[data-bs-target="#categories"]');
    if (categoriesTab) {
        categoriesTab.click();
        showNotification('Switched to Categories tab', 'info');
    }
}

// Handle CSV file selection for import
document.getElementById('beerCsvFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        previewCsvFile(file);
    }
});

// Preview CSV file contents
function previewCsvFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',');

        // Clear previous preview
        const previewBody = document.getElementById('previewTableBody');
        previewBody.innerHTML = '';

        // Show preview section
        document.getElementById('importPreview').style.display = 'block';
        document.getElementById('processImportBtn').style.display = 'inline-block';

        // Process first 5 data rows for preview
        for (let i = 1; i < Math.min(6, lines.length); i++) {
            if (lines[i].trim()) {
                const data = lines[i].split(',');
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${data[0] || ''}</td>
                    <td>${data[1] || ''}</td>
                    <td>${data[3] || ''}%</td>
                    <td>$${data[5] || ''}</td>
                    <td><span class="badge bg-${data[6] === 'Yes' ? 'success' : 'warning'}">${data[6] || 'Unknown'}</span></td>
                `;
                previewBody.appendChild(row);
            }
        }

        if (lines.length > 6) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="5" class="text-muted text-center">... and ${lines.length - 6} more rows</td>`;
            previewBody.appendChild(row);
        }
    };
    reader.readAsText(file);
}

// Process the import
function processImport() {
    const form = document.getElementById('importBeersForm');
    const formData = new FormData(form);
    formData.append('action', 'import_beers');

    // Show loading state
    const btn = document.getElementById('processImportBtn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Importing...';
    btn.disabled = true;

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(`Successfully imported ${result.count || 0} beers!`, 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('importBeersModal'));
            modal.hide();
            // Reload page to show new beers
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Import failed: ' + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        showNotification('Import failed. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Beer management functions
function editBeer(beerId) {
    // Fetch beer data and populate edit modal
    fetch('menu.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_beer&beer_id=${beerId}`
    })
    .then(response => response.json())
    .then(result => {
        if (result.success && result.beer) {
            const beer = result.beer;

            // Populate form fields
            document.getElementById('editBeerId').value = beer.id;
            document.getElementById('editBeerName').value = beer.name || '';

            // Handle style selection - check if it exists in dropdown
            const styleSelect = document.getElementById('editBeerStyle');
            const styleCustom = document.getElementById('editBeerStyleCustom');
            const beerStyle = beer.style || '';

            // Check if the beer's style exists in the dropdown
            let styleFound = false;
            for (let option of styleSelect.options) {
                if (option.value === beerStyle) {
                    styleFound = true;
                    break;
                }
            }

            if (styleFound) {
                styleSelect.value = beerStyle;
                styleCustom.style.display = 'none';
                styleCustom.required = false;
            } else if (beerStyle) {
                // Style not found in dropdown, use custom
                styleSelect.value = 'custom';
                styleCustom.value = beerStyle;
                styleCustom.style.display = 'block';
                styleCustom.required = true;
            } else {
                styleSelect.value = '';
                styleCustom.style.display = 'none';
                styleCustom.required = false;
            }

            document.getElementById('editBeerDescription').value = beer.description || '';
            document.getElementById('editBeerABV').value = beer.abv || '';
            document.getElementById('editBeerIBU').value = beer.ibu || '';
            document.getElementById('editBeerPrice').value = beer.price || '';
            document.getElementById('editBeerTapNumber').value = beer.tap_number || '';
            document.getElementById('editBeerCategory').value = beer.category || '';
            document.getElementById('editBeerAvailable').checked = beer.is_available == 1;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editBeerModal'));
            modal.show();
        } else {
            showNotification('Error loading beer data: ' + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error loading beer data. Please try again.', 'error');
    });
}

function duplicateBeer(beerId) {
    if (confirm('Are you sure you want to duplicate this beer?')) {
        fetch('menu.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=duplicate_beer&beer_id=${beerId}`
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification('Beer duplicated successfully!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification('Error duplicating beer: ' + (result.message || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error duplicating beer. Please try again.', 'error');
        });
    }
}

function toggleBeerAvailability(beerId, newStatus) {
    const action = newStatus ? 'show' : 'hide';

    fetch('menu.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=toggle_beer_availability&beer_id=${beerId}&available=${newStatus}`
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(`Beer ${action}d successfully!`, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(`Error ${action}ing beer: ` + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification(`Error ${action}ing beer. Please try again.`, 'error');
    });
}

function deleteBeer(beerId) {
    if (confirm('Are you sure you want to delete this beer? This action cannot be undone.')) {
        fetch('menu.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete_beer&beer_id=${beerId}`
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification('Beer deleted successfully!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification('Error deleting beer: ' + (result.message || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting beer. Please try again.', 'error');
        });
    }
}

// Reset category modal when closed
document.addEventListener('DOMContentLoaded', function() {
    const categoryModal = document.getElementById('addCategoryModal');
    if (categoryModal) {
        categoryModal.addEventListener('hidden.bs.modal', function() {
            // Reset modal to add mode
            const titleElement = document.querySelector('#addCategoryModal .modal-title');
            const submitBtn = document.getElementById('addCategoryBtn');

            if (titleElement) {
                titleElement.innerHTML = '<i class="fas fa-plus me-2"></i>Add Category';
            }

            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-plus me-1"></i>Add Category';
                submitBtn.className = 'btn btn-primary'; // Reset to primary color
                submitBtn.removeAttribute('data-category-id');
                submitBtn.removeAttribute('data-mode');
            }

            // Clear form
            const form = document.getElementById('addCategoryForm');
            if (form) {
                form.reset();
            }
        });
    }

    // Reset edit food modal when closed
    const editFoodModal = document.getElementById('editFoodModal');
    if (editFoodModal) {
        editFoodModal.addEventListener('hidden.bs.modal', function() {
            // Clear form
            const form = document.getElementById('editFoodForm');
            if (form) {
                form.reset();
            }
        });
    }
});

// Global Add Beer function
function addBeer() {
    const form = document.getElementById('addBeerForm');
    if (!form) {
        showNotification('Error: Form not found', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'add_beer');
    formData.append('name', document.getElementById('beerName').value);

    // Handle style selection (regular or custom)
    const styleSelect = document.getElementById('beerStyle');
    const styleCustom = document.getElementById('beerStyleCustom');
    if (styleSelect && styleCustom && styleSelect.value === 'custom' && styleCustom.value.trim()) {
        formData.append('style', styleCustom.value.trim());
    } else if (styleSelect) {
        formData.append('style', styleSelect.value);
    }

    formData.append('description', document.getElementById('beerDescription').value);
    formData.append('abv', document.getElementById('beerABV').value);
    formData.append('ibu', document.getElementById('beerIBU').value);
    formData.append('price', document.getElementById('beerPrice').value);
    formData.append('is_available', document.getElementById('beerStatus').value === 'available' ? 1 : 0);

    // Show loading state
    const addBeerBtn = document.getElementById('addBeerBtn');
    const originalText = addBeerBtn.innerHTML;
    addBeerBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
    addBeerBtn.disabled = true;

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('Beer added successfully!', 'success');
            // Reset form
            form.reset();
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addBeerModal'));
            modal.hide();
            // Reload page to show new beer
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error adding beer: ' + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error adding beer. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button
        addBeerBtn.innerHTML = originalText;
        addBeerBtn.disabled = false;
    });
}

// Global Edit Food function
function editFood(foodId, name, category, description, price, isAvailable) {
    console.log('Edit food called with:', {foodId, name, category, description, price, isAvailable});

    // Check if required elements exist
    const nameField = document.getElementById('editFoodName');
    const categoryField = document.getElementById('editFoodCategory');
    const descField = document.getElementById('editFoodDescription');
    const priceField = document.getElementById('editFoodPrice');
    const availableField = document.getElementById('editFoodAvailable');
    const idField = document.getElementById('editFoodId');
    const modal = document.getElementById('editFoodModal');

    if (!nameField || !categoryField || !descField || !priceField || !availableField || !idField || !modal) {
        console.error('Required form elements not found');
        showNotification('Error: Form elements not found', 'error');
        return;
    }

    // Populate the form with current values
    idField.value = foodId || '';
    nameField.value = name || '';
    categoryField.value = category || '';
    descField.value = description || '';
    priceField.value = price || '';
    availableField.checked = isAvailable == 1;

    // Show the modal
    try {
        const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);
        modalInstance.show();
        showNotification('Editing food item: ' + name, 'info');
    } catch (error) {
        console.error('Error showing modal:', error);
        showNotification('Error opening edit modal', 'error');
    }
}

// Global Update Food function
function updateFood() {
    const form = document.getElementById('editFoodForm');
    if (!form) {
        showNotification('Error: Form not found', 'error');
        return;
    }

    const formData = new FormData(form);
    formData.append('action', 'update_food');

    // Show loading state
    const updateFoodBtn = document.getElementById('updateFoodBtn');
    const originalText = updateFoodBtn.innerHTML;
    updateFoodBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    updateFoodBtn.disabled = true;

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('Food item updated successfully!', 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editFoodModal'));
            modal.hide();
            // Reload page to show changes
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error updating food item: ' + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating food item. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button
        updateFoodBtn.innerHTML = originalText;
        updateFoodBtn.disabled = false;
    });
}

// Global Update Beer function
function updateBeer() {
    const form = document.getElementById('editBeerForm');
    if (!form) {
        showNotification('Error: Form not found', 'error');
        return;
    }

    const formData = new FormData(form);
    formData.append('action', 'update_beer');

    // Handle style selection (regular or custom)
    const styleSelect = document.getElementById('editBeerStyle');
    const styleCustom = document.getElementById('editBeerStyleCustom');
    if (styleSelect && styleCustom && styleSelect.value === 'custom' && styleCustom.value.trim()) {
        formData.set('style', styleCustom.value.trim());
    }

    // Show loading state
    const updateBeerBtn = document.getElementById('updateBeerBtn');
    const originalText = updateBeerBtn.innerHTML;
    updateBeerBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Updating...';
    updateBeerBtn.disabled = true;

    fetch('menu.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('Beer updated successfully!', 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editBeerModal'));
            modal.hide();
            // Reload page to show changes
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error updating beer: ' + (result.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating beer. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button
        updateBeerBtn.innerHTML = originalText;
        updateBeerBtn.disabled = false;
    });
}

// Handle custom beer style selection
document.addEventListener('DOMContentLoaded', function() {
    // Add Beer Modal - Custom Style Handler
    const beerStyleSelect = document.getElementById('beerStyle');
    const beerStyleCustom = document.getElementById('beerStyleCustom');

    if (beerStyleSelect && beerStyleCustom) {
        beerStyleSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                beerStyleCustom.style.display = 'block';
                beerStyleCustom.required = true;
                beerStyleCustom.focus();
            } else {
                beerStyleCustom.style.display = 'none';
                beerStyleCustom.required = false;
                beerStyleCustom.value = '';
            }
        });
    }

    // Edit Beer Modal - Custom Style Handler
    const editBeerStyleSelect = document.getElementById('editBeerStyle');
    const editBeerStyleCustom = document.getElementById('editBeerStyleCustom');

    if (editBeerStyleSelect && editBeerStyleCustom) {
        editBeerStyleSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                editBeerStyleCustom.style.display = 'block';
                editBeerStyleCustom.required = true;
                editBeerStyleCustom.focus();
            } else {
                editBeerStyleCustom.style.display = 'none';
                editBeerStyleCustom.required = false;
                editBeerStyleCustom.value = '';
            }
        });
    }
});
</script>

<?php include '../includes/footer.php'; ?>
