<?php
require_once '../../config/config.php';
requireRole('brewery');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    http_response_code(400);
    echo json_encode(['error' => 'No brewery associated with your account']);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get and validate input
    $name = sanitizeInput($_POST['name'] ?? '');
    $styleId = sanitizeInput($_POST['style_id'] ?? '');
    $description = sanitizeInput($_POST['description'] ?? '');
    $abv = !empty($_POST['abv']) ? floatval($_POST['abv']) : null;
    $ibu = !empty($_POST['ibu']) ? intval($_POST['ibu']) : null;
    $price = !empty($_POST['price']) ? floatval($_POST['price']) : null;
    $tapNumber = !empty($_POST['tap_number']) ? intval($_POST['tap_number']) : null;
    $available = isset($_POST['available']) ? (bool)$_POST['available'] : true;
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Beer name is required';
    }
    
    if (!empty($styleId)) {
        // Verify style exists
        $stmt = $conn->prepare("SELECT id FROM beer_styles WHERE id = ? AND is_active = 1");
        $stmt->execute([$styleId]);
        if (!$stmt->fetch()) {
            $errors[] = 'Invalid beer style selected';
        }
    }
    
    if ($abv !== null && ($abv < 0 || $abv > 20)) {
        $errors[] = 'ABV must be between 0 and 20';
    }
    
    if ($ibu !== null && ($ibu < 0 || $ibu > 120)) {
        $errors[] = 'IBU must be between 0 and 120';
    }
    
    if ($price !== null && $price < 0) {
        $errors[] = 'Price cannot be negative';
    }
    
    if ($tapNumber !== null) {
        // Check if tap number is already in use
        $stmt = $conn->prepare("SELECT id FROM beer_menu WHERE brewery_id = ? AND tap_number = ? AND available = 1");
        $stmt->execute([$breweryId, $tapNumber]);
        if ($stmt->fetch()) {
            $errors[] = 'Tap number ' . $tapNumber . ' is already in use';
        }
    }
    
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode(['error' => implode(', ', $errors)]);
        exit;
    }
    
    // Insert new beer
    $stmt = $conn->prepare("
        INSERT INTO beer_menu (
            id, brewery_id, beer_style_id, name, description, 
            abv, ibu, price, tap_number, available, created_at
        ) VALUES (
            UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
        )
    ");
    
    $stmt->execute([
        $breweryId,
        $styleId ?: null,
        $name,
        $description ?: null,
        $abv,
        $ibu,
        $price,
        $tapNumber,
        $available
    ]);
    
    $beerId = $conn->lastInsertId();
    
    // Get the created beer with style info
    $stmt = $conn->prepare("
        SELECT bm.*, bs.name as style_name, bs.category as style_category 
        FROM beer_menu bm 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE bm.id = ?
    ");
    $stmt->execute([$beerId]);
    $beer = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'message' => 'Beer added successfully',
        'beer' => $beer
    ]);
    
} catch (Exception $e) {
    error_log("Error adding beer: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Failed to add beer. Please try again.']);
}
?>
