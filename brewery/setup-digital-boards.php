<?php
/**
 * Setup Digital Boards Database Table
 * Creates the digital_boards table if it doesn't exist
 */

require_once '../config/config.php';
requireRole('brewery');

$success = false;
$message = '';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'digital_boards'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Create digital_boards table
        $sql = "
        CREATE TABLE digital_boards (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            brewery_id VARCHAR(36) NOT NULL,
            board_id VARCHAR(100) NOT NULL UNIQUE,
            name VARCHAR(255) NOT NULL DEFAULT 'Digital Board',
            description TEXT NULL,
            
            -- Display Settings
            settings JSON NOT NULL DEFAULT '{}',
            template_id VARCHAR(36) NULL,
            current_slideshow_id VARCHAR(36) NULL,
            
            -- Board Configuration
            display_mode ENUM('static', 'slideshow', 'hybrid') DEFAULT 'static',
            refresh_interval INT DEFAULT 300,
            auto_refresh BOOLEAN DEFAULT TRUE,
            fullscreen_mode BOOLEAN DEFAULT FALSE,
            
            -- Theme and Layout
            theme VARCHAR(50) DEFAULT 'beersty-professional',
            layout ENUM('grid', 'list', 'cards') DEFAULT 'grid',
            columns_count INT DEFAULT 3,
            
            -- Display Options
            show_prices BOOLEAN DEFAULT TRUE,
            show_descriptions BOOLEAN DEFAULT TRUE,
            show_abv BOOLEAN DEFAULT TRUE,
            show_ibu BOOLEAN DEFAULT FALSE,
            show_tap_numbers BOOLEAN DEFAULT FALSE,
            show_availability BOOLEAN DEFAULT TRUE,
            
            -- Header/Footer Options
            show_header BOOLEAN DEFAULT TRUE,
            show_footer BOOLEAN DEFAULT TRUE,
            show_ticker BOOLEAN DEFAULT FALSE,
            ticker_message TEXT NULL,
            ticker_speed INT DEFAULT 50,
            ticker_enabled BOOLEAN DEFAULT FALSE,
            
            -- Styling
            background_color VARCHAR(7) DEFAULT '#1a1a1a',
            background_image VARCHAR(255) NULL,
            
            -- Access Control
            is_public BOOLEAN DEFAULT FALSE,
            access_code VARCHAR(50) NULL,
            
            -- Status
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_brewery_id (brewery_id),
            INDEX idx_board_id (board_id),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $conn->exec($sql);
        $success = true;
        $message = 'Digital boards table created successfully!';
    } else {
        $success = true;
        $message = 'Digital boards table already exists.';
    }
    
    // Also check/create beer_menu table for beer count stats
    $stmt = $conn->prepare("SHOW TABLES LIKE 'beer_menu'");
    $stmt->execute();
    $beerTableExists = $stmt->rowCount() > 0;
    
    if (!$beerTableExists) {
        $sql = "
        CREATE TABLE beer_menu (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            brewery_id VARCHAR(36) NOT NULL,
            name VARCHAR(255) NOT NULL,
            style VARCHAR(100) NULL,
            description TEXT NULL,
            abv DECIMAL(4,2) NULL,
            ibu INT NULL,
            price DECIMAL(6,2) NULL,
            tap_number INT NULL,
            is_available BOOLEAN DEFAULT TRUE,
            is_featured BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_brewery_id (brewery_id),
            INDEX idx_is_available (is_available),
            INDEX idx_tap_number (tap_number)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $conn->exec($sql);
        $message .= ' Beer menu table also created.';
    }
    
} catch (Exception $e) {
    $success = false;
    $message = 'Error setting up database: ' . $e->getMessage();
    error_log("Digital boards setup error: " . $e->getMessage());
}

// Return JSON if requested via AJAX
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => $success, 'message' => $message]);
    exit;
}

// Redirect back to digital board admin
$_SESSION[$success ? 'success_message' : 'error_message'] = $message;
header('Location: digital-board.php');
exit;
?>
