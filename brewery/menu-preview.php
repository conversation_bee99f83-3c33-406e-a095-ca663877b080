<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Menu Preview - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/menu-preview.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

// Get beer menu data
$beerMenu = [];
try {
    $stmt = $conn->prepare("
        SELECT bm.*, bs.name as style_name, bs.category as style_category 
        FROM beer_menu bm 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE bm.brewery_id = ? AND bm.available = 1
        ORDER BY bm.tap_number ASC, bm.name ASC
    ");
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching beer menu: " . $e->getMessage());
}

// Get food menu data
$foodMenu = [];
try {
    $stmt = $conn->prepare("
        SELECT fm.*, fc.name as category_name 
        FROM food_menu fm 
        LEFT JOIN food_categories fc ON fm.category_id = fc.id 
        WHERE fm.brewery_id = ? AND fm.available = 1
        ORDER BY fc.sort_order ASC, fm.name ASC
    ");
    $stmt->execute([$breweryId]);
    $foodMenu = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching food menu: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-eye me-2 text-primary"></i>Menu Preview
                    </h1>
                    <p class="text-muted mb-0">How your menu appears to customers</p>
                </div>
                <div>
                    <a href="menu.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Management
                    </a>
                    <a href="../places/profile/<?php echo $breweryId; ?>/" class="btn btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>View Public Page
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Brewery Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="brewery-header-preview">
                <div class="brewery-info">
                    <h2 class="brewery-name"><?php echo htmlspecialchars($brewery['name']); ?></h2>
                    <p class="brewery-description"><?php echo htmlspecialchars($brewery['description'] ?? 'Welcome to our brewery!'); ?></p>
                    <div class="brewery-meta">
                        <span class="brewery-type badge bg-primary"><?php echo ucfirst($brewery['brewery_type']); ?></span>
                        <?php if ($brewery['city'] && $brewery['state']): ?>
                            <span class="brewery-location">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($brewery['city'] . ', ' . $brewery['state']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs mb-4" id="previewTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="beer-preview-tab" data-bs-toggle="tab" data-bs-target="#beer-preview" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beer Menu
                        <span class="badge bg-primary ms-2"><?php echo count($beerMenu); ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="food-preview-tab" data-bs-toggle="tab" data-bs-target="#food-preview" type="button" role="tab">
                        <i class="fas fa-utensils me-2"></i>Food Menu
                        <span class="badge bg-success ms-2"><?php echo count($foodMenu); ?></span>
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="previewTabContent">
        <!-- Beer Menu Preview -->
        <div class="tab-pane fade show active" id="beer-preview" role="tabpanel">
            <div class="menu-section">
                <h3 class="section-title">
                    <i class="fas fa-beer me-2"></i>Our Beers
                </h3>
                
                <?php if (empty($beerMenu)): ?>
                    <div class="empty-menu">
                        <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                        <h5>No Beers Available</h5>
                        <p class="text-muted">Check back soon for our beer selection!</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($beerMenu as $beer): ?>
                            <div class="col-lg-6 mb-4">
                                <div class="menu-item-preview">
                                    <div class="item-header">
                                        <div class="item-name-price">
                                            <h5 class="item-name"><?php echo htmlspecialchars($beer['name']); ?></h5>
                                            <?php if ($beer['price']): ?>
                                                <span class="item-price">$<?php echo number_format($beer['price'], 2); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="item-badges">
                                            <?php if ($beer['style_category']): ?>
                                                <span class="badge bg-warning"><?php echo htmlspecialchars($beer['style_category']); ?></span>
                                            <?php endif; ?>
                                            <?php if ($beer['tap_number']): ?>
                                                <span class="badge bg-info">Tap <?php echo $beer['tap_number']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($beer['description']): ?>
                                        <p class="item-description"><?php echo htmlspecialchars($beer['description']); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="item-details">
                                        <?php if ($beer['abv']): ?>
                                            <span class="detail-item">
                                                <strong>ABV:</strong> <?php echo $beer['abv']; ?>%
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($beer['ibu']): ?>
                                            <span class="detail-item">
                                                <strong>IBU:</strong> <?php echo $beer['ibu']; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Food Menu Preview -->
        <div class="tab-pane fade" id="food-preview" role="tabpanel">
            <div class="menu-section">
                <h3 class="section-title">
                    <i class="fas fa-utensils me-2"></i>Food Menu
                </h3>
                
                <?php if (empty($foodMenu)): ?>
                    <div class="empty-menu">
                        <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                        <h5>No Food Items Available</h5>
                        <p class="text-muted">Check back soon for our food menu!</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php 
                        $currentCategory = '';
                        foreach ($foodMenu as $food): 
                            if ($food['category_name'] !== $currentCategory):
                                if ($currentCategory !== '') echo '</div>';
                                $currentCategory = $food['category_name'];
                                echo '<div class="col-12"><h4 class="category-title">' . htmlspecialchars($currentCategory ?: 'Other') . '</h4></div>';
                                echo '<div class="row">';
                            endif;
                        ?>
                            <div class="col-lg-6 mb-3">
                                <div class="menu-item-preview">
                                    <div class="item-header">
                                        <div class="item-name-price">
                                            <h5 class="item-name"><?php echo htmlspecialchars($food['name']); ?></h5>
                                            <?php if ($food['price']): ?>
                                                <span class="item-price">$<?php echo number_format($food['price'], 2); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($food['description']): ?>
                                        <p class="item-description"><?php echo htmlspecialchars($food['description']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <?php if (!empty($foodMenu)) echo '</div>'; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
