<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Display Settings - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/digital-board.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-cog me-2 text-primary"></i>Display Settings
                    </h1>
                    <p class="text-muted mb-0">Configure your digital board display preferences</p>
                </div>
                <div>
                    <a href="digital-board.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Digital Board
                    </a>
                    <button class="btn btn-primary" id="saveSettings">
                        <i class="fas fa-save me-1"></i>Save Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Display Configuration -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-desktop me-2"></i>Display Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form id="displaySettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="refreshInterval" class="form-label">Auto-Refresh Interval</label>
                                    <select class="form-select" id="refreshInterval">
                                        <option value="15">15 seconds</option>
                                        <option value="30" selected>30 seconds</option>
                                        <option value="60">1 minute</option>
                                        <option value="120">2 minutes</option>
                                        <option value="300">5 minutes</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="displayOrientation" class="form-label">Display Orientation</label>
                                    <select class="form-select" id="displayOrientation">
                                        <option value="landscape" selected>Landscape</option>
                                        <option value="portrait">Portrait</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fontSize" class="form-label">Font Size</label>
                                    <select class="form-select" id="fontSize">
                                        <option value="small">Small</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="large">Large</option>
                                        <option value="extra-large">Extra Large</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="animationSpeed" class="form-label">Animation Speed</label>
                                    <select class="form-select" id="animationSpeed">
                                        <option value="slow">Slow</option>
                                        <option value="normal" selected>Normal</option>
                                        <option value="fast">Fast</option>
                                        <option value="none">No Animation</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrices" checked>
                                <label class="form-check-label" for="showPrices">
                                    Show Prices
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showABV" checked>
                                <label class="form-check-label" for="showABV">
                                    Show ABV Information
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showAvailability" checked>
                                <label class="form-check-label" for="showAvailability">
                                    Show Availability Status
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Theme Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-palette me-2"></i>Theme Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="primaryColor" class="form-label">Primary Color</label>
                                <input type="color" class="form-control form-control-color" id="primaryColor" value="#FFC107">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="backgroundColor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="backgroundColor" value="#2D2D2D">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="textColor" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="textColor" value="#FFFFFF">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accentColor" class="form-label">Accent Color</label>
                                <input type="color" class="form-control form-control-color" id="accentColor" value="#D69A6B">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>Advanced Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="customCSS" class="form-label">Custom CSS</label>
                        <textarea class="form-control" id="customCSS" rows="6" placeholder="/* Add your custom CSS here */"></textarea>
                        <div class="form-text">Advanced users can add custom CSS to further customize the display appearance.</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableKiosk">
                            <label class="form-check-label" for="enableKiosk">
                                Enable Kiosk Mode
                            </label>
                        </div>
                        <div class="form-text">Kiosk mode prevents user interaction and hides browser controls.</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableScheduling">
                            <label class="form-check-label" for="enableScheduling">
                                Enable Scheduled Updates
                            </label>
                        </div>
                        <div class="form-text">Automatically update content based on time of day or day of week.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 2rem;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Live Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div class="preview-container">
                        <div class="preview-screen" id="livePreview">
                            <div class="preview-header">🍺 NOW POURING</div>
                            <div class="preview-content">
                                <div class="preview-item">
                                    <span class="beer-name">Hoppy IPA</span>
                                    <span class="beer-price">$7.00</span>
                                </div>
                                <div class="preview-item">
                                    <span class="beer-name">Smooth Lager</span>
                                    <span class="beer-price">$6.00</span>
                                </div>
                                <div class="preview-item">
                                    <span class="beer-name">Dark Stout</span>
                                    <span class="beer-price">$8.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-external-link-alt me-1"></i>Full Screen Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.preview-screen {
    background: #2D2D2D;
    color: #FFFFFF;
    padding: 1rem;
    border-radius: 6px;
    min-height: 200px;
    font-family: 'Arial', sans-serif;
}

.preview-header {
    text-align: center;
    color: #FFC107;
    font-weight: bold;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.beer-name {
    font-size: 0.9rem;
}

.beer-price {
    font-weight: bold;
    color: #FFC107;
    font-size: 0.9rem;
}

.form-control-color {
    width: 100%;
    height: 40px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview when settings change
    const settingsInputs = document.querySelectorAll('#displaySettingsForm input, #displaySettingsForm select, input[type="color"]');
    
    settingsInputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    function updatePreview() {
        const preview = document.getElementById('livePreview');
        const primaryColor = document.getElementById('primaryColor').value;
        const backgroundColor = document.getElementById('backgroundColor').value;
        const textColor = document.getElementById('textColor').value;
        const fontSize = document.getElementById('fontSize').value;
        
        // Update preview styles
        preview.style.backgroundColor = backgroundColor;
        preview.style.color = textColor;
        
        const header = preview.querySelector('.preview-header');
        const prices = preview.querySelectorAll('.beer-price');
        
        header.style.color = primaryColor;
        prices.forEach(price => {
            price.style.color = primaryColor;
        });
        
        // Update font size
        let fontSizeValue = '0.9rem';
        switch(fontSize) {
            case 'small': fontSizeValue = '0.8rem'; break;
            case 'medium': fontSizeValue = '0.9rem'; break;
            case 'large': fontSizeValue = '1.1rem'; break;
            case 'extra-large': fontSizeValue = '1.3rem'; break;
        }
        
        preview.querySelectorAll('.preview-item').forEach(item => {
            item.style.fontSize = fontSizeValue;
        });
    }
    
    // Save settings
    document.getElementById('saveSettings').addEventListener('click', function() {
        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>Display settings saved successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
