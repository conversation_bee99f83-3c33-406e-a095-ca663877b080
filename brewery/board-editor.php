<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Board Editor - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/digital-board.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

// Get board name from URL parameter
$boardName = $_GET['board'] ?? 'Main Taproom Board';

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

// Get beer menu for the board
$beerMenu = [];
try {
    $stmt = $conn->prepare("
        SELECT bm.*, bs.name as style_name, bs.category as style_category 
        FROM beer_menu bm 
        LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id 
        WHERE bm.brewery_id = ? AND bm.available = 1
        ORDER BY bm.tap_number ASC, bm.name ASC
    ");
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching beer menu: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-edit me-2 text-primary"></i>Edit Board: <?php echo htmlspecialchars($boardName); ?>
                    </h1>
                    <p class="text-muted mb-0">Customize your digital board content and layout</p>
                </div>
                <div>
                    <a href="digital-board.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Digital Board
                    </a>
                    <button class="btn btn-success me-2" id="saveBoard">
                        <i class="fas fa-save me-1"></i>Save Changes
                    </button>
                    <button class="btn btn-primary" id="previewBoard">
                        <i class="fas fa-eye me-1"></i>Preview
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Editor Panel -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-beer me-2"></i>Beer Selection
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Select which beers to display on this board:</p>
                    
                    <?php if (empty($beerMenu)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                            <h5>No Beers Available</h5>
                            <p class="text-muted">Add beers to your menu first to display them on digital boards.</p>
                            <a href="menu.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Beers to Menu
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($beerMenu as $beer): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card beer-selection-card">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="beer_<?php echo $beer['id']; ?>" checked>
                                                <label class="form-check-label w-100" for="beer_<?php echo $beer['id']; ?>">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h6 class="mb-1"><?php echo htmlspecialchars($beer['name']); ?></h6>
                                                            <small class="text-muted">
                                                                <?php if ($beer['style_category']): ?>
                                                                    <?php echo htmlspecialchars($beer['style_category']); ?>
                                                                <?php endif; ?>
                                                                <?php if ($beer['abv']): ?>
                                                                    • <?php echo $beer['abv']; ?>% ABV
                                                                <?php endif; ?>
                                                                <?php if ($beer['tap_number']): ?>
                                                                    • Tap <?php echo $beer['tap_number']; ?>
                                                                <?php endif; ?>
                                                            </small>
                                                        </div>
                                                        <div class="text-end">
                                                            <?php if ($beer['price']): ?>
                                                                <span class="fw-bold text-primary">$<?php echo number_format($beer['price'], 2); ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Board Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Board Settings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boardTitle" class="form-label">Board Title</label>
                                <input type="text" class="form-control" id="boardTitle" value="🍺 NOW POURING">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="refreshRate" class="form-label">Refresh Rate</label>
                                <select class="form-select" id="refreshRate">
                                    <option value="15">15 seconds</option>
                                    <option value="30" selected>30 seconds</option>
                                    <option value="60">1 minute</option>
                                    <option value="120">2 minutes</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template" class="form-label">Template</label>
                                <select class="form-select" id="template">
                                    <option value="modern-dark" selected>Modern Dark</option>
                                    <option value="bright-summer">Bright Summer</option>
                                    <option value="classic-wood">Classic Wood</option>
                                    <option value="event-slideshow">Event Slideshow</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sortOrder" class="form-label">Sort Order</label>
                                <select class="form-select" id="sortOrder">
                                    <option value="tap_number" selected>By Tap Number</option>
                                    <option value="name">By Name</option>
                                    <option value="price">By Price</option>
                                    <option value="abv">By ABV</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrices" checked>
                                <label class="form-check-label" for="showPrices">
                                    Show Prices
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showABV" checked>
                                <label class="form-check-label" for="showABV">
                                    Show ABV
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showTapNumbers" checked>
                                <label class="form-check-label" for="showTapNumbers">
                                    Show Tap Numbers
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Preview -->
        <div class="col-lg-4">
            <div class="card sticky-top" style="top: 2rem;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Live Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div class="board-preview-container">
                        <div class="board-preview" id="boardPreview">
                            <div class="preview-header">🍺 NOW POURING</div>
                            <div class="preview-content">
                                <?php foreach (array_slice($beerMenu, 0, 6) as $beer): ?>
                                    <div class="preview-beer-item" data-beer-id="<?php echo $beer['id']; ?>">
                                        <div class="beer-info">
                                            <span class="beer-name"><?php echo htmlspecialchars($beer['name']); ?></span>
                                            <?php if ($beer['tap_number']): ?>
                                                <span class="tap-number">Tap <?php echo $beer['tap_number']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="beer-details">
                                            <?php if ($beer['abv']): ?>
                                                <span class="abv"><?php echo $beer['abv']; ?>%</span>
                                            <?php endif; ?>
                                            <?php if ($beer['price']): ?>
                                                <span class="price">$<?php echo number_format($beer['price'], 2); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-outline-primary btn-sm w-100" id="fullscreenPreview">
                            <i class="fas fa-expand me-1"></i>Fullscreen Preview
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.beer-selection-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.beer-selection-card:hover {
    border-color: var(--brand-primary);
}

.board-preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.board-preview {
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    color: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    min-height: 300px;
    font-family: 'Arial', sans-serif;
}

.preview-header {
    text-align: center;
    color: #ffc107;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #ffc107;
    padding-bottom: 0.5rem;
}

.preview-beer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #444;
}

.preview-beer-item:last-child {
    border-bottom: none;
}

.beer-info {
    flex: 1;
}

.beer-name {
    font-weight: 500;
    display: block;
    font-size: 0.9rem;
}

.tap-number {
    font-size: 0.7rem;
    color: #ccc;
}

.beer-details {
    text-align: right;
    font-size: 0.8rem;
}

.abv {
    color: #ddd;
    margin-right: 0.5rem;
}

.price {
    color: #ffc107;
    font-weight: bold;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview when settings change
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    function updatePreview() {
        const title = document.getElementById('boardTitle').value;
        const showPrices = document.getElementById('showPrices').checked;
        const showABV = document.getElementById('showABV').checked;
        const showTapNumbers = document.getElementById('showTapNumbers').checked;
        
        // Update header
        document.querySelector('.preview-header').textContent = title;
        
        // Update beer items visibility
        document.querySelectorAll('.preview-beer-item').forEach(item => {
            const checkbox = document.getElementById('beer_' + item.dataset.beerId);
            item.style.display = checkbox && checkbox.checked ? 'flex' : 'none';
            
            // Toggle price visibility
            const price = item.querySelector('.price');
            if (price) price.style.display = showPrices ? 'inline' : 'none';
            
            // Toggle ABV visibility
            const abv = item.querySelector('.abv');
            if (abv) abv.style.display = showABV ? 'inline' : 'none';
            
            // Toggle tap number visibility
            const tapNumber = item.querySelector('.tap-number');
            if (tapNumber) tapNumber.style.display = showTapNumbers ? 'block' : 'none';
        });
    }
    
    // Save board
    document.getElementById('saveBoard').addEventListener('click', function() {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>Board saved successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    });
    
    // Fullscreen preview
    document.getElementById('fullscreenPreview').addEventListener('click', function() {
        const preview = document.getElementById('boardPreview').cloneNode(true);
        preview.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            margin: 0;
            border-radius: 0;
            font-size: 1.5rem;
            padding: 3rem;
        `;
        
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
        `;
        
        closeBtn.onclick = () => document.body.removeChild(preview);
        preview.appendChild(closeBtn);
        
        document.body.appendChild(preview);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
