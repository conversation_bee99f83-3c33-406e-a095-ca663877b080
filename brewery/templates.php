<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Template Management - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/digital-board.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-palette me-2 text-primary"></i>Template Management
                    </h1>
                    <p class="text-muted mb-0">Design and customize your digital board templates</p>
                </div>
                <div>
                    <a href="digital-board.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Digital Board
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                        <i class="fas fa-plus me-1"></i>Create Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills" id="templateTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="beer-templates-tab" data-bs-toggle="pill" data-bs-target="#beer-templates" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beer Board Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="event-templates-tab" data-bs-toggle="pill" data-bs-target="#event-templates" type="button" role="tab">
                        <i class="fas fa-calendar me-2"></i>Event Templates
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="custom-templates-tab" data-bs-toggle="pill" data-bs-target="#custom-templates" type="button" role="tab">
                        <i class="fas fa-paint-brush me-2"></i>Custom Templates
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Template Content -->
    <div class="tab-content" id="templateTabContent">
        <!-- Beer Board Templates -->
        <div class="tab-pane fade show active" id="beer-templates" role="tabpanel">
            <div class="row">
                <!-- Modern Dark Template -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card">
                        <div class="template-preview">
                            <div class="preview-screen bg-dark text-white p-3">
                                <h5 class="text-center text-warning mb-3">🍺 NOW POURING</h5>
                                <div class="row">
                                    <div class="col-8"><small>Hoppy IPA</small></div>
                                    <div class="col-4 text-end"><small>$7.00</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Smooth Lager</small></div>
                                    <div class="col-4 text-end"><small>$6.00</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Dark Stout</small></div>
                                    <div class="col-4 text-end"><small>$8.00</small></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">Modern Dark</h6>
                            <p class="card-text small text-muted">Clean dark theme with gold accents. Perfect for taprooms.</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>Preview
                                </button>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bright Summer Template -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card">
                        <div class="template-preview">
                            <div class="preview-screen bg-light text-dark p-3" style="background: linear-gradient(135deg, #FFF8DC, #F0E68C);">
                                <h5 class="text-center text-primary mb-3">☀️ SUMMER SPECIALS</h5>
                                <div class="row">
                                    <div class="col-8"><small>Summer Wheat</small></div>
                                    <div class="col-4 text-end"><small>$6.50</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Citrus IPA</small></div>
                                    <div class="col-4 text-end"><small>$7.50</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Light Lager</small></div>
                                    <div class="col-4 text-end"><small>$5.50</small></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">Bright Summer</h6>
                            <p class="card-text small text-muted">Light and cheerful theme for outdoor displays.</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>Preview
                                </button>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classic Wood Template -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card">
                        <div class="template-preview">
                            <div class="preview-screen text-white p-3" style="background: linear-gradient(135deg, #8B4513, #A0522D);">
                                <h5 class="text-center text-warning mb-3">🍻 ON TAP</h5>
                                <div class="row">
                                    <div class="col-8"><small>Porter</small></div>
                                    <div class="col-4 text-end"><small>$8.00</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Brown Ale</small></div>
                                    <div class="col-4 text-end"><small>$7.00</small></div>
                                </div>
                                <div class="row">
                                    <div class="col-8"><small>Amber Lager</small></div>
                                    <div class="col-4 text-end"><small>$6.50</small></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">Classic Wood</h6>
                            <p class="card-text small text-muted">Traditional brewery theme with wood tones.</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>Preview
                                </button>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Templates -->
        <div class="tab-pane fade" id="event-templates" role="tabpanel">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card">
                        <div class="template-preview">
                            <div class="preview-screen bg-primary text-white p-3">
                                <h5 class="text-center mb-3">🎉 UPCOMING EVENTS</h5>
                                <div class="text-center">
                                    <div><small>Live Music Tonight</small></div>
                                    <div><small>Trivia Wednesday</small></div>
                                    <div><small>Food Truck Friday</small></div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">Event Slideshow</h6>
                            <p class="card-text small text-muted">Dynamic slideshow for events and announcements.</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i>Preview
                                </button>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Templates -->
        <div class="tab-pane fade" id="custom-templates" role="tabpanel">
            <div class="text-center py-5">
                <i class="fas fa-paint-brush fa-3x text-muted mb-3"></i>
                <h5>Custom Templates</h5>
                <p class="text-muted">Create your own custom templates with our template builder.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                    <i class="fas fa-plus me-2"></i>Create Custom Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Template Modal -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-palette me-2"></i>Create New Template
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-4">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h5>Template Builder Coming Soon</h5>
                    <p class="text-muted">Advanced template customization tools will be available in the next update.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.template-card {
    transition: transform 0.3s ease;
}

.template-card:hover {
    transform: translateY(-5px);
}

.template-preview {
    height: 150px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.preview-screen {
    height: 100%;
    font-size: 0.8rem;
}

.nav-pills .nav-link {
    color: var(--text-secondary);
    border-radius: 8px;
}

.nav-pills .nav-link.active {
    background-color: var(--brand-primary);
    color: white;
}
</style>

<?php include '../includes/footer.php'; ?>
