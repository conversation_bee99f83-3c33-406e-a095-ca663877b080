<?php
require_once '../config/config.php';
requireRole('brewery');

$pageTitle = 'Digital Beer Board Admin - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery.css', '../assets/css/digital-board.css'];

$user = getCurrentUser();
$breweryId = $user['brewery_id'];

if (!$breweryId) {
    $_SESSION['error_message'] = 'No brewery associated with your account.';
    redirect('../index.php');
}

// Get brewery data
$brewery = null;
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Error fetching brewery: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
}

if (!$brewery) {
    $_SESSION['error_message'] = 'Brewery not found.';
    redirect('../index.php');
}

// Get digital boards for this brewery
$boards = [];
$stats = [
    'active_boards' => 0,
    'beers_on_tap' => 0,
    'templates' => 5,
    'displays_online' => 0
];

try {
    // Get digital boards
    $stmt = $conn->prepare("
        SELECT db.*,
               CASE WHEN db.updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'online' ELSE 'offline' END as status
        FROM digital_boards db
        WHERE db.brewery_id = ?
        ORDER BY db.created_at DESC
    ");
    $stmt->execute([$breweryId]);
    $boards = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate stats
    $stats['active_boards'] = count(array_filter($boards, function($board) { return $board['is_active']; }));
    $stats['displays_online'] = count(array_filter($boards, function($board) { return $board['status'] === 'online'; }));

    // Get beer count (if beer_menu table exists)
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu WHERE brewery_id = ? AND is_available = 1");
        $stmt->execute([$breweryId]);
        $beerCount = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['beers_on_tap'] = $beerCount['count'] ?? 0;
    } catch (Exception $e) {
        // Table might not exist, use default
        $stats['beers_on_tap'] = 12;
    }

} catch (Exception $e) {
    error_log("Error fetching digital boards: " . $e->getMessage());
    // Continue with empty arrays - page will show "no boards" message
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-tv me-2 text-primary"></i>Digital Beer Board Admin
                    </h1>
                    <p class="text-muted mb-0">Manage your digital beer board displays and content</p>
                </div>
                <div>
                    <a href="profile.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Profile
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBoardModal">
                        <i class="fas fa-plus me-1"></i>Create New Board
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="card-title text-primary"><?php echo $stats['active_boards']; ?></h3>
                    <p class="card-text small text-muted">Active Boards</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="card-title text-success"><?php echo $stats['beers_on_tap']; ?></h3>
                    <p class="card-text small text-muted">Beers on Tap</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="card-title text-warning"><?php echo $stats['templates']; ?></h3>
                    <p class="card-text small text-muted">Templates</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="card-title text-info"><?php echo $stats['displays_online']; ?></h3>
                    <p class="card-text small text-muted">Displays Online</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Digital Boards List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Your Digital Boards
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="view" id="grid-view" checked>
                        <label class="btn btn-outline-secondary" for="grid-view">
                            <i class="fas fa-th-large"></i>
                        </label>
                        <input type="radio" class="btn-check" name="view" id="list-view">
                        <label class="btn btn-outline-secondary" for="list-view">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" id="boards-grid">
                        <?php if (empty($boards)): ?>
                            <!-- No Boards Message -->
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <div class="mb-4">
                                        <i class="fas fa-tv fa-4x text-muted"></i>
                                    </div>
                                    <h5 class="text-muted mb-3">No Digital Boards Yet</h5>
                                    <p class="text-muted mb-4">
                                        Create your first digital beer board to display your menu on TVs and monitors throughout your brewery.
                                    </p>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBoardModal">
                                        <i class="fas fa-plus me-2"></i>Create Your First Board
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Display Real Boards -->
                            <?php foreach ($boards as $board): ?>
                                <?php
                                $settings = json_decode($board['settings'] ?? '{}', true);
                                $statusClass = $board['status'] === 'online' ? 'success' : 'warning';
                                $statusText = $board['status'] === 'online' ? 'Online' : 'Offline';
                                $lastUpdated = $board['updated_at'] ? date('M j, Y g:i A', strtotime($board['updated_at'])) : 'Never';
                                ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card board-card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0"><?php echo htmlspecialchars($board['name']); ?></h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="editBoard('<?php echo $board['id']; ?>')">
                                                        <i class="fas fa-edit me-2"></i>Edit Settings</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="duplicateBoard('<?php echo $board['id']; ?>')">
                                                        <i class="fas fa-copy me-2"></i>Duplicate</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="previewBoard('<?php echo $board['id']; ?>')">
                                                        <i class="fas fa-eye me-2"></i>Preview</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteBoard('<?php echo $board['id']; ?>')">
                                                        <i class="fas fa-trash me-2"></i>Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="board-preview mb-3">
                                                <div class="preview-screen">
                                                    <div class="preview-content">
                                                        <div class="preview-header">🍺 <?php echo htmlspecialchars($brewery['name']); ?></div>
                                                        <div class="preview-beers">
                                                            <div class="preview-beer">Template: <?php echo ucfirst(str_replace('-', ' ', $board['theme'] ?? 'beersty-professional')); ?></div>
                                                            <div class="preview-beer">Layout: <?php echo ucfirst($board['layout'] ?? 'grid'); ?></div>
                                                            <div class="preview-beer">Mode: <?php echo ucfirst($board['display_mode'] ?? 'static'); ?></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                <small class="text-muted">Updated: <?php echo $lastUpdated; ?></small>
                                            </div>
                                            <p class="card-text small text-muted mb-3">
                                                <?php echo htmlspecialchars($board['description'] ?: 'Digital beer board display'); ?>
                                            </p>
                                            <div class="d-flex gap-2">
                                                <a href="../business/digital-board/display.php?board_id=<?php echo urlencode($board['board_id']); ?>"
                                                   target="_blank" class="btn btn-primary btn-sm flex-fill">
                                                    <i class="fas fa-tv me-1"></i>View Display
                                                </a>
                                                <button class="btn btn-outline-primary btn-sm" onclick="copyBoardUrl('<?php echo $board['board_id']; ?>')">
                                                    <i class="fas fa-link"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBoardModal">
                            <i class="fas fa-plus me-2"></i>Create New Board
                        </button>
                        <a href="../business/digital-board/templates.php" class="btn btn-outline-primary">
                            <i class="fas fa-palette me-2"></i>Manage Templates
                        </a>
                        <a href="menu.php" class="btn btn-outline-primary">
                            <i class="fas fa-beer me-2"></i>Update Beer List
                        </a>
                        <a href="../business/digital-board/slideshow-builder.php" class="btn btn-outline-primary">
                            <i class="fas fa-play me-2"></i>Slideshow Builder
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h6>
                </div>
                <div class="card-body">
                    <div class="activity-item">
                        <div class="d-flex">
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="activity-content">
                                <p class="mb-1">Updated Main Taproom Board</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="d-flex">
                            <div class="activity-icon bg-success">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="activity-content">
                                <p class="mb-1">Added new beer: Summer IPA</p>
                                <small class="text-muted">4 hours ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="d-flex">
                            <div class="activity-icon bg-warning">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div class="activity-content">
                                <p class="mb-1">Patio Board went offline</p>
                                <small class="text-muted">1 day ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Board Modal -->
<div class="modal fade" id="createBoardModal" tabindex="-1" aria-labelledby="createBoardModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createBoardModalLabel">
                    <i class="fas fa-plus me-2"></i>Create New Digital Board
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createBoardForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boardName" class="form-label">Board Name *</label>
                                <input type="text" class="form-control" id="boardName" name="name" required
                                       placeholder="e.g., Main Taproom Board">
                                <div class="form-text">Give your board a descriptive name</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boardLocation" class="form-label">Location</label>
                                <input type="text" class="form-control" id="boardLocation" name="location"
                                       placeholder="e.g., Main Bar, Patio, Event Room">
                                <div class="form-text">Where will this board be displayed?</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="boardDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="boardDescription" name="description" rows="2"
                                  placeholder="Brief description of this board's purpose"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boardTemplate" class="form-label">Template</label>
                                <select class="form-select" id="boardTemplate" name="template">
                                    <option value="beersty-professional">Beersty Professional</option>
                                    <option value="classic-dark">Classic Dark</option>
                                    <option value="modern-light">Modern Light</option>
                                    <option value="brewery-wood">Brewery Wood</option>
                                    <option value="industrial-steel">Industrial Steel</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="boardLayout" class="form-label">Layout</label>
                                <select class="form-select" id="boardLayout" name="layout">
                                    <option value="grid">Grid View</option>
                                    <option value="list">List View</option>
                                    <option value="cards">Card View</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="refreshInterval" class="form-label">Auto-Refresh Interval</label>
                                <select class="form-select" id="refreshInterval" name="refresh_interval">
                                    <option value="15">15 seconds</option>
                                    <option value="30" selected>30 seconds</option>
                                    <option value="60">1 minute</option>
                                    <option value="300">5 minutes</option>
                                    <option value="0">Manual only</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="displayMode" class="form-label">Display Mode</label>
                                <select class="form-select" id="displayMode" name="display_mode">
                                    <option value="static">Static Beer List</option>
                                    <option value="slideshow">Slideshow</option>
                                    <option value="hybrid">Hybrid (Beer List + Slides)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Display Options</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPrices" name="show_prices" checked>
                                    <label class="form-check-label" for="showPrices">Show Prices</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showDescriptions" name="show_descriptions" checked>
                                    <label class="form-check-label" for="showDescriptions">Show Descriptions</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showABV" name="show_abv" checked>
                                    <label class="form-check-label" for="showABV">Show ABV</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showTapNumbers" name="show_tap_numbers">
                                    <label class="form-check-label" for="showTapNumbers">Show Tap Numbers</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showHeader" name="show_header" checked>
                                    <label class="form-check-label" for="showHeader">Show Header</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showTicker" name="show_ticker">
                                    <label class="form-check-label" for="showTicker">Show Ticker Message</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3" id="tickerMessageGroup" style="display: none;">
                        <label for="tickerMessage" class="form-label">Ticker Message</label>
                        <input type="text" class="form-control" id="tickerMessage" name="ticker_message"
                               placeholder="Welcome message or announcements">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createBoard()">
                    <i class="fas fa-plus me-1"></i>Create Board
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide ticker message input based on checkbox
document.getElementById('showTicker').addEventListener('change', function() {
    const tickerGroup = document.getElementById('tickerMessageGroup');
    tickerGroup.style.display = this.checked ? 'block' : 'none';
});

// Create board function
function createBoard() {
    const form = document.getElementById('createBoardForm');
    const formData = new FormData(form);

    // Convert FormData to object
    const data = {};
    for (let [key, value] of formData.entries()) {
        if (form.elements[key].type === 'checkbox') {
            data[key] = form.elements[key].checked;
        } else {
            data[key] = value;
        }
    }

    // Add brewery ID
    data.brewery_id = '<?php echo $breweryId; ?>';

    // Generate unique board ID
    data.board_id = 'board_' + Date.now();

    console.log('Creating board with data:', data);

    // Show loading state
    const createBtn = event.target;
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
    createBtn.disabled = true;

    // Make API call
    fetch('../api/digital-board.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create_board',
            ...data
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('createBoardModal'));
            modal.hide();

            // Show success message
            showToast('Digital board created successfully!', 'success');

            // Reload page to show new board
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast('Error creating board: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Error creating board. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button
        createBtn.innerHTML = originalText;
        createBtn.disabled = false;
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

// Board management functions
function editBoard(boardId) {
    showToast('Edit board functionality coming soon!', 'info');
    console.log('Edit board:', boardId);
}

function duplicateBoard(boardId) {
    if (confirm('Are you sure you want to duplicate this board?')) {
        showToast('Duplicate board functionality coming soon!', 'info');
        console.log('Duplicate board:', boardId);
    }
}

function previewBoard(boardId) {
    // Open board preview in new window
    const url = '../business/digital-board/display.php?board_id=' + boardId;
    window.open(url, '_blank', 'width=1200,height=800');
    showToast('Opening board preview...', 'info');
}

function deleteBoard(boardId) {
    if (confirm('Are you sure you want to delete this board? This action cannot be undone.')) {
        // Show loading state
        showToast('Deleting board...', 'info');

        // Make API call
        fetch('../api/digital-board.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_board',
                board_id: boardId
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('Board deleted successfully!', 'success');
                // Reload page to update the list
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast('Error deleting board: ' + result.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error deleting board. Please try again.', 'error');
        });
    }
}

function copyBoardUrl(boardId) {
    const url = window.location.origin + '/business/digital-board/display.php?board_id=' + boardId;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showToast('Board URL copied to clipboard!', 'success');
        }).catch(() => {
            showToast('Could not copy URL. Please copy manually: ' + url, 'info');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showToast('Board URL copied to clipboard!', 'success');
        } catch (err) {
            showToast('Could not copy URL. Please copy manually: ' + url, 'info');
        }
        document.body.removeChild(textArea);
    }
}
</script>

<?php include '../includes/footer.php'; ?>
