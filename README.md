# Beersty - Brewery Management System

## Project Overview

A complete PHP web application for managing breweries, built with Bootstrap, FontAwesome, JavaScript, AJAX, CSS, and MySQL.

## 🚀 Quick Start with XAMPP

This is a PHP web application designed to run with XAMPP (no Node.js required).

**Requirements**

- XAMPP (includes Apache, MySQL, PHP)
- Web browser
- PowerShell (for setup script)

**Quick Installation:**

```batch
# Option 1: Automated setup (run as Administrator)
setup-xampp.bat

# Option 2: Manual setup
# 1. Install XAMPP from https://www.apachefriends.org/download.html
# 2. Copy project to C:\xampp\htdocs\beersty\
# 3. Start Apache and MySQL in XAMPP Control Panel
# 4. Run: .\setup-database.ps1
# 5. Access: http://localhost/beersty/
```

## Technologies Used

This project is built with:

- **PHP 8.0+** (Backend)
- **MySQL 8.0+** (Database)
- **Apache** (Web Server)
- **Bootstrap 5.3** (Frontend Framework)
- **FontAwesome 6.4** (Icons)
- **jQuery 3.7** (JavaScript)
- **Custom CSS** (Styling)

## Features

- Multi-role authentication (Admin, Brewery, Customer)
- Brewery profile management
- Menu management (Beer & Food)
- Digital board displays
- Coupon system
- Social features (followers, likes)
- Data import/export
- Responsive design

## Default Login

- **Email**: <EMAIL>
- **Password**: admin123

## Support

For setup help, see:
- `XAMPP-SETUP.md` - Detailed installation guide
- `setup-guide.html` - Visual setup guide
