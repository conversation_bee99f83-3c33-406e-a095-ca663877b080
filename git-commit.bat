@echo off
REM Git Commit Script for Beersty
REM Simple batch file to commit and push changes

title Git Commit - Beersty

echo.
echo ===============================================
echo 🚀 Committing Beersty Changes to GitHub
echo ===============================================
echo.

REM Change to project directory
cd /d "C:\xkinteractive-github\beersty-lovable"

echo 📁 Project Directory: %CD%
echo.

REM Check Git status
echo 📊 Checking Git status...
git status --short

echo.
echo 📦 Adding all changes...
git add .

echo.
echo 💾 Committing changes...
git commit -m "Fix MySQL database connection and complete brewery management system

- Fixed MySQL database connection issues and configuration
- Updated database setup with proper table creation
- Fixed login system and authentication flow
- Added Michigan brewery data import (376 breweries)
- Created comprehensive CSV import system
- Added PowerShell management scripts for easy server management
- Fixed URL path issues and navigation problems
- Completed social features and user management system
- Added brewery listing, search, and management features
- Implemented admin dashboard with full CRUD operations
- Added user registration, login, and profile management
- Created beer rating and social interaction features
- Added responsive design and mobile optimization
- Implemented comprehensive error handling and debugging tools

System is now fully functional with:
- Working MySQL database connection
- Complete brewery management system
- User authentication and authorization
- Social features for beer enthusiasts
- Admin tools for data management
- CSV import for external brewery databases
- Responsive web interface
- PowerShell automation scripts"

echo.
echo 🌐 Checking remote repository...
git remote -v

echo.
set /p push_confirm="Push changes to GitHub? (Y/n): "
if /i "%push_confirm%"=="n" goto :skip_push

echo.
echo 🚀 Pushing to GitHub...
git push origin main

if errorlevel 1 (
    echo.
    echo ❌ Push failed. You may need to authenticate or check your connection.
    echo Try running: git push origin main
    pause
    exit /b 1
)

echo.
echo ✅ Successfully pushed to GitHub!
goto :end

:skip_push
echo.
echo ⚠️ Push skipped. Run 'git push origin main' manually when ready.

:end
echo.
echo 🎉 Git operations complete!
echo.
echo 📝 Summary of changes committed:
echo    🔧 Fixed MySQL database connection
echo    🗄️ Updated database configuration
echo    🔐 Fixed login and authentication
echo    🍺 Added brewery data import
echo    📊 Created CSV import system
echo    ⚙️ Added PowerShell scripts
echo    🐛 Fixed navigation issues
echo    📱 Completed social features
echo.
echo 🔗 Next steps:
echo    • Start server: start-server.ps1
echo    • Login: <EMAIL> / admin123
echo    • Browse: http://localhost:8000/
echo.
pause
