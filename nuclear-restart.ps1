# NUCLEAR RESTART - Kill everything and start completely fresh
# This script stops ALL web server processes and restarts cleanly

Write-Host "NUCLEAR RESTART - KILLING ALL PROCESSES" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host "This will stop ALL web server processes and restart fresh" -ForegroundColor Yellow
Write-Host ""

$confirm = Read-Host "Are you sure you want to kill all processes? (y/n)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "Cancelled." -ForegroundColor Yellow
    exit
}

Write-Host ""
Write-Host "STEP 1: KILLING ALL PROCESSES..." -ForegroundColor Red

# Kill all Apache processes
Write-Host "Killing Apache processes..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "apache" -ErrorAction SilentlyContinue | Stop-Process -Force

# Kill all MySQL processes  
Write-Host "Killing MySQL processes..." -ForegroundColor Yellow
Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "mysql" -ErrorAction SilentlyContinue | Stop-Process -Force

# Kill XAMPP Control Panel
Write-Host "Killing XAMPP Control Panel..." -ForegroundColor Yellow
Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue | Stop-Process -Force

# Kill IIS if running
Write-Host "Stopping IIS..." -ForegroundColor Yellow
Stop-Service -Name "W3SVC" -Force -ErrorAction SilentlyContinue
Stop-Service -Name "IISADMIN" -Force -ErrorAction SilentlyContinue

# Kill any other web servers
Write-Host "Killing other web servers..." -ForegroundColor Yellow
Get-Process -Name "nginx" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force

# Wait for processes to fully terminate
Write-Host "Waiting for processes to terminate..." -ForegroundColor Yellow
Start-Sleep 5

Write-Host "ALL PROCESSES KILLED!" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 2: CLEARING PORTS..." -ForegroundColor Cyan

# Show what was using ports
Write-Host "Checking port usage..." -ForegroundColor Yellow
$port80 = netstat -an | findstr ":80 "
$port3306 = netstat -an | findstr ":3306 "

if ($port80) {
    Write-Host "Port 80 usage:" -ForegroundColor Yellow
    $port80 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} else {
    Write-Host "Port 80 is now FREE" -ForegroundColor Green
}

if ($port3306) {
    Write-Host "Port 3306 usage:" -ForegroundColor Yellow
    $port3306 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} else {
    Write-Host "Port 3306 is now FREE" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 3: FINDING XAMPP..." -ForegroundColor Cyan

$xamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$xamppPath = $xamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $xamppPath) {
    Write-Host "ERROR: XAMPP not found!" -ForegroundColor Red
    Write-Host "Install XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found XAMPP at: $xamppPath" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 4: STARTING FRESH..." -ForegroundColor Cyan

# Start XAMPP Control Panel
$xamppControl = Join-Path $xamppPath "xampp-control.exe"
Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
Start-Process $xamppControl -WindowStyle Normal

Write-Host ""
Write-Host "XAMPP Control Panel started!" -ForegroundColor Green
Write-Host ""
Write-Host "NOW DO THIS MANUALLY:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host "1. In XAMPP Control Panel, click START next to Apache" -ForegroundColor White
Write-Host "2. In XAMPP Control Panel, click START next to MySQL" -ForegroundColor White
Write-Host "3. Wait for both to show 'Running' status" -ForegroundColor White
Write-Host "4. If Apache shows error, click 'Logs' to see what's wrong" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter AFTER you have started Apache and MySQL"

Write-Host ""
Write-Host "STEP 5: TESTING..." -ForegroundColor Cyan

# Test localhost
Write-Host "Testing localhost..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -UseBasicParsing
    Write-Host "SUCCESS: localhost is working!" -ForegroundColor Green
    
    # Test project
    try {
        $projectResponse = Invoke-WebRequest -Uri "http://localhost/beersty-lovable" -TimeoutSec 5 -UseBasicParsing
        Write-Host "SUCCESS: Project is accessible!" -ForegroundColor Green
    } catch {
        Write-Host "WARNING: Project not found - check if files are in htdocs/beersty-lovable" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "ERROR: localhost still not working" -ForegroundColor Red
    Write-Host "Check XAMPP Control Panel - is Apache running?" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "STEP 6: OPENING TEST PAGES..." -ForegroundColor Cyan

# Open test pages
Write-Host "Opening test pages..." -ForegroundColor Yellow
Start-Process "http://localhost"
Start-Sleep 2
Start-Process "http://localhost/beersty-lovable"
Start-Sleep 2
Start-Process "http://localhost/beersty-lovable/test-pdo-simple.php"
Start-Sleep 2
Start-Process "http://localhost/phpmyadmin"

Write-Host ""
Write-Host "NUCLEAR RESTART COMPLETE!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Host "Test these URLs:" -ForegroundColor Cyan
Write-Host "  Main: http://localhost" -ForegroundColor White
Write-Host "  Project: http://localhost/beersty-lovable" -ForegroundColor White
Write-Host "  PDO Test: http://localhost/beersty-lovable/test-pdo-simple.php" -ForegroundColor White
Write-Host "  User Management: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
Write-Host "  phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host ""
Write-Host "If localhost still doesn't work:" -ForegroundColor Yellow
Write-Host "1. Restart your computer" -ForegroundColor White
Write-Host "2. Run XAMPP as Administrator" -ForegroundColor White
Write-Host "3. Check Windows Firewall" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
