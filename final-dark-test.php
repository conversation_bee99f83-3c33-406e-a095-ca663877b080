<?php
require_once 'config/config.php';

$pageTitle = 'Final Dark Mode Test - ' . APP_NAME;
$additionalCSS = ['assets/css/auth.css'];

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">🔧 Final Dark Mode Test</h2>
                        <p class="text-muted">Testing dark mode with proper navigation</p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h5>🎯 How to Enable Dark Mode:</h5>
                        <ol>
                            <li>Look at the top navigation bar</li>
                            <li>Find your profile icon (👤) on the right side</li>
                            <li>Click on it to open the dropdown menu</li>
                            <li>In the "Theme" section, click the "Dark" button</li>
                            <li>The page should immediately switch to dark mode</li>
                        </ol>
                    </div>
                    
                    <h4>🎨 Form Test</h4>
                    <form class="mb-4">
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testPassword" class="form-label">Password</label>
                            <input type="password" class="form-control" id="testPassword" placeholder="Password">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testSelect" class="form-label">Select Option</label>
                            <select class="form-select" id="testSelect">
                                <option>Choose...</option>
                                <option value="1">Option 1</option>
                                <option value="2">Option 2</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="testTextarea" class="form-label">Textarea</label>
                            <textarea class="form-control" id="testTextarea" rows="3" placeholder="Enter text here..."></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="testCheck">
                            <label class="form-check-label" for="testCheck">
                                Check me out
                            </label>
                        </div>
                        
                        <button type="button" class="btn btn-primary me-2">Primary Button</button>
                        <button type="button" class="btn btn-secondary">Secondary Button</button>
                    </form>
                    
                    <div class="text-center mt-4">
                        <button onclick="forceDarkMode()" class="btn btn-warning me-2">
                            <i class="fas fa-moon me-2"></i>Force Dark Mode
                        </button>
                        <button onclick="forceLightMode()" class="btn btn-info me-2">
                            <i class="fas fa-sun me-2"></i>Force Light Mode
                        </button>
                        <button onclick="checkStatus()" class="btn btn-success">
                            <i class="fas fa-search me-2"></i>Check Status
                        </button>
                    </div>
                    
                    <div id="statusDisplay" class="mt-4 p-3 border rounded" style="background: #f8f9fa;">
                        <h6>Status will appear here...</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function forceDarkMode() {
    console.log('Forcing dark mode...');
    const html = document.documentElement;
    const body = document.body;
    
    html.classList.add('dark-mode');
    body.classList.add('dark-mode');
    localStorage.setItem('beersty-theme', 'dark');
    
    // Force style recalculation
    document.body.style.display = 'none';
    document.body.offsetHeight;
    document.body.style.display = '';
    
    updateStatus();
}

function forceLightMode() {
    console.log('Forcing light mode...');
    const html = document.documentElement;
    const body = document.body;
    
    html.classList.remove('dark-mode');
    body.classList.remove('dark-mode');
    localStorage.setItem('beersty-theme', 'light');
    
    // Force style recalculation
    document.body.style.display = 'none';
    document.body.offsetHeight;
    document.body.style.display = '';
    
    updateStatus();
}

function checkStatus() {
    updateStatus();
    
    // Check if dark mode manager exists
    if (window.darkModeManager) {
        console.log('✅ Dark Mode Manager exists');
        console.log('Current theme:', window.darkModeManager.getCurrentTheme());
    } else {
        console.log('❌ Dark Mode Manager NOT found');
    }
}

function updateStatus() {
    const html = document.documentElement;
    const body = document.body;
    const isDark = html.classList.contains('dark-mode');
    
    const statusDiv = document.getElementById('statusDisplay');
    const bodyBg = getComputedStyle(body).backgroundColor;
    const htmlClasses = html.className || 'none';
    const localStorage = window.localStorage.getItem('beersty-theme') || 'not set';
    
    // Get CSS variables
    const styles = getComputedStyle(html);
    const bgPrimary = styles.getPropertyValue('--bg-primary') || 'not set';
    const textPrimary = styles.getPropertyValue('--text-primary') || 'not set';
    
    statusDiv.innerHTML = `
        <h6>🔍 Current Status:</h6>
        <ul>
            <li><strong>Mode:</strong> ${isDark ? '🌙 Dark Mode' : '☀️ Light Mode'}</li>
            <li><strong>HTML Classes:</strong> ${htmlClasses}</li>
            <li><strong>Body Background:</strong> ${bodyBg}</li>
            <li><strong>--bg-primary:</strong> ${bgPrimary}</li>
            <li><strong>--text-primary:</strong> ${textPrimary}</li>
            <li><strong>LocalStorage:</strong> ${localStorage}</li>
            <li><strong>Dark Mode Manager:</strong> ${window.darkModeManager ? '✅ Exists' : '❌ Missing'}</li>
        </ul>
    `;
    
    // Update status div styling based on mode
    statusDiv.style.background = isDark ? '#2d2d2d' : '#f8f9fa';
    statusDiv.style.color = isDark ? '#ffffff' : '#212529';
}

// Update status every 2 seconds
setInterval(updateStatus, 2000);

// Initial status update
setTimeout(updateStatus, 1000);
</script>

<?php include 'includes/footer.php'; ?>
