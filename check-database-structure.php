<?php
/**
 * Check Database Structure
 * Verify table structures and fix any issues
 */

require_once 'config/config.php';

echo "<h1>🗄️ Database Structure Check</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>1. Users Table Structure</h2>";
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($col['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // Check if last_login column exists
    $hasLastLogin = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'last_login') {
            $hasLastLogin = true;
            break;
        }
    }
    
    if (!$hasLastLogin) {
        echo "<p>❌ <strong>last_login</strong> column missing! Adding it...</p>";
        try {
            $conn->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL");
            echo "<p>✅ Added last_login column</p>";
        } catch (Exception $e) {
            echo "<p>❌ Failed to add last_login column: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>✅ last_login column exists</p>";
    }
    
    echo "<h2>2. Profiles Table Structure</h2>";
    $stmt = $conn->query("DESCRIBE profiles");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($col['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($col['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($col['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    echo "<h2>3. Sample Data Check</h2>";
    
    // Check users
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "<p><strong>Users:</strong> $userCount records</p>";
    
    if ($userCount > 0) {
        $stmt = $conn->query("SELECT id, email, created_at FROM users LIMIT 3");
        $users = $stmt->fetchAll();
        echo "<ul>";
        foreach ($users as $user) {
            echo "<li>" . htmlspecialchars($user['email']) . " (ID: " . htmlspecialchars($user['id']) . ")</li>";
        }
        echo "</ul>";
    }
    
    // Check profiles
    $stmt = $conn->query("SELECT COUNT(*) as count FROM profiles");
    $profileCount = $stmt->fetch()['count'];
    echo "<p><strong>Profiles:</strong> $profileCount records</p>";
    
    if ($profileCount > 0) {
        $stmt = $conn->query("SELECT id, email, role FROM profiles LIMIT 3");
        $profiles = $stmt->fetchAll();
        echo "<ul>";
        foreach ($profiles as $profile) {
            echo "<li>" . htmlspecialchars($profile['email']) . " (" . htmlspecialchars($profile['role']) . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>4. Admin User Check</h2>";
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role 
        FROM users u 
        LEFT JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>✅ Admin user exists</p>";
        echo "<ul>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</li>";
        echo "<li><strong>Role:</strong> " . htmlspecialchars($admin['role'] ?? 'NO PROFILE') . "</li>";
        echo "<li><strong>Has Password:</strong> " . (empty($admin['password_hash']) ? 'NO' : 'YES') . "</li>";
        echo "</ul>";
        
        if (empty($admin['role'])) {
            echo "<p>❌ Admin user has no profile! Creating profile...</p>";
            try {
                $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin') ON DUPLICATE KEY UPDATE role = 'admin'");
                $stmt->execute([$admin['id'], $admin['email']]);
                echo "<p>✅ Admin profile created</p>";
            } catch (Exception $e) {
                echo "<p>❌ Failed to create admin profile: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        // Test password
        if (password_verify('admin123', $admin['password_hash'])) {
            echo "<p>✅ Admin password is correct</p>";
        } else {
            echo "<p>❌ Admin password is incorrect! Fixing...</p>";
            try {
                $newHash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
                $stmt->execute([$newHash, '<EMAIL>']);
                echo "<p>✅ Admin password fixed</p>";
            } catch (Exception $e) {
                echo "<p>❌ Failed to fix admin password: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    } else {
        echo "<p>❌ Admin user does not exist! Creating...</p>";
        try {
            $userId = bin2hex(random_bytes(16));
            $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
            
            $conn->beginTransaction();
            
            // Insert user
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
            
            // Insert profile
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
            $stmt->execute([$userId, '<EMAIL>']);
            
            $conn->commit();
            echo "<p>✅ Admin user created successfully</p>";
            
        } catch (Exception $e) {
            $conn->rollback();
            echo "<p>❌ Failed to create admin user: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>5. Test Login Query</h2>";
    try {
        $stmt = $conn->prepare("
            SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.email = ?
        ");
        $stmt->execute(['<EMAIL>']);
        $testUser = $stmt->fetch();
        
        if ($testUser) {
            echo "<p>✅ Login query works! User found with role: " . htmlspecialchars($testUser['role']) . "</p>";
        } else {
            echo "<p>❌ Login query failed - no user found</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Login query error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Database Error!</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='debug-login-detailed.php' class='btn btn-info'>Run Detailed Login Debug</a></li>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>Try Login Page</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    margin: 10px 0;
    font-size: 14px;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: left;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
