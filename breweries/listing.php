<?php
require_once '../config/config.php';

$pageTitle = 'Browse Breweries - ' . APP_NAME;
$additionalCSS = ['../assets/css/breweries.css'];

// Pagination settings
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// Search and filter parameters
$search = sanitizeInput($_GET['search'] ?? '');
$city = sanitizeInput($_GET['city'] ?? '');
$state = sanitizeInput($_GET['state'] ?? '');
$brewery_type = sanitizeInput($_GET['brewery_type'] ?? '');
$sort = sanitizeInput($_GET['sort'] ?? 'name');

// Build query
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($city)) {
    $whereConditions[] = "city = ?";
    $params[] = $city;
}

if (!empty($state)) {
    $whereConditions[] = "state = ?";
    $params[] = $state;
}

if (!empty($brewery_type)) {
    $whereConditions[] = "brewery_type = ?";
    $params[] = $brewery_type;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Valid sort options
$validSorts = ['name', 'city', 'created_at', 'follower_count', 'like_count'];
if (!in_array($sort, $validSorts)) {
    $sort = 'name';
}

$orderClause = "ORDER BY $sort " . ($sort === 'created_at' ? 'DESC' : 'ASC');

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total FROM breweries $whereClause";
    $stmt = $conn->prepare($countQuery);
    $stmt->execute($params);
    $totalBreweries = $stmt->fetch()['total'];
    $totalPages = ceil($totalBreweries / $limit);
    
    // Get breweries
    $query = "
        SELECT id, name, description, city, state, brewery_type, logo, feature_image,
               follower_count, like_count, verified, claimed, created_at
        FROM breweries 
        $whereClause 
        $orderClause 
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $breweries = $stmt->fetchAll();
    
    // Get filter options
    $statesQuery = "SELECT DISTINCT state FROM breweries WHERE state IS NOT NULL AND state != '' ORDER BY state";
    $stmt = $conn->prepare($statesQuery);
    $stmt->execute();
    $states = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $citiesQuery = "SELECT DISTINCT city FROM breweries WHERE city IS NOT NULL AND city != '' ORDER BY city";
    $stmt = $conn->prepare($citiesQuery);
    $stmt->execute();
    $cities = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    error_log("Breweries listing error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading breweries.';
    $breweries = [];
    $totalBreweries = 0;
    $totalPages = 0;
    $states = [];
    $cities = [];
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-3">
                <i class="fas fa-building me-2"></i>Browse Breweries
            </h1>
            <p class="text-muted">Discover amazing breweries in your area and beyond</p>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" id="filterForm">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search breweries...">
                    </div>
                    
                    <div class="col-md-2">
                        <label for="state" class="form-label">State</label>
                        <select class="form-select" id="state" name="state">
                            <option value="">All States</option>
                            <?php foreach ($states as $stateOption): ?>
                                <option value="<?php echo htmlspecialchars($stateOption); ?>"
                                        <?php echo $state === $stateOption ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stateOption); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="city" class="form-label">City</label>
                        <select class="form-select" id="city" name="city">
                            <option value="">All Cities</option>
                            <?php foreach ($cities as $cityOption): ?>
                                <option value="<?php echo htmlspecialchars($cityOption); ?>"
                                        <?php echo $city === $cityOption ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cityOption); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="brewery_type" class="form-label">Type</label>
                        <select class="form-select" id="brewery_type" name="brewery_type">
                            <option value="">All Types</option>
                            <option value="micro" <?php echo $brewery_type === 'micro' ? 'selected' : ''; ?>>Microbrewery</option>
                            <option value="nano" <?php echo $brewery_type === 'nano' ? 'selected' : ''; ?>>Nanobrewery</option>
                            <option value="regional" <?php echo $brewery_type === 'regional' ? 'selected' : ''; ?>>Regional</option>
                            <option value="brewpub" <?php echo $brewery_type === 'brewpub' ? 'selected' : ''; ?>>Brewpub</option>
                            <option value="large" <?php echo $brewery_type === 'large' ? 'selected' : ''; ?>>Large</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="sort" class="form-label">Sort By</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                            <option value="city" <?php echo $sort === 'city' ? 'selected' : ''; ?>>City</option>
                            <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Newest</option>
                            <option value="follower_count" <?php echo $sort === 'follower_count' ? 'selected' : ''; ?>>Most Followers</option>
                            <option value="like_count" <?php echo $sort === 'like_count' ? 'selected' : ''; ?>>Most Liked</option>
                        </select>
                    </div>
                    
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <p class="text-muted mb-0">
                    Showing <?php echo number_format(count($breweries)); ?> of <?php echo number_format($totalBreweries); ?> breweries
                    <?php if ($page > 1): ?>
                        (Page <?php echo $page; ?> of <?php echo $totalPages; ?>)
                    <?php endif; ?>
                </p>
                
                <?php if (!empty($search) || !empty($city) || !empty($state) || !empty($brewery_type)): ?>
                    <a href="listing.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Breweries Grid -->
    <?php if (!empty($breweries)): ?>
        <div class="row g-4">
            <?php foreach ($breweries as $brewery): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 brewery-card">
                        <?php if ($brewery['feature_image']): ?>
                            <img src="<?php echo htmlspecialchars($brewery['feature_image']); ?>" 
                                 class="card-img-top" alt="<?php echo htmlspecialchars($brewery['name']); ?>"
                                 style="height: 200px; object-fit: cover;">
                        <?php else: ?>
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-building fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex align-items-start mb-2">
                                <?php if ($brewery['logo']): ?>
                                    <img src="<?php echo htmlspecialchars($brewery['logo']); ?>" 
                                         alt="Logo" class="brewery-logo me-3">
                                <?php endif; ?>
                                
                                <div class="flex-grow-1">
                                    <h5 class="card-title mb-1">
                                        <a href="detail.php?id=<?php echo $brewery['id']; ?>"
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($brewery['name']); ?>
                                        </a>
                                    </h5>
                                    
                                    <div class="mb-2">
                                        <?php if ($brewery['verified']): ?>
                                            <span class="badge bg-success me-1">
                                                <i class="fas fa-check-circle me-1"></i>Verified
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($brewery['brewery_type']): ?>
                                            <span class="badge bg-secondary">
                                                <?php echo ucfirst($brewery['brewery_type']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($brewery['city'] || $brewery['state']): ?>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars(trim($brewery['city'] . ', ' . $brewery['state'], ', ')); ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if ($brewery['description']): ?>
                                <p class="card-text flex-grow-1">
                                    <?php echo htmlspecialchars(substr($brewery['description'], 0, 120)); ?>
                                    <?php if (strlen($brewery['description']) > 120): ?>...<?php endif; ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <div class="brewery-stats">
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i><?php echo number_format($brewery['follower_count']); ?>
                                        <i class="fas fa-heart ms-2 me-1"></i><?php echo number_format($brewery['like_count']); ?>
                                    </small>
                                </div>
                                
                                <a href="detail.php?id=<?php echo $brewery['id']; ?>"
                                   class="btn btn-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="Breweries pagination" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);
                    
                    if ($startPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                        </li>
                        <?php if ($startPage > 2): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>">
                                <?php echo $totalPages; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
        
    <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4>No breweries found</h4>
            <p class="text-muted">Try adjusting your search criteria or browse all breweries.</p>
            <a href="listing.php" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>View All Breweries
            </a>
        </div>
    <?php endif; ?>
</div>

<?php
$pageJS = "
    // Auto-submit form when filters change
    document.querySelectorAll('#filterForm select').forEach(function(select) {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
    
    // Search on Enter key
    document.getElementById('search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('filterForm').submit();
        }
    });
";

include '../includes/footer.php';
?>
