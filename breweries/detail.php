<?php
require_once '../config/config.php';

$breweryId = $_GET['id'] ?? null;
if (!$breweryId) {
    $_SESSION['error_message'] = 'Brewery ID is required.';
    redirect('listing.php');
}

// Get brewery data
$brewery = null;
$beerMenu = [];
$foodMenu = [];
$coupons = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get brewery details
    $stmt = $conn->prepare("SELECT * FROM breweries WHERE id = ?");
    $stmt->execute([$breweryId]);
    $brewery = $stmt->fetch();
    
    if (!$brewery) {
        $_SESSION['error_message'] = 'Brewery not found.';
        redirect('listing.php');
    }
    
    // Decode social links
    if (isset($brewery['social_links']) && $brewery['social_links']) {
        $brewery['social_links'] = json_decode($brewery['social_links'], true);
    } else {
        $brewery['social_links'] = [];
    }
    
    // Get beer menu
    $stmt = $conn->prepare("SELECT * FROM beer_menu WHERE brewery_id = ? ORDER BY featured DESC, name");
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();
    
    // Get food menu
    $stmt = $conn->prepare("SELECT * FROM food_menu WHERE brewery_id = ? AND available = 1 ORDER BY category, name");
    $stmt->execute([$breweryId]);
    $foodMenu = $stmt->fetchAll();
    
    // Get active coupons
    $stmt = $conn->prepare("SELECT * FROM brewery_coupons WHERE brewery_id = ? AND is_active = 1 AND expiry_date >= CURDATE() ORDER BY expiry_date");
    $stmt->execute([$breweryId]);
    $coupons = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Brewery detail error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery data.';
    redirect('listing.php');
}

$pageTitle = htmlspecialchars($brewery['name']) . ' - ' . APP_NAME;
$additionalCSS = ['../assets/css/brewery-detail.css'];

include '../includes/header.php';
?>

<div class="brewery-hero">
    <?php if (isset($brewery['feature_image']) && $brewery['feature_image']): ?>
        <div class="hero-image" style="background-image: url('<?php echo htmlspecialchars($brewery['feature_image']); ?>');">
            <div class="hero-overlay"></div>
        </div>
    <?php else: ?>
        <div class="hero-image bg-primary">
            <div class="hero-overlay"></div>
        </div>
    <?php endif; ?>
    
    <div class="hero-content">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-3">
                        <?php if (isset($brewery['logo']) && $brewery['logo']): ?>
                            <img src="<?php echo htmlspecialchars($brewery['logo']); ?>" 
                                 alt="<?php echo htmlspecialchars($brewery['name']); ?> Logo" 
                                 class="brewery-logo-large me-4">
                        <?php endif; ?>
                        
                        <div>
                            <h1 class="text-white mb-2"><?php echo htmlspecialchars($brewery['name']); ?></h1>
                            
                            <div class="mb-2">
                                <?php if (isset($brewery['verified']) && $brewery['verified']): ?>
                                    <span class="badge bg-success me-2">
                                        <i class="fas fa-check-circle me-1"></i>Verified
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($brewery['brewery_type']): ?>
                                    <span class="badge bg-light text-dark">
                                        <?php echo ucfirst($brewery['brewery_type']); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($brewery['city'] || $brewery['state']): ?>
                                <p class="text-white-50 mb-0">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars(trim($brewery['city'] . ', ' . $brewery['state'], ', ')); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 text-md-end">
                    <div class="brewery-stats text-white">
                        <div class="d-flex justify-content-md-end justify-content-start gap-4">
                            <div class="text-center">
                                <div class="h4 mb-0"><?php echo number_format($brewery['follower_count'] ?? 0); ?></div>
                                <small>Followers</small>
                            </div>
                            <div class="text-center">
                                <div class="h4 mb-0"><?php echo number_format($brewery['like_count'] ?? 0); ?></div>
                                <small>Likes</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-light me-2">
                                <i class="fas fa-heart me-1"></i>Like
                            </button>
                            <button class="btn btn-outline-light">
                                <i class="fas fa-user-plus me-1"></i>Follow
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="breweryTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="about-tab" data-bs-toggle="tab" data-bs-target="#about" type="button">
                <i class="fas fa-info-circle me-1"></i>About
            </button>
        </li>
        <?php if (!empty($beerMenu)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="beer-tab" data-bs-toggle="tab" data-bs-target="#beer" type="button">
                    <i class="fas fa-beer me-1"></i>Beer Menu (<?php echo count($beerMenu); ?>)
                </button>
            </li>
        <?php endif; ?>
        <?php if (!empty($foodMenu)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="food-tab" data-bs-toggle="tab" data-bs-target="#food" type="button">
                    <i class="fas fa-utensils me-1"></i>Food Menu (<?php echo count($foodMenu); ?>)
                </button>
            </li>
        <?php endif; ?>
        <?php if (!empty($coupons)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="coupons-tab" data-bs-toggle="tab" data-bs-target="#coupons" type="button">
                    <i class="fas fa-tags me-1"></i>Offers (<?php echo count($coupons); ?>)
                </button>
            </li>
        <?php endif; ?>
    </ul>
    
    <!-- Tab Content -->
    <div class="tab-content" id="breweryTabContent">
        <!-- About Tab -->
        <div class="tab-pane fade show active" id="about" role="tabpanel">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">About <?php echo htmlspecialchars($brewery['name']); ?></h5>
                            
                            <?php if ($brewery['description']): ?>
                                <p class="card-text"><?php echo nl2br(htmlspecialchars($brewery['description'])); ?></p>
                            <?php else: ?>
                                <p class="text-muted">No description available.</p>
                            <?php endif; ?>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h6>Contact Information</h6>
                                    
                                    <?php if ($brewery['address']): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                            <?php echo htmlspecialchars($brewery['address']); ?><br>
                                            <?php echo htmlspecialchars(trim($brewery['city'] . ', ' . $brewery['state'] . ' ' . $brewery['zip'], ', ')); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($brewery['phone']): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-phone me-2 text-muted"></i>
                                            <a href="tel:<?php echo htmlspecialchars($brewery['phone']); ?>">
                                                <?php echo htmlspecialchars($brewery['phone']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($brewery['email']): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-envelope me-2 text-muted"></i>
                                            <a href="mailto:<?php echo htmlspecialchars($brewery['email']); ?>">
                                                <?php echo htmlspecialchars($brewery['email']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($brewery['website']): ?>
                                        <p class="mb-2">
                                            <i class="fas fa-globe me-2 text-muted"></i>
                                            <a href="<?php echo htmlspecialchars($brewery['website']); ?>" target="_blank">
                                                Visit Website
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Social Media</h6>
                                    
                                    <?php if ($brewery['social_links']): ?>
                                        <?php if (!empty($brewery['social_links']['facebook'])): ?>
                                            <p class="mb-2">
                                                <i class="fab fa-facebook me-2 text-primary"></i>
                                                <a href="<?php echo htmlspecialchars($brewery['social_links']['facebook']); ?>" target="_blank">
                                                    Facebook
                                                </a>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($brewery['social_links']['instagram'])): ?>
                                            <p class="mb-2">
                                                <i class="fab fa-instagram me-2 text-danger"></i>
                                                <a href="<?php echo htmlspecialchars($brewery['social_links']['instagram']); ?>" target="_blank">
                                                    Instagram
                                                </a>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($brewery['social_links']['twitter'])): ?>
                                            <p class="mb-2">
                                                <i class="fab fa-twitter me-2 text-info"></i>
                                                <a href="<?php echo htmlspecialchars($brewery['social_links']['twitter']); ?>" target="_blank">
                                                    Twitter
                                                </a>
                                            </p>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <p class="text-muted">No social media links available.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6>Quick Actions</h6>
                            
                            <div class="d-grid gap-2">
                                <a href="listing.php" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Breweries
                                </a>
                                
                                <?php if ($brewery['phone']): ?>
                                    <a href="tel:<?php echo htmlspecialchars($brewery['phone']); ?>" class="btn btn-outline-success">
                                        <i class="fas fa-phone me-1"></i>Call Now
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($brewery['website']): ?>
                                    <a href="<?php echo htmlspecialchars($brewery['website']); ?>" target="_blank" class="btn btn-outline-info">
                                        <i class="fas fa-external-link-alt me-1"></i>Visit Website
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Beer Menu Tab -->
        <?php if (!empty($beerMenu)): ?>
            <div class="tab-pane fade" id="beer" role="tabpanel">
                <div class="row">
                    <?php foreach ($beerMenu as $beer): ?>
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0"><?php echo htmlspecialchars($beer['name']); ?></h6>
                                        <?php if ($beer['featured']): ?>
                                            <span class="badge bg-warning">Featured</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($beer['type']): ?>
                                        <p class="text-muted small mb-2"><?php echo htmlspecialchars($beer['type']); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if ($beer['description']): ?>
                                        <p class="card-text small"><?php echo htmlspecialchars($beer['description']); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="beer-specs">
                                            <?php if ($beer['abv']): ?>
                                                <span class="badge bg-secondary me-1"><?php echo $beer['abv']; ?>% ABV</span>
                                            <?php endif; ?>
                                            <?php if ($beer['ibu']): ?>
                                                <span class="badge bg-secondary"><?php echo $beer['ibu']; ?> IBU</span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($beer['price']): ?>
                                            <strong class="text-primary">$<?php echo number_format($beer['price'], 2); ?></strong>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Food Menu Tab -->
        <?php if (!empty($foodMenu)): ?>
            <div class="tab-pane fade" id="food" role="tabpanel">
                <?php
                $categories = [];
                foreach ($foodMenu as $item) {
                    $category = $item['category'] ?: 'Other';
                    $categories[$category][] = $item;
                }
                ?>
                
                <?php foreach ($categories as $category => $items): ?>
                    <div class="mb-4">
                        <h5 class="border-bottom pb-2"><?php echo htmlspecialchars($category); ?></h5>
                        <div class="row">
                            <?php foreach ($items as $item): ?>
                                <div class="col-lg-6 mb-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                            <?php if ($item['description']): ?>
                                                <p class="text-muted small mb-0"><?php echo htmlspecialchars($item['description']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($item['price']): ?>
                                            <strong class="text-primary ms-3">$<?php echo number_format($item['price'], 2); ?></strong>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Coupons Tab -->
        <?php if (!empty($coupons)): ?>
            <div class="tab-pane fade" id="coupons" role="tabpanel">
                <div class="row">
                    <?php foreach ($coupons as $coupon): ?>
                        <div class="col-lg-6 mb-4">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title mb-0"><?php echo htmlspecialchars($coupon['description']); ?></h6>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($coupon['discount_value']); ?></span>
                                    </div>
                                    
                                    <div class="coupon-code mb-3">
                                        <label class="form-label small">Coupon Code:</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($coupon['code']); ?>" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo htmlspecialchars($coupon['code']); ?>')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <p class="text-muted small mb-0">
                                        <i class="fas fa-calendar me-1"></i>
                                        Expires: <?php echo formatDate($coupon['expiry_date']); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // You could add a toast notification here
        alert('Coupon code copied to clipboard!');
    });
}
</script>

<?php include '../includes/footer.php'; ?>
