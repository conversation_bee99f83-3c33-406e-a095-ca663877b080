﻿<?php
echo "<h1>PDO MySQL Extension Test</h1>";

echo "<h2>Extension Status:</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";
echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";
echo "<p>MySQLi: " . (extension_loaded('mysqli') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";

echo "<h2>PHP Version:</h2>";
echo "<p>" . phpversion() . "</p>";

echo "<h2>Configuration File:</h2>";
echo "<p>" . php_ini_loaded_file() . "</p>";

echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color:green'>âœ… PDO MySQL connection successful</p>";
    
    // Test database
    try {
        $pdo->exec("USE beersty_db");
        echo "<p style='color:green'>âœ… Connected to beersty_db database</p>";
        
        // Test users table
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color:green'>âœ… Users table exists</p>";
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch();
            echo "<p>Users in database: <strong>" . $result['count'] . "</strong></p>";
        } else {
            echo "<p style='color:orange'>âš ï¸ Users table does not exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color:orange'>âš ï¸ Database 'beersty_db' not found: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color:red'>âŒ PDO MySQL connection failed: " . $e->getMessage() . "</p>";
}

echo "<h2>All Loaded Extensions:</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<div style='max-height:200px; overflow-y:auto; border:1px solid #ccc; padding:10px;'>";
foreach ($extensions as $ext) {
    $color = (strpos($ext, 'pdo') !== false || strpos($ext, 'mysql') !== false) ? 'color:green; font-weight:bold;' : '';
    echo "<div style='$color'>$ext</div>";
}
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
</style>
