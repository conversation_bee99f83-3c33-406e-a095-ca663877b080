-- Digital Beer Board Demo Setup
-- Creates all necessary tables and demo data

-- Create digital_boards table
CREATE TABLE IF NOT EXISTS digital_boards (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    board_id VARCHAR(100) NOT NULL,
    settings JSON NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_brewery_board (brewery_id, board_id)
);

-- Create beer_menu table
CREATE TABLE IF NOT EXISTS beer_menu (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    brewery_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    style_id VARCHAR(36) NULL,
    abv DECIMAL(4,2) NULL,
    ibu INT NULL,
    srm INT NULL,
    price DECIMAL(8,2) NULL,
    tap_number INT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create beer_styles table if it doesn't exist
CREATE TABLE IF NOT EXISTS beer_styles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    category VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert demo beer styles
INSERT IGNORE INTO beer_styles (id, name, description, category) VALUES
('style-1', 'IPA', 'India Pale Ale - Hoppy and bitter', 'Ale'),
('style-2', 'Wheat Beer', 'Light and refreshing wheat beer', 'Wheat'),
('style-3', 'Stout', 'Dark and rich stout', 'Stout'),
('style-4', 'Lager', 'Crisp and clean lager', 'Lager'),
('style-5', 'Porter', 'Dark and malty porter', 'Porter'),
('style-6', 'Pilsner', 'Light and hoppy pilsner', 'Lager'),
('style-7', 'Brown Ale', 'Malty brown ale', 'Ale'),
('style-8', 'Pale Ale', 'Balanced pale ale', 'Ale');

-- Create demo brewery if it doesn't exist
INSERT IGNORE INTO breweries (id, name, city, state, description, brewery_type, claimed, verified, created_at) VALUES
('demo-brewery-1', 'Demo Craft Brewery', 'Demo City', 'CA', 'A demonstration brewery for testing the digital beer board system', 'micro', 1, 1, NOW());

-- Create demo user if it doesn't exist (password: demo123)
INSERT IGNORE INTO users (id, email, password, role, brewery_id, created_at) VALUES
('demo-user-1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'brewery', 'demo-brewery-1', NOW());

-- Create demo profile
INSERT IGNORE INTO profiles (id, first_name, last_name, username, email, created_at) VALUES
('demo-user-1', 'Demo', 'Brewer', 'demobrewer', '<EMAIL>', NOW());

-- Insert demo beer menu items
INSERT IGNORE INTO beer_menu (id, brewery_id, name, description, style_id, abv, ibu, srm, price, tap_number, is_available) VALUES
('beer-1', 'demo-brewery-1', 'Hoppy IPA', 'Our signature India Pale Ale with citrus and pine notes. Brewed with Cascade, Centennial, and Simcoe hops.', 'style-1', 6.8, 68, 8, 8.50, 1, 1),
('beer-2', 'demo-brewery-1', 'Golden Wheat', 'Light and refreshing wheat beer perfect for any season. Smooth and creamy with a hint of citrus.', 'style-2', 4.5, 12, 4, 6.00, 2, 1),
('beer-3', 'demo-brewery-1', 'Midnight Stout', 'Rich and creamy stout with notes of chocolate, coffee, and vanilla. A perfect winter warmer.', 'style-3', 7.2, 35, 40, 9.00, 3, 1),
('beer-4', 'demo-brewery-1', 'Crisp Lager', 'Our most popular beer - a crisp and clean lager that goes with everything. Light and refreshing.', 'style-4', 4.8, 18, 3, 5.50, 4, 1),
('beer-5', 'demo-brewery-1', 'Robust Porter', 'Dark and malty porter with hints of caramel and toffee. Smooth finish with a touch of roasted barley.', 'style-5', 6.2, 28, 32, 7.50, 5, 1),
('beer-6', 'demo-brewery-1', 'Czech Pilsner', 'Traditional Czech-style pilsner with noble hops. Crisp, clean, and perfectly balanced.', 'style-6', 5.2, 35, 4, 7.00, 6, 1),
('beer-7', 'demo-brewery-1', 'Nutty Brown', 'Malty brown ale with notes of nuts and caramel. Medium-bodied with a smooth finish.', 'style-7', 5.8, 22, 18, 6.50, 7, 1),
('beer-8', 'demo-brewery-1', 'American Pale', 'Balanced American pale ale with citrus hop character. Perfect introduction to craft beer.', 'style-8', 5.5, 42, 6, 6.75, 8, 1),
('beer-9', 'demo-brewery-1', 'Seasonal Special', 'Our rotating seasonal beer - currently a pumpkin spice ale with cinnamon and nutmeg.', 'style-7', 6.0, 25, 12, 8.00, NULL, 1),
('beer-10', 'demo-brewery-1', 'House Blend', 'A unique blend of our porter and IPA - surprisingly delicious and complex.', 'style-5', 6.5, 45, 20, 7.75, NULL, 1);

-- Create demo digital board
INSERT IGNORE INTO digital_boards (id, brewery_id, board_id, settings, is_active) VALUES
('board-1', 'demo-brewery-1', 'demo-board-12345', 
JSON_OBJECT(
    'theme', 'dark',
    'layout', 'grid',
    'refresh_interval', 300,
    'show_prices', true,
    'show_descriptions', true,
    'ticker_message', 'Welcome to Demo Craft Brewery! Try our award-winning beers fresh from the tap!',
    'auto_rotate', false,
    'display_time', 10
), 1);

-- Update users table to ensure brewery_id is set for demo user
UPDATE users SET brewery_id = 'demo-brewery-1' WHERE email = '<EMAIL>';

-- Create additional demo users for testing
INSERT IGNORE INTO users (id, email, password, role, brewery_id, created_at) VALUES
('admin-user-1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NULL, NOW());

INSERT IGNORE INTO profiles (id, first_name, last_name, username, email, created_at) VALUES
('admin-user-1', 'Admin', 'User', 'admin', '<EMAIL>', NOW());

-- Show setup completion message
SELECT 'Digital Beer Board Demo Setup Complete!' as message,
       '<NAME_EMAIL> / demo123 for brewery user' as brewery_login,
       '<NAME_EMAIL> / demo123 for admin user' as admin_login,
       'Visit /business/digital-board/index.php to start' as next_step;
