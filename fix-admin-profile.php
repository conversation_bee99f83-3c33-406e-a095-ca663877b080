<?php
// Fix Admin Profile Script
echo "=== Fixing Admin Profile ===" . PHP_EOL;

try {
    $pdo = new PDO('mysql:host=localhost;dbname=beersty_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if admin profile exists
    $stmt = $pdo->prepare('SELECT * FROM profiles WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($profile) {
        echo "✅ Admin profile already exists" . PHP_EOL;
        echo "Role: " . $profile['role'] . PHP_EOL;
    } else {
        echo "❌ Admin profile missing. Creating..." . PHP_EOL;
        
        // Get admin user ID
        $stmt = $pdo->prepare('SELECT id FROM users WHERE email = ?');
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            $userId = $user['id'];
            echo "Found user ID: $userId" . PHP_EOL;
            
            // Create admin profile
            $stmt = $pdo->prepare('INSERT INTO profiles (id, email, role, first_name, last_name, username, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())');
            $result = $stmt->execute([$userId, '<EMAIL>', 'admin', 'Admin', 'User', 'admin']);
            
            if ($result) {
                echo "✅ Admin profile created successfully!" . PHP_EOL;
            } else {
                echo "❌ Failed to create profile" . PHP_EOL;
            }
        } else {
            echo "❌ Admin user not found!" . PHP_EOL;
        }
    }
    
    // Verify the fix
    echo "\n--- Verification ---" . PHP_EOL;
    $stmt = $pdo->prepare('SELECT u.email, p.role, p.first_name, p.last_name FROM users u LEFT JOIN profiles p ON u.id = p.id WHERE u.email = ?');
    $stmt->execute(['<EMAIL>']);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && $result['role']) {
        echo "✅ Login should work now!" . PHP_EOL;
        echo "Email: " . $result['email'] . PHP_EOL;
        echo "Role: " . $result['role'] . PHP_EOL;
        echo "Name: " . $result['first_name'] . " " . $result['last_name'] . PHP_EOL;
    } else {
        echo "❌ Profile still missing" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
