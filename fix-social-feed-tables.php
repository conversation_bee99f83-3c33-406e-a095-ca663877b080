<?php
require_once 'config/config.php';

echo "<h2>Fixing Social Feed Database Tables</h2>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if user_activities table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_activities'");
    $userActivitiesExists = $stmt->rowCount() > 0;
    
    if (!$userActivitiesExists) {
        echo "<p>❌ user_activities table missing. Creating...</p>";
        $sql = "
        CREATE TABLE user_activities (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            activity_type VARCHAR(100) NOT NULL,
            target_type VARCHAR(50) NULL,
            target_id VARCHAR(36) NULL,
            metadata JSON NULL,
            is_public BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_activities_user_id (user_id),
            INDEX idx_user_activities_type (activity_type),
            INDEX idx_user_activities_created (created_at)
        )";
        $conn->exec($sql);
        echo "<p>✅ user_activities table created</p>";
    } else {
        echo "<p>✅ user_activities table exists</p>";
    }
    
    // Check if user_follows table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_follows'");
    $userFollowsExists = $stmt->rowCount() > 0;
    
    if (!$userFollowsExists) {
        echo "<p>❌ user_follows table missing. Creating...</p>";
        $sql = "
        CREATE TABLE user_follows (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            follower_id VARCHAR(36) NOT NULL,
            following_id VARCHAR(36) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_follow (follower_id, following_id)
        )";
        $conn->exec($sql);
        echo "<p>✅ user_follows table created</p>";
    } else {
        echo "<p>✅ user_follows table exists</p>";
    }
    
    // Check if beer_checkins table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'beer_checkins'");
    $beerCheckinsExists = $stmt->rowCount() > 0;
    
    if (!$beerCheckinsExists) {
        echo "<p>❌ beer_checkins table missing. Creating...</p>";
        $sql = "
        CREATE TABLE beer_checkins (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            beer_id VARCHAR(36) NOT NULL,
            brewery_id VARCHAR(36) NOT NULL,
            rating_id VARCHAR(36) NULL,
            checkin_location VARCHAR(255) NULL,
            checkin_latitude DECIMAL(10, 8) NULL,
            checkin_longitude DECIMAL(11, 8) NULL,
            serving_style ENUM('draft', 'bottle', 'can', 'growler', 'other') NULL,
            checkin_comment TEXT NULL,
            photos JSON NULL,
            is_public BOOLEAN DEFAULT TRUE,
            like_count INT DEFAULT 0,
            comment_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
            FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
        )";
        $conn->exec($sql);
        echo "<p>✅ beer_checkins table created</p>";
    } else {
        echo "<p>✅ beer_checkins table exists</p>";
    }
    
    // Check if beer_styles table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'beer_styles'");
    $beerStylesExists = $stmt->rowCount() > 0;
    
    if (!$beerStylesExists) {
        echo "<p>❌ beer_styles table missing. Creating...</p>";
        $sql = "
        CREATE TABLE beer_styles (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            name VARCHAR(100) NOT NULL,
            category VARCHAR(50) NULL,
            description TEXT NULL,
            abv_min DECIMAL(4,2) NULL,
            abv_max DECIMAL(4,2) NULL,
            ibu_min INT NULL,
            ibu_max INT NULL,
            srm_min INT NULL,
            srm_max INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        echo "<p>✅ beer_styles table created</p>";
        
        // Insert some basic beer styles
        $styles = [
            ['IPA', 'Ale', 'India Pale Ale'],
            ['Lager', 'Lager', 'Light, crisp beer'],
            ['Stout', 'Ale', 'Dark, rich beer'],
            ['Wheat Beer', 'Ale', 'Light, refreshing wheat beer'],
            ['Porter', 'Ale', 'Dark, malty beer']
        ];
        
        $stmt = $conn->prepare("INSERT INTO beer_styles (name, category, description) VALUES (?, ?, ?)");
        foreach ($styles as $style) {
            $stmt->execute($style);
        }
        echo "<p>✅ Basic beer styles inserted</p>";
    } else {
        echo "<p>✅ beer_styles table exists</p>";
    }
    
    // Check if beer_ratings table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'beer_ratings'");
    $beerRatingsExists = $stmt->rowCount() > 0;
    
    if (!$beerRatingsExists) {
        echo "<p>❌ beer_ratings table missing. Creating...</p>";
        $sql = "
        CREATE TABLE beer_ratings (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            beer_id VARCHAR(36) NOT NULL,
            brewery_id VARCHAR(36) NOT NULL,
            overall_rating DECIMAL(2,1) NOT NULL,
            appearance_rating DECIMAL(2,1) NULL,
            aroma_rating DECIMAL(2,1) NULL,
            taste_rating DECIMAL(2,1) NULL,
            mouthfeel_rating DECIMAL(2,1) NULL,
            review_text TEXT NULL,
            is_public BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (beer_id) REFERENCES beer_menu(id) ON DELETE CASCADE,
            FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_beer_rating (user_id, beer_id)
        )";
        $conn->exec($sql);
        echo "<p>✅ beer_ratings table created</p>";
    } else {
        echo "<p>✅ beer_ratings table exists</p>";
    }
    
    echo "<h3>✅ All required tables are now available!</h3>";
    echo "<p><a href='/social/feed.php'>Go to Activity Feed</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
