# WAMP Database Setup Script
# PowerShell script to create Beersty database via phpMyAdmin web interface

Write-Host "=== WAMP Database Setup ===" -ForegroundColor Green

# Configuration
$phpMyAdminUrl = "http://localhost/phpmyadmin"
$dbName = "beersty_db"
$username = "root"
$password = ""

Write-Host "1. Testing phpMyAdmin connection..." -ForegroundColor Yellow

try {
    # Test phpMyAdmin accessibility
    $testResponse = Invoke-WebRequest -Uri "$phpMyAdminUrl/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ phpMyAdmin is accessible (Status: $($testResponse.StatusCode))" -ForegroundColor Green
    
    Write-Host "2. Creating database via web request..." -ForegroundColor Yellow
    
    # Create database using phpMyAdmin's create database endpoint
    $createDbUrl = "$phpMyAdminUrl/server_databases.php"
    $createDbData = @{
        'db' = $dbName
        'collation' = 'utf8mb4_unicode_ci'
        'token' = ''
        'ajax_request' = 'true'
    }
    
    # Get session and token first
    $session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
    $mainPage = Invoke-WebRequest -Uri "$phpMyAdminUrl/" -SessionVariable session -UseBasicParsing
    
    Write-Host "3. Attempting to create database: $dbName" -ForegroundColor Cyan
    
    # Try to create database
    $createResponse = Invoke-WebRequest -Uri $createDbUrl -Method POST -Body $createDbData -WebSession $session -UseBasicParsing
    
    if ($createResponse.StatusCode -eq 200) {
        Write-Host "✅ Database creation request sent successfully" -ForegroundColor Green
    }
    
    Write-Host "4. Verifying database creation..." -ForegroundColor Yellow
    
    # Check if database exists by trying to access it
    $dbCheckUrl = "$phpMyAdminUrl/index.php?route=/database/structure&db=$dbName"
    $dbCheckResponse = Invoke-WebRequest -Uri $dbCheckUrl -WebSession $session -UseBasicParsing
    
    if ($dbCheckResponse.Content -like "*$dbName*") {
        Write-Host "✅ Database '$dbName' created successfully!" -ForegroundColor Green
        
        Write-Host "5. Creating tables..." -ForegroundColor Yellow
        
        # SQL for basic tables
        $sql = @"
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

CREATE TABLE IF NOT EXISTS profiles (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) DEFAULT 'customer',
    brewery_id VARCHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS breweries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip VARCHAR(20),
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    description TEXT,
    brewery_type VARCHAR(50) DEFAULT 'micro',
    verified BOOLEAN DEFAULT 0,
    claimed BOOLEAN DEFAULT 0,
    claimable BOOLEAN DEFAULT 1,
    follower_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    external_id VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
"@
        
        # Execute SQL via phpMyAdmin
        $sqlUrl = "$phpMyAdminUrl/index.php?route=/sql"
        $sqlData = @{
            'sql_query' = $sql
            'db' = $dbName
            'ajax_request' = 'true'
        }
        
        $sqlResponse = Invoke-WebRequest -Uri $sqlUrl -Method POST -Body $sqlData -WebSession $session -UseBasicParsing
        
        if ($sqlResponse.StatusCode -eq 200) {
            Write-Host "✅ Tables created successfully!" -ForegroundColor Green
        }
        
        Write-Host "6. Creating admin user..." -ForegroundColor Yellow
        
        # Create admin user
        $adminSql = @"
INSERT INTO users (id, email, password_hash) VALUES 
('admin-user-id', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

INSERT INTO profiles (id, email, role) VALUES 
('admin-user-id', '<EMAIL>', 'admin');
"@
        
        $adminData = @{
            'sql_query' = $adminSql
            'db' = $dbName
            'ajax_request' = 'true'
        }
        
        $adminResponse = Invoke-WebRequest -Uri $sqlUrl -Method POST -Body $adminData -WebSession $session -UseBasicParsing
        
        if ($adminResponse.StatusCode -eq 200) {
            Write-Host "✅ Admin user created!" -ForegroundColor Green
        }
        
        Write-Host "" -ForegroundColor White
        Write-Host "🎉 Database Setup Complete!" -ForegroundColor Green
        Write-Host "Database: $dbName" -ForegroundColor Cyan
        Write-Host "Admin Email: <EMAIL>" -ForegroundColor Cyan
        Write-Host "Admin Password: admin123" -ForegroundColor Cyan
        Write-Host "phpMyAdmin: $phpMyAdminUrl" -ForegroundColor Cyan
        
    } else {
        Write-Host "❌ Database creation may have failed" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure WAMP is running and phpMyAdmin is accessible" -ForegroundColor Yellow
}

Write-Host "" -ForegroundColor White
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open phpMyAdmin: $phpMyAdminUrl" -ForegroundColor White
Write-Host "2. Verify database '$dbName' exists" -ForegroundColor White
Write-Host "3. Test your application: http://localhost:8000" -ForegroundColor White
