<?php
/**
 * Simple Database Setup Page
 * Works with PHP built-in server on port 8000
 */

echo "<h1>🛠️ Beersty Database Setup</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_GET['action'] ?? 'show_options';

if ($action === 'setup_mysql') {
    echo "<h2>🐬 Setting up MySQL Database</h2>";
    
    try {
        // Try to connect to MySQL server
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $dbName = 'beersty_db';
        
        $dsn = "mysql:host=$host;charset=utf8mb4";
        $conn = new PDO($dsn, $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ MySQL server connection successful</p>";
        
        // Create database
        $conn->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✅ Database '$dbName' created</p>";
        
        // Connect to the specific database
        $dsn = "mysql:host=$host;dbname=$dbName;charset=utf8mb4";
        $conn = new PDO($dsn, $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create basic tables
        $sql = "
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        );
        
        CREATE TABLE IF NOT EXISTS profiles (
            id VARCHAR(36) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            role VARCHAR(50) DEFAULT 'customer',
            brewery_id VARCHAR(36) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES users(id)
        );
        
        CREATE TABLE IF NOT EXISTS breweries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            state VARCHAR(50),
            zip VARCHAR(20),
            phone VARCHAR(50),
            email VARCHAR(255),
            website VARCHAR(255),
            description TEXT,
            brewery_type VARCHAR(50) DEFAULT 'micro',
            verified BOOLEAN DEFAULT 0,
            claimed BOOLEAN DEFAULT 0,
            claimable BOOLEAN DEFAULT 1,
            follower_count INT DEFAULT 0,
            like_count INT DEFAULT 0,
            external_id VARCHAR(100),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
        ";
        
        $conn->exec($sql);
        echo "<p>✅ Database tables created</p>";
        
        // Create admin user
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)");
        $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
        
        $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin') ON DUPLICATE KEY UPDATE role = 'admin'");
        $stmt->execute([$userId, '<EMAIL>']);
        
        echo "<p>✅ Admin user created</p>";
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 MySQL Setup Complete!</h3>";
        echo "<p>Your database is now ready to use with MySQL.</p>";
        echo "<ul>";
        echo "<li><strong>Database:</strong> $dbName</li>";
        echo "<li><strong>Admin email:</strong> <EMAIL></li>";
        echo "<li><strong>Admin password:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ MySQL Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Make sure MySQL is running in XAMPP.</p>";
        echo "</div>";
    }
    
} else {
    echo "<h2>🔧 Database Setup Options</h2>";
    echo "<p>Choose your database setup method:</p>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=setup_mysql' style='display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>🐬 Setup MySQL Database</a>";
    echo "</div>";
    
    echo "<h3>📋 Prerequisites:</h3>";
    echo "<ul>";
    echo "<li>XAMPP installed with MySQL running</li>";
    echo "<li>MySQL accessible on localhost:3306</li>";
    echo "<li>Default MySQL root user (no password)</li>";
    echo "</ul>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='color: #007bff;'>Try Login Now</a></li>";
echo "<li><a href='test-database-connection.php' style='color: #007bff;'>Test Database Connection</a></li>";
echo "<li><a href='/' style='color: #007bff;'>Back to Homepage</a></li>";
echo "</ul>";
?>
