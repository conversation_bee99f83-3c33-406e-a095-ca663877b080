<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            display: none;
            margin-top: 2px;
        }
        
        .suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            color: #212529;
            background-color: #ffffff;
        }
        
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Simple Location Search Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="position-relative">
                    <input type="text" 
                           id="simple-search" 
                           class="form-control" 
                           placeholder="Type a city name..."
                           autocomplete="off">
                    <div id="simple-suggestions" class="suggestions"></div>
                </div>
                
                <div class="mt-3">
                    <button id="test-btn" class="btn btn-primary">Test API</button>
                    <button id="clear-btn" class="btn btn-secondary">Clear</button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Results</h3>
                <div id="results" class="border p-3" style="height: 300px; overflow-y: auto;">
                    <p>Type in the search box or click "Test API" to see results...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const searchInput = document.getElementById('simple-search');
        const suggestionsDiv = document.getElementById('simple-suggestions');
        const resultsDiv = document.getElementById('results');
        const testBtn = document.getElementById('test-btn');
        const clearBtn = document.getElementById('clear-btn');
        
        let searchTimeout;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function searchLocations(query) {
            if (query.length < 2) {
                suggestionsDiv.style.display = 'none';
                return;
            }
            
            log(`Searching for: "${query}"`);
            
            // Show loading
            suggestionsDiv.innerHTML = '<div class="suggestion-item">Searching...</div>';
            suggestionsDiv.style.display = 'block';
            
            fetch(`/api/location-search.php?q=${encodeURIComponent(query)}&limit=5`)
                .then(response => {
                    log(`API Response Status: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log(`API Response: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success && data.suggestions && data.suggestions.length > 0) {
                        displaySuggestions(data.suggestions);
                    } else {
                        suggestionsDiv.innerHTML = '<div class="suggestion-item">No results found</div>';
                    }
                })
                .catch(error => {
                    log(`<span style="color: red;">Error: ${error.message}</span>`);
                    suggestionsDiv.innerHTML = '<div class="suggestion-item" style="color: red;">Search failed</div>';
                });
        }
        
        function displaySuggestions(suggestions) {
            const html = suggestions.map(suggestion => {
                return `
                    <div class="suggestion-item" data-name="${suggestion.name}">
                        ${suggestion.name}
                        ${suggestion.distance ? ` (${suggestion.distance.toFixed(1)} mi)` : ''}
                    </div>
                `;
            }).join('');
            
            suggestionsDiv.innerHTML = html;
            
            // Add click handlers
            suggestionsDiv.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    const name = item.dataset.name;
                    searchInput.value = name;
                    suggestionsDiv.style.display = 'none';
                    log(`Selected: ${name}`);
                });
            });
            
            suggestionsDiv.style.display = 'block';
            log(`Displayed ${suggestions.length} suggestions`);
        }
        
        // Search input event
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchLocations(query);
            }, 300);
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.position-relative')) {
                suggestionsDiv.style.display = 'none';
            }
        });
        
        // Test button
        testBtn.addEventListener('click', () => {
            log('Testing API directly...');
            fetch('/api/location-search.php?q=san&limit=3')
                .then(response => response.json())
                .then(data => {
                    log('Direct API Test: ' + JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    log(`<span style="color: red;">Direct API Error: ${error.message}</span>`);
                });
        });
        
        // Clear button
        clearBtn.addEventListener('click', () => {
            searchInput.value = '';
            suggestionsDiv.style.display = 'none';
            resultsDiv.innerHTML = '<p>Cleared. Type in the search box or click "Test API" to see results...</p>';
        });
        
        log('Simple search test loaded. Try typing "san", "chi", or "new"');
    </script>
</body>
</html>
