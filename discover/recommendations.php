<?php
require_once '../config/config.php';
require_once '../includes/RecommendationService.php';

// Require login
requireLogin();

$pageTitle = 'Recommendations - ' . APP_NAME;
$additionalCSS = ['/assets/css/recommendations.css'];

$user = getCurrentUser();
$userId = $user['id'];

$beerRecommendations = [];
$breweryRecommendations = [];
$trendingBeers = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Initialize recommendation service
    $recommendationService = new RecommendationService($conn);
    
    // Get recommendations
    $beerRecommendations = $recommendationService->getBeerRecommendations($userId, 12);
    $breweryRecommendations = $recommendationService->getBreweryRecommendations($userId, 6);
    $trendingBeers = $recommendationService->getTrendingBeers(8);
    
    // Get user's rating statistics for personalization info
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_ratings,
            COUNT(DISTINCT bm.beer_style_id) as styles_tried,
            COUNT(DISTINCT bm.brewery_id) as breweries_tried
        FROM beer_ratings br
        JOIN beer_menu bm ON br.beer_id = bm.id
        WHERE br.user_id = ?
    ");
    $stmt->execute([$userId]);
    $userStats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Recommendations page error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading recommendations.';
}

include '../includes/header.php';
?>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-magic me-3"></i>Recommendations for You
            </h1>
            <p class="lead text-muted">
                Discover new beers and breweries tailored to your taste
            </p>
        </div>
    </div>
    
    <!-- Personalization Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card personalization-card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo number_format($userStats['total_ratings'] ?? 0); ?></div>
                                <div class="stat-label">Beers Rated</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo number_format($userStats['styles_tried'] ?? 0); ?></div>
                                <div class="stat-label">Styles Explored</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo number_format($userStats['breweries_tried'] ?? 0); ?></div>
                                <div class="stat-label">Breweries Tried</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="personalization-info">
                                <i class="fas fa-brain fa-2x text-primary mb-2"></i>
                                <div class="small text-muted">
                                    Recommendations powered by your taste profile and community preferences
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Beer Recommendations -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="section-header d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title">
                    <i class="fas fa-beer me-2 text-warning"></i>Beers You Might Love
                </h3>
                <a href="/beers/discover.php" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>Explore All Beers
                </a>
            </div>
            
            <?php if (empty($beerRecommendations)): ?>
                <div class="empty-state">
                    <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                    <h5>No recommendations yet</h5>
                    <p class="text-muted mb-4">
                        Rate a few beers to get personalized recommendations!
                    </p>
                    <a href="/beers/discover.php" class="btn btn-primary">
                        <i class="fas fa-star me-2"></i>Start Rating Beers
                    </a>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($beerRecommendations as $beer): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card beer-card h-100">
                                <div class="card-body">
                                    <!-- Beer Image -->
                                    <div class="beer-image mb-3 text-center">
                                        <?php if (!empty($beer['thumbnail'])): ?>
                                            <img src="<?php echo htmlspecialchars($beer['thumbnail']); ?>" 
                                                 alt="<?php echo htmlspecialchars($beer['name']); ?>" 
                                                 class="beer-thumb">
                                        <?php else: ?>
                                            <div class="beer-thumb-placeholder">
                                                <i class="fas fa-beer fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Beer Info -->
                                    <h6 class="beer-name">
                                        <a href="/beers/detail.php?id=<?php echo $beer['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($beer['name']); ?>
                                        </a>
                                    </h6>
                                    
                                    <p class="brewery-name text-muted mb-2">
                                        <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                    </p>
                                    
                                    <?php if (!empty($beer['style_name'])): ?>
                                        <span class="badge bg-primary mb-2"><?php echo htmlspecialchars($beer['style_name']); ?></span>
                                    <?php endif; ?>
                                    
                                    <!-- Beer Stats -->
                                    <div class="beer-stats mb-3">
                                        <?php if (!empty($beer['avg_rating'])): ?>
                                            <div class="rating mb-1">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= round($beer['avg_rating']) ? 'text-warning' : 'text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                                <span class="ms-1"><?php echo number_format($beer['avg_rating'], 1); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <small class="text-muted">
                                            <?php if (!empty($beer['abv'])): ?>
                                                <?php echo number_format($beer['abv'], 1); ?>% ABV
                                            <?php endif; ?>
                                            <?php if (!empty($beer['ibu'])): ?>
                                                • <?php echo $beer['ibu']; ?> IBU
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    
                                    <!-- Recommendation Reason -->
                                    <div class="recommendation-reason mb-3">
                                        <small class="text-info">
                                            <i class="fas fa-lightbulb me-1"></i>
                                            <?php
                                            switch ($beer['recommendation_type']) {
                                                case 'style_based':
                                                    echo 'Based on your favorite styles';
                                                    break;
                                                case 'collaborative':
                                                    echo 'Users with similar taste love this';
                                                    break;
                                                case 'popular':
                                                    echo 'Highly rated by the community';
                                                    break;
                                                case 'location_based':
                                                    echo 'Popular in your area';
                                                    break;
                                                default:
                                                    echo 'Recommended for you';
                                            }
                                            ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <!-- Actions -->
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a href="/beers/rate.php?id=<?php echo $beer['id']; ?>" 
                                           class="btn btn-sm btn-primary flex-fill">
                                            <i class="fas fa-star me-1"></i>Rate
                                        </a>
                                        <a href="/social/checkin.php?beer_id=<?php echo $beer['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary flex-fill">
                                            <i class="fas fa-map-marker-alt me-1"></i>Check In
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Brewery Recommendations -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="section-header d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title">
                    <i class="fas fa-industry me-2 text-success"></i>Breweries to Explore
                </h3>
                <a href="/location/brewery-map.php" class="btn btn-outline-success">
                    <i class="fas fa-map me-2"></i>View Map
                </a>
            </div>
            
            <?php if (empty($breweryRecommendations)): ?>
                <div class="empty-state">
                    <i class="fas fa-industry fa-3x text-muted mb-3"></i>
                    <h5>No brewery recommendations yet</h5>
                    <p class="text-muted">
                        Check in to some breweries to get personalized suggestions!
                    </p>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($breweryRecommendations as $brewery): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card brewery-card h-100">
                                <div class="card-body">
                                    <h5 class="brewery-name">
                                        <a href="/breweries/detail.php?id=<?php echo $brewery['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($brewery['name']); ?>
                                        </a>
                                    </h5>
                                    
                                    <p class="brewery-location text-muted mb-3">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($brewery['city']); ?><?php if ($brewery['state']): ?>, <?php echo htmlspecialchars($brewery['state']); ?><?php endif; ?>
                                    </p>
                                    
                                    <div class="brewery-stats mb-3">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="stat-number"><?php echo number_format($brewery['beer_count']); ?></div>
                                                <div class="stat-label">Beers</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-number">
                                                    <?php echo $brewery['avg_rating'] ? number_format($brewery['avg_rating'], 1) : 'N/A'; ?>
                                                </div>
                                                <div class="stat-label">Rating</div>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-number"><?php echo number_format($brewery['checkin_count']); ?></div>
                                                <div class="stat-label">Check-ins</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a href="/breweries/detail.php?id=<?php echo $brewery['id']; ?>" 
                                           class="btn btn-sm btn-outline-success flex-fill">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <a href="/social/checkin.php?brewery_id=<?php echo $brewery['id']; ?>" 
                                           class="btn btn-sm btn-success flex-fill">
                                            <i class="fas fa-map-marker-alt me-1"></i>Check In
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Trending Beers -->
    <div class="row">
        <div class="col-12">
            <div class="section-header d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title">
                    <i class="fas fa-fire me-2 text-danger"></i>Trending Now
                </h3>
                <span class="badge bg-danger">Hot</span>
            </div>
            
            <?php if (empty($trendingBeers)): ?>
                <div class="empty-state">
                    <i class="fas fa-fire fa-3x text-muted mb-3"></i>
                    <h5>No trending beers yet</h5>
                    <p class="text-muted">
                        Check back soon to see what's popular in the community!
                    </p>
                </div>
            <?php else: ?>
                <div class="row g-3">
                    <?php foreach ($trendingBeers as $index => $beer): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card trending-card">
                                <div class="card-body">
                                    <div class="trending-rank">
                                        <span class="rank-number">#<?php echo $index + 1; ?></span>
                                    </div>
                                    
                                    <h6 class="beer-name">
                                        <a href="/beers/detail.php?id=<?php echo $beer['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($beer['name']); ?>
                                        </a>
                                    </h6>
                                    
                                    <p class="brewery-name text-muted mb-2">
                                        <?php echo htmlspecialchars($beer['brewery_name']); ?>
                                    </p>
                                    
                                    <div class="trending-stats">
                                        <small class="text-success">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            <?php echo number_format($beer['recent_checkins']); ?> recent check-ins
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
