<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Debug</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <style>
        .debug-box {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .theme-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="theme-indicator" id="themeIndicator">
        Light Mode
    </div>

    <div class="container py-5">
        <h1>🔍 Dark Mode Debug Page</h1>
        
        <div class="debug-box">
            <h3>Manual Dark Mode Toggle</h3>
            <button class="btn btn-warning" onclick="toggleDarkMode()">
                <i class="fas fa-moon me-2"></i>Toggle Dark Mode
            </button>
            <button class="btn btn-info" onclick="checkStatus()">
                <i class="fas fa-search me-2"></i>Check Status
            </button>
        </div>
        
        <div class="debug-box">
            <h3>Form Test</h3>
            <form>
                <div class="mb-3">
                    <label for="testEmail" class="form-label">Email</label>
                    <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>">
                </div>
                
                <div class="mb-3">
                    <label for="testPassword" class="form-label">Password</label>
                    <input type="password" class="form-control" id="testPassword" placeholder="Password">
                </div>
                
                <div class="mb-3">
                    <label for="testSelect" class="form-label">Select</label>
                    <select class="form-select" id="testSelect">
                        <option>Choose...</option>
                        <option value="1">Option 1</option>
                        <option value="2">Option 2</option>
                    </select>
                </div>
                
                <button type="button" class="btn btn-primary">Test Button</button>
            </form>
        </div>
        
        <div class="debug-box">
            <h3>Debug Info</h3>
            <div id="debugInfo">
                <p><strong>HTML Class:</strong> <span id="htmlClass"></span></p>
                <p><strong>Body Background:</strong> <span id="bodyBg"></span></p>
                <p><strong>CSS Variables:</strong></p>
                <ul>
                    <li>--bg-primary: <span id="bgPrimary"></span></li>
                    <li>--text-primary: <span id="textPrimary"></span></li>
                </ul>
                <p><strong>LocalStorage:</strong> <span id="localStorage"></span></p>
            </div>
        </div>
    </div>

    <!-- Dark Mode JavaScript -->
    <script src="/assets/js/dark-mode.js"></script>
    
    <script>
        function toggleDarkMode() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark-mode');
            
            if (isDark) {
                html.classList.remove('dark-mode');
                localStorage.setItem('beersty-theme', 'light');
            } else {
                html.classList.add('dark-mode');
                localStorage.setItem('beersty-theme', 'dark');
            }
            
            updateDebugInfo();
        }
        
        function checkStatus() {
            updateDebugInfo();
            
            // Also check if dark mode manager exists
            if (window.darkModeManager) {
                console.log('Dark Mode Manager exists:', window.darkModeManager);
                console.log('Current theme:', window.darkModeManager.getCurrentTheme());
            } else {
                console.log('Dark Mode Manager NOT found');
            }
        }
        
        function updateDebugInfo() {
            const html = document.documentElement;
            const body = document.body;
            const isDark = html.classList.contains('dark-mode');
            
            // Update theme indicator
            const indicator = document.getElementById('themeIndicator');
            indicator.textContent = isDark ? 'Dark Mode' : 'Light Mode';
            indicator.style.background = isDark ? '#28a745' : '#007bff';
            
            // Update debug info
            document.getElementById('htmlClass').textContent = html.className || 'none';
            document.getElementById('bodyBg').textContent = getComputedStyle(body).backgroundColor;
            
            // Get CSS variables
            const styles = getComputedStyle(html);
            document.getElementById('bgPrimary').textContent = styles.getPropertyValue('--bg-primary') || 'not set';
            document.getElementById('textPrimary').textContent = styles.getPropertyValue('--text-primary') || 'not set';
            
            // LocalStorage
            document.getElementById('localStorage').textContent = localStorage.getItem('beersty-theme') || 'not set';
        }
        
        // Update debug info on load and periodically
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
            setInterval(updateDebugInfo, 1000);
        });
        
        // Force enable dark mode for testing
        function forceDarkMode() {
            document.documentElement.classList.add('dark-mode');
            localStorage.setItem('beersty-theme', 'dark');
            updateDebugInfo();
        }
        
        // Add a force button
        setTimeout(() => {
            const forceBtn = document.createElement('button');
            forceBtn.className = 'btn btn-danger ms-2';
            forceBtn.innerHTML = '<i class="fas fa-bolt me-2"></i>Force Dark Mode';
            forceBtn.onclick = forceDarkMode;
            document.querySelector('.debug-box button').parentNode.appendChild(forceBtn);
        }, 1000);
    </script>
</body>
</html>
