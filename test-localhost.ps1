# TEST AND OPEN LOCALHOST
# Quick script to test localhost and open it

Write-Host "🌐 TESTING LOCALHOST..." -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow

# Test different localhost URLs
$TestUrls = @(
    "http://localhost",
    "http://localhost:80", 
    "http://localhost:8080",
    "http://127.0.0.1",
    "http://127.0.0.1:80",
    "http://127.0.0.1:8080"
)

$WorkingUrl = $null

foreach ($url in $TestUrls) {
    Write-Host "Testing $url..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 3 -UseBasicParsing
        Write-Host "✅ $url is WORKING! (Status: $($response.StatusCode))" -ForegroundColor Green
        $WorkingUrl = $url
        break
    } catch {
        Write-Host "❌ $url failed" -ForegroundColor Red
    }
}

if ($WorkingUrl) {
    Write-Host ""
    Write-Host "🎉 SUCCESS! Found working localhost at: $WorkingUrl" -ForegroundColor Green
    Write-Host ""
    Write-Host "Opening URLs..." -ForegroundColor Cyan
    
    # Open the working localhost
    Start-Process $WorkingUrl
    Start-Sleep 2
    
    # Try project URLs
    $ProjectUrls = @(
        "$WorkingUrl/beersty-lovable",
        "$WorkingUrl/beersty-lovable/admin/user-management.php",
        "$WorkingUrl/beersty-lovable/test-pdo-simple.php",
        "$WorkingUrl/phpmyadmin"
    )
    
    foreach ($projectUrl in $ProjectUrls) {
        Write-Host "Opening: $projectUrl" -ForegroundColor White
        Start-Process $projectUrl
        Start-Sleep 1
    }
    
} else {
    Write-Host ""
    Write-Host "❌ NO WORKING LOCALHOST FOUND!" -ForegroundColor Red
    Write-Host ""
    Write-Host "This means Apache is not running. Try:" -ForegroundColor Yellow
    Write-Host "1. .\force-start-apache.ps1" -ForegroundColor White
    Write-Host "2. Start Apache in XAMPP Control Panel" -ForegroundColor White
    Write-Host "3. Run XAMPP as Administrator" -ForegroundColor White
    Write-Host ""
    
    # Try to start XAMPP Control Panel
    $XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
    $XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1
    
    if ($XamppPath) {
        $XamppControl = Join-Path $XamppPath "xampp-control.exe"
        Write-Host "Opening XAMPP Control Panel..." -ForegroundColor Cyan
        Start-Process $XamppControl -WindowStyle Normal
        Write-Host "👆 Click START next to Apache!" -ForegroundColor Yellow
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
