@echo off
echo ========================================
echo    Beersty Clean Installation
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click on this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Checking XAMPP installation...

REM Check if XAMPP is installed
if not exist "C:\xampp\xampp-control.exe" (
    echo ERROR: XAMPP not found at C:\xampp\
    echo.
    echo Please install XAMPP first:
    echo 1. Download from: https://www.apachefriends.org/download.html
    echo 2. Install to C:\xampp
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✓ XAMPP found
echo.

REM Show current htdocs contents
echo Current contents of C:\xampp\htdocs\:
dir "C:\xampp\htdocs" /B
echo.

REM Ask if user wants to clean up
set /p cleanup="Do you want to remove existing applications and install fresh Beersty? (y/n): "
if /i "%cleanup%"=="y" (
    echo.
    echo Cleaning up htdocs directory...
    
    REM Backup existing files
    if exist "C:\xampp\htdocs\backup" (
        rmdir /s /q "C:\xampp\htdocs\backup"
    )
    mkdir "C:\xampp\htdocs\backup"
    
    REM Move existing files to backup (except default XAMPP files)
    for /d %%i in ("C:\xampp\htdocs\*") do (
        if /i not "%%~nxi"=="backup" (
            if /i not "%%~nxi"=="dashboard" (
                if /i not "%%~nxi"=="webalizer" (
                    echo Moving %%~nxi to backup...
                    move "%%i" "C:\xampp\htdocs\backup\" >nul 2>&1
                )
            )
        )
    )
    
    REM Move PHP files to backup
    for %%i in ("C:\xampp\htdocs\*.php") do (
        echo Moving %%~nxi to backup...
        move "%%i" "C:\xampp\htdocs\backup\" >nul 2>&1
    )
    
    echo ✓ Existing applications moved to backup folder
)

echo.
echo Installing Beersty...

REM Create beersty directory
if not exist "C:\xampp\htdocs\beersty" (
    mkdir "C:\xampp\htdocs\beersty"
)

REM Copy all Beersty files
echo Copying Beersty files...
copy "*.php" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1
copy "*.html" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1
copy ".htaccess" "C:\xampp\htdocs\beersty\" /Y >nul 2>&1

REM Copy directories
xcopy "config" "C:\xampp\htdocs\beersty\config\" /E /I /Y /Q >nul 2>&1
xcopy "includes" "C:\xampp\htdocs\beersty\includes\" /E /I /Y /Q >nul 2>&1
xcopy "assets" "C:\xampp\htdocs\beersty\assets\" /E /I /Y /Q >nul 2>&1
xcopy "auth" "C:\xampp\htdocs\beersty\auth\" /E /I /Y /Q >nul 2>&1
xcopy "admin" "C:\xampp\htdocs\beersty\admin\" /E /I /Y /Q >nul 2>&1
xcopy "brewery" "C:\xampp\htdocs\beersty\brewery\" /E /I /Y /Q >nul 2>&1
xcopy "breweries" "C:\xampp\htdocs\beersty\breweries\" /E /I /Y /Q >nul 2>&1
xcopy "database" "C:\xampp\htdocs\beersty\database\" /E /I /Y /Q >nul 2>&1
xcopy "uploads" "C:\xampp\htdocs\beersty\uploads\" /E /I /Y /Q >nul 2>&1

echo ✓ Beersty files copied successfully
echo.

REM Set permissions
echo Setting file permissions...
icacls "C:\xampp\htdocs\beersty\uploads" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
echo ✓ Permissions set
echo.

REM Create database
echo Setting up database...
set /p setupdb="Do you want to set up the database now? (y/n): "
if /i "%setupdb%"=="y" (
    echo Running database setup...
    cd /d "C:\xampp\htdocs\beersty"
    powershell -ExecutionPolicy Bypass -File "setup-database.ps1"
)

echo.
echo ========================================
echo        Installation Complete!
echo ========================================
echo.
echo Your Beersty application is now available at:
echo   http://localhost/beersty/
echo.
echo Test pages:
echo   http://localhost/beersty/simple-test.php
echo   http://localhost/beersty/debug.php
echo.
echo Default admin login:
echo   Email: <EMAIL>
echo   Password: admin123
echo.
echo phpMyAdmin: http://localhost/phpmyadmin/
echo.

set /p openapp="Open Beersty application now? (y/n): "
if /i "%openapp%"=="y" (
    start "" "http://localhost/beersty/"
)

echo.
echo Happy brewing! 🍺
pause
