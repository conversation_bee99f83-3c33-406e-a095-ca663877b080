<?php
/**
 * Create Users Script
 * Creates admin and regular users for Beersty application
 */

require_once 'config/database.php';

echo "<h1>🔧 Creating Beersty Users</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>✅ Database connection successful</h2>";
    
    // Check if tables exist
    $tables = ['users', 'profiles'];
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() == 0) {
            echo "<p>❌ Table '$table' does not exist. Creating...</p>";
            
            if ($table === 'users') {
                $sql = "
                CREATE TABLE users (
                    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL
                )";
                $conn->exec($sql);
                echo "<p>✅ Users table created</p>";
            }
            
            if ($table === 'profiles') {
                $sql = "
                CREATE TABLE profiles (
                    id VARCHAR(36) PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    role VARCHAR(50) DEFAULT 'customer',
                    brewery_id VARCHAR(36) NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
                )";
                $conn->exec($sql);
                echo "<p>✅ Profiles table created</p>";
            }
        } else {
            echo "<p>✅ Table '$table' exists</p>";
        }
    }
    
    // Create users
    $users = [
        [
            'id' => 'admin-user-001',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'role' => 'admin'
        ],
        [
            'id' => 'brewery-user-001', 
            'email' => '<EMAIL>',
            'password' => 'brewery123',
            'role' => 'brewery'
        ],
        [
            'id' => 'customer-user-001',
            'email' => '<EMAIL>', 
            'password' => 'customer123',
            'role' => 'customer'
        ],
        [
            'id' => 'user-regular-001',
            'email' => '<EMAIL>',
            'password' => 'user123', 
            'role' => 'customer'
        ]
    ];
    
    echo "<h2>👥 Creating Users</h2>";
    
    foreach ($users as $userData) {
        // Check if user already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$userData['email']]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p>⚠️ User {$userData['email']} already exists, updating password...</p>";
            
            // Update existing user
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $stmt->execute([$passwordHash, $userData['email']]);
            
            // Update profile
            $stmt = $conn->prepare("UPDATE profiles SET role = ? WHERE email = ?");
            $stmt->execute([$userData['role'], $userData['email']]);
            
        } else {
            echo "<p>➕ Creating user {$userData['email']}...</p>";
            
            // Create new user
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Insert into users table
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$userData['id'], $userData['email'], $passwordHash]);
            
            // Insert into profiles table
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)");
            $stmt->execute([$userData['id'], $userData['email'], $userData['role']]);
            
            echo "<p>✅ User {$userData['email']} created successfully</p>";
        }
    }
    
    // Verify users were created
    echo "<h2>📋 User Verification</h2>";
    $stmt = $conn->prepare("
        SELECT u.email, p.role, u.created_at 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    $allUsers = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Email</th><th>Role</th><th>Created</th></tr>";
    foreach ($allUsers as $user) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Users Created Successfully!</h3>";
    echo "<h4>Login Credentials:</h4>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / admin123</li>";
    echo "<li><strong>Brewery:</strong> <EMAIL> / brewery123</li>";
    echo "<li><strong>Customer:</strong> <EMAIL> / customer123</li>";
    echo "<li><strong>Regular User:</strong> <EMAIL> / user123</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test password verification
    echo "<h2>🔐 Password Verification Test</h2>";
    $testEmail = '<EMAIL>';
    $testPassword = 'admin123';
    
    $stmt = $conn->prepare("SELECT password_hash FROM users WHERE email = ?");
    $stmt->execute([$testEmail]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($testPassword, $user['password_hash'])) {
        echo "<p>✅ Password verification test PASSED for $testEmail</p>";
    } else {
        echo "<p>❌ Password verification test FAILED for $testEmail</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' style='color: #007bff;'>Try Login Now</a></li>";
echo "<li><a href='admin/dashboard.php' style='color: #007bff;'>Admin Dashboard</a></li>";
echo "<li><a href='/' style='color: #007bff;'>Back to Homepage</a></li>";
echo "</ul>";
?>
