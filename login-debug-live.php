<?php
/**
 * Live Login Debug
 * Test login with real form submission
 */

require_once 'config/config.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Live Login Debug</h1>";

// Check if this is a POST request (form submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>📝 Form Submission Detected</h2>";
    
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<p><strong>Submitted Data:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> " . htmlspecialchars($email) . "</li>";
    echo "<li><strong>Password:</strong> " . (empty($password) ? 'EMPTY' : '[' . strlen($password) . ' characters]') . "</li>";
    echo "</ul>";
    
    // Test the exact login process
    try {
        echo "<h3>Step 1: Sanitize Input</h3>";
        $cleanEmail = sanitizeInput($email);
        echo "<p>✅ Email sanitized: " . htmlspecialchars($cleanEmail) . "</p>";
        
        if (empty($cleanEmail) || empty($password)) {
            echo "<p>❌ Empty fields detected</p>";
            echo "<p>Error message would be: 'Please fill in all fields.'</p>";
        } else {
            echo "<h3>Step 2: Database Connection</h3>";
            $db = new Database();
            $conn = $db->getConnection();
            echo "<p>✅ Database connected</p>";
            
            echo "<h3>Step 3: Query User</h3>";
            $stmt = $conn->prepare("
                SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
                FROM users u 
                JOIN profiles p ON u.id = p.id 
                WHERE u.email = ?
            ");
            $stmt->execute([$cleanEmail]);
            $user = $stmt->fetch();
            
            if ($user) {
                echo "<p>✅ User found: " . htmlspecialchars($user['email']) . " (Role: " . htmlspecialchars($user['role']) . ")</p>";
                
                echo "<h3>Step 4: Password Verification</h3>";
                if (password_verify($password, $user['password_hash'])) {
                    echo "<p>✅ Password verification successful!</p>";
                    
                    echo "<h3>Step 5: Update Last Login</h3>";
                    try {
                        $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                        $updateStmt->execute([$user['id']]);
                        echo "<p>✅ Last login updated</p>";
                    } catch (Exception $e) {
                        echo "<p>❌ Last login update failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                    
                    echo "<h3>Step 6: Set Session Variables</h3>";
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['brewery_id'] = $user['brewery_id'];
                    echo "<p>✅ Session variables set</p>";
                    
                    echo "<h3>Step 7: Determine Redirect</h3>";
                    if ($user['role'] === 'admin') {
                        $redirectUrl = url('admin/dashboard.php');
                        echo "<p>✅ Would redirect to admin dashboard: <strong>$redirectUrl</strong></p>";
                    } elseif ($user['role'] === 'brewery') {
                        $redirectUrl = url('brewery/profile.php');
                        echo "<p>✅ Would redirect to brewery profile: <strong>$redirectUrl</strong></p>";
                    } else {
                        $redirectUrl = url('index.php');
                        echo "<p>✅ Would redirect to homepage: <strong>$redirectUrl</strong></p>";
                    }
                    
                    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
                    echo "<h3>🎉 LOGIN SUCCESSFUL!</h3>";
                    echo "<p>You are now logged in as: <strong>" . htmlspecialchars($user['email']) . "</strong></p>";
                    echo "<p>Role: <strong>" . htmlspecialchars($user['role']) . "</strong></p>";
                    echo "<p><a href='$redirectUrl' class='btn btn-primary'>Go to Dashboard</a></p>";
                    echo "</div>";
                    
                } else {
                    echo "<p>❌ Password verification failed!</p>";
                    echo "<p>Error message would be: 'Invalid email or password.'</p>";
                    
                    // Debug password
                    echo "<h4>Password Debug:</h4>";
                    echo "<ul>";
                    echo "<li><strong>Input:</strong> '$password'</li>";
                    echo "<li><strong>Hash:</strong> " . substr($user['password_hash'], 0, 30) . "...</li>";
                    echo "<li><strong>Hash Length:</strong> " . strlen($user['password_hash']) . "</li>";
                    echo "</ul>";
                }
            } else {
                echo "<p>❌ User not found!</p>";
                echo "<p>Error message would be: 'Invalid email or password.'</p>";
                
                // Check if user exists in users table
                $stmt = $conn->prepare("SELECT email FROM users WHERE email = ?");
                $stmt->execute([$cleanEmail]);
                $userExists = $stmt->fetch();
                
                if ($userExists) {
                    echo "<p>⚠️ User exists in users table but not in profiles table!</p>";
                } else {
                    echo "<p>⚠️ User does not exist in users table</p>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ Exception Caught!</h3>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<p><strong>Stack Trace:</strong></p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        echo "</div>";
    }
}

// Show login form
?>

<h2>🔐 Test Login Form</h2>
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <form method="POST" style="max-width: 400px;">
        <div style="margin-bottom: 15px;">
            <label for="email" style="display: block; margin-bottom: 5px;"><strong>Email:</strong></label>
            <input type="email" id="email" name="email" value="<EMAIL>" 
                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="password" style="display: block; margin-bottom: 5px;"><strong>Password:</strong></label>
            <input type="password" id="password" name="password" value="admin123"
                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
        </div>
        
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            🔍 Test Login Process
        </button>
    </form>
</div>

<h2>🔗 Quick Links</h2>
<ul>
    <li><a href="auth/login.php" class="btn btn-primary">Try Real Login Page</a></li>
    <li><a href="check-database-structure.php" class="btn btn-info">Check Database</a></li>
    <li><a href="debug-login-detailed.php" class="btn btn-secondary">Detailed Debug</a></li>
</ul>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
