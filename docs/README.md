# Beersty - Beer Social Network Documentation

## 🍺 Project Overview

Beersty is a comprehensive beer social network platform that combines public social networking features (check-ins, reviews, gamification) with business management tools (profiles, analytics, coupons) and payment systems for ads and merchandise.

## 🎯 Project Goals & Priorities

1. **Primary Focus**: Social networking features and fun user frontend features
2. **Secondary Focus**: Business management features
3. **Implementation**: Phased development building on existing PHP/MySQL/Bootstrap 5 stack

## 🏗️ Technical Stack

- **Backend**: PHP 8+ with MySQL PDO extension
- **Database**: MySQL (beersty_db)
- **Frontend**: Bootstrap 5, JavaScript (ES6+)
- **Development**: XAMPP (preferred over WAMP)
- **Version Control**: Git with GitHub
- **Command Line**: PowerShell (preferred)

## 📁 Project Structure

```
beersty-lovable/
├── admin/                  # Admin dashboard and management
├── api/                    # API endpoints
├── assets/                 # Static assets (CSS, JS, images)
├── brewery/               # Brewery-specific features
├── business/              # Business management tools
├── config/                # Configuration files
├── docs/                  # Documentation (this directory)
├── includes/              # Shared PHP includes
├── layouts-for-reference/ # UI layout templates
├── places/                # Place profiles and search
├── user/                  # User profiles and features
└── [root files]           # Main pages and routing
```

## 🔑 Key Files & Classes

### Configuration
- `config/config.php` - Main configuration and database connection
- `config/Database.php` - Database connection class
- `.htaccess` - URL rewriting rules (production)
- `router.php` - URL routing for development server

### Core Includes
- `includes/header.php` - Site header with navigation
- `includes/footer.php` - Site footer with scripts
- `includes/functions.php` - Utility functions

### Admin System
- `admin/breweries.php` - Main places management (CRUD)
- `admin/beer-menu.php` - Beer menu and styles management
- `admin/user-management.php` - User administration
- `admin/includes/admin-sidebar.php` - Admin navigation

### API Endpoints
- `api/places-management.php` - Places CRUD operations
- `api/beer-styles.php` - Beer styles management
- `api/notifications/count.php` - Notification counts
- `api/messages/count.php` - Message counts

## 🎨 UI Design System

### Color Palette (Brewery Theme)
- **Dark Brown**: #6F4C3E
- **Medium Brown**: #D69A6B  
- **Amber/Gold**: #FFC107
- **Beige**: #F5F5DC
- **Very Dark Brown**: #3B2A2A

### Design Preferences
- ❌ **Avoid**: Blue colors, gray spaces, rounded buttons, visible scrollbars
- ✅ **Prefer**: Brewery colors, straight box buttons, hidden scrollbars, dark mode for admin
- ✅ **Layout**: Full-width tabbed navigation, 4-column card layouts, compact admin interfaces

## 📱 URL Structure & SEO

### SEO-Friendly URLs
- Places: `/places/place-slug` (e.g., `/places/seaside-brewing-co`)
- User profiles: `/user/username`
- Beer details: `/beer/beer-slug`

### Routing System
- **Production**: `.htaccess` URL rewriting
- **Development**: `router.php` with PHP built-in server
- **Start Command**: `php -S localhost:8000 router.php`

## 🗃️ Database Schema

### Key Tables
- `breweries` - Place/business information
- `beer_styles` - Comprehensive beer style definitions
- `beer_menu` - Beer listings for places
- `users` - User accounts and profiles
- `notifications` - User notifications
- `messages` - User messaging system

### Important Fields
- All tables use UUID primary keys
- `is_active` flags for soft deletes
- `created_at`/`updated_at` timestamps
- SEO-friendly slug generation for places

## 🚀 Recent Work Completed (Latest Session)

### Modal System Fixes
- **Problem**: Bootstrap modal backdrop overlays blocking form interactions
- **Solution**: Removed "fade show" effects, fixed z-index issues, eliminated backdrop overlays
- **Files**: `assets/css/admin.css`, `assets/js/places-management.js`, `admin/breweries.php`

### Beer Menu Management Enhancement
- **Before**: Hardcoded 5 basic beer styles
- **After**: Database-driven system with 45+ comprehensive professional beer styles
- **Features**: ADD STYLE modal, category organization, CRUD operations
- **Files**: `admin/beer-menu.php`, `admin/api/beer-styles.php`

### Brewery Type Expansion
- **Added**: "Party Store" to brewery type dropdown
- **Updated**: All validation files to support new type
- **Files**: `admin/breweries.php`, validation files in `admin/`

### PWA & Service Worker Fixes
- **Fixed**: Service worker registration paths
- **Updated**: Manifest.json with correct paths
- **Files**: `assets/js/pwa.js`, `manifest.json`

## 🔧 Development Setup

### XAMPP Configuration
1. **Check MySQL**: `tasklist /FI "IMAGENAME eq mysqld.exe"`
2. **Add PHP to PATH**: `$env:PATH = "C:\xampp\php;$env:PATH"`
3. **Start Server**: `php -S localhost:8000 router.php`
4. **Database**: beersty_db (MySQL)

### Common Commands
```powershell
# Start development server
php -S localhost:8000 router.php

# Check database
php check-database.php

# Git operations (PowerShell preferred)
git add .
git commit -m "Description"
git push origin main
```

## 🎯 Feature Status

### ✅ Completed Features
- Admin dashboard with user management
- Places (breweries) CRUD system
- Beer styles management with comprehensive database
- Modal system fixes for form interactions
- PWA service worker implementation
- SEO-friendly URL structure

### 🚧 In Progress
- Digital beer board for businesses
- Photo gallery management
- Deals/coupons system
- Social features (check-ins, reviews)

### 📋 Planned Features
- Payment systems for ads/merchandise
- Advanced analytics for businesses
- Mobile app development
- Gamification system
