# Beersty Documentation Index

Welcome to the Beersty project documentation. This directory contains comprehensive documentation for developers, AI agents, and team members working on the Beersty beer social network platform.

## 📚 Documentation Structure

### 🎯 For New AI Agents (START HERE)
- **[AI Agent Mindmap](ai-agent-mindmap.md)** - Essential context, user preferences, and critical patterns for AI agents
- **[Recent Work Log](recent-work-log.md)** - Latest development session details and achievements

### 📖 Project Overview
- **[README](README.md)** - Project overview, goals, technical stack, and feature status
- **[File Structure](file-structure.md)** - Detailed directory structure and critical file locations

### 🛠️ Development Resources
- **[Development Guide](development-guide.md)** - Coding standards, patterns, and best practices
- **[Setup Instructions](#setup)** - Environment setup and configuration

## 🚀 Quick Navigation

### For AI Agents Starting a New Session
1. Read **[AI Agent Mindmap](ai-agent-mindmap.md)** first
2. Check **[Recent Work Log](recent-work-log.md)** for latest changes
3. Reference **[File Structure](file-structure.md)** for file locations
4. Follow **[Development Guide](development-guide.md)** for coding standards

### For Human Developers
1. Start with **[README](README.md)** for project overview
2. Follow **[Development Guide](development-guide.md)** for setup and standards
3. Use **[File Structure](file-structure.md)** to navigate codebase
4. Check **[Recent Work Log](recent-work-log.md)** for latest updates

## 🔑 Key Information Summary

### Project Essentials
- **Platform**: Beer social network with business management tools
- **Stack**: PHP 8+, MySQL, Bootstrap 5, JavaScript
- **Environment**: XAMPP, PowerShell, localhost:8000
- **Repository**: https://github.com/XK-Interactive/beersty-lovable.git

### Critical User Preferences
- **Colors**: Brewery theme (browns, amber, beige) - NO BLUE
- **UI**: Straight buttons, hidden scrollbars, dark admin mode
- **Modals**: NO "fade show" effects, NO backdrops
- **Development**: PowerShell commands, package managers only

### Recent Major Work
- ✅ Fixed modal backdrop overlay issues
- ✅ Enhanced beer menu with 45+ comprehensive styles
- ✅ Added "Party Store" brewery type
- ✅ Fixed PWA service worker registration
- ✅ Created comprehensive debugging tools

## 🎯 Most Important Files

### Critical System Files
1. `config/config.php` - Main configuration
2. `admin/breweries.php` - Places management
3. `assets/css/admin.css` - Admin styling and modal fixes
4. `assets/js/places-management.js` - Core admin functionality

### Recently Modified (Test These)
1. `admin/beer-menu.php` - Enhanced beer styles system
2. `admin/api/beer-styles.php` - New CRUD API
3. All validation files in `admin/` - Updated for party_store
4. `assets/js/pwa.js` - Fixed service worker paths

## 🚨 Critical Rules & Patterns

### Modal Development (MUST FOLLOW)
```html
<!-- CORRECT -->
<div class="modal" id="myModal" data-bs-backdrop="false">
```

```css
/* REQUIRED CSS */
.modal-backdrop { display: none !important; }
```

### Color Usage (STRICTLY ENFORCED)
- ✅ Use: #6F4C3E, #D69A6B, #FFC107, #F5F5DC, #3B2A2A
- ❌ Never: Blue colors, gray backgrounds

### Database Patterns
- UUID primary keys: `id VARCHAR(36) PRIMARY KEY DEFAULT (UUID())`
- Soft deletes: `is_active BOOLEAN DEFAULT TRUE`
- Timestamps: `created_at`, `updated_at`

### API Patterns
- Authentication: `requireLogin()`, `requireRole()`
- JSON responses: `{'success': true, 'message': '...'}`
- Error handling: Try-catch with proper HTTP codes

## 🔄 Development Workflow

### Starting Development
```powershell
# Check MySQL
tasklist /FI "IMAGENAME eq mysqld.exe"

# Start server
php -S localhost:8000 router.php
```

### Making Changes
1. Use `codebase-retrieval` for context
2. Use `str-replace-editor` for edits (never overwrite)
3. Test modal interactions thoroughly
4. Verify brewery type validations
5. Check PWA functionality

### Committing Changes
```powershell
git add .
git commit -m "Descriptive message"
git push origin main
```

## 📞 Support & Resources

### Debugging Tools
- Debug modal: `assets/js/debug-modal.js`
- Database checks: Various `check-*.php` files
- Import validation: Files in `admin/` directory

### Testing Endpoints
- Admin: `http://localhost:8000/admin/`
- Places: `http://localhost:8000/admin/breweries.php`
- Beer Menu: `http://localhost:8000/admin/beer-menu.php`

## 📝 Documentation Maintenance

This documentation should be updated when:
- Major features are added or modified
- User preferences change
- Critical bugs are fixed
- New development patterns are established
- Database schema changes occur

---

**Last Updated**: Current session (Modal fixes, Beer menu enhancement, Party store addition)
**Next Review**: After next major feature development

For questions or clarifications, refer to the specific documentation files or check the recent work log for context on latest changes.
