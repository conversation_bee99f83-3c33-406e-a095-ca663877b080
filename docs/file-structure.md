# Beersty File Structure & Important Locations

## 📁 Directory Structure

### `/admin/` - Administration Dashboard
```
admin/
├── api/                    # Admin API endpoints
│   ├── beer-styles.php    # Beer styles CRUD operations
│   └── places-management.php # Places management API
├── includes/              # Admin-specific includes
│   ├── admin-sidebar.php  # Left sidebar navigation
│   └── admin-layout-*.php # Layout components
├── breweries.php          # Main places management (CRITICAL)
├── beer-menu.php          # Beer menu & styles management
├── user-management.php    # User administration
├── category-management.php # Categories for food/beer
└── [other admin files]
```

### `/assets/` - Static Assets
```
assets/
├── css/
│   ├── admin.css          # Admin dashboard styles (CRITICAL)
│   ├── style.css          # Main site styles
│   └── [component styles]
├── js/
│   ├── places-management.js # Places CRUD functionality (CRITICAL)
│   ├── pwa.js             # Service worker registration
│   ├── debug-modal.js     # Debug tools for development
│   └── [feature scripts]
└── images/                # Static images and icons
```

### `/api/` - Public API Endpoints
```
api/
├── messages/
│   └── count.php          # Message count endpoint
├── notifications/
│   └── count.php          # Notification count endpoint
├── places-management.php  # Public places API
└── [other endpoints]
```

### `/config/` - Configuration Files
```
config/
├── config.php             # Main configuration (CRITICAL)
├── Database.php           # Database connection class
└── constants.php          # Application constants
```

### `/includes/` - Shared Components
```
includes/
├── header.php             # Site header with navigation (CRITICAL)
├── footer.php             # Site footer with scripts (CRITICAL)
├── functions.php          # Utility functions
└── auth.php               # Authentication helpers
```

### `/brewery/` - Brewery Features
```
brewery/
├── menu.php               # Brewery menu management
├── digital-board/         # Digital board system
└── [brewery tools]
```

### `/business/` - Business Management
```
business/
├── digital-board/         # Digital board for businesses
└── [business tools]
```

### `/places/` - Place Profiles & Search
```
places/
├── index.php              # Places search/discovery
├── [place-slug]/          # Individual place profiles
└── [place features]
```

### `/user/` - User Features
```
user/
├── profile.php            # User profiles
├── photos.php             # Photo management
└── [user features]
```

## 🔑 Critical Files

### Core System Files
1. **`config/config.php`** - Database connection, constants, auth functions
2. **`includes/header.php`** - Site navigation, user session handling
3. **`includes/footer.php`** - Scripts loading, PWA registration
4. **`router.php`** - Development server routing (root level)

### Admin System (Most Important)
1. **`admin/breweries.php`** - Main places management interface
2. **`admin/beer-menu.php`** - Beer styles and menu management
3. **`assets/css/admin.css`** - All admin styling and modal fixes
4. **`assets/js/places-management.js`** - Places CRUD functionality

### Database Setup
1. **`setup-db-simple.php`** - Database initialization
2. **`database/schema.sql`** - Database schema definition
3. **`database/beer_styles_data.sql`** - Comprehensive beer styles

### API Endpoints
1. **`admin/api/beer-styles.php`** - Beer styles management
2. **`api/places-management.php`** - Places operations
3. **`api/notifications/count.php`** - Notification system
4. **`api/messages/count.php`** - Messaging system

## 🎨 Asset Organization

### CSS Files Priority
1. **`assets/css/admin.css`** - Admin interface, modal fixes, dark mode
2. **`assets/css/style.css`** - Main site styling
3. **Component-specific CSS** - Individual feature styles

### JavaScript Files Priority
1. **`assets/js/places-management.js`** - Core admin functionality
2. **`assets/js/pwa.js`** - Service worker and PWA features
3. **`assets/js/debug-modal.js`** - Development debugging tools
4. **Feature-specific JS** - Individual component scripts

## 🗃️ Database-Related Files

### Schema & Setup
- `database/schema.sql` - Complete database structure
- `setup-db-simple.php` - Automated database setup
- `create-menu-tables.php` - Menu system tables

### Data Files
- `database/beer_styles_data.sql` - 45+ comprehensive beer styles
- Various import scripts in `admin/` for data migration

### Validation Files
- `admin/fix-import.php` - Data import validation
- `admin/csv-import.php` - CSV import with validation
- All validation files updated to support "party_store" brewery type

## 🔧 Development Tools

### Debug & Testing
- `assets/js/debug-modal.js` - Element inspection and debugging
- `admin/test-buttons.php` - UI component testing
- `check-*.php` files - Database and system checks

### Import & Migration
- `admin/brewery-import.php` - Brewery data import
- `admin/run-import-now.php` - Automated import execution
- `admin/test-import.php` - Import testing and validation
