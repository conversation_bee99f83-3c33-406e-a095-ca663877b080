# Beersty Development Guide

## 🚀 Quick Start

### Environment Setup
1. **XAMPP Installation** (preferred over WAMP)
2. **Database**: MySQL with `beersty_db` database
3. **PHP Version**: 8.0+ with PDO extension
4. **Command Line**: PowerShell (user preference)

### Development Server
```powershell
# Check MySQL is running
tasklist /FI "IMAGENAME eq mysqld.exe"

# Add PHP to PATH (if needed)
$env:PATH = "C:\xampp\php;$env:PATH"

# Start development server
php -S localhost:8000 router.php
```

### Database Setup
```powershell
# Initialize database
php setup-db-simple.php

# Check database status
php check-database.php

# Add beer styles (if needed)
php add-beer-styles.php
```

## 🎨 UI Development Rules

### Design System (STRICTLY ENFORCED)
```css
/* Brewery Color Palette */
:root {
    --brewery-dark-brown: #6F4C3E;
    --brewery-medium-brown: #D69A6B;
    --brewery-amber: #FFC107;
    --brewery-beige: #F5F5DC;
    --brewery-very-dark: #3B2A2A;
}
```

### What NOT to Use (User Dislikes)
- ❌ Blue colors in UI design
- ❌ Gray spaces and backgrounds
- ❌ Rounded buttons (prefer straight box style)
- ❌ Visible scrollbars (use hidden/custom)
- ❌ Bootstrap "fade show" modal effects
- ❌ Dark/light mode toggles in brewery admin

### What TO Use (User Preferences)
- ✅ Brewery-themed warm colors
- ✅ Dark mode for admin interfaces
- ✅ Straight box-style buttons
- ✅ Full-width tabbed navigation
- ✅ 4-column card layouts
- ✅ Compact, tight spacing in admin
- ✅ WordPress-style sliding sidebars

## 🔧 Modal Development (CRITICAL)

### Modal Rules (Recently Fixed Issues)
```html
<!-- CORRECT Modal Setup -->
<div class="modal" id="myModal" tabindex="-1" data-bs-backdrop="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal content -->
        </div>
    </div>
</div>
```

### Required CSS (Always Include)
```css
/* Prevent Bootstrap backdrop overlays */
.modal-backdrop {
    display: none !important;
}

/* Ensure modal elements are bright */
.modal * {
    opacity: 1 !important;
    filter: none !important;
}
```

### Modal JavaScript Pattern
```javascript
// Handle form submission
document.getElementById('myForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    
    fetch('api/endpoint.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Success!', 'success');
            // Close modal and reload
            bootstrap.Modal.getInstance(document.getElementById('myModal')).hide();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.error, 'error');
        }
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Submit';
    });
});
```

## 📊 Database Development

### Table Naming Convention
- Use descriptive names: `beer_styles`, `place_beers`, `user_profiles`
- Prefix related tables: `brewery_*`, `user_*`
- Use singular for lookup tables: `beer_style`, not `beer_styles`

### Required Fields Pattern
```sql
CREATE TABLE example_table (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    -- other fields --
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Validation Pattern (PHP)
```php
// Always validate brewery types
$validTypes = [
    'micro', 'nano', 'regional', 'large', 'planning', 
    'bar', 'contract', 'proprietor', 'brewpub', 
    'party_store' // Recently added
];

if (!in_array(strtolower($breweryType), $validTypes)) {
    $breweryType = 'micro'; // Default fallback
}
```

## 🔌 API Development

### API Endpoint Pattern
```php
<?php
require_once '../../config/config.php';
requireLogin();
requireRole('admin'); // or appropriate role

header('Content-Type: application/json');

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            // Create operation
            break;
        case 'GET':
            // Read operation
            break;
        case 'PUT':
            // Update operation
            break;
        case 'DELETE':
            // Delete operation
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
```

## 🎯 Admin Interface Development

### Admin Page Structure
```php
<?php
require_once '../config/config.php';
requireLogin();
requireRole('admin');

$pageTitle = 'Page Title - ' . BUSINESS_APP_NAME;
$additionalCSS = ['../assets/css/admin.css'];

// Get data from database
try {
    $db = new Database();
    $pdo = $db->getConnection();
    // Database queries
} catch (Exception $e) {
    error_log("Error: " . $e->getMessage());
}

include 'includes/admin-layout-start.php';
?>

<!-- Page content -->
<div class="admin-content">
    <!-- Content here -->
</div>

<!-- Modals (remember: no backdrop!) -->
<div class="modal" id="myModal" tabindex="-1" data-bs-backdrop="false">
    <!-- Modal content -->
</div>

<?php include 'includes/admin-layout-end.php'; ?>
```

### Admin Sidebar Pattern
- Use `admin/includes/admin-sidebar.php`
- WordPress-style sliding behavior
- Modern icons with tooltips
- Proper z-index layering
- Toggle button within sidebar (not floating)

## 🔍 Debugging & Testing

### Debug Modal Usage
```javascript
// Available in admin interfaces
// Inspect elements, check z-index, modal states
// File: assets/js/debug-modal.js
```

### Common Debug Commands
```powershell
# Check database connection
php -r "require 'config/config.php'; echo 'DB OK';"

# Test specific functionality
php check-beer-styles.php
php check-database.php

# Git status and operations
git status
git add .
git commit -m "Description"
git push origin main
```

## 📱 PWA Development

### Service Worker Registration
```javascript
// File: assets/js/pwa.js
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
```

### Manifest.json Requirements
- Correct icon paths (relative to root)
- Proper start_url configuration
- Theme colors matching brewery palette

## 🚨 Common Pitfalls to Avoid

1. **Modal Backdrops**: Never use fade effects or default backdrops
2. **Color Usage**: Avoid blue colors and gray backgrounds
3. **Button Styling**: Use straight boxes, not rounded corners
4. **Package Management**: Always use package managers, never edit package files manually
5. **File Editing**: Always use str-replace-editor, never overwrite entire files
6. **Database**: Always use UUID primary keys and soft deletes
7. **Validation**: Update ALL validation files when adding new types

## 🎯 Testing Checklist

### Before Committing
- [ ] Modal forms work without backdrop interference
- [ ] All brewery types validate correctly
- [ ] PWA service worker registers successfully
- [ ] Admin interfaces use dark mode styling
- [ ] No blue colors or rounded buttons used
- [ ] Database operations use proper error handling
- [ ] API endpoints return proper JSON responses

This guide ensures consistent development practices aligned with user preferences and recent fixes.
