# Beersty AI Agent Mindmap & Context

## 🧠 Essential Context for New AI Agents

### Project Identity
- **Name**: Beersty - Beer Social Network
- **Vision**: Comprehensive beer social platform combining social features + business tools
- **Repository**: https://github.com/XK-Interactive/beersty-lovable.git
- **Local Path**: `C:\xkinteractive-github\beersty-lovable`

### User Preferences & Constraints
- **Development Environment**: XAMPP (not WAMP), PowerShell commands
- **Port**: localhost:8000 (consistently working)
- **Database**: MySQL only (no SQLite), beersty_db database
- **Priorities**: Social features > Business features

## 🎨 UI/UX Design Rules (CRITICAL)

### Color Palette (Brewery Theme)
```
Primary Colors:
- Dark Brown: #6F4C3E
- Medium Brown: #D69A6B
- Amber/Gold: #FFC107
- Beige: #F5F5DC
- Very Dark Brown: #3B2A2A
```

### Design Preferences (User is VERY specific about these)
- ❌ **NEVER USE**: Blue colors, gray spaces, rounded buttons, visible scrollbars
- ✅ **ALWAYS USE**: Brewery colors, straight box buttons, hidden scrollbars
- ✅ **Admin Interfaces**: Dark mode preferred
- ✅ **Layouts**: Full-width tabbed navigation, 4-column cards, compact spacing
- ✅ **Navigation**: Clean top menu with avatar dropdowns, no unnecessary toggles

### Modal System (RECENTLY FIXED)
- ❌ **NEVER USE**: Bootstrap "fade show" modal effects (causes form blocking)
- ✅ **ALWAYS USE**: `data-bs-backdrop="false"` on modals
- ✅ **CSS Rule**: `.modal-backdrop { display: none !important; }`

## 🔧 Technical Stack & Setup

### Development Commands (PowerShell)
```powershell
# Check MySQL
tasklist /FI "IMAGENAME eq mysqld.exe"

# Add PHP to PATH
$env:PATH = "C:\xampp\php;$env:PATH"

# Start development server
php -S localhost:8000 router.php
```

### Package Management Rule
- **ALWAYS** use package managers (npm, composer, etc.) instead of manually editing package files
- **NEVER** manually edit package.json, composer.json unless for complex configuration

### File Editing Rules
- **ALWAYS** use `str-replace-editor` for editing existing files
- **NEVER** overwrite entire files unless absolutely necessary
- **ALWAYS** call `codebase-retrieval` before making edits for context

## 📊 Database & Terminology

### Key Terms
- **"PLACES"** - Unified term for all business types (breweries, restaurants, pubs, etc.)
- **"Party Store"** - Recently added brewery type in dropdown
- **Beer Styles** - 45+ comprehensive styles in database (not hardcoded)

### Database Structure
- **Primary Keys**: UUID format for all tables
- **Soft Deletes**: `is_active` flags instead of hard deletes
- **Timestamps**: `created_at`/`updated_at` on all tables
- **SEO URLs**: Slugified place names (`/places/craft-masters-brewery`)

## 🚨 Recent Critical Fixes (Remember These)

### Modal System Issues (SOLVED)
- **Problem**: Bootstrap modal backdrops blocking form interactions
- **Solution**: Removed all backdrop overlays, fixed z-index conflicts
- **Files**: `assets/css/admin.css`, `assets/js/places-management.js`
- **Rule**: Never use fade transitions on modals

### Beer Menu System (ENHANCED)
- **Before**: 5 hardcoded beer styles
- **After**: 45+ database-driven comprehensive beer styles
- **Features**: ADD STYLE modal, category organization, full CRUD
- **Files**: `admin/beer-menu.php`, `admin/api/beer-styles.php`

### Brewery Types (UPDATED)
- **Added**: "Party Store" to all dropdowns and validation
- **Location**: "Other Beverages" optgroup
- **Files**: All validation files in `admin/` directory

## 🎯 Current Feature Status

### ✅ Working Systems
- Admin dashboard with places management
- Beer styles management (comprehensive)
- User management system
- Modal forms (recently fixed)
- PWA service worker
- SEO-friendly URLs

### 🚧 Known Issues to Watch
- Service worker paths (recently fixed but monitor)
- Modal backdrop interference (solved but be careful with new modals)
- Form validation consistency across admin pages

### 📋 Common Tasks
- Adding new brewery/place types to dropdowns
- Creating admin management interfaces
- Fixing modal interaction issues
- Database schema updates
- API endpoint creation

## 🔍 Debugging Tools Available

### Debug Modal System
- **File**: `assets/js/debug-modal.js`
- **Usage**: Inspect elements, check z-index, modal states
- **Trigger**: Available in admin interfaces

### Database Checks
- Various `check-*.php` files for system validation
- Database connection testing utilities
- Import/export validation tools

## 💡 Development Patterns

### Admin Interface Pattern
1. Main page with data table
2. Add/Edit modals (no backdrop!)
3. API endpoint for CRUD operations
4. JavaScript for form handling
5. Success notifications with auto-reload

### API Endpoint Pattern
1. Authentication check (`requireLogin()`, `requireRole()`)
2. Method switching (GET/POST/PUT/DELETE)
3. Input validation and sanitization
4. Database operations with error handling
5. JSON response with success/error states

### CSS Organization
1. `assets/css/admin.css` - Admin interfaces, modal fixes
2. `assets/css/style.css` - Public site styling
3. Component-specific CSS files
4. Dark mode preferences for admin

## 🎪 User Experience Priorities

### Admin Dashboard
- WordPress-style sliding sidebars
- Modern icons and tooltips
- Proper z-index layering
- Compact, tight layouts
- Industry-standard button designs

### Public Site
- Brewery-themed imagery (wood barrels, taps)
- 4-column featured cards
- Full-width tabbed navigation
- Hidden scrollbars for modern look
- Heart icons and check-in buttons (not oversized)

This mindmap provides essential context for any new AI agent working on the Beersty project. Always refer to these patterns and preferences when making changes or additions to the system.
