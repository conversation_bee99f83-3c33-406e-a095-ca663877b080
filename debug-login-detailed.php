<?php
/**
 * Detailed Login Debug
 * Test the exact login process with detailed error reporting
 */

require_once 'config/config.php';

echo "<h1>🔍 Detailed Login Debug</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test credentials
$testEmail = '<EMAIL>';
$testPassword = 'admin123';

echo "<h2>Testing Login Process Step by Step</h2>";
echo "<p><strong>Test Credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>Email:</strong> $testEmail</li>";
echo "<li><strong>Password:</strong> $testPassword</li>";
echo "</ul>";

try {
    echo "<h3>Step 1: Database Connection</h3>";
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connection successful</p>";
    
    echo "<h3>Step 2: Sanitize Input</h3>";
    $email = sanitizeInput($testEmail);
    echo "<p>✅ Email sanitized: " . htmlspecialchars($email) . "</p>";
    
    echo "<h3>Step 3: Prepare SQL Query</h3>";
    $sql = "
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ";
    echo "<p>✅ SQL Query prepared:</p>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<h3>Step 4: Execute Query</h3>";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$email]);
    echo "<p>✅ Query executed successfully</p>";
    
    echo "<h3>Step 5: Fetch User Data</h3>";
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ User found!</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . htmlspecialchars($user['id']) . "</li>";
        echo "<li><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</li>";
        echo "<li><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</li>";
        echo "<li><strong>Brewery ID:</strong> " . htmlspecialchars($user['brewery_id'] ?? 'null') . "</li>";
        echo "<li><strong>Password Hash:</strong> " . substr($user['password_hash'], 0, 20) . "...</li>";
        echo "</ul>";
        
        echo "<h3>Step 6: Verify Password</h3>";
        if (password_verify($testPassword, $user['password_hash'])) {
            echo "<p>✅ Password verification successful!</p>";
            
            echo "<h3>Step 7: Test Session Variables</h3>";
            // Test setting session variables (without actually setting them)
            echo "<p>Would set session variables:</p>";
            echo "<ul>";
            echo "<li>\$_SESSION['user_id'] = '" . htmlspecialchars($user['id']) . "'</li>";
            echo "<li>\$_SESSION['user_email'] = '" . htmlspecialchars($user['email']) . "'</li>";
            echo "<li>\$_SESSION['user_role'] = '" . htmlspecialchars($user['role']) . "'</li>";
            echo "<li>\$_SESSION['brewery_id'] = '" . htmlspecialchars($user['brewery_id'] ?? 'null') . "'</li>";
            echo "</ul>";
            
            echo "<h3>Step 8: Test Redirect Logic</h3>";
            if ($user['role'] === 'admin') {
                $redirectUrl = url('admin/dashboard.php');
                echo "<p>✅ Would redirect to admin dashboard: <strong>$redirectUrl</strong></p>";
            } elseif ($user['role'] === 'brewery') {
                $redirectUrl = url('brewery/profile.php');
                echo "<p>✅ Would redirect to brewery profile: <strong>$redirectUrl</strong></p>";
            } else {
                $redirectUrl = url('index.php');
                echo "<p>✅ Would redirect to homepage: <strong>$redirectUrl</strong></p>";
            }
            
            echo "<h3>Step 9: Test Update Last Login</h3>";
            try {
                $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$user['id']]);
                echo "<p>✅ Last login update successful</p>";
            } catch (Exception $e) {
                echo "<p>❌ Last login update failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
            echo "<h3>🎉 Login Process Test: SUCCESS!</h3>";
            echo "<p>All steps completed successfully. The login should work.</p>";
            echo "</div>";
            
        } else {
            echo "<p>❌ Password verification failed!</p>";
            
            // Test with different password hash
            echo "<h3>Password Debug:</h3>";
            echo "<ul>";
            echo "<li><strong>Input Password:</strong> '$testPassword'</li>";
            echo "<li><strong>Stored Hash:</strong> " . $user['password_hash'] . "</li>";
            echo "<li><strong>Hash Length:</strong> " . strlen($user['password_hash']) . "</li>";
            echo "</ul>";
            
            // Try to fix the password
            echo "<p>🔧 Attempting to fix password...</p>";
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $updateStmt->execute([$newHash, $testEmail]);
            echo "<p>✅ Password hash updated. Try login again.</p>";
        }
        
    } else {
        echo "<p>❌ User not found!</p>";
        
        echo "<h3>Checking Users Table</h3>";
        $stmt = $conn->query("SELECT email FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        echo "<p>Found " . count($users) . " users in database:</p>";
        echo "<ul>";
        foreach ($users as $u) {
            echo "<li>" . htmlspecialchars($u['email']) . "</li>";
        }
        echo "</ul>";
        
        echo "<h3>Checking Profiles Table</h3>";
        $stmt = $conn->query("SELECT email, role FROM profiles LIMIT 5");
        $profiles = $stmt->fetchAll();
        echo "<p>Found " . count($profiles) . " profiles in database:</p>";
        echo "<ul>";
        foreach ($profiles as $p) {
            echo "<li>" . htmlspecialchars($p['email']) . " (" . htmlspecialchars($p['role']) . ")</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Error Occurred!</h3>";
    echo "<p><strong>Error Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack Trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>Try Login Page Again</a></li>";
echo "<li><a href='debug-login.php' class='btn btn-info'>Run Basic Debug</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
