﻿<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Configuration Test</h1>";

echo "<h2>PHP Version:</h2>";
echo "<p>" . phpversion() . "</p>";

echo "<h2>Extension Status:</h2>";
echo "<p>PDO: " . (extension_loaded('pdo') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";
echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";
echo "<p>MySQLi: " . (extension_loaded('mysqli') ? '<span style="color:green">âœ… LOADED</span>' : '<span style="color:red">âŒ NOT LOADED</span>') . "</p>";

echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color:green'>âœ… PDO MySQL connection successful</p>";
    
    // Test beersty_db
    $pdo->exec("USE beersty_db");
    echo "<p style='color:green'>âœ… Connected to beersty_db</p>";
    
    // Check users table structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    echo "<h3>Users Table Structure:</h3>";
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($columns as $col) {
        echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>âŒ Database error: " . $e->getMessage() . "</p>";
}
?>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
