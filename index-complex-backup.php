<?php
require_once 'config/config.php';

$pageTitle = 'Home - ' . APP_NAME;
$additionalCSS = ['/assets/css/home.css', '/assets/css/social-dark.css'];

include 'includes/header.php';
?>

<?php
// Get some stats for the homepage
$stats = ['total_breweries' => 0, 'total_beers' => 0, 'total_users' => 0, 'recent_activities' => []];
try {
    $db = new Database();
    $conn = $db->getConnection();

    // Get brewery count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM breweries");
    $stmt->execute();
    $stats['total_breweries'] = $stmt->fetch()['count'];

    // Get beer count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM beer_menu");
    $stmt->execute();
    $stats['total_beers'] = $stmt->fetch()['count'];

    // Get user count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $stats['total_users'] = $stmt->fetch()['count'];

    // Get recent activities (if table exists)
    $stmt = $conn->prepare("
        SELECT ua.*, p.first_name, p.last_name, p.username, p.role
        FROM user_activities ua
        JOIN profiles p ON ua.user_id = p.id
        WHERE ua.is_public = 1
        ORDER BY ua.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $stats['recent_activities'] = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error fetching homepage stats: " . $e->getMessage());
}
?>

<!-- Enhanced Hero Section with Social Elements -->
<div class="hero-section text-white py-5 position-relative overflow-hidden">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6">
                <?php if (!isLoggedIn()): ?>
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4 text-gradient">
                            <span class="beer-emoji">🍺</span> Discover Your Next Great Beer
                        </h1>
                        <p class="lead mb-4">
                            Join the ultimate beer social network! Check-in at breweries, rate amazing beers, earn badges, and connect with fellow beer enthusiasts worldwide.
                        </p>

                        <!-- Enhanced Community Stats -->
                        <div class="social-stats mb-4">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo number_format($stats['total_breweries']); ?>+</div>
                                        <div class="stat-label">Breweries</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo number_format($stats['total_beers']); ?>+</div>
                                        <div class="stat-label">Beers</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo number_format($stats['total_users']); ?>+</div>
                                        <div class="stat-label">Beer Lovers</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <?php $user = getCurrentUser(); ?>
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4 text-gradient">
                            <span class="beer-emoji">🍻</span> Welcome back, <?php echo htmlspecialchars($user['first_name'] ?: 'Beer Lover'); ?>!
                        </h1>
                        <p class="lead mb-4">
                            Ready for your next beer adventure? Check-in, discover new breweries, and see what your friends are drinking!
                        </p>

                        <!-- User Quick Stats -->
                        <div class="user-quick-stats mb-4">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">0</div>
                                        <div class="stat-label">Check-ins</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">0</div>
                                        <div class="stat-label">Reviews</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number">0</div>
                                        <div class="stat-label">Badges</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="d-flex flex-wrap gap-3">
                    <?php if (!isLoggedIn()): ?>
                        <a href="/account/register/" class="btn btn-primary btn-lg pulse-btn">
                            <i class="fas fa-user-plus me-2"></i>Join the Community
                        </a>
                        <a href="/account/login/" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                        <a href="/breweries/discover/" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-compass me-2"></i>Explore Breweries
                        </a>
                    <?php else: ?>
                        <?php if ($user['role'] === 'admin'): ?>
                            <a href="/admin/dashboard/" class="btn btn-light btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                            <a href="/admin/import/" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-upload me-2"></i>Data Import
                            </a>
                        <?php elseif ($user['role'] === 'brewery'): ?>
                            <a href="/breweries/manage/" class="btn btn-light btn-lg">
                                <i class="fas fa-store me-2"></i>Manage Brewery
                            </a>
                            <a href="/business/menu/" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-utensils me-2"></i>Manage Menu
                            </a>
                        <?php else: ?>
                            <a href="/social/checkin/" class="btn btn-primary btn-lg pulse-btn">
                                <i class="fas fa-check-circle me-2"></i>Check In Now
                            </a>
                            <a href="/social/feed/" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-stream me-2"></i>Activity Feed
                            </a>
                            <a href="/profile/edit/" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-user me-2"></i>My Profile
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-visual position-relative">
                    <!-- Floating Beer Cards -->
                    <div class="floating-cards">
                        <div class="beer-card floating-card card-1">
                            <div class="beer-icon">🍺</div>
                            <div class="beer-name">IPA</div>
                            <div class="beer-rating">⭐⭐⭐⭐⭐</div>
                        </div>
                        <div class="beer-card floating-card card-2">
                            <div class="beer-icon">🍻</div>
                            <div class="beer-name">Stout</div>
                            <div class="beer-rating">⭐⭐⭐⭐</div>
                        </div>
                        <div class="beer-card floating-card card-3">
                            <div class="beer-icon">🍺</div>
                            <div class="beer-name">Lager</div>
                            <div class="beer-rating">⭐⭐⭐⭐⭐</div>
                        </div>
                    </div>

                    <!-- Central Beer Icon -->
                    <div class="hero-beer-icon text-center">
                        <i class="fas fa-beer display-1 text-beer-gold"></i>
                        <div class="beer-bubbles">
                            <div class="bubble bubble-1"></div>
                            <div class="bubble bubble-2"></div>
                            <div class="bubble bubble-3"></div>
                            <div class="bubble bubble-4"></div>
                            <div class="bubble bubble-5"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Animation -->
    <div class="hero-bg-animation">
        <div class="bg-bubble bg-bubble-1"></div>
        <div class="bg-bubble bg-bubble-2"></div>
        <div class="bg-bubble bg-bubble-3"></div>
    </div>
</div>

<?php if (!empty($stats['recent_activities']) && isLoggedIn()): ?>
<!-- Recent Community Activity -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h2 class="h4 fw-bold">Recent Community Activity</h2>
                <p class="text-muted">See what fellow beer enthusiasts are up to</p>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php foreach ($stats['recent_activities'] as $activity): ?>
                    <div class="card mb-3 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">
                                        <?php
                                        $displayName = trim($activity['first_name'] . ' ' . $activity['last_name']);
                                        if (empty($displayName)) {
                                            $displayName = $activity['username'] ?: 'Beer Enthusiast';
                                        }
                                        echo htmlspecialchars($displayName);
                                        ?>
                                        <span class="badge bg-secondary ms-2">
                                            <?php
                                            $roleLabels = [
                                                'beer_enthusiast' => '🍺 Enthusiast',
                                                'beer_expert' => '🎯 Expert',
                                                'customer' => '👤 Customer',
                                                'brewery' => '🏭 Brewery',
                                                'admin' => '⚡ Admin'
                                            ];
                                            echo $roleLabels[$activity['role']] ?? ucfirst($activity['role']);
                                            ?>
                                        </span>
                                    </div>
                                    <div class="text-muted small">
                                        <?php
                                        switch ($activity['activity_type']) {
                                            case 'profile_update':
                                                echo 'updated their profile';
                                                break;
                                            case 'brewery_follow':
                                                echo 'followed a brewery';
                                                break;
                                            case 'brewery_like':
                                                echo 'liked a brewery';
                                                break;
                                            case 'joined':
                                                echo 'joined the community';
                                                break;
                                            default:
                                                echo $activity['activity_type'];
                                        }
                                        echo ' • ' . formatDateTime($activity['created_at']);
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Trending Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-6 fw-bold mb-3 text-white">
                    <i class="fas fa-fire me-3 text-danger"></i>What's Trending
                </h2>
                <p class="lead text-light">See what the beer community is loving right now</p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Trending Beers -->
            <div class="col-lg-4">
                <div class="card h-100 bg-secondary border-0">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-fire me-2"></i>Hot Beers
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="trending-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="trending-rank me-3">
                                    <span class="badge bg-danger rounded-pill">1</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Hazy IPA Supreme</div>
                                    <div class="text-muted small">Craft Masters Brewery</div>
                                    <div class="text-warning">⭐⭐⭐⭐⭐ 4.8 (127 reviews)</div>
                                </div>
                            </div>
                        </div>
                        <div class="trending-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="trending-rank me-3">
                                    <span class="badge bg-warning rounded-pill">2</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Imperial Stout Delight</div>
                                    <div class="text-muted small">Dark Arts Brewing</div>
                                    <div class="text-warning">⭐⭐⭐⭐ 4.6 (89 reviews)</div>
                                </div>
                            </div>
                        </div>
                        <div class="trending-item">
                            <div class="d-flex align-items-center">
                                <div class="trending-rank me-3">
                                    <span class="badge bg-info rounded-pill">3</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Citrus Wheat Wonder</div>
                                    <div class="text-muted small">Sunshine Brewery</div>
                                    <div class="text-warning">⭐⭐⭐⭐ 4.5 (156 reviews)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="/beers/trending/" class="btn btn-outline-light btn-sm w-100">
                            <i class="fas fa-fire me-2"></i>View All Trending Beers
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Check-ins -->
            <div class="col-lg-4">
                <div class="card h-100 bg-secondary border-0">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>Recent Check-ins
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="checkin-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Alex M.</div>
                                    <div class="text-muted small">checked in at Local Brewery</div>
                                    <div class="text-warning small">⭐⭐⭐⭐⭐ "Amazing IPA!"</div>
                                </div>
                            </div>
                        </div>
                        <div class="checkin-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    <div class="rounded-circle bg-info d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Sarah K.</div>
                                    <div class="text-muted small">drinking Stout at Downtown Pub</div>
                                    <div class="text-warning small">⭐⭐⭐⭐ "Rich and creamy"</div>
                                </div>
                            </div>
                        </div>
                        <div class="checkin-item">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    <div class="rounded-circle bg-warning d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-dark"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Mike R.</div>
                                    <div class="text-muted small">tried Wheat Beer at Craft Corner</div>
                                    <div class="text-warning small">⭐⭐⭐⭐⭐ "Perfect summer beer!"</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="/social/feed/" class="btn btn-outline-light btn-sm w-100">
                            <i class="fas fa-stream me-2"></i>View Activity Feed
                        </a>
                    </div>
                </div>
            </div>

            <!-- Top Breweries -->
            <div class="col-lg-4">
                <div class="card h-100 bg-secondary border-0">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>Top Breweries
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="brewery-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="brewery-rank me-3">
                                    <span class="badge bg-warning rounded-pill">🥇</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Craft Masters Brewery</div>
                                    <div class="text-muted small">Downtown • 4.8★ (234 reviews)</div>
                                    <div class="text-success small">127 check-ins this week</div>
                                </div>
                            </div>
                        </div>
                        <div class="brewery-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="brewery-rank me-3">
                                    <span class="badge bg-secondary rounded-pill">🥈</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Hoppy Valley Brewing</div>
                                    <div class="text-muted small">Midtown • 4.7★ (189 reviews)</div>
                                    <div class="text-success small">98 check-ins this week</div>
                                </div>
                            </div>
                        </div>
                        <div class="brewery-item">
                            <div class="d-flex align-items-center">
                                <div class="brewery-rank me-3">
                                    <span class="badge bg-warning rounded-pill">🥉</span>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-white">Sunset Taphouse</div>
                                    <div class="text-muted small">Westside • 4.6★ (156 reviews)</div>
                                    <div class="text-success small">87 check-ins this week</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="/breweries/discover/" class="btn btn-outline-light btn-sm w-100">
                            <i class="fas fa-compass me-2"></i>Explore All Breweries
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <?php if (!isLoggedIn()): ?>
                    <h2 class="display-5 fw-bold">Join the Beer Community</h2>
                    <p class="lead text-muted">Discover, rate, and share your beer experiences</p>
                <?php else: ?>
                    <h2 class="display-5 fw-bold">Explore Features</h2>
                    <p class="lead text-muted">Make the most of your beer journey</p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row g-4">
            <?php if (!isLoggedIn()): ?>
                <!-- Features for non-logged in users -->
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-search fa-3x text-primary"></i>
                            </div>
                            <h4 class="card-title">Discover Breweries</h4>
                            <p class="card-text">
                                Explore local breweries, discover new favorites, and learn about their unique beer offerings.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-star fa-3x text-warning"></i>
                            </div>
                            <h4 class="card-title">Rate & Review</h4>
                            <p class="card-text">
                                Share your beer experiences with detailed ratings and reviews to help fellow enthusiasts.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-users fa-3x text-success"></i>
                            </div>
                            <h4 class="card-title">Connect & Share</h4>
                            <p class="card-text">
                                Follow friends, share check-ins, and build your beer journey with our social features.
                            </p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Features for logged in users -->
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-user-circle fa-3x text-primary"></i>
                            </div>
                            <h4 class="card-title">Personal Profile</h4>
                            <p class="card-text">
                                Customize your profile, set beer preferences, and track your beer journey.
                            </p>
                            <a href="<?php echo url('user/profile.php'); ?>" class="btn btn-outline-primary btn-sm mt-2">
                                <i class="fas fa-edit me-1"></i>Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-heart fa-3x text-danger"></i>
                            </div>
                            <h4 class="card-title">Beer Preferences</h4>
                            <p class="card-text">
                                Set your favorite styles and preferences for personalized beer recommendations.
                            </p>
                            <a href="<?php echo url('user/preferences.php'); ?>" class="btn btn-outline-danger btn-sm mt-2">
                                <i class="fas fa-cog me-1"></i>Set Preferences
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-map-marker-alt fa-3x text-success"></i>
                            </div>
                            <h4 class="card-title">Explore Breweries</h4>
                            <p class="card-text">
                                Discover breweries near you and explore their beer offerings and events.
                            </p>
                            <a href="<?php echo url('breweries/listing.php'); ?>" class="btn btn-outline-success btn-sm mt-2">
                                <i class="fas fa-search me-1"></i>Browse Breweries
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="row g-4 mt-4">
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-tv fa-3x text-info"></i>
                        </div>
                        <h4 class="card-title">Digital Boards</h4>
                        <p class="card-text">
                            Display dynamic menu boards and promotional content on digital screens in your brewery.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-tags fa-3x text-danger"></i>
                        </div>
                        <h4 class="card-title">Coupons & Promotions</h4>
                        <p class="card-text">
                            Create and manage discount coupons and promotional campaigns to attract customers.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-chart-bar fa-3x text-secondary"></i>
                        </div>
                        <h4 class="card-title">Analytics & Reports</h4>
                        <p class="card-text">
                            Track performance metrics and generate reports to make informed business decisions.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-light py-5">
    <div class="container text-center">
        <h3 class="mb-4">Ready to Get Started?</h3>
        <p class="lead mb-4">Join hundreds of breweries already using our platform</p>
        
        <?php if (!isLoggedIn()): ?>
            <a href="<?php echo url('auth/register.php'); ?>" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-rocket me-2"></i>Get Started Today
            </a>
            <a href="<?php echo url('breweries/listing.php'); ?>" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-search me-2"></i>Browse Breweries
            </a>
        <?php else: ?>
            <a href="<?php echo url('breweries/listing.php'); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-search me-2"></i>Explore Breweries
            </a>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
