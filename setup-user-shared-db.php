<?php
/**
 * User-Space Shared Database Setup
 * Creates a shared database in user's home directory without requiring sudo
 */

echo "<h1>🏠 User-Space Shared Database Setup</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
    .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$action = $_GET['action'] ?? 'show_options';

function getCurrentUser() {
    return get_current_user() ?: posix_getpwuid(posix_geteuid())['name'] ?? 'unknown';
}

function getHomeDirectory() {
    return $_SERVER['HOME'] ?? '/home/' . getCurrentUser();
}

if ($action === 'setup_shared') {
    echo "<h2>🔧 Setting up User-Space Shared Database</h2>";
    
    $currentUser = getCurrentUser();
    $homeDir = getHomeDirectory();
    $currentProjectPath = __DIR__;
    
    echo "<p><strong>Current User:</strong> $currentUser</p>";
    echo "<p><strong>Home Directory:</strong> $homeDir</p>";
    echo "<p><strong>Project Path:</strong> $currentProjectPath</p>";
    
    try {
        // 1. Create shared database directory in user's home
        $sharedBasePath = $homeDir . '/.beersty-shared';
        $sharedDbPath = $sharedBasePath . '/database';
        $sharedConfigPath = $sharedBasePath . '/config';
        $sharedUploadsPath = $sharedBasePath . '/uploads';
        
        echo "<h3>📁 Creating Shared Directories</h3>";
        
        $directories = [
            $sharedBasePath => 'Base shared directory',
            $sharedDbPath => 'Database storage',
            $sharedConfigPath => 'Configuration files', 
            $sharedUploadsPath => 'File uploads'
        ];
        
        foreach ($directories as $dir => $description) {
            if (!is_dir($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<p>✅ Created $description: <code>$dir</code></p>";
                } else {
                    throw new Exception("Failed to create directory: $dir");
                }
            } else {
                echo "<p>ℹ️ Directory already exists: <code>$dir</code></p>";
            }
        }
        
        // Set permissions to be readable by group
        chmod($sharedBasePath, 0755);
        chmod($sharedDbPath, 0755);
        chmod($sharedConfigPath, 0755);
        chmod($sharedUploadsPath, 0755);
        
        // 2. Create shared database file
        echo "<h3>🗄️ Creating Shared Database</h3>";
        
        $sharedDbFile = $sharedDbPath . '/beersty_shared.json';
        
        if (!file_exists($sharedDbFile)) {
            $initialData = [
                'users' => [
                    [
                        'id' => 'admin-shared-id',
                        'email' => '<EMAIL>',
                        'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'email_verified' => 1,
                        'last_login' => null
                    ]
                ],
                'profiles' => [
                    [
                        'id' => 'admin-shared-id',
                        'email' => '<EMAIL>',
                        'role' => 'admin',
                        'first_name' => 'Shared',
                        'last_name' => 'Admin',
                        'brewery_id' => null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                ],
                'breweries' => [],
                'metadata' => [
                    'created_by' => $currentUser,
                    'created_at' => date('Y-m-d H:i:s'),
                    'version' => '1.0',
                    'last_modified' => date('Y-m-d H:i:s'),
                    'shared_path' => $sharedBasePath
                ]
            ];
            
            file_put_contents($sharedDbFile, json_encode($initialData, JSON_PRETTY_PRINT));
            chmod($sharedDbFile, 0644);
            echo "<p>✅ Created shared database file: <code>$sharedDbFile</code></p>";
        } else {
            echo "<p>ℹ️ Shared database file already exists</p>";
        }
        
        // 3. Create shared database configuration
        echo "<h3>⚙️ Creating Shared Database Configuration</h3>";
        
        $configFile = $sharedConfigPath . '/user_shared_database.php';
        
        $configContent = '<?php
/**
 * User-Space Shared Database Configuration
 */

class UserSharedDatabase {
    private $dataFile;
    private $lockFile;
    private $data;
    
    public function __construct() {
        $homeDir = $_SERVER["HOME"] ?? "/home/" . get_current_user();
        $this->dataFile = $homeDir . "/.beersty-shared/database/beersty_shared.json";
        $this->lockFile = $homeDir . "/.beersty-shared/database/beersty_shared.lock";
        
        if (!file_exists($this->dataFile)) {
            throw new Exception("Shared database file not found: " . $this->dataFile);
        }
        
        $this->loadData();
    }
    
    private function loadData() {
        // Use file locking for concurrent access
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_SH)) {
            $json = file_get_contents($this->dataFile);
            $this->data = json_decode($json, true) ?: [];
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    private function saveData() {
        $lockHandle = fopen($this->lockFile, "c");
        if (flock($lockHandle, LOCK_EX)) {
            $this->data["metadata"]["last_modified"] = date("Y-m-d H:i:s");
            $this->data["metadata"]["modified_by"] = get_current_user();
            
            file_put_contents($this->dataFile, json_encode($this->data, JSON_PRETTY_PRINT));
            flock($lockHandle, LOCK_UN);
        }
        fclose($lockHandle);
    }
    
    public function prepare($sql) {
        return new UserSharedStatement($this, $sql);
    }
    
    public function setAttribute($attribute, $value) {
        return true;
    }
    
    public function getAttribute($attribute) {
        if ($attribute === PDO::ATTR_DRIVER_NAME) {
            return "user_shared_json";
        }
        return null;
    }
    
    public function executeQuery($sql, $params = []) {
        $this->loadData(); // Reload for latest data
        
        // Simple query parser
        $sql = trim($sql);
        
        if (preg_match("/^SELECT\s+(.+?)\s+FROM\s+(\w+)(?:\s+(\w+))?\s*(?:JOIN\s+(\w+)\s+(\w+)\s+ON\s+(.+?))?\s*(?:WHERE\s+(.+?))?$/i", $sql, $matches)) {
            return $this->handleSelect($matches, $params);
        } elseif (preg_match("/^INSERT\s+INTO\s+(\w+)\s*\((.+?)\)\s*VALUES\s*\((.+?)\)$/i", $sql, $matches)) {
            return $this->handleInsert($matches, $params);
        } elseif (preg_match("/^UPDATE\s+(\w+)\s+SET\s+(.+?)(?:\s+WHERE\s+(.+?))?$/i", $sql, $matches)) {
            return $this->handleUpdate($matches, $params);
        }
        
        return [];
    }
    
    private function handleSelect($matches, $params) {
        $table = $matches[2];
        $whereClause = $matches[7] ?? "";
        
        $results = [];
        
        if (!isset($this->data[$table])) {
            return [];
        }
        
        foreach ($this->data[$table] as $row) {
            $result = $row;
            
            // Handle JOIN (simplified)
            if (isset($matches[4]) && $matches[4]) {
                $joinTable = $matches[4];
                if (isset($this->data[$joinTable])) {
                    foreach ($this->data[$joinTable] as $joinRow) {
                        if (isset($row["id"]) && isset($joinRow["id"]) && $row["id"] === $joinRow["id"]) {
                            $result = array_merge($result, $joinRow);
                            break;
                        }
                    }
                }
            }
            
            // Handle WHERE clause
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($result["email"]) || $result["email"] !== $params[0]) {
                    continue;
                }
            }
            
            $results[] = $result;
        }
        
        return $results;
    }
    
    private function handleInsert($matches, $params) {
        $table = $matches[1];
        $fields = array_map("trim", explode(",", $matches[2]));
        
        if (!isset($this->data[$table])) {
            $this->data[$table] = [];
        }
        
        $row = [];
        for ($i = 0; $i < count($fields); $i++) {
            $field = trim($fields[$i]);
            $row[$field] = $params[$i] ?? null;
        }
        
        $this->data[$table][] = $row;
        $this->saveData();
        
        return true;
    }
    
    private function handleUpdate($matches, $params) {
        $table = $matches[1];
        $whereClause = $matches[3] ?? "";
        
        if (!isset($this->data[$table])) {
            return false;
        }
        
        foreach ($this->data[$table] as &$row) {
            if ($whereClause && strpos($whereClause, "email = ?") !== false) {
                if (!isset($row["email"]) || $row["email"] !== $params[0]) {
                    continue;
                }
            }
            
            // Simple SET parsing
            if (strpos($matches[2], "last_login") !== false) {
                $row["last_login"] = date("Y-m-d H:i:s");
            }
        }
        
        $this->saveData();
        return true;
    }
}

class UserSharedStatement {
    private $db;
    private $sql;
    private $results;
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        $this->results = $this->db->executeQuery($this->sql, $params);
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}
?>';
        
        file_put_contents($configFile, $configContent);
        chmod($configFile, 0644);
        echo "<p>✅ Created shared database configuration: <code>$configFile</code></p>";
        
        // 4. Create symlinks in current project
        echo "<h3>🔗 Creating Symlinks</h3>";
        
        $userDbPath = $currentProjectPath . '/database';
        $userUploadsPath = $currentProjectPath . '/uploads';
        
        // Backup existing database if it exists
        if (is_dir($userDbPath) && !is_link($userDbPath)) {
            $backupPath = $userDbPath . '_backup_' . date('Y-m-d_H-i-s');
            rename($userDbPath, $backupPath);
            echo "<p>📦 Backed up existing database to: <code>$backupPath</code></p>";
        }
        
        // Remove existing symlink if it exists
        if (is_link($userDbPath)) {
            unlink($userDbPath);
        }
        
        // Create symlink to shared database
        if (symlink($sharedDbPath, $userDbPath)) {
            echo "<p>✅ Created database symlink: <code>$userDbPath</code> → <code>$sharedDbPath</code></p>";
        } else {
            echo "<p>❌ Failed to create database symlink</p>";
        }
        
        // Handle uploads directory
        if (is_dir($userUploadsPath) && !is_link($userUploadsPath)) {
            $backupUploadsPath = $userUploadsPath . '_backup_' . date('Y-m-d_H-i-s');
            rename($userUploadsPath, $backupUploadsPath);
            echo "<p>📦 Backed up existing uploads to: <code>$backupUploadsPath</code></p>";
        }
        
        if (is_link($userUploadsPath)) {
            unlink($userUploadsPath);
        }
        
        if (symlink($sharedUploadsPath, $userUploadsPath)) {
            echo "<p>✅ Created uploads symlink: <code>$userUploadsPath</code> → <code>$sharedUploadsPath</code></p>";
        } else {
            echo "<p>❌ Failed to create uploads symlink</p>";
        }
        
        echo "<div class='success'>";
        echo "<h3>🎉 User-Space Shared Database Setup Complete!</h3>";
        echo "<p>Your shared database structure is now set up in your home directory:</p>";
        echo "<ul>";
        echo "<li><strong>Shared Base:</strong> <code>$sharedBasePath</code></li>";
        echo "<li><strong>Shared Database:</strong> <code>$sharedDbFile</code></li>";
        echo "<li><strong>Shared Config:</strong> <code>$configFile</code></li>";
        echo "<li><strong>Shared Uploads:</strong> <code>$sharedUploadsPath</code></li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='info'>";
        echo "<h4>📋 For Other Users:</h4>";
        echo "<p>Other users can access this shared database by:</p>";
        echo "<ol>";
        echo "<li>Getting read/write access to: <code>$sharedBasePath</code></li>";
        echo "<li>Running this setup from their project directory</li>";
        echo "<li>Or manually creating symlinks to the shared directories</li>";
        echo "</ol>";
        echo "<p><strong>Share this path with other users:</strong> <code>$sharedBasePath</code></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} elseif ($action === 'link_existing') {
    echo "<h2>🔗 Link to Existing User-Space Shared Database</h2>";
    
    $sharedPath = $_GET['shared_path'] ?? '';
    
    if (empty($sharedPath)) {
        echo "<form method='GET'>";
        echo "<input type='hidden' name='action' value='link_existing'>";
        echo "<div class='info'>";
        echo "<h4>Enter Shared Database Path</h4>";
        echo "<p>Enter the path to the existing shared database (e.g., /home/<USER>/.beersty-shared):</p>";
        echo "<input type='text' name='shared_path' placeholder='/home/<USER>/.beersty-shared' style='width: 400px; padding: 8px;'>";
        echo "<br><br>";
        echo "<button type='submit' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;'>Link to Shared Database</button>";
        echo "</div>";
        echo "</form>";
        return;
    }
    
    $currentProjectPath = __DIR__;
    $sharedDbPath = $sharedPath . '/database';
    $sharedUploadsPath = $sharedPath . '/uploads';
    
    if (!is_dir($sharedDbPath)) {
        echo "<div class='error'>";
        echo "<h3>❌ Shared Database Not Found</h3>";
        echo "<p>The shared database directory doesn't exist at: <code>$sharedDbPath</code></p>";
        echo "</div>";
    } else {
        try {
            $userDbPath = $currentProjectPath . '/database';
            $userUploadsPath = $currentProjectPath . '/uploads';
            
            // Backup existing database if it exists
            if (is_dir($userDbPath) && !is_link($userDbPath)) {
                $backupPath = $userDbPath . '_backup_' . date('Y-m-d_H-i-s');
                rename($userDbPath, $backupPath);
                echo "<p>📦 Backed up existing database to: <code>$backupPath</code></p>";
            }
            
            // Remove existing symlink if it exists
            if (is_link($userDbPath)) {
                unlink($userDbPath);
            }
            
            // Create new symlink
            if (symlink($sharedDbPath, $userDbPath)) {
                echo "<p>✅ Created database symlink: <code>$userDbPath</code> → <code>$sharedDbPath</code></p>";
            } else {
                throw new Exception("Failed to create database symlink");
            }
            
            // Handle uploads
            if (is_dir($userUploadsPath) && !is_link($userUploadsPath)) {
                $backupUploadsPath = $userUploadsPath . '_backup_' . date('Y-m-d_H-i-s');
                rename($userUploadsPath, $backupUploadsPath);
                echo "<p>📦 Backed up existing uploads to: <code>$backupUploadsPath</code></p>";
            }
            
            if (is_link($userUploadsPath)) {
                unlink($userUploadsPath);
            }
            
            if (symlink($sharedUploadsPath, $userUploadsPath)) {
                echo "<p>✅ Created uploads symlink: <code>$userUploadsPath</code> → <code>$sharedUploadsPath</code></p>";
            }
            
            echo "<div class='success'>";
            echo "<h3>✅ Successfully Linked to Shared Database</h3>";
            echo "<p>Your project is now using the shared database at: <code>$sharedPath</code></p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h3>❌ Linking Failed</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} else {
    echo "<h2>🔧 User-Space Shared Database Setup</h2>";
    
    $currentUser = getCurrentUser();
    $homeDir = getHomeDirectory();
    
    echo "<div class='info'>";
    echo "<h3>📊 Current Setup Information</h3>";
    echo "<p><strong>Current User:</strong> $currentUser</p>";
    echo "<p><strong>Home Directory:</strong> $homeDir</p>";
    echo "<p><strong>Project Path:</strong> " . __DIR__ . "</p>";
    echo "<p><strong>Shared Database Location:</strong> $homeDir/.beersty-shared/</p>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>⚠️ About User-Space Shared Database</h3>";
    echo "<ul>";
    echo "<li>Creates shared database in your home directory (~/.beersty-shared/)</li>";
    echo "<li>No sudo privileges required</li>";
    echo "<li>Other users need read/write access to your home directory</li>";
    echo "<li>All users will share the same data and login credentials</li>";
    echo "<li>File uploads will be stored in the shared location</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🚀 Setup Options</h3>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=setup_shared' style='display: inline-block; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>🔧 Create New Shared Database</a>";
    echo "<a href='?action=link_existing' style='display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px;'>🔗 Link to Existing Shared DB</a>";
    echo "</div>";
    
    echo "<h3>📋 What Each Option Does:</h3>";
    echo "<ul>";
    echo "<li><strong>Create New Shared Database:</strong> Sets up a new shared database structure in your home directory</li>";
    echo "<li><strong>Link to Existing:</strong> Links your current project to an already-created shared database</li>";
    echo "</ul>";
    
    echo "<div class='info'>";
    echo "<h4>🔐 Default Login Credentials</h4>";
    echo "<p>The shared database will include a default admin account:</p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "<li><strong>Role:</strong> admin</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>User-Space Shared Database Setup for Beersty | Current User: " . getCurrentUser() . "</small></p>";
?>
