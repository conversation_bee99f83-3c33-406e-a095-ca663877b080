<?php
/**
 * Phase 9 Test Script
 * Test Design & Mobile Optimization functionality
 */

require_once 'config/config.php';

echo "<h1>🧪 Phase 9 Test: Design & Mobile Optimization</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Testing Database Tables...</h2>";
    
    // Test if Phase 9 tables exist
    $tables = [
        'user_pwa_preferences',
        'user_devices',
        'pwa_analytics',
        'mobile_interactions',
        'offline_sync_queue'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT 1 FROM $table LIMIT 1");
            $existingTables[] = $table;
            echo "<p>✅ Table <strong>$table</strong> exists</p>";
        } catch (Exception $e) {
            $missingTables[] = $table;
            echo "<p>❌ Table <strong>$table</strong> missing</p>";
        }
    }
    
    // Test mobile indexes
    echo "<h2>Testing Mobile Indexes...</h2>";
    
    $indexes = [
        'idx_beer_checkins_mobile',
        'idx_beer_ratings_mobile',
        'idx_user_activities_mobile',
        'idx_user_follows_mobile'
    ];
    
    foreach ($indexes as $index) {
        try {
            $stmt = $conn->prepare("SHOW INDEX FROM beer_checkins WHERE Key_name = ?");
            $stmt->execute([$index]);
            if ($stmt->fetch()) {
                echo "<p>✅ Index <strong>$index</strong> exists</p>";
            } else {
                echo "<p>⚠️ Index <strong>$index</strong> not found</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error checking index <strong>$index</strong></p>";
        }
    }
    
    if (!empty($missingTables)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Missing Tables</h3>";
        echo "<p>Please run the Phase 9 setup script first:</p>";
        echo "<p><a href='setup-phase9.php' class='btn btn-primary'>Run Phase 9 Setup</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>Testing PWA Files...</h2>";
    
    // Test PWA files
    $pwaFiles = [
        'manifest.json' => 'Web App Manifest',
        'sw.js' => 'Service Worker',
        'offline.html' => 'Offline Page',
        'assets/css/mobile.css' => 'Mobile CSS',
        'assets/css/pwa.css' => 'PWA CSS',
        'assets/js/mobile.js' => 'Mobile JavaScript',
        'assets/js/pwa.js' => 'PWA JavaScript'
    ];
    
    foreach ($pwaFiles as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ <strong>$description</strong> ($file) exists</p>";
        } else {
            echo "<p>❌ <strong>$description</strong> ($file) missing</p>";
        }
    }
    
    echo "<h2>Testing Mobile Components...</h2>";
    
    // Test mobile components
    $components = [
        'components/mobile-bottom-nav.php' => 'Mobile Bottom Navigation'
    ];
    
    foreach ($components as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ <strong>$description</strong> component exists</p>";
        } else {
            echo "<p>❌ <strong>$description</strong> component missing</p>";
        }
    }
    
    echo "<h2>Testing Database Procedures...</h2>";
    
    // Test stored procedures
    $procedures = [
        'TrackPWAEvent',
        'UpdateDeviceInfo'
    ];
    
    foreach ($procedures as $procedure) {
        try {
            $stmt = $conn->prepare("SHOW PROCEDURE STATUS WHERE Name = ?");
            $stmt->execute([$procedure]);
            if ($stmt->fetch()) {
                echo "<p>✅ Procedure <strong>$procedure</strong> exists</p>";
            } else {
                echo "<p>⚠️ Procedure <strong>$procedure</strong> not found</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error checking procedure <strong>$procedure</strong></p>";
        }
    }
    
    echo "<h2>Testing Mobile Views...</h2>";
    
    // Test mobile views
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM mobile_user_summary");
        $result = $stmt->fetch();
        echo "<p>✅ Mobile user summary view working: {$result['count']} users</p>";
    } catch (Exception $e) {
        echo "<p>❌ Mobile user summary view error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>Testing PWA Preferences...</h2>";
    
    // Test PWA preferences for existing users
    try {
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users");
        $totalUsers = $stmt->fetch()['total_users'];
        
        $stmt = $conn->query("SELECT COUNT(*) as pwa_users FROM user_pwa_preferences");
        $pwaUsers = $stmt->fetch()['pwa_users'];
        
        echo "<p>✅ Total users: $totalUsers</p>";
        echo "<p>✅ Users with PWA preferences: $pwaUsers</p>";
        
        if ($pwaUsers >= $totalUsers) {
            echo "<p>✅ All users have PWA preferences configured</p>";
        } else {
            echo "<p>⚠️ Some users missing PWA preferences</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ PWA preferences test error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>Testing Frontend Integration...</h2>";
    
    // Test if header includes mobile CSS
    $headerContent = file_get_contents('includes/header.php');
    if (strpos($headerContent, 'mobile.css') !== false) {
        echo "<p>✅ Mobile CSS included in header</p>";
    } else {
        echo "<p>❌ Mobile CSS not included in header</p>";
    }
    
    if (strpos($headerContent, 'pwa.css') !== false) {
        echo "<p>✅ PWA CSS included in header</p>";
    } else {
        echo "<p>❌ PWA CSS not included in header</p>";
    }
    
    if (strpos($headerContent, 'manifest.json') !== false) {
        echo "<p>✅ Web App Manifest linked in header</p>";
    } else {
        echo "<p>❌ Web App Manifest not linked in header</p>";
    }
    
    // Test if footer includes mobile JS
    $footerContent = file_get_contents('includes/footer.php');
    if (strpos($footerContent, 'mobile.js') !== false) {
        echo "<p>✅ Mobile JavaScript included in footer</p>";
    } else {
        echo "<p>❌ Mobile JavaScript not included in footer</p>";
    }
    
    if (strpos($footerContent, 'pwa.js') !== false) {
        echo "<p>✅ PWA JavaScript included in footer</p>";
    } else {
        echo "<p>❌ PWA JavaScript not included in footer</p>";
    }
    
    if (strpos($footerContent, 'mobile-bottom-nav.php') !== false) {
        echo "<p>✅ Mobile bottom navigation included in footer</p>";
    } else {
        echo "<p>❌ Mobile bottom navigation not included in footer</p>";
    }
    
    echo "<h2>Testing Mobile Features...</h2>";
    
    // Test mobile pages
    $mobilePages = [
        '/beersty/' => 'Home Page',
        '/beersty/beers/discover.php' => 'Beer Discovery',
        '/beersty/social/checkin.php' => 'Check-in Page',
        '/beersty/user/profile.php' => 'User Profile'
    ];
    
    foreach ($mobilePages as $page => $name) {
        $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . $page;
        echo "<p>Testing page: <strong>$name</strong> (<code>$page</code>)</p>";
        
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15');
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 302) {
                echo "<p>✅ Page accessible (redirects to login as expected)</p>";
            } elseif ($httpCode === 200) {
                echo "<p>✅ Page accessible (returns 200)</p>";
                
                // Check for mobile-specific elements
                if (strpos($response, 'mobile-bottom-nav') !== false) {
                    echo "<p>✅ Mobile bottom navigation detected</p>";
                }
                if (strpos($response, 'mobile.css') !== false) {
                    echo "<p>✅ Mobile CSS detected</p>";
                }
            } else {
                echo "<p>⚠️ Page returns HTTP $httpCode</p>";
            }
        } else {
            echo "<p>⚠️ cURL not available for page testing</p>";
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Phase 9 Testing Complete!</h3>";
    echo "<p>Design & Mobile Optimization functionality is working correctly.</p>";
    echo "</div>";
    
    echo "<h2>🚀 Mobile Testing Instructions</h2>";
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📱 Mobile Device Testing:</h4>";
    echo "<ol>";
    echo "<li><strong>Open on mobile device:</strong> Visit the site on your smartphone or tablet</li>";
    echo "<li><strong>Install PWA:</strong> Look for the 'Add to Home Screen' prompt</li>";
    echo "<li><strong>Test bottom navigation:</strong> Use the mobile bottom navigation bar</li>";
    echo "<li><strong>Try gestures:</strong> Swipe, pull-to-refresh, and touch interactions</li>";
    echo "<li><strong>Test offline:</strong> Disable network and try browsing cached content</li>";
    echo "</ol>";
    
    echo "<h4>🖥️ Desktop Testing:</h4>";
    echo "<ol>";
    echo "<li><strong>Browser dev tools:</strong> Open Chrome DevTools and switch to mobile view</li>";
    echo "<li><strong>Responsive design:</strong> Test different screen sizes</li>";
    echo "<li><strong>PWA features:</strong> Check for install prompt in supported browsers</li>";
    echo "<li><strong>Touch simulation:</strong> Enable touch simulation in dev tools</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>📊 PWA Features to Test</h2>";
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<ul>";
    echo "<li><strong>Install Prompt:</strong> Should appear on supported browsers</li>";
    echo "<li><strong>Offline Page:</strong> Visit /beersty/offline.html</li>";
    echo "<li><strong>Service Worker:</strong> Check browser dev tools > Application > Service Workers</li>";
    echo "<li><strong>Manifest:</strong> Check browser dev tools > Application > Manifest</li>";
    echo "<li><strong>Cache Storage:</strong> Check browser dev tools > Application > Storage</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎨 Design Features to Test</h2>";
    echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<ul>";
    echo "<li><strong>Mobile Bottom Navigation:</strong> Fixed navigation bar on mobile</li>";
    echo "<li><strong>Touch Feedback:</strong> Visual feedback on button presses</li>";
    echo "<li><strong>Responsive Layout:</strong> Adapts to different screen sizes</li>";
    echo "<li><strong>Dark Mode:</strong> Respects system dark mode preference</li>";
    echo "<li><strong>Accessibility:</strong> Screen reader and keyboard navigation support</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='setup-phase9.php'>← Run Setup Again</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

ol, ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
