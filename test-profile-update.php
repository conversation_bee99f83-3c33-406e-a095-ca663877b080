<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo "❌ Not logged in. Please log in first.";
    exit;
}

$user = getCurrentUser();
echo "<h2>🧪 Profile Update Test</h2>";
echo "<p>Testing profile update functionality for user: " . htmlspecialchars($user['email']) . "</p>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h3>1. Check if profiles table exists and has required columns:</h3>";
    
    // Check table structure
    $stmt = $conn->query("DESCRIBE profiles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['avatar', 'first_name', 'last_name', 'username', 'bio', 'location', 'hometown', 'date_of_birth', 'website', 'instagram', 'twitter', 'facebook', 'profile_visibility', 'show_location', 'show_age', 'allow_messages'];
    
    $missingColumns = [];
    $existingColumns = array_column($columns, 'Field');
    
    foreach ($requiredColumns as $col) {
        if (in_array($col, $existingColumns)) {
            echo "✅ $col<br>";
        } else {
            echo "❌ $col (MISSING)<br>";
            $missingColumns[] = $col;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "<p><strong>❌ Missing columns: " . implode(', ', $missingColumns) . "</strong></p>";
        echo "<p>Run <a href='/fix-profiles-table.php'>fix-profiles-table.php</a> to add missing columns.</p>";
        exit;
    }
    
    echo "<h3>2. Check if user profile exists:</h3>";
    
    $stmt = $conn->prepare("SELECT * FROM profiles WHERE id = ?");
    $stmt->execute([$user['id']]);
    $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($profile) {
        echo "✅ Profile exists for user<br>";
        echo "Current data: " . htmlspecialchars(json_encode($profile, JSON_PRETTY_PRINT)) . "<br>";
    } else {
        echo "❌ No profile found. Creating one...<br>";
        
        // Create profile
        $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)");
        $stmt->execute([$user['id'], $user['email'], $user['role'] ?? 'customer']);
        echo "✅ Profile created<br>";
    }
    
    echo "<h3>3. Test profile update without avatar:</h3>";
    
    try {
        $stmt = $conn->prepare("
            UPDATE profiles SET 
                first_name = ?, last_name = ?, username = ?, bio = ?, 
                location = ?, hometown = ?, date_of_birth = ?, website = ?,
                instagram = ?, twitter = ?, facebook = ?,
                profile_visibility = ?, show_location = ?, show_age = ?, allow_messages = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            'Test', 'User', 'testuser', 'Test bio',
            'Test City', 'Test Hometown', null, 'https://test.com',
            'testinsta', 'testtwitter', 'testfacebook',
            'public', 1, 0, 1,
            $user['id']
        ]);
        
        echo "✅ Profile update without avatar works!<br>";
        
    } catch (Exception $e) {
        echo "❌ Profile update failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
    
    echo "<h3>4. Test profile update with avatar:</h3>";
    
    try {
        $stmt = $conn->prepare("
            UPDATE profiles SET 
                first_name = ?, last_name = ?, username = ?, bio = ?, 
                location = ?, hometown = ?, date_of_birth = ?, website = ?,
                instagram = ?, twitter = ?, facebook = ?,
                profile_visibility = ?, show_location = ?, show_age = ?, allow_messages = ?,
                avatar = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            'Test', 'User', 'testuser', 'Test bio',
            'Test City', 'Test Hometown', null, 'https://test.com',
            'testinsta', 'testtwitter', 'testfacebook',
            'public', 1, 0, 1,
            '/test/avatar.jpg',
            $user['id']
        ]);
        
        echo "✅ Profile update with avatar works!<br>";
        
    } catch (Exception $e) {
        echo "❌ Profile update with avatar failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
    
    echo "<h3>5. Check uploads directory:</h3>";
    
    $uploadsDir = 'uploads/avatars/';
    if (is_dir($uploadsDir)) {
        echo "✅ Uploads directory exists: $uploadsDir<br>";
        if (is_writable($uploadsDir)) {
            echo "✅ Uploads directory is writable<br>";
        } else {
            echo "❌ Uploads directory is not writable<br>";
        }
    } else {
        echo "❌ Uploads directory does not exist: $uploadsDir<br>";
        if (mkdir($uploadsDir, 0755, true)) {
            echo "✅ Created uploads directory<br>";
        } else {
            echo "❌ Failed to create uploads directory<br>";
        }
    }
    
    echo "<h3>✅ Test Complete</h3>";
    echo "<p>If all tests pass, the profile update should work. Try the <a href='/user/profile.php'>profile page</a> again.</p>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . htmlspecialchars($e->getMessage());
}
?>
