# CHECK PHP VERSIONS AND ALL PHP.INI FILES
# Comprehensive check of PHP installations and configurations

Write-Host "🔍 PHP VERSION AND CONFIGURATION ANALYSIS" -ForegroundColor Yellow
Write-Host "==========================================" -ForegroundColor Yellow

# 1. Check command line PHP version
Write-Host ""
Write-Host "1. COMMAND LINE PHP:" -ForegroundColor Cyan
try {
    $phpVersion = php -v 2>$null
    if ($phpVersion) {
        Write-Host "✅ PHP found in system PATH:" -ForegroundColor Green
        $phpVersion | Select-Object -First 1 | ForEach-Object { 
            Write-Host "   $_" -ForegroundColor White 
            # Extract version number
            if ($_ -match "PHP (\d+\.\d+\.\d+)") {
                Write-Host "   Version: $($matches[1])" -ForegroundColor Yellow
            }
        }
        
        # Get PHP executable location
        $phpLocation = where.exe php 2>$null
        if ($phpLocation) {
            Write-Host "   Executable: $phpLocation" -ForegroundColor Gray
        }
        
        # Get PHP configuration file location
        $phpConfigFile = php --ini 2>$null | Select-String "Loaded Configuration File"
        if ($phpConfigFile) {
            Write-Host "   Config File: $($phpConfigFile.ToString().Split(':')[1].Trim())" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ No PHP found in system PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ No PHP found in system PATH" -ForegroundColor Red
}

# 2. Check XAMPP PHP version
Write-Host ""
Write-Host "2. XAMPP PHP:" -ForegroundColor Cyan

$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if ($XamppPath) {
    Write-Host "✅ XAMPP found at: $XamppPath" -ForegroundColor Green
    
    $XamppPhp = Join-Path $XamppPath "php\php.exe"
    if (Test-Path $XamppPhp) {
        Write-Host "✅ XAMPP PHP found at: $XamppPhp" -ForegroundColor Green
        try {
            $xamppPhpVersion = & $XamppPhp -v 2>$null
            if ($xamppPhpVersion) {
                $xamppPhpVersion | Select-Object -First 1 | ForEach-Object { 
                    Write-Host "   $_" -ForegroundColor White 
                    if ($_ -match "PHP (\d+\.\d+\.\d+)") {
                        Write-Host "   Version: $($matches[1])" -ForegroundColor Yellow
                    }
                }
            }
            
            # Get XAMPP PHP ini file
            $xamppPhpIni = & $XamppPhp --ini 2>$null | Select-String "Loaded Configuration File"
            if ($xamppPhpIni) {
                Write-Host "   Config File: $($xamppPhpIni.ToString().Split(':')[1].Trim())" -ForegroundColor Gray
            }
        } catch {
            Write-Host "   Could not get XAMPP PHP version" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ XAMPP PHP not found at: $XamppPhp" -ForegroundColor Red
    }
} else {
    Write-Host "❌ XAMPP not found" -ForegroundColor Red
}

# 3. Find ALL php.ini files on the system
Write-Host ""
Write-Host "3. ALL PHP.INI FILES ON SYSTEM:" -ForegroundColor Cyan

$PhpIniFiles = @()

# Search common locations
$SearchPaths = @(
    "C:\",
    "C:\php",
    "C:\xampp",
    "C:\wamp",
    "C:\laragon",
    "C:\Program Files",
    "C:\Program Files (x86)",
    "C:\Windows"
)

Write-Host "   Searching for php.ini files..." -ForegroundColor Gray

foreach ($searchPath in $SearchPaths) {
    if (Test-Path $searchPath) {
        try {
            $foundFiles = Get-ChildItem -Path $searchPath -Name "php.ini" -Recurse -ErrorAction SilentlyContinue | ForEach-Object {
                Join-Path $searchPath $_
            }
            $PhpIniFiles += $foundFiles
        } catch {
            # Ignore access denied errors
        }
    }
}

# Remove duplicates and display
$PhpIniFiles = $PhpIniFiles | Sort-Object | Get-Unique

if ($PhpIniFiles.Count -gt 0) {
    Write-Host "   Found $($PhpIniFiles.Count) php.ini file(s):" -ForegroundColor Yellow
    
    foreach ($iniFile in $PhpIniFiles) {
        Write-Host "   📄 $iniFile" -ForegroundColor White
        
        # Check file size and modification date
        if (Test-Path $iniFile) {
            $fileInfo = Get-Item $iniFile
            Write-Host "      Size: $([math]::Round($fileInfo.Length / 1KB, 1)) KB" -ForegroundColor Gray
            Write-Host "      Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
            
            # Check if PDO is enabled in this file
            try {
                $pdoCheck = Select-String -Path $iniFile -Pattern "^extension=pdo_mysql" -ErrorAction SilentlyContinue
                if ($pdoCheck) {
                    Write-Host "      ✅ PDO MySQL enabled" -ForegroundColor Green
                } else {
                    $pdoCommented = Select-String -Path $iniFile -Pattern "^;\s*extension=pdo_mysql" -ErrorAction SilentlyContinue
                    if ($pdoCommented) {
                        Write-Host "      ❌ PDO MySQL commented out" -ForegroundColor Red
                    } else {
                        Write-Host "      ⚠️ PDO MySQL not found" -ForegroundColor Yellow
                    }
                }
            } catch {
                Write-Host "      Could not check PDO status" -ForegroundColor Gray
            }
        }
        Write-Host ""
    }
} else {
    Write-Host "   ❌ No php.ini files found!" -ForegroundColor Red
}

# 4. Check which PHP.ini is actually being used by web server
Write-Host ""
Write-Host "4. ACTIVE PHP CONFIGURATION:" -ForegroundColor Cyan

# Create a quick phpinfo test
$PhpInfoContent = @"
<?php
echo "<h1>PHP Configuration Info</h1>";
echo "<h2>PHP Version: " . phpversion() . "</h2>";
echo "<h2>Configuration File: " . php_ini_loaded_file() . "</h2>";
echo "<h2>Additional ini files: " . php_ini_scanned_dir() . "</h2>";
echo "<h2>PDO Status:</h2>";
echo "PDO: " . (extension_loaded('pdo') ? 'LOADED' : 'NOT LOADED') . "<br>";
echo "PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'LOADED' : 'NOT LOADED') . "<br>";
echo "MySQLi: " . (extension_loaded('mysqli') ? 'LOADED' : 'NOT LOADED') . "<br>";
?>
"@

$PhpInfoFile = "phpinfo-check.php"
$PhpInfoContent | Set-Content $PhpInfoFile -Encoding UTF8
Write-Host "   Created PHP info test: $PhpInfoFile" -ForegroundColor Green

# 5. Summary and recommendations
Write-Host ""
Write-Host "5. SUMMARY AND RECOMMENDATIONS:" -ForegroundColor Red
Write-Host "===============================" -ForegroundColor Red

if ($PhpIniFiles.Count -gt 1) {
    Write-Host ""
    Write-Host "⚠️ MULTIPLE PHP.INI FILES DETECTED!" -ForegroundColor Yellow
    Write-Host "This can cause confusion. Here's what's happening:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🎯 WHICH PHP.INI IS ACTIVE?" -ForegroundColor Cyan
    Write-Host "- Command line PHP uses one php.ini" -ForegroundColor White
    Write-Host "- XAMPP/Apache uses a different php.ini" -ForegroundColor White
    Write-Host "- You need to configure the RIGHT one for web development" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 RECOMMENDATION:" -ForegroundColor Cyan
    Write-Host "1. Focus on XAMPP's php.ini for web development" -ForegroundColor White
    Write-Host "2. Enable PDO in XAMPP's php.ini only" -ForegroundColor White
    Write-Host "3. Test via web browser, not command line" -ForegroundColor White
    
} elseif ($PhpIniFiles.Count -eq 1) {
    Write-Host "✅ Single php.ini file found - good!" -ForegroundColor Green
    Write-Host "Make sure PDO MySQL is enabled in: $($PhpIniFiles[0])" -ForegroundColor White
    
} else {
    Write-Host "❌ No php.ini files found - this is a problem!" -ForegroundColor Red
    Write-Host "PHP needs a configuration file to work properly." -ForegroundColor White
}

Write-Host ""
Write-Host "🧪 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Test web PHP: http://localhost/beersty-lovable/phpinfo-check.php" -ForegroundColor White
Write-Host "2. Check PDO status in web browser (not command line)" -ForegroundColor White
Write-Host "3. Configure the correct php.ini file" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to continue"
