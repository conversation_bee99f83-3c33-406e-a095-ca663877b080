<?php
/**
 * Fix all missing PDO methods in UserSharedDatabase class
 */

$sharedConfigPath = '/home/<USER>/.beersty-shared/config/user_shared_database.php';

// Read the current file
$content = file_get_contents($sharedConfigPath);

// Find the position to insert the query method
$insertPosition = strpos($content, 'public function prepare($sql) {');

if ($insertPosition !== false) {
    // Insert the query method before prepare
    $queryMethod = '    public function query($sql) {
        // Execute query directly and return a statement-like object
        $stmt = new UserSharedStatement($this, $sql);
        $stmt->execute();
        return $stmt;
    }

    ';
    
    $content = substr_replace($content, $queryMethod, $insertPosition, 0);
    
    // Write the updated content back to the file
    if (file_put_contents($sharedConfigPath, $content)) {
        echo "<h1>✅ query() Method Added Successfully!</h1>";
        echo "<p>The UserSharedDatabase class has been updated with the missing query() method.</p>";
        echo "<p><strong>Updated file:</strong> <code>$sharedConfigPath</code></p>";
        echo "<p><strong>Added method:</strong> <code>query(\$sql)</code></p>";
        echo "<p><a href='/admin/breweries.php'>🎯 Test Breweries Admin Page</a></p>";
    } else {
        echo "<h1>❌ Update Failed</h1>";
        echo "<p>Failed to update the shared database configuration.</p>";
    }
} else {
    echo "<h1>❌ Method Not Found</h1>";
    echo "<p>Could not find the prepare method to insert the query method.</p>";
}
?>
