# Beersty PHP Application - Git Ignore

# Environment files
config/.env
.env
.env.local
.env.production

# Uploads directory (user-generated content)
uploads/*
!uploads/.gitkeep

# Logs
logs/
*.log
error.log
access.log

# Cache files
cache/
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Database dumps
*.sql.gz
*.sql.bz2
backup.sql

# Node.js (legacy - not used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
dist-ssr/
*.local

# XAMPP specific
xampp/

# Security
*.key
*.pem
*.crt
