<?php
/**
 * Places URL Redirect Handler
 * Handles 301 redirects from old ID-based URLs to new slug-based URLs
 */

require_once '../config/config.php';

// Function to generate SEO-friendly slug from place name
function generateSlug($name) {
    $slug = strtolower($name);
    $slug = str_replace([' ', '&', '.', "'"], ['-', 'and', '', ''], $slug);
    $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}

// Get the place ID from the URL
$place_id = $_GET['id'] ?? null;

if ($place_id && is_numeric($place_id)) {
    try {
        $db = new Database();
        $conn = $db->getConnection();

        // Get brewery name by ID
        $stmt = $conn->prepare("SELECT name FROM breweries WHERE id = ?");
        $stmt->execute([$place_id]);
        $brewery = $stmt->fetch();

        if ($brewery) {
            $slug = generateSlug($brewery['name']);
            $new_url = '/places/' . $slug;

            // Perform 301 redirect
            header("HTTP/1.1 301 Moved Permanently");
            header("Location: " . $new_url);
            exit();
        }
    } catch (Exception $e) {
        error_log("Error in places redirect: " . $e->getMessage());
    }
}

// If no valid ID found, redirect to places search
header("HTTP/1.1 301 Moved Permanently");
header("Location: /places/");
exit();
?>
