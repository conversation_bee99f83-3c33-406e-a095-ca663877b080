<?php
/**
 * Place Profile Page - SEO Friendly URL Structure
 * URL: /places/profile/?id=1 or /places/profile/1
 * Displays detailed information about a specific place
 */





// Fix relative path - check if we're being included from router (root) or accessed directly
if (file_exists('config/config.php')) {
    // Being included from router (root directory)
    require_once 'config/config.php';
} else {
    // Being accessed directly
    require_once '../../config/config.php';
}

// Function to generate SEO-friendly slug from place name
function generateSlug($name) {
    $slug = strtolower($name);
    $slug = str_replace([' ', '&', '.', "'"], ['-', 'and', '', ''], $slug);
    $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}

// Get place ID from URL - support slug, ID parameter, and path-based ID
$place_id = null;
$place_slug = null;



// Check for slug parameter first (SEO-friendly URLs)
if (isset($_GET['slug'])) {
    $place_slug = $_GET['slug'];

} elseif (isset($_GET['id'])) {
    // Legacy ID parameter support
    $place_id = $_GET['id'];
} else {
    // Check for path-based parameters
    $request_uri = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim($request_uri, '/'));

    // Look for slug or numeric ID in the path
    foreach ($path_parts as $part) {
        if (is_numeric($part)) {
            $place_id = $part;
            break;
        } elseif (preg_match('/^[a-z0-9\-]+$/', $part) && strlen($part) > 2) {
            $place_slug = $part;
            break;
        }
    }
}

// Database query to get place data
$place = null;
$place_id = null;

try {
    $db = new Database();
    $conn = $db->getConnection();

    if ($place_slug) {
        // Generate slug from brewery names in database and find match
        $stmt = $conn->prepare("SELECT * FROM breweries WHERE 1=1 ORDER BY created_at DESC");
        $stmt->execute();
        $breweries = $stmt->fetchAll();

        foreach ($breweries as $brewery) {
            $brewery_slug = generateSlug($brewery['name']);
            if ($brewery_slug === $place_slug) {
                $place_id = $brewery['id'];
                break;
            }
        }
    }

    // If no slug match found and we have a numeric place_id, use it
    if (!$place_id && isset($_GET['id']) && is_numeric($_GET['id'])) {
        $place_id = $_GET['id'];
    }

    // Default to first brewery if no ID found
    if (!$place_id) {
        $stmt = $conn->prepare("SELECT id FROM breweries ORDER BY created_at DESC LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch();
        $place_id = $result['id'] ?? 1;
    }

    // Get the specific brewery data
    $stmt = $conn->prepare("
        SELECT id, name, address, city, state, zip, phone, email, website,
               description, brewery_type, verified, claimed, like_count,
               follower_count, logo, feature_image, social_links
        FROM breweries
        WHERE id = ?
    ");
    $stmt->execute([$place_id]);
    $brewery_data = $stmt->fetch();

    if (!$brewery_data) {
        // If brewery not found, redirect to places search
        header('Location: /places/search.php');
        exit;
    }

    // Generate slug for canonical URL
    $current_slug = generateSlug($brewery_data['name']);

    // Transform brewery data to place format
    $place = [
        'id' => $brewery_data['id'],
        'name' => $brewery_data['name'],
        'type' => ucfirst($brewery_data['brewery_type'] ?? 'Brewery'),
        'description' => $brewery_data['description'] ?: 'A great place to enjoy craft beer and good company.',
        'address' => $brewery_data['address'],
        'city' => $brewery_data['city'],
        'state' => $brewery_data['state'],
        'zip' => $brewery_data['zip'],
        'phone' => $brewery_data['phone'],
        'website' => $brewery_data['website'],
        'email' => $brewery_data['email'],
        'rating' => min(5.0, 3.5 + (($brewery_data['like_count'] ?? 0) / 100)),
        'reviews_count' => ($brewery_data['like_count'] ?? 0) + ($brewery_data['follower_count'] ?? 0),
    'price_range' => '$$',
    'hours' => [
        'Monday' => 'Closed',
        'Tuesday' => '4:00 PM - 10:00 PM',
        'Wednesday' => '4:00 PM - 10:00 PM',
        'Thursday' => '4:00 PM - 11:00 PM',
        'Friday' => '2:00 PM - 12:00 AM',
        'Saturday' => '12:00 PM - 12:00 AM',
        'Sunday' => '12:00 PM - 9:00 PM'
    ],
    'features' => ['Outdoor Seating', 'Live Music', 'Food Truck', 'Pet Friendly', 'Parking Available'],
    'images' => [
        '/assets/images/brewery1.jpg',
        '/assets/images/brewery2.jpg',
        '/assets/images/brewery3.jpg'
    ],
    'beers' => [
        // Featured beers (first 4 will show as cards)
        ['name' => 'House Special', 'style' => 'IPA', 'abv' => '6.5%', 'rating' => 4.9, 'price' => '$7.50', 'featured' => true, 'description' => 'Our signature beer with exceptional flavor and quality.'],
        ['name' => 'Chocolate Stout', 'style' => 'Stout', 'abv' => '7.2%', 'rating' => 4.7, 'price' => '$8.00', 'featured' => true, 'description' => 'Rich and complex stout with notes of dark chocolate and coffee.'],
        ['name' => 'Summer Wheat', 'style' => 'Wheat Beer', 'abv' => '4.8%', 'rating' => 4.5, 'price' => '$6.50', 'featured' => true, 'description' => 'Light and refreshing wheat beer perfect for any season.'],
        ['name' => 'Double IPA Crusher', 'style' => 'Double IPA', 'abv' => '8.2%', 'rating' => 4.8, 'price' => '$9.00', 'featured' => true, 'description' => 'Bold double IPA with intense hop character and citrus notes.'],

        // Additional beers for list view
        ['name' => 'Vanilla Porter', 'style' => 'Porter', 'abv' => '5.8%', 'rating' => 4.6, 'price' => '$7.75', 'featured' => false, 'description' => 'Smooth porter with natural vanilla bean flavor.'],
        ['name' => 'Citrus Pale Ale', 'style' => 'Pale Ale', 'abv' => '5.2%', 'rating' => 4.4, 'price' => '$6.75', 'featured' => false, 'description' => 'Bright pale ale with citrus forward hop profile.'],
        ['name' => 'Belgian Tripel', 'style' => 'Belgian Tripel', 'abv' => '9.2%', 'rating' => 4.6, 'price' => '$9.50', 'featured' => false, 'description' => 'Traditional Belgian tripel with complex yeast character.'],
        ['name' => 'Smoky Lager', 'style' => 'Smoked Lager', 'abv' => '5.0%', 'rating' => 4.2, 'price' => '$6.00', 'featured' => false, 'description' => 'Crisp lager with subtle smoky undertones.'],
        ['name' => 'Sour Cherry Ale', 'style' => 'Fruit Sour', 'abv' => '5.5%', 'rating' => 4.0, 'price' => '$8.50', 'featured' => false, 'description' => 'Tart and refreshing sour ale with real cherry puree.'],
        ['name' => 'Coffee Brown Ale', 'style' => 'Brown Ale', 'abv' => '6.0%', 'rating' => 4.4, 'price' => '$7.00', 'featured' => false, 'description' => 'Rich brown ale infused with locally roasted coffee beans.'],
        ['name' => 'Grapefruit IPA', 'style' => 'IPA', 'abv' => '6.5%', 'rating' => 4.3, 'price' => '$7.50', 'featured' => false, 'description' => 'Bright IPA with fresh grapefruit zest and tropical hops.'],
        ['name' => 'Oktoberfest', 'style' => 'Märzen', 'abv' => '5.8%', 'rating' => 4.2, 'price' => '$6.50', 'featured' => false, 'description' => 'Traditional German-style Oktoberfest with malty sweetness.'],
        ['name' => 'Session IPA', 'style' => 'Session IPA', 'abv' => '4.2%', 'rating' => 4.0, 'price' => '$5.50', 'featured' => false, 'description' => 'Lower alcohol IPA with full hop flavor and easy drinkability.'],
        ['name' => 'Barrel-Aged Barleywine', 'style' => 'Barleywine', 'abv' => '11.2%', 'rating' => 4.8, 'price' => '$12.00', 'featured' => false, 'description' => 'Aged in bourbon barrels for 12 months, complex and warming.']
    ],
    'food_menu' => [
        [
            'category' => 'Appetizers',
            'items' => [
                ['name' => 'Beer Cheese Pretzel', 'description' => 'Warm soft pretzel with house-made beer cheese dip', 'price' => '$12.99', 'featured' => true],
                ['name' => 'Buffalo Cauliflower', 'description' => 'Crispy cauliflower tossed in buffalo sauce, served with ranch', 'price' => '$10.99', 'featured' => false],
                ['name' => 'Loaded Nachos', 'description' => 'House-made tortilla chips with cheese, jalapeños, and sour cream', 'price' => '$14.99', 'featured' => true],
                ['name' => 'Wings & Things', 'description' => 'Traditional buffalo wings with celery and blue cheese', 'price' => '$13.99', 'featured' => false],
                ['name' => 'Spinach Artichoke Dip', 'description' => 'Creamy dip served with tortilla chips and bread', 'price' => '$11.99', 'featured' => false],
                ['name' => 'Fried Pickles', 'description' => 'Beer-battered pickle spears with ranch dipping sauce', 'price' => '$9.99', 'featured' => false]
            ]
        ],
        [
            'category' => 'Mains',
            'items' => [
                ['name' => 'Brewery Burger', 'description' => '8oz beef patty with bacon, cheese, lettuce, tomato on brioche bun', 'price' => '$16.99', 'featured' => true],
                ['name' => 'Fish & Chips', 'description' => 'Beer-battered cod with fries and coleslaw', 'price' => '$18.99', 'featured' => false],
                ['name' => 'BBQ Pulled Pork Sandwich', 'description' => 'Slow-cooked pork with BBQ sauce on brioche bun', 'price' => '$15.99', 'featured' => true],
                ['name' => 'Margherita Flatbread', 'description' => 'Fresh mozzarella, tomatoes, basil on house-made flatbread', 'price' => '$13.99', 'featured' => false],
                ['name' => 'Steak & Fries', 'description' => '10oz ribeye steak with seasoned fries and vegetables', 'price' => '$24.99', 'featured' => false],
                ['name' => 'Chicken Parmesan', 'description' => 'Breaded chicken breast with marinara and mozzarella', 'price' => '$19.99', 'featured' => false],
                ['name' => 'Veggie Wrap', 'description' => 'Grilled vegetables, hummus, and greens in a spinach tortilla', 'price' => '$12.99', 'featured' => false],
                ['name' => 'Mac & Cheese', 'description' => 'House-made mac and cheese with breadcrumb topping', 'price' => '$14.99', 'featured' => false]
            ]
        ],
        [
            'category' => 'Desserts',
            'items' => [
                ['name' => 'Chocolate Stout Cake', 'description' => 'Rich chocolate cake made with our signature stout', 'price' => '$8.99', 'featured' => false],
                ['name' => 'Beer Float', 'description' => 'Vanilla ice cream with your choice of beer', 'price' => '$7.99', 'featured' => false],
                ['name' => 'Apple Crisp', 'description' => 'Warm apple crisp with vanilla ice cream', 'price' => '$7.99', 'featured' => false],
                ['name' => 'Cheesecake', 'description' => 'New York style cheesecake with berry compote', 'price' => '$8.99', 'featured' => false]
            ]
        ]
    ],
    'deals' => [
        [
            'id' => 1,
            'title' => 'Happy Hour Special',
            'description' => '50% off all draft beers',
            'discount_type' => 'percentage',
            'discount_value' => 50,
            'valid_until' => '2024-12-31',
            'qr_code' => 'HAPPY50',
            'terms' => 'Valid Monday-Friday 3-6 PM only'
        ],
        [
            'id' => 2,
            'title' => 'Buy 2 Get 1 Free',
            'description' => 'Buy any 2 craft beers and get the 3rd one free',
            'discount_type' => 'bogo',
            'discount_value' => 0,
            'valid_until' => '2024-12-25',
            'qr_code' => 'BOGO3',
            'terms' => 'Applies to beers of equal or lesser value'
        ],
        [
            'id' => 3,
            'title' => 'Free Appetizer',
            'description' => 'Free appetizer with purchase of any entree',
            'discount_type' => 'free_item',
            'discount_value' => 0,
            'valid_until' => '2024-12-20',
            'qr_code' => 'FREEAPP',
            'terms' => 'One per table, dine-in only'
        ]
    ],
    'has_360_tour' => true,
    'tour_url' => 'https://example.com/360-tour',
    'is_claimed' => false,
    'claim_contact' => '<EMAIL>',
    'followers_count' => 245,
    'following_count' => 12,
    'social_media' => [
        'facebook' => 'https://facebook.com/craftmastersbrewery',
        'instagram' => 'https://instagram.com/craftmastersbrewery',
        'twitter' => 'https://twitter.com/craftmasters',
        'yelp' => 'https://yelp.com/biz/craftmasters-brewery',
        'google' => 'https://g.page/craftmasters-brewery'
    ]
    ];

} catch (Exception $e) {
    error_log("Error loading place data: " . $e->getMessage());
    // Redirect to places search if there's an error
    header('Location: /places/search.php');
    exit;
}

// SEO Meta Data
$pageTitle = $place['name'] . ' - ' . $place['city'] . ', ' . $place['state'] . ' - ' . APP_NAME;
$metaDescription = 'Visit ' . $place['name'] . ' in ' . $place['city'] . ', ' . $place['state'] . '. ' . substr($place['description'], 0, 120) . '...';
$canonicalUrl = 'https://beersty.com/places/' . $current_slug;

// Additional CSS for places
$additionalCSS = ['/beersty/assets/css/places.css'];

// Fix header path for router context
if (file_exists('includes/header.php')) {
    require_once 'includes/header.php';
} else {
    require_once '../../includes/header.php';
}
?>

<!-- Force Dark Mode Styles -->
<script>
// Force dark mode immediately
document.documentElement.classList.add('dark-mode');
localStorage.setItem('beersty-dark-mode', 'true');
</script>
<style>
/* Force dark mode with proper contrast */
html, body {
    background-color: #121212 !important;
    color: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Override any light mode styles */
* {
    color: inherit !important;
}

.container, .container-fluid {
    background-color: transparent !important;
}

.main-content {
    background-color: #121212 !important;
    color: #ffffff !important;
    margin-top: 0 !important;
}

/* Ensure navigation is visible */
.navbar {
    position: relative !important;
    z-index: 1030 !important;
    background-color: #000000 !important;
    border-bottom: 2px solid #ffb347 !important;
    padding: 1rem 0 !important;
    min-height: 70px !important;
}

.navbar-brand {
    color: #ffffff !important;
    font-weight: bold !important;
}

.navbar-brand img {
    filter: brightness(1.2) !important;
    display: block !important;
}

.navbar-nav .nav-link {
    color: #ffffff !important;
    font-weight: 500 !important;
}

.navbar-nav .nav-link:hover {
    color: #ffb347 !important;
}

.navbar-toggler {
    border-color: #ffffff !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* Fix all cards */
.card {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #404040 !important;
}

.card-body, .card-header {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Fix text colors */
h1, h2, h3, h4, h5, h6, p, span, div {
    color: #ffffff !important;
}

.text-muted {
    color: #b3b3b3 !important;
}

/* Social Media Icons */
.social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    color: #ffffff !important;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.4rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    text-decoration: none;
    filter: brightness(1.1);
}

.social-link:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Facebook */
.social-link .fab.fa-facebook-f {
    background: #1877f2;
}

.social-link:hover .fab.fa-facebook-f {
    background: #166fe5;
}

/* Instagram */
.social-link .fab.fa-instagram {
    background: linear-gradient(45deg, #e1306c, #fd1d1d, #fcb045);
}

/* Twitter */
.social-link .fab.fa-twitter {
    background: #1da1f2;
}

.social-link:hover .fab.fa-twitter {
    background: #0d8bd9;
}

/* Yelp */
.social-link .fab.fa-yelp {
    background: #d32323;
}

.social-link:hover .fab.fa-yelp {
    background: #c41e3a;
}

/* Google */
.social-link .fab.fa-google {
    background: #4285f4;
}

.social-link:hover .fab.fa-google {
    background: #3367d6;
}

/* Mobile responsive adjustments */
@media (max-width: 576px) {
    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        border-radius: 10px;
    }

    .social-links {
        gap: 0.5rem;
        justify-content: center;
    }
}

/* Star Rating Styles */
.star-rating {
    cursor: pointer;
}

.star-btn {
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s ease;
    color: #dee2e6;
}

.star-btn:hover {
    color: #ffc107 !important;
}

.star-btn.text-warning {
    color: #ffc107 !important;
}

.star-btn.text-muted {
    color: #6c757d !important;
}

/* Beer and Food List Styling */
.beer-list .table,
.food-menu-section .table {
    background-color: #1e1e1e !important;
    border-color: #404040 !important;
}

.beer-list .table th,
.food-menu-section .table th {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
    font-weight: 600;
}

.beer-list .table td,
.food-menu-section .table td {
    border-color: #404040 !important;
    vertical-align: middle;
}

.beer-row:hover,
.food-row:hover {
    background-color: #2d2d2d !important;
}

.beer-name-cell,
.food-name-cell {
    line-height: 1.4;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    font-size: 0.9rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Featured sections */
.featured-beers-section,
.featured-food-section {
    border-bottom: 2px solid #404040;
    padding-bottom: 2rem;
}

.featured-beers-section h4,
.featured-food-section h4 {
    color: #ffb347 !important;
}

/* Table responsive improvements */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Badge improvements */
.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-primary {
    background-color: #007bff !important;
}

/* Modal Form Readability Fixes */
.modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

.modal-body .form-label {
    color: #212529 !important;
    font-weight: 600;
}

.modal-body .form-text {
    color: #6c757d !important;
}

.modal-body .form-control {
    background-color: #ffffff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
}

.modal-body .form-control:focus {
    background-color: #ffffff !important;
    border-color: #86b7fe !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-body .form-select {
    background-color: #ffffff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
}

.modal-body .form-select:focus {
    background-color: #ffffff !important;
    border-color: #86b7fe !important;
    color: #212529 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-body .form-check-label {
    color: #212529 !important;
}

.modal-body .alert-info {
    background-color: #d1ecf1 !important;
    border-color: #bee5eb !important;
    color: #0c5460 !important;
}

.modal-body .alert-info strong {
    color: #0c5460 !important;
}

.modal-body textarea::placeholder,
.modal-body input::placeholder {
    color: #6c757d !important;
}

/* Check-in Modal Fixes */
#checkInModal .modal-content {
    background-color: #ffffff !important;
    color: #212529 !important;
}

#checkInModal .modal-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

#checkInModal .form-label {
    color: #212529 !important;
    font-weight: 600;
}

#checkInModal .form-control {
    background-color: #ffffff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
}

#checkInModal .form-check-label {
    color: #212529 !important;
}

#checkInModal .place-info {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

#checkInModal .place-info h6 {
    color: #212529 !important;
}

#checkInModal .place-info small {
    color: #6c757d !important;
}

/* Full Width Navigation Tabs - CLEAN INDUSTRY STANDARD */
.place-navigation-wrapper {
    background: #6F4C3E;
    padding: 0;
    margin-bottom: 2rem;
    border-radius: 0;
    width: 100%;
    overflow: hidden;
}

.place-nav-tabs {
    border-bottom: none !important;
    display: flex;
    flex-wrap: nowrap;
    margin: 0 !important;
    background: #6F4C3E !important;
    width: 100%;
    gap: 0 !important;
}

.place-nav-tabs .nav-item {
    flex: 1;
    margin: 0 !important;
    padding: 0 !important;
}

.place-nav-tabs .nav-link {
    background: #6F4C3E !important;
    color: #F5F5DC !important;
    border: none !important;
    border-right: 2px solid #3B2A2A !important;
    border-radius: 0 !important;
    padding: 1rem 0.5rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    transition: background-color 0.2s ease !important;
    margin: 0 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
}

.place-nav-tabs .nav-item:last-child .nav-link {
    border-right: none !important;
}

.place-nav-tabs .nav-link:hover {
    background: #FFC107 !important;
    color: #3B2A2A !important;
}

.place-nav-tabs .nav-link.active {
    background: #FFC107 !important;
    color: #3B2A2A !important;
}

.place-nav-tabs .nav-link i {
    font-size: 1rem;
    margin-right: 0.5rem;
}

/* Responsive tabs - NO GAPS OR SPACES */
@media (max-width: 768px) {
    .place-nav-tabs {
        flex-wrap: wrap;
        width: 100%;
    }

    .place-nav-tabs .nav-item {
        flex: 1 1 50%;
        min-width: 0;
        margin: 0 !important;
        padding: 0 !important;
    }

    .place-nav-tabs .nav-link {
        padding: 0.75rem 0.25rem !important;
        font-size: 0.875rem !important;
        height: 50px !important;
        border-right: 2px solid #3B2A2A !important;
        border-bottom: 2px solid #3B2A2A !important;
        width: 100% !important;
    }

    .place-nav-tabs .nav-item:nth-child(even) .nav-link {
        border-right: none !important;
    }

    .place-nav-tabs .nav-item:nth-last-child(-n+2) .nav-link {
        border-bottom: none !important;
    }

    .place-nav-tabs .nav-link i {
        font-size: 0.875rem;
        margin-right: 0.25rem;
    }
}

@media (max-width: 576px) {
    .place-navigation-wrapper {
        padding: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
    }

    .place-nav-tabs .nav-item {
        flex: 1 1 100% !important;
        width: 100% !important;
    }

    .place-nav-tabs .nav-link {
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
        height: 50px !important;
        border-right: none !important;
        border-bottom: 2px solid #3B2A2A !important;
        width: 100% !important;
    }

    .place-nav-tabs .nav-item:last-child .nav-link {
        border-bottom: none !important;
    }
}

/* Fix Check-in Button and Heart Icons */
.place-action-btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 0.375rem !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 100px !important;
}

.place-action-btn i {
    font-size: 0.875rem !important;
    margin-right: 0.25rem !important;
}

.place-action-btn:not(:has(i.fa-heart)) i {
    margin-right: 0.5rem !important;
}

/* Heart icon specific styling */
.fa-heart {
    font-size: 0.8rem !important;
    margin: 0 !important;
}

/* Beer and Food action buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
    height: 28px !important;
    min-width: 32px !important;
}

.btn-group-sm .btn i {
    font-size: 0.7rem !important;
    margin: 0 !important;
}

.food-actions .btn {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
    height: 28px !important;
}

.food-actions .btn i {
    font-size: 0.7rem !important;
    margin-right: 0.25rem !important;
}

/* Primary button styling */
.btn-primary {
    background: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}

.btn-primary:hover {
    background: #D69A6B !important;
    border-color: #D69A6B !important;
    color: #F5F5DC !important;
}

.btn-outline-warning {
    border-color: #FFC107 !important;
    color: #FFC107 !important;
}

.btn-outline-warning:hover {
    background: #FFC107 !important;
    border-color: #FFC107 !important;
    color: #3B2A2A !important;
}
</style>

<!-- SEO Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreweryRestaurant",
  "name": "<?php echo htmlspecialchars($place['name']); ?>",
  "description": "<?php echo htmlspecialchars($place['description']); ?>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "<?php echo htmlspecialchars($place['address']); ?>",
    "addressLocality": "<?php echo htmlspecialchars($place['city']); ?>",
    "addressRegion": "<?php echo htmlspecialchars($place['state']); ?>",
    "postalCode": "<?php echo htmlspecialchars($place['zip']); ?>"
  },
  "telephone": "<?php echo htmlspecialchars($place['phone']); ?>",
  "url": "<?php echo htmlspecialchars($place['website']); ?>",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "<?php echo $place['rating']; ?>",
    "reviewCount": "<?php echo $place['reviews_count']; ?>"
  },
  "priceRange": "<?php echo htmlspecialchars($place['price_range']); ?>",
  "openingHours": [
    <?php 
    $hours_array = [];
    foreach ($place['hours'] as $day => $hours) {
        if ($hours !== 'Closed') {
            $day_abbr = substr($day, 0, 2);
            $hours_array[] = '"' . $day_abbr . ' ' . $hours . '"';
        }
    }
    echo implode(',', $hours_array);
    ?>
  ]
}
</script>

<!-- Place Hero Section -->
<section class="place-hero position-relative">
    <div class="hero-image brewery-hero" style="height: 450px; background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <div class="container h-100">
            <div class="row h-100 align-items-end">
                <div class="col-lg-8">
                    <div class="place-info text-white pb-4">
                        <div class="place-badges mb-3">
                            <span class="badge bg-primary fs-6 me-2"><?php echo $place['type']; ?></span>
                            <span class="badge bg-success fs-6"><?php echo $place['price_range']; ?></span>
                        </div>
                        <h1 class="display-5 fw-bold mb-3"><?php echo htmlspecialchars($place['name']); ?></h1>
                        <div class="place-meta d-flex flex-wrap gap-4 mb-3">
                            <div class="rating-display">
                                <span class="rating-stars fs-5">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <?php if ($i <= floor($place['rating'])): ?>
                                            ⭐
                                        <?php else: ?>
                                            ☆
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </span>
                                <span class="rating-text"><?php echo $place['rating']; ?> (<?php echo $place['reviews_count']; ?> reviews)</span>
                            </div>
                            <div class="location-info">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($place['address']); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end pb-4">
                    <?php if (isLoggedIn()): ?>
                        <div class="place-actions d-flex gap-2 justify-content-end">
                            <button class="btn btn-primary place-action-btn" onclick="checkInToPlace()">
                                <i class="fas fa-check-circle me-1"></i>Check In
                            </button>
                            <button class="btn btn-outline-warning place-action-btn" onclick="toggleFavorite()">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="btn btn-outline-light place-action-btn" onclick="sharePlace()">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Place Content -->
<section class="place-content py-5">
    <div class="container">
        <!-- Full Width Navigation Tabs -->
        <div class="place-navigation-wrapper mb-4">
            <ul class="nav nav-tabs place-nav-tabs" id="placeTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-info-circle me-2"></i>Overview
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="beers-tab" data-bs-toggle="tab" data-bs-target="#beers" type="button" role="tab">
                        <i class="fas fa-beer me-2"></i>Beers
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="food-tab" data-bs-toggle="tab" data-bs-target="#food" type="button" role="tab">
                        <i class="fas fa-utensils me-2"></i>Food Menu
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="deals-tab" data-bs-toggle="tab" data-bs-target="#deals" type="button" role="tab">
                        <i class="fas fa-tags me-2"></i>Deals
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                        <i class="fas fa-star me-2"></i>Reviews
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                        <i class="fas fa-camera me-2"></i>Photos
                    </button>
                </li>
                <?php if ($place['has_360_tour']): ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tour-tab" data-bs-toggle="tab" data-bs-target="#tour" type="button" role="tab">
                        <i class="fas fa-street-view me-2"></i>360° Tour
                    </button>
                </li>
                <?php endif; ?>
            </ul>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">

                <!-- Tab Content -->
                <div class="tab-content" id="placeTabContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="overview-content">
                            <h4 class="mb-3">About <?php echo htmlspecialchars($place['name']); ?></h4>
                            <p class="lead"><?php echo htmlspecialchars($place['description']); ?></p>
                            
                            <div class="features-section mt-4">
                                <h5 class="mb-3">Features & Amenities</h5>
                                <div class="row">
                                    <?php foreach ($place['features'] as $feature): ?>
                                        <div class="col-md-6 mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            <?php echo htmlspecialchars($feature); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Beers Tab -->
                    <div class="tab-pane fade" id="beers" role="tabpanel">
                        <div class="beers-content">
                            <!-- Featured Beers Cards -->
                            <div class="featured-beers-section mb-5">
                                <h4 class="mb-3">Featured Beers</h4>
                                <div class="row g-3">
                                    <?php
                                    $featuredBeers = array_filter($place['beers'], function($beer) {
                                        return isset($beer['featured']) && $beer['featured'] === true;
                                    });
                                    foreach ($featuredBeers as $beer):
                                    ?>
                                        <div class="col-md-6">
                                            <div class="beer-card card h-100">
                                                <div class="card-body">
                                                    <h6 class="card-title text-warning"><?php echo htmlspecialchars($beer['name']); ?></h6>
                                                    <p class="card-text text-muted mb-2">
                                                        <?php echo htmlspecialchars($beer['style']); ?> • <?php echo $beer['abv']; ?> ABV • <?php echo $beer['price']; ?>
                                                    </p>
                                                    <p class="card-text small mb-3"><?php echo htmlspecialchars($beer['description']); ?></p>
                                                    <div class="beer-rating">
                                                        <span class="rating-stars">
                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                <?php if ($i <= floor($beer['rating'])): ?>
                                                                    ⭐
                                                                <?php else: ?>
                                                                    ☆
                                                                <?php endif; ?>
                                                            <?php endfor; ?>
                                                        </span>
                                                        <span class="rating-text small"><?php echo $beer['rating']; ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Complete Beer List -->
                            <div class="beer-list-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">Complete Beer Menu</h4>
                                    <span class="badge bg-primary"><?php echo count($place['beers']); ?> Beers Available</span>
                                </div>

                                <div class="beer-list">
                                    <div class="table-responsive">
                                        <table class="table table-dark table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Beer Name</th>
                                                    <th>Style</th>
                                                    <th>ABV</th>
                                                    <th>Price</th>
                                                    <th>Rating</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($place['beers'] as $index => $beer): ?>
                                                    <tr class="beer-row" data-beer-index="<?php echo $index; ?>">
                                                        <td>
                                                            <div class="beer-name-cell">
                                                                <strong class="text-warning"><?php echo htmlspecialchars($beer['name']); ?></strong>
                                                                <?php if (isset($beer['featured']) && $beer['featured']): ?>
                                                                    <span class="badge bg-success ms-2">Featured</span>
                                                                <?php endif; ?>
                                                                <?php if (isset($beer['description'])): ?>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($beer['description']); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($beer['style']); ?></span>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo $beer['abv']; ?></strong>
                                                        </td>
                                                        <td>
                                                            <strong class="text-success"><?php echo $beer['price']; ?></strong>
                                                        </td>
                                                        <td>
                                                            <div class="rating-display">
                                                                <span class="rating-stars">
                                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                        <?php if ($i <= floor($beer['rating'])): ?>
                                                                            ⭐
                                                                        <?php else: ?>
                                                                            ☆
                                                                        <?php endif; ?>
                                                                    <?php endfor; ?>
                                                                </span>
                                                                <small class="text-muted"><?php echo $beer['rating']; ?></small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php if (isLoggedIn()): ?>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button class="btn btn-outline-warning btn-sm" onclick="likeBeer(<?php echo $index; ?>)" title="Like">
                                                                        <i class="fas fa-heart"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-primary btn-sm" onclick="shareBeer(<?php echo $index; ?>)" title="Share">
                                                                        <i class="fas fa-share"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-success btn-sm" onclick="addToWishlist(<?php echo $index; ?>)" title="Add to Wishlist">
                                                                        <i class="fas fa-bookmark"></i>
                                                                    </button>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Food Menu Tab -->
                    <div class="tab-pane fade" id="food" role="tabpanel">
                        <div class="food-content">
                            <div class="food-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Food Menu</h4>
                                <span class="badge bg-success">Fresh Daily</span>
                            </div>

                            <!-- Featured Food Items Cards -->
                            <div class="featured-food-section mb-5">
                                <h4 class="mb-3">Featured Dishes</h4>
                                <div class="row g-3">
                                    <?php
                                    $featuredItems = [];
                                    foreach ($place['food_menu'] as $category) {
                                        foreach ($category['items'] as $item) {
                                            if (isset($item['featured']) && $item['featured'] === true) {
                                                $item['category'] = $category['category'];
                                                $featuredItems[] = $item;
                                            }
                                        }
                                    }
                                    foreach ($featuredItems as $item):
                                    ?>
                                        <div class="col-lg-6">
                                            <div class="food-item card h-100 border-0 shadow-sm">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <h6 class="card-title mb-0 text-warning fw-bold">
                                                            <?php echo htmlspecialchars($item['name']); ?>
                                                        </h6>
                                                        <span class="price-tag badge bg-primary fs-6">
                                                            <?php echo htmlspecialchars($item['price']); ?>
                                                        </span>
                                                    </div>
                                                    <div class="mb-2">
                                                        <span class="badge bg-secondary small"><?php echo htmlspecialchars($item['category']); ?></span>
                                                        <span class="badge bg-success small ms-1">Featured</span>
                                                    </div>
                                                    <p class="card-text text-muted small mb-3">
                                                        <?php echo htmlspecialchars($item['description']); ?>
                                                    </p>
                                                    <?php if (isLoggedIn()): ?>
                                                        <div class="food-actions d-flex gap-2">
                                                            <button class="btn btn-outline-warning btn-sm">
                                                                <i class="fas fa-heart me-1"></i>Like
                                                            </button>
                                                            <button class="btn btn-outline-primary btn-sm">
                                                                <i class="fas fa-share me-1"></i>Share
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Complete Food Menu by Category -->
                            <div class="food-menu-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">Complete Menu</h4>
                                    <span class="badge bg-primary">
                                        <?php
                                        $totalItems = 0;
                                        foreach ($place['food_menu'] as $category) {
                                            $totalItems += count($category['items']);
                                        }
                                        echo $totalItems;
                                        ?> Items Available
                                    </span>
                                </div>

                                <?php foreach ($place['food_menu'] as $category): ?>
                                    <div class="food-category mb-4">
                                        <h5 class="category-title mb-3 pb-2 border-bottom border-warning">
                                            <i class="fas fa-utensils text-warning me-2"></i>
                                            <?php echo htmlspecialchars($category['category']); ?>
                                        </h5>

                                        <div class="table-responsive">
                                            <table class="table table-dark table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Item</th>
                                                        <th>Description</th>
                                                        <th>Price</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($category['items'] as $index => $item): ?>
                                                        <tr class="food-row">
                                                            <td>
                                                                <div class="food-name-cell">
                                                                    <strong class="text-warning"><?php echo htmlspecialchars($item['name']); ?></strong>
                                                                    <?php if (isset($item['featured']) && $item['featured']): ?>
                                                                        <span class="badge bg-success ms-2">Featured</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <small class="text-muted"><?php echo htmlspecialchars($item['description']); ?></small>
                                                            </td>
                                                            <td>
                                                                <strong class="text-success"><?php echo $item['price']; ?></strong>
                                                            </td>
                                                            <td>
                                                                <?php if (isLoggedIn()): ?>
                                                                    <div class="btn-group btn-group-sm">
                                                                        <button class="btn btn-outline-warning btn-sm" onclick="likeFood('<?php echo $category['category']; ?>', <?php echo $index; ?>)" title="Like">
                                                                            <i class="fas fa-heart"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-primary btn-sm" onclick="shareFood('<?php echo $category['category']; ?>', <?php echo $index; ?>)" title="Share">
                                                                            <i class="fas fa-share"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-success btn-sm" onclick="addToFavorites('<?php echo $category['category']; ?>', <?php echo $index; ?>)" title="Add to Favorites">
                                                                            <i class="fas fa-bookmark"></i>
                                                                        </button>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Food Menu Info -->
                            <div class="food-info mt-4 p-4 bg-dark rounded border border-warning">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="text-warning mb-1">
                                            <i class="fas fa-info-circle me-2"></i>Menu Information
                                        </h6>
                                        <p class="text-light mb-0 small">
                                            All dishes are prepared fresh daily. Menu items may vary based on seasonal availability. 
                                            Please inform staff of any allergies or dietary restrictions.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="food-badges">
                                            <span class="badge bg-success me-1">Fresh Daily</span>
                                            <span class="badge bg-info">Local Sourced</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deals Tab -->
                    <div class="tab-pane fade" id="deals" role="tabpanel">
                        <div class="deals-content">
                            <div class="deals-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Current Deals & Offers</h4>
                                <span class="badge bg-success"><?php echo count($place['deals']); ?> Active Deals</span>
                            </div>

                            <div class="row g-4">
                                <?php foreach ($place['deals'] as $deal): ?>
                                    <div class="col-md-6">
                                        <div class="deal-card card border-0 shadow-sm">
                                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($deal['title']); ?></h6>
                                                <i class="fas fa-tags"></i>
                                            </div>
                                            <div class="card-body">
                                                <p class="card-text mb-3"><?php echo htmlspecialchars($deal['description']); ?></p>

                                                <!-- QR Code Section -->
                                                <div class="qr-section text-center mb-3 p-3 bg-light rounded">
                                                    <div class="qr-code-placeholder mb-2" style="width: 100px; height: 100px; margin: 0 auto; background: #333; color: white; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                                                        <i class="fas fa-qrcode fa-2x"></i>
                                                    </div>
                                                    <small class="text-muted">Show this code: <strong><?php echo $deal['qr_code']; ?></strong></small>
                                                </div>

                                                <div class="deal-details">
                                                    <div class="deal-validity mb-2">
                                                        <i class="fas fa-calendar-alt text-warning me-2"></i>
                                                        <small>Valid until: <?php echo date('M j, Y', strtotime($deal['valid_until'])); ?></small>
                                                    </div>
                                                    <div class="deal-terms">
                                                        <i class="fas fa-info-circle text-info me-2"></i>
                                                        <small class="text-muted"><?php echo htmlspecialchars($deal['terms']); ?></small>
                                                    </div>
                                                </div>

                                                <?php if (isLoggedIn()): ?>
                                                    <div class="deal-actions mt-3 d-flex gap-2">
                                                        <button class="btn btn-primary btn-sm flex-fill" onclick="saveDeal(<?php echo $deal['id']; ?>)">
                                                            <i class="fas fa-bookmark me-1"></i>Save Deal
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="shareDeal(<?php echo $deal['id']; ?>)">
                                                            <i class="fas fa-share me-1"></i>Share
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Reviews Tab -->
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <div class="reviews-content">
                            <div class="reviews-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Reviews (<?php echo $place['reviews_count']; ?>)</h4>
                                <?php if (isLoggedIn()): ?>
                                    <button class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Write Review
                                    </button>
                                <?php endif; ?>
                            </div>

                            <!-- Sample Reviews -->
                            <div class="review-item border-bottom pb-3 mb-3">
                                <div class="review-header d-flex justify-content-between mb-2">
                                    <div class="reviewer-info">
                                        <strong>Sarah K.</strong>
                                        <span class="rating-stars ms-2">⭐⭐⭐⭐⭐</span>
                                    </div>
                                    <small class="text-muted">2 days ago</small>
                                </div>
                                <p class="review-text">Amazing brewery with fantastic IPAs! The outdoor seating is perfect for summer evenings. Highly recommend the Hazy IPA Supreme.</p>
                            </div>

                            <div class="review-item border-bottom pb-3 mb-3">
                                <div class="review-header d-flex justify-content-between mb-2">
                                    <div class="reviewer-info">
                                        <strong>Mike R.</strong>
                                        <span class="rating-stars ms-2">⭐⭐⭐⭐</span>
                                    </div>
                                    <small class="text-muted">1 week ago</small>
                                </div>
                                <p class="review-text">Great atmosphere and friendly staff. The beer selection is excellent. Only wish they had more food options.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Photos Tab -->
                    <div class="tab-pane fade" id="photos" role="tabpanel">
                        <div class="photos-content">
                            <div class="photos-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Photos</h4>
                                <?php if (isLoggedIn()): ?>
                                    <button class="btn btn-primary btn-sm" onclick="uploadPhoto()">
                                        <i class="fas fa-camera me-2"></i>Add Photo
                                    </button>
                                <?php endif; ?>
                            </div>

                            <div class="row g-3" id="photo-gallery">
                                <?php
                                $photo_themes = [
                                    'Beer+Taps', 'Brewery+Interior', 'Beer+Barrels', 'Craft+Beer',
                                    'Beer+Garden', 'Brewing+Equipment', 'Beer+Flight', 'Taproom',
                                    'Beer+Bottles', 'Brewery+Staff', 'Beer+Food', 'Happy+Hour'
                                ];
                                for ($i = 1; $i <= 12; $i++):
                                    $theme = $photo_themes[($i-1) % count($photo_themes)];
                                    $bg_color = ['8B4513', 'A0522D', 'CD853F', 'DEB887', '654321', 'D2691E'][($i-1) % 6];
                                ?>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="photo-item position-relative">
                                            <a href="https://via.placeholder.com/800x600/<?php echo $bg_color; ?>/FFFFFF?text=<?php echo $theme; ?>+Large"
                                               data-fancybox="gallery"
                                               data-caption="<?php echo str_replace('+', ' ', $theme); ?> - <?php echo htmlspecialchars($place['name']); ?>">
                                                <img src="https://via.placeholder.com/300x200/<?php echo $bg_color; ?>/FFFFFF?text=<?php echo $theme; ?>"
                                                     class="img-fluid rounded photo-thumbnail" alt="<?php echo str_replace('+', ' ', $theme); ?>">
                                                <div class="photo-overlay">
                                                    <i class="fas fa-search-plus"></i>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 360° Tour Tab -->
                    <?php if ($place['has_360_tour']): ?>
                    <div class="tab-pane fade" id="tour" role="tabpanel">
                        <div class="tour-content">
                            <h4 class="mb-3">360° Virtual Tour</h4>
                            <div class="tour-container">
                                <div class="tour-iframe-wrapper" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                                    <iframe src="<?php echo htmlspecialchars($place['tour_url']); ?>"
                                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"
                                            allowfullscreen>
                                    </iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Contact Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-3">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            <div>
                                <strong>Address</strong><br>
                                <?php echo htmlspecialchars($place['address']); ?><br>
                                <?php echo htmlspecialchars($place['city'] . ', ' . $place['state'] . ' ' . $place['zip']); ?>
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-phone text-primary me-2"></i>
                            <div>
                                <strong>Phone</strong><br>
                                <a href="tel:<?php echo $place['phone']; ?>"><?php echo $place['phone']; ?></a>
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-globe text-primary me-2"></i>
                            <div>
                                <strong>Website</strong><br>
                                <a href="<?php echo $place['website']; ?>" target="_blank">Visit Website</a>
                            </div>
                        </div>

                        <!-- Social Media Links -->
                        <div class="contact-item">
                            <i class="fas fa-share-alt text-primary me-2"></i>
                            <div>
                                <strong>Follow Us</strong><br>
                                <div class="social-links mt-2">
                                    <?php if (!empty($place['social_media']['facebook'])): ?>
                                        <a href="<?php echo $place['social_media']['facebook']; ?>" target="_blank" class="social-link me-3" title="Facebook">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($place['social_media']['instagram'])): ?>
                                        <a href="<?php echo $place['social_media']['instagram']; ?>" target="_blank" class="social-link me-3" title="Instagram">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($place['social_media']['twitter'])): ?>
                                        <a href="<?php echo $place['social_media']['twitter']; ?>" target="_blank" class="social-link me-3" title="Twitter">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($place['social_media']['yelp'])): ?>
                                        <a href="<?php echo $place['social_media']['yelp']; ?>" target="_blank" class="social-link me-3" title="Yelp">
                                            <i class="fab fa-yelp"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($place['social_media']['google'])): ?>
                                        <a href="<?php echo $place['social_media']['google']; ?>" target="_blank" class="social-link me-3" title="Google">
                                            <i class="fab fa-google"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hours -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Hours
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($place['hours'] as $day => $hours): ?>
                            <div class="hours-item d-flex justify-content-between mb-2">
                                <span><?php echo $day; ?></span>
                                <span class="<?php echo $hours === 'Closed' ? 'text-muted' : ''; ?>">
                                    <?php echo $hours; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Claim Listing -->
                <?php if (!$place['is_claimed']): ?>
                <div class="card mb-4 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-building me-2"></i>Business Owner?
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">
                            <strong>Claim this listing</strong> to manage your business profile,
                            update information, respond to reviews, and access business tools.
                        </p>
                        <div class="claim-benefits mb-3">
                            <div class="benefit-item mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Update business information</small>
                            </div>
                            <div class="benefit-item mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Manage photos and menu</small>
                            </div>
                            <div class="benefit-item mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Respond to customer reviews</small>
                            </div>
                            <div class="benefit-item mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Access analytics and insights</small>
                            </div>
                            <div class="benefit-item mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Create deals and promotions</small>
                            </div>
                        </div>
                        <button class="btn btn-warning w-100" onclick="claimListing()">
                            <i class="fas fa-hand-paper me-2"></i>Claim This Listing
                        </button>
                    </div>
                </div>
                <?php else: ?>
                <div class="card mb-4 border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>Verified Business
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">
                            <i class="fas fa-shield-alt text-success me-2"></i>
                            This business has been claimed and verified by the owner.
                        </p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Fancybox CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />

<!-- Fancybox JS -->
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>

<script>
// Initialize Fancybox
Fancybox.bind("[data-fancybox]", {
    Toolbar: {
        display: {
            left: ["infobar"],
            middle: ["zoomIn", "zoomOut", "toggle1to1", "rotateCCW", "rotateCW", "flipX", "flipY"],
            right: ["slideshow", "thumbs", "close"],
        },
    },
    Thumbs: {
        autoStart: false,
    },
});

// Deal functions
function saveDeal(dealId) {
    alert('Deal saved! (Demo functionality)');
}

function shareDeal(dealId) {
    if (navigator.share) {
        navigator.share({
            title: 'Great Deal at <?php echo htmlspecialchars($place['name']); ?>',
            text: 'Check out this amazing deal!',
            url: window.location.href + '#deal-' + dealId
        });
    } else {
        alert('Deal link copied to clipboard! (Demo functionality)');
    }
}

function uploadPhoto() {
    alert('Photo upload functionality coming soon!');
}

// Check-in functionality
function checkInToPlace() {
    // Show check-in modal
    showCheckInModal();
}

function showCheckInModal() {
    const modalHtml = `
        <div class="modal fade" id="checkInModal" tabindex="-1" aria-labelledby="checkInModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="checkInModalLabel">
                            <i class="fas fa-check-circle me-2"></i>Check In to <?php echo htmlspecialchars($place['name']); ?>
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="checkInForm">
                            <div class="place-info mb-4 p-3 bg-light rounded">
                                <div class="d-flex align-items-center">
                                    <div class="place-icon me-3">
                                        <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($place['name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($place['address']); ?></small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="checkInMessage" class="form-label">What's happening? (Optional)</label>
                                <textarea class="form-control" id="checkInMessage" rows="3"
                                          placeholder="Share your experience, what you're drinking, or just say hi!"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="checkInRating" class="form-label">Rate your experience</label>
                                <div class="rating-input d-flex gap-2 align-items-center">
                                    <div class="star-rating" id="starRating">
                                        <i class="fas fa-star star-btn" data-rating="1"></i>
                                        <i class="fas fa-star star-btn" data-rating="2"></i>
                                        <i class="fas fa-star star-btn" data-rating="3"></i>
                                        <i class="fas fa-star star-btn" data-rating="4"></i>
                                        <i class="fas fa-star star-btn" data-rating="5"></i>
                                    </div>
                                    <span id="ratingText" class="text-muted">Click to rate</span>
                                </div>
                                <input type="hidden" id="checkInRating" value="0">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="shareToFeed" checked>
                                    <label class="form-check-label" for="shareToFeed">
                                        Share this check-in to my activity feed
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="shareToSocial">
                                    <label class="form-check-label" for="shareToSocial">
                                        Share to social media (Facebook, Twitter)
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitCheckIn()">
                            <i class="fas fa-check-circle me-2"></i>Check In Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page and show it
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('checkInModal'));
    modal.show();

    // Initialize star rating
    initializeStarRating();

    // Clean up modal when hidden
    document.getElementById('checkInModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function initializeStarRating() {
    const stars = document.querySelectorAll('.star-btn');
    const ratingText = document.getElementById('ratingText');
    const ratingInput = document.getElementById('checkInRating');

    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;

            // Update star display
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.add('text-warning');
                    s.classList.remove('text-muted');
                } else {
                    s.classList.add('text-muted');
                    s.classList.remove('text-warning');
                }
            });

            // Update rating text
            const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
            ratingText.textContent = ratingTexts[rating];
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.add('text-warning');
                } else {
                    s.classList.remove('text-warning');
                }
            });
        });
    });

    // Reset stars on mouse leave
    document.getElementById('starRating').addEventListener('mouseleave', function() {
        const currentRating = parseInt(ratingInput.value);
        stars.forEach((s, i) => {
            if (i < currentRating) {
                s.classList.add('text-warning');
                s.classList.remove('text-muted');
            } else {
                s.classList.add('text-muted');
                s.classList.remove('text-warning');
            }
        });
    });
}

function submitCheckIn() {
    const message = document.getElementById('checkInMessage').value;
    const rating = document.getElementById('checkInRating').value;
    const shareToFeed = document.getElementById('shareToFeed').checked;
    const shareToSocial = document.getElementById('shareToSocial').checked;

    // Show loading state
    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking In...';
    submitBtn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Hide modal
        bootstrap.Modal.getInstance(document.getElementById('checkInModal')).hide();

        // Show success message
        showCheckInSuccess(message, rating, shareToFeed, shareToSocial);

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function showCheckInSuccess(message, rating, shareToFeed, shareToSocial) {
    const successHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                <div>
                    <h6 class="alert-heading mb-1">Check-in Successful!</h6>
                    <small>You've checked in to <?php echo htmlspecialchars($place['name']); ?></small>
                    ${rating > 0 ? '<br><small>Rating: ' + '⭐'.repeat(rating) + '</small>' : ''}
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', successHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert-success');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// Favorite functionality
function toggleFavorite() {
    const heartBtn = event.target.closest('button');
    const heartIcon = heartBtn.querySelector('i');

    if (heartIcon.classList.contains('fas')) {
        // Remove from favorites
        heartIcon.classList.remove('fas');
        heartIcon.classList.add('far');
        heartBtn.classList.remove('btn-outline-light');
        heartBtn.classList.add('btn-outline-light');
        showToast('Removed from favorites', 'info');
    } else {
        // Add to favorites
        heartIcon.classList.remove('far');
        heartIcon.classList.add('fas');
        heartBtn.classList.remove('btn-outline-light');
        heartBtn.classList.add('btn-outline-danger');
        showToast('Added to favorites!', 'success');
    }
}

// Share functionality
function sharePlace() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($place['name']); ?>',
            text: 'Check out <?php echo htmlspecialchars($place['name']); ?> on Beersty!',
            url: window.location.href
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('Link copied to clipboard!', 'success');
        });
    }
}

// Toast notification helper
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0 position-fixed"
             style="top: 20px; right: 20px; z-index: 9999;" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.querySelector('.toast:last-child');
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Clean up after toast is hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Beer interaction functions
function likeBeer(beerIndex) {
    showToast('Beer added to favorites!', 'success');
}

function shareBeer(beerIndex) {
    const beers = <?php echo json_encode($place['beers']); ?>;
    const beer = beers[beerIndex];

    if (navigator.share) {
        navigator.share({
            title: beer.name + ' at <?php echo htmlspecialchars($place['name']); ?>',
            text: `Check out this ${beer.style} (${beer.abv} ABV) for ${beer.price}!`,
            url: window.location.href
        });
    } else {
        showToast('Beer shared!', 'success');
    }
}

function addToWishlist(beerIndex) {
    showToast('Beer added to wishlist!', 'info');
}

// Food interaction functions
function likeFood(category, itemIndex) {
    showToast('Food item added to favorites!', 'success');
}

function shareFood(category, itemIndex) {
    const foodMenu = <?php echo json_encode($place['food_menu']); ?>;
    const categoryData = foodMenu.find(cat => cat.category === category);
    const item = categoryData.items[itemIndex];

    if (navigator.share) {
        navigator.share({
            title: item.name + ' at <?php echo htmlspecialchars($place['name']); ?>',
            text: `${item.description} - ${item.price}`,
            url: window.location.href
        });
    } else {
        showToast('Food item shared!', 'success');
    }
}

function addToFavorites(category, itemIndex) {
    showToast('Food item added to favorites!', 'info');
}

// Claim listing functionality
function claimListing() {
    if (confirm('Are you the owner or manager of <?php echo htmlspecialchars($place['name']); ?>?\n\nBy claiming this listing, you agree to our Terms of Service and will need to verify your ownership.')) {
        // Show claim form modal or redirect to claim process
        showClaimForm();
    }
}

function showClaimForm() {
    // Create modal for claim form
    const modalHtml = `
        <div class="modal fade" id="claimModal" tabindex="-1" aria-labelledby="claimModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title" id="claimModalLabel">
                            <i class="fas fa-building me-2"></i>Claim Business Listing
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="claimForm">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Claiming: <?php echo htmlspecialchars($place['name']); ?></strong><br>
                                We'll need to verify your ownership before activating your business account.
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="claimFirstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="claimFirstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="claimLastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="claimLastName" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="claimEmail" class="form-label">Business Email *</label>
                                <input type="email" class="form-control" id="claimEmail" required>
                                <div class="form-text">We'll send verification instructions to this email.</div>
                            </div>

                            <div class="mb-3">
                                <label for="claimPhone" class="form-label">Business Phone *</label>
                                <input type="tel" class="form-control" id="claimPhone" required>
                            </div>

                            <div class="mb-3">
                                <label for="claimTitle" class="form-label">Your Title/Position *</label>
                                <select class="form-select" id="claimTitle" required>
                                    <option value="">Select your role...</option>
                                    <option value="owner">Owner</option>
                                    <option value="manager">Manager</option>
                                    <option value="marketing">Marketing Manager</option>
                                    <option value="authorized">Authorized Representative</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="claimVerification" class="form-label">Verification Method *</label>
                                <select class="form-select" id="claimVerification" required>
                                    <option value="">How would you like to verify ownership?</option>
                                    <option value="business_email">Business Email Verification</option>
                                    <option value="phone_call">Phone Call Verification</option>
                                    <option value="document">Business Document Upload</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="claimMessage" class="form-label">Additional Information</label>
                                <textarea class="form-control" id="claimMessage" rows="3"
                                          placeholder="Any additional information that might help us verify your ownership..."></textarea>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="claimTerms" required>
                                <label class="form-check-label" for="claimTerms">
                                    I agree to the <a href="/terms" target="_blank">Terms of Service</a> and
                                    <a href="/privacy" target="_blank">Privacy Policy</a> *
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="submitClaimForm()">
                            <i class="fas fa-paper-plane me-2"></i>Submit Claim Request
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page and show it
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('claimModal'));
    modal.show();

    // Clean up modal when hidden
    document.getElementById('claimModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function submitClaimForm() {
    const form = document.getElementById('claimForm');
    if (form.checkValidity()) {
        // Collect form data
        const formData = {
            place_id: <?php echo $place_id; ?>,
            first_name: document.getElementById('claimFirstName').value,
            last_name: document.getElementById('claimLastName').value,
            email: document.getElementById('claimEmail').value,
            phone: document.getElementById('claimPhone').value,
            title: document.getElementById('claimTitle').value,
            verification_method: document.getElementById('claimVerification').value,
            message: document.getElementById('claimMessage').value
        };

        // Show loading state
        const submitBtn = event.target;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
        submitBtn.disabled = true;

        // Simulate API call (replace with actual endpoint)
        setTimeout(() => {
            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('claimModal')).hide();

            // Show success message
            alert('Claim request submitted successfully!\\n\\nWe will review your request and contact you within 1-2 business days with verification instructions.\\n\\nThank you for your interest in managing your business listing on Beersty!');

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    } else {
        form.reportValidity();
    }
}
</script>

<?php
// Fix footer path for router context
if (file_exists('includes/footer.php')) {
    require_once 'includes/footer.php';
} else {
    require_once '../../includes/footer.php';
}
?>
