<?php
/**
 * Place Profile Page
 * Displays detailed information about a specific place
 */

require_once '../config/config.php';

// Get place ID from URL
$place_id = $_GET['id'] ?? 1;

// Sample place data (replace with database query)
$place = [
    'id' => 1,
    'name' => 'Craft Masters Brewery',
    'type' => 'Brewery',
    'description' => 'A local craft brewery specializing in innovative IPAs and seasonal brews. Family-owned and operated since 2015, we pride ourselves on quality ingredients and traditional brewing methods.',
    'address' => '123 Beer Street, Downtown District',
    'city' => 'Beer City',
    'state' => 'CA',
    'zip' => '90210',
    'phone' => '(555) 123-BEER',
    'website' => 'https://craftmastersbrewery.com',
    'email' => '<EMAIL>',
    'rating' => 4.8,
    'reviews_count' => 156,
    'price_range' => '$$',
    'hours' => [
        'Monday' => 'Closed',
        'Tuesday' => '4:00 PM - 10:00 PM',
        'Wednesday' => '4:00 PM - 10:00 PM',
        'Thursday' => '4:00 PM - 11:00 PM',
        'Friday' => '2:00 PM - 12:00 AM',
        'Saturday' => '12:00 PM - 12:00 AM',
        'Sunday' => '12:00 PM - 9:00 PM'
    ],
    'features' => ['Outdoor Seating', 'Live Music', 'Food Truck', 'Pet Friendly', 'Parking Available'],
    'images' => [
        '/assets/images/brewery1.jpg',
        '/assets/images/brewery2.jpg',
        '/assets/images/brewery3.jpg'
    ],
    'beers' => [
        ['name' => 'Hazy IPA Supreme', 'style' => 'IPA', 'abv' => '6.5%', 'rating' => 4.9, 'price' => '$7.50'],
        ['name' => 'Chocolate Stout', 'style' => 'Stout', 'abv' => '7.2%', 'rating' => 4.7, 'price' => '$8.00'],
        ['name' => 'Summer Wheat', 'style' => 'Wheat Beer', 'abv' => '4.8%', 'rating' => 4.5, 'price' => '$6.50'],
        ['name' => 'Double IPA Crusher', 'style' => 'Double IPA', 'abv' => '8.2%', 'rating' => 4.8, 'price' => '$9.00'],
        ['name' => 'Vanilla Porter', 'style' => 'Porter', 'abv' => '5.8%', 'rating' => 4.6, 'price' => '$7.75'],
        ['name' => 'Citrus Pale Ale', 'style' => 'Pale Ale', 'abv' => '5.2%', 'rating' => 4.4, 'price' => '$6.75']
    ],
    'food_menu' => [
        [
            'category' => 'Appetizers',
            'items' => [
                ['name' => 'Beer Cheese Pretzel', 'description' => 'Warm soft pretzel with house-made beer cheese dip', 'price' => '$12.99'],
                ['name' => 'Buffalo Cauliflower', 'description' => 'Crispy cauliflower tossed in buffalo sauce, served with ranch', 'price' => '$10.99'],
                ['name' => 'Loaded Nachos', 'description' => 'House-made tortilla chips with cheese, jalapeños, and sour cream', 'price' => '$14.99']
            ]
        ],
        [
            'category' => 'Mains',
            'items' => [
                ['name' => 'Brewery Burger', 'description' => '8oz beef patty with bacon, cheese, lettuce, tomato on brioche bun', 'price' => '$16.99'],
                ['name' => 'Fish & Chips', 'description' => 'Beer-battered cod with fries and coleslaw', 'price' => '$18.99'],
                ['name' => 'BBQ Pulled Pork Sandwich', 'description' => 'Slow-cooked pork with BBQ sauce on brioche bun', 'price' => '$15.99'],
                ['name' => 'Margherita Flatbread', 'description' => 'Fresh mozzarella, tomatoes, basil on house-made flatbread', 'price' => '$13.99']
            ]
        ],
        [
            'category' => 'Desserts',
            'items' => [
                ['name' => 'Chocolate Stout Cake', 'description' => 'Rich chocolate cake made with our signature stout', 'price' => '$8.99'],
                ['name' => 'Beer Float', 'description' => 'Vanilla ice cream with your choice of beer', 'price' => '$7.99']
            ]
        ]
    ],
    'deals' => [
        [
            'id' => 1,
            'title' => 'Happy Hour Special',
            'description' => '50% off all draft beers',
            'discount_type' => 'percentage',
            'discount_value' => 50,
            'valid_until' => '2024-12-31',
            'qr_code' => 'HAPPY50',
            'terms' => 'Valid Monday-Friday 3-6 PM only'
        ],
        [
            'id' => 2,
            'title' => 'Buy 2 Get 1 Free',
            'description' => 'Buy any 2 craft beers and get the 3rd one free',
            'discount_type' => 'bogo',
            'discount_value' => 0,
            'valid_until' => '2024-12-25',
            'qr_code' => 'BOGO3',
            'terms' => 'Applies to beers of equal or lesser value'
        ],
        [
            'id' => 3,
            'title' => 'Free Appetizer',
            'description' => 'Free appetizer with purchase of any entree',
            'discount_type' => 'free_item',
            'discount_value' => 0,
            'valid_until' => '2024-12-20',
            'qr_code' => 'FREEAPP',
            'terms' => 'One per table, dine-in only'
        ]
    ],
    'has_360_tour' => true,
    'tour_url' => 'https://example.com/360-tour',
    'is_claimed' => false,
    'claim_contact' => '<EMAIL>',
    'followers_count' => 245,
    'following_count' => 12
];

$pageTitle = $place['name'] . ' - ' . APP_NAME;
$additionalCSS = ['/beersty/assets/css/places.css'];

require_once '../includes/header.php';
?>

<!-- Place Hero Section -->
<section class="place-hero position-relative">
    <div class="hero-image" style="height: 400px; background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://via.placeholder.com/1200x400?text=<?php echo urlencode($place['name']); ?>') center/cover;">
        <div class="container h-100">
            <div class="row h-100 align-items-end">
                <div class="col-lg-8">
                    <div class="place-info text-white pb-4">
                        <div class="place-badges mb-3">
                            <span class="badge bg-primary fs-6 me-2"><?php echo $place['type']; ?></span>
                            <span class="badge bg-success fs-6"><?php echo $place['price_range']; ?></span>
                        </div>
                        <h1 class="display-5 fw-bold mb-3"><?php echo htmlspecialchars($place['name']); ?></h1>
                        <div class="place-meta d-flex flex-wrap gap-4 mb-3">
                            <div class="rating-display">
                                <span class="rating-stars fs-5">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <?php if ($i <= floor($place['rating'])): ?>
                                            ⭐
                                        <?php else: ?>
                                            ☆
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </span>
                                <span class="rating-text"><?php echo $place['rating']; ?> (<?php echo $place['reviews_count']; ?> reviews)</span>
                            </div>
                            <div class="location-info">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($place['address']); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end pb-4">
                    <?php if (isLoggedIn()): ?>
                        <div class="place-actions d-flex gap-2 justify-content-end">
                            <button class="btn btn-primary">
                                <i class="fas fa-check-circle me-2"></i>Check In
                            </button>
                            <button class="btn btn-outline-light">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="btn btn-outline-light">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Place Content -->
<section class="place-content py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="placeTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                            <i class="fas fa-info-circle me-2"></i>Overview
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="beers-tab" data-bs-toggle="tab" data-bs-target="#beers" type="button" role="tab">
                            <i class="fas fa-beer me-2"></i>Beers
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="food-tab" data-bs-toggle="tab" data-bs-target="#food" type="button" role="tab">
                            <i class="fas fa-utensils me-2"></i>Food Menu
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="deals-tab" data-bs-toggle="tab" data-bs-target="#deals" type="button" role="tab">
                            <i class="fas fa-tags me-2"></i>Deals
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab">
                            <i class="fas fa-star me-2"></i>Reviews
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="photos-tab" data-bs-toggle="tab" data-bs-target="#photos" type="button" role="tab">
                            <i class="fas fa-camera me-2"></i>Photos
                        </button>
                    </li>
                    <?php if ($place['has_360_tour']): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tour-tab" data-bs-toggle="tab" data-bs-target="#tour" type="button" role="tab">
                            <i class="fas fa-street-view me-2"></i>360° Tour
                        </button>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="placeTabContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div class="overview-content">
                            <h4 class="mb-3">About <?php echo htmlspecialchars($place['name']); ?></h4>
                            <p class="lead"><?php echo htmlspecialchars($place['description']); ?></p>
                            
                            <div class="features-section mt-4">
                                <h5 class="mb-3">Features & Amenities</h5>
                                <div class="row">
                                    <?php foreach ($place['features'] as $feature): ?>
                                        <div class="col-md-6 mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            <?php echo htmlspecialchars($feature); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Beers Tab -->
                    <div class="tab-pane fade" id="beers" role="tabpanel">
                        <div class="beers-content">
                            <h4 class="mb-3">Featured Beers</h4>
                            <div class="row g-3">
                                <?php foreach ($place['beers'] as $beer): ?>
                                    <div class="col-md-6">
                                        <div class="beer-card card">
                                            <div class="card-body">
                                                <h6 class="card-title"><?php echo htmlspecialchars($beer['name']); ?></h6>
                                                <p class="card-text text-muted mb-2">
                                                    <?php echo htmlspecialchars($beer['style']); ?> • <?php echo $beer['abv']; ?> ABV • <?php echo $beer['price']; ?>
                                                </p>
                                                <div class="beer-rating">
                                                    <span class="rating-stars">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <?php if ($i <= floor($beer['rating'])): ?>
                                                                ⭐
                                                            <?php else: ?>
                                                                ☆
                                                            <?php endif; ?>
                                                        <?php endfor; ?>
                                                    </span>
                                                    <span class="rating-text small"><?php echo $beer['rating']; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Food Menu Tab -->
                    <div class="tab-pane fade" id="food" role="tabpanel">
                        <div class="food-content">
                            <div class="food-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Food Menu</h4>
                                <span class="badge bg-success">Fresh Daily</span>
                            </div>

                            <?php foreach ($place['food_menu'] as $category): ?>
                                <div class="food-category mb-5">
                                    <h5 class="category-title mb-3 pb-2 border-bottom border-warning">
                                        <i class="fas fa-utensils text-warning me-2"></i>
                                        <?php echo htmlspecialchars($category['category']); ?>
                                    </h5>

                                    <div class="row g-3">
                                        <?php foreach ($category['items'] as $item): ?>
                                            <div class="col-lg-6">
                                                <div class="food-item card h-100 border-0 shadow-sm">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="card-title mb-0 text-warning fw-bold">
                                                                <?php echo htmlspecialchars($item['name']); ?>
                                                            </h6>
                                                            <span class="price-tag badge bg-primary fs-6">
                                                                <?php echo htmlspecialchars($item['price']); ?>
                                                            </span>
                                                        </div>
                                                        <p class="card-text text-muted small mb-3">
                                                            <?php echo htmlspecialchars($item['description']); ?>
                                                        </p>
                                                        <?php if (isLoggedIn()): ?>
                                                            <div class="food-actions d-flex gap-2">
                                                                <button class="btn btn-outline-warning btn-sm">
                                                                    <i class="fas fa-heart me-1"></i>Like
                                                                </button>
                                                                <button class="btn btn-outline-primary btn-sm">
                                                                    <i class="fas fa-share me-1"></i>Share
                                                                </button>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <!-- Food Menu Info -->
                            <div class="food-info mt-4 p-4 bg-dark rounded border border-warning">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="text-warning mb-1">
                                            <i class="fas fa-info-circle me-2"></i>Menu Information
                                        </h6>
                                        <p class="text-light mb-0 small">
                                            All dishes are prepared fresh daily. Menu items may vary based on seasonal availability.
                                            Please inform staff of any allergies or dietary restrictions.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="food-badges">
                                            <span class="badge bg-success me-1">Fresh Daily</span>
                                            <span class="badge bg-info">Local Sourced</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deals Tab -->
                    <div class="tab-pane fade" id="deals" role="tabpanel">
                        <div class="deals-content">
                            <div class="deals-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Current Deals & Offers</h4>
                                <span class="badge bg-success"><?php echo count($place['deals']); ?> Active Deals</span>
                            </div>

                            <div class="row g-4">
                                <?php foreach ($place['deals'] as $deal): ?>
                                    <div class="col-md-6">
                                        <div class="deal-card card border-0 shadow-sm">
                                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($deal['title']); ?></h6>
                                                <i class="fas fa-tags"></i>
                                            </div>
                                            <div class="card-body">
                                                <p class="card-text mb-3"><?php echo htmlspecialchars($deal['description']); ?></p>

                                                <!-- QR Code Section -->
                                                <div class="qr-section text-center mb-3 p-3 bg-light rounded">
                                                    <div class="qr-code-placeholder mb-2" style="width: 100px; height: 100px; margin: 0 auto; background: #333; color: white; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                                                        <i class="fas fa-qrcode fa-2x"></i>
                                                    </div>
                                                    <small class="text-muted">Show this code: <strong><?php echo $deal['qr_code']; ?></strong></small>
                                                </div>

                                                <div class="deal-details">
                                                    <div class="deal-validity mb-2">
                                                        <i class="fas fa-calendar-alt text-warning me-2"></i>
                                                        <small>Valid until: <?php echo date('M j, Y', strtotime($deal['valid_until'])); ?></small>
                                                    </div>
                                                    <div class="deal-terms">
                                                        <i class="fas fa-info-circle text-info me-2"></i>
                                                        <small class="text-muted"><?php echo htmlspecialchars($deal['terms']); ?></small>
                                                    </div>
                                                </div>

                                                <?php if (isLoggedIn()): ?>
                                                    <div class="deal-actions mt-3 d-flex gap-2">
                                                        <button class="btn btn-primary btn-sm flex-fill" onclick="saveDeal(<?php echo $deal['id']; ?>)">
                                                            <i class="fas fa-bookmark me-1"></i>Save Deal
                                                        </button>
                                                        <button class="btn btn-outline-primary btn-sm" onclick="shareDeal(<?php echo $deal['id']; ?>)">
                                                            <i class="fas fa-share me-1"></i>Share
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Deal Subscription -->
                            <?php if (isLoggedIn()): ?>
                                <div class="deal-subscription mt-4 p-4 bg-light rounded">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h6 class="mb-1">Get notified about new deals</h6>
                                            <p class="text-muted mb-0">Be the first to know when <?php echo htmlspecialchars($place['name']); ?> posts new offers</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <button class="btn btn-outline-primary" onclick="toggleDealNotifications()">
                                                <i class="fas fa-bell me-2"></i>Follow Deals
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Reviews Tab -->
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <div class="reviews-content">
                            <div class="reviews-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Reviews (<?php echo $place['reviews_count']; ?>)</h4>
                                <?php if (isLoggedIn()): ?>
                                    <button class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Write Review
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Sample Reviews -->
                            <div class="review-item border-bottom pb-3 mb-3">
                                <div class="review-header d-flex justify-content-between mb-2">
                                    <div class="reviewer-info">
                                        <strong>Sarah K.</strong>
                                        <span class="rating-stars ms-2">⭐⭐⭐⭐⭐</span>
                                    </div>
                                    <small class="text-muted">2 days ago</small>
                                </div>
                                <p class="review-text">Amazing brewery with fantastic IPAs! The outdoor seating is perfect for summer evenings. Highly recommend the Hazy IPA Supreme.</p>
                            </div>
                            
                            <div class="review-item border-bottom pb-3 mb-3">
                                <div class="review-header d-flex justify-content-between mb-2">
                                    <div class="reviewer-info">
                                        <strong>Mike R.</strong>
                                        <span class="rating-stars ms-2">⭐⭐⭐⭐</span>
                                    </div>
                                    <small class="text-muted">1 week ago</small>
                                </div>
                                <p class="review-text">Great atmosphere and friendly staff. The beer selection is excellent. Only wish they had more food options.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Photos Tab -->
                    <div class="tab-pane fade" id="photos" role="tabpanel">
                        <div class="photos-content">
                            <div class="photos-header d-flex justify-content-between align-items-center mb-4">
                                <h4 class="mb-0">Photos</h4>
                                <?php if (isLoggedIn()): ?>
                                    <button class="btn btn-primary btn-sm" onclick="uploadPhoto()">
                                        <i class="fas fa-camera me-2"></i>Add Photo
                                    </button>
                                <?php endif; ?>
                            </div>

                            <div class="photo-categories mb-4">
                                <ul class="nav nav-pills">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#" data-category="all">All Photos</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" data-category="food">Food & Drinks</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" data-category="interior">Interior</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" data-category="exterior">Exterior</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#" data-category="events">Events</a>
                                    </li>
                                </ul>
                            </div>

                            <div class="row g-3" id="photo-gallery">
                                <?php for ($i = 1; $i <= 12; $i++): ?>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="photo-item position-relative">
                                            <a href="https://via.placeholder.com/800x600?text=Large+Photo+<?php echo $i; ?>"
                                               data-fancybox="gallery"
                                               data-caption="Photo <?php echo $i; ?> - <?php echo htmlspecialchars($place['name']); ?>">
                                                <img src="https://via.placeholder.com/300x200?text=Photo+<?php echo $i; ?>"
                                                     class="img-fluid rounded photo-thumbnail" alt="Photo <?php echo $i; ?>">
                                                <div class="photo-overlay">
                                                    <i class="fas fa-search-plus"></i>
                                                </div>
                                            </a>
                                            <?php if (isLoggedIn()): ?>
                                                <div class="photo-actions position-absolute top-0 end-0 m-2">
                                                    <button class="btn btn-sm btn-light rounded-circle" onclick="likePhoto(<?php echo $i; ?>)">
                                                        <i class="fas fa-heart"></i>
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <div class="text-center mt-4">
                                <button class="btn btn-outline-primary" onclick="loadMorePhotos()">
                                    <i class="fas fa-images me-2"></i>Load More Photos
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 360° Tour Tab -->
                    <?php if ($place['has_360_tour']): ?>
                    <div class="tab-pane fade" id="tour" role="tabpanel">
                        <div class="tour-content">
                            <h4 class="mb-3">360° Virtual Tour</h4>
                            <div class="tour-container">
                                <div class="tour-iframe-wrapper" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                                    <iframe src="<?php echo htmlspecialchars($place['tour_url']); ?>"
                                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"
                                            allowfullscreen>
                                    </iframe>
                                </div>
                                <div class="tour-info mt-3 p-3 bg-light rounded">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6>Interactive Virtual Tour</h6>
                                            <p class="text-muted mb-0">Explore <?php echo htmlspecialchars($place['name']); ?> from the comfort of your home. Click and drag to look around, and click on hotspots for more information.</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <button class="btn btn-primary" onclick="openFullscreenTour()">
                                                <i class="fas fa-expand me-2"></i>Fullscreen
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Social Stats & Follow -->
                <?php if (isLoggedIn()): ?>
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row g-3 mb-3">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h5 class="mb-1"><?php echo number_format($place['followers_count']); ?></h5>
                                    <small class="text-muted">Followers</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h5 class="mb-1"><?php echo number_format($place['following_count']); ?></h5>
                                    <small class="text-muted">Following</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h5 class="mb-1"><?php echo number_format($place['reviews_count']); ?></h5>
                                    <small class="text-muted">Reviews</small>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="followPlace(<?php echo $place['id']; ?>)">
                                <i class="fas fa-plus me-2"></i>Follow Place
                            </button>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary" onclick="sharePlace()">
                                    <i class="fas fa-share me-1"></i>Share
                                </button>
                                <button class="btn btn-outline-secondary" onclick="savePlace()">
                                    <i class="fas fa-bookmark me-1"></i>Save
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Claim Listing -->
                <?php if (!$place['is_claimed']): ?>
                <div class="card mb-4 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Claim This Listing
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-3">Are you the owner or manager of <?php echo htmlspecialchars($place['name']); ?>?</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="claimListing(<?php echo $place['id']; ?>)">
                                <i class="fas fa-key me-2"></i>Claim This Business
                            </button>
                            <small class="text-muted text-center">
                                Claiming allows you to update information, respond to reviews, and post deals.
                            </small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Contact Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Contact Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="contact-item mb-3">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            <div>
                                <strong>Address</strong><br>
                                <?php echo htmlspecialchars($place['address']); ?><br>
                                <?php echo htmlspecialchars($place['city'] . ', ' . $place['state'] . ' ' . $place['zip']); ?>
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-phone text-primary me-2"></i>
                            <div>
                                <strong>Phone</strong><br>
                                <a href="tel:<?php echo $place['phone']; ?>"><?php echo $place['phone']; ?></a>
                            </div>
                        </div>
                        <div class="contact-item mb-3">
                            <i class="fas fa-globe text-primary me-2"></i>
                            <div>
                                <strong>Website</strong><br>
                                <a href="<?php echo $place['website']; ?>" target="_blank">Visit Website</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hours -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Hours
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($place['hours'] as $day => $hours): ?>
                            <div class="hours-item d-flex justify-content-between mb-2">
                                <span><?php echo $day; ?></span>
                                <span class="<?php echo $hours === 'Closed' ? 'text-muted' : ''; ?>">
                                    <?php echo $hours; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Map -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-map me-2"></i>Location
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="map-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <div class="text-center text-muted">
                                <i class="fas fa-map fa-3x mb-2"></i>
                                <p>Interactive Map<br>Coming Soon</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Fancybox CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />

<!-- Fancybox JS -->
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>

<!-- Place Profile JavaScript -->
<script>
// Initialize Fancybox
Fancybox.bind("[data-fancybox]", {
    Toolbar: {
        display: {
            left: ["infobar"],
            middle: [
                "zoomIn",
                "zoomOut",
                "toggle1to1",
                "rotateCCW",
                "rotateCW",
                "flipX",
                "flipY",
            ],
            right: ["slideshow", "thumbs", "close"],
        },
    },
    Thumbs: {
        autoStart: false,
    },
});

// Follow place functionality
function followPlace(placeId) {
    fetch('/api/follow-place.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            place_id: placeId,
            action: 'follow'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button text and follower count
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-check me-2"></i>Following';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');
            btn.onclick = () => unfollowPlace(placeId);

            // Update follower count
            const followerCount = document.querySelector('.stat-item h5');
            if (followerCount) {
                const current = parseInt(followerCount.textContent.replace(/,/g, ''));
                followerCount.textContent = (current + 1).toLocaleString();
            }
        } else {
            alert('Error following place: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error following place');
    });
}

// Unfollow place functionality
function unfollowPlace(placeId) {
    fetch('/api/follow-place.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            place_id: placeId,
            action: 'unfollow'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button text and follower count
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-plus me-2"></i>Follow Place';
            btn.classList.remove('btn-success');
            btn.classList.add('btn-primary');
            btn.onclick = () => followPlace(placeId);

            // Update follower count
            const followerCount = document.querySelector('.stat-item h5');
            if (followerCount) {
                const current = parseInt(followerCount.textContent.replace(/,/g, ''));
                followerCount.textContent = Math.max(0, current - 1).toLocaleString();
            }
        } else {
            alert('Error unfollowing place: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error unfollowing place');
    });
}

// Claim listing functionality
function claimListing(placeId) {
    if (confirm('Are you sure you want to claim this business listing? You will need to verify your ownership.')) {
        fetch('/api/claim-listing.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                place_id: placeId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Claim request submitted! We will contact you within 24 hours to verify ownership.');
                location.reload();
            } else {
                alert('Error submitting claim: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error submitting claim request');
        });
    }
}

// Save deal functionality
function saveDeal(dealId) {
    fetch('/api/save-deal.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            deal_id: dealId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Saved';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-success');
            btn.disabled = true;
        } else {
            alert('Error saving deal: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving deal');
    });
}

// Share deal functionality
function shareDeal(dealId) {
    if (navigator.share) {
        navigator.share({
            title: 'Great Deal at <?php echo htmlspecialchars($place['name']); ?>',
            text: 'Check out this amazing deal!',
            url: window.location.href + '#deal-' + dealId
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href + '#deal-' + dealId;
        navigator.clipboard.writeText(url).then(() => {
            alert('Deal link copied to clipboard!');
        });
    }
}

// Toggle deal notifications
function toggleDealNotifications() {
    fetch('/api/toggle-deal-notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            place_id: <?php echo $place['id']; ?>
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const btn = event.target;
            if (data.subscribed) {
                btn.innerHTML = '<i class="fas fa-bell-slash me-2"></i>Unfollow Deals';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-primary');
            } else {
                btn.innerHTML = '<i class="fas fa-bell me-2"></i>Follow Deals';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-primary');
            }
        } else {
            alert('Error updating notification preferences: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating notification preferences');
    });
}

// Share place functionality
function sharePlace() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($place['name']); ?>',
            text: 'Check out this amazing place on Beersty!',
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Place link copied to clipboard!');
        });
    }
}

// Save place functionality
function savePlace() {
    fetch('/api/save-place.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            place_id: <?php echo $place['id']; ?>
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const btn = event.target;
            btn.innerHTML = '<i class="fas fa-check me-1"></i>Saved';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');
        } else {
            alert('Error saving place: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving place');
    });
}

// Photo category filtering
document.querySelectorAll('[data-category]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();

        // Update active state
        document.querySelectorAll('.nav-pills .nav-link').forEach(l => l.classList.remove('active'));
        this.classList.add('active');

        // Filter photos (placeholder - would filter based on actual photo categories)
        const category = this.dataset.category;
        console.log('Filtering photos by category:', category);
    });
});

// Upload photo functionality
function uploadPhoto() {
    alert('Photo upload functionality would be implemented here');
}

// Like photo functionality
function likePhoto(photoId) {
    fetch('/api/like-photo.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            photo_id: photoId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const btn = event.target;
            btn.classList.toggle('text-danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Load more photos
function loadMorePhotos() {
    alert('Load more photos functionality would be implemented here');
}

// Open fullscreen tour
function openFullscreenTour() {
    const tourUrl = '<?php echo htmlspecialchars($place['tour_url']); ?>';
    window.open(tourUrl, '_blank', 'fullscreen=yes');
}
</script>

<?php include '../includes/footer.php'; ?>
