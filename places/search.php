<?php
/**
 * Places Search Page
 * Displays search results for breweries, bars, and beer places
 */

require_once '../config/config.php';

$pageTitle = 'Search Places - ' . APP_NAME;
$additionalCSS = ['/assets/css/search.css', '/assets/css/places.css'];
$additionalJS = ['/assets/js/image-fallback.js'];

// Get search parameters
$search = $_GET['search'] ?? '';
$location = $_GET['location'] ?? '';
$category = $_GET['category'] ?? '';
$sort = $_GET['sort'] ?? 'relevance';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 12;

// Database query for places
$places = [];
$totalPlaces = 0;

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Build WHERE clause based on search parameters
    $whereConditions = [];
    $params = [];

    if (!empty($search)) {
        $whereConditions[] = "(name LIKE ? OR description LIKE ? OR city LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($location)) {
        $whereConditions[] = "(city LIKE ? OR state LIKE ? OR address LIKE ?)";
        $locationTerm = "%$location%";
        $params[] = $locationTerm;
        $params[] = $locationTerm;
        $params[] = $locationTerm;
    }

    if (!empty($category)) {
        $whereConditions[] = "brewery_type = ?";
        $params[] = $category;
    }

    // Base query
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // Count total results
    $countSql = "SELECT COUNT(*) as total FROM breweries $whereClause";
    $stmt = $conn->prepare($countSql);
    $stmt->execute($params);
    $totalPlaces = $stmt->fetch()['total'];

    // Build ORDER BY clause
    $orderBy = 'ORDER BY ';
    switch ($sort) {
        case 'name':
            $orderBy .= 'name ASC';
            break;
        case 'rating':
            $orderBy .= 'like_count DESC, follower_count DESC';
            break;
        case 'distance':
            $orderBy .= 'city ASC, name ASC';
            break;
        default:
            $orderBy .= 'verified DESC, created_at DESC';
    }

    // Calculate offset for pagination
    $offset = ($page - 1) * $per_page;

    // Get places with pagination
    $sql = "SELECT id, name, address, city, state, zip, phone, email, website, description,
                   brewery_type, verified, like_count, follower_count, logo, feature_image
            FROM breweries
            $whereClause
            $orderBy
            LIMIT $per_page OFFSET $offset";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $breweries = $stmt->fetchAll();

    // Transform brewery data to places format
    foreach ($breweries as $brewery) {
        // Generate SEO-friendly slug (must match function in places/profile/index.php)
        $slug = strtolower(str_replace([' ', '&', '.', "'"], ['-', 'and', '', ''], $brewery['name']));
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        $places[] = [
            'id' => $brewery['id'],
            'name' => $brewery['name'],
            'slug' => $slug,
            'type' => ucfirst($brewery['brewery_type'] ?? 'Brewery'),
            'address' => $brewery['address'] . ', ' . $brewery['city'] . ', ' . $brewery['state'],
            'rating' => min(5.0, 3.5 + (($brewery['like_count'] ?? 0) / 100)), // Generate rating based on likes
            'reviews' => ($brewery['like_count'] ?? 0) + ($brewery['follower_count'] ?? 0),
            'image' => $brewery['feature_image'] ?: '/placeholders/450x300_beer_placeholder' . (($brewery['id'] % 3) + 1) . '.jpg',
            'distance' => rand(1, 50) / 10 . ' miles', // Random distance for now
            'price_range' => ['$', '$$', '$$$'][array_rand(['$', '$$', '$$$'])],
            'features' => ['Craft Beer', 'Local Brewery', $brewery['brewery_type'] ? ucfirst($brewery['brewery_type']) : 'Brewery']
        ];
    }

} catch (Exception $e) {
    error_log("Error fetching places: " . $e->getMessage());
    $places = [];
    $totalPlaces = 0;
}

require_once '../includes/header.php';
?>

<!-- Search Header -->
<section class="search-header bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h3 mb-3">
                    <i class="fas fa-search me-2 text-primary"></i>
                    Search Places
                </h1>
                
                <!-- Search Form -->
                <form method="GET" class="search-form">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Search places..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <input type="text" name="location" class="form-control" 
                                       placeholder="Location" value="<?php echo htmlspecialchars($location); ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                <option value="brewery" <?php echo $category === 'brewery' ? 'selected' : ''; ?>>Breweries</option>
                                <option value="bar" <?php echo $category === 'bar' ? 'selected' : ''; ?>>Bars</option>
                                <option value="restaurant" <?php echo $category === 'restaurant' ? 'selected' : ''; ?>>Restaurants</option>
                                <option value="beer_garden" <?php echo $category === 'beer_garden' ? 'selected' : ''; ?>>Beer Gardens</option>
                                <option value="party_store" <?php echo $category === 'party_store' ? 'selected' : ''; ?>>Party Stores</option>
                                <option value="liquor_store" <?php echo $category === 'liquor_store' ? 'selected' : ''; ?>>Liquor Stores</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-lg-4 text-end">
                <div class="view-toggle btn-group" role="group">
                    <input type="radio" class="btn-check" name="view" id="grid-view" checked>
                    <label class="btn btn-outline-secondary" for="grid-view">
                        <i class="fas fa-th-large"></i>
                    </label>
                    <input type="radio" class="btn-check" name="view" id="list-view">
                    <label class="btn btn-outline-secondary" for="list-view">
                        <i class="fas fa-list"></i>
                    </label>
                    <input type="radio" class="btn-check" name="view" id="map-view">
                    <label class="btn btn-outline-secondary" for="map-view">
                        <i class="fas fa-map"></i>
                    </label>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Results -->
<section class="search-results py-4">
    <div class="container">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3">
                <div class="filters-sidebar">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i>Filters
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Distance Filter -->
                            <div class="filter-group mb-4">
                                <h6 class="filter-title">Distance</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="distance" id="distance1" value="1">
                                    <label class="form-check-label" for="distance1">Within 1 mile</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="distance" id="distance5" value="5">
                                    <label class="form-check-label" for="distance5">Within 5 miles</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="distance" id="distance10" value="10">
                                    <label class="form-check-label" for="distance10">Within 10 miles</label>
                                </div>
                            </div>
                            
                            <!-- Rating Filter -->
                            <div class="filter-group mb-4">
                                <h6 class="filter-title">Rating</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rating5">
                                    <label class="form-check-label" for="rating5">
                                        ⭐⭐⭐⭐⭐ 5 stars
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rating4">
                                    <label class="form-check-label" for="rating4">
                                        ⭐⭐⭐⭐ 4+ stars
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rating3">
                                    <label class="form-check-label" for="rating3">
                                        ⭐⭐⭐ 3+ stars
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Features Filter -->
                            <div class="filter-group mb-4">
                                <h6 class="filter-title">Features</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="outdoor">
                                    <label class="form-check-label" for="outdoor">Outdoor Seating</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="food">
                                    <label class="form-check-label" for="food">Food Available</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="live_music">
                                    <label class="form-check-label" for="live_music">Live Music</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="pet_friendly">
                                    <label class="form-check-label" for="pet_friendly">Pet Friendly</label>
                                </div>
                            </div>
                            
                            <button class="btn btn-outline-secondary btn-sm w-100">
                                <i class="fas fa-times me-1"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Results Content -->
            <div class="col-lg-9">
                <!-- Results Header -->
                <div class="results-header d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h5 class="mb-1"><?php echo number_format($totalPlaces); ?> places found</h5>
                        <?php if ($search || $location): ?>
                            <p class="text-muted mb-0">
                                <?php if ($search): ?>
                                    for "<?php echo htmlspecialchars($search); ?>"
                                <?php endif; ?>
                                <?php if ($location): ?>
                                    near "<?php echo htmlspecialchars($location); ?>"
                                <?php endif; ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    <div class="sort-options">
                        <form method="GET" class="d-inline">
                            <?php if ($search): ?><input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>"><?php endif; ?>
                            <?php if ($location): ?><input type="hidden" name="location" value="<?php echo htmlspecialchars($location); ?>"><?php endif; ?>
                            <?php if ($category): ?><input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>"><?php endif; ?>
                            <select name="sort" class="form-select form-select-sm" onchange="this.form.submit()">
                                <option value="relevance" <?php echo $sort === 'relevance' ? 'selected' : ''; ?>>Sort by Relevance</option>
                                <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>Highest Rated</option>
                                <option value="distance" <?php echo $sort === 'distance' ? 'selected' : ''; ?>>Nearest First</option>
                                <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                            </select>
                        </form>
                    </div>
                </div>
                
                <!-- Places Grid -->
                <div class="places-grid" id="places-grid">
                    <div class="row g-4">
                        <?php foreach ($places as $place): ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="place-card card h-100 shadow-sm">
                                    <div class="place-image position-relative">
                                        <?php
                                        // Use actual image if available, otherwise use placeholder
                                        $imageUrl = !empty($place['image']) ? $place['image'] : '/placeholders/450x300_beer_placeholder' . (($place['id'] % 3) + 1) . '.jpg';
                                        ?>
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                             class="card-img-top place-img"
                                             alt="<?php echo htmlspecialchars($place['name']); ?>"
                                             style="height: 200px; object-fit: cover;"
                                             data-type="place"
                                             loading="lazy">
                                        <div class="place-badge position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-primary"><?php echo htmlspecialchars($place['type']); ?></span>
                                        </div>
                                        <div class="place-distance position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-dark"><?php echo htmlspecialchars($place['distance']); ?></span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title mb-2">
                                            <a href="/places/<?php echo $place['slug']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($place['name']); ?>
                                            </a>
                                        </h6>
                                        <p class="card-text text-muted small mb-2">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($place['address']); ?>
                                        </p>
                                        <div class="place-rating mb-2">
                                            <span class="rating-stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= floor($place['rating'])): ?>
                                                        ⭐
                                                    <?php elseif ($i - 0.5 <= $place['rating']): ?>
                                                        ⭐
                                                    <?php else: ?>
                                                        ☆
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </span>
                                            <span class="rating-text small text-muted">
                                                <?php echo $place['rating']; ?> (<?php echo $place['reviews']; ?> reviews)
                                            </span>
                                        </div>
                                        <div class="place-features mb-3">
                                            <?php foreach (array_slice($place['features'], 0, 2) as $feature): ?>
                                                <span class="badge bg-light text-dark me-1"><?php echo $feature; ?></span>
                                            <?php endforeach; ?>
                                            <?php if (count($place['features']) > 2): ?>
                                                <span class="badge bg-light text-dark">+<?php echo count($place['features']) - 2; ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="place-actions d-flex gap-2">
                                            <a href="/places/<?php echo $place['slug']; ?>" class="btn btn-sm flex-fill" style="background-color: #FFC107; border-color: #FFC107; color: #3B2A2A; font-weight: bold;">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                            <?php if (isLoggedIn()): ?>
                                                <button class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-heart"></i>
                                                </button>
                                                <button class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPlaces > $per_page): ?>
                    <?php
                    $totalPages = ceil($totalPlaces / $per_page);
                    $currentPage = $page;

                    // Build query string for pagination links
                    $queryParams = [];
                    if ($search) $queryParams['search'] = $search;
                    if ($location) $queryParams['location'] = $location;
                    if ($category) $queryParams['category'] = $category;
                    if ($sort !== 'relevance') $queryParams['sort'] = $sort;

                    function buildPaginationUrl($page, $queryParams) {
                        $queryParams['page'] = $page;
                        return '?' . http_build_query($queryParams);
                    }
                    ?>
                    <nav aria-label="Search results pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <!-- Previous -->
                            <li class="page-item <?php echo $currentPage <= 1 ? 'disabled' : ''; ?>">
                                <?php if ($currentPage <= 1): ?>
                                    <span class="page-link">Previous</span>
                                <?php else: ?>
                                    <a class="page-link" href="<?php echo buildPaginationUrl($currentPage - 1, $queryParams); ?>">Previous</a>
                                <?php endif; ?>
                            </li>

                            <!-- Page numbers -->
                            <?php
                            $startPage = max(1, $currentPage - 2);
                            $endPage = min($totalPages, $currentPage + 2);

                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                                <li class="page-item <?php echo $i === $currentPage ? 'active' : ''; ?>">
                                    <?php if ($i === $currentPage): ?>
                                        <span class="page-link"><?php echo $i; ?></span>
                                    <?php else: ?>
                                        <a class="page-link" href="<?php echo buildPaginationUrl($i, $queryParams); ?>"><?php echo $i; ?></a>
                                    <?php endif; ?>
                                </li>
                            <?php endfor; ?>

                            <!-- Next -->
                            <li class="page-item <?php echo $currentPage >= $totalPages ? 'disabled' : ''; ?>">
                                <?php if ($currentPage >= $totalPages): ?>
                                    <span class="page-link">Next</span>
                                <?php else: ?>
                                    <a class="page-link" href="<?php echo buildPaginationUrl($currentPage + 1, $queryParams); ?>">Next</a>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php include '../includes/footer.php'; ?>
