<?php
/**
 * Authentication Helper Functions
 * Provides authentication and authorization functions for the social platform
 */

// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Require user to be logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        // Store the current URL to redirect back after login
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        
        // Redirect to login page
        header('Location: ' . getBaseUrl() . '/login/');
        exit;
    }
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    return $_SESSION['user_role'] === $role;
}

/**
 * Require user to have specific role
 */
function requireRole($role) {
    requireLogin();
    
    if (!hasRole($role)) {
        // Redirect to unauthorized page or dashboard based on current role
        if ($_SESSION['user_role'] === 'admin') {
            header('Location: ' . getBaseUrl() . '/admin/dashboard.php');
        } elseif ($_SESSION['user_role'] === 'brewery') {
            header('Location: ' . getBaseUrl() . '/brewery/profile.php');
        } else {
            header('Location: ' . getBaseUrl() . '/index.php');
        }
        exit;
    }
}

/**
 * Get current user information
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'email' => $_SESSION['user_email'],
        'role' => $_SESSION['user_role'],
        'brewery_id' => $_SESSION['brewery_id'] ?? null,
        'first_name' => $_SESSION['first_name'] ?? '',
        'last_name' => $_SESSION['last_name'] ?? '',
        'username' => $_SESSION['username'] ?? '',
        'avatar' => $_SESSION['avatar'] ?? null
    ];
}

/**
 * Get user's full profile from database
 */
function getUserProfile($user_id = null) {
    if (!$user_id) {
        $current_user = getCurrentUser();
        if (!$current_user) {
            return null;
        }
        $user_id = $current_user['id'];
    }
    
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            SELECT u.*, p.* 
            FROM users u 
            JOIN profiles p ON u.id = p.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

/**
 * Update user session with fresh data
 */
function refreshUserSession($user_id = null) {
    if (!$user_id) {
        $current_user = getCurrentUser();
        if (!$current_user) {
            return false;
        }
        $user_id = $current_user['id'];
    }
    
    $profile = getUserProfile($user_id);
    if ($profile) {
        $_SESSION['user_id'] = $profile['id'];
        $_SESSION['user_email'] = $profile['email'];
        $_SESSION['user_role'] = $profile['role'];
        $_SESSION['brewery_id'] = $profile['brewery_id'];
        $_SESSION['first_name'] = $profile['first_name'];
        $_SESSION['last_name'] = $profile['last_name'];
        $_SESSION['username'] = $profile['username'];
        $_SESSION['avatar'] = $profile['avatar'];
        return true;
    }
    
    return false;
}

/**
 * Check if user owns a resource
 */
function isOwner($resource_user_id) {
    $current_user = getCurrentUser();
    if (!$current_user) {
        return false;
    }
    
    return $current_user['id'] === $resource_user_id;
}

/**
 * Check if user can access resource (owner or admin)
 */
function canAccess($resource_user_id) {
    $current_user = getCurrentUser();
    if (!$current_user) {
        return false;
    }
    
    return $current_user['role'] === 'admin' || $current_user['id'] === $resource_user_id;
}

/**
 * Logout user
 */
function logout() {
    // Clear all session variables
    $_SESSION = array();
    
    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy the session
    session_destroy();
    
    // Redirect to login page
    header('Location: ' . getBaseUrl() . '/auth/login.php');
    exit;
}

/**
 * Get base URL for redirects
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    
    // Remove /social from path if present
    $path = str_replace('/social', '', $path);
    
    return $protocol . '://' . $host . $path;
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user has permission for specific action
 */
function hasPermission($action, $resource = null) {
    $current_user = getCurrentUser();
    if (!$current_user) {
        return false;
    }
    
    // Admin has all permissions
    if ($current_user['role'] === 'admin') {
        return true;
    }
    
    // Define role-based permissions
    $permissions = [
        'brewery' => [
            'create_event',
            'manage_own_events',
            'create_trade',
            'manage_own_trades',
            'join_club',
            'create_club',
            'join_challenge'
        ],
        'user' => [
            'create_event',
            'manage_own_events',
            'create_trade',
            'manage_own_trades',
            'join_club',
            'create_club',
            'join_challenge'
        ]
    ];
    
    $user_permissions = $permissions[$current_user['role']] ?? [];
    
    // Check if user has the permission
    if (!in_array($action, $user_permissions)) {
        return false;
    }
    
    // For "own" permissions, check ownership
    if (strpos($action, 'own') !== false && $resource) {
        return isOwner($resource);
    }
    
    return true;
}

/**
 * Require specific permission
 */
function requirePermission($action, $resource = null) {
    if (!hasPermission($action, $resource)) {
        http_response_code(403);
        die('Access denied. You do not have permission to perform this action.');
    }
}

/**
 * Get user's social statistics
 */
function getUserSocialStats($user_id = null) {
    if (!$user_id) {
        $current_user = getCurrentUser();
        if (!$current_user) {
            return null;
        }
        $user_id = $current_user['id'];
    }
    
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            SELECT 
                us.*,
                COUNT(DISTINCT f1.id) as following_count,
                COUNT(DISTINCT f2.id) as followers_count
            FROM user_statistics us
            LEFT JOIN user_follows f1 ON us.user_id = f1.follower_id AND f1.status = 'accepted'
            LEFT JOIN user_follows f2 ON us.user_id = f2.following_id AND f2.status = 'accepted'
            WHERE us.user_id = ?
            GROUP BY us.user_id
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Error getting user social stats: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if user is following another user
 */
function isFollowing($follower_id, $following_id) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("
            SELECT id FROM user_follows 
            WHERE follower_id = ? AND following_id = ? AND status = 'accepted'
        ");
        $stmt->execute([$follower_id, $following_id]);
        return $stmt->fetch() !== false;
        
    } catch (Exception $e) {
        error_log("Error checking follow status: " . $e->getMessage());
        return false;
    }
}

/**
 * Rate limiting for API calls
 */
function checkRateLimit($action, $limit = 60, $window = 3600) {
    $current_user = getCurrentUser();
    if (!$current_user) {
        return false;
    }
    
    $key = "rate_limit_{$action}_{$current_user['id']}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $window];
    }
    
    $rate_data = $_SESSION[$key];
    
    // Reset if window has passed
    if (time() > $rate_data['reset_time']) {
        $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $window];
        $rate_data = $_SESSION[$key];
    }
    
    // Check if limit exceeded
    if ($rate_data['count'] >= $limit) {
        return false;
    }
    
    // Increment counter
    $_SESSION[$key]['count']++;
    
    return true;
}
?>
