<?php
/**
 * Digital Board Analytics Service
 * Phase 7 - Advanced Features
 * 
 * Comprehensive analytics and reporting for digital board system
 */

class DigitalBoardAnalytics {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get comprehensive board analytics
     */
    public function getBoardAnalytics($boardId, $timeframe = '30_days') {
        return [
            'overview' => $this->getBoardOverview($boardId, $timeframe),
            'engagement' => $this->getEngagementMetrics($boardId, $timeframe),
            'content_performance' => $this->getContentPerformance($boardId, $timeframe),
            'display_metrics' => $this->getDisplayMetrics($boardId, $timeframe),
            'user_interactions' => $this->getUserInteractions($boardId, $timeframe),
            'trends' => $this->getTrendAnalysis($boardId, $timeframe)
        ];
    }
    
    /**
     * Get board overview statistics
     */
    public function getBoardOverview($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Basic board stats
            $stmt = $this->conn->prepare("
                SELECT 
                    b.name,
                    b.display_mode,
                    b.theme,
                    b.is_active,
                    b.created_at,
                    COUNT(DISTINCT bv.id) as total_views,
                    COUNT(DISTINCT DATE(bv.viewed_at)) as active_days,
                    AVG(bv.view_duration) as avg_view_duration,
                    SUM(bv.view_duration) as total_view_time
                FROM digital_boards b
                LEFT JOIN board_views bv ON b.id = bv.board_id 
                    AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                WHERE b.id = ?
                GROUP BY b.id
            ");
            $stmt->execute([$boardId]);
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate additional metrics
            $overview['avg_daily_views'] = $overview['active_days'] > 0 ? 
                round($overview['total_views'] / $overview['active_days'], 2) : 0;
            $overview['total_view_hours'] = round($overview['total_view_time'] / 3600, 2);
            
            return $overview;
            
        } catch (Exception $e) {
            error_log("Board overview error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get engagement metrics
     */
    public function getEngagementMetrics($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Engagement statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(DISTINCT bi.session_id) as unique_sessions,
                    COUNT(bi.id) as total_interactions,
                    COUNT(DISTINCT bi.interaction_type) as interaction_types,
                    AVG(bi.interaction_duration) as avg_interaction_time,
                    COUNT(CASE WHEN bi.interaction_type = 'tap' THEN 1 END) as tap_interactions,
                    COUNT(CASE WHEN bi.interaction_type = 'swipe' THEN 1 END) as swipe_interactions,
                    COUNT(CASE WHEN bi.interaction_type = 'long_press' THEN 1 END) as long_press_interactions,
                    COUNT(CASE WHEN bi.interaction_type = 'qr_scan' THEN 1 END) as qr_scans
                FROM board_interactions bi
                WHERE bi.board_id = ? 
                AND bi.created_at >= DATE_SUB(NOW(), INTERVAL $interval)
            ");
            $stmt->execute([$boardId]);
            $engagement = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate engagement rate
            $totalViews = $this->getTotalViews($boardId, $timeframe);
            $engagement['engagement_rate'] = $totalViews > 0 ? 
                round(($engagement['total_interactions'] / $totalViews) * 100, 2) : 0;
            
            return $engagement;
            
        } catch (Exception $e) {
            error_log("Engagement metrics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get content performance metrics
     */
    public function getContentPerformance($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Slide performance
            $stmt = $this->conn->prepare("
                SELECT 
                    s.id,
                    s.title,
                    s.type,
                    COUNT(sv.id) as views,
                    AVG(sv.view_duration) as avg_duration,
                    COUNT(CASE WHEN sv.completed = 1 THEN 1 END) as completed_views,
                    COUNT(DISTINCT sv.session_id) as unique_viewers
                FROM slideshow_slides s
                LEFT JOIN slide_views sv ON s.id = sv.slide_id 
                    AND sv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                WHERE s.slideshow_id IN (
                    SELECT current_slideshow_id FROM digital_boards WHERE id = ?
                )
                GROUP BY s.id
                ORDER BY views DESC
            ");
            $stmt->execute([$boardId]);
            $slidePerformance = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Beer menu performance
            $stmt = $this->conn->prepare("
                SELECT 
                    bm.id,
                    bm.name,
                    bm.style,
                    COUNT(bmi.id) as interactions,
                    COUNT(CASE WHEN bmi.interaction_type = 'view_details' THEN 1 END) as detail_views,
                    COUNT(CASE WHEN bmi.interaction_type = 'qr_scan' THEN 1 END) as qr_scans,
                    AVG(bmi.interaction_duration) as avg_interaction_time
                FROM beer_menu bm
                LEFT JOIN beer_menu_interactions bmi ON bm.id = bmi.beer_id 
                    AND bmi.created_at >= DATE_SUB(NOW(), INTERVAL $interval)
                WHERE bm.brewery_id = (
                    SELECT brewery_id FROM digital_boards WHERE id = ?
                )
                GROUP BY bm.id
                ORDER BY interactions DESC
                LIMIT 20
            ");
            $stmt->execute([$boardId]);
            $beerPerformance = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'slides' => $slidePerformance,
                'beers' => $beerPerformance
            ];
            
        } catch (Exception $e) {
            error_log("Content performance error: " . $e->getMessage());
            return ['slides' => [], 'beers' => []];
        }
    }
    
    /**
     * Get display metrics
     */
    public function getDisplayMetrics($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Display statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE(bv.viewed_at) as date,
                    HOUR(bv.viewed_at) as hour,
                    COUNT(*) as views,
                    AVG(bv.view_duration) as avg_duration,
                    COUNT(DISTINCT bv.session_id) as unique_sessions
                FROM board_views bv
                WHERE bv.board_id = ? 
                AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                GROUP BY DATE(bv.viewed_at), HOUR(bv.viewed_at)
                ORDER BY date, hour
            ");
            $stmt->execute([$boardId]);
            $hourlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Peak hours analysis
            $stmt = $this->conn->prepare("
                SELECT 
                    HOUR(bv.viewed_at) as peak_hour,
                    COUNT(*) as views
                FROM board_views bv
                WHERE bv.board_id = ? 
                AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                GROUP BY HOUR(bv.viewed_at)
                ORDER BY views DESC
                LIMIT 5
            ");
            $stmt->execute([$boardId]);
            $peakHours = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Device and browser stats
            $stmt = $this->conn->prepare("
                SELECT 
                    bv.device_type,
                    bv.browser,
                    bv.screen_resolution,
                    COUNT(*) as views,
                    AVG(bv.view_duration) as avg_duration
                FROM board_views bv
                WHERE bv.board_id = ? 
                AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                GROUP BY bv.device_type, bv.browser, bv.screen_resolution
                ORDER BY views DESC
            ");
            $stmt->execute([$boardId]);
            $deviceStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'hourly_data' => $hourlyData,
                'peak_hours' => $peakHours,
                'device_stats' => $deviceStats
            ];
            
        } catch (Exception $e) {
            error_log("Display metrics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user interaction patterns
     */
    public function getUserInteractions($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Interaction patterns
            $stmt = $this->conn->prepare("
                SELECT 
                    bi.interaction_type,
                    COUNT(*) as count,
                    AVG(bi.interaction_duration) as avg_duration,
                    COUNT(DISTINCT bi.session_id) as unique_users
                FROM board_interactions bi
                WHERE bi.board_id = ? 
                AND bi.created_at >= DATE_SUB(NOW(), INTERVAL $interval)
                GROUP BY bi.interaction_type
                ORDER BY count DESC
            ");
            $stmt->execute([$boardId]);
            $interactionTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Interaction heatmap data
            $stmt = $this->conn->prepare("
                SELECT 
                    bi.element_id,
                    bi.x_coordinate,
                    bi.y_coordinate,
                    COUNT(*) as interaction_count
                FROM board_interactions bi
                WHERE bi.board_id = ? 
                AND bi.created_at >= DATE_SUB(NOW(), INTERVAL $interval)
                AND bi.x_coordinate IS NOT NULL
                GROUP BY bi.element_id, bi.x_coordinate, bi.y_coordinate
                ORDER BY interaction_count DESC
            ");
            $stmt->execute([$boardId]);
            $heatmapData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'interaction_types' => $interactionTypes,
                'heatmap_data' => $heatmapData
            ];
            
        } catch (Exception $e) {
            error_log("User interactions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get trend analysis
     */
    public function getTrendAnalysis($boardId, $timeframe) {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Daily trends
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE(bv.viewed_at) as date,
                    COUNT(*) as views,
                    COUNT(DISTINCT bv.session_id) as unique_sessions,
                    AVG(bv.view_duration) as avg_duration
                FROM board_views bv
                WHERE bv.board_id = ? 
                AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                GROUP BY DATE(bv.viewed_at)
                ORDER BY date
            ");
            $stmt->execute([$boardId]);
            $dailyTrends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate growth rates
            $trends = [];
            for ($i = 1; $i < count($dailyTrends); $i++) {
                $current = $dailyTrends[$i]['views'];
                $previous = $dailyTrends[$i-1]['views'];
                $growthRate = $previous > 0 ? (($current - $previous) / $previous) * 100 : 0;
                
                $trends[] = [
                    'date' => $dailyTrends[$i]['date'],
                    'views' => $current,
                    'growth_rate' => round($growthRate, 2)
                ];
            }
            
            return [
                'daily_trends' => $dailyTrends,
                'growth_analysis' => $trends
            ];
            
        } catch (Exception $e) {
            error_log("Trend analysis error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get brewery-wide analytics
     */
    public function getBreweryAnalytics($breweryId, $timeframe = '30_days') {
        try {
            $interval = $this->getIntervalFromTimeframe($timeframe);
            
            // Overall brewery stats
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(DISTINCT b.id) as total_boards,
                    COUNT(DISTINCT CASE WHEN b.is_active = 1 THEN b.id END) as active_boards,
                    COUNT(DISTINCT bv.id) as total_views,
                    COUNT(DISTINCT bv.session_id) as unique_viewers,
                    AVG(bv.view_duration) as avg_view_duration,
                    SUM(bv.view_duration) as total_view_time
                FROM digital_boards b
                LEFT JOIN board_views bv ON b.id = bv.board_id 
                    AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                WHERE b.brewery_id = ?
            ");
            $stmt->execute([$breweryId]);
            $overview = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Board performance comparison
            $stmt = $this->conn->prepare("
                SELECT 
                    b.id,
                    b.name,
                    COUNT(bv.id) as views,
                    COUNT(DISTINCT bv.session_id) as unique_viewers,
                    AVG(bv.view_duration) as avg_duration
                FROM digital_boards b
                LEFT JOIN board_views bv ON b.id = bv.board_id 
                    AND bv.viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
                WHERE b.brewery_id = ?
                GROUP BY b.id
                ORDER BY views DESC
            ");
            $stmt->execute([$breweryId]);
            $boardPerformance = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'overview' => $overview,
                'board_performance' => $boardPerformance
            ];
            
        } catch (Exception $e) {
            error_log("Brewery analytics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Track board view
     */
    public function trackBoardView($boardId, $sessionId, $deviceInfo = []) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO board_views (
                    board_id, session_id, device_type, browser, screen_resolution,
                    user_agent, ip_address, viewed_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $boardId,
                $sessionId,
                $deviceInfo['device_type'] ?? 'unknown',
                $deviceInfo['browser'] ?? 'unknown',
                $deviceInfo['screen_resolution'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
            
            return $this->conn->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Track board view error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Track user interaction
     */
    public function trackInteraction($boardId, $sessionId, $interactionData) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO board_interactions (
                    board_id, session_id, interaction_type, element_id,
                    x_coordinate, y_coordinate, interaction_duration, metadata, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $boardId,
                $sessionId,
                $interactionData['type'],
                $interactionData['element_id'] ?? null,
                $interactionData['x'] ?? null,
                $interactionData['y'] ?? null,
                $interactionData['duration'] ?? 0,
                json_encode($interactionData['metadata'] ?? [])
            ]);
            
            return $this->conn->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Track interaction error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Helper methods
     */
    private function getIntervalFromTimeframe($timeframe) {
        $intervals = [
            '7_days' => '7 DAY',
            '30_days' => '30 DAY',
            '90_days' => '90 DAY',
            '1_year' => '1 YEAR'
        ];
        
        return $intervals[$timeframe] ?? '30 DAY';
    }
    
    private function getTotalViews($boardId, $timeframe) {
        $interval = $this->getIntervalFromTimeframe($timeframe);
        
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) FROM board_views 
            WHERE board_id = ? AND viewed_at >= DATE_SUB(NOW(), INTERVAL $interval)
        ");
        $stmt->execute([$boardId]);
        
        return $stmt->fetchColumn();
    }
}
