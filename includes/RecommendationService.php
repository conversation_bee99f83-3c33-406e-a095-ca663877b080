<?php

class RecommendationService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get beer recommendations for a user
     */
    public function getBeerRecommendations($userId, $limit = 10) {
        $recommendations = [];
        
        try {
            // Get user's rating history and preferences
            $userProfile = $this->getUserProfile($userId);
            
            // Combine different recommendation strategies
            $styleBasedRecs = $this->getStyleBasedRecommendations($userId, $userProfile, $limit);
            $collaborativeRecs = $this->getCollaborativeRecommendations($userId, $limit);
            $popularRecs = $this->getPopularRecommendations($userId, $limit);
            $locationBasedRecs = $this->getLocationBasedRecommendations($userId, $limit);
            
            // Merge and score recommendations
            $allRecs = array_merge($styleBasedRecs, $collaborativeRecs, $popularRecs, $locationBasedRecs);
            $recommendations = $this->scoreAndRankRecommendations($allRecs, $userProfile, $limit);
            
        } catch (Exception $e) {
            error_log("Beer recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Get brewery recommendations for a user
     */
    public function getBreweryRecommendations($userId, $limit = 5) {
        $recommendations = [];
        
        try {
            $stmt = $this->conn->prepare("
                SELECT b.*, 
                       COUNT(DISTINCT bm.id) as beer_count,
                       AVG(br.overall_rating) as avg_rating,
                       COUNT(DISTINCT bc.id) as checkin_count,
                       (
                           SELECT COUNT(*) FROM beer_checkins bc2 
                           WHERE bc2.brewery_id = b.id AND bc2.user_id = ?
                       ) as user_checkins
                FROM breweries b
                LEFT JOIN beer_menu bm ON b.id = bm.brewery_id AND bm.available = 1
                LEFT JOIN beer_ratings br ON bm.id = br.beer_id AND br.is_public = 1
                LEFT JOIN beer_checkins bc ON b.id = bc.brewery_id
                WHERE b.id NOT IN (
                    SELECT DISTINCT brewery_id FROM beer_checkins WHERE user_id = ?
                )
                GROUP BY b.id
                HAVING beer_count > 0
                ORDER BY 
                    (avg_rating * 0.4 + (checkin_count / 100) * 0.3 + beer_count * 0.3) DESC,
                    RAND()
                LIMIT ?
            ");
            $stmt->execute([$userId, $userId, $limit]);
            $recommendations = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Brewery recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Get user's taste profile
     */
    private function getUserProfile($userId) {
        $profile = [
            'favorite_styles' => [],
            'avg_rating' => 0,
            'rating_count' => 0,
            'preferred_abv_range' => [0, 15],
            'preferred_ibu_range' => [0, 100]
        ];
        
        try {
            // Get favorite beer styles
            $stmt = $this->conn->prepare("
                SELECT bs.id, bs.name, bs.category, COUNT(*) as rating_count, AVG(br.overall_rating) as avg_rating
                FROM beer_ratings br
                JOIN beer_menu bm ON br.beer_id = bm.id
                JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE br.user_id = ? AND br.overall_rating >= 4.0
                GROUP BY bs.id
                ORDER BY rating_count DESC, avg_rating DESC
                LIMIT 5
            ");
            $stmt->execute([$userId]);
            $profile['favorite_styles'] = $stmt->fetchAll();
            
            // Get user's rating statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    AVG(overall_rating) as avg_rating,
                    COUNT(*) as rating_count,
                    AVG(bm.abv) as avg_abv,
                    AVG(bm.ibu) as avg_ibu
                FROM beer_ratings br
                JOIN beer_menu bm ON br.beer_id = bm.id
                WHERE br.user_id = ?
            ");
            $stmt->execute([$userId]);
            $stats = $stmt->fetch();
            
            if ($stats) {
                $profile['avg_rating'] = $stats['avg_rating'] ?: 0;
                $profile['rating_count'] = $stats['rating_count'] ?: 0;
                
                // Set preferred ranges based on user's history
                if ($stats['avg_abv']) {
                    $profile['preferred_abv_range'] = [
                        max(0, $stats['avg_abv'] - 2),
                        min(15, $stats['avg_abv'] + 2)
                    ];
                }
                
                if ($stats['avg_ibu']) {
                    $profile['preferred_ibu_range'] = [
                        max(0, $stats['avg_ibu'] - 20),
                        min(100, $stats['avg_ibu'] + 20)
                    ];
                }
            }
            
        } catch (Exception $e) {
            error_log("User profile error: " . $e->getMessage());
        }
        
        return $profile;
    }
    
    /**
     * Get recommendations based on user's favorite styles
     */
    private function getStyleBasedRecommendations($userId, $userProfile, $limit) {
        $recommendations = [];
        
        if (empty($userProfile['favorite_styles'])) {
            return $recommendations;
        }
        
        try {
            $styleIds = array_column($userProfile['favorite_styles'], 'id');
            $placeholders = str_repeat('?,', count($styleIds) - 1) . '?';
            
            $stmt = $this->conn->prepare("
                SELECT bm.*, b.name as brewery_name, bs.name as style_name,
                       AVG(br.overall_rating) as avg_rating,
                       COUNT(br.id) as rating_count,
                       'style_based' as recommendation_type
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                JOIN beer_styles bs ON bm.beer_style_id = bs.id
                LEFT JOIN beer_ratings br ON bm.id = br.beer_id AND br.is_public = 1
                WHERE bm.available = 1 
                AND bm.beer_style_id IN ($placeholders)
                AND bm.id NOT IN (
                    SELECT beer_id FROM beer_ratings WHERE user_id = ?
                )
                AND bm.abv BETWEEN ? AND ?
                GROUP BY bm.id
                HAVING rating_count >= 3 AND avg_rating >= 3.5
                ORDER BY avg_rating DESC, rating_count DESC
                LIMIT ?
            ");
            
            $params = array_merge(
                $styleIds, 
                [$userId], 
                $userProfile['preferred_abv_range'],
                [$limit]
            );
            
            $stmt->execute($params);
            $recommendations = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Style-based recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Get collaborative filtering recommendations
     */
    private function getCollaborativeRecommendations($userId, $limit) {
        $recommendations = [];
        
        try {
            // Find users with similar taste
            $stmt = $this->conn->prepare("
                SELECT br2.beer_id, bm.*, b.name as brewery_name, bs.name as style_name,
                       AVG(br2.overall_rating) as avg_rating,
                       COUNT(br2.id) as rating_count,
                       'collaborative' as recommendation_type
                FROM beer_ratings br1
                JOIN beer_ratings br2 ON br1.beer_id = br2.beer_id
                JOIN beer_menu bm ON br2.beer_id = bm.id
                JOIN breweries b ON bm.brewery_id = b.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE br1.user_id = ? 
                AND br2.user_id != ?
                AND br1.overall_rating >= 4.0 
                AND br2.overall_rating >= 4.0
                AND br2.beer_id NOT IN (
                    SELECT beer_id FROM beer_ratings WHERE user_id = ?
                )
                AND bm.available = 1
                GROUP BY br2.beer_id
                HAVING rating_count >= 2
                ORDER BY avg_rating DESC, rating_count DESC
                LIMIT ?
            ");
            $stmt->execute([$userId, $userId, $userId, $limit]);
            $recommendations = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Collaborative recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Get popular beer recommendations
     */
    private function getPopularRecommendations($userId, $limit) {
        $recommendations = [];
        
        try {
            $stmt = $this->conn->prepare("
                SELECT bm.*, b.name as brewery_name, bs.name as style_name,
                       bm.average_rating as avg_rating,
                       bm.total_ratings as rating_count,
                       'popular' as recommendation_type
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE bm.available = 1 
                AND bm.id NOT IN (
                    SELECT beer_id FROM beer_ratings WHERE user_id = ?
                )
                AND bm.total_ratings >= 10
                AND bm.average_rating >= 4.0
                ORDER BY (bm.average_rating * 0.7 + (bm.total_ratings / 100) * 0.3) DESC
                LIMIT ?
            ");
            $stmt->execute([$userId, $limit]);
            $recommendations = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Popular recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Get location-based recommendations
     */
    private function getLocationBasedRecommendations($userId, $limit) {
        $recommendations = [];
        
        try {
            // Get user's recent check-in locations
            $stmt = $this->conn->prepare("
                SELECT checkin_latitude, checkin_longitude
                FROM beer_checkins 
                WHERE user_id = ? AND checkin_latitude IS NOT NULL
                ORDER BY created_at DESC
                LIMIT 5
            ");
            $stmt->execute([$userId]);
            $locations = $stmt->fetchAll();
            
            if (empty($locations)) {
                return $recommendations;
            }
            
            // Find beers from nearby breweries
            $stmt = $this->conn->prepare("
                SELECT bm.*, b.name as brewery_name, bs.name as style_name,
                       AVG(br.overall_rating) as avg_rating,
                       COUNT(br.id) as rating_count,
                       'location_based' as recommendation_type
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                LEFT JOIN beer_ratings br ON bm.id = br.beer_id AND br.is_public = 1
                WHERE bm.available = 1 
                AND b.latitude IS NOT NULL 
                AND b.longitude IS NOT NULL
                AND bm.id NOT IN (
                    SELECT beer_id FROM beer_ratings WHERE user_id = ?
                )
                GROUP BY bm.id
                HAVING rating_count >= 2
                ORDER BY avg_rating DESC
                LIMIT ?
            ");
            $stmt->execute([$userId, $limit]);
            $recommendations = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Location-based recommendation error: " . $e->getMessage());
        }
        
        return $recommendations;
    }
    
    /**
     * Score and rank final recommendations
     */
    private function scoreAndRankRecommendations($recommendations, $userProfile, $limit) {
        $scored = [];
        $seen = [];
        
        foreach ($recommendations as $rec) {
            // Avoid duplicates
            if (isset($seen[$rec['id']])) {
                continue;
            }
            $seen[$rec['id']] = true;
            
            $score = 0;
            
            // Base score from rating
            $score += ($rec['avg_rating'] ?? 0) * 20;
            
            // Popularity bonus
            $score += min(($rec['rating_count'] ?? 0) / 10, 10);
            
            // Style preference bonus
            if (!empty($userProfile['favorite_styles'])) {
                foreach ($userProfile['favorite_styles'] as $style) {
                    if (isset($rec['beer_style_id']) && $rec['beer_style_id'] === $style['id']) {
                        $score += 15;
                        break;
                    }
                }
            }
            
            // Recommendation type bonus
            switch ($rec['recommendation_type']) {
                case 'style_based':
                    $score += 10;
                    break;
                case 'collaborative':
                    $score += 8;
                    break;
                case 'popular':
                    $score += 5;
                    break;
                case 'location_based':
                    $score += 6;
                    break;
            }
            
            $rec['recommendation_score'] = $score;
            $scored[] = $rec;
        }
        
        // Sort by score and return top results
        usort($scored, function($a, $b) {
            return $b['recommendation_score'] <=> $a['recommendation_score'];
        });
        
        return array_slice($scored, 0, $limit);
    }
    
    /**
     * Get trending beers
     */
    public function getTrendingBeers($limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT bm.*, b.name as brewery_name, bs.name as style_name,
                       COUNT(bc.id) as recent_checkins,
                       AVG(br.overall_rating) as avg_rating
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                LEFT JOIN beer_checkins bc ON bm.id = bc.beer_id 
                    AND bc.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                LEFT JOIN beer_ratings br ON bm.id = br.beer_id AND br.is_public = 1
                WHERE bm.available = 1
                GROUP BY bm.id
                HAVING recent_checkins >= 3
                ORDER BY recent_checkins DESC, avg_rating DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Trending beers error: " . $e->getMessage());
            return [];
        }
    }
}
?>
