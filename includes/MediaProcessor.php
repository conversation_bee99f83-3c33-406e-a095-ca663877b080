<?php
/**
 * Media Processor Class
 * Phase 4 12.0 - Media Management System
 * 
 * Handles media file processing, thumbnail generation, and optimization
 */

class MediaProcessor {
    private $uploadDir;
    private $thumbnailDir;
    private $maxImageWidth = 1920;
    private $maxImageHeight = 1080;
    private $thumbnailWidth = 300;
    private $thumbnailHeight = 200;
    private $jpegQuality = 85;
    
    public function __construct($uploadDir = '../../uploads/digital-board/') {
        $this->uploadDir = rtrim($uploadDir, '/') . '/';
        $this->thumbnailDir = $this->uploadDir . 'thumbnails/';
        
        // Create directories if they don't exist
        $this->ensureDirectoryExists($this->uploadDir);
        $this->ensureDirectoryExists($this->thumbnailDir);
    }
    
    /**
     * Process uploaded media file
     */
    public function processMedia($filePath, $contentType, $breweryId) {
        $result = [
            'success' => false,
            'thumbnail_path' => null,
            'optimized_path' => null,
            'width' => null,
            'height' => null,
            'duration' => null,
            'error' => null
        ];
        
        try {
            switch ($contentType) {
                case 'image':
                    return $this->processImage($filePath, $breweryId);
                case 'video':
                    return $this->processVideo($filePath, $breweryId);
                case 'audio':
                    return $this->processAudio($filePath, $breweryId);
                default:
                    $result['error'] = 'Unsupported content type';
                    return $result;
            }
        } catch (Exception $e) {
            $result['error'] = $e->getMessage();
            return $result;
        }
    }
    
    /**
     * Process image file
     */
    private function processImage($filePath, $breweryId) {
        $result = [
            'success' => false,
            'thumbnail_path' => null,
            'optimized_path' => null,
            'width' => null,
            'height' => null,
            'error' => null
        ];
        
        // Get image info
        $imageInfo = getimagesize($filePath);
        if (!$imageInfo) {
            $result['error'] = 'Invalid image file';
            return $result;
        }
        
        $result['width'] = $imageInfo[0];
        $result['height'] = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Create image resource
        $sourceImage = $this->createImageFromFile($filePath, $mimeType);
        if (!$sourceImage) {
            $result['error'] = 'Failed to create image resource';
            return $result;
        }
        
        try {
            // Generate thumbnail
            $thumbnailPath = $this->generateThumbnail($sourceImage, $filePath, $breweryId, $mimeType);
            if ($thumbnailPath) {
                $result['thumbnail_path'] = $thumbnailPath;
            }
            
            // Optimize original image if needed
            if ($result['width'] > $this->maxImageWidth || $result['height'] > $this->maxImageHeight) {
                $optimizedPath = $this->optimizeImage($sourceImage, $filePath, $breweryId, $mimeType);
                if ($optimizedPath) {
                    $result['optimized_path'] = $optimizedPath;
                }
            }
            
            $result['success'] = true;
            
        } finally {
            imagedestroy($sourceImage);
        }
        
        return $result;
    }
    
    /**
     * Process video file
     */
    private function processVideo($filePath, $breweryId) {
        $result = [
            'success' => false,
            'thumbnail_path' => null,
            'width' => null,
            'height' => null,
            'duration' => null,
            'error' => null
        ];
        
        // For video processing, you would typically use FFmpeg
        // This is a basic implementation that extracts basic info
        
        try {
            // Try to get video info using getID3 library if available
            if (class_exists('getID3')) {
                $getID3 = new getID3();
                $fileInfo = $getID3->analyze($filePath);
                
                if (isset($fileInfo['video'])) {
                    $result['width'] = $fileInfo['video']['resolution_x'] ?? null;
                    $result['height'] = $fileInfo['video']['resolution_y'] ?? null;
                    $result['duration'] = $fileInfo['playtime_seconds'] ?? null;
                }
            }
            
            // Generate video thumbnail (requires FFmpeg)
            $thumbnailPath = $this->generateVideoThumbnail($filePath, $breweryId);
            if ($thumbnailPath) {
                $result['thumbnail_path'] = $thumbnailPath;
            }
            
            $result['success'] = true;
            
        } catch (Exception $e) {
            $result['error'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Process audio file
     */
    private function processAudio($filePath, $breweryId) {
        $result = [
            'success' => false,
            'duration' => null,
            'error' => null
        ];
        
        try {
            // Try to get audio info using getID3 library if available
            if (class_exists('getID3')) {
                $getID3 = new getID3();
                $fileInfo = $getID3->analyze($filePath);
                
                if (isset($fileInfo['audio'])) {
                    $result['duration'] = $fileInfo['playtime_seconds'] ?? null;
                }
            }
            
            $result['success'] = true;
            
        } catch (Exception $e) {
            $result['error'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Create image resource from file
     */
    private function createImageFromFile($filePath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filePath);
            case 'image/png':
                return imagecreatefrompng($filePath);
            case 'image/gif':
                return imagecreatefromgif($filePath);
            case 'image/webp':
                return imagecreatefromwebp($filePath);
            default:
                return false;
        }
    }
    
    /**
     * Generate thumbnail for image
     */
    private function generateThumbnail($sourceImage, $originalPath, $breweryId, $mimeType) {
        $sourceWidth = imagesx($sourceImage);
        $sourceHeight = imagesy($sourceImage);
        
        // Calculate thumbnail dimensions maintaining aspect ratio
        $aspectRatio = $sourceWidth / $sourceHeight;
        
        if ($aspectRatio > ($this->thumbnailWidth / $this->thumbnailHeight)) {
            $thumbWidth = $this->thumbnailWidth;
            $thumbHeight = $this->thumbnailWidth / $aspectRatio;
        } else {
            $thumbHeight = $this->thumbnailHeight;
            $thumbWidth = $this->thumbnailHeight * $aspectRatio;
        }
        
        // Create thumbnail image
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // Handle transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $thumbnail, $sourceImage,
            0, 0, 0, 0,
            $thumbWidth, $thumbHeight,
            $sourceWidth, $sourceHeight
        );
        
        // Generate thumbnail filename
        $pathInfo = pathinfo($originalPath);
        $thumbnailFilename = $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
        $thumbnailPath = $this->thumbnailDir . $breweryId . '/' . $thumbnailFilename;
        
        // Ensure brewery thumbnail directory exists
        $this->ensureDirectoryExists(dirname($thumbnailPath));
        
        // Save thumbnail
        $success = $this->saveImage($thumbnail, $thumbnailPath, $mimeType);
        imagedestroy($thumbnail);
        
        return $success ? $thumbnailPath : null;
    }
    
    /**
     * Optimize image by resizing if too large
     */
    private function optimizeImage($sourceImage, $originalPath, $breweryId, $mimeType) {
        $sourceWidth = imagesx($sourceImage);
        $sourceHeight = imagesy($sourceImage);
        
        // Calculate optimized dimensions
        $aspectRatio = $sourceWidth / $sourceHeight;
        
        if ($aspectRatio > ($this->maxImageWidth / $this->maxImageHeight)) {
            $newWidth = $this->maxImageWidth;
            $newHeight = $this->maxImageWidth / $aspectRatio;
        } else {
            $newHeight = $this->maxImageHeight;
            $newWidth = $this->maxImageHeight * $aspectRatio;
        }
        
        // Create optimized image
        $optimizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Handle transparency
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($optimizedImage, false);
            imagesavealpha($optimizedImage, true);
            $transparent = imagecolorallocatealpha($optimizedImage, 255, 255, 255, 127);
            imagefill($optimizedImage, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $optimizedImage, $sourceImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $sourceWidth, $sourceHeight
        );
        
        // Generate optimized filename
        $pathInfo = pathinfo($originalPath);
        $optimizedFilename = $pathInfo['filename'] . '_optimized.' . $pathInfo['extension'];
        $optimizedPath = dirname($originalPath) . '/' . $optimizedFilename;
        
        // Save optimized image
        $success = $this->saveImage($optimizedImage, $optimizedPath, $mimeType);
        imagedestroy($optimizedImage);
        
        return $success ? $optimizedPath : null;
    }
    
    /**
     * Save image to file
     */
    private function saveImage($imageResource, $filePath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagejpeg($imageResource, $filePath, $this->jpegQuality);
            case 'image/png':
                return imagepng($imageResource, $filePath, 8);
            case 'image/gif':
                return imagegif($imageResource, $filePath);
            case 'image/webp':
                return imagewebp($imageResource, $filePath, $this->jpegQuality);
            default:
                return false;
        }
    }
    
    /**
     * Generate video thumbnail (requires FFmpeg)
     */
    private function generateVideoThumbnail($videoPath, $breweryId) {
        // This requires FFmpeg to be installed on the server
        // For now, return null - implement when FFmpeg is available
        return null;
    }
    
    /**
     * Ensure directory exists
     */
    private function ensureDirectoryExists($directory) {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }
    
    /**
     * Get file size in human readable format
     */
    public static function formatFileSize($bytes) {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    /**
     * Validate file type
     */
    public static function isValidFileType($mimeType) {
        $allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/webm', 'video/ogg',
            'audio/mp3', 'audio/wav', 'audio/ogg'
        ];
        
        return in_array($mimeType, $allowedTypes);
    }
}
