<?php
/**
 * Monitoring and Alerting System
 * Phase 8 - Production Deployment & Optimization
 * 
 * Comprehensive system monitoring, health checks, and alerting
 */

class MonitoringSystem {
    private $config;
    private $redis;
    private $pdo;
    private $alerts;
    
    public function __construct($pdo, $redisConnection = null, $config = []) {
        $this->pdo = $pdo;
        $this->redis = $redisConnection;
        $this->config = array_merge([
            'monitoring_enabled' => true,
            'health_check_interval' => 60,
            'alert_thresholds' => [
                'cpu_usage' => 80,
                'memory_usage' => 85,
                'disk_usage' => 90,
                'response_time' => 2000,
                'error_rate' => 5,
                'database_connections' => 80
            ],
            'notification_channels' => [
                'email' => true,
                'slack' => false,
                'discord' => false,
                'webhook' => false
            ],
            'retention_days' => 30
        ], $config);
        
        $this->alerts = [];
        $this->initializeMonitoring();
    }
    
    /**
     * Initialize monitoring system
     */
    private function initializeMonitoring() {
        if (!$this->config['monitoring_enabled']) return;
        
        // Register shutdown function for final metrics
        register_shutdown_function([$this, 'recordShutdownMetrics']);
        
        // Start health check monitoring
        $this->startHealthChecks();
    }
    
    /**
     * Comprehensive health check
     */
    public function performHealthCheck() {
        $startTime = microtime(true);
        $healthStatus = [
            'timestamp' => date('c'),
            'overall_status' => 'healthy',
            'checks' => []
        ];
        
        // Database health check
        $healthStatus['checks']['database'] = $this->checkDatabaseHealth();
        
        // Redis health check
        $healthStatus['checks']['redis'] = $this->checkRedisHealth();
        
        // File system health check
        $healthStatus['checks']['filesystem'] = $this->checkFilesystemHealth();
        
        // Memory usage check
        $healthStatus['checks']['memory'] = $this->checkMemoryUsage();
        
        // CPU usage check
        $healthStatus['checks']['cpu'] = $this->checkCPUUsage();
        
        // External API health check
        $healthStatus['checks']['external_apis'] = $this->checkExternalAPIs();
        
        // SSL certificate check
        $healthStatus['checks']['ssl'] = $this->checkSSLCertificate();
        
        // Application-specific checks
        $healthStatus['checks']['application'] = $this->checkApplicationHealth();
        
        // Determine overall status
        $healthStatus['overall_status'] = $this->determineOverallStatus($healthStatus['checks']);
        $healthStatus['response_time'] = round((microtime(true) - $startTime) * 1000, 2);
        
        // Store health check results
        $this->storeHealthCheck($healthStatus);
        
        // Check for alerts
        $this->checkAlertConditions($healthStatus);
        
        return $healthStatus;
    }
    
    /**
     * Database health check
     */
    private function checkDatabaseHealth() {
        try {
            $startTime = microtime(true);
            
            // Test connection
            $stmt = $this->pdo->query('SELECT 1');
            $connectionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // Check active connections
            $stmt = $this->pdo->query('SHOW STATUS LIKE "Threads_connected"');
            $connections = $stmt->fetch(PDO::FETCH_ASSOC);
            $activeConnections = (int)$connections['Value'];
            
            // Check database size
            $stmt = $this->pdo->query('SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS db_size FROM information_schema.tables WHERE table_schema = DATABASE()');
            $sizeResult = $stmt->fetch(PDO::FETCH_ASSOC);
            $databaseSize = (float)$sizeResult['db_size'];
            
            return [
                'status' => 'healthy',
                'connection_time' => $connectionTime,
                'active_connections' => $activeConnections,
                'database_size_mb' => $databaseSize,
                'message' => 'Database is responding normally'
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'message' => 'Database connection failed'
            ];
        }
    }
    
    /**
     * Redis health check
     */
    private function checkRedisHealth() {
        if (!$this->redis) {
            return [
                'status' => 'disabled',
                'message' => 'Redis not configured'
            ];
        }
        
        try {
            $startTime = microtime(true);
            $this->redis->ping();
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // Get Redis info
            $info = $this->redis->info();
            $memoryUsage = isset($info['used_memory_human']) ? $info['used_memory_human'] : 'unknown';
            $connectedClients = isset($info['connected_clients']) ? (int)$info['connected_clients'] : 0;
            
            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'memory_usage' => $memoryUsage,
                'connected_clients' => $connectedClients,
                'message' => 'Redis is responding normally'
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'message' => 'Redis connection failed'
            ];
        }
    }
    
    /**
     * Filesystem health check
     */
    private function checkFilesystemHealth() {
        $checks = [];
        
        // Check disk space
        $totalSpace = disk_total_space('.');
        $freeSpace = disk_free_space('.');
        $usedSpace = $totalSpace - $freeSpace;
        $usagePercentage = round(($usedSpace / $totalSpace) * 100, 2);
        
        $checks['disk_usage'] = [
            'total_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
            'free_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
            'used_percentage' => $usagePercentage,
            'status' => $usagePercentage > $this->config['alert_thresholds']['disk_usage'] ? 'warning' : 'healthy'
        ];
        
        // Check critical directories
        $criticalDirs = ['uploads/', 'logs/', 'cache/', 'backups/'];
        foreach ($criticalDirs as $dir) {
            $checks['directories'][$dir] = [
                'exists' => is_dir($dir),
                'writable' => is_writable($dir),
                'status' => (is_dir($dir) && is_writable($dir)) ? 'healthy' : 'unhealthy'
            ];
        }
        
        return [
            'status' => $this->getWorstStatus($checks),
            'details' => $checks,
            'message' => 'Filesystem check completed'
        ];
    }
    
    /**
     * Memory usage check
     */
    private function checkMemoryUsage() {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        $usagePercentage = $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0;
        
        return [
            'status' => $usagePercentage > $this->config['alert_thresholds']['memory_usage'] ? 'warning' : 'healthy',
            'current_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_mb' => round($memoryPeak / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percentage' => $usagePercentage,
            'message' => "Memory usage: {$usagePercentage}%"
        ];
    }
    
    /**
     * CPU usage check (simplified)
     */
    private function checkCPUUsage() {
        // Simple CPU usage estimation based on load average (Unix systems)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $cpuCores = $this->getCPUCores();
            $cpuUsage = $cpuCores > 0 ? round(($load[0] / $cpuCores) * 100, 2) : 0;
            
            return [
                'status' => $cpuUsage > $this->config['alert_thresholds']['cpu_usage'] ? 'warning' : 'healthy',
                'load_average' => $load,
                'cpu_cores' => $cpuCores,
                'usage_percentage' => $cpuUsage,
                'message' => "CPU usage: {$cpuUsage}%"
            ];
        }
        
        return [
            'status' => 'unknown',
            'message' => 'CPU monitoring not available on this system'
        ];
    }
    
    /**
     * External APIs health check
     */
    private function checkExternalAPIs() {
        $apis = [
            'weather' => 'https://api.openweathermap.org/data/2.5/weather?q=London&appid=test',
            'eventbrite' => 'https://www.eventbriteapi.com/v3/users/me/',
            'instagram' => 'https://graph.instagram.com/me'
        ];
        
        $results = [];
        foreach ($apis as $name => $url) {
            $startTime = microtime(true);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            curl_close($ch);
            
            $results[$name] = [
                'status' => ($httpCode >= 200 && $httpCode < 400) ? 'healthy' : 'unhealthy',
                'http_code' => $httpCode,
                'response_time' => $responseTime
            ];
        }
        
        return [
            'status' => $this->getWorstStatus($results),
            'apis' => $results,
            'message' => 'External API health check completed'
        ];
    }
    
    /**
     * SSL certificate check
     */
    private function checkSSLCertificate() {
        if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
            return [
                'status' => 'disabled',
                'message' => 'SSL not enabled'
            ];
        }
        
        $domain = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $context = stream_context_create([
            'ssl' => [
                'capture_peer_cert' => true,
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);
        
        $socket = @stream_socket_client("ssl://{$domain}:443", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        
        if (!$socket) {
            return [
                'status' => 'unhealthy',
                'error' => $errstr,
                'message' => 'SSL connection failed'
            ];
        }
        
        $cert = stream_context_get_params($socket);
        $certInfo = openssl_x509_parse($cert['options']['ssl']['peer_certificate']);
        
        $expiryDate = $certInfo['validTo_time_t'];
        $daysUntilExpiry = round(($expiryDate - time()) / 86400);
        
        fclose($socket);
        
        return [
            'status' => $daysUntilExpiry < 30 ? 'warning' : 'healthy',
            'expires_in_days' => $daysUntilExpiry,
            'expiry_date' => date('Y-m-d H:i:s', $expiryDate),
            'issuer' => $certInfo['issuer']['CN'] ?? 'Unknown',
            'message' => "SSL certificate expires in {$daysUntilExpiry} days"
        ];
    }
    
    /**
     * Application-specific health checks
     */
    private function checkApplicationHealth() {
        $checks = [];
        
        // Check critical files
        $criticalFiles = [
            'config/database.php',
            'includes/auth.php',
            '.htaccess'
        ];
        
        foreach ($criticalFiles as $file) {
            $checks['files'][$file] = file_exists($file);
        }
        
        // Check session functionality
        $checks['sessions'] = session_status() !== PHP_SESSION_DISABLED;
        
        // Check error log size
        $errorLogSize = file_exists('logs/error.log') ? filesize('logs/error.log') : 0;
        $checks['error_log_size_mb'] = round($errorLogSize / 1024 / 1024, 2);
        
        return [
            'status' => 'healthy',
            'details' => $checks,
            'message' => 'Application health check completed'
        ];
    }
    
    /**
     * Performance metrics collection
     */
    public function collectMetrics() {
        $metrics = [
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'included_files' => count(get_included_files()),
            'database_queries' => $this->getDatabaseQueryCount(),
            'cache_hits' => $this->getCacheHitCount(),
            'active_sessions' => $this->getActiveSessionCount()
        ];
        
        // Store metrics in Redis for real-time monitoring
        if ($this->redis) {
            $this->redis->lpush('performance_metrics', json_encode($metrics));
            $this->redis->ltrim('performance_metrics', 0, 999); // Keep last 1000 metrics
        }
        
        return $metrics;
    }
    
    /**
     * Alert system
     */
    private function checkAlertConditions($healthStatus) {
        foreach ($healthStatus['checks'] as $checkName => $checkResult) {
            if (isset($checkResult['status']) && $checkResult['status'] === 'unhealthy') {
                $this->triggerAlert('health_check_failed', [
                    'check' => $checkName,
                    'details' => $checkResult
                ]);
            }
        }
        
        // Check response time
        if ($healthStatus['response_time'] > $this->config['alert_thresholds']['response_time']) {
            $this->triggerAlert('slow_response', [
                'response_time' => $healthStatus['response_time'],
                'threshold' => $this->config['alert_thresholds']['response_time']
            ]);
        }
    }
    
    /**
     * Trigger alert
     */
    public function triggerAlert($type, $data = []) {
        $alert = [
            'id' => uniqid('alert_'),
            'type' => $type,
            'severity' => $this->getAlertSeverity($type),
            'timestamp' => date('c'),
            'data' => $data,
            'resolved' => false
        ];
        
        // Store alert
        $this->storeAlert($alert);
        
        // Send notifications
        $this->sendAlertNotifications($alert);
        
        return $alert['id'];
    }
    
    /**
     * Send alert notifications
     */
    private function sendAlertNotifications($alert) {
        if ($this->config['notification_channels']['email']) {
            $this->sendEmailAlert($alert);
        }
        
        if ($this->config['notification_channels']['slack']) {
            $this->sendSlackAlert($alert);
        }
        
        if ($this->config['notification_channels']['discord']) {
            $this->sendDiscordAlert($alert);
        }
        
        if ($this->config['notification_channels']['webhook']) {
            $this->sendWebhookAlert($alert);
        }
    }
    
    /**
     * Helper methods
     */
    private function determineOverallStatus($checks) {
        foreach ($checks as $check) {
            if (isset($check['status']) && $check['status'] === 'unhealthy') {
                return 'unhealthy';
            }
        }
        
        foreach ($checks as $check) {
            if (isset($check['status']) && $check['status'] === 'warning') {
                return 'warning';
            }
        }
        
        return 'healthy';
    }
    
    private function getWorstStatus($items) {
        $statuses = array_column($items, 'status');
        
        if (in_array('unhealthy', $statuses)) return 'unhealthy';
        if (in_array('warning', $statuses)) return 'warning';
        if (in_array('healthy', $statuses)) return 'healthy';
        
        return 'unknown';
    }
    
    private function parseMemoryLimit($limit) {
        if ($limit === '-1') return PHP_INT_MAX;
        
        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);
        
        switch ($unit) {
            case 'g': return $value * 1024 * 1024 * 1024;
            case 'm': return $value * 1024 * 1024;
            case 'k': return $value * 1024;
            default: return (int)$limit;
        }
    }
    
    private function getCPUCores() {
        if (is_file('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            preg_match_all('/^processor/m', $cpuinfo, $matches);
            return count($matches[0]);
        }
        
        return 1; // Default fallback
    }
    
    private function startHealthChecks() {
        // This would typically be handled by a cron job or background process
        // For demonstration, we'll just record that monitoring is active
        if ($this->redis) {
            $this->redis->set('monitoring_active', time());
        }
    }
    
    private function storeHealthCheck($healthStatus) {
        if ($this->redis) {
            $this->redis->lpush('health_checks', json_encode($healthStatus));
            $this->redis->ltrim('health_checks', 0, 99); // Keep last 100 checks
        }
    }
    
    private function storeAlert($alert) {
        if ($this->redis) {
            $this->redis->lpush('alerts', json_encode($alert));
        }
    }
    
    private function getAlertSeverity($type) {
        $severities = [
            'health_check_failed' => 'high',
            'slow_response' => 'medium',
            'high_memory_usage' => 'medium',
            'disk_space_low' => 'high',
            'ssl_expiring' => 'medium'
        ];
        
        return $severities[$type] ?? 'low';
    }
    
    private function getDatabaseQueryCount() {
        // This would be implemented by your database class
        return 0;
    }
    
    private function getCacheHitCount() {
        // This would be implemented by your cache class
        return 0;
    }
    
    private function getActiveSessionCount() {
        // Count active sessions
        return session_status() === PHP_SESSION_ACTIVE ? 1 : 0;
    }
    
    private function sendEmailAlert($alert) {
        // Email alert implementation
    }
    
    private function sendSlackAlert($alert) {
        // Slack alert implementation
    }
    
    private function sendDiscordAlert($alert) {
        // Discord alert implementation
    }
    
    private function sendWebhookAlert($alert) {
        // Webhook alert implementation
    }
    
    public function recordShutdownMetrics() {
        $this->collectMetrics();
    }
}
