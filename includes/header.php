<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo $pageTitle ?? APP_NAME; ?></title>

    <!-- PWA Meta Tags (Phase 9) -->
    <meta name="theme-color" content="#f8b500">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Beersty">
    <meta name="msapplication-TileColor" content="#f8b500">
    <meta name="description" content="Discover, rate, and share your beer journey with the Beersty community">

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/icons/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/beersty-layouts.css">

    <!-- Mobile & PWA CSS (Phase 9) -->
    <link rel="stylesheet" href="/assets/css/mobile.css">
    <link rel="stylesheet" href="/assets/css/pwa.css">

    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Dark Mode JavaScript (Load Early) -->
    <script src="/assets/js/dark-mode.js"></script>
</head>
<body<?php echo isset($bodyClass) ? ' class="' . htmlspecialchars($bodyClass) . '"' : ''; ?>>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?php echo url('index.php'); ?>">
                <img src="/assets/images/beersty-logo2.png" alt="Beersty Logo" height="32">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo url('places/search.php'); ?>">
                            <i class="fas fa-map-marker-alt me-1"></i>Places
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-compass me-1"></i>Discover
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/beers/discover.php">
                                <i class="fas fa-beer me-2"></i>Browse Beers
                            </a></li>
                            <li><a class="dropdown-item" href="/discover/recommendations.php">
                                <i class="fas fa-magic me-2"></i>Recommendations
                            </a></li>
                            <li><a class="dropdown-item" href="/location/brewery-map.php">
                                <i class="fas fa-map-marked-alt me-2"></i>Brewery Map
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/search/">
                                <i class="fas fa-search me-2"></i>Advanced Search
                            </a></li>
                        </ul>
                    </li>
                    <?php if (isLoggedIn() && in_array(getCurrentUser()['role'], ['beer_enthusiast', 'beer_expert', 'customer'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users me-1"></i>Social
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo url('social/feed.php'); ?>">
                                    <i class="fas fa-stream me-2"></i>Activity Feed
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo url('social/checkin.php'); ?>">
                                    <i class="fas fa-map-marker-alt me-2"></i>Check In
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo url('social/discover-users.php'); ?>">
                                    <i class="fas fa-user-friends me-2"></i>Find Friends
                                </a></li>
                            </ul>
                        </li>
                    <?php endif; ?>

                    <?php if (isLoggedIn() && getCurrentUser()['role'] === 'brewery'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('brewery/digital-board.php'); ?>">
                                <i class="fas fa-tv me-1"></i>Digital Board
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <!-- Right Side Navigation -->
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <?php $user = getCurrentUser(); ?>
                        <?php if (!$user): ?>
                            <?php
                            // If user data is incomplete, logout and redirect
                            session_destroy();
                            header('Location: ' . url('login/'));
                            exit;
                            ?>
                        <?php endif; ?>

                        <!-- Notifications Bell -->
                        <li class="nav-item dropdown me-2">
                            <a class="nav-link position-relative p-2" href="#" role="button" data-bs-toggle="dropdown" id="notificationsDropdown" title="Notifications">
                                <i class="fas fa-bell fs-5"></i>
                                <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none; font-size: 0.7rem;">
                                    0
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center py-3">
                                    <h6 class="mb-0">Notifications</h6>
                                    <a href="/user/notifications.php" class="btn btn-sm btn-outline-primary">View All</a>
                                </li>
                                <li><hr class="dropdown-divider m-0"></li>
                                <div id="notification-dropdown-list">
                                    <li class="text-center py-4">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </li>
                                </div>
                            </ul>
                        </li>

                        <!-- User Profile Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" id="userDropdown">
                                <div class="user-avatar me-2">
                                    <?php if (!empty($user['avatar'])): ?>
                                        <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="Profile" class="rounded-circle" width="32" height="32">
                                    <?php else: ?>
                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <span class="d-none d-md-inline"><?php echo htmlspecialchars($user['email'] ?? 'User'); ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow" style="min-width: 280px;">
                                <!-- User Info Header -->
                                <li class="dropdown-header py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3">
                                            <?php if (!empty($user['avatar'])): ?>
                                                <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="Profile" class="rounded-circle" width="40" height="40">
                                            <?php else: ?>
                                                <div class="avatar-placeholder-large rounded-circle d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['email'] ?? 'User'); ?></div>
                                            <small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?></small>
                                        </div>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider m-0"></li>

                                <!-- Profile & Account -->
                                <?php if (in_array($user['role'], ['beer_enthusiast', 'beer_expert', 'customer'])): ?>
                                    <li><a class="dropdown-item py-2" href="/user/profile.php">
                                        <i class="fas fa-user me-3 text-primary"></i>My Profile
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="/user/preferences.php">
                                        <i class="fas fa-heart me-3 text-danger"></i>Beer Preferences
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="/user/lists.php">
                                        <i class="fas fa-list me-3 text-info"></i>My Beer Lists
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="/user/photos.php">
                                        <i class="fas fa-images me-3 text-warning"></i>My Photos
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>

                                    <!-- Messages -->
                                    <li><a class="dropdown-item py-2 position-relative" href="/user/messages.php">
                                        <i class="fas fa-envelope me-3 text-success"></i>Messages
                                        <span id="message-badge-dropdown" class="position-absolute top-50 end-0 translate-middle-y badge rounded-pill bg-success me-3" style="display: none; font-size: 0.7rem;">
                                            0
                                        </span>
                                    </a></li>

                                    <!-- Statistics & Achievements -->
                                    <li><a class="dropdown-item py-2" href="/user/statistics.php">
                                        <i class="fas fa-chart-bar me-3 text-info"></i>My Statistics
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="/user/badges.php">
                                        <i class="fas fa-trophy me-3 text-warning"></i>My Badges
                                    </a></li>
                                <?php endif; ?>

                                <!-- Admin Section -->
                                <?php if ($user['role'] === 'admin'): ?>
                                    <li><h6 class="dropdown-header">Administration</h6></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('admin/dashboard.php'); ?>">
                                        <i class="fas fa-tachometer-alt me-3 text-primary"></i>Admin Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('admin/user-management.php'); ?>">
                                        <i class="fas fa-users me-3 text-info"></i>User Management
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('admin/breweries.php'); ?>">
                                        <i class="fas fa-map-marker-alt me-3 text-warning"></i>Manage Places
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('admin/menu-management.php'); ?>">
                                        <i class="fas fa-utensils me-3 text-success"></i>Menu Management
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>

                                <!-- Brewery Section -->
                                <?php if ($user['role'] === 'brewery'): ?>
                                    <li><h6 class="dropdown-header">My Brewery</h6></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('brewery/profile.php'); ?>">
                                        <i class="fas fa-edit me-3 text-primary"></i>Edit Profile
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('brewery/menu.php'); ?>">
                                        <i class="fas fa-utensils me-3 text-success"></i>Manage Menu
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="<?php echo url('brewery/digital-board.php'); ?>">
                                        <i class="fas fa-tv me-3 text-info"></i>Digital Board
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>

                                <!-- Theme Toggle -->
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">Theme</h6></li>
                                <li>
                                    <div class="px-3 py-2">
                                        <div class="btn-group w-100" role="group">
                                            <button type="button" class="btn btn-outline-secondary btn-sm active" id="theme-toggle-light">
                                                <i class="fas fa-sun me-2"></i>Light
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="theme-toggle-dark">
                                                <i class="fas fa-moon me-2"></i>Dark
                                            </button>
                                        </div>
                                    </div>
                                </li>

                                <!-- Logout -->
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item py-2 text-danger" href="/account/logout/">
                                    <i class="fas fa-sign-out-alt me-3"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Guest Navigation -->
                        <li class="nav-item">
                            <a class="nav-link" href="/login/">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary text-white ms-2 px-3" href="/register/">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">

    <!-- Navigation JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize notification and message badges
        updateNotificationBadge();
        updateMessageBadge();

        // Initialize theme toggle buttons
        initializeThemeToggle();

        // Update badges every 30 seconds
        setInterval(function() {
            updateNotificationBadge();
            updateMessageBadge();
        }, 30000);
    });

    function initializeThemeToggle() {
        // Wait for dark mode manager to be ready
        setTimeout(() => {
            if (window.darkModeManager) {
                window.darkModeManager.updateDropdownButtons();
            }
        }, 100);
    }

    function updateNotificationBadge() {
        <?php if (isLoggedIn()): ?>
        fetch('/api/notifications/count.php')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('notification-badge');
                if (data.count > 0) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                    badge.style.display = 'block';
                } else {
                    badge.style.display = 'none';
                }
            })
            .catch(error => console.log('Notification count error:', error));
        <?php endif; ?>
    }

    function updateMessageBadge() {
        <?php if (isLoggedIn()): ?>
        fetch('/api/messages/count.php')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('message-badge-dropdown');
                if (data.count > 0) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                    badge.style.display = 'block';
                } else {
                    badge.style.display = 'none';
                }
            })
            .catch(error => console.log('Message count error:', error));
        <?php endif; ?>
    }

    // Load notifications when dropdown is opened
    document.getElementById('notificationsDropdown')?.addEventListener('click', function() {
        loadNotifications();
    });

    function loadNotifications() {
        const notificationList = document.getElementById('notification-dropdown-list');

        fetch('/api/notifications/recent.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.notifications.length > 0) {
                    notificationList.innerHTML = '';
                    data.notifications.forEach(notification => {
                        const notificationItem = document.createElement('li');
                        notificationItem.innerHTML = `
                            <a class="dropdown-item py-2 ${notification.is_read ? '' : 'fw-bold'}" href="${notification.link || '#'}">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-${notification.icon || 'bell'} me-3 mt-1 text-primary"></i>
                                    <div class="flex-grow-1">
                                        <div class="notification-title">${notification.title}</div>
                                        <small class="text-muted">${notification.time_ago}</small>
                                    </div>
                                    ${!notification.is_read ? '<div class="notification-dot bg-primary rounded-circle ms-2" style="width: 8px; height: 8px;"></div>' : ''}
                                </div>
                            </a>
                        `;
                        notificationList.appendChild(notificationItem);
                    });
                } else {
                    notificationList.innerHTML = `
                        <li class="text-center py-4">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <div class="text-muted">No notifications</div>
                        </li>
                    `;
                }
            })
            .catch(error => {
                console.log('Notification load error:', error);
                notificationList.innerHTML = `
                    <li class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <div class="text-muted">Failed to load notifications</div>
                    </li>
                `;
            });
    }
    </script>
        <!-- SEO Breadcrumbs -->
        <?php
        if (file_exists('includes/breadcrumbs.php')) {
            require_once 'includes/breadcrumbs.php';
            $breadcrumbHtml = renderBreadcrumbs(true);
            if (!empty($breadcrumbHtml)):
        ?>
            <div class="container mt-3">
                <?php echo $breadcrumbHtml; ?>
            </div>
        <?php
            endif;
        }
        ?>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="container mt-3">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="container mt-3">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        <?php endif; ?>
