<?php
/**
 * SEO-Friendly Breadcrumb Component
 * Generates structured breadcrumbs based on URL path
 */

class BreadcrumbGenerator {
    private $breadcrumbs = [];
    private $currentPath;
    
    public function __construct() {
        $this->currentPath = $this->getCurrentPath();
        $this->generateBreadcrumbs();
    }
    
    private function getCurrentPath() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove base path if running in subdirectory
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/') {
            $path = substr($path, strlen($basePath));
        }
        
        return trim($path, '/');
    }
    
    private function generateBreadcrumbs() {
        // Always start with home
        $this->addBreadcrumb('Home', '/');
        
        if (empty($this->currentPath)) {
            return; // We're on homepage
        }
        
        $pathParts = explode('/', $this->currentPath);
        $currentUrl = '';
        
        foreach ($pathParts as $index => $part) {
            if (empty($part)) continue;
            
            $currentUrl .= '/' . $part;
            $isLast = ($index === count($pathParts) - 1);
            
            // Generate breadcrumb based on path structure
            $breadcrumbData = $this->getBreadcrumbData($pathParts, $index);
            
            if ($breadcrumbData) {
                $this->addBreadcrumb(
                    $breadcrumbData['title'], 
                    $isLast ? null : $currentUrl . '/',
                    $breadcrumbData['schema_type'] ?? null
                );
            }
        }
    }
    
    private function getBreadcrumbData($pathParts, $index) {
        $part = $pathParts[$index];
        $section = $pathParts[0] ?? '';
        
        // Define breadcrumb mappings
        $breadcrumbMap = [
            // Account section
            'account' => [
                'title' => 'Account',
                'schema_type' => 'WebPage',
                'children' => [
                    'login' => ['title' => 'Login', 'schema_type' => 'WebPage'],
                    'register' => ['title' => 'Sign Up', 'schema_type' => 'WebPage'],
                    'logout' => ['title' => 'Logout', 'schema_type' => 'WebPage'],
                    'forgot-password' => ['title' => 'Forgot Password', 'schema_type' => 'WebPage'],
                ]
            ],
            
            // Profile section
            'profile' => [
                'title' => 'Profile',
                'schema_type' => 'ProfilePage',
                'children' => [
                    'edit' => ['title' => 'Edit Profile', 'schema_type' => 'WebPage'],
                    'preferences' => ['title' => 'Preferences', 'schema_type' => 'WebPage'],
                    'notifications' => ['title' => 'Notifications', 'schema_type' => 'WebPage'],
                    'messages' => ['title' => 'Messages', 'schema_type' => 'WebPage'],
                    'photos' => ['title' => 'Photos', 'schema_type' => 'ImageGallery'],
                    'lists' => ['title' => 'Beer Lists', 'schema_type' => 'CollectionPage'],
                    'badges' => ['title' => 'Badges', 'schema_type' => 'CollectionPage'],
                    'statistics' => ['title' => 'Statistics', 'schema_type' => 'WebPage'],
                ]
            ],
            
            // Beer section
            'beers' => [
                'title' => 'Beers',
                'schema_type' => 'CollectionPage',
                'children' => [
                    'discover' => ['title' => 'Discover Beers', 'schema_type' => 'SearchResultsPage'],
                    'trending' => ['title' => 'Trending Beers', 'schema_type' => 'CollectionPage'],
                    'styles' => ['title' => 'Beer Styles', 'schema_type' => 'CollectionPage'],
                    'rate' => ['title' => 'Rate Beer', 'schema_type' => 'WebPage'],
                    'reviews' => ['title' => 'Beer Reviews', 'schema_type' => 'CollectionPage'],
                    'recommendations' => ['title' => 'Recommendations', 'schema_type' => 'CollectionPage'],
                ]
            ],
            
            // Brewery section
            'breweries' => [
                'title' => 'Breweries',
                'schema_type' => 'CollectionPage',
                'children' => [
                    'discover' => ['title' => 'Discover Breweries', 'schema_type' => 'SearchResultsPage'],
                    'map' => ['title' => 'Brewery Map', 'schema_type' => 'WebPage'],
                    'manage' => ['title' => 'Manage Brewery', 'schema_type' => 'WebPage'],
                    'claim' => ['title' => 'Claim Brewery', 'schema_type' => 'WebPage'],
                ]
            ],
            
            // Social section
            'social' => [
                'title' => 'Social',
                'schema_type' => 'WebPage',
                'children' => [
                    'feed' => ['title' => 'Activity Feed', 'schema_type' => 'CollectionPage'],
                    'checkin' => ['title' => 'Check In', 'schema_type' => 'WebPage'],
                    'discover-users' => ['title' => 'Discover Users', 'schema_type' => 'SearchResultsPage'],
                    'friends' => ['title' => 'Friends', 'schema_type' => 'CollectionPage'],
                ]
            ],
            
            // Search section
            'search' => [
                'title' => 'Search',
                'schema_type' => 'SearchResultsPage',
                'children' => [
                    'global' => ['title' => 'Global Search', 'schema_type' => 'SearchResultsPage'],
                    'beers' => ['title' => 'Search Beers', 'schema_type' => 'SearchResultsPage'],
                    'breweries' => ['title' => 'Search Breweries', 'schema_type' => 'SearchResultsPage'],
                    'users' => ['title' => 'Search Users', 'schema_type' => 'SearchResultsPage'],
                ]
            ],
            
            // Business section
            'business' => [
                'title' => 'Business',
                'schema_type' => 'WebPage',
                'children' => [
                    'dashboard' => ['title' => 'Dashboard', 'schema_type' => 'WebPage'],
                    'profile' => ['title' => 'Business Profile', 'schema_type' => 'ProfilePage'],
                    'menu' => ['title' => 'Menu Management', 'schema_type' => 'WebPage'],
                    'analytics' => ['title' => 'Analytics', 'schema_type' => 'WebPage'],
                ]
            ],
            
            // Admin section
            'admin' => [
                'title' => 'Admin',
                'schema_type' => 'WebPage',
                'children' => [
                    'dashboard' => ['title' => 'Admin Dashboard', 'schema_type' => 'WebPage'],
                    'breweries' => ['title' => 'Manage Breweries', 'schema_type' => 'WebPage'],
                    'analytics' => ['title' => 'Analytics', 'schema_type' => 'WebPage'],
                    'import' => ['title' => 'Import Data', 'schema_type' => 'WebPage'],
                ]
            ],
        ];
        
        if ($index === 0) {
            // First level (section)
            return $breadcrumbMap[$part] ?? ['title' => ucfirst($part), 'schema_type' => 'WebPage'];
        } else {
            // Child level
            $parentSection = $breadcrumbMap[$section] ?? null;
            if ($parentSection && isset($parentSection['children'][$part])) {
                return $parentSection['children'][$part];
            }
            
            // Dynamic content (e.g., brewery names, usernames)
            if (preg_match('/^[a-zA-Z0-9\-_]+$/', $part)) {
                return [
                    'title' => $this->formatDynamicTitle($part, $section),
                    'schema_type' => $this->getDynamicSchemaType($section)
                ];
            }
        }
        
        return ['title' => ucfirst(str_replace('-', ' ', $part)), 'schema_type' => 'WebPage'];
    }
    
    private function formatDynamicTitle($slug, $section) {
        // Convert slug to readable title
        $title = ucwords(str_replace(['-', '_'], ' ', $slug));
        
        // Add context based on section
        switch ($section) {
            case 'breweries':
                return $title . ' Brewery';
            case 'beers':
                return $title . ' Beer';
            case 'profile':
                return '@' . $slug;
            default:
                return $title;
        }
    }
    
    private function getDynamicSchemaType($section) {
        switch ($section) {
            case 'breweries':
                return 'LocalBusiness';
            case 'beers':
                return 'Product';
            case 'profile':
                return 'ProfilePage';
            default:
                return 'WebPage';
        }
    }
    
    private function addBreadcrumb($title, $url = null, $schemaType = null) {
        $this->breadcrumbs[] = [
            'title' => $title,
            'url' => $url,
            'schema_type' => $schemaType ?? 'WebPage'
        ];
    }
    
    public function render($includeSchema = true) {
        if (count($this->breadcrumbs) <= 1) {
            return ''; // Don't show breadcrumbs for homepage only
        }
        
        $html = '<nav aria-label="breadcrumb" class="breadcrumb-nav mb-3">';
        
        if ($includeSchema) {
            $html .= $this->generateSchemaMarkup();
        }
        
        $html .= '<ol class="breadcrumb">';
        
        foreach ($this->breadcrumbs as $index => $crumb) {
            $isLast = ($index === count($this->breadcrumbs) - 1);
            
            if ($isLast || $crumb['url'] === null) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">';
                $html .= htmlspecialchars($crumb['title']);
                $html .= '</li>';
            } else {
                $html .= '<li class="breadcrumb-item">';
                $html .= '<a href="' . htmlspecialchars($crumb['url']) . '">';
                $html .= htmlspecialchars($crumb['title']);
                $html .= '</a>';
                $html .= '</li>';
            }
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
    
    private function generateSchemaMarkup() {
        $schemaItems = [];
        
        foreach ($this->breadcrumbs as $index => $crumb) {
            $position = $index + 1;
            $item = [
                '@type' => 'ListItem',
                'position' => $position,
                'name' => $crumb['title']
            ];
            
            if ($crumb['url']) {
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'];
                $item['item'] = $protocol . '://' . $host . $crumb['url'];
            }
            
            $schemaItems[] = $item;
        }
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $schemaItems
        ];
        
        return '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>';
    }
    
    public function getBreadcrumbs() {
        return $this->breadcrumbs;
    }
    
    public function getPageTitle() {
        if (count($this->breadcrumbs) > 1) {
            $lastCrumb = end($this->breadcrumbs);
            return $lastCrumb['title'];
        }
        return 'Home';
    }
}

// Helper function to render breadcrumbs
function renderBreadcrumbs($includeSchema = true) {
    $generator = new BreadcrumbGenerator();
    return $generator->render($includeSchema);
}

// Helper function to get page title from breadcrumbs
function getBreadcrumbPageTitle() {
    $generator = new BreadcrumbGenerator();
    return $generator->getPageTitle();
}
