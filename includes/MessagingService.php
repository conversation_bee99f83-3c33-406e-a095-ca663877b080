<?php
/**
 * MessagingService - Handles all messaging functionality
 * Phase 7: Notifications & Communication
 */

class MessagingService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Create a new conversation
     */
    public function createConversation($createdBy, $type = 'direct', $title = null, $description = null) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO conversations (created_by, type, title, description)
                VALUES (?, ?, ?, ?)
            ");
            
            $stmt->execute([$createdBy, $type, $title, $description]);
            $conversationId = $this->conn->lastInsertId();
            
            // Add creator as participant
            $this->addParticipant($conversationId, $createdBy, 'admin');
            
            return $conversationId;
            
        } catch (Exception $e) {
            error_log("MessagingService createConversation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get or create direct conversation between two users
     */
    public function getOrCreateDirectConversation($user1Id, $user2Id) {
        try {
            // Check if conversation already exists
            $stmt = $this->conn->prepare("
                SELECT c.id 
                FROM conversations c
                JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
                JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
                WHERE c.type = 'direct' 
                AND cp1.user_id = ? AND cp2.user_id = ?
                AND cp1.is_active = TRUE AND cp2.is_active = TRUE
                LIMIT 1
            ");
            
            $stmt->execute([$user1Id, $user2Id]);
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing) {
                return $existing['id'];
            }
            
            // Create new conversation
            $conversationId = $this->createConversation($user1Id, 'direct');
            
            if ($conversationId) {
                // Add second participant
                $this->addParticipant($conversationId, $user2Id, 'member');
                return $conversationId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("MessagingService getOrCreateDirectConversation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add participant to conversation
     */
    public function addParticipant($conversationId, $userId, $role = 'member') {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO conversation_participants (conversation_id, user_id, role)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE is_active = TRUE, role = VALUES(role)
            ");
            
            $result = $stmt->execute([$conversationId, $userId, $role]);
            
            if ($result) {
                // Update participant count
                $this->updateParticipantCount($conversationId);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("MessagingService addParticipant error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send a message
     */
    public function sendMessage($conversationId, $senderId, $content, $messageType = 'text', $attachments = null) {
        try {
            // Check if user is participant
            if (!$this->isParticipant($conversationId, $senderId)) {
                return false;
            }
            
            // Check user's messaging permissions
            if (!$this->canUserSendMessage($senderId)) {
                return false;
            }
            
            $stmt = $this->conn->prepare("
                INSERT INTO messages (conversation_id, sender_id, message_type, content, attachments)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $conversationId,
                $senderId,
                $messageType,
                $content,
                $attachments ? json_encode($attachments) : null
            ]);
            
            if ($result) {
                $messageId = $this->conn->lastInsertId();
                
                // Update conversation's last message time
                $this->updateConversationLastMessage($conversationId);
                
                // Create notifications for other participants
                $this->notifyParticipants($conversationId, $senderId, $messageId);
                
                return $messageId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("MessagingService sendMessage error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get conversation messages
     */
    public function getConversationMessages($conversationId, $userId, $limit = 50, $offset = 0) {
        try {
            // Check if user is participant
            if (!$this->isParticipant($conversationId, $userId)) {
                return [];
            }
            
            $stmt = $this->conn->prepare("
                SELECT m.*, 
                       p.first_name, p.last_name, p.avatar,
                       u.email
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                JOIN profiles p ON u.id = p.id
                WHERE m.conversation_id = ? AND m.is_deleted = FALSE
                ORDER BY m.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $stmt->execute([$conversationId, $limit, $offset]);
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Mark messages as read
            $this->markMessagesAsRead($conversationId, $userId);
            
            return array_reverse($messages); // Return in chronological order
            
        } catch (Exception $e) {
            error_log("MessagingService getConversationMessages error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user's conversations
     */
    public function getUserConversations($userId, $limit = 20) {
        try {
            $stmt = $this->conn->prepare("
                SELECT c.*, 
                       cp.last_read_at,
                       (SELECT COUNT(*) FROM messages m 
                        WHERE m.conversation_id = c.id 
                        AND m.created_at > COALESCE(cp.last_read_at, '1970-01-01')
                        AND m.sender_id != ?) as unread_count,
                       (SELECT m.content FROM messages m 
                        WHERE m.conversation_id = c.id 
                        AND m.is_deleted = FALSE
                        ORDER BY m.created_at DESC LIMIT 1) as last_message,
                       (SELECT GROUP_CONCAT(CONCAT(p.first_name, ' ', p.last_name) SEPARATOR ', ')
                        FROM conversation_participants cp2
                        JOIN profiles p ON cp2.user_id = p.id
                        WHERE cp2.conversation_id = c.id 
                        AND cp2.user_id != ? 
                        AND cp2.is_active = TRUE) as other_participants
                FROM conversations c
                JOIN conversation_participants cp ON c.id = cp.conversation_id
                WHERE cp.user_id = ? AND cp.is_active = TRUE AND c.is_active = TRUE
                ORDER BY c.last_message_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$userId, $userId, $userId, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("MessagingService getUserConversations error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if user is participant in conversation
     */
    public function isParticipant($conversationId, $userId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) as count 
            FROM conversation_participants 
            WHERE conversation_id = ? AND user_id = ? AND is_active = TRUE
        ");
        $stmt->execute([$conversationId, $userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
    }
    
    /**
     * Check if user can send messages (based on privacy settings)
     */
    private function canUserSendMessage($userId) {
        $stmt = $this->conn->prepare("
            SELECT allow_messages FROM profiles WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['allow_messages'] ?? true;
    }
    
    /**
     * Update conversation's last message timestamp
     */
    private function updateConversationLastMessage($conversationId) {
        $stmt = $this->conn->prepare("
            UPDATE conversations 
            SET last_message_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$conversationId]);
    }
    
    /**
     * Update participant count
     */
    private function updateParticipantCount($conversationId) {
        $stmt = $this->conn->prepare("
            UPDATE conversations 
            SET participant_count = (
                SELECT COUNT(*) FROM conversation_participants 
                WHERE conversation_id = ? AND is_active = TRUE
            )
            WHERE id = ?
        ");
        $stmt->execute([$conversationId, $conversationId]);
    }
    
    /**
     * Mark messages as read for a user
     */
    private function markMessagesAsRead($conversationId, $userId) {
        try {
            // Update last read timestamp
            $stmt = $this->conn->prepare("
                UPDATE conversation_participants 
                SET last_read_at = NOW() 
                WHERE conversation_id = ? AND user_id = ?
            ");
            $stmt->execute([$conversationId, $userId]);
            
        } catch (Exception $e) {
            error_log("MessagingService markMessagesAsRead error: " . $e->getMessage());
        }
    }
    
    /**
     * Notify other participants about new message
     */
    private function notifyParticipants($conversationId, $senderId, $messageId) {
        try {
            // Get other participants
            $stmt = $this->conn->prepare("
                SELECT cp.user_id, p.first_name, p.last_name
                FROM conversation_participants cp
                JOIN profiles p ON cp.user_id = p.id
                WHERE cp.conversation_id = ? AND cp.user_id != ? AND cp.is_active = TRUE
            ");
            $stmt->execute([$conversationId, $senderId]);
            $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get sender info
            $stmt = $this->conn->prepare("
                SELECT first_name, last_name FROM profiles WHERE id = ?
            ");
            $stmt->execute([$senderId]);
            $sender = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $senderName = $sender['first_name'] . ' ' . $sender['last_name'];
            
            // Create notifications
            require_once 'NotificationService.php';
            $notificationService = new NotificationService($this->conn);
            
            foreach ($participants as $participant) {
                $notificationService->createNotification(
                    $participant['user_id'],
                    'message_received',
                    'New Message',
                    "You have a new message from {$senderName}",
                    [
                        'conversation_id' => $conversationId,
                        'message_id' => $messageId,
                        'sender_name' => $senderName
                    ],
                    'message',
                    $messageId
                );
            }
            
        } catch (Exception $e) {
            error_log("MessagingService notifyParticipants error: " . $e->getMessage());
        }
    }
    
    /**
     * Delete message (soft delete)
     */
    public function deleteMessage($messageId, $userId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE messages 
                SET is_deleted = TRUE, deleted_at = NOW() 
                WHERE id = ? AND sender_id = ?
            ");
            return $stmt->execute([$messageId, $userId]);
            
        } catch (Exception $e) {
            error_log("MessagingService deleteMessage error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Flag message for moderation
     */
    public function flagMessage($messageId, $userId, $reason) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE messages 
                SET is_flagged = TRUE, flagged_reason = ?, flagged_by = ? 
                WHERE id = ?
            ");
            return $stmt->execute([$reason, $userId, $messageId]);
            
        } catch (Exception $e) {
            error_log("MessagingService flagMessage error: " . $e->getMessage());
            return false;
        }
    }
}
?>
