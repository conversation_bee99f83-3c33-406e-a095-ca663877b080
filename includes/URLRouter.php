<?php
/**
 * SEO-Friendly URL Router for Beersty Platform
 * Handles clean URL routing and 301 redirects
 */

class URLRouter {
    private $routes = [];
    private $redirects = [];
    private $currentRoute = null;
    private $breadcrumbs = [];
    
    public function __construct() {
        $this->initializeRoutes();
        $this->initializeRedirects();
    }
    
    /**
     * Initialize SEO-friendly routes
     */
    private function initializeRoutes() {
        // Authentication & Account Management
        $this->addRoute('/account/login/', 'auth/login.php', 'Login', ['Account']);
        $this->addRoute('/account/register/', 'auth/register.php', 'Register', ['Account']);
        $this->addRoute('/account/logout/', 'auth/logout.php', 'Logout', ['Account']);
        $this->addRoute('/account/forgot-password/', 'auth/forgot-password.php', 'Forgot Password', ['Account']);
        
        // User Profiles & Social
        $this->addRoute('/profile/edit/', 'user/profile.php', 'Edit Profile', ['Profile']);
        $this->addRoute('/profile/preferences/', 'user/preferences.php', 'Preferences', ['Profile']);
        $this->addRoute('/profile/notifications/', 'user/notifications.php', 'Notifications', ['Profile']);
        $this->addRoute('/profile/messages/', 'user/messages.php', 'Messages', ['Profile']);
        $this->addRoute('/profile/photos/', 'user/photos.php', 'Photos', ['Profile']);
        $this->addRoute('/profile/lists/', 'user/lists.php', 'Beer Lists', ['Profile']);
        $this->addRoute('/profile/badges/', 'user/badges.php', 'Badges', ['Profile']);
        $this->addRoute('/profile/statistics/', 'user/statistics.php', 'Statistics', ['Profile']);
        
        // Beer Discovery & Information
        $this->addRoute('/beers/discover/', 'beers/discover.php', 'Discover Beers', ['Beers']);
        $this->addRoute('/beers/trending/', 'beers/trending.php', 'Trending Beers', ['Beers']);
        $this->addRoute('/beers/styles/', 'beers/styles.php', 'Beer Styles', ['Beers']);
        $this->addRoute('/beers/rate/', 'beers/rate.php', 'Rate Beer', ['Beers']);
        $this->addRoute('/beers/reviews/', 'beers/reviews.php', 'Beer Reviews', ['Beers']);
        $this->addRoute('/beers/recommendations/', 'discover/recommendations.php', 'Recommendations', ['Beers']);
        
        // Brewery Discovery & Management
        $this->addRoute('/breweries/discover/', 'breweries/listing.php', 'Discover Breweries', ['Breweries']);
        $this->addRoute('/breweries/map/', 'location/brewery-map.php', 'Brewery Map', ['Breweries']);
        $this->addRoute('/breweries/claim/', 'breweries/claim.php', 'Claim Brewery', ['Breweries']);
        $this->addRoute('/breweries/manage/', 'brewery/profile.php', 'Manage Brewery', ['Breweries']);
        
        // Social Features
        $this->addRoute('/social/feed/', 'social/feed.php', 'Activity Feed', ['Social']);
        $this->addRoute('/social/checkin/', 'social/checkin.php', 'Check In', ['Social']);
        $this->addRoute('/social/discover-users/', 'social/discover-users.php', 'Discover Users', ['Social']);
        $this->addRoute('/social/friends/', 'social/friends.php', 'Friends', ['Social']);
        
        // Search & Discovery
        $this->addRoute('/search/', 'search/index.php', 'Search', []);
        $this->addRoute('/search/global/', 'search/global.php', 'Global Search', ['Search']);
        $this->addRoute('/search/beers/', 'search/beers.php', 'Search Beers', ['Search']);
        $this->addRoute('/search/breweries/', 'search/breweries.php', 'Search Breweries', ['Search']);
        $this->addRoute('/search/users/', 'search/users.php', 'Search Users', ['Search']);
        
        // Business Features
        $this->addRoute('/business/dashboard/', 'business/dashboard.php', 'Business Dashboard', ['Business']);
        $this->addRoute('/business/profile/', 'business/profile.php', 'Business Profile', ['Business']);
        $this->addRoute('/business/menu/', 'business/menu.php', 'Menu Management', ['Business']);
        $this->addRoute('/business/analytics/', 'business/analytics.php', 'Analytics', ['Business']);
        
        // Admin
        $this->addRoute('/admin/dashboard/', 'admin/dashboard.php', 'Admin Dashboard', ['Admin']);
        $this->addRoute('/admin/breweries/', 'admin/breweries.php', 'Manage Breweries', ['Admin']);
        $this->addRoute('/admin/analytics/', 'admin/analytics.php', 'Analytics', ['Admin']);
        $this->addRoute('/admin/import/', 'admin/brewery-import.php', 'Import Data', ['Admin']);
    }
    
    /**
     * Initialize 301 redirects from old URLs
     */
    private function initializeRedirects() {
        // Old auth URLs
        $this->addRedirect('/auth/login.php', '/account/login/');
        $this->addRedirect('/auth/register.php', '/account/register/');
        $this->addRedirect('/auth/logout.php', '/account/logout/');
        
        // Old user URLs
        $this->addRedirect('/user/profile.php', '/profile/edit/');
        $this->addRedirect('/user/preferences.php', '/profile/preferences/');
        $this->addRedirect('/user/notifications.php', '/profile/notifications/');
        $this->addRedirect('/user/messages.php', '/profile/messages/');
        $this->addRedirect('/user/photos.php', '/profile/photos/');
        $this->addRedirect('/user/lists.php', '/profile/lists/');
        $this->addRedirect('/user/badges.php', '/profile/badges/');
        $this->addRedirect('/user/statistics.php', '/profile/statistics/');
        
        // Old brewery URLs
        $this->addRedirect('/breweries/listing.php', '/breweries/discover/');
        $this->addRedirect('/brewery/profile.php', '/breweries/manage/');
        $this->addRedirect('/location/brewery-map.php', '/breweries/map/');
        
        // Old beer URLs
        $this->addRedirect('/beers/discover.php', '/beers/discover/');
        $this->addRedirect('/beers/rate.php', '/beers/rate/');
        $this->addRedirect('/discover/recommendations.php', '/beers/recommendations/');
        
        // Old social URLs
        $this->addRedirect('/social/feed.php', '/social/feed/');
        $this->addRedirect('/social/checkin.php', '/social/checkin/');
        $this->addRedirect('/social/discover-users.php', '/social/discover-users/');
        
        // Old search URLs
        $this->addRedirect('/search/index.php', '/search/');
        
        // Old admin URLs
        $this->addRedirect('/admin/dashboard.php', '/admin/dashboard/');
        $this->addRedirect('/admin/breweries.php', '/admin/breweries/');
        $this->addRedirect('/admin/analytics.php', '/admin/analytics/');
        $this->addRedirect('/admin/brewery-import.php', '/admin/import/');
    }
    
    /**
     * Add a route
     */
    public function addRoute($pattern, $file, $title = '', $breadcrumbs = []) {
        $this->routes[$pattern] = [
            'file' => $file,
            'title' => $title,
            'breadcrumbs' => $breadcrumbs
        ];
    }
    
    /**
     * Add a redirect
     */
    public function addRedirect($from, $to) {
        $this->redirects[$from] = $to;
    }
    
    /**
     * Route the current request
     */
    public function route() {
        $requestUri = $_SERVER['REQUEST_URI'];
        $path = parse_url($requestUri, PHP_URL_PATH);
        
        // Remove base path if running in subdirectory
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/') {
            $path = substr($path, strlen($basePath));
        }
        
        // Ensure path starts with /
        if (!str_starts_with($path, '/')) {
            $path = '/' . $path;
        }
        
        // Check for redirects first
        if (isset($this->redirects[$path])) {
            $this->redirect301($this->redirects[$path]);
            return;
        }
        
        // Check for exact route match
        if (isset($this->routes[$path])) {
            $this->currentRoute = $this->routes[$path];
            $this->loadRoute($this->routes[$path]);
            return;
        }
        
        // Check for dynamic routes (e.g., /profile/@username/)
        foreach ($this->routes as $pattern => $route) {
            if ($this->matchDynamicRoute($pattern, $path)) {
                $this->currentRoute = $route;
                $this->loadRoute($route);
                return;
            }
        }
        
        // No route found, show 404
        $this->show404();
    }
    
    /**
     * Match dynamic routes
     */
    private function matchDynamicRoute($pattern, $path) {
        // Convert pattern to regex
        $regex = preg_replace('/\{[^}]+\}/', '([^/]+)', $pattern);
        $regex = '#^' . $regex . '$#';
        
        return preg_match($regex, $path);
    }
    
    /**
     * Load a route
     */
    private function loadRoute($route) {
        $filePath = $route['file'];
        
        // Set page title and breadcrumbs
        if (!empty($route['title'])) {
            $GLOBALS['pageTitle'] = $route['title'] . ' - ' . APP_NAME;
        }
        
        $this->breadcrumbs = $route['breadcrumbs'];
        
        // Include the file
        if (file_exists($filePath)) {
            include $filePath;
        } else {
            $this->show404();
        }
    }
    
    /**
     * Perform 301 redirect
     */
    private function redirect301($url) {
        header("HTTP/1.1 301 Moved Permanently");
        header("Location: " . $url);
        exit;
    }
    
    /**
     * Show 404 page
     */
    private function show404() {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit;
    }
    
    /**
     * Get current route info
     */
    public function getCurrentRoute() {
        return $this->currentRoute;
    }
    
    /**
     * Get breadcrumbs
     */
    public function getBreadcrumbs() {
        return $this->breadcrumbs;
    }
    
    /**
     * Generate SEO-friendly URL
     */
    public function url($route, $params = []) {
        // Find route by file
        foreach ($this->routes as $pattern => $routeData) {
            if ($routeData['file'] === $route) {
                $url = $pattern;
                
                // Replace parameters
                foreach ($params as $key => $value) {
                    $url = str_replace('{' . $key . '}', $value, $url);
                }
                
                return $url;
            }
        }
        
        // Fallback to old URL generation
        return '/' . $route;
    }
    
    /**
     * Generate canonical URL
     */
    public function getCanonicalUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        return $protocol . '://' . $host . $uri;
    }
    
    /**
     * Generate breadcrumb HTML
     */
    public function renderBreadcrumbs() {
        if (empty($this->breadcrumbs)) {
            return '';
        }
        
        $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
        $html .= '<li class="breadcrumb-item"><a href="/">Home</a></li>';
        
        foreach ($this->breadcrumbs as $crumb) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($crumb) . '</li>';
        }
        
        $html .= '</ol></nav>';
        
        return $html;
    }
}
