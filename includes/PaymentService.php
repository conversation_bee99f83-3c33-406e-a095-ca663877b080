<?php
/**
 * Payment Service
 * Phase 10: Advanced Features & API Development
 * Handle payments and subscriptions for premium features
 */

class PaymentService {
    private $conn;
    private $stripeSecretKey;
    private $stripePublishableKey;
    private $webhookSecret;
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->stripeSecretKey = $_ENV['STRIPE_SECRET_KEY'] ?? 'sk_test_...';
        $this->stripePublishableKey = $_ENV['STRIPE_PUBLISHABLE_KEY'] ?? 'pk_test_...';
        $this->webhookSecret = $_ENV['STRIPE_WEBHOOK_SECRET'] ?? 'whsec_...';
    }
    
    /**
     * Get subscription plans
     */
    public function getSubscriptionPlans() {
        return [
            'basic' => [
                'id' => 'basic',
                'name' => 'Basic',
                'price' => 0,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => [
                    'Basic beer tracking',
                    'Check-ins and ratings',
                    'Social features',
                    'Basic analytics'
                ],
                'limits' => [
                    'api_requests' => 100,
                    'exports_per_month' => 1,
                    'custom_lists' => 3
                ]
            ],
            'premium' => [
                'id' => 'premium',
                'name' => 'Premium',
                'price' => 9.99,
                'currency' => 'USD',
                'interval' => 'month',
                'stripe_price_id' => 'price_premium_monthly',
                'features' => [
                    'Everything in Basic',
                    'Advanced analytics',
                    'Export data',
                    'Priority support',
                    'Custom beer lists',
                    'QR code generation'
                ],
                'limits' => [
                    'api_requests' => 1000,
                    'exports_per_month' => 10,
                    'custom_lists' => 20
                ]
            ],
            'pro' => [
                'id' => 'pro',
                'name' => 'Pro',
                'price' => 19.99,
                'currency' => 'USD',
                'interval' => 'month',
                'stripe_price_id' => 'price_pro_monthly',
                'features' => [
                    'Everything in Premium',
                    'API access',
                    'Brewery management tools',
                    'Advanced integrations',
                    'White-label options',
                    'Custom branding'
                ],
                'limits' => [
                    'api_requests' => 5000,
                    'exports_per_month' => 'unlimited',
                    'custom_lists' => 'unlimited'
                ]
            ]
        ];
    }
    
    /**
     * Create payment intent for one-time payment
     */
    public function createPaymentIntent($amount, $currency, $userId, $description = null) {
        try {
            // Initialize Stripe (would require Stripe PHP SDK)
            // \Stripe\Stripe::setApiKey($this->stripeSecretKey);
            
            // For now, simulate payment intent creation
            $paymentIntentId = 'pi_' . uniqid();
            
            // Save payment record
            $stmt = $this->conn->prepare("
                INSERT INTO payments (user_id, stripe_payment_intent_id, amount, currency, status, description, created_at)
                VALUES (?, ?, ?, ?, 'pending', ?, NOW())
            ");
            
            $stmt->execute([$userId, $paymentIntentId, $amount, $currency, $description]);
            $paymentId = $this->conn->lastInsertId();
            
            return [
                'success' => true,
                'payment_id' => $paymentId,
                'payment_intent_id' => $paymentIntentId,
                'client_secret' => $paymentIntentId . '_secret_' . uniqid(),
                'amount' => $amount,
                'currency' => $currency
            ];
            
        } catch (Exception $e) {
            error_log("Create payment intent error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to create payment intent'];
        }
    }
    
    /**
     * Create subscription
     */
    public function createSubscription($userId, $planId, $paymentMethodId = null) {
        try {
            $plans = $this->getSubscriptionPlans();
            
            if (!isset($plans[$planId])) {
                return ['success' => false, 'error' => 'Invalid plan'];
            }
            
            $plan = $plans[$planId];
            
            if ($plan['price'] == 0) {
                // Free plan - just update user subscription
                return $this->updateUserSubscription($userId, $planId, null, 'active');
            }
            
            // For paid plans, would integrate with Stripe
            // \Stripe\Stripe::setApiKey($this->stripeSecretKey);
            
            // Simulate subscription creation
            $subscriptionId = 'sub_' . uniqid();
            
            $result = $this->updateUserSubscription($userId, $planId, $subscriptionId, 'active');
            
            if ($result['success']) {
                // Log subscription creation
                $this->logSubscriptionEvent($userId, 'subscription_created', [
                    'plan_id' => $planId,
                    'subscription_id' => $subscriptionId
                ]);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Create subscription error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to create subscription'];
        }
    }
    
    /**
     * Update user subscription
     */
    private function updateUserSubscription($userId, $planId, $subscriptionId, $status) {
        try {
            $plans = $this->getSubscriptionPlans();
            $plan = $plans[$planId];
            
            $expiresAt = null;
            if ($plan['price'] > 0) {
                $expiresAt = date('Y-m-d H:i:s', strtotime('+1 month'));
            }
            
            // Check if user already has a subscription
            $stmt = $this->conn->prepare("
                SELECT id FROM user_subscriptions WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // Update existing subscription
                $stmt = $this->conn->prepare("
                    UPDATE user_subscriptions 
                    SET plan_id = ?, stripe_subscription_id = ?, status = ?, expires_at = ?, updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$planId, $subscriptionId, $status, $expiresAt, $userId]);
            } else {
                // Create new subscription
                $stmt = $this->conn->prepare("
                    INSERT INTO user_subscriptions (user_id, plan_id, stripe_subscription_id, status, expires_at, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$userId, $planId, $subscriptionId, $status, $expiresAt]);
            }
            
            // Update user's API tier
            $this->updateUserApiTier($userId, $planId);
            
            return [
                'success' => true,
                'plan_id' => $planId,
                'status' => $status,
                'expires_at' => $expiresAt
            ];
            
        } catch (Exception $e) {
            error_log("Update user subscription error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to update subscription'];
        }
    }
    
    /**
     * Update user API tier based on subscription
     */
    private function updateUserApiTier($userId, $planId) {
        try {
            $tier = match($planId) {
                'basic' => 'public',
                'premium' => 'authenticated',
                'pro' => 'premium',
                default => 'public'
            };
            
            // Update existing API keys
            $stmt = $this->conn->prepare("
                UPDATE api_keys SET tier = ? WHERE user_id = ?
            ");
            $stmt->execute([$tier, $userId]);
            
        } catch (Exception $e) {
            error_log("Update user API tier error: " . $e->getMessage());
        }
    }
    
    /**
     * Cancel subscription
     */
    public function cancelSubscription($userId, $immediately = false) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM user_subscriptions WHERE user_id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);
            $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$subscription) {
                return ['success' => false, 'error' => 'No active subscription found'];
            }
            
            if ($immediately) {
                // Cancel immediately
                $stmt = $this->conn->prepare("
                    UPDATE user_subscriptions 
                    SET status = 'cancelled', cancelled_at = NOW(), updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$userId]);
                
                // Downgrade to basic plan
                $this->updateUserSubscription($userId, 'basic', null, 'active');
            } else {
                // Cancel at period end
                $stmt = $this->conn->prepare("
                    UPDATE user_subscriptions 
                    SET status = 'cancel_at_period_end', updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$userId]);
            }
            
            // Log cancellation
            $this->logSubscriptionEvent($userId, 'subscription_cancelled', [
                'immediately' => $immediately,
                'subscription_id' => $subscription['stripe_subscription_id']
            ]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("Cancel subscription error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to cancel subscription'];
        }
    }
    
    /**
     * Get user subscription
     */
    public function getUserSubscription($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT us.*, 
                       CASE 
                           WHEN us.expires_at IS NULL OR us.expires_at > NOW() THEN 1 
                           ELSE 0 
                       END as is_active
                FROM user_subscriptions us
                WHERE us.user_id = ?
                ORDER BY us.created_at DESC
                LIMIT 1
            ");
            
            $stmt->execute([$userId]);
            $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$subscription) {
                // Return default basic plan
                return [
                    'plan_id' => 'basic',
                    'status' => 'active',
                    'is_active' => true,
                    'expires_at' => null
                ];
            }
            
            $plans = $this->getSubscriptionPlans();
            $subscription['plan_details'] = $plans[$subscription['plan_id']] ?? $plans['basic'];
            
            return $subscription;
            
        } catch (Exception $e) {
            error_log("Get user subscription error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user has feature access
     */
    public function hasFeatureAccess($userId, $feature) {
        $subscription = $this->getUserSubscription($userId);
        
        if (!$subscription || !$subscription['is_active']) {
            $planId = 'basic';
        } else {
            $planId = $subscription['plan_id'];
        }
        
        $plans = $this->getSubscriptionPlans();
        $plan = $plans[$planId] ?? $plans['basic'];
        
        // Define feature access by plan
        $featureAccess = [
            'basic' => ['checkins', 'ratings', 'social', 'basic_analytics'],
            'premium' => ['checkins', 'ratings', 'social', 'basic_analytics', 'advanced_analytics', 'export', 'custom_lists', 'qr_codes'],
            'pro' => ['checkins', 'ratings', 'social', 'basic_analytics', 'advanced_analytics', 'export', 'custom_lists', 'qr_codes', 'api_access', 'brewery_tools', 'integrations']
        ];
        
        return in_array($feature, $featureAccess[$planId] ?? []);
    }
    
    /**
     * Process webhook from Stripe
     */
    public function processWebhook($payload, $signature) {
        try {
            // Verify webhook signature (would use Stripe SDK)
            // $event = \Stripe\Webhook::constructEvent($payload, $signature, $this->webhookSecret);
            
            // Simulate webhook processing
            $event = json_decode($payload, true);
            
            switch ($event['type']) {
                case 'invoice.payment_succeeded':
                    $this->handlePaymentSucceeded($event['data']['object']);
                    break;
                    
                case 'invoice.payment_failed':
                    $this->handlePaymentFailed($event['data']['object']);
                    break;
                    
                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;
                    
                default:
                    error_log("Unhandled webhook event: " . $event['type']);
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("Webhook processing error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Webhook processing failed'];
        }
    }
    
    /**
     * Handle successful payment
     */
    private function handlePaymentSucceeded($invoice) {
        try {
            $subscriptionId = $invoice['subscription'];
            
            // Update subscription status
            $stmt = $this->conn->prepare("
                UPDATE user_subscriptions 
                SET status = 'active', expires_at = DATE_ADD(NOW(), INTERVAL 1 MONTH), updated_at = NOW()
                WHERE stripe_subscription_id = ?
            ");
            $stmt->execute([$subscriptionId]);
            
        } catch (Exception $e) {
            error_log("Handle payment succeeded error: " . $e->getMessage());
        }
    }
    
    /**
     * Handle failed payment
     */
    private function handlePaymentFailed($invoice) {
        try {
            $subscriptionId = $invoice['subscription'];
            
            // Update subscription status
            $stmt = $this->conn->prepare("
                UPDATE user_subscriptions 
                SET status = 'past_due', updated_at = NOW()
                WHERE stripe_subscription_id = ?
            ");
            $stmt->execute([$subscriptionId]);
            
        } catch (Exception $e) {
            error_log("Handle payment failed error: " . $e->getMessage());
        }
    }
    
    /**
     * Handle subscription deletion
     */
    private function handleSubscriptionDeleted($subscription) {
        try {
            $subscriptionId = $subscription['id'];
            
            // Get user ID
            $stmt = $this->conn->prepare("
                SELECT user_id FROM user_subscriptions WHERE stripe_subscription_id = ?
            ");
            $stmt->execute([$subscriptionId]);
            $result = $stmt->fetch();
            
            if ($result) {
                // Downgrade to basic plan
                $this->updateUserSubscription($result['user_id'], 'basic', null, 'active');
            }
            
        } catch (Exception $e) {
            error_log("Handle subscription deleted error: " . $e->getMessage());
        }
    }
    
    /**
     * Log subscription events
     */
    private function logSubscriptionEvent($userId, $eventType, $data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO subscription_events (user_id, event_type, event_data, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            
            $stmt->execute([$userId, $eventType, json_encode($data)]);
            
        } catch (Exception $e) {
            error_log("Log subscription event error: " . $e->getMessage());
        }
    }
    
    /**
     * Get payment history
     */
    public function getPaymentHistory($userId, $limit = 20) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM payments
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$userId, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get payment history error: " . $e->getMessage());
            return [];
        }
    }
}
