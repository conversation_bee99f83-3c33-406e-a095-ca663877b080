<?php
/**
 * Social Media Integration Service
 * Phase 7 - Advanced Features
 * 
 * Integration with Instagram, Facebook, Twitter for digital board content
 */

class SocialMediaIntegration {
    private $conn;
    private $config;
    
    public function __construct($connection, $config = []) {
        $this->conn = $connection;
        $this->config = array_merge([
            'instagram_access_token' => '',
            'facebook_access_token' => '',
            'twitter_bearer_token' => '',
            'cache_duration' => 3600, // 1 hour
            'max_posts' => 10
        ], $config);
    }
    
    /**
     * Get Instagram posts for a brewery
     */
    public function getInstagramPosts($username, $limit = 10) {
        try {
            // Check cache first
            $cacheKey = "instagram_posts_{$username}_{$limit}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            // Instagram Basic Display API
            $accessToken = $this->config['instagram_access_token'];
            if (empty($accessToken)) {
                return $this->getMockInstagramPosts($username, $limit);
            }
            
            $url = "https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,thumbnail_url,permalink,timestamp&access_token={$accessToken}&limit={$limit}";
            
            $response = $this->makeAPIRequest($url);
            if (!$response || !isset($response['data'])) {
                return $this->getMockInstagramPosts($username, $limit);
            }
            
            $posts = [];
            foreach ($response['data'] as $post) {
                $posts[] = [
                    'id' => $post['id'],
                    'type' => 'instagram',
                    'content' => $post['caption'] ?? '',
                    'media_url' => $post['media_url'] ?? '',
                    'thumbnail_url' => $post['thumbnail_url'] ?? $post['media_url'] ?? '',
                    'permalink' => $post['permalink'] ?? '',
                    'timestamp' => $post['timestamp'] ?? '',
                    'media_type' => $post['media_type'] ?? 'IMAGE',
                    'platform' => 'Instagram',
                    'username' => $username
                ];
            }
            
            // Cache the results
            $this->saveToCache($cacheKey, $posts);
            
            return $posts;
            
        } catch (Exception $e) {
            error_log("Instagram API error: " . $e->getMessage());
            return $this->getMockInstagramPosts($username, $limit);
        }
    }
    
    /**
     * Get Facebook posts for a brewery page
     */
    public function getFacebookPosts($pageId, $limit = 10) {
        try {
            // Check cache first
            $cacheKey = "facebook_posts_{$pageId}_{$limit}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            // Facebook Graph API
            $accessToken = $this->config['facebook_access_token'];
            if (empty($accessToken)) {
                return $this->getMockFacebookPosts($pageId, $limit);
            }
            
            $url = "https://graph.facebook.com/v18.0/{$pageId}/posts?fields=id,message,full_picture,permalink_url,created_time,attachments&access_token={$accessToken}&limit={$limit}";
            
            $response = $this->makeAPIRequest($url);
            if (!$response || !isset($response['data'])) {
                return $this->getMockFacebookPosts($pageId, $limit);
            }
            
            $posts = [];
            foreach ($response['data'] as $post) {
                $posts[] = [
                    'id' => $post['id'],
                    'type' => 'facebook',
                    'content' => $post['message'] ?? '',
                    'media_url' => $post['full_picture'] ?? '',
                    'thumbnail_url' => $post['full_picture'] ?? '',
                    'permalink' => $post['permalink_url'] ?? '',
                    'timestamp' => $post['created_time'] ?? '',
                    'media_type' => 'IMAGE',
                    'platform' => 'Facebook',
                    'page_id' => $pageId
                ];
            }
            
            // Cache the results
            $this->saveToCache($cacheKey, $posts);
            
            return $posts;
            
        } catch (Exception $e) {
            error_log("Facebook API error: " . $e->getMessage());
            return $this->getMockFacebookPosts($pageId, $limit);
        }
    }
    
    /**
     * Get Twitter posts for a brewery
     */
    public function getTwitterPosts($username, $limit = 10) {
        try {
            // Check cache first
            $cacheKey = "twitter_posts_{$username}_{$limit}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            // Twitter API v2
            $bearerToken = $this->config['twitter_bearer_token'];
            if (empty($bearerToken)) {
                return $this->getMockTwitterPosts($username, $limit);
            }
            
            // First get user ID
            $userUrl = "https://api.twitter.com/2/users/by/username/{$username}";
            $userResponse = $this->makeAPIRequest($userUrl, [
                'Authorization: Bearer ' . $bearerToken
            ]);
            
            if (!$userResponse || !isset($userResponse['data']['id'])) {
                return $this->getMockTwitterPosts($username, $limit);
            }
            
            $userId = $userResponse['data']['id'];
            
            // Get user tweets
            $tweetsUrl = "https://api.twitter.com/2/users/{$userId}/tweets?tweet.fields=created_at,public_metrics,attachments&expansions=attachments.media_keys&media.fields=url,preview_image_url&max_results={$limit}";
            
            $response = $this->makeAPIRequest($tweetsUrl, [
                'Authorization: Bearer ' . $bearerToken
            ]);
            
            if (!$response || !isset($response['data'])) {
                return $this->getMockTwitterPosts($username, $limit);
            }
            
            $posts = [];
            foreach ($response['data'] as $tweet) {
                $mediaUrl = '';
                $thumbnailUrl = '';
                
                // Check for media attachments
                if (isset($tweet['attachments']['media_keys']) && isset($response['includes']['media'])) {
                    foreach ($response['includes']['media'] as $media) {
                        if (in_array($media['media_key'], $tweet['attachments']['media_keys'])) {
                            $mediaUrl = $media['url'] ?? '';
                            $thumbnailUrl = $media['preview_image_url'] ?? $mediaUrl;
                            break;
                        }
                    }
                }
                
                $posts[] = [
                    'id' => $tweet['id'],
                    'type' => 'twitter',
                    'content' => $tweet['text'] ?? '',
                    'media_url' => $mediaUrl,
                    'thumbnail_url' => $thumbnailUrl,
                    'permalink' => "https://twitter.com/{$username}/status/{$tweet['id']}",
                    'timestamp' => $tweet['created_at'] ?? '',
                    'media_type' => $mediaUrl ? 'IMAGE' : 'TEXT',
                    'platform' => 'Twitter',
                    'username' => $username,
                    'metrics' => $tweet['public_metrics'] ?? []
                ];
            }
            
            // Cache the results
            $this->saveToCache($cacheKey, $posts);
            
            return $posts;
            
        } catch (Exception $e) {
            error_log("Twitter API error: " . $e->getMessage());
            return $this->getMockTwitterPosts($username, $limit);
        }
    }
    
    /**
     * Get combined social media feed
     */
    public function getCombinedFeed($socialAccounts, $limit = 20) {
        $allPosts = [];
        
        foreach ($socialAccounts as $account) {
            switch ($account['platform']) {
                case 'instagram':
                    $posts = $this->getInstagramPosts($account['username'], $limit);
                    break;
                case 'facebook':
                    $posts = $this->getFacebookPosts($account['page_id'], $limit);
                    break;
                case 'twitter':
                    $posts = $this->getTwitterPosts($account['username'], $limit);
                    break;
                default:
                    continue 2;
            }
            
            $allPosts = array_merge($allPosts, $posts);
        }
        
        // Sort by timestamp (newest first)
        usort($allPosts, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        // Limit results
        return array_slice($allPosts, 0, $limit);
    }
    
    /**
     * Create social media slide content
     */
    public function createSocialSlide($posts, $template = 'grid') {
        $slideContent = [
            'type' => 'social_media',
            'template' => $template,
            'posts' => array_slice($posts, 0, 6), // Limit to 6 posts for display
            'generated_at' => date('c'),
            'auto_refresh' => true,
            'refresh_interval' => 300 // 5 minutes
        ];
        
        return $slideContent;
    }
    
    /**
     * Save social media configuration for brewery
     */
    public function saveSocialConfig($breweryId, $config) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO brewery_social_config (brewery_id, platform, username, page_id, access_token, is_active, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                username = VALUES(username),
                page_id = VALUES(page_id),
                access_token = VALUES(access_token),
                is_active = VALUES(is_active),
                updated_at = NOW()
            ");
            
            foreach ($config as $platform => $settings) {
                $stmt->execute([
                    $breweryId,
                    $platform,
                    $settings['username'] ?? null,
                    $settings['page_id'] ?? null,
                    $settings['access_token'] ?? null,
                    $settings['is_active'] ?? true
                ]);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Save social config error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get social media configuration for brewery
     */
    public function getSocialConfig($breweryId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT platform, username, page_id, is_active
                FROM brewery_social_config
                WHERE brewery_id = ? AND is_active = 1
            ");
            $stmt->execute([$breweryId]);
            
            $config = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $config[$row['platform']] = $row;
            }
            
            return $config;
            
        } catch (Exception $e) {
            error_log("Get social config error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Helper methods
     */
    private function makeAPIRequest($url, $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with code: {$httpCode}");
        }
        
        return json_decode($response, true);
    }
    
    private function getFromCache($key) {
        try {
            $stmt = $this->conn->prepare("
                SELECT data FROM social_media_cache 
                WHERE cache_key = ? AND expires_at > NOW()
            ");
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? json_decode($result['data'], true) : null;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    private function saveToCache($key, $data) {
        try {
            $expiresAt = date('Y-m-d H:i:s', time() + $this->config['cache_duration']);
            
            $stmt = $this->conn->prepare("
                INSERT INTO social_media_cache (cache_key, data, expires_at)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                data = VALUES(data),
                expires_at = VALUES(expires_at)
            ");
            
            $stmt->execute([$key, json_encode($data), $expiresAt]);
            
        } catch (Exception $e) {
            error_log("Cache save error: " . $e->getMessage());
        }
    }
    
    // Mock data methods for development/fallback
    private function getMockInstagramPosts($username, $limit) {
        $posts = [];
        for ($i = 0; $i < $limit; $i++) {
            $posts[] = [
                'id' => 'mock_ig_' . $i,
                'type' => 'instagram',
                'content' => "Fresh craft beer on tap! Come try our latest IPA. #craftbeer #brewery #fresh",
                'media_url' => "https://picsum.photos/400/400?random=" . $i,
                'thumbnail_url' => "https://picsum.photos/200/200?random=" . $i,
                'permalink' => "https://instagram.com/p/mock{$i}",
                'timestamp' => date('c', time() - ($i * 3600)),
                'media_type' => 'IMAGE',
                'platform' => 'Instagram',
                'username' => $username
            ];
        }
        return $posts;
    }
    
    private function getMockFacebookPosts($pageId, $limit) {
        $posts = [];
        for ($i = 0; $i < $limit; $i++) {
            $posts[] = [
                'id' => 'mock_fb_' . $i,
                'type' => 'facebook',
                'content' => "Join us tonight for live music and great beer! Happy hour starts at 5 PM.",
                'media_url' => "https://picsum.photos/600/400?random=" . ($i + 100),
                'thumbnail_url' => "https://picsum.photos/300/200?random=" . ($i + 100),
                'permalink' => "https://facebook.com/mock/posts/{$i}",
                'timestamp' => date('c', time() - ($i * 7200)),
                'media_type' => 'IMAGE',
                'platform' => 'Facebook',
                'page_id' => $pageId
            ];
        }
        return $posts;
    }
    
    private function getMockTwitterPosts($username, $limit) {
        $posts = [];
        for ($i = 0; $i < $limit; $i++) {
            $posts[] = [
                'id' => 'mock_tw_' . $i,
                'type' => 'twitter',
                'content' => "New beer alert! 🍺 Our seasonal porter is now available. Rich, smooth, and perfect for the weather. #newbeer #porter #craftbeer",
                'media_url' => $i % 3 === 0 ? "https://picsum.photos/500/300?random=" . ($i + 200) : '',
                'thumbnail_url' => $i % 3 === 0 ? "https://picsum.photos/250/150?random=" . ($i + 200) : '',
                'permalink' => "https://twitter.com/{$username}/status/mock{$i}",
                'timestamp' => date('c', time() - ($i * 1800)),
                'media_type' => $i % 3 === 0 ? 'IMAGE' : 'TEXT',
                'platform' => 'Twitter',
                'username' => $username,
                'metrics' => [
                    'retweet_count' => rand(5, 50),
                    'like_count' => rand(10, 100),
                    'reply_count' => rand(1, 20)
                ]
            ];
        }
        return $posts;
    }
}
