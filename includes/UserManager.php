<?php
/**
 * Enhanced User Manager Class
 * Phase 5 - User Management & Authentication
 * 
 * Comprehensive user management with role-based permissions and digital board access
 */

class UserManager {
    private $conn;
    private $permissionManager;
    
    // Enhanced user roles for digital board system
    const ROLES = [
        'admin' => 'System Administrator',
        'site_moderator' => 'Site Moderator',
        'business_owner' => 'Business Owner',
        'business_manager' => 'Business Manager',
        'digital_board_operator' => 'Digital Board Operator',
        'user' => 'Standard User'
    ];
    
    const USER_STATUSES = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'pending' => 'Pending Verification',
        'locked' => 'Account Locked'
    ];
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->permissionManager = new PermissionManager($connection);
    }
    
    /**
     * Create a new user with enhanced validation
     */
    public function createUser($userData) {
        $validation = $this->validateUserData($userData);
        if (!$validation['success']) {
            return $validation;
        }
        
        try {
            $this->conn->beginTransaction();
            
            // Generate user ID
            $userId = $this->generateUserId();
            
            // Hash password
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            // Insert user
            $stmt = $this->conn->prepare("
                INSERT INTO users (
                    id, email, password_hash, first_name, last_name, 
                    role, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $userId,
                $userData['email'],
                $passwordHash,
                $userData['first_name'] ?? '',
                $userData['last_name'] ?? '',
                $userData['role'] ?? 'user',
                $userData['status'] ?? 'active'
            ]);
            
            // Insert profile
            $stmt = $this->conn->prepare("
                INSERT INTO profiles (
                    id, email, role, brewery_id, phone, address, city, state, 
                    zip_code, preferences, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $userId,
                $userData['email'],
                $userData['role'] ?? 'user',
                $userData['brewery_id'] ?? null,
                $userData['phone'] ?? null,
                $userData['address'] ?? null,
                $userData['city'] ?? null,
                $userData['state'] ?? null,
                $userData['zip_code'] ?? null,
                json_encode($userData['preferences'] ?? [])
            ]);
            
            // Create digital board permissions if applicable
            if (in_array($userData['role'], ['business_owner', 'business_manager', 'digital_board_operator'])) {
                $this->createDigitalBoardPermissions($userId, $userData['brewery_id'], $userData['role']);
            }
            
            // Log user creation
            $this->logUserActivity($userId, 'user_created', [
                'created_by' => $_SESSION['user_id'] ?? 'system',
                'role' => $userData['role']
            ]);
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'user_id' => $userId,
                'message' => 'User created successfully'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("UserManager: Error creating user - " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'Failed to create user: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update user with enhanced validation
     */
    public function updateUser($userId, $userData) {
        $validation = $this->validateUserData($userData, $userId);
        if (!$validation['success']) {
            return $validation;
        }
        
        try {
            $this->conn->beginTransaction();
            
            // Get current user data for comparison
            $currentUser = $this->getUserById($userId);
            if (!$currentUser) {
                return ['success' => false, 'error' => 'User not found'];
            }
            
            // Update users table
            $userFields = [];
            $userParams = [];
            
            foreach (['email', 'first_name', 'last_name', 'role', 'status'] as $field) {
                if (isset($userData[$field]) && $userData[$field] !== $currentUser[$field]) {
                    $userFields[] = "$field = ?";
                    $userParams[] = $userData[$field];
                }
            }
            
            if (!empty($userFields)) {
                $userFields[] = "updated_at = NOW()";
                $userParams[] = $userId;
                
                $stmt = $this->conn->prepare("
                    UPDATE users SET " . implode(', ', $userFields) . " WHERE id = ?
                ");
                $stmt->execute($userParams);
            }
            
            // Update profiles table
            $profileFields = [];
            $profileParams = [];
            
            foreach (['role', 'brewery_id', 'phone', 'address', 'city', 'state', 'zip_code'] as $field) {
                if (isset($userData[$field]) && $userData[$field] !== $currentUser[$field]) {
                    $profileFields[] = "$field = ?";
                    $profileParams[] = $userData[$field];
                }
            }
            
            if (isset($userData['preferences'])) {
                $profileFields[] = "preferences = ?";
                $profileParams[] = json_encode($userData['preferences']);
            }
            
            if (!empty($profileFields)) {
                $profileFields[] = "updated_at = NOW()";
                $profileParams[] = $userId;
                
                $stmt = $this->conn->prepare("
                    UPDATE profiles SET " . implode(', ', $profileFields) . " WHERE id = ?
                ");
                $stmt->execute($profileParams);
            }
            
            // Update digital board permissions if role changed
            if (isset($userData['role']) && $userData['role'] !== $currentUser['role']) {
                $this->updateDigitalBoardPermissions($userId, $userData['brewery_id'] ?? $currentUser['brewery_id'], $userData['role']);
            }
            
            // Log user update
            $this->logUserActivity($userId, 'user_updated', [
                'updated_by' => $_SESSION['user_id'] ?? 'system',
                'changes' => array_keys($userData)
            ]);
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'message' => 'User updated successfully'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("UserManager: Error updating user - " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'Failed to update user: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user by ID with full profile data
     */
    public function getUserById($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    u.*,
                    p.brewery_id,
                    p.phone,
                    p.address,
                    p.city,
                    p.state,
                    p.zip_code,
                    p.preferences,
                    b.name as brewery_name
                FROM users u
                LEFT JOIN profiles p ON u.id = p.id
                LEFT JOIN breweries b ON p.brewery_id = b.id
                WHERE u.id = ?
            ");
            
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                $user['preferences'] = json_decode($user['preferences'], true) ?? [];
                $user['permissions'] = $this->permissionManager->getUserPermissions($userId);
            }
            
            return $user;
            
        } catch (Exception $e) {
            error_log("UserManager: Error getting user by ID - " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get users with filtering and pagination
     */
    public function getUsers($filters = [], $page = 1, $limit = 20) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Apply filters
            if (!empty($filters['role'])) {
                $where[] = 'u.role = ?';
                $params[] = $filters['role'];
            }
            
            if (!empty($filters['status'])) {
                $where[] = 'u.status = ?';
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['brewery_id'])) {
                $where[] = 'p.brewery_id = ?';
                $params[] = $filters['brewery_id'];
            }
            
            if (!empty($filters['search'])) {
                $where[] = '(u.email LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)';
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            // Count total records
            $countQuery = "
                SELECT COUNT(DISTINCT u.id)
                FROM users u
                LEFT JOIN profiles p ON u.id = p.id
                WHERE " . implode(' AND ', $where);
            
            $countStmt = $this->conn->prepare($countQuery);
            $countStmt->execute($params);
            $total = $countStmt->fetchColumn();
            
            // Get paginated results
            $offset = ($page - 1) * $limit;
            $query = "
                SELECT 
                    u.*,
                    p.brewery_id,
                    p.phone,
                    b.name as brewery_name,
                    (SELECT COUNT(*) FROM user_activity_log WHERE user_id = u.id) as activity_count
                FROM users u
                LEFT JOIN profiles p ON u.id = p.id
                LEFT JOIN breweries b ON p.brewery_id = b.id
                WHERE " . implode(' AND ', $where) . "
                ORDER BY u.created_at DESC
                LIMIT $limit OFFSET $offset
            ";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'data' => $users,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit),
                    'has_next' => $page < ceil($total / $limit),
                    'has_prev' => $page > 1
                ]
            ];
            
        } catch (Exception $e) {
            error_log("UserManager: Error getting users - " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Failed to retrieve users: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Validate user data
     */
    private function validateUserData($userData, $userId = null) {
        $errors = [];
        
        // Email validation
        if (empty($userData['email'])) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        } else {
            // Check for duplicate email
            $stmt = $this->conn->prepare("SELECT id FROM users WHERE email = ?" . ($userId ? " AND id != ?" : ""));
            $params = [$userData['email']];
            if ($userId) $params[] = $userId;
            
            $stmt->execute($params);
            if ($stmt->fetch()) {
                $errors[] = 'Email already exists';
            }
        }
        
        // Password validation (for new users)
        if (!$userId && empty($userData['password'])) {
            $errors[] = 'Password is required';
        } elseif (!empty($userData['password']) && strlen($userData['password']) < 6) {
            $errors[] = 'Password must be at least 6 characters long';
        }
        
        // Role validation
        if (!empty($userData['role']) && !array_key_exists($userData['role'], self::ROLES)) {
            $errors[] = 'Invalid role specified';
        }
        
        // Status validation
        if (!empty($userData['status']) && !array_key_exists($userData['status'], self::USER_STATUSES)) {
            $errors[] = 'Invalid status specified';
        }
        
        // Brewery validation for business roles
        if (in_array($userData['role'] ?? '', ['business_owner', 'business_manager', 'digital_board_operator'])) {
            if (empty($userData['brewery_id'])) {
                $errors[] = 'Brewery is required for business roles';
            }
        }
        
        return [
            'success' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Generate unique user ID
     */
    private function generateUserId() {
        return 'user_' . bin2hex(random_bytes(16));
    }
    
    /**
     * Create digital board permissions for user
     */
    private function createDigitalBoardPermissions($userId, $breweryId, $role) {
        if (!$breweryId) return;
        
        $permissions = $this->permissionManager->getDefaultPermissionsForRole($role);
        $this->permissionManager->assignPermissions($userId, $breweryId, $permissions);
    }
    
    /**
     * Update digital board permissions for user
     */
    private function updateDigitalBoardPermissions($userId, $breweryId, $role) {
        if (!$breweryId) return;
        
        $this->permissionManager->revokeAllPermissions($userId);
        $this->createDigitalBoardPermissions($userId, $breweryId, $role);
    }
    
    /**
     * Log user activity
     */
    private function logUserActivity($userId, $action, $details = []) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO user_activity_log (user_id, action, details, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $action,
                json_encode($details),
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            error_log("UserManager: Error logging activity - " . $e->getMessage());
        }
    }
}
