<?php
/**
 * AnalyticsService - Advanced analytics and business intelligence
 * Phase 8: Analytics & Business Intelligence
 */

class AnalyticsService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get comprehensive user analytics
     */
    public function getUserAnalytics($userId, $timeframe = '1_year') {
        $analytics = [
            'drinking_patterns' => $this->getDrinkingPatterns($userId, $timeframe),
            'beer_preferences' => $this->getBeerPreferenceEvolution($userId, $timeframe),
            'social_analytics' => $this->getSocialAnalytics($userId, $timeframe),
            'achievement_timeline' => $this->getAchievementTimeline($userId, $timeframe),
            'rating_behavior' => $this->getRatingBehaviorAnalysis($userId, $timeframe),
            'location_insights' => $this->getLocationInsights($userId, $timeframe)
        ];
        
        return $analytics;
    }
    
    /**
     * Analyze drinking patterns
     */
    public function getDrinkingPatterns($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe);
        
        try {
            // Hourly patterns
            $stmt = $this->conn->prepare("
                SELECT 
                    HOUR(created_at) as hour,
                    COUNT(*) as checkin_count,
                    AVG(rating) as avg_rating
                FROM beer_checkins 
                WHERE user_id = ? $timeCondition
                GROUP BY HOUR(created_at)
                ORDER BY hour
            ");
            $stmt->execute([$userId]);
            $hourlyPatterns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Daily patterns
            $stmt = $this->conn->prepare("
                SELECT 
                    DAYOFWEEK(created_at) as day_of_week,
                    DAYNAME(created_at) as day_name,
                    COUNT(*) as checkin_count,
                    AVG(rating) as avg_rating
                FROM beer_checkins 
                WHERE user_id = ? $timeCondition
                GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
                ORDER BY day_of_week
            ");
            $stmt->execute([$userId]);
            $dailyPatterns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Monthly trends
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    COUNT(*) as checkin_count,
                    COUNT(DISTINCT beer_id) as unique_beers,
                    AVG(rating) as avg_rating
                FROM beer_checkins 
                WHERE user_id = ? $timeCondition
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month
            ");
            $stmt->execute([$userId]);
            $monthlyTrends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Seasonal analysis
            $stmt = $this->conn->prepare("
                SELECT 
                    CASE 
                        WHEN MONTH(created_at) IN (12, 1, 2) THEN 'Winter'
                        WHEN MONTH(created_at) IN (3, 4, 5) THEN 'Spring'
                        WHEN MONTH(created_at) IN (6, 7, 8) THEN 'Summer'
                        WHEN MONTH(created_at) IN (9, 10, 11) THEN 'Fall'
                    END as season,
                    COUNT(*) as checkin_count,
                    AVG(rating) as avg_rating
                FROM beer_checkins 
                WHERE user_id = ? $timeCondition
                GROUP BY season
                ORDER BY 
                    CASE season
                        WHEN 'Spring' THEN 1
                        WHEN 'Summer' THEN 2
                        WHEN 'Fall' THEN 3
                        WHEN 'Winter' THEN 4
                    END
            ");
            $stmt->execute([$userId]);
            $seasonalPatterns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'hourly' => $hourlyPatterns,
                'daily' => $dailyPatterns,
                'monthly' => $monthlyTrends,
                'seasonal' => $seasonalPatterns
            ];
            
        } catch (Exception $e) {
            error_log("Drinking patterns analysis error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Analyze beer preference evolution
     */
    public function getBeerPreferenceEvolution($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe);
        
        try {
            // Style preferences over time
            $stmt = $this->conn->prepare("
                SELECT 
                    bs.name as style_name,
                    DATE_FORMAT(bc.created_at, '%Y-%m') as month,
                    COUNT(*) as checkin_count,
                    AVG(bc.rating) as avg_rating
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE bc.user_id = ? $timeCondition
                GROUP BY bs.id, bs.name, DATE_FORMAT(bc.created_at, '%Y-%m')
                ORDER BY month, checkin_count DESC
            ");
            $stmt->execute([$userId]);
            $styleEvolution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // ABV preferences
            $stmt = $this->conn->prepare("
                SELECT 
                    CASE 
                        WHEN bm.abv < 4.0 THEN 'Low (< 4%)'
                        WHEN bm.abv BETWEEN 4.0 AND 6.0 THEN 'Medium (4-6%)'
                        WHEN bm.abv BETWEEN 6.0 AND 8.0 THEN 'High (6-8%)'
                        WHEN bm.abv > 8.0 THEN 'Very High (> 8%)'
                        ELSE 'Unknown'
                    END as abv_range,
                    COUNT(*) as checkin_count,
                    AVG(bc.rating) as avg_rating
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                WHERE bc.user_id = ? $timeCondition AND bm.abv IS NOT NULL
                GROUP BY abv_range
                ORDER BY checkin_count DESC
            ");
            $stmt->execute([$userId]);
            $abvPreferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // IBU preferences
            $stmt = $this->conn->prepare("
                SELECT 
                    CASE 
                        WHEN bm.ibu < 20 THEN 'Low Bitterness (< 20)'
                        WHEN bm.ibu BETWEEN 20 AND 40 THEN 'Medium Bitterness (20-40)'
                        WHEN bm.ibu BETWEEN 40 AND 60 THEN 'High Bitterness (40-60)'
                        WHEN bm.ibu > 60 THEN 'Very High Bitterness (> 60)'
                        ELSE 'Unknown'
                    END as ibu_range,
                    COUNT(*) as checkin_count,
                    AVG(bc.rating) as avg_rating
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                WHERE bc.user_id = ? $timeCondition AND bm.ibu IS NOT NULL
                GROUP BY ibu_range
                ORDER BY checkin_count DESC
            ");
            $stmt->execute([$userId]);
            $ibuPreferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'style_evolution' => $styleEvolution,
                'abv_preferences' => $abvPreferences,
                'ibu_preferences' => $ibuPreferences
            ];
            
        } catch (Exception $e) {
            error_log("Beer preference evolution error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get social analytics
     */
    public function getSocialAnalytics($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe);
        
        try {
            // Follower growth
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    COUNT(*) as new_followers
                FROM user_follows 
                WHERE followed_id = ? $timeCondition
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month
            ");
            $stmt->execute([$userId]);
            $followerGrowth = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Activity engagement
            $stmt = $this->conn->prepare("
                SELECT 
                    activity_type,
                    COUNT(*) as activity_count,
                    DATE_FORMAT(created_at, '%Y-%m') as month
                FROM user_activities 
                WHERE user_id = ? $timeCondition
                GROUP BY activity_type, DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month, activity_count DESC
            ");
            $stmt->execute([$userId]);
            $activityEngagement = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Social influence score
            $stmt = $this->conn->prepare("
                SELECT 
                    (SELECT COUNT(*) FROM user_follows WHERE followed_id = ?) as followers,
                    (SELECT COUNT(*) FROM user_follows WHERE follower_id = ?) as following,
                    (SELECT COUNT(*) FROM activity_likes al 
                     JOIN user_activities ua ON al.activity_id = ua.id 
                     WHERE ua.user_id = ?) as likes_received,
                    (SELECT COUNT(*) FROM beer_ratings WHERE user_id = ?) as total_ratings,
                    (SELECT COUNT(*) FROM beer_checkins WHERE user_id = ?) as total_checkins
            ");
            $stmt->execute([$userId, $userId, $userId, $userId, $userId]);
            $influenceData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Calculate influence score
            $influenceScore = $this->calculateInfluenceScore($influenceData);
            
            return [
                'follower_growth' => $followerGrowth,
                'activity_engagement' => $activityEngagement,
                'influence_score' => $influenceScore,
                'influence_data' => $influenceData
            ];
            
        } catch (Exception $e) {
            error_log("Social analytics error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get achievement timeline
     */
    public function getAchievementTimeline($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe, 'ub.earned_at');
        
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    b.name as badge_name,
                    b.description,
                    b.category,
                    b.rarity,
                    ub.earned_at,
                    DATE_FORMAT(ub.earned_at, '%Y-%m') as month
                FROM user_badges ub
                JOIN badges b ON ub.badge_id = b.id
                WHERE ub.user_id = ? $timeCondition
                ORDER BY ub.earned_at DESC
            ");
            $stmt->execute([$userId]);
            $achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Group by month for timeline
            $timeline = [];
            foreach ($achievements as $achievement) {
                $month = $achievement['month'];
                if (!isset($timeline[$month])) {
                    $timeline[$month] = [];
                }
                $timeline[$month][] = $achievement;
            }
            
            return [
                'achievements' => $achievements,
                'timeline' => $timeline
            ];
            
        } catch (Exception $e) {
            error_log("Achievement timeline error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Analyze rating behavior
     */
    public function getRatingBehaviorAnalysis($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe, 'br.created_at');
        
        try {
            // Rating distribution
            $stmt = $this->conn->prepare("
                SELECT 
                    overall_rating,
                    COUNT(*) as rating_count
                FROM beer_ratings 
                WHERE user_id = ? $timeCondition
                GROUP BY overall_rating
                ORDER BY overall_rating
            ");
            $stmt->execute([$userId]);
            $ratingDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Rating trends over time
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    AVG(overall_rating) as avg_rating,
                    COUNT(*) as rating_count
                FROM beer_ratings 
                WHERE user_id = ? $timeCondition
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month
            ");
            $stmt->execute([$userId]);
            $ratingTrends = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Detailed rating analysis
            $stmt = $this->conn->prepare("
                SELECT 
                    AVG(overall_rating) as avg_overall,
                    AVG(taste_rating) as avg_taste,
                    AVG(aroma_rating) as avg_aroma,
                    AVG(appearance_rating) as avg_appearance,
                    AVG(mouthfeel_rating) as avg_mouthfeel,
                    COUNT(*) as total_ratings
                FROM beer_ratings 
                WHERE user_id = ? $timeCondition
            ");
            $stmt->execute([$userId]);
            $detailedAnalysis = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'distribution' => $ratingDistribution,
                'trends' => $ratingTrends,
                'detailed_analysis' => $detailedAnalysis
            ];
            
        } catch (Exception $e) {
            error_log("Rating behavior analysis error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get location insights
     */
    public function getLocationInsights($userId, $timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe, 'bc.created_at');
        
        try {
            // Top locations
            $stmt = $this->conn->prepare("
                SELECT 
                    bc.checkin_location,
                    COUNT(*) as checkin_count,
                    AVG(bc.rating) as avg_rating,
                    COUNT(DISTINCT bc.beer_id) as unique_beers
                FROM beer_checkins bc
                WHERE bc.user_id = ? AND bc.checkin_location IS NOT NULL $timeCondition
                GROUP BY bc.checkin_location
                ORDER BY checkin_count DESC
                LIMIT 10
            ");
            $stmt->execute([$userId]);
            $topLocations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Brewery visits
            $stmt = $this->conn->prepare("
                SELECT 
                    b.name as brewery_name,
                    b.city,
                    b.state,
                    COUNT(*) as visit_count,
                    AVG(bc.rating) as avg_rating,
                    COUNT(DISTINCT bc.beer_id) as unique_beers_tried
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN breweries b ON bm.brewery_id = b.id
                WHERE bc.user_id = ? $timeCondition
                GROUP BY b.id, b.name, b.city, b.state
                ORDER BY visit_count DESC
                LIMIT 10
            ");
            $stmt->execute([$userId]);
            $breweryVisits = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'top_locations' => $topLocations,
                'brewery_visits' => $breweryVisits
            ];
            
        } catch (Exception $e) {
            error_log("Location insights error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate influence score
     */
    private function calculateInfluenceScore($data) {
        $followers = $data['followers'] ?? 0;
        $following = $data['following'] ?? 0;
        $likesReceived = $data['likes_received'] ?? 0;
        $totalRatings = $data['total_ratings'] ?? 0;
        $totalCheckins = $data['total_checkins'] ?? 0;
        
        // Weighted influence calculation
        $followerScore = $followers * 2;
        $engagementScore = $likesReceived * 1.5;
        $contentScore = ($totalRatings * 1.2) + ($totalCheckins * 0.8);
        $followRatio = $following > 0 ? ($followers / $following) : $followers;
        
        $influenceScore = ($followerScore + $engagementScore + $contentScore) * min($followRatio, 2);
        
        return round($influenceScore, 2);
    }
    
    /**
     * Get platform-wide statistics (Admin)
     */
    public function getPlatformStatistics($timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe);

        try {
            // Total users and new users
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL " . $this->getIntervalFromTimeframe($timeframe) . ") THEN 1 END) as new_users
                FROM users
            ");
            $stmt->execute();
            $userStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Total check-ins and new check-ins
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(*) as total_checkins,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL " . $this->getIntervalFromTimeframe($timeframe) . ") THEN 1 END) as new_checkins
                FROM beer_checkins
            ");
            $stmt->execute();
            $checkinStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Total ratings and new ratings
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(*) as total_ratings,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL " . $this->getIntervalFromTimeframe($timeframe) . ") THEN 1 END) as new_ratings
                FROM beer_ratings
            ");
            $stmt->execute();
            $ratingStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // Active breweries
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(DISTINCT b.id) as active_breweries,
                    COUNT(DISTINCT CASE WHEN b.created_at >= DATE_SUB(NOW(), INTERVAL " . $this->getIntervalFromTimeframe($timeframe) . ") THEN b.id END) as new_breweries
                FROM breweries b
                WHERE b.is_active = TRUE
            ");
            $stmt->execute();
            $breweryStats = $stmt->fetch(PDO::FETCH_ASSOC);

            return array_merge($userStats, $checkinStats, $ratingStats, $breweryStats);

        } catch (Exception $e) {
            error_log("Platform statistics error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user engagement metrics (Admin)
     */
    public function getUserEngagementMetrics($timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe);

        try {
            // Daily active users
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT user_id) as dau
                FROM user_activities
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            ");
            $stmt->execute();
            $dauResult = $stmt->fetch(PDO::FETCH_ASSOC);

            // Monthly active users
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT user_id) as mau
                FROM user_activities
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            ");
            $stmt->execute();
            $mauResult = $stmt->fetch(PDO::FETCH_ASSOC);

            // User retention rate (simplified calculation)
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(DISTINCT CASE WHEN ua.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) THEN ua.user_id END) as active_users,
                    COUNT(DISTINCT u.id) as total_users
                FROM users u
                LEFT JOIN user_activities ua ON u.id = ua.user_id
                WHERE u.created_at <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
            ");
            $stmt->execute();
            $retentionData = $stmt->fetch(PDO::FETCH_ASSOC);

            $retentionRate = $retentionData['total_users'] > 0 ?
                ($retentionData['active_users'] / $retentionData['total_users']) * 100 : 0;

            // Daily activity trends
            $stmt = $this->conn->prepare("
                SELECT
                    DATE(created_at) as date,
                    COUNT(DISTINCT user_id) as active_users,
                    COUNT(DISTINCT CASE WHEN activity_type = 'joined' THEN user_id END) as new_registrations
                FROM user_activities
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date
            ");
            $stmt->execute();
            $dailyActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'dau' => $dauResult['dau'],
                'mau' => $mauResult['mau'],
                'retention_rate' => $retentionRate,
                'avg_session_duration' => 15, // Placeholder - would need session tracking
                'daily_activity' => $dailyActivity
            ];

        } catch (Exception $e) {
            error_log("User engagement metrics error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get beer trends (Admin)
     */
    public function getBeerTrends($timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe, 'bc.created_at');

        try {
            // Popular beer styles
            $stmt = $this->conn->prepare("
                SELECT
                    bs.name as style_name,
                    COUNT(bc.id) as checkin_count,
                    AVG(bc.rating) as avg_rating
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE 1=1 $timeCondition
                GROUP BY bs.id, bs.name
                ORDER BY checkin_count DESC
                LIMIT 10
            ");
            $stmt->execute();
            $popularStyles = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Top rated beers
            $stmt = $this->conn->prepare("
                SELECT
                    bm.name as beer_name,
                    b.name as brewery_name,
                    AVG(br.overall_rating) as avg_rating,
                    COUNT(br.id) as review_count
                FROM beer_ratings br
                JOIN beer_menu bm ON br.beer_id = bm.id
                JOIN breweries b ON bm.brewery_id = b.id
                WHERE br.created_at >= DATE_SUB(NOW(), INTERVAL " . $this->getIntervalFromTimeframe($timeframe) . ")
                GROUP BY bm.id, bm.name, b.name
                HAVING review_count >= 5
                ORDER BY avg_rating DESC, review_count DESC
                LIMIT 15
            ");
            $stmt->execute();
            $topRated = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'popular_styles' => $popularStyles,
                'top_rated' => $topRated
            ];

        } catch (Exception $e) {
            error_log("Beer trends error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get brewery performance metrics (Admin)
     */
    public function getBreweryPerformanceMetrics($timeframe = '1_year') {
        $timeCondition = $this->getTimeCondition($timeframe, 'bc.created_at');

        try {
            // Top breweries by check-ins
            $stmt = $this->conn->prepare("
                SELECT
                    b.name,
                    b.city,
                    b.state,
                    COUNT(bc.id) as total_checkins,
                    AVG(bc.rating) as avg_rating,
                    COUNT(DISTINCT bc.user_id) as unique_visitors
                FROM breweries b
                JOIN beer_menu bm ON b.id = bm.brewery_id
                JOIN beer_checkins bc ON bm.id = bc.beer_id
                WHERE 1=1 $timeCondition
                GROUP BY b.id, b.name, b.city, b.state
                ORDER BY total_checkins DESC
                LIMIT 10
            ");
            $stmt->execute();
            $topBreweries = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Monthly performance trends
            $stmt = $this->conn->prepare("
                SELECT
                    DATE_FORMAT(bc.created_at, '%Y-%m') as month,
                    COUNT(bc.id) as total_checkins,
                    COUNT(DISTINCT bc.user_id) as unique_users,
                    COUNT(DISTINCT bm.brewery_id) as active_breweries
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                WHERE bc.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(bc.created_at, '%Y-%m')
                ORDER BY month
            ");
            $stmt->execute();
            $monthlyPerformance = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'top_breweries' => $topBreweries,
                'monthly_performance' => $monthlyPerformance
            ];

        } catch (Exception $e) {
            error_log("Brewery performance metrics error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get geographic usage patterns (Admin)
     */
    public function getGeographicUsagePatterns($timeframe = '1_year') {
        try {
            // Top locations by user count
            $stmt = $this->conn->prepare("
                SELECT
                    p.city,
                    p.state,
                    COUNT(DISTINCT p.id) as user_count,
                    COUNT(bc.id) as checkin_count
                FROM profiles p
                LEFT JOIN beer_checkins bc ON p.id = bc.user_id
                WHERE p.city IS NOT NULL AND p.state IS NOT NULL
                GROUP BY p.city, p.state
                ORDER BY user_count DESC, checkin_count DESC
                LIMIT 15
            ");
            $stmt->execute();
            $topLocations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'top_locations' => $topLocations
            ];

        } catch (Exception $e) {
            error_log("Geographic usage patterns error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get year in review data for a user
     */
    public function getYearInReview($userId, $year) {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    COUNT(bc.id) as total_checkins,
                    COUNT(DISTINCT bc.beer_id) as unique_beers,
                    COUNT(DISTINCT bm.brewery_id) as unique_breweries,
                    AVG(bc.rating) as avg_rating,
                    COUNT(DISTINCT bs.id) as unique_styles
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE bc.user_id = ? AND YEAR(bc.created_at) = ?
            ");
            $stmt->execute([$userId, $year]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

        } catch (Exception $e) {
            error_log("Year in review error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get year highlights for a user
     */
    public function getYearHighlights($userId, $year) {
        try {
            $highlights = [];

            // Favorite beer (highest rated with multiple check-ins)
            $stmt = $this->conn->prepare("
                SELECT
                    bm.name,
                    b.name as brewery,
                    AVG(bc.rating) as rating,
                    COUNT(bc.id) as checkin_count
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN breweries b ON bm.brewery_id = b.id
                WHERE bc.user_id = ? AND YEAR(bc.created_at) = ?
                GROUP BY bm.id, bm.name, b.name
                HAVING checkin_count >= 2
                ORDER BY rating DESC, checkin_count DESC
                LIMIT 1
            ");
            $stmt->execute([$userId, $year]);
            $highlights['favorite_beer'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // Most active month
            $stmt = $this->conn->prepare("
                SELECT
                    MONTH(created_at) as month,
                    COUNT(*) as checkins
                FROM beer_checkins
                WHERE user_id = ? AND YEAR(created_at) = ?
                GROUP BY MONTH(created_at)
                ORDER BY checkins DESC
                LIMIT 1
            ");
            $stmt->execute([$userId, $year]);
            $highlights['most_active_month'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // Favorite style
            $stmt = $this->conn->prepare("
                SELECT
                    bs.name,
                    COUNT(bc.id) as count
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE bc.user_id = ? AND YEAR(bc.created_at) = ?
                GROUP BY bs.id, bs.name
                ORDER BY count DESC
                LIMIT 1
            ");
            $stmt->execute([$userId, $year]);
            $highlights['favorite_style'] = $stmt->fetch(PDO::FETCH_ASSOC);

            return $highlights;

        } catch (Exception $e) {
            error_log("Year highlights error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get year journey (monthly breakdown)
     */
    public function getYearJourney($userId, $year) {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    MONTH(created_at) as month,
                    COUNT(*) as checkins,
                    COUNT(DISTINCT beer_id) as unique_beers,
                    AVG(rating) as avg_rating
                FROM beer_checkins
                WHERE user_id = ? AND YEAR(created_at) = ?
                GROUP BY MONTH(created_at)
                ORDER BY month
            ");
            $stmt->execute([$userId, $year]);
            $monthlyData = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to associative array with month as key
            $journey = [];
            foreach ($monthlyData as $data) {
                $journey[$data['month']] = $data;
            }

            return $journey;

        } catch (Exception $e) {
            error_log("Year journey error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get interval string from timeframe
     */
    private function getIntervalFromTimeframe($timeframe) {
        switch ($timeframe) {
            case '1_month':
                return '1 MONTH';
            case '3_months':
                return '3 MONTH';
            case '6_months':
                return '6 MONTH';
            case '1_year':
                return '1 YEAR';
            case 'all_time':
            default:
                return '10 YEAR'; // Large interval for "all time"
        }
    }

    /**
     * Get time condition for SQL queries
     */
    private function getTimeCondition($timeframe, $dateColumn = 'created_at') {
        switch ($timeframe) {
            case '1_month':
                return "AND $dateColumn >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
            case '3_months':
                return "AND $dateColumn >= DATE_SUB(NOW(), INTERVAL 3 MONTH)";
            case '6_months':
                return "AND $dateColumn >= DATE_SUB(NOW(), INTERVAL 6 MONTH)";
            case '1_year':
                return "AND $dateColumn >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            case 'all_time':
            default:
                return "";
        }
    }
}
?>
