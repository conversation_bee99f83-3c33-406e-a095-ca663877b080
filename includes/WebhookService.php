<?php
/**
 * Webhook Service
 * Phase 10: Advanced Features & API Development
 * Manage outgoing webhooks for third-party integrations
 */

class WebhookService {
    private $conn;
    private $maxRetries = 3;
    private $retryDelays = [60, 300, 900]; // 1min, 5min, 15min
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Register a webhook endpoint
     */
    public function registerWebhook($userId, $url, $events, $secret = null, $isActive = true) {
        try {
            if (!$secret) {
                $secret = bin2hex(random_bytes(32));
            }
            
            $stmt = $this->conn->prepare("
                INSERT INTO webhooks (user_id, url, events, secret, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $userId,
                $url,
                json_encode($events),
                $secret,
                $isActive ? 1 : 0
            ]);
            
            $webhookId = $this->conn->lastInsertId();
            
            return [
                'success' => true,
                'webhook_id' => $webhookId,
                'secret' => $secret,
                'url' => $url,
                'events' => $events
            ];
            
        } catch (Exception $e) {
            error_log("Register webhook error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to register webhook'];
        }
    }
    
    /**
     * Trigger webhook for specific event
     */
    public function triggerWebhook($eventType, $data, $userId = null) {
        try {
            // Get all webhooks that should receive this event
            $webhooks = $this->getWebhooksForEvent($eventType, $userId);
            
            foreach ($webhooks as $webhook) {
                $this->queueWebhookDelivery($webhook['id'], $eventType, $data);
            }
            
            return ['success' => true, 'webhooks_triggered' => count($webhooks)];
            
        } catch (Exception $e) {
            error_log("Trigger webhook error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to trigger webhooks'];
        }
    }
    
    /**
     * Get webhooks that should receive a specific event
     */
    private function getWebhooksForEvent($eventType, $userId = null) {
        try {
            $sql = "
                SELECT * FROM webhooks
                WHERE is_active = 1 
                AND JSON_CONTAINS(events, ?)
            ";
            $params = ['"' . $eventType . '"'];
            
            if ($userId) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get webhooks for event error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Queue webhook delivery
     */
    private function queueWebhookDelivery($webhookId, $eventType, $data) {
        try {
            $payload = [
                'event' => $eventType,
                'data' => $data,
                'timestamp' => time(),
                'id' => uniqid('wh_', true)
            ];
            
            $stmt = $this->conn->prepare("
                INSERT INTO webhook_deliveries (webhook_id, event_type, payload, status, created_at)
                VALUES (?, ?, ?, 'pending', NOW())
            ");
            
            $stmt->execute([
                $webhookId,
                $eventType,
                json_encode($payload)
            ]);
            
            return $this->conn->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Queue webhook delivery error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Process pending webhook deliveries
     */
    public function processPendingWebhooks($limit = 50) {
        try {
            $stmt = $this->conn->prepare("
                SELECT wd.*, w.url, w.secret
                FROM webhook_deliveries wd
                JOIN webhooks w ON wd.webhook_id = w.id
                WHERE wd.status = 'pending' AND w.is_active = 1
                ORDER BY wd.created_at ASC
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            $deliveries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $processed = 0;
            $failed = 0;
            
            foreach ($deliveries as $delivery) {
                $result = $this->deliverWebhook($delivery);
                if ($result['success']) {
                    $processed++;
                } else {
                    $failed++;
                }
            }
            
            return [
                'success' => true,
                'processed' => $processed,
                'failed' => $failed,
                'total' => count($deliveries)
            ];
            
        } catch (Exception $e) {
            error_log("Process pending webhooks error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to process webhooks'];
        }
    }
    
    /**
     * Deliver individual webhook
     */
    private function deliverWebhook($delivery) {
        try {
            $payload = $delivery['payload'];
            $signature = $this->generateSignature($payload, $delivery['secret']);
            
            $headers = [
                'Content-Type: application/json',
                'X-Beersty-Signature: ' . $signature,
                'X-Beersty-Event: ' . $delivery['event_type'],
                'User-Agent: Beersty-Webhooks/1.0'
            ];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $delivery['url'],
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_USERAGENT => 'Beersty-Webhooks/1.0'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception("cURL error: " . $error);
            }
            
            if ($httpCode >= 200 && $httpCode < 300) {
                // Success
                $this->updateDeliveryStatus($delivery['id'], 'delivered', $httpCode, $response);
                return ['success' => true];
            } else {
                // HTTP error
                $this->handleDeliveryFailure($delivery, $httpCode, $response);
                return ['success' => false, 'http_code' => $httpCode];
            }
            
        } catch (Exception $e) {
            error_log("Deliver webhook error: " . $e->getMessage());
            $this->handleDeliveryFailure($delivery, 0, $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Generate webhook signature
     */
    private function generateSignature($payload, $secret) {
        return 'sha256=' . hash_hmac('sha256', $payload, $secret);
    }
    
    /**
     * Update delivery status
     */
    private function updateDeliveryStatus($deliveryId, $status, $httpCode = null, $response = null) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE webhook_deliveries
                SET status = ?, http_code = ?, response = ?, delivered_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([$status, $httpCode, $response, $deliveryId]);
            
        } catch (Exception $e) {
            error_log("Update delivery status error: " . $e->getMessage());
        }
    }
    
    /**
     * Handle delivery failure
     */
    private function handleDeliveryFailure($delivery, $httpCode, $response) {
        try {
            $retryCount = $delivery['retry_count'] ?? 0;
            
            if ($retryCount < $this->maxRetries) {
                // Schedule retry
                $nextRetry = date('Y-m-d H:i:s', time() + $this->retryDelays[$retryCount]);
                
                $stmt = $this->conn->prepare("
                    UPDATE webhook_deliveries
                    SET status = 'retry', retry_count = retry_count + 1, 
                        next_retry_at = ?, http_code = ?, response = ?
                    WHERE id = ?
                ");
                
                $stmt->execute([$nextRetry, $httpCode, $response, $delivery['id']]);
            } else {
                // Max retries reached, mark as failed
                $this->updateDeliveryStatus($delivery['id'], 'failed', $httpCode, $response);
                
                // Optionally disable webhook after too many failures
                $this->checkWebhookHealth($delivery['webhook_id']);
            }
            
        } catch (Exception $e) {
            error_log("Handle delivery failure error: " . $e->getMessage());
        }
    }
    
    /**
     * Check webhook health and disable if too many failures
     */
    private function checkWebhookHealth($webhookId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_deliveries,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_deliveries
                FROM webhook_deliveries
                WHERE webhook_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            
            $stmt->execute([$webhookId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($stats['total_deliveries'] >= 10 && 
                ($stats['failed_deliveries'] / $stats['total_deliveries']) > 0.8) {
                
                // Disable webhook due to high failure rate
                $stmt = $this->conn->prepare("
                    UPDATE webhooks SET is_active = 0, disabled_reason = 'High failure rate'
                    WHERE id = ?
                ");
                $stmt->execute([$webhookId]);
                
                error_log("Webhook $webhookId disabled due to high failure rate");
            }
            
        } catch (Exception $e) {
            error_log("Check webhook health error: " . $e->getMessage());
        }
    }
    
    /**
     * Get webhook statistics
     */
    public function getWebhookStats($webhookId, $timeframe = '24h') {
        try {
            $interval = match($timeframe) {
                '1h' => 'INTERVAL 1 HOUR',
                '24h' => 'INTERVAL 24 HOUR',
                '7d' => 'INTERVAL 7 DAY',
                '30d' => 'INTERVAL 30 DAY',
                default => 'INTERVAL 24 HOUR'
            };
            
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_deliveries,
                    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as successful_deliveries,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_deliveries,
                    COUNT(CASE WHEN status = 'retry' THEN 1 END) as retry_deliveries,
                    AVG(CASE WHEN delivered_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, created_at, delivered_at) END) as avg_delivery_time
                FROM webhook_deliveries
                WHERE webhook_id = ? AND created_at > DATE_SUB(NOW(), $interval)
            ");
            
            $stmt->execute([$webhookId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stats['success_rate'] = $stats['total_deliveries'] > 0 
                ? ($stats['successful_deliveries'] / $stats['total_deliveries']) * 100 
                : 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Get webhook stats error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get user webhooks
     */
    public function getUserWebhooks($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT w.*, 
                       COUNT(wd.id) as total_deliveries,
                       COUNT(CASE WHEN wd.status = 'delivered' THEN 1 END) as successful_deliveries
                FROM webhooks w
                LEFT JOIN webhook_deliveries wd ON w.id = wd.webhook_id 
                    AND wd.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
                WHERE w.user_id = ?
                GROUP BY w.id
                ORDER BY w.created_at DESC
            ");
            
            $stmt->execute([$userId]);
            $webhooks = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($webhooks as &$webhook) {
                $webhook['events'] = json_decode($webhook['events'], true);
                $webhook['success_rate'] = $webhook['total_deliveries'] > 0 
                    ? ($webhook['successful_deliveries'] / $webhook['total_deliveries']) * 100 
                    : 0;
            }
            
            return $webhooks;
            
        } catch (Exception $e) {
            error_log("Get user webhooks error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete webhook
     */
    public function deleteWebhook($webhookId, $userId) {
        try {
            $stmt = $this->conn->prepare("
                DELETE FROM webhooks WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([$webhookId, $userId]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("Delete webhook error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to delete webhook'];
        }
    }
    
    /**
     * Test webhook endpoint
     */
    public function testWebhook($webhookId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM webhooks WHERE id = ?
            ");
            
            $stmt->execute([$webhookId]);
            $webhook = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$webhook) {
                return ['success' => false, 'error' => 'Webhook not found'];
            }
            
            // Send test payload
            $testData = [
                'test' => true,
                'webhook_id' => $webhookId,
                'message' => 'This is a test webhook delivery from Beersty'
            ];
            
            $deliveryId = $this->queueWebhookDelivery($webhookId, 'webhook.test', $testData);
            
            if ($deliveryId) {
                // Process immediately for testing
                $delivery = [
                    'id' => $deliveryId,
                    'webhook_id' => $webhookId,
                    'event_type' => 'webhook.test',
                    'payload' => json_encode([
                        'event' => 'webhook.test',
                        'data' => $testData,
                        'timestamp' => time(),
                        'id' => uniqid('wh_test_', true)
                    ]),
                    'url' => $webhook['url'],
                    'secret' => $webhook['secret']
                ];
                
                return $this->deliverWebhook($delivery);
            }
            
            return ['success' => false, 'error' => 'Failed to queue test delivery'];
            
        } catch (Exception $e) {
            error_log("Test webhook error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to test webhook'];
        }
    }
}
