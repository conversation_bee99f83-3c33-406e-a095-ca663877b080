<?php
/**
 * Digital Board API Service
 * Phase 4 11.0 - API Development
 * 
 * Centralized API service for digital board system with enhanced schema support
 */

class DigitalBoardAPI {
    private $conn;
    private $user;
    
    public function __construct($connection, $user = null) {
        $this->conn = $connection;
        $this->user = $user;
    }
    
    /**
     * Validate user permissions for brewery access
     */
    public function validateBreweryAccess($breweryId, $action = 'read') {
        if (!$this->user) {
            return ['success' => false, 'error' => 'Authentication required', 'code' => 401];
        }
        
        // Admin has access to all breweries
        if ($this->user['role'] === 'admin') {
            return ['success' => true];
        }
        
        // Business owners/managers can only access their own brewery
        if ($this->user['brewery_id'] !== $breweryId) {
            return ['success' => false, 'error' => 'Access denied to this brewery', 'code' => 403];
        }
        
        // Check specific action permissions
        $allowedActions = ['read', 'write', 'delete'];
        if (!in_array($action, $allowedActions)) {
            return ['success' => false, 'error' => 'Invalid action', 'code' => 400];
        }
        
        return ['success' => true];
    }
    
    /**
     * Validate and sanitize input data
     */
    public function validateInput($data, $rules) {
        $errors = [];
        $sanitized = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            
            // Check required fields
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[] = "Field '$field' is required";
                continue;
            }
            
            // Skip validation if field is not required and empty
            if (empty($value) && !isset($rule['required'])) {
                $sanitized[$field] = null;
                continue;
            }
            
            // Type validation
            switch ($rule['type']) {
                case 'string':
                    $sanitized[$field] = filter_var($value, FILTER_SANITIZE_STRING);
                    if (isset($rule['max_length']) && strlen($sanitized[$field]) > $rule['max_length']) {
                        $errors[] = "Field '$field' exceeds maximum length of {$rule['max_length']}";
                    }
                    break;
                    
                case 'int':
                    $sanitized[$field] = filter_var($value, FILTER_VALIDATE_INT);
                    if ($sanitized[$field] === false) {
                        $errors[] = "Field '$field' must be a valid integer";
                    }
                    break;
                    
                case 'float':
                    $sanitized[$field] = filter_var($value, FILTER_VALIDATE_FLOAT);
                    if ($sanitized[$field] === false) {
                        $errors[] = "Field '$field' must be a valid number";
                    }
                    break;
                    
                case 'boolean':
                    $sanitized[$field] = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                    if ($sanitized[$field] === null) {
                        $errors[] = "Field '$field' must be a valid boolean";
                    }
                    break;
                    
                case 'email':
                    $sanitized[$field] = filter_var($value, FILTER_VALIDATE_EMAIL);
                    if ($sanitized[$field] === false) {
                        $errors[] = "Field '$field' must be a valid email address";
                    }
                    break;
                    
                case 'json':
                    if (is_array($value)) {
                        $sanitized[$field] = $value;
                    } else {
                        $decoded = json_decode($value, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $errors[] = "Field '$field' must be valid JSON";
                        } else {
                            $sanitized[$field] = $decoded;
                        }
                    }
                    break;
                    
                case 'enum':
                    if (!in_array($value, $rule['values'])) {
                        $errors[] = "Field '$field' must be one of: " . implode(', ', $rule['values']);
                    } else {
                        $sanitized[$field] = $value;
                    }
                    break;
                    
                default:
                    $sanitized[$field] = $value;
            }
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        return ['success' => true, 'data' => $sanitized];
    }
    
    /**
     * Format API response
     */
    public function formatResponse($data, $success = true, $message = null, $code = 200) {
        http_response_code($code);
        
        $response = [
            'success' => $success,
            'timestamp' => date('c'),
            'data' => $data
        ];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if (!$success && isset($data['errors'])) {
            $response['errors'] = $data['errors'];
            unset($response['data']['errors']);
        }
        
        return json_encode($response, JSON_PRETTY_PRINT);
    }
    
    /**
     * Handle database errors
     */
    public function handleDatabaseError($e, $operation = 'database operation') {
        error_log("Digital Board API Error ($operation): " . $e->getMessage());
        
        // Don't expose internal database errors to API consumers
        return $this->formatResponse(
            ['error' => 'Internal server error'],
            false,
            "Failed to perform $operation",
            500
        );
    }
    
    /**
     * Generate unique ID
     */
    public function generateId($prefix = '') {
        return $prefix . bin2hex(random_bytes(16));
    }
    
    /**
     * Check rate limiting
     */
    public function checkRateLimit($action, $limit = 100, $window = 3600) {
        if (!$this->user) {
            return false;
        }
        
        $key = "api_rate_limit_{$action}_{$this->user['id']}";
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $window];
        }
        
        $rate_data = $_SESSION[$key];
        
        // Reset if window has passed
        if (time() > $rate_data['reset_time']) {
            $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $window];
            $rate_data = $_SESSION[$key];
        }
        
        // Check if limit exceeded
        if ($rate_data['count'] >= $limit) {
            return false;
        }
        
        // Increment counter
        $_SESSION[$key]['count']++;
        
        return true;
    }
    
    /**
     * Log API activity
     */
    public function logActivity($action, $resource_type, $resource_id = null, $details = null) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO api_activity_log (user_id, action, resource_type, resource_id, details, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $this->user['id'] ?? null,
                $action,
                $resource_type,
                $resource_id,
                $details ? json_encode($details) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the API call
            error_log("Failed to log API activity: " . $e->getMessage());
        }
    }
    
    /**
     * Get paginated results
     */
    public function paginate($query, $params = [], $page = 1, $limit = 20) {
        $page = max(1, (int)$page);
        $limit = min(100, max(1, (int)$limit)); // Max 100 items per page
        $offset = ($page - 1) * $limit;
        
        // Get total count
        $countQuery = "SELECT COUNT(*) FROM ($query) as count_query";
        $countStmt = $this->conn->prepare($countQuery);
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // Get paginated results
        $paginatedQuery = $query . " LIMIT $limit OFFSET $offset";
        $stmt = $this->conn->prepare($paginatedQuery);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'data' => $results,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ];
    }
}
