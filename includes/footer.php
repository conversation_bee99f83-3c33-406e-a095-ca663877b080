    </main>

    <!-- Mobile Bottom Navigation (Phase 9) -->
    <?php include __DIR__ . '/../components/mobile-bottom-nav.php'; ?>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-beer me-2"></i><?php echo APP_NAME; ?></h5>
                    <p class="mb-0">Complete brewery management platform for connecting breweries with customers.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.</small>
                    </p>
                    <div class="mt-2">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/assets/js/main.js"></script>

    <!-- Mobile & PWA JS (Phase 9) -->
    <script src="/assets/js/mobile.js"></script>
    <!-- <script src="/assets/js/pwa.js"></script> <!-- Temporarily disabled for debugging -->

    <!-- Notification System JS (Phase 7) -->
    <?php if (isLoggedIn() && in_array(getCurrentUser()['role'], ['beer_enthusiast', 'beer_expert', 'customer'])): ?>
    <script>
        // Notification system for header
        let notificationPollingInterval = null;

        // Initialize notification system
        document.addEventListener('DOMContentLoaded', function() {
            loadHeaderNotifications();
            startNotificationPolling();

            // Load notifications when dropdown is opened
            const notificationDropdown = document.getElementById('notificationsDropdown');
            if (notificationDropdown) {
                notificationDropdown.addEventListener('click', function() {
                    loadHeaderNotifications();
                });
            }
        });

        // Load notifications for header dropdown
        async function loadHeaderNotifications() {
            try {
                const response = await fetch('/api/notifications.php?action=unread&limit=5');
                const data = await response.json();

                if (data.success) {
                    updateNotificationBadge(data.unread_count);
                    displayHeaderNotifications(data.notifications);
                }
            } catch (error) {
                console.error('Error loading header notifications:', error);
            }
        }

        // Update notification badge
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-badge');
            if (badge) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline' : 'none';
            }
        }

        // Display notifications in header dropdown
        function displayHeaderNotifications(notifications) {
            const container = document.getElementById('notification-dropdown-list');
            if (!container) return;

            if (notifications.length === 0) {
                container.innerHTML = `
                    <li class="text-center py-3">
                        <i class="fas fa-bell-slash text-muted"></i>
                        <p class="mb-0 text-muted small">No new notifications</p>
                    </li>
                `;
                return;
            }

            container.innerHTML = notifications.map(notification => {
                const timeAgo = formatTimeAgo(notification.created_at);
                const iconClass = getNotificationIcon(notification.type);

                return `
                    <li>
                        <a class="dropdown-item py-2" href="/user/notifications.php"
                           onclick="markNotificationAsRead('${notification.id}')">
                            <div class="d-flex align-items-start">
                                <div class="me-2">
                                    <i class="${iconClass} text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold small">${escapeHtml(notification.title)}</div>
                                    <div class="text-muted small">${escapeHtml(notification.message.substring(0, 60))}${notification.message.length > 60 ? '...' : ''}</div>
                                    <div class="text-muted" style="font-size: 0.75rem;">${timeAgo}</div>
                                </div>
                            </div>
                        </a>
                    </li>
                `;
            }).join('');
        }

        // Mark notification as read
        async function markNotificationAsRead(notificationId) {
            try {
                await fetch('/api/notifications.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'mark_read',
                        notification_id: notificationId
                    })
                });
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        }

        // Start notification polling
        function startNotificationPolling() {
            notificationPollingInterval = setInterval(() => {
                loadHeaderNotifications();
            }, 30000); // Poll every 30 seconds
        }

        // Get notification icon
        function getNotificationIcon(type) {
            const icons = {
                'new_follower': 'fas fa-user-plus',
                'friend_checkin': 'fas fa-map-marker-alt',
                'beer_release': 'fas fa-beer',
                'brewery_event': 'fas fa-calendar-alt',
                'achievement_unlocked': 'fas fa-trophy',
                'message_received': 'fas fa-envelope',
                'rating_liked': 'fas fa-heart',
                'comment_received': 'fas fa-comment',
                'badge_earned': 'fas fa-medal'
            };
            return icons[type] || 'fas fa-bell';
        }

        // Format time ago
        function formatTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + 'm ago';
            if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + 'h ago';
            if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + 'd ago';

            return date.toLocaleDateString();
        }

        // Escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (notificationPollingInterval) {
                clearInterval(notificationPollingInterval);
            }
        });
    </script>
    <?php endif; ?>
    
    <!-- Additional JS -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Page-specific JS -->
    <?php if (isset($pageJS)): ?>
        <script>
            <?php echo $pageJS; ?>
        </script>
    <?php endif; ?>
</body>
</html>
