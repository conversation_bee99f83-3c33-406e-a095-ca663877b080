<?php
/**
 * NotificationService - Handles all notification functionality
 * Phase 7: Notifications & Communication
 */

class NotificationService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Create a new notification
     */
    public function createNotification($userId, $type, $title, $message, $data = null, $relatedType = null, $relatedId = null) {
        try {
            // Get user's notification preferences
            $preferences = $this->getUserNotificationPreferences($userId);
            
            // Check if user wants this type of notification
            if (!$this->shouldSendNotification($type, $preferences)) {
                return false;
            }
            
            // Determine delivery methods
            $sendEmail = $this->shouldSendEmail($type, $preferences);
            $sendPush = $this->shouldSendPush($type, $preferences);
            
            // Insert notification
            $stmt = $this->conn->prepare("
                INSERT INTO notifications (user_id, type, title, message, data, related_type, related_id, send_email, send_push)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $type,
                $title,
                $message,
                $data ? json_encode($data) : null,
                $relatedType,
                $relatedId,
                $sendEmail,
                $sendPush
            ]);
            
            $notificationId = $this->conn->lastInsertId();
            
            // Send email if required
            if ($sendEmail) {
                $this->sendEmailNotification($notificationId);
            }
            
            // TODO: Send push notification if required
            if ($sendPush) {
                $this->sendPushNotification($notificationId);
            }
            
            return $notificationId;
            
        } catch (Exception $e) {
            error_log("NotificationService error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's notification preferences
     */
    public function getUserNotificationPreferences($userId) {
        $stmt = $this->conn->prepare("
            SELECT * FROM notification_preferences WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $preferences = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Create default preferences if none exist
        if (!$preferences) {
            $this->createDefaultNotificationPreferences($userId);
            return $this->getUserNotificationPreferences($userId);
        }
        
        return $preferences;
    }
    
    /**
     * Create default notification preferences for a user
     */
    public function createDefaultNotificationPreferences($userId) {
        $stmt = $this->conn->prepare("
            INSERT INTO notification_preferences (user_id) VALUES (?)
        ");
        $stmt->execute([$userId]);
    }
    
    /**
     * Check if notification should be sent based on preferences
     */
    private function shouldSendNotification($type, $preferences) {
        // Always send achievement notifications
        if ($type === 'achievement_unlocked' || $type === 'badge_earned') {
            return true;
        }
        
        // Check specific preferences
        $emailField = 'email_' . $type;
        $pushField = 'push_' . $type;
        
        return (isset($preferences[$emailField]) && $preferences[$emailField]) ||
               (isset($preferences[$pushField]) && $preferences[$pushField]);
    }
    
    /**
     * Check if email should be sent for this notification type
     */
    private function shouldSendEmail($type, $preferences) {
        $emailField = 'email_' . $type;
        return isset($preferences[$emailField]) && $preferences[$emailField];
    }
    
    /**
     * Check if push notification should be sent
     */
    private function shouldSendPush($type, $preferences) {
        $pushField = 'push_' . $type;
        return isset($preferences[$pushField]) && $preferences[$pushField];
    }
    
    /**
     * Send email notification
     */
    private function sendEmailNotification($notificationId) {
        try {
            // Get notification details
            $stmt = $this->conn->prepare("
                SELECT n.*, u.email, u.id as user_id, p.first_name, p.last_name
                FROM notifications n
                JOIN users u ON n.user_id = u.id
                JOIN profiles p ON u.id = p.id
                WHERE n.id = ?
            ");
            $stmt->execute([$notificationId]);
            $notification = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$notification) {
                return false;
            }
            
            // Prepare email content
            $to = $notification['email'];
            $subject = 'Beersty - ' . $notification['title'];
            $message = $this->buildEmailMessage($notification);
            
            // Send email using PHP mail() function
            // In production, consider using a proper email service like SendGrid, Mailgun, etc.
            $headers = [
                'From: Beersty <<EMAIL>>',
                'Reply-To: <EMAIL>',
                'Content-Type: text/html; charset=UTF-8',
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $success = mail($to, $subject, $message, implode("\r\n", $headers));
            
            if ($success) {
                // Mark as sent
                $stmt = $this->conn->prepare("
                    UPDATE notifications 
                    SET is_sent = TRUE, sent_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$notificationId]);
            }
            
            return $success;
            
        } catch (Exception $e) {
            error_log("Email notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Build HTML email message
     */
    private function buildEmailMessage($notification) {
        $userName = $notification['first_name'] . ' ' . $notification['last_name'];
        
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . htmlspecialchars($notification['title']) . '</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f8b500; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background: #f8b500; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🍺 Beersty</h1>
                </div>
                <div class="content">
                    <h2>Hi ' . htmlspecialchars($userName) . '!</h2>
                    <p>' . nl2br(htmlspecialchars($notification['message'])) . '</p>
                    <p><a href="http://localhost/beersty/" class="button">Visit Beersty</a></p>
                </div>
                <div class="footer">
                    <p>You received this email because you have notifications enabled in your Beersty account.</p>
                    <p><a href="http://localhost/beersty/user/preferences.php">Manage your notification preferences</a></p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html;
    }
    
    /**
     * Send push notification (placeholder for future implementation)
     */
    private function sendPushNotification($notificationId) {
        // TODO: Implement push notification service
        // This could integrate with services like Firebase Cloud Messaging, OneSignal, etc.
        return true;
    }
    
    /**
     * Get user's unread notifications
     */
    public function getUnreadNotifications($userId, $limit = 10) {
        $stmt = $this->conn->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? AND is_read = FALSE 
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all notifications for a user
     */
    public function getUserNotifications($userId, $limit = 50, $offset = 0) {
        $stmt = $this->conn->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId) {
        $stmt = $this->conn->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$notificationId, $userId]);
    }
    
    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead($userId) {
        $stmt = $this->conn->prepare("
            UPDATE notifications 
            SET is_read = TRUE 
            WHERE user_id = ? AND is_read = FALSE
        ");
        return $stmt->execute([$userId]);
    }
    
    /**
     * Get unread notification count
     */
    public function getUnreadCount($userId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) as count 
            FROM notifications 
            WHERE user_id = ? AND is_read = FALSE
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    }
}
?>
