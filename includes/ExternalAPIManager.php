<?php
/**
 * External API Manager
 * Phase 7 - Advanced Features
 * 
 * Integration with external APIs for weather, events, POS systems, etc.
 */

class ExternalAPIManager {
    private $conn;
    private $config;
    
    public function __construct($connection, $config = []) {
        $this->conn = $connection;
        $this->config = array_merge([
            'weather_api_key' => '',
            'eventbrite_api_key' => '',
            'square_access_token' => '',
            'untappd_client_id' => '',
            'untappd_client_secret' => '',
            'cache_duration' => 1800, // 30 minutes
        ], $config);
    }
    
    /**
     * Get weather information for brewery location
     */
    public function getWeatherData($latitude, $longitude) {
        try {
            $cacheKey = "weather_{$latitude}_{$longitude}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            $apiKey = $this->config['weather_api_key'];
            if (empty($apiKey)) {
                return $this->getMockWeatherData();
            }
            
            // OpenWeatherMap API
            $url = "https://api.openweathermap.org/data/2.5/weather?lat={$latitude}&lon={$longitude}&appid={$apiKey}&units=imperial";
            
            $response = $this->makeAPIRequest($url);
            if (!$response) {
                return $this->getMockWeatherData();
            }
            
            $weatherData = [
                'temperature' => round($response['main']['temp']),
                'feels_like' => round($response['main']['feels_like']),
                'humidity' => $response['main']['humidity'],
                'description' => ucfirst($response['weather'][0]['description']),
                'icon' => $response['weather'][0]['icon'],
                'wind_speed' => round($response['wind']['speed']),
                'visibility' => round($response['visibility'] / 1000, 1), // Convert to miles
                'pressure' => $response['main']['pressure'],
                'city' => $response['name'],
                'country' => $response['sys']['country'],
                'sunrise' => date('g:i A', $response['sys']['sunrise']),
                'sunset' => date('g:i A', $response['sys']['sunset']),
                'updated_at' => date('c')
            ];
            
            // Get forecast data
            $forecastUrl = "https://api.openweathermap.org/data/2.5/forecast?lat={$latitude}&lon={$longitude}&appid={$apiKey}&units=imperial&cnt=8";
            $forecastResponse = $this->makeAPIRequest($forecastUrl);
            
            if ($forecastResponse && isset($forecastResponse['list'])) {
                $weatherData['forecast'] = [];
                foreach (array_slice($forecastResponse['list'], 0, 5) as $forecast) {
                    $weatherData['forecast'][] = [
                        'time' => date('g A', $forecast['dt']),
                        'temperature' => round($forecast['main']['temp']),
                        'description' => ucfirst($forecast['weather'][0]['description']),
                        'icon' => $forecast['weather'][0]['icon']
                    ];
                }
            }
            
            $this->saveToCache($cacheKey, $weatherData);
            return $weatherData;
            
        } catch (Exception $e) {
            error_log("Weather API error: " . $e->getMessage());
            return $this->getMockWeatherData();
        }
    }
    
    /**
     * Get local events from Eventbrite
     */
    public function getLocalEvents($latitude, $longitude, $radius = 10) {
        try {
            $cacheKey = "events_{$latitude}_{$longitude}_{$radius}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            $apiKey = $this->config['eventbrite_api_key'];
            if (empty($apiKey)) {
                return $this->getMockEventsData();
            }
            
            // Eventbrite API
            $url = "https://www.eventbriteapi.com/v3/events/search/?location.latitude={$latitude}&location.longitude={$longitude}&location.within={$radius}km&categories=103,110&sort_by=date&token={$apiKey}";
            
            $response = $this->makeAPIRequest($url);
            if (!$response || !isset($response['events'])) {
                return $this->getMockEventsData();
            }
            
            $events = [];
            foreach (array_slice($response['events'], 0, 10) as $event) {
                $events[] = [
                    'id' => $event['id'],
                    'name' => $event['name']['text'],
                    'description' => substr(strip_tags($event['description']['text'] ?? ''), 0, 200) . '...',
                    'start_time' => $event['start']['local'],
                    'end_time' => $event['end']['local'],
                    'url' => $event['url'],
                    'venue' => $event['venue']['name'] ?? 'TBA',
                    'address' => $event['venue']['address']['localized_area_display'] ?? '',
                    'is_free' => $event['is_free'],
                    'logo_url' => $event['logo']['url'] ?? '',
                    'category' => 'Event'
                ];
            }
            
            $this->saveToCache($cacheKey, $events);
            return $events;
            
        } catch (Exception $e) {
            error_log("Events API error: " . $e->getMessage());
            return $this->getMockEventsData();
        }
    }
    
    /**
     * Get beer data from Untappd
     */
    public function getUntappdBeerData($beerId) {
        try {
            $cacheKey = "untappd_beer_{$beerId}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            $clientId = $this->config['untappd_client_id'];
            $clientSecret = $this->config['untappd_client_secret'];
            
            if (empty($clientId) || empty($clientSecret)) {
                return $this->getMockUntappdData($beerId);
            }
            
            // Untappd API
            $url = "https://api.untappd.com/v4/beer/info/{$beerId}?client_id={$clientId}&client_secret={$clientSecret}";
            
            $response = $this->makeAPIRequest($url);
            if (!$response || !isset($response['response']['beer'])) {
                return $this->getMockUntappdData($beerId);
            }
            
            $beer = $response['response']['beer'];
            $beerData = [
                'id' => $beer['bid'],
                'name' => $beer['beer_name'],
                'style' => $beer['beer_style'],
                'abv' => $beer['beer_abv'],
                'ibu' => $beer['beer_ibu'],
                'description' => $beer['beer_description'],
                'label_url' => $beer['beer_label'],
                'brewery' => $beer['brewery']['brewery_name'],
                'rating' => $beer['rating_score'],
                'rating_count' => $beer['rating_count'],
                'checkin_count' => $beer['stats']['total_count'],
                'updated_at' => date('c')
            ];
            
            $this->saveToCache($cacheKey, $beerData);
            return $beerData;
            
        } catch (Exception $e) {
            error_log("Untappd API error: " . $e->getMessage());
            return $this->getMockUntappdData($beerId);
        }
    }
    
    /**
     * Get POS system data (Square integration)
     */
    public function getPOSData($locationId) {
        try {
            $cacheKey = "pos_data_{$locationId}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            $accessToken = $this->config['square_access_token'];
            if (empty($accessToken)) {
                return $this->getMockPOSData();
            }
            
            // Square API - Get inventory
            $url = "https://connect.squareup.com/v2/inventory/counts/batch-retrieve";
            $headers = [
                'Authorization: Bearer ' . $accessToken,
                'Content-Type: application/json',
                'Square-Version: 2023-10-18'
            ];
            
            $postData = json_encode([
                'location_ids' => [$locationId],
                'updated_after' => date('c', strtotime('-1 hour'))
            ]);
            
            $response = $this->makeAPIRequest($url, $headers, $postData);
            if (!$response || !isset($response['counts'])) {
                return $this->getMockPOSData();
            }
            
            $inventory = [];
            foreach ($response['counts'] as $item) {
                $inventory[] = [
                    'catalog_object_id' => $item['catalog_object_id'],
                    'quantity' => $item['quantity'],
                    'location_id' => $item['location_id'],
                    'calculated_at' => $item['calculated_at']
                ];
            }
            
            $posData = [
                'inventory' => $inventory,
                'location_id' => $locationId,
                'updated_at' => date('c')
            ];
            
            $this->saveToCache($cacheKey, $posData);
            return $posData;
            
        } catch (Exception $e) {
            error_log("POS API error: " . $e->getMessage());
            return $this->getMockPOSData();
        }
    }
    
    /**
     * Get news and updates from RSS feeds
     */
    public function getRSSFeed($feedUrl, $limit = 5) {
        try {
            $cacheKey = "rss_" . md5($feedUrl) . "_{$limit}";
            $cached = $this->getFromCache($cacheKey);
            if ($cached) {
                return $cached;
            }
            
            $rss = simplexml_load_file($feedUrl);
            if (!$rss) {
                return $this->getMockRSSData();
            }
            
            $items = [];
            $count = 0;
            foreach ($rss->channel->item as $item) {
                if ($count >= $limit) break;
                
                $items[] = [
                    'title' => (string)$item->title,
                    'description' => substr(strip_tags((string)$item->description), 0, 200) . '...',
                    'link' => (string)$item->link,
                    'pub_date' => (string)$item->pubDate,
                    'category' => (string)$item->category
                ];
                $count++;
            }
            
            $this->saveToCache($cacheKey, $items);
            return $items;
            
        } catch (Exception $e) {
            error_log("RSS feed error: " . $e->getMessage());
            return $this->getMockRSSData();
        }
    }
    
    /**
     * Create dynamic content slide from external data
     */
    public function createDynamicSlide($type, $data, $template = 'default') {
        $slideContent = [
            'type' => 'dynamic_content',
            'content_type' => $type,
            'template' => $template,
            'data' => $data,
            'generated_at' => date('c'),
            'auto_refresh' => true,
            'refresh_interval' => $this->getRefreshInterval($type)
        ];
        
        return $slideContent;
    }
    
    /**
     * Save API configuration for brewery
     */
    public function saveAPIConfig($breweryId, $config) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO brewery_api_config (brewery_id, api_name, api_key, api_secret, endpoint_url, is_active, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                api_key = VALUES(api_key),
                api_secret = VALUES(api_secret),
                endpoint_url = VALUES(endpoint_url),
                is_active = VALUES(is_active),
                updated_at = NOW()
            ");
            
            foreach ($config as $apiName => $settings) {
                $stmt->execute([
                    $breweryId,
                    $apiName,
                    $settings['api_key'] ?? null,
                    $settings['api_secret'] ?? null,
                    $settings['endpoint_url'] ?? null,
                    $settings['is_active'] ?? true
                ]);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Save API config error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Helper methods
     */
    private function makeAPIRequest($url, $headers = [], $postData = null) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        
        if ($postData) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with code: {$httpCode}");
        }
        
        return json_decode($response, true);
    }
    
    private function getFromCache($key) {
        try {
            $stmt = $this->conn->prepare("
                SELECT data FROM api_cache 
                WHERE cache_key = ? AND expires_at > NOW()
            ");
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ? json_decode($result['data'], true) : null;
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    private function saveToCache($key, $data) {
        try {
            $expiresAt = date('Y-m-d H:i:s', time() + $this->config['cache_duration']);
            
            $stmt = $this->conn->prepare("
                INSERT INTO api_cache (cache_key, data, expires_at)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                data = VALUES(data),
                expires_at = VALUES(expires_at)
            ");
            
            $stmt->execute([$key, json_encode($data), $expiresAt]);
            
        } catch (Exception $e) {
            error_log("Cache save error: " . $e->getMessage());
        }
    }
    
    private function getRefreshInterval($type) {
        $intervals = [
            'weather' => 1800,    // 30 minutes
            'events' => 3600,     // 1 hour
            'social' => 300,      // 5 minutes
            'pos' => 600,         // 10 minutes
            'rss' => 1800         // 30 minutes
        ];
        
        return $intervals[$type] ?? 1800;
    }
    
    // Mock data methods for development/fallback
    private function getMockWeatherData() {
        return [
            'temperature' => 72,
            'feels_like' => 75,
            'humidity' => 65,
            'description' => 'Partly cloudy',
            'icon' => '02d',
            'wind_speed' => 8,
            'visibility' => 10,
            'pressure' => 1013,
            'city' => 'Sample City',
            'country' => 'US',
            'sunrise' => '6:30 AM',
            'sunset' => '7:45 PM',
            'forecast' => [
                ['time' => '3 PM', 'temperature' => 74, 'description' => 'Sunny', 'icon' => '01d'],
                ['time' => '6 PM', 'temperature' => 71, 'description' => 'Clear', 'icon' => '01n'],
                ['time' => '9 PM', 'temperature' => 68, 'description' => 'Clear', 'icon' => '01n']
            ],
            'updated_at' => date('c')
        ];
    }
    
    private function getMockEventsData() {
        return [
            [
                'id' => 'mock_event_1',
                'name' => 'Live Music Night',
                'description' => 'Join us for an evening of live acoustic music featuring local artists...',
                'start_time' => date('c', strtotime('+2 days 19:00')),
                'end_time' => date('c', strtotime('+2 days 23:00')),
                'url' => 'https://example.com/events/1',
                'venue' => 'Local Brewery',
                'address' => 'Downtown',
                'is_free' => false,
                'logo_url' => 'https://picsum.photos/300/200?random=1',
                'category' => 'Music'
            ]
        ];
    }
    
    private function getMockUntappdData($beerId) {
        return [
            'id' => $beerId,
            'name' => 'Sample IPA',
            'style' => 'American IPA',
            'abv' => 6.5,
            'ibu' => 65,
            'description' => 'A hoppy American IPA with citrus and pine notes.',
            'label_url' => 'https://picsum.photos/200/300?random=2',
            'brewery' => 'Sample Brewery',
            'rating' => 4.2,
            'rating_count' => 1250,
            'checkin_count' => 5000,
            'updated_at' => date('c')
        ];
    }
    
    private function getMockPOSData() {
        return [
            'inventory' => [
                ['catalog_object_id' => 'beer_1', 'quantity' => '15', 'location_id' => 'loc_1'],
                ['catalog_object_id' => 'beer_2', 'quantity' => '8', 'location_id' => 'loc_1']
            ],
            'location_id' => 'mock_location',
            'updated_at' => date('c')
        ];
    }
    
    private function getMockRSSData() {
        return [
            [
                'title' => 'Craft Beer Industry News',
                'description' => 'Latest trends and developments in the craft beer industry...',
                'link' => 'https://example.com/news/1',
                'pub_date' => date('r'),
                'category' => 'Industry News'
            ]
        ];
    }
}
