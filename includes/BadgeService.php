<?php

class BadgeService {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Check and award badges for a user based on their activity
     */
    public function checkAndAwardBadges($userId, $activityType = null, $activityData = []) {
        $newBadges = [];
        
        try {
            // Get user's current statistics
            $userStats = $this->getUserStatistics($userId);
            
            // Get badges user hasn't earned yet
            $availableBadges = $this->getAvailableBadges($userId);
            
            foreach ($availableBadges as $badge) {
                if ($this->checkBadgeCriteria($badge, $userStats, $activityType, $activityData)) {
                    if ($this->awardBadge($userId, $badge['id'])) {
                        $newBadges[] = $badge;
                    }
                }
            }
            
            // Update user statistics if new badges were earned
            if (!empty($newBadges)) {
                $this->updateUserBadgeCount($userId);
            }
            
        } catch (Exception $e) {
            error_log("Badge check error: " . $e->getMessage());
        }
        
        return $newBadges;
    }
    
    /**
     * Get user statistics for badge checking
     */
    private function getUserStatistics($userId) {
        // Get or create user statistics
        $stmt = $this->conn->prepare("
            SELECT * FROM user_statistics WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $stats = $stmt->fetch();
        
        if (!$stats) {
            $this->calculateUserStatistics($userId);
            $stmt->execute([$userId]);
            $stats = $stmt->fetch();
        }
        
        return $stats ?: [];
    }
    
    /**
     * Calculate and update user statistics
     */
    public function calculateUserStatistics($userId) {
        try {
            $this->conn->beginTransaction();
            
            // Calculate check-in statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_checkins,
                    COUNT(DISTINCT beer_id) as unique_beers,
                    COUNT(DISTINCT brewery_id) as unique_breweries,
                    MIN(DATE(created_at)) as first_checkin,
                    MAX(DATE(created_at)) as last_checkin
                FROM beer_checkins 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $checkinStats = $stmt->fetch();
            
            // Calculate unique styles tried
            $stmt = $this->conn->prepare("
                SELECT COUNT(DISTINCT bs.category) as unique_styles
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                WHERE bc.user_id = ? AND bs.id IS NOT NULL
            ");
            $stmt->execute([$userId]);
            $styleStats = $stmt->fetch();
            
            // Calculate rating statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_ratings,
                    AVG(overall_rating) as avg_rating,
                    MAX(overall_rating) as highest_rating,
                    MIN(overall_rating) as lowest_rating,
                    COUNT(CASE WHEN review_text IS NOT NULL AND review_text != '' THEN 1 END) as review_count
                FROM beer_ratings 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $ratingStats = $stmt->fetch();
            
            // Calculate social statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    (SELECT COUNT(*) FROM user_follows WHERE following_id = ?) as followers,
                    (SELECT COUNT(*) FROM user_follows WHERE follower_id = ?) as following
            ");
            $stmt->execute([$userId, $userId]);
            $socialStats = $stmt->fetch();
            
            // Calculate badge statistics
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_badges,
                    COALESCE(SUM(b.points_value), 0) as total_points
                FROM user_badges ub
                JOIN badges b ON ub.badge_id = b.id
                WHERE ub.user_id = ?
            ");
            $stmt->execute([$userId]);
            $badgeStats = $stmt->fetch();
            
            // Insert or update user statistics
            $stmt = $this->conn->prepare("
                INSERT INTO user_statistics (
                    user_id, total_checkins, unique_beers_tried, unique_breweries_visited,
                    unique_styles_tried, total_ratings, average_rating_given, 
                    highest_rating_given, lowest_rating_given, total_followers,
                    total_following, total_badges_earned, total_points_earned,
                    first_checkin_date, last_checkin_date, last_calculated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                    total_checkins = VALUES(total_checkins),
                    unique_beers_tried = VALUES(unique_beers_tried),
                    unique_breweries_visited = VALUES(unique_breweries_visited),
                    unique_styles_tried = VALUES(unique_styles_tried),
                    total_ratings = VALUES(total_ratings),
                    average_rating_given = VALUES(average_rating_given),
                    highest_rating_given = VALUES(highest_rating_given),
                    lowest_rating_given = VALUES(lowest_rating_given),
                    total_followers = VALUES(total_followers),
                    total_following = VALUES(total_following),
                    total_badges_earned = VALUES(total_badges_earned),
                    total_points_earned = VALUES(total_points_earned),
                    first_checkin_date = VALUES(first_checkin_date),
                    last_checkin_date = VALUES(last_checkin_date),
                    last_calculated_at = VALUES(last_calculated_at)
            ");
            
            $stmt->execute([
                $userId,
                $checkinStats['total_checkins'] ?? 0,
                $checkinStats['unique_beers'] ?? 0,
                $checkinStats['unique_breweries'] ?? 0,
                $styleStats['unique_styles'] ?? 0,
                $ratingStats['total_ratings'] ?? 0,
                $ratingStats['avg_rating'] ?? 0,
                $ratingStats['highest_rating'] ?? 0,
                $ratingStats['lowest_rating'] ?? 5,
                $socialStats['followers'] ?? 0,
                $socialStats['following'] ?? 0,
                $badgeStats['total_badges'] ?? 0,
                $badgeStats['total_points'] ?? 0,
                $checkinStats['first_checkin'],
                $checkinStats['last_checkin']
            ]);
            
            $this->conn->commit();
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Statistics calculation error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get badges that user hasn't earned yet
     */
    private function getAvailableBadges($userId) {
        $stmt = $this->conn->prepare("
            SELECT b.* FROM badges b
            WHERE b.is_active = 1 
            AND b.id NOT IN (
                SELECT badge_id FROM user_badges WHERE user_id = ?
            )
            ORDER BY b.criteria_value ASC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Check if user meets criteria for a specific badge
     */
    private function checkBadgeCriteria($badge, $userStats, $activityType, $activityData) {
        $criteriaType = $badge['criteria_type'];
        $criteriaValue = $badge['criteria_value'];
        $criteriaMetadata = json_decode($badge['criteria_metadata'] ?? '{}', true);
        
        switch ($criteriaType) {
            case 'checkin_count':
                return ($userStats['total_checkins'] ?? 0) >= $criteriaValue;
                
            case 'beer_count':
                return ($userStats['unique_beers_tried'] ?? 0) >= $criteriaValue;
                
            case 'brewery_count':
                return ($userStats['unique_breweries_visited'] ?? 0) >= $criteriaValue;
                
            case 'style_count':
                return ($userStats['unique_styles_tried'] ?? 0) >= $criteriaValue;
                
            case 'rating_count':
                return ($userStats['total_ratings'] ?? 0) >= $criteriaValue;
                
            case 'follower_count':
                return ($userStats['total_followers'] ?? 0) >= $criteriaValue;
                
            case 'review_count':
                // Count reviews from ratings table
                $stmt = $this->conn->prepare("
                    SELECT COUNT(*) FROM beer_ratings 
                    WHERE user_id = ? AND review_text IS NOT NULL AND review_text != ''
                ");
                $stmt->execute([$userStats['user_id']]);
                $reviewCount = $stmt->fetchColumn();
                return $reviewCount >= $criteriaValue;
                
            case 'special':
                return $this->checkSpecialCriteria($badge, $userStats, $activityType, $activityData, $criteriaMetadata);
                
            default:
                return false;
        }
    }
    
    /**
     * Check special badge criteria
     */
    private function checkSpecialCriteria($badge, $userStats, $activityType, $activityData, $metadata) {
        // This method handles complex badge logic
        // Implementation would depend on specific badge requirements
        
        if (isset($metadata['action']) && $metadata['action'] === 'first_follow') {
            return $activityType === 'user_follow' && ($userStats['total_following'] ?? 0) >= 1;
        }
        
        if (isset($metadata['rating'])) {
            return $activityType === 'beer_rating' && 
                   isset($activityData['rating']) && 
                   $activityData['rating'] == $metadata['rating'];
        }
        
        // Add more special criteria as needed
        return false;
    }
    
    /**
     * Award a badge to a user
     */
    private function awardBadge($userId, $badgeId) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO user_badges (user_id, badge_id) 
                VALUES (?, ?)
            ");
            $stmt->execute([$userId, $badgeId]);
            
            // Log badge earning activity
            $stmt = $this->conn->prepare("
                INSERT INTO user_activities (user_id, activity_type, target_type, target_id, metadata)
                VALUES (?, 'badge_earned', 'badge', ?, ?)
            ");
            $stmt->execute([
                $userId,
                $badgeId,
                json_encode(['badge_id' => $badgeId])
            ]);

            // Create notification for badge earned (Phase 7)
            $this->createBadgeNotification($userId, $badgeId);

            return true;
            
        } catch (Exception $e) {
            error_log("Badge award error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update user's badge count
     */
    private function updateUserBadgeCount($userId) {
        $stmt = $this->conn->prepare("
            UPDATE user_statistics 
            SET total_badges_earned = (
                SELECT COUNT(*) FROM user_badges WHERE user_id = ?
            ),
            total_points_earned = (
                SELECT COALESCE(SUM(b.points_value), 0) 
                FROM user_badges ub 
                JOIN badges b ON ub.badge_id = b.id 
                WHERE ub.user_id = ?
            )
            WHERE user_id = ?
        ");
        $stmt->execute([$userId, $userId, $userId]);
    }
    
    /**
     * Get user's earned badges
     */
    public function getUserBadges($userId, $featured = false) {
        $whereClause = $featured ? "AND ub.is_featured = 1" : "";
        
        $stmt = $this->conn->prepare("
            SELECT b.*, ub.earned_at, ub.is_featured
            FROM user_badges ub
            JOIN badges b ON ub.badge_id = b.id
            WHERE ub.user_id = ? $whereClause
            ORDER BY ub.earned_at DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    /**
     * Create notification for badge earned (Phase 7)
     */
    private function createBadgeNotification($userId, $badgeId) {
        try {
            // Get badge details
            $stmt = $this->conn->prepare("
                SELECT name, description FROM badges WHERE id = ?
            ");
            $stmt->execute([$badgeId]);
            $badge = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($badge) {
                require_once 'NotificationService.php';
                $notificationService = new NotificationService($this->conn);

                $notificationService->createNotification(
                    $userId,
                    'achievement_unlocked',
                    'Achievement Unlocked! 🏆',
                    "Congratulations! You've earned the '{$badge['name']}' badge. {$badge['description']}",
                    [
                        'badge_id' => $badgeId,
                        'badge_name' => $badge['name'],
                        'badge_description' => $badge['description']
                    ],
                    'badge',
                    $badgeId
                );
            }

        } catch (Exception $e) {
            error_log("Create badge notification error: " . $e->getMessage());
        }
    }

    /**
     * Get badges earned by user in a specific year
     */
    public function getYearBadges($userId, $year) {
        try {
            $stmt = $this->conn->prepare("
                SELECT b.*, ub.earned_at
                FROM user_badges ub
                JOIN badges b ON ub.badge_id = b.id
                WHERE ub.user_id = ? AND YEAR(ub.earned_at) = ?
                ORDER BY ub.earned_at DESC
            ");
            $stmt->execute([$userId, $year]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Get year badges error: " . $e->getMessage());
            return [];
        }
    }
}
?>
