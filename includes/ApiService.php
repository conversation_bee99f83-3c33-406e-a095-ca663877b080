<?php
/**
 * API Service
 * Phase 10: Advanced Features & API Development
 * Comprehensive API management and authentication
 */

class ApiService {
    private $conn;
    private $apiVersion = 'v1';
    private $rateLimits = [
        'public' => 100,    // requests per hour for public endpoints
        'authenticated' => 1000, // requests per hour for authenticated users
        'premium' => 5000   // requests per hour for premium API keys
    ];
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Authenticate API request
     */
    public function authenticateRequest($headers) {
        // Check for API key in headers
        $apiKey = $headers['X-API-Key'] ?? $headers['Authorization'] ?? null;
        
        if (!$apiKey) {
            return ['success' => false, 'error' => 'API key required', 'code' => 401];
        }
        
        // Remove 'Bearer ' prefix if present
        $apiKey = str_replace('Bearer ', '', $apiKey);
        
        try {
            $stmt = $this->conn->prepare("
                SELECT ak.*, u.id as user_id, u.email, u.role
                FROM api_keys ak
                LEFT JOIN users u ON ak.user_id = u.id
                WHERE ak.api_key = ? AND ak.is_active = 1 AND ak.expires_at > NOW()
            ");
            $stmt->execute([$apiKey]);
            $keyData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$keyData) {
                return ['success' => false, 'error' => 'Invalid or expired API key', 'code' => 401];
            }
            
            // Check rate limits
            $rateLimitCheck = $this->checkRateLimit($keyData['id'], $keyData['tier']);
            if (!$rateLimitCheck['success']) {
                return $rateLimitCheck;
            }
            
            // Update last used timestamp
            $this->updateApiKeyUsage($keyData['id']);
            
            return [
                'success' => true,
                'api_key_id' => $keyData['id'],
                'user_id' => $keyData['user_id'],
                'tier' => $keyData['tier'],
                'permissions' => json_decode($keyData['permissions'], true) ?? []
            ];
            
        } catch (Exception $e) {
            error_log("API authentication error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Authentication failed', 'code' => 500];
        }
    }
    
    /**
     * Check rate limits for API key
     */
    public function checkRateLimit($apiKeyId, $tier) {
        try {
            $limit = $this->rateLimits[$tier] ?? $this->rateLimits['public'];
            $window = 3600; // 1 hour in seconds
            
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as request_count
                FROM api_requests
                WHERE api_key_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute([$apiKeyId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['request_count'] >= $limit) {
                return [
                    'success' => false,
                    'error' => 'Rate limit exceeded',
                    'code' => 429,
                    'retry_after' => $window
                ];
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Rate limit check failed', 'code' => 500];
        }
    }
    
    /**
     * Log API request
     */
    public function logRequest($apiKeyId, $endpoint, $method, $responseCode, $responseTime = null) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO api_requests (api_key_id, endpoint, method, response_code, response_time_ms, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            $stmt->execute([
                $apiKeyId,
                $endpoint,
                $method,
                $responseCode,
                $responseTime,
                $ipAddress,
                $userAgent
            ]);
            
        } catch (Exception $e) {
            error_log("API request logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Update API key usage
     */
    private function updateApiKeyUsage($apiKeyId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE api_keys 
                SET last_used_at = NOW(), request_count = request_count + 1
                WHERE id = ?
            ");
            $stmt->execute([$apiKeyId]);
            
        } catch (Exception $e) {
            error_log("API key usage update error: " . $e->getMessage());
        }
    }
    
    /**
     * Generate new API key
     */
    public function generateApiKey($userId, $name, $tier = 'public', $permissions = []) {
        try {
            $apiKey = 'bst_' . bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 year'));
            
            $stmt = $this->conn->prepare("
                INSERT INTO api_keys (user_id, api_key, name, tier, permissions, expires_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $apiKey,
                $name,
                $tier,
                json_encode($permissions),
                $expiresAt
            ]);
            
            return [
                'success' => true,
                'api_key' => $apiKey,
                'expires_at' => $expiresAt
            ];
            
        } catch (Exception $e) {
            error_log("API key generation error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to generate API key'];
        }
    }
    
    /**
     * Validate API permissions
     */
    public function hasPermission($permissions, $requiredPermission) {
        if (empty($permissions)) {
            return false;
        }
        
        // Check for wildcard permission
        if (in_array('*', $permissions)) {
            return true;
        }
        
        // Check for specific permission
        return in_array($requiredPermission, $permissions);
    }
    
    /**
     * Format API response
     */
    public function formatResponse($data, $success = true, $message = null, $meta = []) {
        $response = [
            'success' => $success,
            'version' => $this->apiVersion,
            'timestamp' => date('c'),
        ];
        
        if ($success) {
            $response['data'] = $data;
        } else {
            $response['error'] = $data;
        }
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        return $response;
    }
    
    /**
     * Handle CORS headers
     */
    public function setCorsHeaders() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');
        header('Access-Control-Max-Age: 86400');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * Get API usage statistics
     */
    public function getUsageStats($apiKeyId, $timeframe = '24h') {
        try {
            $interval = match($timeframe) {
                '1h' => 'INTERVAL 1 HOUR',
                '24h' => 'INTERVAL 24 HOUR',
                '7d' => 'INTERVAL 7 DAY',
                '30d' => 'INTERVAL 30 DAY',
                default => 'INTERVAL 24 HOUR'
            };
            
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN response_code = 200 THEN 1 END) as successful_requests,
                    COUNT(CASE WHEN response_code >= 400 THEN 1 END) as error_requests,
                    AVG(response_time_ms) as avg_response_time,
                    COUNT(DISTINCT endpoint) as unique_endpoints
                FROM api_requests
                WHERE api_key_id = ? AND created_at > DATE_SUB(NOW(), $interval)
            ");
            
            $stmt->execute([$apiKeyId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("API usage stats error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get popular endpoints
     */
    public function getPopularEndpoints($limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    endpoint,
                    COUNT(*) as request_count,
                    AVG(response_time_ms) as avg_response_time
                FROM api_requests
                WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY endpoint
                ORDER BY request_count DESC
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Popular endpoints error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Revoke API key
     */
    public function revokeApiKey($apiKeyId, $userId = null) {
        try {
            $sql = "UPDATE api_keys SET is_active = 0, revoked_at = NOW() WHERE id = ?";
            $params = [$apiKeyId];
            
            if ($userId) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("API key revocation error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to revoke API key'];
        }
    }
}
