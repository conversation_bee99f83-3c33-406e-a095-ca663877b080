<?php
/**
 * Permission Manager Class
 * Phase 5 - User Management & Authentication
 * 
 * Advanced permission system for digital board access control
 */

class PermissionManager {
    private $conn;
    
    // Digital board specific permissions
    const PERMISSIONS = [
        // Board Management
        'view_boards' => 'View Digital Boards',
        'create_boards' => 'Create Digital Boards',
        'edit_boards' => 'Edit Digital Boards',
        'delete_boards' => 'Delete Digital Boards',
        'manage_board_settings' => 'Manage Board Settings',
        
        // Template Management
        'view_templates' => 'View Templates',
        'create_templates' => 'Create Templates',
        'edit_templates' => 'Edit Templates',
        'delete_templates' => 'Delete Templates',
        'share_templates' => 'Share Templates',
        
        // Slideshow Management
        'view_slideshows' => 'View Slideshows',
        'create_slideshows' => 'Create Slideshows',
        'edit_slideshows' => 'Edit Slideshows',
        'delete_slideshows' => 'Delete Slideshows',
        'manage_slides' => 'Manage Slides',
        
        // Media Management
        'view_media' => 'View Media Library',
        'upload_media' => 'Upload Media',
        'edit_media' => 'Edit Media',
        'delete_media' => 'Delete Media',
        'organize_media' => 'Organize Media',
        
        // Beer Menu Management
        'view_beer_menu' => 'View Beer Menu',
        'edit_beer_menu' => 'Edit Beer Menu',
        'manage_beer_categories' => 'Manage Beer Categories',
        'import_beer_data' => 'Import Beer Data',
        
        // Analytics & Reports
        'view_analytics' => 'View Analytics',
        'export_data' => 'Export Data',
        'view_usage_stats' => 'View Usage Statistics',
        
        // User Management (for business owners)
        'manage_brewery_users' => 'Manage Brewery Users',
        'view_user_activity' => 'View User Activity',
        
        // System Administration
        'manage_all_breweries' => 'Manage All Breweries',
        'system_settings' => 'System Settings',
        'view_system_logs' => 'View System Logs'
    ];
    
    // Role-based permission sets
    const ROLE_PERMISSIONS = [
        'admin' => [
            'view_boards', 'create_boards', 'edit_boards', 'delete_boards', 'manage_board_settings',
            'view_templates', 'create_templates', 'edit_templates', 'delete_templates', 'share_templates',
            'view_slideshows', 'create_slideshows', 'edit_slideshows', 'delete_slideshows', 'manage_slides',
            'view_media', 'upload_media', 'edit_media', 'delete_media', 'organize_media',
            'view_beer_menu', 'edit_beer_menu', 'manage_beer_categories', 'import_beer_data',
            'view_analytics', 'export_data', 'view_usage_stats',
            'manage_brewery_users', 'view_user_activity',
            'manage_all_breweries', 'system_settings', 'view_system_logs'
        ],
        'site_moderator' => [
            'view_boards', 'edit_boards', 'manage_board_settings',
            'view_templates', 'edit_templates',
            'view_slideshows', 'edit_slideshows', 'manage_slides',
            'view_media', 'edit_media', 'organize_media',
            'view_beer_menu', 'edit_beer_menu',
            'view_analytics', 'view_usage_stats',
            'view_user_activity'
        ],
        'business_owner' => [
            'view_boards', 'create_boards', 'edit_boards', 'delete_boards', 'manage_board_settings',
            'view_templates', 'create_templates', 'edit_templates', 'delete_templates', 'share_templates',
            'view_slideshows', 'create_slideshows', 'edit_slideshows', 'delete_slideshows', 'manage_slides',
            'view_media', 'upload_media', 'edit_media', 'delete_media', 'organize_media',
            'view_beer_menu', 'edit_beer_menu', 'manage_beer_categories', 'import_beer_data',
            'view_analytics', 'export_data', 'view_usage_stats',
            'manage_brewery_users', 'view_user_activity'
        ],
        'business_manager' => [
            'view_boards', 'create_boards', 'edit_boards', 'manage_board_settings',
            'view_templates', 'create_templates', 'edit_templates',
            'view_slideshows', 'create_slideshows', 'edit_slideshows', 'delete_slideshows', 'manage_slides',
            'view_media', 'upload_media', 'edit_media', 'delete_media', 'organize_media',
            'view_beer_menu', 'edit_beer_menu', 'manage_beer_categories',
            'view_analytics', 'view_usage_stats'
        ],
        'digital_board_operator' => [
            'view_boards', 'edit_boards',
            'view_templates',
            'view_slideshows', 'edit_slideshows', 'manage_slides',
            'view_media', 'upload_media', 'edit_media',
            'view_beer_menu', 'edit_beer_menu'
        ],
        'user' => [
            'view_boards',
            'view_templates',
            'view_slideshows',
            'view_media',
            'view_beer_menu'
        ]
    ];
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Check if user has specific permission
     */
    public function hasPermission($userId, $permission, $breweryId = null) {
        try {
            // Get user role and brewery
            $stmt = $this->conn->prepare("
                SELECT u.role, p.brewery_id 
                FROM users u 
                LEFT JOIN profiles p ON u.id = p.id 
                WHERE u.id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return false;
            }
            
            // Admin has all permissions
            if ($user['role'] === 'admin') {
                return true;
            }
            
            // Check if permission exists in role permissions
            $rolePermissions = self::ROLE_PERMISSIONS[$user['role']] ?? [];
            if (!in_array($permission, $rolePermissions)) {
                return false;
            }
            
            // For brewery-specific permissions, check brewery access
            if ($breweryId && $user['brewery_id'] !== $breweryId && $user['role'] !== 'admin') {
                // Check if user has explicit permission for this brewery
                $stmt = $this->conn->prepare("
                    SELECT COUNT(*) 
                    FROM user_brewery_permissions 
                    WHERE user_id = ? AND brewery_id = ? AND permission = ? AND is_active = 1
                ");
                $stmt->execute([$userId, $breweryId, $permission]);
                return $stmt->fetchColumn() > 0;
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("PermissionManager: Error checking permission - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all permissions for a user
     */
    public function getUserPermissions($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT u.role, p.brewery_id 
                FROM users u 
                LEFT JOIN profiles p ON u.id = p.id 
                WHERE u.id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return [];
            }
            
            // Get role-based permissions
            $permissions = self::ROLE_PERMISSIONS[$user['role']] ?? [];
            
            // Get additional brewery-specific permissions
            $stmt = $this->conn->prepare("
                SELECT DISTINCT permission 
                FROM user_brewery_permissions 
                WHERE user_id = ? AND is_active = 1
            ");
            $stmt->execute([$userId]);
            $additionalPermissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            return array_unique(array_merge($permissions, $additionalPermissions));
            
        } catch (Exception $e) {
            error_log("PermissionManager: Error getting user permissions - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Assign specific permissions to user for a brewery
     */
    public function assignPermissions($userId, $breweryId, $permissions) {
        try {
            $this->conn->beginTransaction();
            
            foreach ($permissions as $permission) {
                if (!array_key_exists($permission, self::PERMISSIONS)) {
                    continue;
                }
                
                // Check if permission already exists
                $stmt = $this->conn->prepare("
                    SELECT id FROM user_brewery_permissions 
                    WHERE user_id = ? AND brewery_id = ? AND permission = ?
                ");
                $stmt->execute([$userId, $breweryId, $permission]);
                
                if (!$stmt->fetch()) {
                    // Insert new permission
                    $stmt = $this->conn->prepare("
                        INSERT INTO user_brewery_permissions (user_id, brewery_id, permission, is_active, created_at)
                        VALUES (?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute([$userId, $breweryId, $permission]);
                } else {
                    // Activate existing permission
                    $stmt = $this->conn->prepare("
                        UPDATE user_brewery_permissions 
                        SET is_active = 1, updated_at = NOW()
                        WHERE user_id = ? AND brewery_id = ? AND permission = ?
                    ");
                    $stmt->execute([$userId, $breweryId, $permission]);
                }
            }
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("PermissionManager: Error assigning permissions - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Revoke specific permissions from user
     */
    public function revokePermissions($userId, $breweryId, $permissions) {
        try {
            $placeholders = str_repeat('?,', count($permissions) - 1) . '?';
            $params = array_merge([$userId, $breweryId], $permissions);
            
            $stmt = $this->conn->prepare("
                UPDATE user_brewery_permissions 
                SET is_active = 0, updated_at = NOW()
                WHERE user_id = ? AND brewery_id = ? AND permission IN ($placeholders)
            ");
            $stmt->execute($params);
            
            return true;
            
        } catch (Exception $e) {
            error_log("PermissionManager: Error revoking permissions - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Revoke all permissions from user
     */
    public function revokeAllPermissions($userId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE user_brewery_permissions 
                SET is_active = 0, updated_at = NOW()
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("PermissionManager: Error revoking all permissions - " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get default permissions for a role
     */
    public function getDefaultPermissionsForRole($role) {
        return self::ROLE_PERMISSIONS[$role] ?? [];
    }
    
    /**
     * Get all available permissions
     */
    public function getAllPermissions() {
        return self::PERMISSIONS;
    }
    
    /**
     * Get breweries accessible by user
     */
    public function getUserBreweries($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT u.role, p.brewery_id 
                FROM users u 
                LEFT JOIN profiles p ON u.id = p.id 
                WHERE u.id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return [];
            }
            
            // Admin can access all breweries
            if ($user['role'] === 'admin') {
                $stmt = $this->conn->query("SELECT id, name FROM breweries ORDER BY name");
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            // Get user's primary brewery
            $breweries = [];
            if ($user['brewery_id']) {
                $stmt = $this->conn->prepare("SELECT id, name FROM breweries WHERE id = ?");
                $stmt->execute([$user['brewery_id']]);
                $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($brewery) {
                    $breweries[] = $brewery;
                }
            }
            
            // Get additional breweries with explicit permissions
            $stmt = $this->conn->prepare("
                SELECT DISTINCT b.id, b.name 
                FROM breweries b
                JOIN user_brewery_permissions ubp ON b.id = ubp.brewery_id
                WHERE ubp.user_id = ? AND ubp.is_active = 1
                AND b.id != ?
                ORDER BY b.name
            ");
            $stmt->execute([$userId, $user['brewery_id'] ?? '']);
            $additionalBreweries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return array_merge($breweries, $additionalBreweries);
            
        } catch (Exception $e) {
            error_log("PermissionManager: Error getting user breweries - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if user can access brewery
     */
    public function canAccessBrewery($userId, $breweryId) {
        $userBreweries = $this->getUserBreweries($userId);
        return in_array($breweryId, array_column($userBreweries, 'id'));
    }
}
