<?php
/**
 * PhotoManager Class
 * Handles photo uploads, processing, and management
 */

class PhotoManager {
    private $uploadPath;
    private $maxFileSize;
    private $allowedTypes;
    private $db;
    
    public function __construct() {
        $this->uploadPath = UPLOAD_PATH;
        $this->maxFileSize = MAX_FILE_SIZE;
        $this->allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Ensure upload directories exist
        $this->createUploadDirectories();
    }
    
    /**
     * Create necessary upload directories
     */
    private function createUploadDirectories() {
        $directories = [
            $this->uploadPath,
            $this->uploadPath . 'photos/',
            $this->uploadPath . 'photos/checkins/',
            $this->uploadPath . 'photos/beers/',
            $this->uploadPath . 'photos/breweries/',
            $this->uploadPath . 'photos/users/',
            $this->uploadPath . 'photos/thumbnails/',
            $this->uploadPath . 'photos/temp/'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * Upload multiple photos
     */
    public function uploadMultiplePhotos($files, $type, $targetId, $userId) {
        $results = [];
        
        if (!is_array($files['name'])) {
            // Single file upload
            $files = [
                'name' => [$files['name']],
                'type' => [$files['type']],
                'tmp_name' => [$files['tmp_name']],
                'error' => [$files['error']],
                'size' => [$files['size']]
            ];
        }
        
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ];
                
                $result = $this->uploadSinglePhoto($file, $type, $targetId, $userId);
                $results[] = $result;
            } else {
                $results[] = [
                    'success' => false,
                    'error' => $this->getUploadErrorMessage($files['error'][$i]),
                    'filename' => $files['name'][$i]
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Upload a single photo
     */
    public function uploadSinglePhoto($file, $type, $targetId, $userId) {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error'],
                    'filename' => $file['name']
                ];
            }
            
            // Generate unique filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $relativePath = "photos/{$type}/" . $filename;
            $fullPath = $this->uploadPath . $relativePath;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
                return [
                    'success' => false,
                    'error' => 'Failed to move uploaded file',
                    'filename' => $file['name']
                ];
            }
            
            // Create thumbnail
            $thumbnailPath = $this->createThumbnail($fullPath, $filename);
            
            // Get image dimensions
            $imageInfo = getimagesize($fullPath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;
            
            // Save to database
            $photoId = $this->savePhotoToDatabase([
                'filename' => $filename,
                'original_name' => $file['name'],
                'file_path' => $relativePath,
                'thumbnail_path' => $thumbnailPath,
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'width' => $width,
                'height' => $height,
                'type' => $type,
                'target_id' => $targetId,
                'user_id' => $userId
            ]);
            
            return [
                'success' => true,
                'photo_id' => $photoId,
                'filename' => $filename,
                'url' => '/' . $relativePath,
                'thumbnail_url' => $thumbnailPath ? '/' . $thumbnailPath : null,
                'width' => $width,
                'height' => $height
            ];
            
        } catch (Exception $e) {
            error_log("Photo upload error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Upload failed: ' . $e->getMessage(),
                'filename' => $file['name']
            ];
        }
    }
    
    /**
     * Validate uploaded file
     */
    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => $this->getUploadErrorMessage($file['error'])
            ];
        }
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size (' . $this->formatFileSize($this->maxFileSize) . ')'
            ];
        }
        
        // Check file type
        if (!in_array($file['type'], $this->allowedTypes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Allowed types: JPEG, PNG, GIF, WebP'
            ];
        }
        
        // Verify it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return [
                'valid' => false,
                'error' => 'File is not a valid image'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Create thumbnail for uploaded image
     */
    private function createThumbnail($sourcePath, $filename) {
        try {
            $thumbnailDir = $this->uploadPath . 'photos/thumbnails/';
            $thumbnailFilename = 'thumb_' . $filename;
            $thumbnailPath = $thumbnailDir . $thumbnailFilename;
            
            // Get image info
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                return null;
            }
            
            $sourceWidth = $imageInfo[0];
            $sourceHeight = $imageInfo[1];
            $mimeType = $imageInfo['mime'];
            
            // Calculate thumbnail dimensions (max 300x300, maintain aspect ratio)
            $maxSize = 300;
            if ($sourceWidth > $sourceHeight) {
                $thumbWidth = $maxSize;
                $thumbHeight = intval(($sourceHeight * $maxSize) / $sourceWidth);
            } else {
                $thumbHeight = $maxSize;
                $thumbWidth = intval(($sourceWidth * $maxSize) / $sourceHeight);
            }
            
            // Create source image
            switch ($mimeType) {
                case 'image/jpeg':
                    $sourceImage = imagecreatefromjpeg($sourcePath);
                    break;
                case 'image/png':
                    $sourceImage = imagecreatefrompng($sourcePath);
                    break;
                case 'image/gif':
                    $sourceImage = imagecreatefromgif($sourcePath);
                    break;
                case 'image/webp':
                    $sourceImage = imagecreatefromwebp($sourcePath);
                    break;
                default:
                    return null;
            }
            
            if (!$sourceImage) {
                return null;
            }
            
            // Create thumbnail
            $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
            
            // Preserve transparency for PNG and GIF
            if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefilledrectangle($thumbnail, 0, 0, $thumbWidth, $thumbHeight, $transparent);
            }
            
            // Resize image
            imagecopyresampled(
                $thumbnail, $sourceImage,
                0, 0, 0, 0,
                $thumbWidth, $thumbHeight,
                $sourceWidth, $sourceHeight
            );
            
            // Save thumbnail
            $success = false;
            switch ($mimeType) {
                case 'image/jpeg':
                    $success = imagejpeg($thumbnail, $thumbnailPath, 85);
                    break;
                case 'image/png':
                    $success = imagepng($thumbnail, $thumbnailPath, 8);
                    break;
                case 'image/gif':
                    $success = imagegif($thumbnail, $thumbnailPath);
                    break;
                case 'image/webp':
                    $success = imagewebp($thumbnail, $thumbnailPath, 85);
                    break;
            }
            
            // Clean up
            imagedestroy($sourceImage);
            imagedestroy($thumbnail);
            
            return $success ? 'photos/thumbnails/' . $thumbnailFilename : null;
            
        } catch (Exception $e) {
            error_log("Thumbnail creation error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Save photo metadata to database
     */
    private function savePhotoToDatabase($data) {
        $stmt = $this->db->prepare("
            INSERT INTO photos (
                filename, original_name, file_path, thumbnail_path,
                file_size, mime_type, width, height,
                type, target_id, user_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $data['filename'],
            $data['original_name'],
            $data['file_path'],
            $data['thumbnail_path'],
            $data['file_size'],
            $data['mime_type'],
            $data['width'],
            $data['height'],
            $data['type'],
            $data['target_id'],
            $data['user_id']
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Get photos by type and target ID
     */
    public function getPhotos($type, $targetId, $limit = null) {
        $sql = "
            SELECT p.*, u.username, pr.first_name, pr.last_name
            FROM photos p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN profiles pr ON p.user_id = pr.id
            WHERE p.type = ? AND p.target_id = ?
            ORDER BY p.created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$type, $targetId]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Delete photo
     */
    public function deletePhoto($photoId, $userId = null) {
        try {
            // Get photo info
            $stmt = $this->db->prepare("SELECT * FROM photos WHERE id = ?");
            $stmt->execute([$photoId]);
            $photo = $stmt->fetch();
            
            if (!$photo) {
                return ['success' => false, 'error' => 'Photo not found'];
            }
            
            // Check permissions (user can only delete their own photos)
            if ($userId && $photo['user_id'] !== $userId) {
                return ['success' => false, 'error' => 'Permission denied'];
            }
            
            // Delete files
            $fullPath = $this->uploadPath . $photo['file_path'];
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
            
            if ($photo['thumbnail_path']) {
                $thumbnailPath = $this->uploadPath . $photo['thumbnail_path'];
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
            }
            
            // Delete from database
            $stmt = $this->db->prepare("DELETE FROM photos WHERE id = ?");
            $stmt->execute([$photoId]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log("Photo deletion error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to delete photo'];
        }
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'File size exceeds maximum allowed size';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Format file size for display
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
?>
