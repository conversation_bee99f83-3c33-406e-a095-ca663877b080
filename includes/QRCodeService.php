<?php
/**
 * QR Code Service
 * Phase 10: Advanced Features & API Development
 * Generate and manage QR codes for various content
 */

class QRCodeService {
    private $conn;
    private $baseUrl;
    private $qrApiUrl = 'https://api.qrserver.com/v1/create-qr-code/';
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->baseUrl = BASE_URL;
    }
    
    /**
     * Generate QR code for different content types
     */
    public function generateQRCode($type, $id, $options = []) {
        $data = $this->getQRData($type, $id);
        
        if (!$data) {
            return null;
        }
        
        $qrOptions = array_merge([
            'size' => '300x300',
            'format' => 'png',
            'error_correction' => 'M',
            'margin' => 10,
            'color' => '000000',
            'bgcolor' => 'ffffff'
        ], $options);
        
        $qrUrl = $this->generateQRUrl($data['url'], $qrOptions);
        
        // Save QR code record
        $qrId = $this->saveQRCode($type, $id, $data['url'], $qrUrl, $data['title']);
        
        return [
            'id' => $qrId,
            'url' => $qrUrl,
            'data_url' => $data['url'],
            'title' => $data['title'],
            'description' => $data['description'],
            'type' => $type,
            'content_id' => $id,
            'download_url' => $this->baseUrl . '/api/qr-download.php?id=' . $qrId
        ];
    }
    
    /**
     * Get QR data based on content type
     */
    private function getQRData($type, $id) {
        switch ($type) {
            case 'beer':
                return $this->getBeerQRData($id);
            case 'brewery':
                return $this->getBreweryQRData($id);
            case 'user_profile':
                return $this->getUserProfileQRData($id);
            case 'checkin':
                return $this->getCheckinQRData($id);
            case 'menu':
                return $this->getMenuQRData($id);
            case 'event':
                return $this->getEventQRData($id);
            case 'custom':
                return $this->getCustomQRData($id);
            default:
                return null;
        }
    }
    
    /**
     * Get beer QR data
     */
    private function getBeerQRData($beerId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT bm.name, b.name as brewery_name
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                WHERE bm.id = ?
            ");
            
            $stmt->execute([$beerId]);
            $beer = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$beer) {
                return null;
            }
            
            return [
                'url' => $this->baseUrl . '/beers/detail.php?id=' . $beerId,
                'title' => $beer['name'],
                'description' => 'Beer by ' . $beer['brewery_name']
            ];
            
        } catch (Exception $e) {
            error_log("Beer QR data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get brewery QR data
     */
    private function getBreweryQRData($breweryId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT name, address
                FROM breweries
                WHERE id = ?
            ");
            
            $stmt->execute([$breweryId]);
            $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$brewery) {
                return null;
            }
            
            return [
                'url' => $this->baseUrl . '/breweries/profile.php?id=' . $breweryId,
                'title' => $brewery['name'],
                'description' => 'Brewery located at ' . $brewery['address']
            ];
            
        } catch (Exception $e) {
            error_log("Brewery QR data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get user profile QR data
     */
    private function getUserProfileQRData($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT CONCAT(p.first_name, ' ', p.last_name) as full_name
                FROM users u
                JOIN profiles p ON u.id = p.id
                WHERE u.id = ?
            ");
            
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return null;
            }
            
            return [
                'url' => $this->baseUrl . '/user/profile.php?id=' . $userId,
                'title' => $user['full_name'],
                'description' => 'Beer enthusiast profile on Beersty'
            ];
            
        } catch (Exception $e) {
            error_log("User profile QR data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get menu QR data (for brewery menus)
     */
    private function getMenuQRData($breweryId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT name
                FROM breweries
                WHERE id = ?
            ");
            
            $stmt->execute([$breweryId]);
            $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$brewery) {
                return null;
            }
            
            return [
                'url' => $this->baseUrl . '/breweries/menu.php?id=' . $breweryId,
                'title' => $brewery['name'] . ' Menu',
                'description' => 'Digital beer menu for ' . $brewery['name']
            ];
            
        } catch (Exception $e) {
            error_log("Menu QR data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Generate QR code URL using external service
     */
    private function generateQRUrl($data, $options) {
        $params = [
            'data' => $data,
            'size' => $options['size'],
            'format' => $options['format'],
            'ecc' => $options['error_correction'],
            'margin' => $options['margin'],
            'color' => $options['color'],
            'bgcolor' => $options['bgcolor']
        ];
        
        return $this->qrApiUrl . '?' . http_build_query($params);
    }
    
    /**
     * Save QR code record to database
     */
    private function saveQRCode($type, $contentId, $dataUrl, $qrUrl, $title) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO qr_codes (content_type, content_id, data_url, qr_url, title, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([$type, $contentId, $dataUrl, $qrUrl, $title]);
            
            return $this->conn->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Save QR code error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get existing QR code
     */
    public function getQRCode($type, $contentId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM qr_codes
                WHERE content_type = ? AND content_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            ");
            
            $stmt->execute([$type, $contentId]);
            $qr = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($qr) {
                $qr['download_url'] = $this->baseUrl . '/api/qr-download.php?id=' . $qr['id'];
            }
            
            return $qr;
            
        } catch (Exception $e) {
            error_log("Get QR code error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Track QR code scan
     */
    public function trackScan($qrId, $userId = null, $metadata = []) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO qr_scans (qr_code_id, user_id, ip_address, user_agent, metadata, scanned_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            $stmt->execute([
                $qrId,
                $userId,
                $ipAddress,
                $userAgent,
                json_encode($metadata)
            ]);
            
            // Update scan count
            $this->updateScanCount($qrId);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Track QR scan error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update QR code scan count
     */
    private function updateScanCount($qrId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE qr_codes 
                SET scan_count = scan_count + 1, last_scanned_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([$qrId]);
            
        } catch (Exception $e) {
            error_log("Update scan count error: " . $e->getMessage());
        }
    }
    
    /**
     * Get QR code analytics
     */
    public function getQRAnalytics($qrId, $timeframe = '30d') {
        try {
            $interval = match($timeframe) {
                '24h' => 'INTERVAL 24 HOUR',
                '7d' => 'INTERVAL 7 DAY',
                '30d' => 'INTERVAL 30 DAY',
                '1y' => 'INTERVAL 1 YEAR',
                default => 'INTERVAL 30 DAY'
            };
            
            // Get basic stats
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_scans,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    MIN(scanned_at) as first_scan,
                    MAX(scanned_at) as last_scan
                FROM qr_scans
                WHERE qr_code_id = ? AND scanned_at > DATE_SUB(NOW(), $interval)
            ");
            
            $stmt->execute([$qrId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get daily breakdown
            $stmt = $this->conn->prepare("
                SELECT 
                    DATE(scanned_at) as scan_date,
                    COUNT(*) as scans,
                    COUNT(DISTINCT user_id) as unique_users
                FROM qr_scans
                WHERE qr_code_id = ? AND scanned_at > DATE_SUB(NOW(), $interval)
                GROUP BY DATE(scanned_at)
                ORDER BY scan_date
            ");
            
            $stmt->execute([$qrId]);
            $daily = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'stats' => $stats,
                'daily_breakdown' => $daily,
                'timeframe' => $timeframe
            ];
            
        } catch (Exception $e) {
            error_log("QR analytics error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Generate batch QR codes (for events, menus, etc.)
     */
    public function generateBatchQRCodes($items, $type, $options = []) {
        $results = [];
        
        foreach ($items as $item) {
            $qr = $this->generateQRCode($type, $item['id'], $options);
            if ($qr) {
                $results[] = array_merge($qr, [
                    'item_name' => $item['name'] ?? 'Item ' . $item['id']
                ]);
            }
        }
        
        return $results;
    }
    
    /**
     * Create custom QR code with arbitrary data
     */
    public function createCustomQR($data, $title, $description = null, $options = []) {
        $qrOptions = array_merge([
            'size' => '300x300',
            'format' => 'png',
            'error_correction' => 'M',
            'margin' => 10,
            'color' => '000000',
            'bgcolor' => 'ffffff'
        ], $options);
        
        $qrUrl = $this->generateQRUrl($data, $qrOptions);
        
        // Save custom QR code
        $qrId = $this->saveQRCode('custom', null, $data, $qrUrl, $title);
        
        return [
            'id' => $qrId,
            'url' => $qrUrl,
            'data_url' => $data,
            'title' => $title,
            'description' => $description,
            'type' => 'custom',
            'download_url' => $this->baseUrl . '/api/qr-download.php?id=' . $qrId
        ];
    }
    
    /**
     * Get QR codes by user or content
     */
    public function getQRCodes($filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            if (isset($filters['type'])) {
                $where[] = 'content_type = ?';
                $params[] = $filters['type'];
            }
            
            if (isset($filters['content_id'])) {
                $where[] = 'content_id = ?';
                $params[] = $filters['content_id'];
            }
            
            $whereClause = implode(' AND ', $where);
            
            $stmt = $this->conn->prepare("
                SELECT *, 
                       CONCAT('" . $this->baseUrl . "/api/qr-download.php?id=', id) as download_url
                FROM qr_codes
                WHERE $whereClause
                ORDER BY created_at DESC
            ");
            
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Get QR codes error: " . $e->getMessage());
            return [];
        }
    }
}
