<?php
/**
 * Performance Optimizer
 * Phase 8 - Production Deployment & Optimization
 * 
 * Comprehensive performance optimization and caching system
 */

class PerformanceOptimizer {
    private $redis;
    private $config;
    private $metrics;
    
    public function __construct($redisConnection = null, $config = []) {
        $this->redis = $redisConnection;
        $this->config = array_merge([
            'cache_enabled' => true,
            'cache_ttl' => 3600,
            'compression_enabled' => true,
            'minification_enabled' => true,
            'image_optimization' => true,
            'cdn_enabled' => false,
            'cdn_url' => '',
            'performance_monitoring' => true
        ], $config);
        
        $this->metrics = [];
        $this->initializeOptimizations();
    }
    
    /**
     * Initialize performance optimizations
     */
    private function initializeOptimizations() {
        // Enable output compression
        if ($this->config['compression_enabled'] && !ob_get_level()) {
            ob_start('ob_gzhandler');
        }
        
        // Set performance headers
        $this->setPerformanceHeaders();
        
        // Initialize metrics collection
        if ($this->config['performance_monitoring']) {
            $this->startMetricsCollection();
        }
    }
    
    /**
     * Set performance-related HTTP headers
     */
    private function setPerformanceHeaders() {
        // Cache control headers
        if (!headers_sent()) {
            header('X-Powered-By: Beersty Digital Board System');
            header('X-Performance-Optimized: true');
            
            // Enable browser caching for static assets
            if ($this->isStaticAsset()) {
                header('Cache-Control: public, max-age=31536000, immutable');
                header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
            }
            
            // Preload critical resources
            $this->addPreloadHeaders();
        }
    }
    
    /**
     * Add resource preload headers
     */
    private function addPreloadHeaders() {
        $preloadResources = [
            '/assets/css/digital-board-mobile.css' => 'style',
            '/assets/js/digital-board-mobile.js' => 'script',
            '/assets/images/beersty-logo.png' => 'image'
        ];
        
        foreach ($preloadResources as $resource => $type) {
            header("Link: <{$resource}>; rel=preload; as={$type}", false);
        }
    }
    
    /**
     * Cache management
     */
    public function cache($key, $data, $ttl = null) {
        if (!$this->config['cache_enabled'] || !$this->redis) {
            return false;
        }
        
        $ttl = $ttl ?? $this->config['cache_ttl'];
        $serializedData = serialize($data);
        
        // Compress data if large
        if (strlen($serializedData) > 1024) {
            $serializedData = gzcompress($serializedData, 6);
            $key = $key . ':compressed';
        }
        
        return $this->redis->setex($key, $ttl, $serializedData);
    }
    
    /**
     * Retrieve from cache
     */
    public function getCache($key) {
        if (!$this->config['cache_enabled'] || !$this->redis) {
            return null;
        }
        
        $data = $this->redis->get($key);
        if ($data === false) {
            // Try compressed version
            $data = $this->redis->get($key . ':compressed');
            if ($data !== false) {
                $data = gzuncompress($data);
            }
        }
        
        return $data !== false ? unserialize($data) : null;
    }
    
    /**
     * Clear cache by pattern
     */
    public function clearCache($pattern = '*') {
        if (!$this->redis) return false;
        
        $keys = $this->redis->keys($pattern);
        if (!empty($keys)) {
            return $this->redis->del($keys);
        }
        
        return true;
    }
    
    /**
     * Database query optimization
     */
    public function optimizeQuery($query, $params = [], $cacheKey = null, $ttl = null) {
        $startTime = microtime(true);
        
        // Check cache first
        if ($cacheKey && $this->config['cache_enabled']) {
            $cached = $this->getCache($cacheKey);
            if ($cached !== null) {
                $this->recordMetric('query_cache_hit', microtime(true) - $startTime);
                return $cached;
            }
        }
        
        // Execute query (this would be called from your database class)
        $result = $this->executeQuery($query, $params);
        
        // Cache result
        if ($cacheKey && $result) {
            $this->cache($cacheKey, $result, $ttl);
        }
        
        $this->recordMetric('query_execution', microtime(true) - $startTime);
        return $result;
    }
    
    /**
     * Image optimization
     */
    public function optimizeImage($imagePath, $options = []) {
        if (!$this->config['image_optimization']) {
            return $imagePath;
        }
        
        $options = array_merge([
            'quality' => 85,
            'format' => 'webp',
            'resize' => null,
            'cache' => true
        ], $options);
        
        $optimizedPath = $this->getOptimizedImagePath($imagePath, $options);
        
        // Check if optimized version exists
        if (file_exists($optimizedPath) && $options['cache']) {
            return $this->getCDNUrl($optimizedPath);
        }
        
        // Optimize image
        $this->processImageOptimization($imagePath, $optimizedPath, $options);
        
        return $this->getCDNUrl($optimizedPath);
    }
    
    /**
     * CSS/JS minification
     */
    public function minifyAsset($content, $type = 'css') {
        if (!$this->config['minification_enabled']) {
            return $content;
        }
        
        switch ($type) {
            case 'css':
                return $this->minifyCSS($content);
            case 'js':
                return $this->minifyJS($content);
            default:
                return $content;
        }
    }
    
    /**
     * Minify CSS content
     */
    private function minifyCSS($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        $css = preg_replace('/\s*([{}|:;,>+~])\s*/', '$1', $css);
        
        // Remove trailing semicolon before closing brace
        $css = str_replace(';}', '}', $css);
        
        return trim($css);
    }
    
    /**
     * Minify JavaScript content
     */
    private function minifyJS($js) {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);
        $js = preg_replace('/\s*([{}|:;,>+~=()])\s*/', '$1', $js);
        
        return trim($js);
    }
    
    /**
     * Asset bundling and versioning
     */
    public function bundleAssets($assets, $type = 'css') {
        $bundleKey = md5(implode('|', $assets) . $type);
        $bundlePath = "assets/bundles/{$bundleKey}.{$type}";
        
        // Check if bundle exists
        if (file_exists($bundlePath)) {
            return $this->getCDNUrl($bundlePath);
        }
        
        $bundledContent = '';
        foreach ($assets as $asset) {
            if (file_exists($asset)) {
                $content = file_get_contents($asset);
                $bundledContent .= $this->minifyAsset($content, $type) . "\n";
            }
        }
        
        // Create bundle directory if it doesn't exist
        $bundleDir = dirname($bundlePath);
        if (!is_dir($bundleDir)) {
            mkdir($bundleDir, 0755, true);
        }
        
        // Save bundle
        file_put_contents($bundlePath, $bundledContent);
        
        return $this->getCDNUrl($bundlePath);
    }
    
    /**
     * Lazy loading implementation
     */
    public function addLazyLoading($html) {
        // Add lazy loading to images
        $html = preg_replace(
            '/<img([^>]*?)src=(["\'])([^"\']*?)\2([^>]*?)>/i',
            '<img$1data-src=$2$3$2 src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1 1\'%3E%3C/svg%3E" loading="lazy"$4>',
            $html
        );
        
        // Add lazy loading script
        $html .= '<script>
            document.addEventListener("DOMContentLoaded", function() {
                const images = document.querySelectorAll("img[data-src]");
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.removeAttribute("data-src");
                            observer.unobserve(img);
                        }
                    });
                });
                images.forEach(img => imageObserver.observe(img));
            });
        </script>';
        
        return $html;
    }
    
    /**
     * Performance monitoring
     */
    private function startMetricsCollection() {
        $this->metrics['start_time'] = microtime(true);
        $this->metrics['memory_start'] = memory_get_usage(true);
    }
    
    /**
     * Record performance metric
     */
    public function recordMetric($name, $value) {
        $this->metrics[$name] = $value;
        
        // Store in Redis for monitoring
        if ($this->redis) {
            $metricKey = "metrics:{$name}:" . date('Y-m-d-H');
            $this->redis->lpush($metricKey, json_encode([
                'value' => $value,
                'timestamp' => time()
            ]));
            $this->redis->expire($metricKey, 86400); // Keep for 24 hours
        }
    }
    
    /**
     * Get performance report
     */
    public function getPerformanceReport() {
        $endTime = microtime(true);
        $memoryEnd = memory_get_usage(true);
        
        return [
            'execution_time' => $endTime - $this->metrics['start_time'],
            'memory_usage' => $memoryEnd - $this->metrics['memory_start'],
            'peak_memory' => memory_get_peak_usage(true),
            'queries_executed' => $this->metrics['query_execution'] ?? 0,
            'cache_hits' => $this->metrics['query_cache_hit'] ?? 0,
            'timestamp' => date('c')
        ];
    }
    
    /**
     * Critical CSS extraction
     */
    public function extractCriticalCSS($html, $css) {
        // Simple critical CSS extraction (in production, use a more sophisticated tool)
        $criticalSelectors = [];
        
        // Extract selectors used in the HTML
        preg_match_all('/class=["\']([^"\']*)["\']/', $html, $classes);
        preg_match_all('/id=["\']([^"\']*)["\']/', $html, $ids);
        
        foreach ($classes[1] as $classList) {
            $classNames = explode(' ', $classList);
            foreach ($classNames as $className) {
                if (!empty($className)) {
                    $criticalSelectors[] = '.' . $className;
                }
            }
        }
        
        foreach ($ids[1] as $id) {
            if (!empty($id)) {
                $criticalSelectors[] = '#' . $id;
            }
        }
        
        // Extract matching CSS rules (simplified)
        $criticalCSS = '';
        foreach ($criticalSelectors as $selector) {
            $pattern = '/' . preg_quote($selector, '/') . '\s*\{[^}]*\}/';
            if (preg_match($pattern, $css, $matches)) {
                $criticalCSS .= $matches[0] . "\n";
            }
        }
        
        return $this->minifyCSS($criticalCSS);
    }
    
    /**
     * Helper methods
     */
    private function isStaticAsset() {
        $path = $_SERVER['REQUEST_URI'] ?? '';
        return preg_match('/\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$/i', $path);
    }
    
    private function getCDNUrl($path) {
        if ($this->config['cdn_enabled'] && !empty($this->config['cdn_url'])) {
            return $this->config['cdn_url'] . '/' . ltrim($path, '/');
        }
        return $path;
    }
    
    private function getOptimizedImagePath($imagePath, $options) {
        $pathInfo = pathinfo($imagePath);
        $hash = md5($imagePath . serialize($options));
        return $pathInfo['dirname'] . '/optimized/' . $pathInfo['filename'] . '_' . $hash . '.' . $options['format'];
    }
    
    private function processImageOptimization($source, $destination, $options) {
        // Create directory if it doesn't exist
        $dir = dirname($destination);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Simple image optimization (in production, use ImageMagick or similar)
        if (function_exists('imagecreatefromstring')) {
            $image = imagecreatefromstring(file_get_contents($source));
            if ($image) {
                // Resize if specified
                if ($options['resize']) {
                    $image = $this->resizeImage($image, $options['resize']);
                }
                
                // Save in specified format
                switch ($options['format']) {
                    case 'webp':
                        imagewebp($image, $destination, $options['quality']);
                        break;
                    case 'jpeg':
                        imagejpeg($image, $destination, $options['quality']);
                        break;
                    case 'png':
                        imagepng($image, $destination, 9 - round($options['quality'] / 10));
                        break;
                }
                
                imagedestroy($image);
            }
        } else {
            // Fallback: copy original file
            copy($source, $destination);
        }
    }
    
    private function resizeImage($image, $size) {
        $width = imagesx($image);
        $height = imagesy($image);
        
        list($newWidth, $newHeight) = explode('x', $size);
        
        $resized = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        return $resized;
    }
    
    private function executeQuery($query, $params) {
        // This would be implemented by your database class
        // Placeholder for demonstration
        return [];
    }
}
