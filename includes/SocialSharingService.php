<?php
/**
 * Social Sharing Service
 * Phase 10: Advanced Features & API Development
 * Handle social media sharing and integration
 */

class SocialSharingService {
    private $conn;
    private $baseUrl;
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->baseUrl = BASE_URL;
    }
    
    /**
     * Generate sharing URLs for different platforms
     */
    public function generateSharingUrls($type, $id, $customMessage = null) {
        $shareData = $this->getShareData($type, $id);
        
        if (!$shareData) {
            return null;
        }
        
        $message = $customMessage ?: $shareData['default_message'];
        $url = $shareData['url'];
        $title = $shareData['title'];
        $description = $shareData['description'];
        $image = $shareData['image'];
        
        return [
            'facebook' => $this->getFacebookShareUrl($url, $title, $description),
            'twitter' => $this->getTwitterShareUrl($url, $message),
            'instagram' => $this->getInstagramShareUrl($message, $image),
            'linkedin' => $this->getLinkedInShareUrl($url, $title, $description),
            'whatsapp' => $this->getWhatsAppShareUrl($message, $url),
            'telegram' => $this->getTelegramShareUrl($message, $url),
            'reddit' => $this->getRedditShareUrl($url, $title),
            'pinterest' => $this->getPinterestShareUrl($url, $description, $image),
            'email' => $this->getEmailShareUrl($title, $message, $url),
            'copy_link' => $url
        ];
    }
    
    /**
     * Get share data based on content type
     */
    private function getShareData($type, $id) {
        switch ($type) {
            case 'beer':
                return $this->getBeerShareData($id);
            case 'brewery':
                return $this->getBreweryShareData($id);
            case 'checkin':
                return $this->getCheckinShareData($id);
            case 'rating':
                return $this->getRatingShareData($id);
            case 'user_profile':
                return $this->getUserProfileShareData($id);
            case 'year_review':
                return $this->getYearReviewShareData($id);
            default:
                return null;
        }
    }
    
    /**
     * Get beer sharing data
     */
    private function getBeerShareData($beerId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    bm.name,
                    bm.description,
                    bm.abv,
                    bm.images,
                    b.name as brewery_name,
                    bs.name as style_name,
                    COALESCE(AVG(br.overall_rating), 0) as avg_rating
                FROM beer_menu bm
                JOIN breweries b ON bm.brewery_id = b.id
                LEFT JOIN beer_styles bs ON bm.beer_style_id = bs.id
                LEFT JOIN beer_ratings br ON bm.id = br.beer_id
                WHERE bm.id = ?
                GROUP BY bm.id
            ");
            
            $stmt->execute([$beerId]);
            $beer = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$beer) {
                return null;
            }
            
            $images = json_decode($beer['images'], true) ?? [];
            $image = !empty($images) ? $this->baseUrl . '/uploads/' . $images[0] : null;
            
            return [
                'url' => $this->baseUrl . '/beers/detail.php?id=' . $beerId,
                'title' => $beer['name'] . ' by ' . $beer['brewery_name'],
                'description' => $beer['description'] ?: 'A ' . $beer['style_name'] . ' with ' . $beer['abv'] . '% ABV',
                'image' => $image,
                'default_message' => "Check out this amazing beer: {$beer['name']} by {$beer['brewery_name']}! 🍺 #Beersty #CraftBeer"
            ];
            
        } catch (Exception $e) {
            error_log("Beer share data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get brewery sharing data
     */
    private function getBreweryShareData($breweryId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT name, description, address, images
                FROM breweries
                WHERE id = ?
            ");
            
            $stmt->execute([$breweryId]);
            $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$brewery) {
                return null;
            }
            
            $images = json_decode($brewery['images'], true) ?? [];
            $image = !empty($images) ? $this->baseUrl . '/uploads/' . $images[0] : null;
            
            return [
                'url' => $this->baseUrl . '/breweries/profile.php?id=' . $breweryId,
                'title' => $brewery['name'],
                'description' => $brewery['description'] ?: 'Craft brewery located in ' . $brewery['address'],
                'image' => $image,
                'default_message' => "Discovered an amazing brewery: {$brewery['name']}! 🍺 #Beersty #CraftBeer #Brewery"
            ];
            
        } catch (Exception $e) {
            error_log("Brewery share data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get check-in sharing data
     */
    private function getCheckinShareData($checkinId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    bc.*,
                    bm.name as beer_name,
                    b.name as brewery_name,
                    CONCAT(p.first_name, ' ', p.last_name) as user_name
                FROM beer_checkins bc
                JOIN beer_menu bm ON bc.beer_id = bm.id
                JOIN breweries b ON bm.brewery_id = b.id
                JOIN users u ON bc.user_id = u.id
                JOIN profiles p ON u.id = p.id
                WHERE bc.id = ?
            ");
            
            $stmt->execute([$checkinId]);
            $checkin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$checkin) {
                return null;
            }
            
            $rating = $checkin['rating'] ? " (★ {$checkin['rating']}/5)" : '';
            $location = $checkin['location'] ? " at {$checkin['location']}" : '';
            
            return [
                'url' => $this->baseUrl . '/social/checkin.php?id=' . $checkinId,
                'title' => "{$checkin['user_name']} checked in to {$checkin['beer_name']}",
                'description' => "Just enjoyed {$checkin['beer_name']} by {$checkin['brewery_name']}{$rating}{$location}",
                'image' => null,
                'default_message' => "Just checked in to {$checkin['beer_name']} by {$checkin['brewery_name']}{$rating}! 🍺 #Beersty #BeerCheckin"
            ];
            
        } catch (Exception $e) {
            error_log("Checkin share data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get user profile sharing data
     */
    private function getUserProfileShareData($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    CONCAT(p.first_name, ' ', p.last_name) as full_name,
                    p.bio,
                    us.total_checkins,
                    us.unique_beers_tried,
                    us.total_badges_earned
                FROM users u
                JOIN profiles p ON u.id = p.id
                LEFT JOIN user_statistics us ON u.id = us.user_id
                WHERE u.id = ?
            ");
            
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return null;
            }
            
            return [
                'url' => $this->baseUrl . '/user/profile.php?id=' . $userId,
                'title' => "{$user['full_name']}'s Beer Profile",
                'description' => "Beer enthusiast with {$user['total_checkins']} check-ins, {$user['unique_beers_tried']} unique beers, and {$user['total_badges_earned']} badges!",
                'image' => null,
                'default_message' => "Check out my beer journey on Beersty! 🍺 {$user['total_checkins']} check-ins and counting! #Beersty #BeerLife"
            ];
            
        } catch (Exception $e) {
            error_log("User profile share data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Generate platform-specific sharing URLs
     */
    private function getFacebookShareUrl($url, $title, $description) {
        return 'https://www.facebook.com/sharer/sharer.php?' . http_build_query([
            'u' => $url,
            'quote' => $title . ' - ' . $description
        ]);
    }
    
    private function getTwitterShareUrl($url, $message) {
        return 'https://twitter.com/intent/tweet?' . http_build_query([
            'text' => $message,
            'url' => $url
        ]);
    }
    
    private function getInstagramShareUrl($message, $image) {
        // Instagram doesn't support direct URL sharing, return message for manual sharing
        return [
            'type' => 'manual',
            'message' => $message,
            'image' => $image,
            'instructions' => 'Copy this message and share manually on Instagram'
        ];
    }
    
    private function getLinkedInShareUrl($url, $title, $description) {
        return 'https://www.linkedin.com/sharing/share-offsite/?' . http_build_query([
            'url' => $url,
            'title' => $title,
            'summary' => $description
        ]);
    }
    
    private function getWhatsAppShareUrl($message, $url) {
        return 'https://wa.me/?' . http_build_query([
            'text' => $message . ' ' . $url
        ]);
    }
    
    private function getTelegramShareUrl($message, $url) {
        return 'https://t.me/share/url?' . http_build_query([
            'url' => $url,
            'text' => $message
        ]);
    }
    
    private function getRedditShareUrl($url, $title) {
        return 'https://reddit.com/submit?' . http_build_query([
            'url' => $url,
            'title' => $title
        ]);
    }
    
    private function getPinterestShareUrl($url, $description, $image) {
        return 'https://pinterest.com/pin/create/button/?' . http_build_query([
            'url' => $url,
            'description' => $description,
            'media' => $image
        ]);
    }
    
    private function getEmailShareUrl($title, $message, $url) {
        return 'mailto:?' . http_build_query([
            'subject' => $title,
            'body' => $message . "\n\n" . $url
        ]);
    }
    
    /**
     * Track sharing activity
     */
    public function trackShare($userId, $type, $contentId, $platform) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO social_shares (user_id, content_type, content_id, platform, shared_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([$userId, $type, $contentId, $platform]);
            
            // Update user activity
            $this->logUserActivity($userId, 'social_share', [
                'content_type' => $type,
                'content_id' => $contentId,
                'platform' => $platform
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Track share error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get sharing statistics
     */
    public function getSharingStats($type = null, $contentId = null, $timeframe = '30d') {
        try {
            $interval = match($timeframe) {
                '24h' => 'INTERVAL 24 HOUR',
                '7d' => 'INTERVAL 7 DAY',
                '30d' => 'INTERVAL 30 DAY',
                '1y' => 'INTERVAL 1 YEAR',
                default => 'INTERVAL 30 DAY'
            };
            
            $where = ['shared_at > DATE_SUB(NOW(), ' . $interval . ')'];
            $params = [];
            
            if ($type) {
                $where[] = 'content_type = ?';
                $params[] = $type;
            }
            
            if ($contentId) {
                $where[] = 'content_id = ?';
                $params[] = $contentId;
            }
            
            $whereClause = implode(' AND ', $where);
            
            $stmt = $this->conn->prepare("
                SELECT 
                    platform,
                    COUNT(*) as share_count,
                    COUNT(DISTINCT user_id) as unique_users
                FROM social_shares
                WHERE $whereClause
                GROUP BY platform
                ORDER BY share_count DESC
            ");
            
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Sharing stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Log user activity
     */
    private function logUserActivity($userId, $activityType, $data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO user_activities (user_id, activity_type, activity_data, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            
            $stmt->execute([$userId, $activityType, json_encode($data)]);
            
        } catch (Exception $e) {
            error_log("Log user activity error: " . $e->getMessage());
        }
    }
}
