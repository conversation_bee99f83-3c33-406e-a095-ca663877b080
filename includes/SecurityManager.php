<?php
/**
 * Security Manager
 * Phase 8 - Production Deployment & Optimization
 * 
 * Comprehensive security hardening and protection system
 */

class SecurityManager {
    private $config;
    private $redis;
    private $logger;
    
    public function __construct($redisConnection = null, $config = []) {
        $this->redis = $redisConnection;
        $this->config = array_merge([
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 100,
            'rate_limit_window' => 60,
            'csrf_protection' => true,
            'xss_protection' => true,
            'sql_injection_protection' => true,
            'file_upload_security' => true,
            'session_security' => true,
            'ip_whitelist' => [],
            'ip_blacklist' => [],
            'security_headers' => true,
            'content_security_policy' => true,
            'brute_force_protection' => true,
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 minutes
        ], $config);
        
        $this->initializeSecurity();
    }
    
    /**
     * Initialize security measures
     */
    private function initializeSecurity() {
        // Set security headers
        if ($this->config['security_headers']) {
            $this->setSecurityHeaders();
        }
        
        // Initialize session security
        if ($this->config['session_security']) {
            $this->initializeSessionSecurity();
        }
        
        // Start request monitoring
        $this->startRequestMonitoring();
    }
    
    /**
     * Set comprehensive security headers
     */
    private function setSecurityHeaders() {
        if (headers_sent()) return;
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // Enable XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // HSTS (HTTP Strict Transport Security)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Content Security Policy
        if ($this->config['content_security_policy']) {
            $this->setContentSecurityPolicy();
        }
        
        // Permissions Policy
        header('Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()');
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
    }
    
    /**
     * Set Content Security Policy
     */
    private function setContentSecurityPolicy() {
        $csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "img-src 'self' data: https: blob:",
            "font-src 'self' https://cdnjs.cloudflare.com",
            "connect-src 'self' https://api.openweathermap.org https://api.eventbrite.com",
            "media-src 'self' blob:",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests"
        ];
        
        header('Content-Security-Policy: ' . implode('; ', $csp));
    }
    
    /**
     * Initialize secure session configuration
     */
    private function initializeSessionSecurity() {
        if (session_status() === PHP_SESSION_NONE) {
            // Secure session configuration
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.use_strict_mode', 1);
            ini_set('session.use_only_cookies', 1);
            ini_set('session.cookie_lifetime', 0);
            
            // Regenerate session ID periodically
            if (isset($_SESSION['last_regeneration'])) {
                if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                    session_regenerate_id(true);
                    $_SESSION['last_regeneration'] = time();
                }
            } else {
                $_SESSION['last_regeneration'] = time();
            }
        }
    }
    
    /**
     * Rate limiting implementation
     */
    public function checkRateLimit($identifier = null, $action = 'general') {
        if (!$this->config['rate_limit_enabled'] || !$this->redis) {
            return ['allowed' => true, 'remaining' => $this->config['rate_limit_requests']];
        }
        
        $identifier = $identifier ?: $this->getClientIdentifier();
        $key = "rate_limit:{$action}:{$identifier}";
        $window = $this->config['rate_limit_window'];
        $limit = $this->config['rate_limit_requests'];
        
        // Get current count
        $current = $this->redis->get($key);
        
        if ($current === false) {
            // First request in window
            $this->redis->setex($key, $window, 1);
            return ['allowed' => true, 'remaining' => $limit - 1];
        }
        
        if ($current >= $limit) {
            // Rate limit exceeded
            $ttl = $this->redis->ttl($key);
            return [
                'allowed' => false,
                'remaining' => 0,
                'reset_time' => time() + $ttl,
                'retry_after' => $ttl
            ];
        }
        
        // Increment counter
        $this->redis->incr($key);
        
        return ['allowed' => true, 'remaining' => $limit - $current - 1];
    }
    
    /**
     * CSRF protection
     */
    public function generateCSRFToken() {
        if (!$this->config['csrf_protection']) return null;
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();
        
        return $token;
    }
    
    /**
     * Validate CSRF token
     */
    public function validateCSRFToken($token) {
        if (!$this->config['csrf_protection']) return true;
        
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Check token age (expire after 1 hour)
        if (time() - $_SESSION['csrf_token_time'] > 3600) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Input sanitization and validation
     */
    public function sanitizeInput($input, $type = 'string') {
        switch ($type) {
            case 'string':
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            
            case 'url':
                return filter_var(trim($input), FILTER_SANITIZE_URL);
            
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            
            case 'html':
                return $this->sanitizeHTML($input);
            
            case 'sql':
                return $this->sanitizeSQL($input);
            
            default:
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
    }
    
    /**
     * HTML sanitization
     */
    private function sanitizeHTML($html) {
        // Allow only safe HTML tags
        $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
        return strip_tags($html, $allowedTags);
    }
    
    /**
     * SQL injection protection
     */
    private function sanitizeSQL($input) {
        // Remove common SQL injection patterns
        $patterns = [
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)(\s|$)/i',
            '/(\s|^)(or|and)(\s|$)(\d+(\s|$)=(\s|$)\d+|\'\w*\'(\s|$)=(\s|$)\'\w*\')/i',
            '/(\s|^)(\'|\"|;|--|\#|\*|\/\*|\*\/)/i'
        ];
        
        foreach ($patterns as $pattern) {
            $input = preg_replace($pattern, '', $input);
        }
        
        return trim($input);
    }
    
    /**
     * File upload security
     */
    public function validateFileUpload($file, $allowedTypes = [], $maxSize = null) {
        if (!$this->config['file_upload_security']) {
            return ['valid' => true];
        }
        
        $errors = [];
        
        // Check file size
        $maxSize = $maxSize ?: (int)ini_get('upload_max_filesize') * 1024 * 1024;
        if ($file['size'] > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
            $errors[] = 'File type not allowed';
        }
        
        // Check for malicious content
        if ($this->containsMaliciousContent($file['tmp_name'])) {
            $errors[] = 'File contains potentially malicious content';
        }
        
        // Validate file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $dangerousExtensions = ['php', 'phtml', 'php3', 'php4', 'php5', 'pl', 'py', 'jsp', 'asp', 'sh', 'cgi'];
        
        if (in_array($extension, $dangerousExtensions)) {
            $errors[] = 'File extension not allowed';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'mime_type' => $mimeType,
            'safe_filename' => $this->generateSafeFilename($file['name'])
        ];
    }
    
    /**
     * Brute force protection
     */
    public function checkBruteForce($identifier, $action = 'login') {
        if (!$this->config['brute_force_protection'] || !$this->redis) {
            return ['allowed' => true];
        }
        
        $key = "brute_force:{$action}:{$identifier}";
        $attempts = $this->redis->get($key);
        
        if ($attempts >= $this->config['max_login_attempts']) {
            $ttl = $this->redis->ttl($key);
            return [
                'allowed' => false,
                'attempts' => $attempts,
                'lockout_remaining' => $ttl
            ];
        }
        
        return ['allowed' => true, 'attempts' => $attempts ?: 0];
    }
    
    /**
     * Record failed attempt
     */
    public function recordFailedAttempt($identifier, $action = 'login') {
        if (!$this->redis) return;
        
        $key = "brute_force:{$action}:{$identifier}";
        $attempts = $this->redis->incr($key);
        
        if ($attempts === 1) {
            $this->redis->expire($key, $this->config['lockout_duration']);
        }
        
        // Log security event
        $this->logSecurityEvent('failed_attempt', [
            'identifier' => $identifier,
            'action' => $action,
            'attempts' => $attempts,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    }
    
    /**
     * Clear failed attempts (on successful login)
     */
    public function clearFailedAttempts($identifier, $action = 'login') {
        if (!$this->redis) return;
        
        $key = "brute_force:{$action}:{$identifier}";
        $this->redis->del($key);
    }
    
    /**
     * IP address validation and filtering
     */
    public function validateIPAddress($ip = null) {
        $ip = $ip ?: $this->getClientIP();
        
        // Check whitelist
        if (!empty($this->config['ip_whitelist'])) {
            return in_array($ip, $this->config['ip_whitelist']);
        }
        
        // Check blacklist
        if (!empty($this->config['ip_blacklist'])) {
            return !in_array($ip, $this->config['ip_blacklist']);
        }
        
        // Check for suspicious patterns
        return !$this->isSuspiciousIP($ip);
    }
    
    /**
     * Password strength validation
     */
    public function validatePasswordStrength($password) {
        $score = 0;
        $feedback = [];
        
        // Length check
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'Password must be at least 8 characters long';
        }
        
        // Uppercase check
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password must contain at least one uppercase letter';
        }
        
        // Lowercase check
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password must contain at least one lowercase letter';
        }
        
        // Number check
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password must contain at least one number';
        }
        
        // Special character check
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password must contain at least one special character';
        }
        
        // Common password check
        if ($this->isCommonPassword($password)) {
            $score -= 2;
            $feedback[] = 'Password is too common';
        }
        
        return [
            'score' => max(0, $score),
            'strength' => $this->getPasswordStrengthLabel($score),
            'valid' => $score >= 4,
            'feedback' => $feedback
        ];
    }
    
    /**
     * Security event logging
     */
    public function logSecurityEvent($event, $data = []) {
        $logEntry = [
            'timestamp' => date('c'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data
        ];
        
        // Log to file
        $logFile = 'logs/security_' . date('Y-m-d') . '.log';
        error_log(json_encode($logEntry) . "\n", 3, $logFile);
        
        // Store in Redis for real-time monitoring
        if ($this->redis) {
            $this->redis->lpush('security_events', json_encode($logEntry));
            $this->redis->ltrim('security_events', 0, 999); // Keep last 1000 events
        }
    }
    
    /**
     * Helper methods
     */
    private function getClientIdentifier() {
        return hash('sha256', $this->getClientIP() . ($_SERVER['HTTP_USER_AGENT'] ?? ''));
    }
    
    private function getClientIP() {
        $headers = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    private function startRequestMonitoring() {
        // Monitor request patterns for anomalies
        if ($this->redis) {
            $ip = $this->getClientIP();
            $key = "request_monitor:{$ip}:" . date('Y-m-d-H');
            $this->redis->incr($key);
            $this->redis->expire($key, 3600);
        }
    }
    
    private function containsMaliciousContent($filePath) {
        $content = file_get_contents($filePath, false, null, 0, 1024); // Read first 1KB
        
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec/i',
            '/base64_decode/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function generateSafeFilename($filename) {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $basename = pathinfo($filename, PATHINFO_FILENAME);
        
        // Remove dangerous characters
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = substr($basename, 0, 50); // Limit length
        
        return $basename . '_' . uniqid() . '.' . $extension;
    }
    
    private function isSuspiciousIP($ip) {
        // Check against known malicious IP patterns
        $suspiciousPatterns = [
            '/^10\./',      // Private networks (if not expected)
            '/^192\.168\./', // Private networks (if not expected)
            '/^172\.16\./',  // Private networks (if not expected)
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $ip)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function isCommonPassword($password) {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }
    
    private function getPasswordStrengthLabel($score) {
        if ($score >= 5) return 'Very Strong';
        if ($score >= 4) return 'Strong';
        if ($score >= 3) return 'Medium';
        if ($score >= 2) return 'Weak';
        return 'Very Weak';
    }
}
