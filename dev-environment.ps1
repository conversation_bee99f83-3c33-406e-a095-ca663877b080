# Beersty Development Environment Manager
# Comprehensive script for managing the development environment

param(
    [Parameter(Position=0)]
    [ValidateSet("setup", "start", "stop", "test", "db-check", "fix-permissions", "help")]
    [string]$Command = "help"
)

# Configuration
$projectName = "beersty-lovable"
$dbName = "beersty_db"

# Find XAMPP
$xamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp", "E:\xampp")
$xamppPath = $xamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $xamppPath -and $Command -ne "help") {
    Write-Host "❌ XAMPP not found. Please install XAMPP first." -ForegroundColor Red
    Write-Host "   Download from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    exit 1
}

function Write-Header {
    param($Title)
    Write-Host ""
    Write-Host "🍺 $Title" -ForegroundColor Yellow
    Write-Host ("=" * ($Title.Length + 3)) -ForegroundColor Yellow
}

function Test-ServiceRunning {
    param($ProcessName)
    return (Get-Process -Name $ProcessName -ErrorAction SilentlyContinue) -ne $null
}

function Start-XamppServices {
    Write-Header "Starting XAMPP Services"
    
    $xamppControl = Join-Path $xamppPath "xampp-control.exe"
    if (Test-Path $xamppControl) {
        Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Cyan
        Start-Process $xamppControl -WindowStyle Normal
        Start-Sleep 5
        
        # Check services
        if (Test-ServiceRunning "httpd") {
            Write-Host "✅ Apache is running" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Apache not detected. Please start it manually in XAMPP Control Panel." -ForegroundColor Yellow
        }
        
        if (Test-ServiceRunning "mysqld") {
            Write-Host "✅ MySQL is running" -ForegroundColor Green
        } else {
            Write-Host "⚠️  MySQL not detected. Please start it manually in XAMPP Control Panel." -ForegroundColor Yellow
        }
    }
}

function Stop-XamppServices {
    Write-Header "Stopping XAMPP Services"
    
    # Stop processes
    Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue | Stop-Process -Force
    
    Write-Host "✅ Services stopped" -ForegroundColor Green
}

function Test-Environment {
    Write-Header "Testing Development Environment"
    
    # Test web server
    Write-Host "Testing web server..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ Web server responding (Status: $($response.StatusCode))" -ForegroundColor Green
    } catch {
        Write-Host "❌ Web server not responding" -ForegroundColor Red
    }
    
    # Test project
    Write-Host "Testing project..." -ForegroundColor Cyan
    try {
        $projectUrl = "http://localhost/$projectName"
        $response = Invoke-WebRequest -Uri $projectUrl -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ Project accessible at $projectUrl" -ForegroundColor Green
    } catch {
        Write-Host "❌ Project not accessible. Check if files are in htdocs/$projectName" -ForegroundColor Red
    }
    
    # Test MySQL
    Write-Host "Testing MySQL..." -ForegroundColor Cyan
    $mysqlClient = Join-Path $xamppPath "mysql\bin\mysql.exe"
    if (Test-Path $mysqlClient) {
        try {
            $result = & $mysqlClient -u root -e "SELECT VERSION();" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ MySQL connection successful" -ForegroundColor Green
            } else {
                Write-Host "❌ MySQL connection failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ MySQL test failed" -ForegroundColor Red
        }
    }
    
    # Test phpMyAdmin
    Write-Host "Testing phpMyAdmin..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/phpmyadmin" -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ phpMyAdmin accessible" -ForegroundColor Green
    } catch {
        Write-Host "❌ phpMyAdmin not accessible" -ForegroundColor Red
    }
}

function Test-Database {
    Write-Header "Checking Database"
    
    $mysqlClient = Join-Path $xamppPath "mysql\bin\mysql.exe"
    if (-not (Test-Path $mysqlClient)) {
        Write-Host "❌ MySQL client not found" -ForegroundColor Red
        return
    }
    
    # Check if database exists
    Write-Host "Checking database '$dbName'..." -ForegroundColor Cyan
    try {
        $result = & $mysqlClient -u root -e "USE $dbName; SHOW TABLES;" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database '$dbName' exists" -ForegroundColor Green
            
            # Check users table
            $userTableCheck = & $mysqlClient -u root -e "USE $dbName; DESCRIBE users;" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Users table exists" -ForegroundColor Green
            } else {
                Write-Host "❌ Users table not found" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Database '$dbName' not found" -ForegroundColor Red
            Write-Host "   You may need to create it manually or run database setup" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Database check failed" -ForegroundColor Red
    }
}

function Setup-Environment {
    Write-Header "Setting Up Development Environment"
    
    # Check project location
    $htdocsPath = Join-Path $xamppPath "htdocs"
    $projectPath = Join-Path $htdocsPath $projectName
    
    Write-Host "Checking project location..." -ForegroundColor Cyan
    if (Test-Path $projectPath) {
        Write-Host "✅ Project found at: $projectPath" -ForegroundColor Green
    } else {
        Write-Host "❌ Project not found at: $projectPath" -ForegroundColor Red
        Write-Host "   Current directory: $(Get-Location)" -ForegroundColor Yellow
        
        $copyProject = Read-Host "Copy current directory to htdocs? (y/n)"
        if ($copyProject -eq "y" -or $copyProject -eq "Y") {
            try {
                Copy-Item -Path "." -Destination $projectPath -Recurse -Force
                Write-Host "✅ Project copied to htdocs" -ForegroundColor Green
            } catch {
                Write-Host "❌ Failed to copy project: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    # Start services
    Start-XamppServices
    
    # Wait and test
    Start-Sleep 5
    Test-Environment
}

function Fix-Permissions {
    Write-Header "Fixing File Permissions"
    
    $htdocsPath = Join-Path $xamppPath "htdocs"
    $projectPath = Join-Path $htdocsPath $projectName
    
    if (Test-Path $projectPath) {
        try {
            # Give full control to current user
            $acl = Get-Acl $projectPath
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($env:USERNAME, "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            Set-Acl -Path $projectPath -AclObject $acl
            
            Write-Host "✅ Permissions updated for $projectPath" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to update permissions: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Project path not found: $projectPath" -ForegroundColor Red
    }
}

function Show-Help {
    Write-Header "Beersty Development Environment Manager"
    
    Write-Host "Usage: .\dev-environment.ps1 [command]" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  setup          - Complete environment setup" -ForegroundColor White
    Write-Host "  start          - Start XAMPP services" -ForegroundColor White
    Write-Host "  stop           - Stop XAMPP services" -ForegroundColor White
    Write-Host "  test           - Test all services" -ForegroundColor White
    Write-Host "  db-check       - Check database status" -ForegroundColor White
    Write-Host "  fix-permissions- Fix file permissions" -ForegroundColor White
    Write-Host "  help           - Show this help" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\dev-environment.ps1 setup" -ForegroundColor Gray
    Write-Host "  .\dev-environment.ps1 start" -ForegroundColor Gray
    Write-Host "  .\dev-environment.ps1 test" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Quick URLs after starting:" -ForegroundColor Yellow
    Write-Host "  Main Site: http://localhost/$projectName" -ForegroundColor Gray
    Write-Host "  User Admin: http://localhost/$projectName/admin/user-management.php" -ForegroundColor Gray
    Write-Host "  phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor Gray
}

# Execute command
switch ($Command) {
    "setup" { Setup-Environment }
    "start" { Start-XamppServices }
    "stop" { Stop-XamppServices }
    "test" { Test-Environment }
    "db-check" { Test-Database }
    "fix-permissions" { Fix-Permissions }
    "help" { Show-Help }
    default { Show-Help }
}

if ($Command -ne "help") {
    Write-Host ""
    Write-Host "💡 Run '.\dev-environment.ps1 help' for more options" -ForegroundColor Gray
}
