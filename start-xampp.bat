@echo off
REM Start XAMPP Services for Beersty
REM Simple batch file to start MySQL and Apache

title Start XAMPP for Beersty

echo.
echo ===============================================
echo Starting XAMPP Services for Beersty
echo ===============================================
echo.

REM Check common XAMPP locations
set XAMPP_PATH=

if exist "C:\xampp\xampp-control.exe" (
    set XAMPP_PATH=C:\xampp
    echo Found XAMPP at: C:\xampp
) else if exist "C:\Program Files\XAMPP\xampp-control.exe" (
    set XAMPP_PATH=C:\Program Files\XAMPP
    echo Found XAMPP at: C:\Program Files\XAMPP
) else if exist "C:\Program Files (x86)\XAMPP\xampp-control.exe" (
    set XAMPP_PATH=C:\Program Files (x86)\XAMPP
    echo Found XAMPP at: C:\Program Files (x86)\XAMPP
) else (
    echo ERROR: XAMPP not found in common locations
    echo Please install XAMPP from: https://www.apachefriends.org/download.html
    echo.
    pause
    exit /b 1
)

echo.
echo XAMPP Directory: %XAMPP_PATH%
echo.

REM Check if MySQL is running
echo Checking MySQL status...
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo MySQL is already running
) else (
    echo MySQL is not running
    echo Starting MySQL...
    
    REM Start MySQL
    start /B "" "%XAMPP_PATH%\mysql\bin\mysqld.exe" --defaults-file="%XAMPP_PATH%\mysql\bin\my.ini"
    
    REM Wait a moment
    timeout /t 3 /nobreak >nul
    
    REM Check if it started
    tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo MySQL started successfully!
    ) else (
        echo Failed to start MySQL
    )
)

echo.
echo Testing MySQL connection...
"%XAMPP_PATH%\mysql\bin\mysql.exe" -u root -e "SELECT 1;" >nul 2>&1
if "%ERRORLEVEL%"=="0" (
    echo MySQL connection test successful!
) else (
    echo MySQL connection test failed
)

echo.
echo ===============================================
echo Next Steps:
echo ===============================================
echo 1. Open XAMPP Control Panel (if needed)
echo 2. Make sure MySQL is started (green)
echo 3. Test database: http://localhost:8000/setup-database.php
echo 4. Try login: http://localhost:8000/auth/login.php
echo.

set /p choice="Open XAMPP Control Panel? (y/N): "
if /i "%choice%"=="y" (
    start "" "%XAMPP_PATH%\xampp-control.exe"
    echo XAMPP Control Panel opened
)

echo.
echo XAMPP startup completed!
pause
