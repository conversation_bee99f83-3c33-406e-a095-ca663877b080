# FIX BEERSTY PROJECT - Correct folder name and PHP config

Write-Host "FIXING BEERSTY PROJECT" -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow

# The correct folder is "beersty" not "beersty-lovable"
Write-Host "Project folder: C:\xampp\htdocs\beersty" -ForegroundColor Green

# Test the correct URLs
Write-Host "Testing correct URLs..." -ForegroundColor Cyan

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/beersty" -TimeoutSec 5 -UseBasicParsing
    Write-Host "SUCCESS: Main site works!" -ForegroundColor Green
} catch {
    Write-Host "Main site failed: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/beersty/admin/user-management.php" -TimeoutSec 5 -UseBasicParsing
    Write-Host "SUCCESS: User management works!" -ForegroundColor Green
} catch {
    Write-Host "User management failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Open the correct URLs
Write-Host "Opening correct URLs..." -ForegroundColor Cyan
Start-Process "http://localhost:8000/beersty"
Start-Process "http://localhost:8000/beersty/admin/user-management.php"
Start-Process "http://localhost:8000/beersty/test-pdo-simple.php"

Write-Host ""
Write-Host "CORRECT URLS:" -ForegroundColor Green
Write-Host "  Main: http://localhost:8000/beersty" -ForegroundColor White
Write-Host "  Admin: http://localhost:8000/beersty/admin/user-management.php" -ForegroundColor White
Write-Host "  PDO Test: http://localhost:8000/beersty/test-pdo-simple.php" -ForegroundColor White

Read-Host "Press Enter to continue"
