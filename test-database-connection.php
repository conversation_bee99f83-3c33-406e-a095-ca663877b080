<?php
/**
 * Test Database Connection
 * Check what database options are available and test connections
 */

echo "<h1>🗄️ Database Connection Test</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>📋 PHP Database Extensions</h2>";
echo "<ul>";
echo "<li><strong>PDO:</strong> " . (extension_loaded('pdo') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>PDO SQLite:</strong> " . (extension_loaded('pdo_sqlite') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ Available' : '❌ Not available') . "</li>";
echo "</ul>";

echo "<h2>🔧 Current Database Configuration</h2>";
require_once 'config/database.php';

$db = new Database();

// Use reflection to get private properties
$reflection = new ReflectionClass($db);
$hostProperty = $reflection->getProperty('host');
$hostProperty->setAccessible(true);
$dbNameProperty = $reflection->getProperty('db_name');
$dbNameProperty->setAccessible(true);
$usernameProperty = $reflection->getProperty('username');
$usernameProperty->setAccessible(true);
$passwordProperty = $reflection->getProperty('password');
$passwordProperty->setAccessible(true);

echo "<ul>";
echo "<li><strong>Host:</strong> " . $hostProperty->getValue($db) . "</li>";
echo "<li><strong>Database:</strong> " . $dbNameProperty->getValue($db) . "</li>";
echo "<li><strong>Username:</strong> " . $usernameProperty->getValue($db) . "</li>";
echo "<li><strong>Password:</strong> " . (empty($passwordProperty->getValue($db)) ? 'Empty' : '[Set]') . "</li>";
echo "</ul>";

echo "<h2>🧪 Test MySQL Connection</h2>";
try {
    $host = $hostProperty->getValue($db);
    $username = $usernameProperty->getValue($db);
    $password = $passwordProperty->getValue($db);
    
    // Test connection to MySQL server (without specific database)
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $testConn = new PDO($dsn, $username, $password);
    $testConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ MySQL server connection successful!</p>";
    
    // Check if database exists
    $dbName = $dbNameProperty->getValue($db);
    $stmt = $testConn->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([$dbName]);
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "<p>✅ Database '$dbName' exists</p>";
        
        // Test full connection
        try {
            $fullConn = $db->getConnection();
            echo "<p>✅ Full database connection successful!</p>";
            
            // Test if tables exist
            $stmt = $fullConn->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "<p><strong>Tables found:</strong> " . count($tables) . "</p>";
            if (!empty($tables)) {
                echo "<ul>";
                foreach ($tables as $table) {
                    echo "<li>$table</li>";
                }
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Full database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p>❌ Database '$dbName' does not exist</p>";
        echo "<p>🔧 Creating database...</p>";
        
        try {
            $testConn->exec("CREATE DATABASE `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p>✅ Database '$dbName' created successfully!</p>";
            
            // Now test the full connection
            try {
                $fullConn = $db->getConnection();
                echo "<p>✅ Connection to new database successful!</p>";
            } catch (Exception $e) {
                echo "<p>❌ Connection to new database failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Failed to create database: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ MySQL server connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h2>🔄 Alternative: SQLite Setup</h2>";
    echo "<p>Since MySQL connection failed, let's try SQLite as an alternative:</p>";
    
    if (extension_loaded('pdo_sqlite')) {
        echo "<p>✅ SQLite is available</p>";
        
        try {
            // Test SQLite connection
            $sqliteDb = 'data/beersty.sqlite';
            
            // Create data directory if it doesn't exist
            if (!is_dir('data')) {
                mkdir('data', 0755, true);
                echo "<p>✅ Created data directory</p>";
            }
            
            $sqliteDsn = "sqlite:$sqliteDb";
            $sqliteConn = new PDO($sqliteDsn);
            $sqliteConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<p>✅ SQLite connection successful!</p>";
            echo "<p><strong>SQLite database file:</strong> $sqliteDb</p>";
            
            // Check if tables exist
            $stmt = $sqliteConn->query("SELECT name FROM sqlite_master WHERE type='table'");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "<p><strong>SQLite tables found:</strong> " . count($tables) . "</p>";
            if (!empty($tables)) {
                echo "<ul>";
                foreach ($tables as $table) {
                    echo "<li>$table</li>";
                }
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ SQLite connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>❌ SQLite is not available</p>";
    }
}

echo "<h2>🛠️ Recommended Solutions</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Option 1: Fix MySQL (Recommended)</h3>";
echo "<ol>";
echo "<li><strong>Start MySQL/XAMPP:</strong> Make sure MySQL server is running</li>";
echo "<li><strong>Check credentials:</strong> Verify username/password are correct</li>";
echo "<li><strong>Create database:</strong> Run the database creation script</li>";
echo "</ol>";

echo "<h3>Option 2: Switch to SQLite (Easier)</h3>";
echo "<ol>";
echo "<li><strong>Modify database config:</strong> Switch to SQLite for simpler setup</li>";
echo "<li><strong>No server required:</strong> SQLite works with just PHP</li>";
echo "<li><strong>Portable:</strong> Database file can be easily moved</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔗 Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='setup-sqlite.php' class='btn btn-primary'>Setup SQLite Database</a></li>";
echo "<li><a href='setup-mysql.php' class='btn btn-info'>Setup MySQL Database</a></li>";
echo "<li><a href='auth/login.php' class='btn btn-secondary'>Try Login Again</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
