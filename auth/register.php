<?php
require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect(url('index.php'));
}

$pageTitle = 'Register - ' . APP_NAME;
$additionalCSS = ['../assets/css/auth.css'];

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $role = sanitizeInput($_POST['role'] ?? 'customer');
    $breweryId = sanitizeInput($_POST['brewery_id'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }
    
    if ($password !== $confirmPassword) {
        $errors[] = 'Passwords do not match.';
    }
    
    if (!in_array($role, ['customer', 'brewery', 'beer_enthusiast', 'beer_expert', 'admin'])) {
        $errors[] = 'Invalid role selected.';
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $errors[] = 'An account with this email already exists.';
            } else {
                // Create user account
                $userId = bin2hex(random_bytes(16)); // Generate UUID-like ID
                $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                
                $conn->beginTransaction();
                
                // Insert user
                $stmt = $conn->prepare("
                    INSERT INTO users (id, email, password_hash) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$userId, $email, $passwordHash]);
                
                // Insert profile
                $stmt = $conn->prepare("
                    INSERT INTO profiles (id, email, role, brewery_id) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$userId, $email, $role, $breweryId ?: null]);
                
                $conn->commit();

                // Auto-login the user and redirect to onboarding for social roles
                if (in_array($role, ['beer_enthusiast', 'beer_expert', 'customer'])) {
                    $_SESSION['user_id'] = $userId;
                    $_SESSION['user_email'] = $email;
                    $_SESSION['user_role'] = $role;
                    $_SESSION['brewery_id'] = $breweryId ?: null;

                    $_SESSION['success_message'] = 'Welcome to Beersty! Let\'s set up your profile.';
                    redirect('/user/onboarding.php');
                } else {
                    $_SESSION['success_message'] = 'Account created successfully! You can now log in.';
                    redirect('/auth/login.php');
                }
            }
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->rollBack();
            }
            error_log("Registration error: " . $e->getMessage());
            $errors[] = 'An error occurred while creating your account. Please try again.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Get breweries for brewery role selection
$breweries = [];
try {
    $db = new Database();
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT id, name FROM breweries WHERE claimable = 1 ORDER BY name");
    $stmt->execute();
    $breweries = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching breweries: " . $e->getMessage());
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">Create Account</h2>
                        <p class="text-muted">Join our brewery community</p>
                    </div>
                    
                    <form method="POST" id="registerForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirm Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">
                                <i class="fas fa-user-tag me-2"></i>Account Type
                            </label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Choose your account type...</option>
                                <option value="beer_enthusiast" <?php echo ($_POST['role'] ?? '') === 'beer_enthusiast' ? 'selected' : ''; ?>>
                                    🍺 Beer Enthusiast - Discover and rate beers
                                </option>
                                <option value="beer_expert" <?php echo ($_POST['role'] ?? '') === 'beer_expert' ? 'selected' : ''; ?>>
                                    🎯 Beer Expert/Critic - Professional reviews and insights
                                </option>
                                <option value="customer" <?php echo ($_POST['role'] ?? '') === 'customer' ? 'selected' : ''; ?>>
                                    👤 Customer - Basic brewery visitor
                                </option>
                                <option value="brewery" <?php echo ($_POST['role'] ?? '') === 'brewery' ? 'selected' : ''; ?>>
                                    🏭 Brewery Owner - Manage your brewery
                                </option>
                            </select>
                            <div class="form-text">
                                <small>
                                    <strong>Beer Enthusiast:</strong> Perfect for beer lovers who want to discover, rate, and share their beer experiences.<br>
                                    <strong>Beer Expert:</strong> For professional reviewers, sommeliers, and industry experts.<br>
                                    <strong>Customer:</strong> Basic account for brewery visitors.<br>
                                    <strong>Brewery Owner:</strong> Manage your brewery's profile and offerings.
                                </small>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="brewerySelection" style="display: none;">
                            <label for="brewery_id" class="form-label">
                                <i class="fas fa-building me-2"></i>Select Your Brewery
                            </label>
                            <select class="form-select" id="brewery_id" name="brewery_id">
                                <option value="">Choose a brewery...</option>
                                <?php foreach ($breweries as $brewery): ?>
                                    <option value="<?php echo htmlspecialchars($brewery['id']); ?>"
                                            <?php echo ($_POST['brewery_id'] ?? '') === $brewery['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($brewery['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Select the brewery you want to manage.</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Already have an account?</p>
                        <a href="/auth/login.php" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$pageJS = "
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Show/hide brewery selection based on role
    document.getElementById('role').addEventListener('change', function() {
        const brewerySelection = document.getElementById('brewerySelection');
        const brewerySelect = document.getElementById('brewery_id');
        
        if (this.value === 'brewery') {
            brewerySelection.style.display = 'block';
            brewerySelect.required = true;
        } else {
            brewerySelection.style.display = 'none';
            brewerySelect.required = false;
            brewerySelect.value = '';
        }
    });
    
    // Initialize brewery selection visibility
    document.getElementById('role').dispatchEvent(new Event('change'));
    
    // Form validation
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const role = document.getElementById('role').value;
        const breweryId = document.getElementById('brewery_id').value;
        
        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long.');
            return false;
        }
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match.');
            return false;
        }
        
        if (role === 'brewery' && !breweryId) {
            e.preventDefault();
            alert('Please select a brewery.');
            return false;
        }
    });
";

include '../includes/footer.php';
?>
