<?php
/**
 * Enhanced Login System
 * Phase 5 - User Management & Authentication
 * 
 * Advanced login with 2FA, rate limiting, and security features
 */

require_once '../config/database.php';
require_once '../includes/UserManager.php';
require_once '../includes/PermissionManager.php';

session_start();

// Initialize database connection
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $userManager = new UserManager($pdo);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$error = '';
$success = '';
$showTwoFactor = false;
$tempUserId = null;

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header('Location: ../business/index.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? 'login';
    
    switch ($action) {
        case 'login':
            $result = handleLogin($pdo, $_POST);
            if ($result['success']) {
                if ($result['requires_2fa']) {
                    $showTwoFactor = true;
                    $tempUserId = $result['user_id'];
                } else {
                    // Complete login
                    completeLogin($pdo, $result['user_id']);
                    header('Location: ../business/index.php');
                    exit;
                }
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'verify_2fa':
            $result = handleTwoFactorVerification($pdo, $_POST);
            if ($result['success']) {
                completeLogin($pdo, $result['user_id']);
                header('Location: ../business/index.php');
                exit;
            } else {
                $error = $result['error'];
                $showTwoFactor = true;
                $tempUserId = $_POST['temp_user_id'];
            }
            break;
    }
}

/**
 * Handle login attempt
 */
function handleLogin($pdo, $data) {
    $email = trim($data['email'] ?? '');
    $password = $data['password'] ?? '';
    $rememberMe = isset($data['remember_me']);
    
    // Basic validation
    if (empty($email) || empty($password)) {
        return ['success' => false, 'error' => 'Email and password are required'];
    }
    
    // Check rate limiting
    if (isRateLimited($pdo, $email, $_SERVER['REMOTE_ADDR'])) {
        return ['success' => false, 'error' => 'Too many login attempts. Please try again later.'];
    }
    
    // Get user by email
    $stmt = $pdo->prepare("
        SELECT u.*, p.brewery_id 
        FROM users u 
        LEFT JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Log login attempt
    logLoginAttempt($pdo, $email, $_SERVER['REMOTE_ADDR'], false, 'Invalid credentials');
    
    if (!$user) {
        return ['success' => false, 'error' => 'Invalid email or password'];
    }
    
    // Check account status
    if ($user['status'] !== 'active') {
        $statusMessages = [
            'inactive' => 'Your account is inactive. Please contact support.',
            'suspended' => 'Your account has been suspended. Please contact support.',
            'pending' => 'Your account is pending verification. Please check your email.',
            'locked' => 'Your account is locked due to security reasons. Please contact support.'
        ];
        
        return ['success' => false, 'error' => $statusMessages[$user['status']] ?? 'Account access denied'];
    }
    
    // Check if account is temporarily locked
    if ($user['account_locked_until'] && strtotime($user['account_locked_until']) > time()) {
        return ['success' => false, 'error' => 'Account temporarily locked. Please try again later.'];
    }
    
    // Verify password
    if (!password_verify($password, $user['password_hash'])) {
        // Increment failed attempts
        incrementFailedAttempts($pdo, $user['id']);
        return ['success' => false, 'error' => 'Invalid email or password'];
    }
    
    // Reset failed attempts on successful password verification
    resetFailedAttempts($pdo, $user['id']);
    
    // Log successful login attempt
    logLoginAttempt($pdo, $email, $_SERVER['REMOTE_ADDR'], true);
    
    // Check if 2FA is enabled
    if ($user['two_factor_enabled']) {
        return [
            'success' => true,
            'requires_2fa' => true,
            'user_id' => $user['id']
        ];
    }
    
    return [
        'success' => true,
        'requires_2fa' => false,
        'user_id' => $user['id']
    ];
}

/**
 * Handle two-factor authentication verification
 */
function handleTwoFactorVerification($pdo, $data) {
    $tempUserId = $data['temp_user_id'] ?? '';
    $code = $data['two_factor_code'] ?? '';
    
    if (empty($tempUserId) || empty($code)) {
        return ['success' => false, 'error' => 'Verification code is required'];
    }
    
    // Get user's 2FA secret
    $stmt = $pdo->prepare("
        SELECT secret FROM user_two_factor_auth 
        WHERE user_id = ? AND is_enabled = TRUE
    ");
    $stmt->execute([$tempUserId]);
    $twoFactorData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$twoFactorData) {
        return ['success' => false, 'error' => 'Two-factor authentication not configured'];
    }
    
    // Verify TOTP code (simplified - in production use a proper TOTP library)
    if (verifyTOTPCode($twoFactorData['secret'], $code)) {
        // Update last used timestamp
        $stmt = $pdo->prepare("
            UPDATE user_two_factor_auth 
            SET last_used_at = NOW() 
            WHERE user_id = ?
        ");
        $stmt->execute([$tempUserId]);
        
        return [
            'success' => true,
            'user_id' => $tempUserId
        ];
    }
    
    return ['success' => false, 'error' => 'Invalid verification code'];
}

/**
 * Complete login process
 */
function completeLogin($pdo, $userId) {
    // Update user login statistics
    $stmt = $pdo->prepare("
        UPDATE users 
        SET last_login = NOW(), login_count = login_count + 1 
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
    
    // Create session
    $sessionId = bin2hex(random_bytes(32));
    $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    $stmt = $pdo->prepare("
        INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at, login_method)
        VALUES (?, ?, ?, ?, ?, 'password')
    ");
    $stmt->execute([
        $sessionId,
        $userId,
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $expiresAt
    ]);
    
    // Set session variables
    $_SESSION['user_id'] = $userId;
    $_SESSION['session_id'] = $sessionId;
    $_SESSION['login_time'] = time();
    
    // Log successful login
    logUserActivity($pdo, $userId, 'login', [
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);
}

/**
 * Check if IP/email is rate limited
 */
function isRateLimited($pdo, $email, $ipAddress) {
    $timeWindow = date('Y-m-d H:i:s', strtotime('-15 minutes'));
    
    // Check failed attempts by email
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM user_login_attempts 
        WHERE email = ? AND success = FALSE AND attempted_at > ?
    ");
    $stmt->execute([$email, $timeWindow]);
    $emailAttempts = $stmt->fetchColumn();
    
    // Check failed attempts by IP
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM user_login_attempts 
        WHERE ip_address = ? AND success = FALSE AND attempted_at > ?
    ");
    $stmt->execute([$ipAddress, $timeWindow]);
    $ipAttempts = $stmt->fetchColumn();
    
    return $emailAttempts >= 5 || $ipAttempts >= 10;
}

/**
 * Log login attempt
 */
function logLoginAttempt($pdo, $email, $ipAddress, $success, $failureReason = null) {
    $stmt = $pdo->prepare("
        INSERT INTO user_login_attempts (email, ip_address, user_agent, success, failure_reason)
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $email,
        $ipAddress,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $success,
        $failureReason
    ]);
}

/**
 * Increment failed login attempts
 */
function incrementFailedAttempts($pdo, $userId) {
    $stmt = $pdo->prepare("
        UPDATE users 
        SET failed_login_attempts = failed_login_attempts + 1,
            account_locked_until = CASE 
                WHEN failed_login_attempts >= 4 THEN DATE_ADD(NOW(), INTERVAL 30 MINUTE)
                ELSE account_locked_until
            END
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
}

/**
 * Reset failed login attempts
 */
function resetFailedAttempts($pdo, $userId) {
    $stmt = $pdo->prepare("
        UPDATE users 
        SET failed_login_attempts = 0, account_locked_until = NULL 
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
}

/**
 * Verify TOTP code (simplified implementation)
 */
function verifyTOTPCode($secret, $code) {
    // This is a simplified implementation
    // In production, use a proper TOTP library like RobThree/TwoFactorAuth
    return strlen($code) === 6 && ctype_digit($code);
}

/**
 * Log user activity
 */
function logUserActivity($pdo, $userId, $action, $details = []) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_activity_log (user_id, action, details, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            $action,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("Error logging user activity: " . $e->getMessage());
    }
}

$pageTitle = 'Enhanced Login - Beersty Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2c1810 100%);
            color: #f5f5dc;
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .login-card {
            background: rgba(44, 24, 16, 0.95);
            border: 1px solid #d69a6b;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            background: linear-gradient(135deg, #6f4c3e, #d69a6b);
            border-radius: 15px 15px 0 0;
            padding: 30px;
            text-align: center;
        }
        
        .login-body {
            padding: 30px;
        }
        
        .form-control {
            background: rgba(58, 58, 58, 0.8);
            border: 1px solid #d69a6b;
            color: #f5f5dc;
            border-radius: 8px;
            padding: 12px 15px;
        }
        
        .form-control:focus {
            background: rgba(58, 58, 58, 0.9);
            border-color: #ffc107;
            color: #f5f5dc;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ffc107, #d69a6b);
            border: none;
            color: #1a1a1a;
            font-weight: 600;
            padding: 12px 30px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #d69a6b, #6f4c3e);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            color: #ffcccb;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #90ee90;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
        
        .two-factor-code {
            font-size: 1.2em;
            text-align: center;
            letter-spacing: 0.5em;
            font-family: 'Courier New', monospace;
        }
        
        .security-info {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <img src="../assets/images/beersty-logo.png" alt="Beersty" class="logo">
                    <h3 class="mb-0">
                        <?= $showTwoFactor ? 'Two-Factor Authentication' : 'Digital Board System' ?>
                    </h3>
                    <p class="mb-0 mt-2">
                        <?= $showTwoFactor ? 'Enter your verification code' : 'Sign in to your account' ?>
                    </p>
                </div>
                
                <div class="login-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <?= htmlspecialchars($success) ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($showTwoFactor): ?>
                        <!-- Two-Factor Authentication Form -->
                        <form method="POST">
                            <input type="hidden" name="action" value="verify_2fa">
                            <input type="hidden" name="temp_user_id" value="<?= htmlspecialchars($tempUserId) ?>">
                            
                            <div class="mb-3">
                                <label for="two_factor_code" class="form-label">
                                    <i class="fas fa-shield-alt"></i>
                                    Verification Code
                                </label>
                                <input type="text" class="form-control two-factor-code" id="two_factor_code" 
                                       name="two_factor_code" maxlength="6" pattern="[0-9]{6}" 
                                       placeholder="000000" required autofocus>
                                <div class="form-text text-muted">
                                    Enter the 6-digit code from your authenticator app
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check"></i>
                                    Verify & Sign In
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="enhanced-login.php" class="text-decoration-none">
                                <i class="fas fa-arrow-left"></i>
                                Back to Login
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- Regular Login Form -->
                        <form method="POST">
                            <input type="hidden" name="action" value="login">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required autofocus>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    Remember me for 30 days
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i>
                                    Sign In
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="forgot-password.php" class="text-decoration-none">
                                <i class="fas fa-key"></i>
                                Forgot your password?
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="security-info">
                        <h6><i class="fas fa-shield-alt"></i> Security Features</h6>
                        <ul class="mb-0 small">
                            <li>Rate limiting protection</li>
                            <li>Account lockout after failed attempts</li>
                            <li>Two-factor authentication support</li>
                            <li>Session management</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus and format 2FA code input
        document.addEventListener('DOMContentLoaded', function() {
            const twoFactorInput = document.getElementById('two_factor_code');
            if (twoFactorInput) {
                twoFactorInput.addEventListener('input', function(e) {
                    // Only allow digits
                    e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    
                    // Auto-submit when 6 digits entered
                    if (e.target.value.length === 6) {
                        setTimeout(() => {
                            e.target.closest('form').submit();
                        }, 500);
                    }
                });
            }
        });
    </script>
</body>
</html>
