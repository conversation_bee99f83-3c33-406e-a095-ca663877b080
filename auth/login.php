<?php
require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'admin') {
        redirect(url('admin/dashboard.php'));
    } elseif ($user['role'] === 'brewery') {
        redirect(url('brewery/profile.php'));
    } else {
        redirect(url('index.php'));
    }
}

$pageTitle = 'Login - ' . APP_NAME;
$additionalCSS = ['../assets/css/auth.css'];

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    // Debug information (remove in production)
    $debug_info = [];
    $debug_info['email_provided'] = !empty($email);
    $debug_info['password_provided'] = !empty($password);
    $debug_info['email_value'] = $email;

    if (empty($email) || empty($password)) {
        $_SESSION['error_message'] = 'Please fill in all fields.';
        $_SESSION['debug_info'] = 'Empty fields: email=' . ($email ? 'provided' : 'empty') . ', password=' . ($password ? 'provided' : 'empty');
    } else {
        try {
            // Test database connection first
            $debug_info['step'] = 'Connecting to database';
            $db = new Database();
            $conn = $db->getConnection();
            $debug_info['database_connected'] = true;

            // Check if database and tables exist
            $debug_info['step'] = 'Checking database structure';

            // Get database type
            $driver = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
            $debug_info['database_type'] = $driver;

            // For JSON databases, we don't need to check DATABASE()
            if ($driver === 'json' || $driver === 'user_shared_json') {
                $debug_info['current_database'] = 'JSON Database (' . $driver . ')';
            } else {
                $stmt = $conn->prepare("SELECT DATABASE() as current_db");
                $stmt->execute();
                $dbInfo = $stmt->fetch();
                $debug_info['current_database'] = $dbInfo ? $dbInfo['current_db'] : 'Unknown';
            }

            // Check if users table exists
            if ($driver === 'json' || $driver === 'user_shared_json') {
                // For JSON databases, check if we can query users
                try {
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM users");
                    $stmt->execute();
                    $debug_info['users_table_exists'] = true;
                } catch (Exception $e) {
                    $debug_info['users_table_exists'] = false;
                }

                // Check if profiles table exists
                try {
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM profiles");
                    $stmt->execute();
                    $debug_info['profiles_table_exists'] = true;
                } catch (Exception $e) {
                    $debug_info['profiles_table_exists'] = false;
                }
            } else {
                $stmt = $conn->prepare("SHOW TABLES LIKE 'users'");
                $stmt->execute();
                $debug_info['users_table_exists'] = $stmt->rowCount() > 0;

                // Check if profiles table exists
                $stmt = $conn->prepare("SHOW TABLES LIKE 'profiles'");
                $stmt->execute();
                $debug_info['profiles_table_exists'] = $stmt->rowCount() > 0;
            }

            if (!$debug_info['users_table_exists'] || !$debug_info['profiles_table_exists']) {
                $_SESSION['error_message'] = 'Database tables are missing. Please run the database setup.';
                $_SESSION['debug_info'] = 'Missing tables: users=' . ($debug_info['users_table_exists'] ? 'exists' : 'missing') . ', profiles=' . ($debug_info['profiles_table_exists'] ? 'exists' : 'missing');
            } else {
                // Get user by email
                $debug_info['step'] = 'Looking up user';
                $stmt = $conn->prepare("
                    SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id
                    FROM users u
                    JOIN profiles p ON u.id = p.id
                    WHERE u.email = ?
                ");
                $stmt->execute([$email]);
                $user = $stmt->fetch();

                $debug_info['user_found'] = $user !== false;

                if (!$user) {
                    // Check if user exists in users table without profile
                    $stmt = $conn->prepare("SELECT id, email FROM users WHERE email = ?");
                    $stmt->execute([$email]);
                    $userOnly = $stmt->fetch();

                    if ($userOnly) {
                        $_SESSION['error_message'] = 'User account found but profile is missing. Please contact support.';
                        $_SESSION['debug_info'] = 'User exists in users table but no profile found';
                    } else {
                        $_SESSION['error_message'] = 'No account found with that email address.';
                        $_SESSION['debug_info'] = 'Email not found in database: ' . $email;
                    }
                } else {
                    $debug_info['step'] = 'Verifying password';
                    $debug_info['user_role'] = $user['role'];
                    $debug_info['password_hash_length'] = strlen($user['password_hash']);

                    if (password_verify($password, $user['password_hash'])) {
                        $debug_info['password_verified'] = true;

                        // Update last login
                        $debug_info['step'] = 'Updating last login';
                        $updateStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                        $updateStmt->execute([$user['id']]);

                        // Set session variables
                        $debug_info['step'] = 'Setting session variables';
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_email'] = $user['email'];
                        $_SESSION['user_role'] = $user['role'];
                        $_SESSION['brewery_id'] = $user['brewery_id'];

                        $_SESSION['success_message'] = 'Welcome back, ' . htmlspecialchars($user['email']) . '!';
                        $_SESSION['debug_info'] = 'Login successful for ' . $user['role'] . ' user';

                        // Redirect based on role
                        if ($user['role'] === 'admin') {
                            redirect(url('admin/dashboard.php'));
                        } elseif ($user['role'] === 'brewery') {
                            redirect(url('brewery/profile.php'));
                        } else {
                            redirect(url('index.php'));
                        }
                    } else {
                        $debug_info['password_verified'] = false;
                        $_SESSION['error_message'] = 'Incorrect password for this email address.';
                        $_SESSION['debug_info'] = 'Password verification failed for user: ' . $email;
                    }
                }
            }
        } catch (Exception $e) {
            $debug_info['exception'] = $e->getMessage();
            $debug_info['exception_file'] = $e->getFile();
            $debug_info['exception_line'] = $e->getLine();

            error_log("Login error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            $_SESSION['error_message'] = 'Database connection error. Please try again.';
            $_SESSION['debug_info'] = 'Exception: ' . $e->getMessage() . ' (Step: ' . ($debug_info['step'] ?? 'unknown') . ')';
        }
    }

    // Store debug info in session for display (remove in production)
    $_SESSION['login_debug'] = $debug_info;
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-sign-in-alt fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <form method="POST" id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Don't have an account?</p>
                        <a href="<?php echo url('auth/register.php'); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </a>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="<?php echo url('auth/forgot-password.php'); ?>" class="text-muted">
                            <small>Forgot your password?</small>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Demo Accounts -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-info-circle me-2"></i>Demo Accounts
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Admin Account:</strong><br>
                            <small class="text-muted">
                                Email: <EMAIL><br>
                                Password: admin123
                            </small>
                        </div>
                        <div class="col-6">
                            <strong>Brewery Account:</strong><br>
                            <small class="text-muted">
                                Email: <EMAIL><br>
                                Password: brewery123
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debug Information (remove in production) -->
            <?php if (isset($_SESSION['login_debug']) || isset($_SESSION['debug_info'])): ?>
            <div class="card mt-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <i class="fas fa-bug me-2"></i>Debug Information
                    <small class="float-end">Remove this in production</small>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['debug_info'])): ?>
                        <div class="alert alert-info">
                            <strong>Last Action:</strong> <?php echo htmlspecialchars($_SESSION['debug_info']); ?>
                        </div>
                        <?php unset($_SESSION['debug_info']); ?>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['login_debug'])): ?>
                        <h6>Detailed Debug Info:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <?php foreach ($_SESSION['login_debug'] as $key => $value): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($key); ?></strong></td>
                                    <td>
                                        <?php
                                        if (is_bool($value)) {
                                            echo $value ? '✅ true' : '❌ false';
                                        } else {
                                            echo htmlspecialchars($value);
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                        <?php unset($_SESSION['login_debug']); ?>
                    <?php endif; ?>

                    <div class="mt-3">
                        <h6>Quick Actions:</h6>
                        <a href="../test-database-connection.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-database me-1"></i>Test Database
                        </a>
                        <a href="../xampp-setup.php" class="btn btn-sm btn-outline-success me-2">
                            <i class="fas fa-cog me-1"></i>Setup Database
                        </a>
                        <a href="http://localhost/phpmyadmin" class="btn btn-sm btn-outline-info" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>phpMyAdmin
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
$pageJS = "
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Form validation
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        
        if (!email || !password) {
            e.preventDefault();
            alert('Please fill in all fields.');
            return false;
        }
        
        if (!email.includes('@')) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }
    });
";

include '../includes/footer.php';
?>
