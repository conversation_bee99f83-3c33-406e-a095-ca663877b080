<?php
/**
 * Digital Board User Management
 * Phase 5 - User Management & Authentication
 * 
 * Enhanced user management interface for digital board system
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/UserManager.php';
require_once '../../includes/PermissionManager.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/login.php');
    exit;
}

// Initialize managers
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $userManager = new UserManager($pdo);
    $permissionManager = new PermissionManager($pdo);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Check permissions
$canManageUsers = $permissionManager->hasPermission($user['id'], 'manage_brewery_users');
if (!$canManageUsers && $user['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

// Get accessible breweries
$userBreweries = $permissionManager->getUserBreweries($user['id']);
$selectedBreweryId = $_GET['brewery_id'] ?? ($userBreweries[0]['id'] ?? null);

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_user':
            $result = $userManager->createUser($_POST);
            $message = $result['success'] ? $result['message'] : $result['error'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
            
        case 'update_user':
            $result = $userManager->updateUser($_POST['user_id'], $_POST);
            $message = $result['success'] ? $result['message'] : $result['error'];
            $messageType = $result['success'] ? 'success' : 'danger';
            break;
    }
}

$pageTitle = 'User Management - Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/admin.css" rel="stylesheet">
    
    <style>
        body { background: #1a1a1a; color: #f5f5dc; font-family: 'Inter', sans-serif; }
        .navbar { background: #000000 !important; }
        .card { background: #2c1810; border: 1px solid #d69a6b; }
        .card-header { background: #6f4c3e; color: #f5f5dc; }
        .btn-primary { background: #ffc107; border-color: #ffc107; color: #1a1a1a; }
        .btn-primary:hover { background: #d69a6b; border-color: #d69a6b; }
        .btn-secondary { background: #6c757d; border-color: #6c757d; }
        .btn-danger { background: #dc3545; border-color: #dc3545; }
        .btn-success { background: #28a745; border-color: #28a745; }
        .form-control, .form-select { background: #3a3a3a; border-color: #d69a6b; color: #f5f5dc; }
        .form-control:focus, .form-select:focus { background: #3a3a3a; border-color: #ffc107; color: #f5f5dc; box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25); }
        .table-dark { background: #2c1810; }
        .table-dark td, .table-dark th { border-color: #d69a6b; }
        .modal-content { background: #2c1810; border: 1px solid #d69a6b; }
        .modal-header { background: #6f4c3e; border-bottom: 1px solid #d69a6b; }
        .modal-footer { border-top: 1px solid #d69a6b; }
        .badge { font-size: 0.8em; }
        .user-avatar { width: 40px; height: 40px; border-radius: 50%; background: #6f4c3e; display: flex; align-items: center; justify-content: center; color: #ffc107; font-weight: bold; }
        .permission-badge { margin: 2px; padding: 4px 8px; font-size: 0.7em; }
        .alert-success { background: #2d5a2d; border-color: #4caf50; color: #90ee90; }
        .alert-danger { background: #5a2d2d; border-color: #f44336; color: #ffcccb; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <img src="../../assets/images/beersty-logo.png" alt="Beersty" height="40">
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?= htmlspecialchars($user['name'] ?? $user['email']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../../auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-users"></i>
                            User Management
                        </h1>
                        <p class="text-muted mb-0">Manage digital board system users and permissions</p>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-user-plus"></i> Add User
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Brewery Selection -->
                <?php if (count($userBreweries) > 1): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <label for="brewerySelect" class="form-label">Select Brewery</label>
                                <select class="form-select" id="brewerySelect" onchange="changeBrewery(this.value)">
                                    <?php foreach ($userBreweries as $brewery): ?>
                                        <option value="<?= $brewery['id'] ?>" <?= $selectedBreweryId === $brewery['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($brewery['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    Managing users for: <strong><?= htmlspecialchars($userBreweries[array_search($selectedBreweryId, array_column($userBreweries, 'id'))]['name'] ?? 'All Breweries') ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- User Management Interface -->
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0"><i class="fas fa-users"></i> System Users</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex gap-2">
                                    <input type="text" class="form-control form-control-sm" id="userSearch" placeholder="Search users...">
                                    <select class="form-select form-select-sm" id="roleFilter" style="max-width: 150px;">
                                        <option value="">All Roles</option>
                                        <?php foreach (UserManager::ROLES as $role => $label): ?>
                                            <option value="<?= $role ?>"><?= $label ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <select class="form-select form-select-sm" id="statusFilter" style="max-width: 120px;">
                                        <option value="">All Status</option>
                                        <?php foreach (UserManager::USER_STATUSES as $status => $label): ?>
                                            <option value="<?= $status ?>"><?= $label ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body p-0">
                        <!-- Users Table -->
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Brewery</th>
                                        <th>Status</th>
                                        <th>Permissions</th>
                                        <th>Last Activity</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2 mb-0">Loading users...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3">
                            <div class="text-muted">
                                <span id="userCount">Loading...</span>
                            </div>
                            <nav aria-label="Users pagination">
                                <ul class="pagination pagination-sm mb-0" id="usersPagination">
                                    <!-- Pagination will be populated by JavaScript -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus"></i> Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="addUserForm">
                    <input type="hidden" name="action" value="create_user">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" name="password" required minlength="6">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" name="first_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" name="last_name">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role *</label>
                                    <select class="form-select" name="role" required onchange="toggleBreweryField(this.value)">
                                        <option value="">Select Role</option>
                                        <?php foreach (UserManager::ROLES as $role => $label): ?>
                                            <?php if ($user['role'] === 'admin' || !in_array($role, ['admin', 'site_moderator'])): ?>
                                                <option value="<?= $role ?>"><?= $label ?></option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3" id="breweryField">
                                    <label for="brewery_id" class="form-label">Brewery</label>
                                    <select class="form-select" name="brewery_id">
                                        <option value="">Select Brewery</option>
                                        <?php foreach ($userBreweries as $brewery): ?>
                                            <option value="<?= $brewery['id'] ?>"><?= htmlspecialchars($brewery['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" name="status">
                                        <?php foreach (UserManager::USER_STATUSES as $status => $label): ?>
                                            <option value="<?= $status ?>" <?= $status === 'active' ? 'selected' : '' ?>><?= $label ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Hidden configuration -->
    <script>
        window.userManagementConfig = {
            breweryId: <?= json_encode($selectedBreweryId) ?>,
            userRole: <?= json_encode($user['role']) ?>,
            apiBaseUrl: '../../api/',
            currentUser: <?= json_encode([
                'id' => $user['id'],
                'role' => $user['role']
            ]) ?>
        };
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../../assets/js/user-management.js"></script>
    
    <script>
        function changeBrewery(breweryId) {
            if (breweryId) {
                window.location.href = `?brewery_id=${breweryId}`;
            }
        }
        
        function toggleBreweryField(role) {
            const breweryField = document.getElementById('breweryField');
            const brewerySelect = breweryField.querySelector('select');
            
            if (['business_owner', 'business_manager', 'digital_board_operator'].includes(role)) {
                breweryField.style.display = 'block';
                brewerySelect.required = true;
            } else {
                breweryField.style.display = 'none';
                brewerySelect.required = false;
                brewerySelect.value = '';
            }
        }
    </script>
</body>
</html>
