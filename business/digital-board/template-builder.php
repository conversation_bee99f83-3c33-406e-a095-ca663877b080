<?php
/**
 * Advanced Digital Board Template Builder
 * Create custom templates with advanced design options
 */

$pageTitle = 'Template Builder - Beersty';
$mode = $_GET['mode'] ?? 'custom';
$duplicateId = $_GET['duplicate'] ?? null;

// Default template settings
$defaultSettings = [
    'name' => 'Custom Template',
    'description' => 'My custom digital board template',
    'background_type' => 'gradient',
    'background_color1' => '#1a1a1a',
    'background_color2' => '#2d2d2d',
    'background_image' => '',
    'text_color' => '#ffffff',
    'accent_color' => '#ffc107',
    'card_bg_color' => 'rgba(255, 255, 255, 0.1)',
    'card_border_color' => 'rgba(255, 255, 255, 0.2)',
    'font_family' => 'Segoe UI',
    'font_size_base' => '16',
    'layout' => 'grid',
    'card_style' => 'rounded',
    'animation_style' => 'fade',
    'show_tap_numbers' => true,
    'show_prices' => true,
    'show_descriptions' => true,
    'show_abv' => true,
    'show_ibu' => true,
    'show_srm' => false,
    'ticker_enabled' => true,
    'ticker_message' => 'Welcome to our brewery!',
    'ticker_speed' => '30',
    'header_style' => 'standard',
    'logo_position' => 'center',
    'border_radius' => '15',
    'card_spacing' => '1.5',
    'padding' => '2'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .builder-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .preview-frame { 
            border: 3px solid #dee2e6; 
            border-radius: 10px; 
            height: 500px; 
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        .color-picker { width: 100%; height: 40px; border: none; border-radius: 5px; cursor: pointer; }
        .settings-panel { max-height: 600px; overflow-y: auto; }
        .setting-group { border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; }
        .setting-group h6 { color: #495057; border-bottom: 1px solid #e9ecef; padding-bottom: 0.5rem; margin-bottom: 1rem; }
        .preview-mini { width: 100%; height: 200px; border: 1px solid #dee2e6; border-radius: 5px; margin-top: 0.5rem; }
        .template-actions { position: sticky; bottom: 0; background: white; padding: 1rem; border-top: 1px solid #e9ecef; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="builder-container p-4">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-2">
                    <i class="fas fa-tools me-2 text-primary"></i>Advanced Template Builder
                </h1>
                <p class="text-muted">Create custom digital board templates with advanced design options</p>
            </div>
            
            <div class="row">
                <!-- Settings Panel -->
                <div class="col-lg-4">
                    <div class="settings-panel">
                        <!-- Template Info -->
                        <div class="setting-group">
                            <h6><i class="fas fa-info-circle me-2"></i>Template Information</h6>
                            <div class="mb-3">
                                <label class="form-label">Template Name</label>
                                <input type="text" id="templateName" class="form-control" value="<?php echo $defaultSettings['name']; ?>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea id="templateDescription" class="form-control" rows="2"><?php echo $defaultSettings['description']; ?></textarea>
                            </div>
                        </div>
                        
                        <!-- Background Settings -->
                        <div class="setting-group">
                            <h6><i class="fas fa-image me-2"></i>Background</h6>
                            <div class="mb-3">
                                <label class="form-label">Background Type</label>
                                <select id="backgroundType" class="form-select">
                                    <option value="solid">Solid Color</option>
                                    <option value="gradient" selected>Gradient</option>
                                    <option value="image">Background Image</option>
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">Color 1</label>
                                    <input type="color" id="backgroundColor1" class="color-picker" value="<?php echo $defaultSettings['background_color1']; ?>">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">Color 2</label>
                                    <input type="color" id="backgroundColor2" class="color-picker" value="<?php echo $defaultSettings['background_color2']; ?>">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Colors -->
                        <div class="setting-group">
                            <h6><i class="fas fa-palette me-2"></i>Colors</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <label class="form-label small">Text Color</label>
                                    <input type="color" id="textColor" class="color-picker" value="<?php echo $defaultSettings['text_color']; ?>">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Accent Color</label>
                                    <input type="color" id="accentColor" class="color-picker" value="<?php echo $defaultSettings['accent_color']; ?>">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Card Background</label>
                                    <input type="color" id="cardBgColor" class="color-picker" value="#333333">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Card Border</label>
                                    <input type="color" id="cardBorderColor" class="color-picker" value="#555555">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Typography -->
                        <div class="setting-group">
                            <h6><i class="fas fa-font me-2"></i>Typography</h6>
                            <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select id="fontFamily" class="form-select">
                                    <option value="Segoe UI">Segoe UI</option>
                                    <option value="Arial">Arial</option>
                                    <option value="Helvetica">Helvetica</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                    <option value="Roboto">Roboto</option>
                                    <option value="Open Sans">Open Sans</option>
                                    <option value="Lato">Lato</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Base Font Size</label>
                                <input type="range" id="fontSize" class="form-range" min="12" max="24" value="16">
                                <small class="text-muted">16px</small>
                            </div>
                        </div>
                        
                        <!-- Layout -->
                        <div class="setting-group">
                            <h6><i class="fas fa-th me-2"></i>Layout</h6>
                            <div class="mb-3">
                                <label class="form-label">Layout Style</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="layout" id="gridLayout" value="grid" checked>
                                    <label class="btn btn-outline-primary" for="gridLayout">Grid</label>
                                    <input type="radio" class="btn-check" name="layout" id="listLayout" value="list">
                                    <label class="btn btn-outline-primary" for="listLayout">List</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">Border Radius</label>
                                    <input type="range" id="borderRadius" class="form-range" min="0" max="30" value="15">
                                    <small class="text-muted">15px</small>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">Card Spacing</label>
                                    <input type="range" id="cardSpacing" class="form-range" min="0.5" max="3" step="0.5" value="1.5">
                                    <small class="text-muted">1.5rem</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Display Options -->
                        <div class="setting-group">
                            <h6><i class="fas fa-eye me-2"></i>Display Options</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showTapNumbers" checked>
                                <label class="form-check-label" for="showTapNumbers">Show Tap Numbers</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrices" checked>
                                <label class="form-check-label" for="showPrices">Show Prices</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showDescriptions" checked>
                                <label class="form-check-label" for="showDescriptions">Show Descriptions</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showABV" checked>
                                <label class="form-check-label" for="showABV">Show ABV</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showIBU" checked>
                                <label class="form-check-label" for="showIBU">Show IBU</label>
                            </div>
                        </div>
                        
                        <!-- Ticker Settings -->
                        <div class="setting-group">
                            <h6><i class="fas fa-scroll me-2"></i>Ticker Message</h6>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="tickerEnabled" checked>
                                <label class="form-check-label" for="tickerEnabled">Enable Ticker</label>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ticker Message</label>
                                <input type="text" id="tickerMessage" class="form-control" value="<?php echo $defaultSettings['ticker_message']; ?>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Animation Speed</label>
                                <input type="range" id="tickerSpeed" class="form-range" min="10" max="60" value="30">
                                <small class="text-muted">30 seconds</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Preview Panel -->
                <div class="col-lg-8">
                    <div class="mb-3">
                        <h5>Live Preview</h5>
                        <p class="text-muted">See your template changes in real-time</p>
                    </div>
                    
                    <div class="preview-frame" id="previewFrame">
                        <iframe id="previewIframe" src="display-template-preview.php?template=classic-dark" 
                                style="width: 100%; height: 100%; border: none; border-radius: 7px;"></iframe>
                    </div>
                    
                    <!-- Preview Controls -->
                    <div class="mt-3">
                        <div class="btn-group me-3" role="group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="updatePreview()">
                                <i class="fas fa-sync-alt me-1"></i>Update Preview
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="fullPreview()">
                                <i class="fas fa-expand me-1"></i>Full Preview
                            </button>
                        </div>
                        
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-info btn-sm" onclick="previewMobile()">
                                <i class="fas fa-mobile-alt me-1"></i>Mobile
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="previewTablet()">
                                <i class="fas fa-tablet-alt me-1"></i>Tablet
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="previewDesktop()">
                                <i class="fas fa-desktop me-1"></i>Desktop
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Template Actions -->
            <div class="template-actions">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <button class="btn btn-success" onclick="saveTemplate()">
                                <i class="fas fa-save me-2"></i>Save Template
                            </button>
                            <button class="btn btn-primary" onclick="applyTemplate()">
                                <i class="fas fa-check me-2"></i>Apply to Board
                            </button>
                            <button class="btn btn-outline-info" onclick="exportTemplate()">
                                <i class="fas fa-download me-2"></i>Export
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="templates.php" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Templates
                        </a>
                        <button class="btn btn-outline-danger" onclick="resetTemplate()">
                            <i class="fas fa-undo me-2"></i>Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSettings = <?php echo json_encode($defaultSettings); ?>;
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }
        
        function updatePreview() {
            // Collect current settings
            collectSettings();
            showToast('Preview updated!', 'info');
        }
        
        function collectSettings() {
            currentSettings.name = document.getElementById('templateName').value;
            currentSettings.description = document.getElementById('templateDescription').value;
            currentSettings.background_type = document.getElementById('backgroundType').value;
            currentSettings.background_color1 = document.getElementById('backgroundColor1').value;
            currentSettings.background_color2 = document.getElementById('backgroundColor2').value;
            currentSettings.text_color = document.getElementById('textColor').value;
            currentSettings.accent_color = document.getElementById('accentColor').value;
            currentSettings.font_family = document.getElementById('fontFamily').value;
            currentSettings.layout = document.querySelector('input[name="layout"]:checked').value;
            currentSettings.show_tap_numbers = document.getElementById('showTapNumbers').checked;
            currentSettings.show_prices = document.getElementById('showPrices').checked;
            currentSettings.show_descriptions = document.getElementById('showDescriptions').checked;
            currentSettings.ticker_enabled = document.getElementById('tickerEnabled').checked;
            currentSettings.ticker_message = document.getElementById('tickerMessage').value;
        }
        
        function fullPreview() {
            window.open('display-template-preview.php?template=classic-dark', '_blank');
        }
        
        function previewMobile() {
            document.getElementById('previewFrame').style.width = '375px';
            showToast('Mobile preview mode', 'info');
        }
        
        function previewTablet() {
            document.getElementById('previewFrame').style.width = '768px';
            showToast('Tablet preview mode', 'info');
        }
        
        function previewDesktop() {
            document.getElementById('previewFrame').style.width = '100%';
            showToast('Desktop preview mode', 'info');
        }
        
        function saveTemplate() {
            collectSettings();
            showToast(`Template "${currentSettings.name}" saved successfully!`, 'success');
        }
        
        function applyTemplate() {
            collectSettings();
            showToast('Template applied to digital board!', 'success');
        }
        
        function exportTemplate() {
            collectSettings();
            const dataStr = JSON.stringify(currentSettings, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${currentSettings.name.replace(/\s+/g, '_')}_template.json`;
            link.click();
            showToast('Template exported!', 'success');
        }
        
        function resetTemplate() {
            if (confirm('Reset template to default settings?')) {
                location.reload();
            }
        }
        
        // Auto-update range displays
        document.getElementById('fontSize').addEventListener('input', function() {
            this.nextElementSibling.textContent = this.value + 'px';
        });
        
        document.getElementById('borderRadius').addEventListener('input', function() {
            this.nextElementSibling.textContent = this.value + 'px';
        });
        
        document.getElementById('cardSpacing').addEventListener('input', function() {
            this.nextElementSibling.textContent = this.value + 'rem';
        });
        
        document.getElementById('tickerSpeed').addEventListener('input', function() {
            this.nextElementSibling.textContent = this.value + ' seconds';
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Advanced Template Builder loaded');
            showToast('Template builder ready!', 'info');
        });
    </script>
</body>
</html>
