<?php
/**
 * Digital Beer Board Template Preview
 * Shows how different templates look with sample data
 */

$templateId = $_GET['template'] ?? 'classic-dark';

// Special handling for Beersty Professional template
if ($templateId === 'beersty-professional') {
    header('Location: display-beersty-professional.php');
    exit;
}

// Template definitions
$templates = [
    'classic-dark' => [
        'name' => 'Classic Dark',
        'background' => 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
        'text_color' => '#ffffff',
        'accent_color' => '#ffc107',
        'card_bg' => 'rgba(255, 255, 255, 0.1)',
        'card_border' => 'rgba(255, 255, 255, 0.2)',
        'font_family' => 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
    ],
    'brewery-wood' => [
        'name' => 'Brewery Wood',
        'background' => 'linear-gradient(135deg, #8B4513 0%, #D2691E 100%)',
        'text_color' => '#F5DEB3',
        'accent_color' => '#D2691E',
        'card_bg' => 'rgba(139, 69, 19, 0.3)',
        'card_border' => 'rgba(210, 105, 30, 0.5)',
        'font_family' => 'Georgia, serif'
    ],
    'modern-light' => [
        'name' => 'Modern Light',
        'background' => 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
        'text_color' => '#333333',
        'accent_color' => '#007bff',
        'card_bg' => '#ffffff',
        'card_border' => '#dee2e6',
        'font_family' => 'Arial, sans-serif'
    ],
    'industrial-steel' => [
        'name' => 'Industrial Steel',
        'background' => 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
        'text_color' => '#ecf0f1',
        'accent_color' => '#e67e22',
        'card_bg' => 'rgba(52, 73, 94, 0.8)',
        'card_border' => 'rgba(230, 126, 34, 0.3)',
        'font_family' => 'Roboto, sans-serif'
    ],
    'craft-vintage' => [
        'name' => 'Craft Vintage',
        'background' => 'linear-gradient(135deg, #3e2723 0%, #5d4037 100%)',
        'text_color' => '#fff8e1',
        'accent_color' => '#ff8f00',
        'card_bg' => 'rgba(62, 39, 35, 0.7)',
        'card_border' => 'rgba(255, 143, 0, 0.4)',
        'font_family' => 'Times New Roman, serif'
    ],
    'minimalist-clean' => [
        'name' => 'Minimalist Clean',
        'background' => '#ffffff',
        'text_color' => '#333333',
        'accent_color' => '#000000',
        'card_bg' => '#ffffff',
        'card_border' => '#e9ecef',
        'font_family' => 'Helvetica, Arial, sans-serif'
    ],
    'beersty-professional' => [
        'name' => 'Beersty Professional',
        'background' => '#000000',
        'text_color' => '#f8f9fa',
        'accent_color' => '#6c757d',
        'card_bg' => '#495057',
        'card_border' => '#343a40',
        'font_family' => 'Bootstrap, sans-serif'
    ]
];

$template = $templates[$templateId] ?? $templates['classic-dark'];

// Sample beer data
$sampleBeers = [
    ['name' => 'Hoppy IPA', 'style' => 'IPA', 'abv' => 6.8, 'ibu' => 68, 'price' => 8.50, 'tap' => 1],
    ['name' => 'Golden Wheat', 'style' => 'Wheat Beer', 'abv' => 4.5, 'ibu' => 12, 'price' => 6.00, 'tap' => 2],
    ['name' => 'Midnight Stout', 'style' => 'Stout', 'abv' => 7.2, 'ibu' => 35, 'price' => 9.00, 'tap' => 3],
    ['name' => 'Crisp Lager', 'style' => 'Lager', 'abv' => 4.8, 'ibu' => 18, 'price' => 5.50, 'tap' => 4],
    ['name' => 'Robust Porter', 'style' => 'Porter', 'abv' => 6.2, 'ibu' => 28, 'price' => 7.50, 'tap' => 5],
    ['name' => 'Czech Pilsner', 'style' => 'Pilsner', 'abv' => 5.2, 'ibu' => 35, 'price' => 7.00, 'tap' => 6]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $template['name']; ?> - Template Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: <?php echo $template['font_family']; ?>;
            background: <?php echo $template['background']; ?>;
            color: <?php echo $template['text_color']; ?>;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .template-preview {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .preview-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 3px solid <?php echo $template['accent_color']; ?>;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .brewery-name {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .brewery-location {
            font-size: 1.5rem;
            opacity: 0.8;
        }
        
        .template-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            background: <?php echo $template['accent_color']; ?>;
            color: <?php echo $templateId === 'modern-light' || $templateId === 'minimalist-clean' ? '#ffffff' : '#000000'; ?>;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .beer-grid {
            flex: 1;
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .beer-card {
            background: <?php echo $template['card_bg']; ?>;
            border: 1px solid <?php echo $template['card_border']; ?>;
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
            transition: transform 0.3s ease;
            <?php if ($templateId === 'modern-light' || $templateId === 'minimalist-clean'): ?>
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            <?php else: ?>
                backdrop-filter: blur(10px);
            <?php endif; ?>
        }
        
        .beer-card:hover {
            transform: translateY(-5px);
        }
        
        .tap-number {
            position: absolute;
            top: -10px;
            left: 20px;
            background: <?php echo $template['accent_color']; ?>;
            color: <?php echo $templateId === 'modern-light' || $templateId === 'minimalist-clean' ? '#ffffff' : '#000000'; ?>;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .beer-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .beer-style {
            color: <?php echo $template['accent_color']; ?>;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .beer-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .beer-specs {
            display: flex;
            gap: 2rem;
        }
        
        .spec-item {
            text-align: center;
        }
        
        .spec-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: <?php echo $template['accent_color']; ?>;
        }
        
        .spec-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .beer-price {
            font-size: 2rem;
            font-weight: bold;
            color: <?php echo $template['accent_color']; ?>;
        }
        
        .ticker {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: <?php echo $template['accent_color']; ?>;
            color: <?php echo $templateId === 'modern-light' || $templateId === 'minimalist-clean' ? '#ffffff' : '#000000'; ?>;
            padding: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
            text-align: center;
            animation: ticker-scroll 30s linear infinite;
        }
        
        @keyframes ticker-scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .preview-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
        
        @media (max-width: 768px) {
            .brewery-name { font-size: 2rem; }
            .brewery-location { font-size: 1.2rem; }
            .beer-name { font-size: 1.4rem; }
            .beer-specs { gap: 1rem; }
            .beer-grid { grid-template-columns: 1fr; }
        }
        
        /* Template-specific styles */
        <?php if ($templateId === 'brewery-wood'): ?>
        .beer-card {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
        }
        <?php endif; ?>
        
        <?php if ($templateId === 'industrial-steel'): ?>
        .beer-card {
            border-left: 4px solid <?php echo $template['accent_color']; ?>;
        }
        <?php endif; ?>
        
        <?php if ($templateId === 'craft-vintage'): ?>
        .beer-card {
            border: 2px solid <?php echo $template['accent_color']; ?>;
            background-image: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 143, 0, 0.1) 10px,
                rgba(255, 143, 0, 0.1) 20px
            );
        }
        <?php endif; ?>
        
        <?php if ($templateId === 'minimalist-clean'): ?>
        .beer-card {
            border: 1px solid #e9ecef;
            border-left: 4px solid <?php echo $template['accent_color']; ?>;
        }
        .tap-number {
            background: <?php echo $template['accent_color']; ?>;
            color: white;
        }
        <?php endif; ?>
    </style>
</head>
<body>
    <div class="template-preview">
        <!-- Template Badge -->
        <div class="template-badge">
            <?php echo $template['name']; ?> Template
        </div>
        
        <!-- Preview Controls -->
        <div class="preview-controls">
            <button class="control-btn" onclick="window.close()">
                <i class="fas fa-times me-1"></i>Close
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand me-1"></i>Fullscreen
            </button>
        </div>
        
        <!-- Header -->
        <div class="preview-header">
            <div class="brewery-name">Demo Craft Brewery</div>
            <div class="brewery-location">Demo City, CA</div>
        </div>
        
        <!-- Beer Grid -->
        <div class="beer-grid">
            <?php foreach ($sampleBeers as $beer): ?>
                <div class="beer-card">
                    <div class="tap-number"><?php echo $beer['tap']; ?></div>
                    
                    <div class="beer-name"><?php echo $beer['name']; ?></div>
                    <div class="beer-style"><?php echo $beer['style']; ?></div>
                    
                    <div class="beer-details">
                        <div class="beer-specs">
                            <div class="spec-item">
                                <div class="spec-value"><?php echo $beer['abv']; ?>%</div>
                                <div class="spec-label">ABV</div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-value"><?php echo $beer['ibu']; ?></div>
                                <div class="spec-label">IBU</div>
                            </div>
                        </div>
                        <div class="beer-price">$<?php echo number_format($beer['price'], 2); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Ticker -->
        <div class="ticker">
            Welcome to Demo Craft Brewery! Experience the <?php echo $template['name']; ?> template design
        </div>
    </div>
    
    <script>
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    window.close();
                }
            }
            
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
        
        console.log('Template Preview: <?php echo $template['name']; ?>');
    </script>
</body>
</html>
