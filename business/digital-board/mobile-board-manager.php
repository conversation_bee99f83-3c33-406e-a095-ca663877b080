<?php
/**
 * Mobile Board Manager
 * Phase 6 - Mobile & Responsive Optimization
 * 
 * Touch-friendly interface for managing digital boards on mobile devices
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/enhanced-login.php');
    exit;
}

$pageTitle = 'Board Manager - Mobile';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#ffc107">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/digital-board-mobile.css" rel="stylesheet">
    
    <style>
        .board-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 16px 0;
        }
        
        .board-card {
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .board-card.active {
            border-color: var(--mobile-primary);
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
        }
        
        .board-preview {
            height: 120px;
            background: linear-gradient(135deg, #1a1a1a, #2c1810);
            position: relative;
            overflow: hidden;
        }
        
        .board-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .board-status {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .board-status.active {
            background: #28a745;
            color: white;
        }
        
        .board-status.inactive {
            background: #6c757d;
            color: white;
        }
        
        .board-info {
            padding: 16px;
        }
        
        .board-title {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--mobile-text);
            margin-bottom: 8px;
        }
        
        .board-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .board-template {
            font-size: 0.85rem;
            color: var(--mobile-text-muted);
        }
        
        .board-updated {
            font-size: 0.75rem;
            color: var(--mobile-text-muted);
        }
        
        .board-actions {
            display: flex;
            gap: 8px;
        }
        
        .board-action-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--mobile-primary-dark);
            background: transparent;
            color: var(--mobile-text);
            border-radius: 6px;
            font-size: 0.85rem;
            transition: all 0.2s ease;
        }
        
        .board-action-btn.primary {
            background: var(--mobile-primary);
            color: var(--mobile-background);
            border-color: var(--mobile-primary);
        }
        
        .board-action-btn:hover,
        .board-action-btn:focus {
            background: var(--mobile-primary);
            color: var(--mobile-background);
            border-color: var(--mobile-primary);
        }
        
        .search-bar {
            position: sticky;
            top: var(--mobile-header-height);
            background: var(--mobile-background);
            padding: 16px;
            border-bottom: 1px solid var(--mobile-primary-dark);
            z-index: 100;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px;
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 24px;
            color: var(--mobile-text);
            font-size: 1rem;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--mobile-primary);
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            padding: 16px;
            overflow-x: auto;
            background: var(--mobile-background);
        }
        
        .filter-tab {
            padding: 8px 16px;
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 20px;
            color: var(--mobile-text);
            text-decoration: none;
            font-size: 0.85rem;
            white-space: nowrap;
            transition: all 0.2s ease;
        }
        
        .filter-tab.active {
            background: var(--mobile-primary);
            color: var(--mobile-background);
            border-color: var(--mobile-primary);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--mobile-text-muted);
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .context-menu {
            position: fixed;
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            z-index: 2000;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: scale(0.9);
            transition: all 0.2s ease;
        }
        
        .context-menu.show {
            opacity: 1;
            visibility: visible;
            transform: scale(1);
        }
        
        .context-menu-item {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(214, 154, 107, 0.2);
            color: var(--mobile-text);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: background 0.2s ease;
        }
        
        .context-menu-item:last-child {
            border-bottom: none;
        }
        
        .context-menu-item:hover {
            background: rgba(255, 193, 7, 0.1);
            color: var(--mobile-text);
        }
        
        .context-menu-item.danger {
            color: #dc3545;
        }
        
        .context-menu-item.danger:hover {
            background: rgba(220, 53, 69, 0.1);
        }
    </style>
</head>
<body>
    <!-- Search Bar -->
    <div class="search-bar">
        <input type="text" class="search-input" placeholder="Search boards..." id="searchInput">
    </div>
    
    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <a href="#" class="filter-tab active" data-filter="all">All Boards</a>
        <a href="#" class="filter-tab" data-filter="active">Active</a>
        <a href="#" class="filter-tab" data-filter="inactive">Inactive</a>
        <a href="#" class="filter-tab" data-filter="recent">Recent</a>
    </div>

    <!-- Mobile Content -->
    <div class="mobile-content">
        <!-- Board Grid -->
        <div class="board-grid" id="boardGrid">
            <!-- Boards will be loaded here -->
        </div>
        
        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-tv empty-icon"></i>
            <h5>No boards found</h5>
            <p>Create your first digital board to get started</p>
            <button class="mobile-btn mobile-btn-primary" onclick="createNewBoard()">
                <i class="fas fa-plus"></i>
                Create Board
            </button>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="contextMenu">
        <a href="#" class="context-menu-item" onclick="editBoard()">
            <i class="fas fa-edit"></i>
            Edit Board
        </a>
        <a href="#" class="context-menu-item" onclick="duplicateBoard()">
            <i class="fas fa-copy"></i>
            Duplicate
        </a>
        <a href="#" class="context-menu-item" onclick="previewBoard()">
            <i class="fas fa-eye"></i>
            Preview
        </a>
        <a href="#" class="context-menu-item" onclick="shareBoard()">
            <i class="fas fa-share"></i>
            Share
        </a>
        <a href="#" class="context-menu-item danger" onclick="deleteBoard()">
            <i class="fas fa-trash"></i>
            Delete
        </a>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="mobile-bottom-nav">
        <a href="mobile-dashboard.php" class="mobile-nav-item">
            <i class="fas fa-tachometer-alt mobile-nav-icon"></i>
            <span class="mobile-nav-text">Dashboard</span>
        </a>
        <a href="templates.php" class="mobile-nav-item">
            <i class="fas fa-layer-group mobile-nav-icon"></i>
            <span class="mobile-nav-text">Templates</span>
        </a>
        <a href="mobile-board-manager.php" class="mobile-nav-item active">
            <i class="fas fa-tv mobile-nav-icon"></i>
            <span class="mobile-nav-text">Boards</span>
        </a>
        <a href="media-manager.php" class="mobile-nav-item">
            <i class="fas fa-images mobile-nav-icon"></i>
            <span class="mobile-nav-text">Media</span>
        </a>
        <a href="user-management.php" class="mobile-nav-item">
            <i class="fas fa-users mobile-nav-icon"></i>
            <span class="mobile-nav-text">Users</span>
        </a>
    </nav>

    <!-- Floating Action Button -->
    <button class="mobile-fab" onclick="createNewBoard()">
        <i class="fas fa-plus"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/digital-board-mobile.js"></script>
    
    <script>
        class MobileBoardManager {
            constructor() {
                this.boards = [];
                this.filteredBoards = [];
                this.currentFilter = 'all';
                this.selectedBoard = null;
                this.init();
            }
            
            init() {
                this.loadBoards();
                this.setupEventListeners();
                this.setupSearch();
                this.setupFilters();
            }
            
            setupEventListeners() {
                // Long press for context menu
                document.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    const boardCard = e.target.closest('.board-card');
                    if (boardCard) {
                        this.showContextMenu(e, boardCard);
                    }
                });
                
                // Click outside to close context menu
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.context-menu')) {
                        this.hideContextMenu();
                    }
                });
                
                // Swipe gestures
                this.setupSwipeGestures();
            }
            
            setupSearch() {
                const searchInput = document.getElementById('searchInput');
                let searchTimeout;
                
                searchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        this.filterBoards(e.target.value);
                    }, 300);
                });
            }
            
            setupFilters() {
                const filterTabs = document.querySelectorAll('.filter-tab');
                filterTabs.forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        // Update active tab
                        filterTabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');
                        
                        // Apply filter
                        this.currentFilter = tab.dataset.filter;
                        this.applyFilter();
                    });
                });
            }
            
            setupSwipeGestures() {
                let startX, startY;
                
                document.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                }, { passive: true });
                
                document.addEventListener('touchend', (e) => {
                    if (!startX || !startY) return;
                    
                    const endX = e.changedTouches[0].clientX;
                    const endY = e.changedTouches[0].clientY;
                    
                    const deltaX = endX - startX;
                    const deltaY = endY - startY;
                    
                    const boardCard = e.target.closest('.board-card');
                    if (boardCard && Math.abs(deltaX) > 50 && Math.abs(deltaX) > Math.abs(deltaY)) {
                        if (deltaX > 0) {
                            this.handleSwipeRight(boardCard);
                        } else {
                            this.handleSwipeLeft(boardCard);
                        }
                    }
                    
                    startX = startY = null;
                }, { passive: true });
            }
            
            async loadBoards() {
                try {
                    // Simulate API call - replace with actual API
                    this.boards = await this.fetchBoards();
                    this.applyFilter();
                } catch (error) {
                    console.error('Failed to load boards:', error);
                    this.showError('Failed to load boards');
                }
            }
            
            async fetchBoards() {
                // Simulate API call
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve([
                            {
                                id: 1,
                                title: 'Main Beer Board',
                                template: 'Professional',
                                status: 'active',
                                updated: '2 hours ago',
                                preview: '../../assets/images/board-preview-1.jpg'
                            },
                            {
                                id: 2,
                                title: 'Happy Hour Specials',
                                template: 'Modern',
                                status: 'active',
                                updated: '1 day ago',
                                preview: '../../assets/images/board-preview-2.jpg'
                            },
                            {
                                id: 3,
                                title: 'Weekend Events',
                                template: 'Classic',
                                status: 'inactive',
                                updated: '3 days ago',
                                preview: '../../assets/images/board-preview-3.jpg'
                            }
                        ]);
                    }, 1000);
                });
            }
            
            filterBoards(searchTerm = '') {
                this.filteredBoards = this.boards.filter(board => {
                    const matchesSearch = !searchTerm || 
                        board.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        board.template.toLowerCase().includes(searchTerm.toLowerCase());
                    
                    const matchesFilter = this.currentFilter === 'all' || 
                        board.status === this.currentFilter ||
                        (this.currentFilter === 'recent' && this.isRecent(board));
                    
                    return matchesSearch && matchesFilter;
                });
                
                this.renderBoards();
            }
            
            applyFilter() {
                const searchTerm = document.getElementById('searchInput').value;
                this.filterBoards(searchTerm);
            }
            
            isRecent(board) {
                // Simple recent check - boards updated in last 24 hours
                return board.updated.includes('hour') || board.updated === '1 day ago';
            }
            
            renderBoards() {
                const boardGrid = document.getElementById('boardGrid');
                const emptyState = document.getElementById('emptyState');
                
                if (this.filteredBoards.length === 0) {
                    boardGrid.style.display = 'none';
                    emptyState.style.display = 'block';
                    return;
                }
                
                boardGrid.style.display = 'grid';
                emptyState.style.display = 'none';
                
                boardGrid.innerHTML = this.filteredBoards.map(board => this.renderBoardCard(board)).join('');
            }
            
            renderBoardCard(board) {
                return `
                    <div class="board-card" data-board-id="${board.id}">
                        <div class="board-preview">
                            <img src="${board.preview}" alt="${board.title}" onerror="this.style.display='none'">
                            <div class="board-status ${board.status}">${board.status}</div>
                        </div>
                        <div class="board-info">
                            <div class="board-title">${board.title}</div>
                            <div class="board-meta">
                                <span class="board-template">${board.template} Template</span>
                                <span class="board-updated">${board.updated}</span>
                            </div>
                            <div class="board-actions">
                                <button class="board-action-btn primary" onclick="mobileBoardManager.editBoard(${board.id})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="board-action-btn" onclick="mobileBoardManager.previewBoard(${board.id})">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="board-action-btn" onclick="mobileBoardManager.toggleStatus(${board.id})">
                                    <i class="fas fa-power-off"></i> ${board.status === 'active' ? 'Stop' : 'Start'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            showContextMenu(event, boardCard) {
                const boardId = boardCard.dataset.boardId;
                this.selectedBoard = this.boards.find(b => b.id == boardId);
                
                const contextMenu = document.getElementById('contextMenu');
                contextMenu.style.left = `${event.clientX}px`;
                contextMenu.style.top = `${event.clientY}px`;
                contextMenu.classList.add('show');
                
                // Haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }
            
            hideContextMenu() {
                document.getElementById('contextMenu').classList.remove('show');
            }
            
            handleSwipeRight(boardCard) {
                // Quick action - toggle status
                const boardId = boardCard.dataset.boardId;
                this.toggleStatus(boardId);
            }
            
            handleSwipeLeft(boardCard) {
                // Quick action - edit
                const boardId = boardCard.dataset.boardId;
                this.editBoard(boardId);
            }
            
            // Board actions
            editBoard(boardId) {
                console.log('Edit board:', boardId);
                window.location.href = `template-builder.php?board_id=${boardId}`;
            }
            
            previewBoard(boardId) {
                console.log('Preview board:', boardId);
                window.location.href = `display.php?board_id=${boardId}`;
            }
            
            duplicateBoard(boardId) {
                console.log('Duplicate board:', boardId || this.selectedBoard?.id);
                // Implementation for duplicating board
            }
            
            shareBoard(boardId) {
                console.log('Share board:', boardId || this.selectedBoard?.id);
                // Implementation for sharing board
            }
            
            deleteBoard(boardId) {
                const id = boardId || this.selectedBoard?.id;
                if (confirm('Are you sure you want to delete this board?')) {
                    console.log('Delete board:', id);
                    // Implementation for deleting board
                }
            }
            
            toggleStatus(boardId) {
                const board = this.boards.find(b => b.id == boardId);
                if (board) {
                    board.status = board.status === 'active' ? 'inactive' : 'active';
                    this.renderBoards();
                }
            }
            
            createNewBoard() {
                window.location.href = 'template-builder.php';
            }
            
            showError(message) {
                // Show error message
                console.error(message);
            }
        }
        
        // Global functions for context menu
        function editBoard() {
            mobileBoardManager.editBoard();
            mobileBoardManager.hideContextMenu();
        }
        
        function duplicateBoard() {
            mobileBoardManager.duplicateBoard();
            mobileBoardManager.hideContextMenu();
        }
        
        function previewBoard() {
            mobileBoardManager.previewBoard();
            mobileBoardManager.hideContextMenu();
        }
        
        function shareBoard() {
            mobileBoardManager.shareBoard();
            mobileBoardManager.hideContextMenu();
        }
        
        function deleteBoard() {
            mobileBoardManager.deleteBoard();
            mobileBoardManager.hideContextMenu();
        }
        
        function createNewBoard() {
            mobileBoardManager.createNewBoard();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            window.mobileBoardManager = new MobileBoardManager();
        });
    </script>
</body>
</html>
