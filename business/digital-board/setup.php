<?php
/**
 * Digital Beer Board Setup & Diagnostics
 * Checks and creates necessary database tables and test data
 */

require_once '../../config/config.php';
requireLogin();

$pageTitle = 'Digital Board Setup - ' . APP_NAME;

$user = getCurrentUser();
$setupMessages = [];
$errors = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check if digital_boards table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'digital_boards'");
    $digitalBoardsExists = $tableCheck->rowCount() > 0;
    
    // Check if beer_menu table exists
    $menuCheck = $conn->query("SHOW TABLES LIKE 'beer_menu'");
    $beerMenuExists = $menuCheck->rowCount() > 0;
    
    // Check user's brewery association
    $userBrewery = null;
    if ($user['role'] === 'brewery' || $user['role'] === 'business_owner') {
        if (isset($user['brewery_id']) && $user['brewery_id']) {
            $breweryQuery = "SELECT * FROM breweries WHERE id = ?";
            $stmt = $conn->prepare($breweryQuery);
            $stmt->execute([$user['brewery_id']]);
            $userBrewery = $stmt->fetch();
        }
    }
    
    // Get all breweries for admin users
    $allBreweries = [];
    if ($user['role'] === 'admin') {
        $breweriesQuery = "SELECT * FROM breweries ORDER BY name";
        $stmt = $conn->prepare($breweriesQuery);
        $stmt->execute();
        $allBreweries = $stmt->fetchAll();
    }
    
    // Auto-fix: Create digital_boards table if it doesn't exist
    if (!$digitalBoardsExists) {
        $createDigitalBoards = "
            CREATE TABLE digital_boards (
                id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                brewery_id VARCHAR(36) NOT NULL,
                board_id VARCHAR(100) NOT NULL,
                settings JSON NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
                UNIQUE KEY unique_brewery_board (brewery_id, board_id)
            )
        ";
        
        try {
            $conn->exec($createDigitalBoards);
            $setupMessages[] = "✅ Created digital_boards table successfully";
            $digitalBoardsExists = true;
        } catch (Exception $e) {
            $errors[] = "❌ Failed to create digital_boards table: " . $e->getMessage();
        }
    } else {
        $setupMessages[] = "✅ digital_boards table exists";
    }
    
    // Auto-fix: Create beer_menu table if it doesn't exist
    if (!$beerMenuExists) {
        $createBeerMenu = "
            CREATE TABLE beer_menu (
                id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                brewery_id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT NULL,
                style_id VARCHAR(36) NULL,
                abv DECIMAL(4,2) NULL,
                ibu INT NULL,
                srm INT NULL,
                price DECIMAL(8,2) NULL,
                tap_number INT NULL,
                is_available BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE
            )
        ";
        
        try {
            $conn->exec($createBeerMenu);
            $setupMessages[] = "✅ Created beer_menu table successfully";
            $beerMenuExists = true;
        } catch (Exception $e) {
            $errors[] = "❌ Failed to create beer_menu table: " . $e->getMessage();
        }
    } else {
        $setupMessages[] = "✅ beer_menu table exists";
    }
    
    // Auto-fix: Create a test brewery if user doesn't have one
    if (($user['role'] === 'brewery' || $user['role'] === 'business_owner') && !$userBrewery) {
        $createBrewery = "
            INSERT INTO breweries (id, name, city, state, description, brewery_type, claimed, verified)
            VALUES (UUID(), ?, 'Demo City', 'Demo State', 'Demo brewery for testing digital board features', 'micro', 1, 1)
        ";
        
        try {
            $stmt = $conn->prepare($createBrewery);
            $stmt->execute(["Demo Brewery - " . $user['email']]);
            $newBreweryId = $conn->lastInsertId();
            
            // Update user's brewery_id
            $updateUser = "UPDATE users SET brewery_id = ? WHERE id = ?";
            $stmt = $conn->prepare($updateUser);
            $stmt->execute([$newBreweryId, $user['id']]);
            
            $setupMessages[] = "✅ Created demo brewery and associated with your account";
            
            // Refresh user data
            $userBrewery = [
                'id' => $newBreweryId,
                'name' => "Demo Brewery - " . $user['email'],
                'city' => 'Demo City',
                'state' => 'Demo State'
            ];
            
        } catch (Exception $e) {
            $errors[] = "❌ Failed to create demo brewery: " . $e->getMessage();
        }
    }
    
    // Auto-fix: Add sample beer menu items
    if ($userBrewery && $beerMenuExists) {
        $menuCountQuery = "SELECT COUNT(*) as count FROM beer_menu WHERE brewery_id = ?";
        $stmt = $conn->prepare($menuCountQuery);
        $stmt->execute([$userBrewery['id']]);
        $menuCount = $stmt->fetch()['count'];
        
        if ($menuCount == 0) {
            $sampleBeers = [
                ['name' => 'House IPA', 'description' => 'Our signature India Pale Ale with citrus notes', 'abv' => 6.5, 'ibu' => 65, 'price' => 7.50, 'tap_number' => 1],
                ['name' => 'Wheat Beer', 'description' => 'Light and refreshing wheat beer perfect for any season', 'abv' => 4.8, 'ibu' => 15, 'price' => 6.00, 'tap_number' => 2],
                ['name' => 'Stout', 'description' => 'Rich and creamy stout with chocolate and coffee notes', 'abv' => 7.2, 'ibu' => 35, 'price' => 8.00, 'tap_number' => 3],
                ['name' => 'Lager', 'description' => 'Crisp and clean lager, our most popular beer', 'abv' => 5.0, 'ibu' => 20, 'price' => 5.50, 'tap_number' => 4],
                ['name' => 'Seasonal Ale', 'description' => 'Limited edition seasonal brew', 'abv' => 6.0, 'ibu' => 40, 'price' => 7.00, 'tap_number' => 5]
            ];
            
            $insertBeer = "
                INSERT INTO beer_menu (id, brewery_id, name, description, abv, ibu, price, tap_number, is_available)
                VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 1)
            ";
            
            $stmt = $conn->prepare($insertBeer);
            foreach ($sampleBeers as $beer) {
                try {
                    $stmt->execute([
                        $userBrewery['id'],
                        $beer['name'],
                        $beer['description'],
                        $beer['abv'],
                        $beer['ibu'],
                        $beer['price'],
                        $beer['tap_number']
                    ]);
                } catch (Exception $e) {
                    $errors[] = "❌ Failed to create sample beer: " . $beer['name'];
                }
            }
            
            $setupMessages[] = "✅ Added sample beer menu items";
        } else {
            $setupMessages[] = "✅ Beer menu already has {$menuCount} items";
        }
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Database error: " . $e->getMessage();
}

require_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Page Header -->
            <div class="text-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-tools me-2 text-primary"></i>Digital Beer Board Setup
                </h1>
                <p class="text-muted">Diagnostic and setup tool for the Digital Beer Board system</p>
            </div>
            
            <!-- User Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Current User Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                            <p><strong>Role:</strong> 
                                <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'brewery' ? 'success' : 'primary'); ?>">
                                    <?php echo htmlspecialchars($user['role']); ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>User ID:</strong> <?php echo htmlspecialchars($user['id']); ?></p>
                            <p><strong>Brewery ID:</strong> 
                                <?php if (isset($user['brewery_id']) && $user['brewery_id']): ?>
                                    <?php echo htmlspecialchars($user['brewery_id']); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not assigned</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Setup Messages -->
            <?php if (!empty($setupMessages) || !empty($errors)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>Setup Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($setupMessages as $message): ?>
                            <div class="alert alert-success mb-2"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                        
                        <?php foreach ($errors as $error): ?>
                            <div class="alert alert-danger mb-2"><?php echo $error; ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Brewery Information -->
            <?php if ($userBrewery): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Your Brewery
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6><?php echo htmlspecialchars($userBrewery['name']); ?></h6>
                        <p class="text-muted mb-2">
                            <?php echo htmlspecialchars($userBrewery['city'] . ', ' . $userBrewery['state']); ?>
                        </p>
                        <p class="mb-0">
                            <strong>Brewery ID:</strong> <?php echo htmlspecialchars($userBrewery['id']); ?>
                        </p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Admin: All Breweries -->
            <?php if ($user['role'] === 'admin' && !empty($allBreweries)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Breweries (Admin View)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>ID</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($allBreweries as $brewery): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($brewery['name']); ?></td>
                                            <td><?php echo htmlspecialchars($brewery['city'] . ', ' . $brewery['state']); ?></td>
                                            <td><small><?php echo htmlspecialchars($brewery['id']); ?></small></td>
                                            <td>
                                                <a href="../digital-board/index.php?brewery_id=<?php echo $brewery['id']; ?>" 
                                                   class="btn btn-sm btn-primary">
                                                    Manage Board
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Next Steps -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-arrow-right me-2"></i>Next Steps
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($errors)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Setup completed successfully! You can now access the Digital Beer Board.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <?php if ($userBrewery): ?>
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-tv me-2"></i>Access Digital Beer Board
                                </a>
                            <?php elseif ($user['role'] === 'admin' && !empty($allBreweries)): ?>
                                <a href="index.php?brewery_id=<?php echo $allBreweries[0]['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-tv me-2"></i>Access Digital Beer Board (Admin)
                                </a>
                            <?php endif; ?>
                            
                            <a href="../../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Back to Homepage
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Some setup issues were encountered. Please check the errors above or contact support.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="window.location.reload()">
                                <i class="fas fa-redo me-2"></i>Retry Setup
                            </button>
                            
                            <a href="../../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Back to Homepage
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../includes/footer.php'; ?>
