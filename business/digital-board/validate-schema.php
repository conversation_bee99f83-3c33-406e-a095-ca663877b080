<?php
/**
 * Digital Board Schema Validation Tool
 * Phase 4 10.0 - Database Schema Enhancement
 * 
 * This tool validates the database schema and provides detailed diagnostics
 * for the enhanced digital board system.
 */

require_once '../../config/database.php';

// Set content type to HTML for better display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema Validation - Digital Board System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #1a1a1a; color: #f5f5dc; font-family: 'Inter', sans-serif; }
        .container { max-width: 1400px; }
        .card { background: #2c1810; border: 1px solid #d69a6b; }
        .card-header { background: #6f4c3e; color: #f5f5dc; }
        .table-dark { background: #2c1810; }
        .table-dark td, .table-dark th { border-color: #d69a6b; }
        .badge-success { background: #4caf50; }
        .badge-warning { background: #ff9800; }
        .badge-danger { background: #f44336; }
        .badge-info { background: #2196f3; }
        .validation-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .validation-success { background: rgba(76, 175, 80, 0.1); border-left: 3px solid #4caf50; }
        .validation-warning { background: rgba(255, 152, 0, 0.1); border-left: 3px solid #ff9800; }
        .validation-error { background: rgba(244, 67, 54, 0.1); border-left: 3px solid #f44336; }
        .validation-info { background: rgba(33, 150, 243, 0.1); border-left: 3px solid #2196f3; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-check-double"></i>
                            Digital Board Schema Validation
                        </h1>
                        <p class="mb-0 mt-2">Phase 4 10.0 - Enhanced Database Schema Diagnostics</p>
                    </div>
                    <div class="card-body">

<?php
$validationResults = [];
$overallStatus = 'success';

try {
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // Define expected schema structure
    $expectedTables = [
        'digital_boards' => [
            'description' => 'Enhanced digital boards with full settings',
            'required_columns' => [
                'id', 'brewery_id', 'board_id', 'name', 'description', 'settings',
                'template_id', 'current_slideshow_id', 'display_mode', 'theme',
                'layout', 'columns_count', 'show_prices', 'show_descriptions',
                'show_abv', 'show_ibu', 'show_tap_numbers', 'show_availability',
                'ticker_message', 'ticker_enabled', 'is_active', 'created_at'
            ],
            'indexes' => [
                'idx_digital_boards_brewery', 'idx_digital_boards_active',
                'idx_digital_boards_mode', 'idx_digital_boards_template'
            ]
        ],
        'slideshow_presentations' => [
            'description' => 'Slideshow presentation management',
            'required_columns' => [
                'id', 'brewery_id', 'name', 'description', 'is_active',
                'loop_enabled', 'auto_advance', 'global_duration', 'global_transition',
                'total_slides', 'total_duration', 'created_at', 'updated_at'
            ],
            'indexes' => [
                'idx_slideshow_brewery', 'idx_slideshow_active'
            ]
        ],
        'slideshow_slides' => [
            'description' => 'Individual slides with ordering and content',
            'required_columns' => [
                'id', 'slideshow_id', 'slide_order', 'title', 'slide_type',
                'content_id', 'template_id', 'duration', 'transition',
                'settings', 'is_active', 'created_at', 'updated_at'
            ],
            'indexes' => [
                'idx_slides_slideshow', 'idx_slides_order', 'idx_slides_type'
            ]
        ],
        'template_library' => [
            'description' => 'Custom template storage and management',
            'required_columns' => [
                'id', 'brewery_id', 'name', 'description', 'category',
                'template_type', 'theme_settings', 'layout_settings',
                'color_scheme', 'typography_settings', 'is_public', 'is_active'
            ],
            'indexes' => [
                'idx_template_brewery', 'idx_template_category', 'idx_template_type'
            ]
        ],
        'slide_content' => [
            'description' => 'Media and content storage for slides',
            'required_columns' => [
                'id', 'brewery_id', 'content_type', 'filename', 'file_path',
                'file_size', 'mime_type', 'title', 'processing_status',
                'is_active', 'created_at', 'updated_at'
            ],
            'indexes' => [
                'idx_content_brewery', 'idx_content_type', 'idx_content_status'
            ]
        ]
    ];
    
    // Validate each table
    foreach ($expectedTables as $tableName => $tableInfo) {
        $tableStatus = 'success';
        $tableMessages = [];
        
        // Check if table exists
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
            if ($stmt->rowCount() == 0) {
                $tableStatus = 'error';
                $tableMessages[] = "Table '$tableName' does not exist";
                $overallStatus = 'error';
                continue;
            }
        } catch (PDOException $e) {
            $tableStatus = 'error';
            $tableMessages[] = "Error checking table '$tableName': " . $e->getMessage();
            $overallStatus = 'error';
            continue;
        }
        
        // Check table structure
        try {
            $stmt = $pdo->query("DESCRIBE $tableName");
            $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $missingColumns = array_diff($tableInfo['required_columns'], $existingColumns);
            $extraColumns = array_diff($existingColumns, $tableInfo['required_columns']);
            
            if (!empty($missingColumns)) {
                $tableStatus = 'warning';
                $tableMessages[] = "Missing columns: " . implode(', ', $missingColumns);
                if ($overallStatus === 'success') $overallStatus = 'warning';
            }
            
            if (!empty($extraColumns)) {
                $tableMessages[] = "Extra columns found: " . implode(', ', $extraColumns);
            }
            
            $tableMessages[] = "Total columns: " . count($existingColumns);
            
        } catch (PDOException $e) {
            $tableStatus = 'error';
            $tableMessages[] = "Error checking columns: " . $e->getMessage();
            $overallStatus = 'error';
        }
        
        // Check indexes
        try {
            $stmt = $pdo->query("SHOW INDEX FROM $tableName");
            $existingIndexes = array_unique($stmt->fetchAll(PDO::FETCH_COLUMN, 2)); // Key_name column
            
            $missingIndexes = array_diff($tableInfo['indexes'], $existingIndexes);
            
            if (!empty($missingIndexes)) {
                $tableStatus = 'warning';
                $tableMessages[] = "Missing indexes: " . implode(', ', $missingIndexes);
                if ($overallStatus === 'success') $overallStatus = 'warning';
            }
            
            $tableMessages[] = "Indexes found: " . count($existingIndexes);
            
        } catch (PDOException $e) {
            $tableMessages[] = "Could not check indexes: " . $e->getMessage();
        }
        
        // Check row count
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $tableName");
            $rowCount = $stmt->fetchColumn();
            $tableMessages[] = "Records: $rowCount";
        } catch (PDOException $e) {
            $tableMessages[] = "Could not count records: " . $e->getMessage();
        }
        
        $validationResults[$tableName] = [
            'status' => $tableStatus,
            'description' => $tableInfo['description'],
            'messages' => $tableMessages
        ];
    }
    
    // Check foreign key relationships
    try {
        $stmt = $pdo->query("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = '$database' 
            AND TABLE_NAME IN ('" . implode("','", array_keys($expectedTables)) . "')
            ORDER BY TABLE_NAME, COLUMN_NAME
        ");
        $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $validationResults['foreign_keys'] = [
            'status' => count($foreignKeys) >= 4 ? 'success' : 'warning',
            'description' => 'Foreign key relationships',
            'messages' => [
                "Foreign key constraints found: " . count($foreignKeys),
                "Expected minimum: 4 constraints"
            ]
        ];
        
    } catch (PDOException $e) {
        $validationResults['foreign_keys'] = [
            'status' => 'info',
            'description' => 'Foreign key relationships',
            'messages' => ["Could not verify foreign keys (normal for some MySQL configurations)"]
        ];
    }
    
    // Check system templates
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM template_library WHERE category = 'system'");
        $systemTemplates = $stmt->fetchColumn();
        
        $validationResults['system_templates'] = [
            'status' => $systemTemplates >= 4 ? 'success' : 'warning',
            'description' => 'System templates',
            'messages' => [
                "System templates installed: $systemTemplates",
                "Expected: 4 templates (Classic Dark, Brewery Wood, Modern Light, Beersty Professional)"
            ]
        ];
        
    } catch (PDOException $e) {
        $validationResults['system_templates'] = [
            'status' => 'error',
            'description' => 'System templates',
            'messages' => ["Error checking system templates: " . $e->getMessage()]
        ];
    }
    
} catch (PDOException $e) {
    $validationResults['connection'] = [
        'status' => 'error',
        'description' => 'Database connection',
        'messages' => ["Failed to connect: " . $e->getMessage()]
    ];
    $overallStatus = 'error';
}

// Display overall status
$statusBadge = [
    'success' => 'badge-success',
    'warning' => 'badge-warning',
    'error' => 'badge-danger'
][$overallStatus];

$statusIcon = [
    'success' => 'fas fa-check-circle',
    'warning' => 'fas fa-exclamation-triangle',
    'error' => 'fas fa-times-circle'
][$overallStatus];

$statusMessage = [
    'success' => 'All schema validations passed successfully!',
    'warning' => 'Schema validation completed with warnings.',
    'error' => 'Schema validation failed with errors.'
][$overallStatus];

echo "<div class='alert alert-" . ($overallStatus === 'success' ? 'success' : ($overallStatus === 'warning' ? 'warning' : 'danger')) . " mb-4'>";
echo "<h5><i class='$statusIcon'></i> Overall Status: <span class='badge $statusBadge'>$overallStatus</span></h5>";
echo "<p class='mb-0'>$statusMessage</p>";
echo "</div>";

// Display detailed results
echo "<div class='row'>";
foreach ($validationResults as $component => $result) {
    $statusClass = 'validation-' . $result['status'];
    $statusIcon = [
        'success' => 'fas fa-check-circle text-success',
        'warning' => 'fas fa-exclamation-triangle text-warning',
        'error' => 'fas fa-times-circle text-danger',
        'info' => 'fas fa-info-circle text-info'
    ][$result['status']];
    
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='validation-item $statusClass'>";
    echo "<h6><i class='$statusIcon'></i> " . ucwords(str_replace('_', ' ', $component)) . "</h6>";
    echo "<p class='small text-muted mb-2'>{$result['description']}</p>";
    foreach ($result['messages'] as $message) {
        echo "<div class='small'>• $message</div>";
    }
    echo "</div>";
    echo "</div>";
}
echo "</div>";
?>

                        <div class="mt-4">
                            <h5>Schema Information</h5>
                            <div class="table-responsive">
                                <table class="table table-dark table-sm">
                                    <thead>
                                        <tr>
                                            <th>Component</th>
                                            <th>Purpose</th>
                                            <th>Phase</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>digital_boards</td>
                                            <td>Enhanced digital board management with full settings</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">Enhanced</span></td>
                                        </tr>
                                        <tr>
                                            <td>slideshow_presentations</td>
                                            <td>Slideshow configuration and management</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">New</span></td>
                                        </tr>
                                        <tr>
                                            <td>slideshow_slides</td>
                                            <td>Individual slide content and ordering</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">New</span></td>
                                        </tr>
                                        <tr>
                                            <td>template_library</td>
                                            <td>Custom template storage and sharing</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">New</span></td>
                                        </tr>
                                        <tr>
                                            <td>slide_content</td>
                                            <td>Media and content file management</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">New</span></td>
                                        </tr>
                                        <tr>
                                            <td>beer_menu</td>
                                            <td>Enhanced beer menu with advanced features</td>
                                            <td>Phase 4 10.0</td>
                                            <td><span class="badge badge-success">Enhanced</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Quick Actions</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="setup-enhanced.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-cog"></i> Setup System
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="index.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button onclick="location.reload()" class="btn btn-secondary w-100 mb-2">
                                        <i class="fas fa-sync-alt"></i> Refresh Validation
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <a href="../../database/" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-database"></i> View SQL Files
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
