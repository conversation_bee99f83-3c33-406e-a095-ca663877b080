<?php
/**
 * Digital Beer Board Templates Manager
 * Design and customize digital board layouts and themes
 */

require_once '../../config/config.php';

$pageTitle = 'Digital Board Templates - Beersty';

// Demo templates data
$templates = [
    [
        'id' => 'classic-dark',
        'name' => 'Classic Dark',
        'description' => 'Traditional dark theme with gold accents',
        'category' => 'Dark Themes',
        'preview_image' => 'classic-dark.jpg',
        'settings' => [
            'theme' => 'dark',
            'layout' => 'grid',
            'background_color' => '#1a1a1a',
            'accent_color' => '#ffc107',
            'text_color' => '#ffffff',
            'card_style' => 'rounded',
            'font_family' => 'Segoe UI',
            'show_tap_numbers' => true,
            'show_prices' => true,
            'show_descriptions' => true
        ]
    ],
    [
        'id' => 'brewery-wood',
        'name' => 'Brewery Wood',
        'description' => 'Warm wood theme with rustic brewery feel',
        'category' => 'Rustic Themes',
        'preview_image' => 'brewery-wood.jpg',
        'settings' => [
            'theme' => 'wood',
            'layout' => 'grid',
            'background_color' => '#8B4513',
            'accent_color' => '#D2691E',
            'text_color' => '#F5DEB3',
            'card_style' => 'wood',
            'font_family' => 'Georgia',
            'show_tap_numbers' => true,
            'show_prices' => true,
            'show_descriptions' => true
        ]
    ],
    [
        'id' => 'modern-light',
        'name' => 'Modern Light',
        'description' => 'Clean, modern light theme for contemporary spaces',
        'category' => 'Light Themes',
        'preview_image' => 'modern-light.jpg',
        'settings' => [
            'theme' => 'light',
            'layout' => 'grid',
            'background_color' => '#f8f9fa',
            'accent_color' => '#007bff',
            'text_color' => '#333333',
            'card_style' => 'modern',
            'font_family' => 'Arial',
            'show_tap_numbers' => true,
            'show_prices' => true,
            'show_descriptions' => true
        ]
    ],
    [
        'id' => 'industrial-steel',
        'name' => 'Industrial Steel',
        'description' => 'Industrial theme with steel and copper accents',
        'category' => 'Industrial Themes',
        'preview_image' => 'industrial-steel.jpg',
        'settings' => [
            'theme' => 'industrial',
            'layout' => 'list',
            'background_color' => '#2c3e50',
            'accent_color' => '#e67e22',
            'text_color' => '#ecf0f1',
            'card_style' => 'industrial',
            'font_family' => 'Roboto',
            'show_tap_numbers' => true,
            'show_prices' => true,
            'show_descriptions' => false
        ]
    ],
    [
        'id' => 'craft-vintage',
        'name' => 'Craft Vintage',
        'description' => 'Vintage-inspired design with craft beer aesthetics',
        'category' => 'Vintage Themes',
        'preview_image' => 'craft-vintage.jpg',
        'settings' => [
            'theme' => 'vintage',
            'layout' => 'grid',
            'background_color' => '#3e2723',
            'accent_color' => '#ff8f00',
            'text_color' => '#fff8e1',
            'card_style' => 'vintage',
            'font_family' => 'Times New Roman',
            'show_tap_numbers' => true,
            'show_prices' => true,
            'show_descriptions' => true
        ]
    ],
    [
        'id' => 'minimalist-clean',
        'name' => 'Minimalist Clean',
        'description' => 'Ultra-clean minimalist design',
        'category' => 'Minimalist Themes',
        'preview_image' => 'minimalist-clean.jpg',
        'settings' => [
            'theme' => 'minimal',
            'layout' => 'list',
            'background_color' => '#ffffff',
            'accent_color' => '#000000',
            'text_color' => '#333333',
            'card_style' => 'minimal',
            'font_family' => 'Helvetica',
            'show_tap_numbers' => false,
            'show_prices' => true,
            'show_descriptions' => false
        ]
    ],
    [
        'id' => 'beersty-professional',
        'name' => 'Beersty Professional',
        'description' => 'Professional two-column layout with beer images and multiple pricing',
        'category' => 'Professional Themes',
        'preview_image' => 'beersty-professional.jpg',
        'settings' => [
            'theme' => 'professional',
            'layout' => 'columns',
            'background_color' => '#000000',
            'accent_color' => '#6c757d',
            'text_color' => '#f8f9fa',
            'card_style' => 'professional',
            'font_family' => 'Bootstrap',
            'show_tap_numbers' => false,
            'show_prices' => true,
            'show_descriptions' => true,
            'show_beer_images' => true,
            'show_multiple_sizes' => true
        ]
    ]
];

$categories = array_unique(array_column($templates, 'category'));
$selectedCategory = $_GET['category'] ?? 'all';
$selectedTemplate = $_GET['template'] ?? null;

// Filter templates by category
$filteredTemplates = $selectedCategory === 'all' 
    ? $templates 
    : array_filter($templates, fn($t) => $t['category'] === $selectedCategory);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .main-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .template-card { 
            border: 2px solid #e9ecef; 
            border-radius: 10px; 
            transition: all 0.3s ease; 
            cursor: pointer;
            height: 100%;
        }
        .template-card:hover { 
            border-color: #007bff; 
            transform: translateY(-5px); 
            box-shadow: 0 10px 25px rgba(0,123,255,0.15);
        }
        .template-card.selected { 
            border-color: #28a745; 
            background-color: #f8fff9;
        }
        .template-preview { 
            height: 200px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            border-radius: 8px; 
            position: relative;
            overflow: hidden;
        }
        .template-preview.dark { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); }
        .template-preview.wood { background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%); }
        .template-preview.light { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); }
        .template-preview.industrial { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); }
        .template-preview.vintage { background: linear-gradient(135deg, #3e2723 0%, #5d4037 100%); }
        .template-preview.minimal { background: #ffffff; border: 1px solid #dee2e6; }
        
        .preview-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            width: 90%;
        }
        .template-preview.light .preview-content,
        .template-preview.minimal .preview-content { color: #333; }
        
        .category-filter { margin-bottom: 2rem; }
        .category-btn { margin-right: 0.5rem; margin-bottom: 0.5rem; }
        .design-panel { background: #f8f9fa; border-radius: 10px; padding: 2rem; }
        .color-picker { width: 50px; height: 35px; border: none; border-radius: 5px; cursor: pointer; }
        .preview-frame { 
            border: 3px solid #dee2e6; 
            border-radius: 10px; 
            height: 400px; 
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="main-container p-4">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-2">
                    <i class="fas fa-palette me-2 text-primary"></i>Digital Board Templates
                </h1>
                <p class="text-muted">Design and customize your digital beer board appearance</p>
            </div>
            
            <!-- Category Filter -->
            <div class="category-filter">
                <h6 class="mb-3">Filter by Category:</h6>
                <button class="btn btn-<?php echo $selectedCategory === 'all' ? 'primary' : 'outline-primary'; ?> category-btn" 
                        onclick="filterCategory('all')">
                    All Templates
                </button>
                <?php foreach ($categories as $category): ?>
                    <button class="btn btn-<?php echo $selectedCategory === $category ? 'primary' : 'outline-primary'; ?> category-btn" 
                            onclick="filterCategory('<?php echo urlencode($category); ?>')">
                        <?php echo htmlspecialchars($category); ?>
                    </button>
                <?php endforeach; ?>
            </div>
            
            <div class="row">
                <!-- Templates Grid -->
                <div class="col-lg-8">
                    <div class="row">
                        <?php foreach ($filteredTemplates as $template): ?>
                            <div class="col-md-6 mb-4">
                                <div class="template-card p-3 <?php echo $selectedTemplate === $template['id'] ? 'selected' : ''; ?>" 
                                     onclick="selectTemplate('<?php echo $template['id']; ?>')">
                                    <div class="template-preview <?php echo $template['settings']['theme']; ?> mb-3">
                                        <div class="preview-content">
                                            <h6 class="mb-2"><?php echo htmlspecialchars($template['name']); ?></h6>
                                            <div class="d-flex justify-content-center gap-2">
                                                <div style="width: 20px; height: 20px; background: <?php echo $template['settings']['accent_color']; ?>; border-radius: 3px;"></div>
                                                <div style="width: 20px; height: 20px; background: <?php echo $template['settings']['background_color']; ?>; border-radius: 3px; border: 1px solid rgba(255,255,255,0.3);"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6 class="mb-2"><?php echo htmlspecialchars($template['name']); ?></h6>
                                    <p class="text-muted small mb-2"><?php echo htmlspecialchars($template['description']); ?></p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($template['category']); ?></span>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="previewTemplate('<?php echo $template['id']; ?>')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-primary" onclick="useTemplate('<?php echo $template['id']; ?>')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Design Panel -->
                <div class="col-lg-4">
                    <div class="design-panel">
                        <h5 class="mb-3">
                            <i class="fas fa-paint-brush me-2"></i>Custom Design
                        </h5>
                        
                        <!-- Template Info -->
                        <div id="templateInfo" class="mb-4" style="display: none;">
                            <h6 id="templateName"></h6>
                            <p id="templateDescription" class="text-muted small"></p>
                        </div>
                        
                        <!-- Color Customization -->
                        <div class="mb-4">
                            <h6>Colors</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <label class="form-label small">Background</label>
                                    <input type="color" id="backgroundColor" class="color-picker form-control" value="#1a1a1a">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Accent</label>
                                    <input type="color" id="accentColor" class="color-picker form-control" value="#ffc107">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Text</label>
                                    <input type="color" id="textColor" class="color-picker form-control" value="#ffffff">
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">Cards</label>
                                    <input type="color" id="cardColor" class="color-picker form-control" value="#2d2d2d">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Layout Options -->
                        <div class="mb-4">
                            <h6>Layout</h6>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="layout" id="gridLayout" value="grid" checked>
                                <label class="btn btn-outline-primary" for="gridLayout">
                                    <i class="fas fa-th me-1"></i>Grid
                                </label>
                                <input type="radio" class="btn-check" name="layout" id="listLayout" value="list">
                                <label class="btn btn-outline-primary" for="listLayout">
                                    <i class="fas fa-list me-1"></i>List
                                </label>
                            </div>
                        </div>
                        
                        <!-- Font Options -->
                        <div class="mb-4">
                            <h6>Font Family</h6>
                            <select id="fontFamily" class="form-select">
                                <option value="Segoe UI">Segoe UI</option>
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Georgia">Georgia</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Roboto">Roboto</option>
                            </select>
                        </div>
                        
                        <!-- Display Options -->
                        <div class="mb-4">
                            <h6>Display Options</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showTapNumbers" checked>
                                <label class="form-check-label" for="showTapNumbers">Show Tap Numbers</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showPrices" checked>
                                <label class="form-check-label" for="showPrices">Show Prices</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showDescriptions" checked>
                                <label class="form-check-label" for="showDescriptions">Show Descriptions</label>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="applyCustomDesign()">
                                <i class="fas fa-save me-2"></i>Apply Design
                            </button>
                            <button class="btn btn-outline-secondary" onclick="previewCustom()">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                            <button class="btn btn-outline-info" onclick="saveAsTemplate()">
                                <i class="fas fa-plus me-2"></i>Save as Template
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Template Actions -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-magic me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="createBlankTemplate()">
                                    <i class="fas fa-plus me-2"></i>Create Blank Template
                                </button>
                                <button class="btn btn-outline-primary" onclick="duplicateTemplate()">
                                    <i class="fas fa-copy me-2"></i>Duplicate Selected
                                </button>
                                <button class="btn btn-outline-info" onclick="importTemplate()">
                                    <i class="fas fa-upload me-2"></i>Import Template
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Template Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="small mb-0">
                                <li>Dark themes work best for bars and restaurants</li>
                                <li>Light themes are great for daytime venues</li>
                                <li>Use high contrast colors for better readability</li>
                                <li>Test your template on different screen sizes</li>
                                <li>Consider your brand colors when customizing</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="demo.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Demo
                </a>
                <a href="display-simple.php" class="btn btn-success me-2" target="_blank">
                    <i class="fas fa-tv me-2"></i>View Display
                </a>
                <a href="template-builder.php" class="btn btn-warning me-2">
                    <i class="fas fa-tools me-2"></i>Advanced Builder
                </a>
                <a href="slideshow-builder.php" class="btn btn-info">
                    <i class="fas fa-play-circle me-2"></i>Slideshow Builder
                </a>
            </div>
        </div>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const templates = <?php echo json_encode($templates); ?>;
        let selectedTemplateId = null;
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }
        
        function filterCategory(category) {
            const url = new URL(window.location);
            url.searchParams.set('category', category);
            window.location.href = url.toString();
        }
        
        function selectTemplate(templateId) {
            selectedTemplateId = templateId;
            
            // Remove previous selection
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked template
            event.currentTarget.classList.add('selected');
            
            // Load template settings
            const template = templates.find(t => t.id === templateId);
            if (template) {
                loadTemplateSettings(template);
                showToast(`Selected template: ${template.name}`, 'success');
            }
        }
        
        function loadTemplateSettings(template) {
            document.getElementById('templateInfo').style.display = 'block';
            document.getElementById('templateName').textContent = template.name;
            document.getElementById('templateDescription').textContent = template.description;
            
            // Load settings into form
            document.getElementById('backgroundColor').value = template.settings.background_color;
            document.getElementById('accentColor').value = template.settings.accent_color;
            document.getElementById('textColor').value = template.settings.text_color;
            document.getElementById('fontFamily').value = template.settings.font_family;
            document.getElementById('showTapNumbers').checked = template.settings.show_tap_numbers;
            document.getElementById('showPrices').checked = template.settings.show_prices;
            document.getElementById('showDescriptions').checked = template.settings.show_descriptions;
            
            // Set layout
            if (template.settings.layout === 'grid') {
                document.getElementById('gridLayout').checked = true;
            } else {
                document.getElementById('listLayout').checked = true;
            }
        }
        
        function previewTemplate(templateId) {
            const template = templates.find(t => t.id === templateId);
            if (template) {
                const previewUrl = `display-template-preview.php?template=${templateId}`;
                window.open(previewUrl, '_blank', 'width=1200,height=800');
                showToast(`Previewing ${template.name}`, 'info');
            }
        }
        
        function useTemplate(templateId) {
            const template = templates.find(t => t.id === templateId);
            if (template) {
                // Apply template to current board
                showToast(`Applied template: ${template.name}`, 'success');
                
                // Redirect to display with template
                setTimeout(() => {
                    window.open(`display-template-preview.php?template=${templateId}`, '_blank');
                }, 1000);
            }
        }
        
        function applyCustomDesign() {
            const customSettings = {
                background_color: document.getElementById('backgroundColor').value,
                accent_color: document.getElementById('accentColor').value,
                text_color: document.getElementById('textColor').value,
                font_family: document.getElementById('fontFamily').value,
                layout: document.querySelector('input[name="layout"]:checked').value,
                show_tap_numbers: document.getElementById('showTapNumbers').checked,
                show_prices: document.getElementById('showPrices').checked,
                show_descriptions: document.getElementById('showDescriptions').checked
            };
            
            showToast('Custom design applied successfully!', 'success');
            console.log('Custom settings:', customSettings);
        }
        
        function previewCustom() {
            showToast('Opening custom design preview...', 'info');
            // Would open preview with custom settings
        }
        
        function saveAsTemplate() {
            const templateName = prompt('Enter a name for your custom template:');
            if (templateName) {
                showToast(`Template "${templateName}" saved successfully!`, 'success');
            }
        }

        function createBlankTemplate() {
            showToast('Opening template builder for blank template...', 'info');
            setTimeout(() => {
                window.open('template-builder.php?mode=blank', '_blank');
            }, 1000);
        }

        function duplicateTemplate() {
            if (selectedTemplateId) {
                const template = templates.find(t => t.id === selectedTemplateId);
                showToast(`Duplicating template: ${template.name}`, 'info');
                setTimeout(() => {
                    window.open(`template-builder.php?duplicate=${selectedTemplateId}`, '_blank');
                }, 1000);
            } else {
                showToast('Please select a template to duplicate', 'warning');
            }
        }

        function importTemplate() {
            showToast('Template import functionality coming soon!', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Digital Board Templates Manager loaded');
            console.log(`${templates.length} templates available`);
        });
    </script>
</body>
</html>
