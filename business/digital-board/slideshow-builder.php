<?php
/**
 * Digital Board Slideshow Builder
 * Create dynamic presentations with beer boards, images, videos, and custom content
 */

require_once '../../config/config.php';

$pageTitle = 'Slideshow Builder - Beersty';

// Demo slideshow data
$slideshow = [
    'id' => 'demo-slideshow-1',
    'name' => 'Demo Brewery Slideshow',
    'description' => 'Professional slideshow with beer board, promotions, and videos',
    'is_active' => true,
    'loop' => true,
    'auto_advance' => true,
    'global_transition' => 'fade',
    'global_duration' => 10,
    'slides' => [
        [
            'id' => 'slide-1',
            'type' => 'beer_board',
            'title' => 'Current Beer Menu',
            'template' => 'beersty-professional',
            'duration' => 15,
            'transition' => 'fade',
            'order' => 1,
            'settings' => [
                'show_header' => true,
                'show_ticker' => true,
                'custom_message' => 'Fresh Craft Beers on Tap!'
            ]
        ],
        [
            'id' => 'slide-2',
            'type' => 'image',
            'title' => 'Happy Hour Special',
            'content' => 'assets/images/happy-hour-promo.jpg',
            'duration' => 8,
            'transition' => 'slide_left',
            'order' => 2,
            'settings' => [
                'overlay_text' => 'Happy Hour 4-6 PM Daily!',
                'overlay_position' => 'bottom',
                'background_color' => '#000000'
            ]
        ],
        [
            'id' => 'slide-3',
            'type' => 'video',
            'title' => 'Brewery Tour',
            'content' => 'assets/videos/brewery-tour.mp4',
            'duration' => 20,
            'transition' => 'fade',
            'order' => 3,
            'settings' => [
                'autoplay' => true,
                'muted' => true,
                'loop_video' => false,
                'show_controls' => false
            ]
        ],
        [
            'id' => 'slide-4',
            'type' => 'custom_html',
            'title' => 'Events Calendar',
            'content' => '<div class="events-slide">...</div>',
            'duration' => 12,
            'transition' => 'slide_up',
            'order' => 4,
            'settings' => [
                'background_color' => '#2c3e50',
                'text_color' => '#ffffff'
            ]
        ]
    ]
];

// Available transitions
$transitions = [
    'fade' => 'Fade In/Out',
    'slide_left' => 'Slide Left',
    'slide_right' => 'Slide Right',
    'slide_up' => 'Slide Up',
    'slide_down' => 'Slide Down',
    'zoom_in' => 'Zoom In',
    'zoom_out' => 'Zoom Out',
    'flip_horizontal' => 'Flip Horizontal',
    'flip_vertical' => 'Flip Vertical',
    'rotate' => 'Rotate',
    'cube' => 'Cube Transition',
    'dissolve' => 'Dissolve'
];

// Available slide types
$slideTypes = [
    'beer_board' => [
        'name' => 'Beer Board',
        'icon' => 'fas fa-list',
        'description' => 'Display your current beer menu'
    ],
    'image' => [
        'name' => 'Image',
        'icon' => 'fas fa-image',
        'description' => 'Show promotional images or photos'
    ],
    'video' => [
        'name' => 'Video',
        'icon' => 'fas fa-video',
        'description' => 'Play promotional or brewery videos'
    ],
    'custom_html' => [
        'name' => 'Custom Content',
        'icon' => 'fas fa-code',
        'description' => 'Create custom HTML content'
    ],
    'events' => [
        'name' => 'Events',
        'icon' => 'fas fa-calendar',
        'description' => 'Display upcoming events'
    ],
    'social_feed' => [
        'name' => 'Social Feed',
        'icon' => 'fas fa-hashtag',
        'description' => 'Show social media posts'
    ],
    'weather' => [
        'name' => 'Weather',
        'icon' => 'fas fa-cloud-sun',
        'description' => 'Current weather information'
    ],
    'qr_code' => [
        'name' => 'QR Code',
        'icon' => 'fas fa-qrcode',
        'description' => 'Display QR codes for menus or apps'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .builder-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .slide-item {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: move;
            transition: all 0.3s ease;
            background: white;
        }
        .slide-item:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.15);
        }
        .slide-item.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .slide-preview {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        .slide-preview.beer-board { background: linear-gradient(135deg, #000 0%, #333 100%); color: white; }
        .slide-preview.image { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .slide-preview.video { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; }
        .slide-preview.custom { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; }

        .slide-controls { position: absolute; top: 5px; right: 5px; }
        .slide-controls .btn { padding: 0.25rem 0.5rem; margin-left: 0.25rem; }

        .slide-type-selector { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .slide-type-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .slide-type-card:hover { border-color: #007bff; transform: translateY(-2px); }
        .slide-type-card.selected { border-color: #28a745; background-color: #f8fff9; }

        .preview-panel {
            border: 3px solid #dee2e6;
            border-radius: 10px;
            height: 400px;
            background: #000;
            position: relative;
            overflow: hidden;
        }

        .timeline {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            min-height: 100px;
        }

        .timeline-track {
            height: 60px;
            background: #e9ecef;
            border-radius: 5px;
            position: relative;
            overflow-x: auto;
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        .timeline-slide {
            height: 40px;
            border-radius: 5px;
            margin-right: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
            cursor: pointer;
            flex-shrink: 0;
        }

        .settings-panel { max-height: 500px; overflow-y: auto; }
        .sortable-ghost { opacity: 0.5; }
        .sortable-chosen { transform: scale(1.05); }

        .duration-display {
            position: absolute;
            bottom: 5px;
            left: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .transition-preview {
            width: 50px;
            height: 30px;
            background: #007bff;
            border-radius: 3px;
            margin: 0 auto 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .transition-preview::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.5);
            animation: transition-demo 2s infinite;
        }

        @keyframes transition-demo {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: -100%; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="builder-container p-4">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-2">
                    <i class="fas fa-play-circle me-2 text-primary"></i>Digital Board Slideshow Builder
                </h1>
                <p class="text-muted">Create dynamic presentations with beer boards, images, videos, and custom content</p>
            </div>

            <!-- Slideshow Info -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Slideshow Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Slideshow Name</label>
                                        <input type="text" id="slideshowName" class="form-control" value="<?php echo $slideshow['name']; ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Description</label>
                                        <textarea id="slideshowDescription" class="form-control" rows="2"><?php echo $slideshow['description']; ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Global Transition</label>
                                        <select id="globalTransition" class="form-select">
                                            <?php foreach ($transitions as $key => $name): ?>
                                                <option value="<?php echo $key; ?>" <?php echo $slideshow['global_transition'] === $key ? 'selected' : ''; ?>>
                                                    <?php echo $name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Default Duration (seconds)</label>
                                        <input type="number" id="globalDuration" class="form-control" value="<?php echo $slideshow['global_duration']; ?>" min="1" max="300">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="autoAdvance" <?php echo $slideshow['auto_advance'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="autoAdvance">Auto Advance</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="loopSlideshow" <?php echo $slideshow['loop'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="loopSlideshow">Loop Slideshow</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="slideshowActive" <?php echo $slideshow['is_active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="slideshowActive">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clock me-2"></i>Slideshow Stats
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 text-primary" id="totalSlides"><?php echo count($slideshow['slides']); ?></div>
                                    <small class="text-muted">Total Slides</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 text-success" id="totalDuration">
                                        <?php echo array_sum(array_column($slideshow['slides'], 'duration')); ?>s
                                    </div>
                                    <small class="text-muted">Total Duration</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Slides Panel -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>Slides
                            </h5>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSlideModal">
                                <i class="fas fa-plus me-1"></i>Add Slide
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="slidesList" class="slides-container">
                                <?php foreach ($slideshow['slides'] as $slide): ?>
                                    <div class="slide-item" data-slide-id="<?php echo $slide['id']; ?>" data-order="<?php echo $slide['order']; ?>">
                                        <div class="slide-preview <?php echo str_replace('_', '-', $slide['type']); ?>">
                                            <div class="slide-controls">
                                                <button class="btn btn-sm btn-outline-light" onclick="editSlide('<?php echo $slide['id']; ?>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="deleteSlide('<?php echo $slide['id']; ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="duration-display"><?php echo $slide['duration']; ?>s</div>
                                            <div class="text-center">
                                                <i class="<?php echo $slideTypes[$slide['type']]['icon']; ?> fa-2x mb-2"></i>
                                                <div class="fw-bold"><?php echo $slideTypes[$slide['type']]['name']; ?></div>
                                            </div>
                                        </div>
                                        <div class="slide-info">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($slide['title']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo ucfirst(str_replace('_', ' ', $slide['transition'])); ?> •
                                                <?php echo $slide['duration']; ?>s
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview Panel -->
                <div class="col-lg-5">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>Preview
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="playSlideshow()">
                                    <i class="fas fa-play me-1"></i>Play
                                </button>
                                <button class="btn btn-outline-secondary" onclick="pauseSlideshow()">
                                    <i class="fas fa-pause me-1"></i>Pause
                                </button>
                                <button class="btn btn-outline-info" onclick="fullscreenPreview()">
                                    <i class="fas fa-expand me-1"></i>Fullscreen
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="preview-panel" id="previewPanel">
                                <div class="d-flex align-items-center justify-content-center h-100 text-white">
                                    <div class="text-center">
                                        <i class="fas fa-play-circle fa-4x mb-3"></i>
                                        <h5>Click Play to Preview Slideshow</h5>
                                        <p class="text-muted">Select a slide to preview individual content</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Timeline -->
                            <div class="timeline">
                                <h6 class="mb-2">
                                    <i class="fas fa-timeline me-2"></i>Timeline
                                </h6>
                                <div class="timeline-track" id="timelineTrack">
                                    <?php
                                    $colors = ['#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1', '#fd7e14'];
                                    foreach ($slideshow['slides'] as $index => $slide):
                                        $width = ($slide['duration'] / array_sum(array_column($slideshow['slides'], 'duration'))) * 100;
                                        $color = $colors[$index % count($colors)];
                                    ?>
                                        <div class="timeline-slide"
                                             style="width: <?php echo max($width, 10); ?>%; background-color: <?php echo $color; ?>;"
                                             data-slide-id="<?php echo $slide['id']; ?>"
                                             onclick="previewSlide('<?php echo $slide['id']; ?>')">
                                            <?php echo substr($slide['title'], 0, 8); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Panel -->
                <div class="col-lg-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Slide Settings
                            </h5>
                        </div>
                        <div class="card-body settings-panel" id="settingsPanel">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-mouse-pointer fa-2x mb-3"></i>
                                <p>Select a slide to edit its settings</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-success btn-sm" onclick="saveSlideshow()">
                                    <i class="fas fa-save me-1"></i>Save Slideshow
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="deploySlideshow()">
                                    <i class="fas fa-rocket me-1"></i>Deploy Live
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="exportSlideshow()">
                                    <i class="fas fa-download me-1"></i>Export
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="duplicateSlideshow()">
                                    <i class="fas fa-copy me-1"></i>Duplicate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="templates.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Templates
                </a>
                <a href="demo.php" class="btn btn-outline-primary">
                    <i class="fas fa-tv me-2"></i>View Demo Board
                </a>
            </div>
        </div>
    </div>

    <!-- Add Slide Modal -->
    <div class="modal fade" id="addSlideModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New Slide
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6 class="mb-3">Choose Slide Type:</h6>
                    <div class="slide-type-selector">
                        <?php foreach ($slideTypes as $type => $info): ?>
                            <div class="slide-type-card" data-type="<?php echo $type; ?>" onclick="selectSlideType('<?php echo $type; ?>')">
                                <i class="<?php echo $info['icon']; ?> fa-2x mb-3 text-primary"></i>
                                <h6><?php echo $info['name']; ?></h6>
                                <p class="text-muted small"><?php echo $info['description']; ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div id="slideTypeSettings" style="display: none;">
                        <hr>
                        <h6>Slide Settings:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Slide Title</label>
                                    <input type="text" id="newSlideTitle" class="form-control" placeholder="Enter slide title">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Duration (seconds)</label>
                                    <input type="number" id="newSlideDuration" class="form-control" value="10" min="1" max="300">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Transition</label>
                                    <select id="newSlideTransition" class="form-select">
                                        <?php foreach ($transitions as $key => $name): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Position</label>
                                    <select id="newSlidePosition" class="form-select">
                                        <option value="end">Add to End</option>
                                        <option value="beginning">Add to Beginning</option>
                                        <option value="after_current">After Current Slide</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Type-specific settings will be loaded here -->
                        <div id="typeSpecificSettings"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createSlide()" disabled id="createSlideBtn">
                        <i class="fas fa-plus me-1"></i>Create Slide
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        let slideshow = <?php echo json_encode($slideshow); ?>;
        let selectedSlideId = null;
        let selectedSlideType = null;
        let slideshowPlayer = null;
        let currentSlideIndex = 0;
        let isPlaying = false;

        // Initialize sortable slides
        document.addEventListener('DOMContentLoaded', function() {
            const slidesList = document.getElementById('slidesList');
            Sortable.create(slidesList, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                onEnd: function(evt) {
                    updateSlideOrder();
                    showToast('Slide order updated', 'success');
                }
            });

            console.log('Slideshow Builder initialized');
            updateStats();
        });

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            document.getElementById('toastContainer').appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }

        function selectSlideType(type) {
            selectedSlideType = type;

            // Update UI
            document.querySelectorAll('.slide-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');

            // Show settings
            document.getElementById('slideTypeSettings').style.display = 'block';
            document.getElementById('createSlideBtn').disabled = false;

            // Load type-specific settings
            loadTypeSpecificSettings(type);

            showToast(`Selected ${type.replace('_', ' ')} slide type`, 'info');
        }

        function loadTypeSpecificSettings(type) {
            const container = document.getElementById('typeSpecificSettings');
            let html = '';

            switch(type) {
                case 'beer_board':
                    html = `
                        <div class="mb-3">
                            <label class="form-label">Template</label>
                            <select id="beerBoardTemplate" class="form-select">
                                <option value="beersty-professional">Beersty Professional</option>
                                <option value="classic-dark">Classic Dark</option>
                                <option value="modern-light">Modern Light</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showHeader" checked>
                            <label class="form-check-label" for="showHeader">Show Header</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showTicker" checked>
                            <label class="form-check-label" for="showTicker">Show Ticker</label>
                        </div>
                    `;
                    break;
                case 'image':
                    html = `
                        <div class="mb-3">
                            <label class="form-label">Image File</label>
                            <input type="file" id="imageFile" class="form-control" accept="image/*">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overlay Text (optional)</label>
                            <input type="text" id="overlayText" class="form-control" placeholder="Add text overlay">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overlay Position</label>
                            <select id="overlayPosition" class="form-select">
                                <option value="top">Top</option>
                                <option value="center">Center</option>
                                <option value="bottom">Bottom</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'video':
                    html = `
                        <div class="mb-3">
                            <label class="form-label">Video File</label>
                            <input type="file" id="videoFile" class="form-control" accept="video/*">
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoplay" checked>
                            <label class="form-check-label" for="autoplay">Autoplay</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="muted" checked>
                            <label class="form-check-label" for="muted">Muted</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="loopVideo">
                            <label class="form-check-label" for="loopVideo">Loop Video</label>
                        </div>
                    `;
                    break;
                case 'custom_html':
                    html = `
                        <div class="mb-3">
                            <label class="form-label">Custom HTML Content</label>
                            <textarea id="customHtml" class="form-control" rows="5" placeholder="Enter your custom HTML content"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Background Color</label>
                                <input type="color" id="backgroundColor" class="form-control" value="#2c3e50">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Text Color</label>
                                <input type="color" id="textColor" class="form-control" value="#ffffff">
                            </div>
                        </div>
                    `;
                    break;
                default:
                    html = `<p class="text-muted">No additional settings for this slide type.</p>`;
            }

            container.innerHTML = html;
        }

        function createSlide() {
            if (!selectedSlideType) {
                showToast('Please select a slide type', 'error');
                return;
            }

            const title = document.getElementById('newSlideTitle').value;
            const duration = parseInt(document.getElementById('newSlideDuration').value);
            const transition = document.getElementById('newSlideTransition').value;

            if (!title) {
                showToast('Please enter a slide title', 'error');
                return;
            }

            // Create new slide object
            const newSlide = {
                id: 'slide-' + Date.now(),
                type: selectedSlideType,
                title: title,
                duration: duration,
                transition: transition,
                order: slideshow.slides.length + 1,
                settings: getTypeSpecificSettings(selectedSlideType)
            };

            // Add to slideshow
            slideshow.slides.push(newSlide);

            // Update UI
            addSlideToUI(newSlide);
            updateStats();
            updateTimeline();

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addSlideModal'));
            modal.hide();

            // Reset form
            resetAddSlideForm();

            showToast(`${title} slide created successfully!`, 'success');
        }

        function getTypeSpecificSettings(type) {
            const settings = {};

            switch(type) {
                case 'beer_board':
                    settings.template = document.getElementById('beerBoardTemplate')?.value || 'beersty-professional';
                    settings.show_header = document.getElementById('showHeader')?.checked || true;
                    settings.show_ticker = document.getElementById('showTicker')?.checked || true;
                    break;
                case 'image':
                    settings.overlay_text = document.getElementById('overlayText')?.value || '';
                    settings.overlay_position = document.getElementById('overlayPosition')?.value || 'bottom';
                    break;
                case 'video':
                    settings.autoplay = document.getElementById('autoplay')?.checked || true;
                    settings.muted = document.getElementById('muted')?.checked || true;
                    settings.loop_video = document.getElementById('loopVideo')?.checked || false;
                    break;
                case 'custom_html':
                    settings.custom_html = document.getElementById('customHtml')?.value || '';
                    settings.background_color = document.getElementById('backgroundColor')?.value || '#2c3e50';
                    settings.text_color = document.getElementById('textColor')?.value || '#ffffff';
                    break;
            }

            return settings;
        }

        function addSlideToUI(slide) {
            const slidesList = document.getElementById('slidesList');
            const slideTypes = <?php echo json_encode($slideTypes); ?>;

            const slideElement = document.createElement('div');
            slideElement.className = 'slide-item';
            slideElement.setAttribute('data-slide-id', slide.id);
            slideElement.setAttribute('data-order', slide.order);

            slideElement.innerHTML = `
                <div class="slide-preview ${slide.type.replace('_', '-')}">
                    <div class="slide-controls">
                        <button class="btn btn-sm btn-outline-light" onclick="editSlide('${slide.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSlide('${slide.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="duration-display">${slide.duration}s</div>
                    <div class="text-center">
                        <i class="${slideTypes[slide.type].icon} fa-2x mb-2"></i>
                        <div class="fw-bold">${slideTypes[slide.type].name}</div>
                    </div>
                </div>
                <div class="slide-info">
                    <h6 class="mb-1">${slide.title}</h6>
                    <small class="text-muted">
                        ${slide.transition.replace('_', ' ')} • ${slide.duration}s
                    </small>
                </div>
            `;

            slidesList.appendChild(slideElement);
        }

        function resetAddSlideForm() {
            selectedSlideType = null;
            document.getElementById('newSlideTitle').value = '';
            document.getElementById('newSlideDuration').value = '10';
            document.getElementById('slideTypeSettings').style.display = 'none';
            document.getElementById('createSlideBtn').disabled = true;
            document.querySelectorAll('.slide-type-card').forEach(card => {
                card.classList.remove('selected');
            });
        }

        function updateStats() {
            document.getElementById('totalSlides').textContent = slideshow.slides.length;
            const totalDuration = slideshow.slides.reduce((sum, slide) => sum + slide.duration, 0);
            document.getElementById('totalDuration').textContent = totalDuration + 's';
        }

        function updateTimeline() {
            const track = document.getElementById('timelineTrack');
            const totalDuration = slideshow.slides.reduce((sum, slide) => sum + slide.duration, 0);
            const colors = ['#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1', '#fd7e14'];

            track.innerHTML = '';

            slideshow.slides.forEach((slide, index) => {
                const width = (slide.duration / totalDuration) * 100;
                const color = colors[index % colors.length];

                const timelineSlide = document.createElement('div');
                timelineSlide.className = 'timeline-slide';
                timelineSlide.style.width = Math.max(width, 10) + '%';
                timelineSlide.style.backgroundColor = color;
                timelineSlide.setAttribute('data-slide-id', slide.id);
                timelineSlide.textContent = slide.title.substring(0, 8);
                timelineSlide.onclick = () => previewSlide(slide.id);

                track.appendChild(timelineSlide);
            });
        }

        function updateSlideOrder() {
            const slideElements = document.querySelectorAll('.slide-item');
            slideElements.forEach((element, index) => {
                const slideId = element.getAttribute('data-slide-id');
                const slide = slideshow.slides.find(s => s.id === slideId);
                if (slide) {
                    slide.order = index + 1;
                }
            });

            // Sort slides by order
            slideshow.slides.sort((a, b) => a.order - b.order);
            updateTimeline();
        }

        function editSlide(slideId) {
            selectedSlideId = slideId;
            const slide = slideshow.slides.find(s => s.id === slideId);
            if (slide) {
                showToast(`Editing ${slide.title}`, 'info');
                loadSlideSettings(slide);
            }
        }

        function deleteSlide(slideId) {
            if (confirm('Are you sure you want to delete this slide?')) {
                slideshow.slides = slideshow.slides.filter(s => s.id !== slideId);
                document.querySelector(`[data-slide-id="${slideId}"]`).remove();
                updateStats();
                updateTimeline();
                showToast('Slide deleted successfully', 'success');
            }
        }

        function loadSlideSettings(slide) {
            const settingsPanel = document.getElementById('settingsPanel');
            // Load slide-specific settings UI
            settingsPanel.innerHTML = `
                <h6>${slide.title}</h6>
                <p class="text-muted">${slide.type.replace('_', ' ')} slide</p>
                <div class="mb-3">
                    <label class="form-label">Duration</label>
                    <input type="number" class="form-control" value="${slide.duration}" onchange="updateSlideDuration('${slide.id}', this.value)">
                </div>
                <div class="mb-3">
                    <label class="form-label">Transition</label>
                    <select class="form-select" onchange="updateSlideTransition('${slide.id}', this.value)">
                        ${Object.entries(<?php echo json_encode($transitions); ?>).map(([key, name]) =>
                            `<option value="${key}" ${slide.transition === key ? 'selected' : ''}>${name}</option>`
                        ).join('')}
                    </select>
                </div>
            `;
        }

        function previewSlide(slideId) {
            const slide = slideshow.slides.find(s => s.id === slideId);
            if (slide) {
                const previewPanel = document.getElementById('previewPanel');

                switch(slide.type) {
                    case 'beer_board':
                        previewPanel.innerHTML = `
                            <iframe src="display-${slide.settings.template || 'beersty-professional'}.php"
                                    style="width: 100%; height: 100%; border: none;"></iframe>
                        `;
                        break;
                    case 'image':
                        previewPanel.innerHTML = `
                            <div class="d-flex align-items-center justify-content-center h-100 text-white">
                                <div class="text-center">
                                    <i class="fas fa-image fa-4x mb-3"></i>
                                    <h5>${slide.title}</h5>
                                    <p>Image Slide Preview</p>
                                </div>
                            </div>
                        `;
                        break;
                    case 'video':
                        previewPanel.innerHTML = `
                            <div class="d-flex align-items-center justify-content-center h-100 text-white">
                                <div class="text-center">
                                    <i class="fas fa-video fa-4x mb-3"></i>
                                    <h5>${slide.title}</h5>
                                    <p>Video Slide Preview</p>
                                </div>
                            </div>
                        `;
                        break;
                    default:
                        previewPanel.innerHTML = `
                            <div class="d-flex align-items-center justify-content-center h-100 text-white">
                                <div class="text-center">
                                    <i class="fas fa-file fa-4x mb-3"></i>
                                    <h5>${slide.title}</h5>
                                    <p>${slide.type.replace('_', ' ')} Preview</p>
                                </div>
                            </div>
                        `;
                }

                showToast(`Previewing ${slide.title}`, 'info');
            }
        }

        function playSlideshow() {
            isPlaying = true;
            currentSlideIndex = 0;
            showToast('Slideshow started', 'success');
            playNextSlide();
        }

        function pauseSlideshow() {
            isPlaying = false;
            if (slideshowPlayer) {
                clearTimeout(slideshowPlayer);
            }
            showToast('Slideshow paused', 'info');
        }

        function playNextSlide() {
            if (!isPlaying || currentSlideIndex >= slideshow.slides.length) {
                if (slideshow.loop && isPlaying) {
                    currentSlideIndex = 0;
                } else {
                    isPlaying = false;
                    showToast('Slideshow completed', 'success');
                    return;
                }
            }

            const slide = slideshow.slides[currentSlideIndex];
            previewSlide(slide.id);

            slideshowPlayer = setTimeout(() => {
                currentSlideIndex++;
                playNextSlide();
            }, slide.duration * 1000);
        }

        function fullscreenPreview() {
            const url = 'slideshow-player.php?slideshow_id=' + slideshow.id;
            window.open(url, '_blank', 'fullscreen=yes');
            showToast('Opening fullscreen slideshow', 'info');
        }

        function saveSlideshow() {
            showToast('Slideshow saved successfully!', 'success');
            console.log('Saving slideshow:', slideshow);
        }

        function deploySlideshow() {
            showToast('Slideshow deployed to live display!', 'success');
        }

        function exportSlideshow() {
            const dataStr = JSON.stringify(slideshow, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${slideshow.name.replace(/\s+/g, '_')}_slideshow.json`;
            link.click();
            showToast('Slideshow exported!', 'success');
        }

        function duplicateSlideshow() {
            showToast('Slideshow duplicated!', 'success');
        }
    </script>
</body>
</html>