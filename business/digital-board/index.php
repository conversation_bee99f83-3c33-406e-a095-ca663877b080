<?php
/**
 * Digital Beer Board - Business Admin Interface
 * BUSINESS USERS ONLY - Manage digital beer board displays
 */

require_once '../../config/config.php';
requireLogin();

// Check if user has business access
$user = getCurrentUser();
if ($user['role'] !== 'brewery' && $user['role'] !== 'admin' && $user['role'] !== 'business_owner') {
    header('Location: ../../index.php');
    exit;
}

$pageTitle = 'Digital Beer Board Management - ' . APP_NAME;
$additionalCSS = ['../../assets/css/business.css', '../../assets/css/digital-board.css'];
$additionalJS = ['../../assets/js/digital-board.js'];

// Get brewery information
$breweryId = $user['brewery_id'] ?? $_GET['brewery_id'] ?? null;

// If no brewery ID and not admin, redirect to setup
if (!$breweryId && $user['role'] !== 'admin') {
    header('Location: setup.php');
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get brewery details
    if ($user['role'] === 'admin' && isset($_GET['brewery_id'])) {
        $breweryQuery = "SELECT * FROM breweries WHERE id = ?";
        $stmt = $conn->prepare($breweryQuery);
        $stmt->execute([$_GET['brewery_id']]);
    } else {
        $breweryQuery = "SELECT * FROM breweries WHERE id = ?";
        $stmt = $conn->prepare($breweryQuery);
        $stmt->execute([$breweryId]);
    }
    $brewery = $stmt->fetch();
    
    if (!$brewery) {
        die("Brewery not found.");
    }
    
    // Get existing digital board configuration
    $boardQuery = "SELECT * FROM digital_boards WHERE brewery_id = ?";
    $stmt = $conn->prepare($boardQuery);
    $stmt->execute([$brewery['id']]);
    $digitalBoard = $stmt->fetch();
    
    // Get beer menu items for this brewery
    $menuQuery = "
        SELECT bm.*, bs.name as style_name 
        FROM beer_menu bm
        LEFT JOIN beer_styles bs ON bm.style_id = bs.id
        WHERE bm.brewery_id = ? AND bm.is_available = 1
        ORDER BY bm.tap_number ASC, bm.name ASC
    ";
    $stmt = $conn->prepare($menuQuery);
    $stmt->execute([$brewery['id']]);
    $beerMenu = $stmt->fetchAll();
    
    // Get board statistics
    $statsQuery = "
        SELECT 
            COUNT(*) as total_beers,
            COUNT(CASE WHEN tap_number IS NOT NULL THEN 1 END) as on_tap,
            AVG(abv) as avg_abv,
            AVG(ibu) as avg_ibu
        FROM beer_menu 
        WHERE brewery_id = ? AND is_available = 1
    ";
    $stmt = $conn->prepare($statsQuery);
    $stmt->execute([$brewery['id']]);
    $menuStats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Digital board error: " . $e->getMessage());
    die("Error loading digital board data.");
}

require_once '../../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tv me-2 text-primary"></i>Digital Beer Board
                    </h1>
                    <p class="text-muted mb-0">
                        Manage your digital beer menu display for <?php echo htmlspecialchars($brewery['name']); ?>
                    </p>
                </div>
                <div>
                    <?php if ($digitalBoard && $digitalBoard['is_active']): ?>
                        <a href="display.php?brewery_id=<?php echo $brewery['id']; ?>&board_id=<?php echo $digitalBoard['board_id']; ?>" 
                           class="btn btn-success me-2" target="_blank">
                            <i class="fas fa-eye me-2"></i>View Display
                        </a>
                    <?php endif; ?>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                        <i class="fas fa-cog me-2"></i>Board Settings
                    </button>
                </div>
            </div>
            
            <!-- Board Status -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                Digital Board Status
                                <?php if ($digitalBoard && $digitalBoard['is_active']): ?>
                                    <span class="badge bg-success ms-2">ACTIVE</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary ms-2">INACTIVE</span>
                                <?php endif; ?>
                            </h5>
                            <p class="text-muted mb-0">
                                <?php if ($digitalBoard): ?>
                                    Board ID: <?php echo htmlspecialchars($digitalBoard['board_id']); ?>
                                    <?php if ($digitalBoard['is_active']): ?>
                                        • Last updated: <?php echo date('M j, Y g:i A', strtotime($digitalBoard['updated_at'])); ?>
                                    <?php endif; ?>
                                <?php else: ?>
                                    No digital board configured yet.
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if ($digitalBoard): ?>
                                <button class="btn btn-<?php echo $digitalBoard['is_active'] ? 'warning' : 'success'; ?>" 
                                        onclick="toggleBoard('<?php echo $digitalBoard['id']; ?>', <?php echo $digitalBoard['is_active'] ? 'false' : 'true'; ?>)">
                                    <i class="fas fa-power-off me-2"></i>
                                    <?php echo $digitalBoard['is_active'] ? 'Disable' : 'Enable'; ?> Board
                                </button>
                            <?php else: ?>
                                <button class="btn btn-primary" onclick="createBoard()">
                                    <i class="fas fa-plus me-2"></i>Create Digital Board
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Menu Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-beer text-primary"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $menuStats['total_beers']; ?></div>
                            <div class="stat-label">Total Beers</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-faucet text-success"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $menuStats['on_tap']; ?></div>
                            <div class="stat-label">On Tap</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo number_format($menuStats['avg_abv'], 1); ?>%</div>
                            <div class="stat-label">Avg ABV</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-bitter text-info"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo round($menuStats['avg_ibu']); ?></div>
                            <div class="stat-label">Avg IBU</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Beer Menu Management -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Beer Menu Items
                    </h5>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshDisplay()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh Display
                        </button>
                        <a href="../menu/manage.php?brewery_id=<?php echo $brewery['id']; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Manage Menu
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($beerMenu)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tap #</th>
                                        <th>Beer Name</th>
                                        <th>Style</th>
                                        <th>ABV</th>
                                        <th>IBU</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($beerMenu as $beer): ?>
                                        <tr>
                                            <td>
                                                <?php if ($beer['tap_number']): ?>
                                                    <span class="badge bg-primary"><?php echo $beer['tap_number']; ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($beer['name']); ?></strong>
                                                <?php if ($beer['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($beer['description'], 0, 50)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($beer['style_name'] ?? 'Unknown'); ?></td>
                                            <td><?php echo $beer['abv'] ? $beer['abv'] . '%' : '-'; ?></td>
                                            <td><?php echo $beer['ibu'] ?? '-'; ?></td>
                                            <td>
                                                <?php if ($beer['price']): ?>
                                                    $<?php echo number_format($beer['price'], 2); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $beer['is_available'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $beer['is_available'] ? 'Available' : 'Unavailable'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" 
                                                            onclick="editBeer('<?php echo $beer['id']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-<?php echo $beer['is_available'] ? 'warning' : 'success'; ?>" 
                                                            onclick="toggleAvailability('<?php echo $beer['id']; ?>', <?php echo $beer['is_available'] ? 'false' : 'true'; ?>)">
                                                        <i class="fas fa-<?php echo $beer['is_available'] ? 'eye-slash' : 'eye'; ?>"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-beer fa-3x text-muted mb-3"></i>
                            <h4>No Beer Menu Items</h4>
                            <p class="text-muted">Add beer items to your menu to display on the digital board.</p>
                            <a href="../menu/manage.php?brewery_id=<?php echo $brewery['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Beer Items
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($digitalBoard && $digitalBoard['is_active']): ?>
                            <a href="display.php?brewery_id=<?php echo $brewery['id']; ?>&board_id=<?php echo $digitalBoard['board_id']; ?>" 
                               class="btn btn-success" target="_blank">
                                <i class="fas fa-tv me-2"></i>View Live Display
                            </a>
                            <button class="btn btn-outline-primary" onclick="generateQRCode()">
                                <i class="fas fa-qrcode me-2"></i>Generate QR Code
                            </button>
                        <?php endif; ?>
                        <a href="../menu/manage.php?brewery_id=<?php echo $brewery['id']; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Manage Menu
                        </a>
                        <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#previewModal">
                            <i class="fas fa-eye me-2"></i>Preview Display
                        </button>
                        <button class="btn btn-outline-warning" onclick="exportMenu()">
                            <i class="fas fa-download me-2"></i>Export Menu
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Display Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Display Settings
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($digitalBoard): ?>
                        <?php 
                        $settings = json_decode($digitalBoard['settings'], true);
                        ?>
                        <div class="setting-item mb-3">
                            <label class="form-label small">Refresh Interval</label>
                            <div class="text-muted"><?php echo $settings['refresh_interval'] ?? 300; ?> seconds</div>
                        </div>
                        <div class="setting-item mb-3">
                            <label class="form-label small">Theme</label>
                            <div class="text-muted"><?php echo ucfirst($settings['theme'] ?? 'dark'); ?></div>
                        </div>
                        <div class="setting-item mb-3">
                            <label class="form-label small">Layout</label>
                            <div class="text-muted"><?php echo ucfirst($settings['layout'] ?? 'grid'); ?></div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm w-100" data-bs-toggle="modal" data-bs-target="#settingsModal">
                            <i class="fas fa-edit me-1"></i>Edit Settings
                        </button>
                    <?php else: ?>
                        <p class="text-muted small">Configure your digital board to customize display settings.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Help & Support -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Help & Support
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-item mb-3">
                        <h6 class="small">📺 Display Setup</h6>
                        <p class="small text-muted">Connect a TV or monitor to display your beer menu</p>
                    </div>
                    <div class="help-item mb-3">
                        <h6 class="small">🔄 Auto Refresh</h6>
                        <p class="small text-muted">Display updates automatically when you change menu items</p>
                    </div>
                    <div class="help-item mb-3">
                        <h6 class="small">📱 QR Code</h6>
                        <p class="small text-muted">Generate QR codes for easy access to your digital menu</p>
                    </div>
                    <a href="help.php" class="btn btn-outline-info btn-sm w-100">
                        <i class="fas fa-book me-1"></i>View Full Guide
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../includes/footer.php'; ?>
