<?php
/**
 * Digital Beer Board - Working Demo
 * Complete demo without database dependencies
 */

$pageTitle = 'Digital Beer Board Demo - Beersty';

// Demo data
$brewery = [
    'id' => 'demo-brewery-1',
    'name' => 'Demo Craft Brewery',
    'city' => 'Demo City',
    'state' => 'CA',
    'description' => 'A demonstration brewery showcasing the digital beer board system'
];

$digitalBoard = [
    'id' => 'board-1',
    'board_id' => 'demo-board-12345',
    'is_active' => true,
    'settings' => [
        'theme' => 'dark',
        'layout' => 'grid',
        'refresh_interval' => 300,
        'show_prices' => true,
        'show_descriptions' => true,
        'ticker_message' => 'Welcome to Demo Craft Brewery! Try our award-winning beers fresh from the tap!'
    ]
];

$beerMenu = [
    ['id' => 1, 'name' => 'Hoppy IPA', 'style_name' => 'IPA', 'abv' => 6.8, 'ibu' => 68, 'price' => 8.50, 'tap_number' => 1, 'is_available' => 1],
    ['id' => 2, 'name' => 'Golden Wheat', 'style_name' => 'Wheat Beer', 'abv' => 4.5, 'ibu' => 12, 'price' => 6.00, 'tap_number' => 2, 'is_available' => 1],
    ['id' => 3, 'name' => 'Midnight Stout', 'style_name' => 'Stout', 'abv' => 7.2, 'ibu' => 35, 'price' => 9.00, 'tap_number' => 3, 'is_available' => 1],
    ['id' => 4, 'name' => 'Crisp Lager', 'style_name' => 'Lager', 'abv' => 4.8, 'ibu' => 18, 'price' => 5.50, 'tap_number' => 4, 'is_available' => 1],
    ['id' => 5, 'name' => 'Robust Porter', 'style_name' => 'Porter', 'abv' => 6.2, 'ibu' => 28, 'price' => 7.50, 'tap_number' => 5, 'is_available' => 1],
    ['id' => 6, 'name' => 'Czech Pilsner', 'style_name' => 'Pilsner', 'abv' => 5.2, 'ibu' => 35, 'price' => 7.00, 'tap_number' => 6, 'is_available' => 1],
    ['id' => 7, 'name' => 'Nutty Brown', 'style_name' => 'Brown Ale', 'abv' => 5.8, 'ibu' => 22, 'price' => 6.50, 'tap_number' => 7, 'is_available' => 1],
    ['id' => 8, 'name' => 'American Pale', 'style_name' => 'Pale Ale', 'abv' => 5.5, 'ibu' => 42, 'price' => 6.75, 'tap_number' => 8, 'is_available' => 1],
    ['id' => 9, 'name' => 'Seasonal Special', 'style_name' => 'Brown Ale', 'abv' => 6.0, 'ibu' => 25, 'price' => 8.00, 'tap_number' => null, 'is_available' => 1],
    ['id' => 10, 'name' => 'House Blend', 'style_name' => 'Porter', 'abv' => 6.5, 'ibu' => 45, 'price' => 7.75, 'tap_number' => null, 'is_available' => 0]
];

$menuStats = [
    'total_beers' => count($beerMenu),
    'on_tap' => count(array_filter($beerMenu, fn($beer) => $beer['tap_number'] !== null)),
    'avg_abv' => array_sum(array_column($beerMenu, 'abv')) / count($beerMenu),
    'avg_ibu' => array_sum(array_column($beerMenu, 'ibu')) / count($beerMenu)
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .main-container { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 1.5rem; text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; }
        .stat-label { font-size: 0.9rem; opacity: 0.9; }
        .beer-item-row { transition: all 0.3s ease; }
        .beer-item-row:hover { background-color: #f8f9fa; }
        .demo-badge { background: linear-gradient(45deg, #ff6b6b, #feca57); color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem; }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="main-container p-4">
            <!-- Demo Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-2">
                    <i class="fas fa-tv me-2 text-primary"></i>Digital Beer Board Demo
                </h1>
                <span class="demo-badge">WORKING DEMO</span>
                <p class="text-muted mt-2">Complete digital beer board system demonstration</p>
            </div>
            
            <!-- Board Status -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                Digital Board Status
                                <span class="badge bg-success ms-2">ACTIVE</span>
                            </h5>
                            <p class="text-muted mb-0">
                                Board ID: <?php echo $digitalBoard['board_id']; ?> • 
                                Last updated: <?php echo date('M j, Y g:i A'); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="display-simple.php" class="btn btn-success me-2" target="_blank">
                                <i class="fas fa-eye me-2"></i>View Live Display
                            </a>
                            <button class="btn btn-warning" onclick="toggleDemo()">
                                <i class="fas fa-power-off me-2"></i>Demo Toggle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Menu Statistics -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $menuStats['total_beers']; ?></div>
                        <div class="stat-label">Total Beers</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $menuStats['on_tap']; ?></div>
                        <div class="stat-label">On Tap</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($menuStats['avg_abv'], 1); ?>%</div>
                        <div class="stat-label">Avg ABV</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo round($menuStats['avg_ibu']); ?></div>
                        <div class="stat-label">Avg IBU</div>
                    </div>
                </div>
            </div>
            
            <!-- Beer Menu Management -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Demo Beer Menu
                    </h5>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshDemo()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh Display
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="showSettings()">
                            <i class="fas fa-cog me-1"></i>Settings
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tap #</th>
                                    <th>Beer Name</th>
                                    <th>Style</th>
                                    <th>ABV</th>
                                    <th>IBU</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($beerMenu as $beer): ?>
                                    <tr class="beer-item-row" data-beer-id="<?php echo $beer['id']; ?>">
                                        <td>
                                            <?php if ($beer['tap_number']): ?>
                                                <span class="badge bg-primary"><?php echo $beer['tap_number']; ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($beer['name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($beer['style_name']); ?></td>
                                        <td><?php echo $beer['abv']; ?>%</td>
                                        <td><?php echo $beer['ibu']; ?></td>
                                        <td>$<?php echo number_format($beer['price'], 2); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $beer['is_available'] ? 'success' : 'secondary'; ?> status-badge">
                                                <?php echo $beer['is_available'] ? 'Available' : 'Unavailable'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editBeerDemo(<?php echo $beer['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-<?php echo $beer['is_available'] ? 'warning' : 'success'; ?>" 
                                                        onclick="toggleBeerDemo(<?php echo $beer['id']; ?>, <?php echo $beer['is_available'] ? 'false' : 'true'; ?>)">
                                                    <i class="fas fa-<?php echo $beer['is_available'] ? 'eye-slash' : 'eye'; ?>"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Demo Actions -->
            <div class="mt-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-rocket me-2"></i>Demo Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="display-simple.php" class="btn btn-success" target="_blank">
                                        <i class="fas fa-tv me-2"></i>View Live Display
                                    </a>
                                    <button class="btn btn-info" onclick="generateQRDemo()">
                                        <i class="fas fa-qrcode me-2"></i>Generate QR Code
                                    </button>
                                    <button class="btn btn-warning" onclick="exportMenuDemo()">
                                        <i class="fas fa-download me-2"></i>Export Menu (Demo)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Demo Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="small mb-2">
                                    <strong>Brewery:</strong> <?php echo htmlspecialchars($brewery['name']); ?>
                                </p>
                                <p class="small mb-2">
                                    <strong>Location:</strong> <?php echo htmlspecialchars($brewery['city'] . ', ' . $brewery['state']); ?>
                                </p>
                                <p class="small mb-2">
                                    <strong>Board Theme:</strong> <?php echo ucfirst($digitalBoard['settings']['theme']); ?>
                                </p>
                                <p class="small mb-0">
                                    <strong>Layout:</strong> <?php echo ucfirst($digitalBoard['settings']['layout']); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="../../index.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-home me-2"></i>Back to Homepage
                </a>
                <a href="../../../setup-digital-board-demo.php" class="btn btn-primary">
                    <i class="fas fa-database me-2"></i>Setup Full Database Demo
                </a>
            </div>
        </div>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }
        
        function toggleDemo() {
            showToast('Demo board toggled successfully!', 'success');
        }
        
        function refreshDemo() {
            showToast('Display refreshed!', 'info');
        }
        
        function toggleBeerDemo(beerId, isAvailable) {
            const row = document.querySelector(`[data-beer-id="${beerId}"]`);
            const statusBadge = row.querySelector('.status-badge');
            const toggleBtn = row.querySelector('.btn-outline-warning, .btn-outline-success');
            
            if (isAvailable === 'true') {
                statusBadge.className = 'badge bg-success status-badge';
                statusBadge.textContent = 'Available';
                toggleBtn.className = 'btn btn-outline-warning btn-sm';
                toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
                showToast('Beer enabled successfully!', 'success');
            } else {
                statusBadge.className = 'badge bg-secondary status-badge';
                statusBadge.textContent = 'Unavailable';
                toggleBtn.className = 'btn btn-outline-success btn-sm';
                toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
                showToast('Beer disabled successfully!', 'success');
            }
            
            // Update onclick
            toggleBtn.setAttribute('onclick', `toggleBeerDemo(${beerId}, '${isAvailable === 'true' ? 'false' : 'true'}')`);
        }
        
        function editBeerDemo(beerId) {
            showToast('Edit beer functionality (demo mode)', 'info');
        }
        
        function showSettings() {
            showToast('Settings panel (demo mode)', 'info');
        }
        
        function generateQRDemo() {
            const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' + 
                         encodeURIComponent(window.location.origin + '/business/digital-board/display-simple.php');
            window.open(qrUrl, '_blank');
            showToast('QR code generated!', 'success');
        }
        
        function exportMenuDemo() {
            showToast('Menu export functionality (demo mode)', 'info');
        }
        
        console.log('Digital Beer Board Demo loaded successfully!');
    </script>
</body>
</html>
