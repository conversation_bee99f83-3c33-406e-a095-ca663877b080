<?php
/**
 * Digital Board Media Manager
 * Phase 4 12.0 - Media Management System
 * 
 * Comprehensive media management interface for digital board content
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/login.php');
    exit;
}

// Get brewery information
$breweryId = $user['brewery_id'];
if (!$breweryId && $user['role'] !== 'admin') {
    die('No brewery associated with your account.');
}

// Get brewery details
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    
    if ($user['role'] === 'admin') {
        // Admin can select brewery
        $breweryId = $_GET['brewery_id'] ?? null;
        if ($breweryId) {
            $stmt = $pdo->prepare("SELECT * FROM breweries WHERE id = ?");
            $stmt->execute([$breweryId]);
            $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $brewery = null;
        }
        
        // Get all breweries for admin dropdown
        $stmt = $pdo->query("SELECT id, name FROM breweries ORDER BY name");
        $breweries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $stmt = $pdo->prepare("SELECT * FROM breweries WHERE id = ?");
        $stmt->execute([$breweryId]);
        $brewery = $stmt->fetch(PDO::FETCH_ASSOC);
        $breweries = [$brewery];
    }
    
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$pageTitle = 'Media Manager - Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/admin.css" rel="stylesheet">
    <link href="../../assets/css/media-manager.css" rel="stylesheet">
    
    <style>
        body { background: #1a1a1a; color: #f5f5dc; font-family: 'Inter', sans-serif; }
        .navbar { background: #000000 !important; }
        .card { background: #2c1810; border: 1px solid #d69a6b; }
        .card-header { background: #6f4c3e; color: #f5f5dc; }
        .btn-primary { background: #ffc107; border-color: #ffc107; color: #1a1a1a; }
        .btn-primary:hover { background: #d69a6b; border-color: #d69a6b; }
        .btn-secondary { background: #6c757d; border-color: #6c757d; }
        .btn-danger { background: #dc3545; border-color: #dc3545; }
        .form-control, .form-select { background: #3a3a3a; border-color: #d69a6b; color: #f5f5dc; }
        .form-control:focus, .form-select:focus { background: #3a3a3a; border-color: #ffc107; color: #f5f5dc; box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25); }
        .table-dark { background: #2c1810; }
        .table-dark td, .table-dark th { border-color: #d69a6b; }
        .nav-pills .nav-link.active { background: #ffc107; color: #1a1a1a; }
        .nav-pills .nav-link { color: #f5f5dc; }
        .nav-pills .nav-link:hover { background: #d69a6b; color: #1a1a1a; }
        .alert-info { background: #2d4d5a; border-color: #2196f3; color: #b3d9ff; }
        .alert-warning { background: #5a4d2d; border-color: #ff9800; color: #ffe0b3; }
        .alert-success { background: #2d5a2d; border-color: #4caf50; color: #90ee90; }
        .alert-danger { background: #5a2d2d; border-color: #f44336; color: #ffcccb; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <img src="../../assets/images/beersty-logo.png" alt="Beersty" height="40">
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?= htmlspecialchars($user['name'] ?? $user['email']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../../auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="fas fa-photo-video"></i>
                            Media Manager
                        </h1>
                        <p class="text-muted mb-0">Manage images, videos, and media content for digital boards</p>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="openUploadModal()">
                            <i class="fas fa-upload"></i> Upload Media
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                <!-- Brewery Selection (Admin Only) -->
                <?php if ($user['role'] === 'admin'): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <label for="brewerySelect" class="form-label">Select Brewery</label>
                                <select class="form-select" id="brewerySelect" onchange="changeBrewery(this.value)">
                                    <option value="">-- Select a Brewery --</option>
                                    <?php foreach ($breweries as $b): ?>
                                        <option value="<?= $b['id'] ?>" <?= $breweryId === $b['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($b['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <?php if ($brewery): ?>
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        Managing media for: <strong><?= htmlspecialchars($brewery['name']) ?></strong>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!$brewery): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Please select a brewery to manage media content.
                    </div>
                <?php else: ?>

                <!-- Media Management Interface -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-pills card-header-pills" id="mediaTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="library-tab" data-bs-toggle="pill" data-bs-target="#library" type="button">
                                    <i class="fas fa-images"></i> Media Library
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="upload-tab" data-bs-toggle="pill" data-bs-target="#upload" type="button">
                                    <i class="fas fa-upload"></i> Upload Files
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="organize-tab" data-bs-toggle="pill" data-bs-target="#organize" type="button">
                                    <i class="fas fa-folder-open"></i> Organize
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="mediaTabsContent">
                            
                            <!-- Media Library Tab -->
                            <div class="tab-pane fade show active" id="library" role="tabpanel">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="mediaSearch" placeholder="Search media files...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="mediaTypeFilter">
                                            <option value="">All Types</option>
                                            <option value="image">Images</option>
                                            <option value="video">Videos</option>
                                            <option value="audio">Audio</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="btn-group w-100" role="group">
                                            <button type="button" class="btn btn-outline-secondary active" id="gridViewBtn" onclick="setViewMode('grid')">
                                                <i class="fas fa-th"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="listViewBtn" onclick="setViewMode('list')">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Media Grid/List Container -->
                                <div id="mediaContainer" class="media-grid">
                                    <div class="text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Loading media files...</p>
                                    </div>
                                </div>
                                
                                <!-- Pagination -->
                                <nav aria-label="Media pagination" class="mt-4">
                                    <ul class="pagination justify-content-center" id="mediaPagination">
                                        <!-- Pagination will be populated by JavaScript -->
                                    </ul>
                                </nav>
                            </div>
                            
                            <!-- Upload Tab -->
                            <div class="tab-pane fade" id="upload" role="tabpanel">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-drop-zone" id="dropZone">
                                        <div class="text-center py-5">
                                            <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                                            <h4>Drag & Drop Files Here</h4>
                                            <p class="text-muted">or click to browse files</p>
                                            <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                                <i class="fas fa-folder-open"></i> Browse Files
                                            </button>
                                            <input type="file" id="fileInput" multiple accept="image/*,video/*,audio/*" style="display: none;">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Upload Progress -->
                                <div id="uploadProgress" class="mt-4" style="display: none;">
                                    <h5>Upload Progress</h5>
                                    <div id="uploadList">
                                        <!-- Upload items will be added here -->
                                    </div>
                                </div>
                                
                                <!-- Upload Guidelines -->
                                <div class="alert alert-info mt-4">
                                    <h6><i class="fas fa-info-circle"></i> Upload Guidelines</h6>
                                    <ul class="mb-0">
                                        <li><strong>Supported formats:</strong> JPEG, PNG, GIF, WebP, MP4, WebM, OGG, MP3, WAV</li>
                                        <li><strong>Maximum file size:</strong> 50MB per file</li>
                                        <li><strong>Recommended image size:</strong> 1920x1080 for best display quality</li>
                                        <li><strong>Video recommendations:</strong> MP4 format, H.264 codec for best compatibility</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Organize Tab -->
                            <div class="tab-pane fade" id="organize" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Storage Statistics</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="storageStats">
                                                    <div class="text-center">
                                                        <div class="spinner-border spinner-border-sm" role="status"></div>
                                                        <span class="ms-2">Loading statistics...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-tools"></i> Bulk Actions</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-warning" onclick="regenerateThumbnails()">
                                                        <i class="fas fa-sync-alt"></i> Regenerate Thumbnails
                                                    </button>
                                                    <button type="button" class="btn btn-info" onclick="optimizeStorage()">
                                                        <i class="fas fa-compress"></i> Optimize Storage
                                                    </button>
                                                    <button type="button" class="btn btn-danger" onclick="cleanupUnused()">
                                                        <i class="fas fa-trash"></i> Cleanup Unused Files
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Hidden data for JavaScript -->
    <script>
        window.mediaManagerConfig = {
            breweryId: <?= json_encode($breweryId) ?>,
            apiBaseUrl: '../../api/digital-board/',
            currentUser: <?= json_encode([
                'id' => $user['id'],
                'role' => $user['role'],
                'brewery_id' => $user['brewery_id']
            ]) ?>
        };
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../../assets/js/media-manager.js"></script>
</body>
</html>
