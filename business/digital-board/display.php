<?php
/**
 * Digital Beer Board - Kiosk Display
 * Full-screen beer menu display for TVs and monitors
 */

require_once '../../config/config.php';

// Get parameters
$breweryId = $_GET['brewery_id'] ?? null;
$boardId = $_GET['board_id'] ?? null;

if (!$breweryId || !$boardId) {
    die("Invalid display parameters.");
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Verify board exists and is active
    $boardQuery = "
        SELECT db.*, b.name as brewery_name, b.city, b.state, b.logo_url
        FROM digital_boards db
        JOIN breweries b ON db.brewery_id = b.id
        WHERE db.brewery_id = ? AND db.board_id = ? AND db.is_active = 1
    ";
    $stmt = $conn->prepare($boardQuery);
    $stmt->execute([$breweryId, $boardId]);
    $board = $stmt->fetch();
    
    if (!$board) {
        die("Digital board not found or inactive.");
    }
    
    // Get board settings
    $settings = json_decode($board['settings'], true) ?: [];
    $theme = $settings['theme'] ?? 'dark';
    $layout = $settings['layout'] ?? 'grid';
    $refreshInterval = ($settings['refresh_interval'] ?? 300) * 1000; // Convert to milliseconds
    $showPrices = $settings['show_prices'] ?? true;
    $showDescriptions = $settings['show_descriptions'] ?? true;
    $tickerMessage = $settings['ticker_message'] ?? '';
    
    // Get beer menu items
    $menuQuery = "
        SELECT bm.*, bs.name as style_name 
        FROM beer_menu bm
        LEFT JOIN beer_styles bs ON bm.style_id = bs.id
        WHERE bm.brewery_id = ? AND bm.is_available = 1
        ORDER BY bm.tap_number ASC, bm.name ASC
    ";
    $stmt = $conn->prepare($menuQuery);
    $stmt->execute([$breweryId]);
    $beerMenu = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Digital board display error: " . $e->getMessage());
    die("Error loading digital board.");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($board['brewery_name']); ?> - Digital Beer Board</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            <?php if ($theme === 'dark'): ?>
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                color: #ffffff;
            <?php else: ?>
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                color: #333333;
            <?php endif; ?>
        }
        
        .digital-board {
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .board-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 3px solid #ffc107;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .brewery-logo {
            max-height: 80px;
            margin-bottom: 1rem;
        }
        
        .brewery-name {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .brewery-location {
            font-size: 1.5rem;
            opacity: 0.8;
        }
        
        .beer-grid {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }
        
        .beer-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .beer-item:hover {
            transform: translateY(-5px);
        }
        
        .tap-number {
            position: absolute;
            top: -10px;
            left: 20px;
            background: #ffc107;
            color: #000;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .beer-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .beer-style {
            color: #ffc107;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .beer-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .beer-specs {
            display: flex;
            gap: 2rem;
        }
        
        .spec-item {
            text-align: center;
        }
        
        .spec-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffc107;
        }
        
        .spec-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .beer-price {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .beer-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .ticker {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #ffc107;
            color: #000;
            padding: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
            text-align: center;
            animation: ticker-scroll 30s linear infinite;
        }
        
        @keyframes ticker-scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .last-updated {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .grid-layout {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }
        
        .list-layout .beer-item {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        .list-layout .beer-content {
            flex: 1;
        }
        
        .no-beers {
            text-align: center;
            padding: 4rem;
            opacity: 0.7;
        }
        
        .no-beers i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .brewery-name { font-size: 2rem; }
            .brewery-location { font-size: 1.2rem; }
            .beer-name { font-size: 1.4rem; }
            .beer-specs { gap: 1rem; }
            .grid-layout { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="digital-board">
        <!-- Header -->
        <div class="board-header">
            <?php if ($board['logo_url']): ?>
                <img src="<?php echo htmlspecialchars($board['logo_url']); ?>" 
                     alt="<?php echo htmlspecialchars($board['brewery_name']); ?>" 
                     class="brewery-logo">
            <?php endif; ?>
            <div class="brewery-name"><?php echo htmlspecialchars($board['brewery_name']); ?></div>
            <div class="brewery-location">
                <?php echo htmlspecialchars($board['city'] . ', ' . $board['state']); ?>
            </div>
        </div>
        
        <!-- Beer Menu -->
        <div class="beer-grid">
            <?php if (!empty($beerMenu)): ?>
                <div class="<?php echo $layout === 'list' ? 'list-layout' : 'grid-layout'; ?>">
                    <?php foreach ($beerMenu as $beer): ?>
                        <div class="beer-item position-relative">
                            <?php if ($beer['tap_number']): ?>
                                <div class="tap-number"><?php echo $beer['tap_number']; ?></div>
                            <?php endif; ?>
                            
                            <div class="beer-content">
                                <div class="beer-name"><?php echo htmlspecialchars($beer['name']); ?></div>
                                
                                <?php if ($beer['style_name']): ?>
                                    <div class="beer-style"><?php echo htmlspecialchars($beer['style_name']); ?></div>
                                <?php endif; ?>
                                
                                <div class="beer-details">
                                    <div class="beer-specs">
                                        <?php if ($beer['abv']): ?>
                                            <div class="spec-item">
                                                <div class="spec-value"><?php echo $beer['abv']; ?>%</div>
                                                <div class="spec-label">ABV</div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($beer['ibu']): ?>
                                            <div class="spec-item">
                                                <div class="spec-value"><?php echo $beer['ibu']; ?></div>
                                                <div class="spec-label">IBU</div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($beer['srm']): ?>
                                            <div class="spec-item">
                                                <div class="spec-value"><?php echo $beer['srm']; ?></div>
                                                <div class="spec-label">SRM</div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($showPrices && $beer['price']): ?>
                                        <div class="beer-price">$<?php echo number_format($beer['price'], 2); ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($showDescriptions && $beer['description']): ?>
                                    <div class="beer-description">
                                        <?php echo htmlspecialchars($beer['description']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-beers">
                    <i class="fas fa-beer"></i>
                    <h2>No Beers Currently Available</h2>
                    <p>Check back soon for our latest beer offerings!</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Ticker Message -->
        <?php if ($tickerMessage): ?>
            <div class="ticker">
                <?php echo htmlspecialchars($tickerMessage); ?>
            </div>
        <?php endif; ?>
        
        <!-- Last Updated -->
        <div class="last-updated">
            <i class="fas fa-clock me-2"></i>
            Updated: <span id="lastUpdated"><?php echo date('g:i A'); ?></span>
        </div>
    </div>
    
    <script>
        // Auto-refresh functionality
        const refreshInterval = <?php echo $refreshInterval; ?>;
        
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('lastUpdated').textContent = now.toLocaleTimeString([], {
                hour: 'numeric',
                minute: '2-digit'
            });
        }
        
        function refreshDisplay() {
            // Update timestamp
            updateTimestamp();
            
            // Reload page to get fresh data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        // Set up auto-refresh
        if (refreshInterval > 0) {
            setInterval(refreshDisplay, refreshInterval);
        }
        
        // Update timestamp every minute
        setInterval(updateTimestamp, 60000);
        
        // Prevent context menu and selection
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('selectstart', e => e.preventDefault());
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // F5 or Ctrl+R to refresh
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshDisplay();
            }
            
            // Escape to exit fullscreen
            if (e.key === 'Escape') {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
            }
            
            // F11 to toggle fullscreen
            if (e.key === 'F11') {
                e.preventDefault();
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
        });
        
        // Auto-enter fullscreen on load (if supported)
        window.addEventListener('load', function() {
            // Small delay to ensure page is fully loaded
            setTimeout(() => {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.log('Fullscreen not available:', err);
                    });
                }
            }, 1000);
        });
        
        console.log('Digital Beer Board loaded for <?php echo htmlspecialchars($board['brewery_name']); ?>');
        console.log('Refresh interval: <?php echo $refreshInterval / 1000; ?> seconds');
        console.log('Theme: <?php echo $theme; ?>');
        console.log('Layout: <?php echo $layout; ?>');
    </script>
</body>
</html>
