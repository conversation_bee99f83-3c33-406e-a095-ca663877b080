<?php
/**
 * Media Library Browser Component
 * Phase 4 12.0 - Media Management System
 * 
 * Standalone media library browser for use in modals and embedded contexts
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Get parameters
$breweryId = $_GET['brewery_id'] ?? $user['brewery_id'];
$mode = $_GET['mode'] ?? 'select'; // 'select' or 'browse'
$allowedTypes = $_GET['types'] ?? 'image,video,audio'; // comma-separated
$multiple = $_GET['multiple'] ?? 'false';

// Validate brewery access
if ($user['role'] !== 'admin' && $breweryId !== $user['brewery_id']) {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Library Browser</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/media-manager.css" rel="stylesheet">
    
    <style>
        body { 
            background: #1a1a1a; 
            color: #f5f5dc; 
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .media-browser {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .media-item.selectable {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .media-item.selectable:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        
        .media-item.selected {
            border: 2px solid #ffc107;
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
        }
        
        .browser-header {
            background: #2c1810;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #d69a6b;
        }
        
        .browser-footer {
            background: #2c1810;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #d69a6b;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .selected-info {
            color: #ffc107;
            font-weight: 600;
        }
        
        .btn-primary { 
            background: #ffc107; 
            border-color: #ffc107; 
            color: #1a1a1a; 
        }
        
        .btn-primary:hover { 
            background: #d69a6b; 
            border-color: #d69a6b; 
        }
        
        .form-control, .form-select { 
            background: #3a3a3a; 
            border-color: #d69a6b; 
            color: #f5f5dc; 
        }
        
        .form-control:focus, .form-select:focus { 
            background: #3a3a3a; 
            border-color: #ffc107; 
            color: #f5f5dc; 
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25); 
        }
    </style>
</head>
<body>
    <div class="media-library-browser">
        <!-- Header -->
        <div class="browser-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-images"></i>
                        Media Library
                    </h5>
                    <small class="text-muted">
                        <?= $mode === 'select' ? 'Select media files' : 'Browse media files' ?>
                        <?= $multiple === 'true' ? ' (multiple selection allowed)' : '' ?>
                    </small>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" id="quickSearch" placeholder="Search...">
                        <select class="form-select form-select-sm" id="typeFilter" style="max-width: 120px;">
                            <option value="">All Types</option>
                            <?php if (strpos($allowedTypes, 'image') !== false): ?>
                                <option value="image">Images</option>
                            <?php endif; ?>
                            <?php if (strpos($allowedTypes, 'video') !== false): ?>
                                <option value="video">Videos</option>
                            <?php endif; ?>
                            <?php if (strpos($allowedTypes, 'audio') !== false): ?>
                                <option value="audio">Audio</option>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Media Grid -->
        <div class="media-browser" id="mediaBrowser">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading media files...</p>
            </div>
        </div>
        
        <!-- Footer (for select mode) -->
        <?php if ($mode === 'select'): ?>
        <div class="browser-footer">
            <div class="selected-info">
                <span id="selectedCount">0</span> file(s) selected
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                    Clear Selection
                </button>
                <button type="button" class="btn btn-primary btn-sm" onclick="confirmSelection()" disabled id="confirmBtn">
                    <i class="fas fa-check"></i> Select Files
                </button>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Hidden configuration -->
    <script>
        window.mediaBrowserConfig = {
            breweryId: <?= json_encode($breweryId) ?>,
            mode: <?= json_encode($mode) ?>,
            allowedTypes: <?= json_encode(explode(',', $allowedTypes)) ?>,
            multiple: <?= json_encode($multiple === 'true') ?>,
            apiBaseUrl: '../../api/digital-board/'
        };
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Media Browser JavaScript -->
    <script>
        class MediaBrowser {
            constructor() {
                this.config = window.mediaBrowserConfig;
                this.selectedItems = new Set();
                this.currentFilter = { type: '', search: '' };
                this.mediaData = [];
                
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.loadMedia();
            }
            
            setupEventListeners() {
                // Search
                const searchInput = document.getElementById('quickSearch');
                if (searchInput) {
                    let searchTimeout;
                    searchInput.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.currentFilter.search = e.target.value;
                            this.filterAndRenderMedia();
                        }, 300);
                    });
                }
                
                // Type filter
                const typeFilter = document.getElementById('typeFilter');
                if (typeFilter) {
                    typeFilter.addEventListener('change', (e) => {
                        this.currentFilter.type = e.target.value;
                        this.filterAndRenderMedia();
                    });
                }
            }
            
            async loadMedia() {
                try {
                    const response = await fetch(`${this.config.apiBaseUrl}media.php?brewery_id=${this.config.breweryId}&limit=100`);
                    const result = await response.json();
                    
                    if (result.success) {
                        this.mediaData = result.data.data || [];
                        this.filterAndRenderMedia();
                    } else {
                        this.showError('Failed to load media');
                    }
                } catch (error) {
                    console.error('Error loading media:', error);
                    this.showError('Error loading media');
                }
            }
            
            filterAndRenderMedia() {
                let filteredData = this.mediaData;
                
                // Filter by type
                if (this.currentFilter.type) {
                    filteredData = filteredData.filter(item => item.content_type === this.currentFilter.type);
                }
                
                // Filter by allowed types
                filteredData = filteredData.filter(item => this.config.allowedTypes.includes(item.content_type));
                
                // Filter by search
                if (this.currentFilter.search) {
                    const search = this.currentFilter.search.toLowerCase();
                    filteredData = filteredData.filter(item => 
                        item.title.toLowerCase().includes(search) ||
                        item.filename.toLowerCase().includes(search)
                    );
                }
                
                this.renderMedia(filteredData);
            }
            
            renderMedia(mediaData) {
                const browser = document.getElementById('mediaBrowser');
                if (!browser) return;
                
                if (mediaData.length === 0) {
                    browser.innerHTML = this.getEmptyStateHTML();
                    return;
                }
                
                browser.innerHTML = `<div class="media-grid">${mediaData.map(item => this.renderMediaItem(item)).join('')}</div>`;
                this.setupMediaItemListeners();
            }
            
            renderMediaItem(item) {
                const isImage = item.content_type === 'image';
                const isVideo = item.content_type === 'video';
                const isAudio = item.content_type === 'audio';
                
                const thumbnailHTML = isImage 
                    ? `<img src="${this.getThumbnailUrl(item)}" alt="${item.title}" loading="lazy">`
                    : `<i class="fas ${isVideo ? 'fa-video' : isAudio ? 'fa-music' : 'fa-file'} media-icon"></i>`;
                
                const sizeFormatted = this.formatFileSize(item.file_size);
                const selectableClass = this.config.mode === 'select' ? 'selectable' : '';
                const selectedClass = this.selectedItems.has(item.id) ? 'selected' : '';
                
                return `
                    <div class="media-item ${selectableClass} ${selectedClass}" data-media-id="${item.id}" data-media-type="${item.content_type}">
                        <div class="media-thumbnail">
                            ${thumbnailHTML}
                            <div class="media-type-icon ${item.content_type}">
                                <i class="fas ${isImage ? 'fa-image' : isVideo ? 'fa-video' : 'fa-music'}"></i>
                            </div>
                        </div>
                        <div class="media-info">
                            <div class="media-title" title="${item.title}">${item.title}</div>
                            <div class="media-meta">${sizeFormatted}</div>
                        </div>
                    </div>
                `;
            }
            
            setupMediaItemListeners() {
                if (this.config.mode !== 'select') return;
                
                const mediaItems = document.querySelectorAll('.media-item.selectable');
                mediaItems.forEach(item => {
                    item.addEventListener('click', () => {
                        const mediaId = item.dataset.mediaId;
                        this.toggleSelection(mediaId, item);
                    });
                });
            }
            
            toggleSelection(mediaId, itemElement) {
                if (this.selectedItems.has(mediaId)) {
                    this.selectedItems.delete(mediaId);
                    itemElement.classList.remove('selected');
                } else {
                    if (!this.config.multiple) {
                        // Clear previous selection for single select
                        this.clearSelection();
                    }
                    this.selectedItems.add(mediaId);
                    itemElement.classList.add('selected');
                }
                
                this.updateSelectionUI();
            }
            
            updateSelectionUI() {
                const countElement = document.getElementById('selectedCount');
                const confirmBtn = document.getElementById('confirmBtn');
                
                if (countElement) {
                    countElement.textContent = this.selectedItems.size;
                }
                
                if (confirmBtn) {
                    confirmBtn.disabled = this.selectedItems.size === 0;
                }
            }
            
            getThumbnailUrl(item) {
                return item.file_path.replace('../../', '');
            }
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            getEmptyStateHTML() {
                return `
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h5>No Media Files Found</h5>
                        <p class="text-muted">No files match your current filters.</p>
                    </div>
                `;
            }
            
            showError(message) {
                const browser = document.getElementById('mediaBrowser');
                if (browser) {
                    browser.innerHTML = `
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                            <h5>Error</h5>
                            <p class="text-muted">${message}</p>
                        </div>
                    `;
                }
            }
            
            getSelectedMedia() {
                return Array.from(this.selectedItems).map(id => 
                    this.mediaData.find(item => item.id === id)
                ).filter(Boolean);
            }
        }
        
        // Global functions
        function clearSelection() {
            if (window.mediaBrowser) {
                window.mediaBrowser.selectedItems.clear();
                document.querySelectorAll('.media-item.selected').forEach(item => {
                    item.classList.remove('selected');
                });
                window.mediaBrowser.updateSelectionUI();
            }
        }
        
        function confirmSelection() {
            if (window.mediaBrowser) {
                const selectedMedia = window.mediaBrowser.getSelectedMedia();
                
                // Send selection to parent window if in iframe/modal
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'mediaSelected',
                        data: selectedMedia
                    }, '*');
                } else {
                    // Handle selection in same window
                    console.log('Selected media:', selectedMedia);
                }
            }
        }
        
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            window.mediaBrowser = new MediaBrowser();
        });
    </script>
</body>
</html>
