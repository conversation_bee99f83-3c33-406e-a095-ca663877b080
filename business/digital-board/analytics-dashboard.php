<?php
/**
 * Analytics Dashboard
 * Phase 7 - Advanced Features
 * 
 * Comprehensive analytics and reporting interface for digital boards
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/DigitalBoardAnalytics.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/enhanced-login.php');
    exit;
}

// Initialize analytics service
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $analytics = new DigitalBoardAnalytics($pdo);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get parameters
$boardId = $_GET['board_id'] ?? null;
$timeframe = $_GET['timeframe'] ?? '30_days';
$view = $_GET['view'] ?? 'overview';

// Get brewery boards for selection
$stmt = $pdo->prepare("
    SELECT id, name, is_active 
    FROM digital_boards 
    WHERE brewery_id = ? 
    ORDER BY name
");
$stmt->execute([$user['brewery_id']]);
$boards = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get analytics data
$analyticsData = [];
if ($boardId) {
    $analyticsData = $analytics->getBoardAnalytics($boardId, $timeframe);
} else {
    $analyticsData = $analytics->getBreweryAnalytics($user['brewery_id'], $timeframe);
}

$pageTitle = 'Analytics Dashboard - Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin.css" rel="stylesheet">
    <link href="../../assets/css/digital-board-mobile.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .analytics-header {
            background: linear-gradient(135deg, #6f4c3e, #d69a6b);
            color: #f5f5dc;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #ffc107;
            margin-bottom: 8px;
        }
        
        .metric-label {
            color: #f5f5dc;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-change {
            font-size: 0.85rem;
            margin-top: 8px;
        }
        
        .metric-change.positive {
            color: #28a745;
        }
        
        .metric-change.negative {
            color: #dc3545;
        }
        
        .chart-container {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }
        
        .chart-title {
            color: #f5f5dc;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .analytics-filters {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-label {
            color: #f5f5dc;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .performance-table {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .performance-table .table {
            margin-bottom: 0;
            color: #f5f5dc;
        }
        
        .performance-table .table th {
            background: #6f4c3e;
            color: #f5f5dc;
            border-color: #d69a6b;
        }
        
        .performance-table .table td {
            border-color: rgba(214, 154, 107, 0.3);
        }
        
        .heatmap-container {
            position: relative;
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .heatmap-point {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: rgba(255, 193, 7, 0.7);
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        
        .export-buttons {
            margin-bottom: 20px;
        }
        
        .real-time-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #28a745;
            font-size: 0.85rem;
        }
        
        .real-time-dot {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <img src="../../assets/images/beersty-logo.png" alt="Beersty" height="40">
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?= htmlspecialchars($user['name'] ?? $user['email']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../../auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Analytics Header -->
    <div class="analytics-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h2 mb-2">
                        <i class="fas fa-chart-line"></i>
                        Analytics Dashboard
                    </h1>
                    <p class="mb-0">Comprehensive insights into your digital board performance</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="real-time-indicator">
                        <div class="real-time-dot"></div>
                        Real-time data
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Filters -->
        <div class="analytics-filters">
            <div class="row">
                <div class="col-md-3">
                    <div class="filter-group">
                        <label class="filter-label">Board Selection</label>
                        <select class="form-select" id="boardSelect" onchange="changeBoard(this.value)">
                            <option value="">All Boards</option>
                            <?php foreach ($boards as $board): ?>
                                <option value="<?= $board['id'] ?>" <?= $boardId === $board['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($board['name']) ?>
                                    <?= $board['is_active'] ? '' : ' (Inactive)' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label class="filter-label">Time Period</label>
                        <select class="form-select" id="timeframeSelect" onchange="changeTimeframe(this.value)">
                            <option value="7_days" <?= $timeframe === '7_days' ? 'selected' : '' ?>>Last 7 Days</option>
                            <option value="30_days" <?= $timeframe === '30_days' ? 'selected' : '' ?>>Last 30 Days</option>
                            <option value="90_days" <?= $timeframe === '90_days' ? 'selected' : '' ?>>Last 90 Days</option>
                            <option value="1_year" <?= $timeframe === '1_year' ? 'selected' : '' ?>>Last Year</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label class="filter-label">View Type</label>
                        <select class="form-select" id="viewSelect" onchange="changeView(this.value)">
                            <option value="overview" <?= $view === 'overview' ? 'selected' : '' ?>>Overview</option>
                            <option value="engagement" <?= $view === 'engagement' ? 'selected' : '' ?>>Engagement</option>
                            <option value="content" <?= $view === 'content' ? 'selected' : '' ?>>Content Performance</option>
                            <option value="heatmap" <?= $view === 'heatmap' ? 'selected' : '' ?>>Interaction Heatmap</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="filter-group">
                        <label class="filter-label">Export Data</label>
                        <div class="export-buttons">
                            <button class="btn btn-outline-primary btn-sm" onclick="exportData('csv')">
                                <i class="fas fa-download"></i> CSV
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="exportData('pdf')">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overview Metrics -->
        <?php if ($view === 'overview'): ?>
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="totalViews">
                        <?= number_format($analyticsData['overview']['total_views'] ?? 0) ?>
                    </div>
                    <div class="metric-label">Total Views</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +12.5% from last period
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="uniqueViewers">
                        <?= number_format($analyticsData['overview']['unique_viewers'] ?? 0) ?>
                    </div>
                    <div class="metric-label">Unique Viewers</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +8.3% from last period
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="avgDuration">
                        <?= gmdate("i:s", $analyticsData['overview']['avg_view_duration'] ?? 0) ?>
                    </div>
                    <div class="metric-label">Avg. View Duration</div>
                    <div class="metric-change negative">
                        <i class="fas fa-arrow-down"></i> -2.1% from last period
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="engagementRate">
                        <?= number_format($analyticsData['engagement']['engagement_rate'] ?? 0, 1) ?>%
                    </div>
                    <div class="metric-label">Engagement Rate</div>
                    <div class="metric-change positive">
                        <i class="fas fa-arrow-up"></i> +5.7% from last period
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <h5 class="chart-title">Views Over Time</h5>
                    <canvas id="viewsChart" height="300"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h5 class="chart-title">Device Types</h5>
                    <canvas id="deviceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Content Performance -->
        <?php if ($view === 'content'): ?>
        <div class="row">
            <div class="col-md-6">
                <div class="performance-table">
                    <h5 class="chart-title p-3 mb-0">Top Performing Slides</h5>
                    <table class="table table-dark">
                        <thead>
                            <tr>
                                <th>Slide</th>
                                <th>Views</th>
                                <th>Avg. Duration</th>
                                <th>Completion Rate</th>
                            </tr>
                        </thead>
                        <tbody id="slidePerformanceTable">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <div class="performance-table">
                    <h5 class="chart-title p-3 mb-0">Beer Menu Interactions</h5>
                    <table class="table table-dark">
                        <thead>
                            <tr>
                                <th>Beer</th>
                                <th>Interactions</th>
                                <th>QR Scans</th>
                                <th>Detail Views</th>
                            </tr>
                        </thead>
                        <tbody id="beerPerformanceTable">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Interaction Heatmap -->
        <?php if ($view === 'heatmap'): ?>
        <div class="row">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="chart-title">Interaction Heatmap</h5>
                    <div class="heatmap-container" id="heatmapContainer" style="height: 600px;">
                        <!-- Heatmap will be generated here -->
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/digital-board-mobile.js"></script>
    
    <script>
        // Analytics data from PHP
        const analyticsData = <?= json_encode($analyticsData) ?>;
        
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            setupRealTimeUpdates();
        });
        
        function initializeCharts() {
            // Views over time chart
            if (document.getElementById('viewsChart')) {
                const ctx = document.getElementById('viewsChart').getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Views',
                            data: [120, 190, 300, 500, 200, 300, 450],
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: { color: '#f5f5dc' }
                            }
                        },
                        scales: {
                            x: { ticks: { color: '#f5f5dc' } },
                            y: { ticks: { color: '#f5f5dc' } }
                        }
                    }
                });
            }
            
            // Device types chart
            if (document.getElementById('deviceChart')) {
                const ctx = document.getElementById('deviceChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Desktop', 'Mobile', 'Tablet'],
                        datasets: [{
                            data: [45, 35, 20],
                            backgroundColor: ['#ffc107', '#d69a6b', '#6f4c3e']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: { color: '#f5f5dc' }
                            }
                        }
                    }
                });
            }
        }
        
        function setupRealTimeUpdates() {
            // Update analytics data every 30 seconds
            setInterval(updateAnalytics, 30000);
        }
        
        function updateAnalytics() {
            // Fetch updated analytics data
            fetch(`../../api/analytics.php?board_id=${getCurrentBoardId()}&timeframe=${getCurrentTimeframe()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateMetrics(data.analytics);
                    }
                })
                .catch(error => console.error('Analytics update error:', error));
        }
        
        function updateMetrics(data) {
            // Update metric cards with new data
            if (data.overview) {
                document.getElementById('totalViews').textContent = 
                    new Intl.NumberFormat().format(data.overview.total_views);
                document.getElementById('uniqueViewers').textContent = 
                    new Intl.NumberFormat().format(data.overview.unique_viewers);
            }
        }
        
        // Filter change handlers
        function changeBoard(boardId) {
            updateURL({ board_id: boardId });
        }
        
        function changeTimeframe(timeframe) {
            updateURL({ timeframe: timeframe });
        }
        
        function changeView(view) {
            updateURL({ view: view });
        }
        
        function updateURL(params) {
            const url = new URL(window.location);
            Object.keys(params).forEach(key => {
                if (params[key]) {
                    url.searchParams.set(key, params[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.location.href = url.toString();
        }
        
        function getCurrentBoardId() {
            return document.getElementById('boardSelect').value;
        }
        
        function getCurrentTimeframe() {
            return document.getElementById('timeframeSelect').value;
        }
        
        function exportData(format) {
            const boardId = getCurrentBoardId();
            const timeframe = getCurrentTimeframe();
            const url = `../../api/analytics.php?export=${format}&board_id=${boardId}&timeframe=${timeframe}`;
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
