<?php
/**
 * Enhanced Digital Board Setup Script
 * Phase 4 10.0 - Database Schema Enhancement
 * 
 * This script sets up the enhanced digital board system with all new tables
 * and validates the database schema for proper operation.
 */

require_once '../../config/database.php';

// Set content type to HTML for better display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Digital Board Setup - Phase 4 10.0</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #1a1a1a; color: #f5f5dc; font-family: 'Inter', sans-serif; }
        .container { max-width: 1200px; }
        .card { background: #2c1810; border: 1px solid #d69a6b; }
        .card-header { background: #6f4c3e; color: #f5f5dc; }
        .btn-primary { background: #ffc107; border-color: #ffc107; color: #1a1a1a; }
        .btn-primary:hover { background: #d69a6b; border-color: #d69a6b; }
        .alert-success { background: #2d5a2d; border-color: #4caf50; color: #90ee90; }
        .alert-danger { background: #5a2d2d; border-color: #f44336; color: #ffcccb; }
        .alert-warning { background: #5a4d2d; border-color: #ff9800; color: #ffe0b3; }
        .alert-info { background: #2d4d5a; border-color: #2196f3; color: #b3d9ff; }
        .table-dark { background: #2c1810; }
        .table-dark td, .table-dark th { border-color: #d69a6b; }
        .status-icon { font-size: 1.2em; margin-right: 8px; }
        .setup-step { margin-bottom: 20px; padding: 15px; border-radius: 8px; }
        .step-success { background: rgba(76, 175, 80, 0.1); border-left: 4px solid #4caf50; }
        .step-error { background: rgba(244, 67, 54, 0.1); border-left: 4px solid #f44336; }
        .step-warning { background: rgba(255, 152, 0, 0.1); border-left: 4px solid #ff9800; }
        .step-info { background: rgba(33, 150, 243, 0.1); border-left: 4px solid #2196f3; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-database"></i>
                            Enhanced Digital Board Setup - Phase 4 10.0
                        </h1>
                        <p class="mb-0 mt-2">Database Schema Enhancement & Validation</p>
                    </div>
                    <div class="card-body">
                        
<?php
// Initialize setup status
$setupResults = [];
$hasErrors = false;

try {
    // Test database connection
    $pdo = new PDO($dsn, $username, $password, $options);
    $setupResults[] = [
        'step' => 'Database Connection',
        'status' => 'success',
        'message' => 'Successfully connected to database: ' . $database,
        'icon' => 'fas fa-check-circle'
    ];
} catch (PDOException $e) {
    $setupResults[] = [
        'step' => 'Database Connection',
        'status' => 'error',
        'message' => 'Failed to connect to database: ' . $e->getMessage(),
        'icon' => 'fas fa-times-circle'
    ];
    $hasErrors = true;
}

if (!$hasErrors) {
    // Check if enhanced tables exist
    $requiredTables = [
        'digital_boards' => 'Enhanced digital boards table with full settings',
        'slideshow_presentations' => 'Slideshow presentations management',
        'slideshow_slides' => 'Individual slides with ordering',
        'template_library' => 'Custom template storage',
        'slide_content' => 'Media and content storage',
        'beer_menu' => 'Enhanced beer menu table',
        'beer_styles' => 'Beer styles classification',
        'breweries' => 'Brewery information'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($requiredTables as $table => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $existingTables[$table] = $description;
            } else {
                $missingTables[$table] = $description;
            }
        } catch (PDOException $e) {
            $missingTables[$table] = $description;
        }
    }
    
    if (empty($missingTables)) {
        $setupResults[] = [
            'step' => 'Table Structure Validation',
            'status' => 'success',
            'message' => 'All required tables exist (' . count($existingTables) . ' tables found)',
            'icon' => 'fas fa-check-circle'
        ];
    } else {
        $setupResults[] = [
            'step' => 'Table Structure Validation',
            'status' => 'warning',
            'message' => 'Missing tables detected: ' . implode(', ', array_keys($missingTables)),
            'icon' => 'fas fa-exclamation-triangle'
        ];
    }
    
    // Check enhanced digital_boards table structure
    try {
        $stmt = $pdo->query("DESCRIBE digital_boards");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = [
            'id', 'brewery_id', 'board_id', 'name', 'description', 'settings',
            'template_id', 'current_slideshow_id', 'display_mode', 'theme',
            'layout', 'show_prices', 'show_descriptions', 'show_abv', 'show_ibu',
            'ticker_message', 'ticker_enabled', 'is_active'
        ];
        
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            $setupResults[] = [
                'step' => 'Digital Boards Table Enhancement',
                'status' => 'success',
                'message' => 'Digital boards table has all enhanced columns (' . count($columns) . ' columns)',
                'icon' => 'fas fa-check-circle'
            ];
        } else {
            $setupResults[] = [
                'step' => 'Digital Boards Table Enhancement',
                'status' => 'warning',
                'message' => 'Missing enhanced columns: ' . implode(', ', $missingColumns),
                'icon' => 'fas fa-exclamation-triangle'
            ];
        }
    } catch (PDOException $e) {
        $setupResults[] = [
            'step' => 'Digital Boards Table Enhancement',
            'status' => 'error',
            'message' => 'Error checking digital_boards table: ' . $e->getMessage(),
            'icon' => 'fas fa-times-circle'
        ];
    }
    
    // Check for system templates
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM template_library WHERE category = 'system'");
        $systemTemplateCount = $stmt->fetchColumn();
        
        if ($systemTemplateCount >= 4) {
            $setupResults[] = [
                'step' => 'System Templates',
                'status' => 'success',
                'message' => "System templates installed ($systemTemplateCount templates available)",
                'icon' => 'fas fa-check-circle'
            ];
        } else {
            $setupResults[] = [
                'step' => 'System Templates',
                'status' => 'warning',
                'message' => "Only $systemTemplateCount system templates found (4 expected)",
                'icon' => 'fas fa-exclamation-triangle'
            ];
        }
    } catch (PDOException $e) {
        $setupResults[] = [
            'step' => 'System Templates',
            'status' => 'error',
            'message' => 'Error checking system templates: ' . $e->getMessage(),
            'icon' => 'fas fa-times-circle'
        ];
    }
    
    // Check foreign key relationships
    try {
        $stmt = $pdo->query("
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = '$database' 
            AND TABLE_NAME IN ('digital_boards', 'slideshow_presentations', 'slideshow_slides', 'template_library', 'slide_content')
        ");
        $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($foreignKeys) >= 6) {
            $setupResults[] = [
                'step' => 'Foreign Key Relationships',
                'status' => 'success',
                'message' => 'Foreign key relationships properly configured (' . count($foreignKeys) . ' constraints)',
                'icon' => 'fas fa-check-circle'
            ];
        } else {
            $setupResults[] = [
                'step' => 'Foreign Key Relationships',
                'status' => 'warning',
                'message' => 'Some foreign key relationships may be missing (' . count($foreignKeys) . ' found)',
                'icon' => 'fas fa-exclamation-triangle'
            ];
        }
    } catch (PDOException $e) {
        $setupResults[] = [
            'step' => 'Foreign Key Relationships',
            'status' => 'info',
            'message' => 'Could not verify foreign key relationships (this is normal for some MySQL configurations)',
            'icon' => 'fas fa-info-circle'
        ];
    }
    
    // Test data insertion capability
    try {
        $testBreweryId = 'test-brewery-' . uniqid();
        $testBoardId = 'test-board-' . uniqid();
        
        // Insert test brewery
        $stmt = $pdo->prepare("INSERT INTO breweries (id, name) VALUES (?, ?)");
        $stmt->execute([$testBreweryId, 'Test Brewery for Setup']);
        
        // Insert test digital board
        $stmt = $pdo->prepare("
            INSERT INTO digital_boards (brewery_id, board_id, name, settings) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $testBreweryId, 
            $testBoardId, 
            'Test Board', 
            json_encode(['test' => true])
        ]);
        
        // Clean up test data
        $pdo->prepare("DELETE FROM digital_boards WHERE brewery_id = ?")->execute([$testBreweryId]);
        $pdo->prepare("DELETE FROM breweries WHERE id = ?")->execute([$testBreweryId]);
        
        $setupResults[] = [
            'step' => 'Data Operations Test',
            'status' => 'success',
            'message' => 'Database operations working correctly (insert/delete test passed)',
            'icon' => 'fas fa-check-circle'
        ];
    } catch (PDOException $e) {
        $setupResults[] = [
            'step' => 'Data Operations Test',
            'status' => 'error',
            'message' => 'Database operations test failed: ' . $e->getMessage(),
            'icon' => 'fas fa-times-circle'
        ];
        $hasErrors = true;
    }
}

// Display results
foreach ($setupResults as $result) {
    $stepClass = 'step-' . $result['status'];
    echo "<div class='setup-step $stepClass'>";
    echo "<h5><i class='{$result['icon']} status-icon'></i>{$result['step']}</h5>";
    echo "<p class='mb-0'>{$result['message']}</p>";
    echo "</div>";
}

// Overall status
if ($hasErrors) {
    echo "<div class='alert alert-danger mt-4'>";
    echo "<h5><i class='fas fa-times-circle'></i> Setup Issues Detected</h5>";
    echo "<p class='mb-0'>Some issues were found during setup validation. Please run the migration script or check your database configuration.</p>";
    echo "</div>";
} else {
    $warningCount = count(array_filter($setupResults, function($r) { return $r['status'] === 'warning'; }));
    if ($warningCount > 0) {
        echo "<div class='alert alert-warning mt-4'>";
        echo "<h5><i class='fas fa-exclamation-triangle'></i> Setup Complete with Warnings</h5>";
        echo "<p class='mb-0'>The enhanced digital board system is functional but some optimizations may be needed. Consider running the migration script for full enhancement.</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success mt-4'>";
        echo "<h5><i class='fas fa-check-circle'></i> Setup Complete!</h5>";
        echo "<p class='mb-0'>The enhanced digital board system is fully configured and ready for use. All Phase 4 10.0 enhancements are active.</p>";
        echo "</div>";
    }
}
?>

                        <div class="mt-4">
                            <h5>Quick Actions</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="index.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="templates.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-palette"></i> Templates
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="slideshow-builder.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-play-circle"></i> Slideshows
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="validate-schema.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-check-double"></i> Validate Schema
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Migration Scripts</h5>
                            <p class="text-muted">If you need to upgrade from an older version or fix missing components:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Full Schema Enhancement</h6>
                                            <p class="small">Run the complete Phase 4 10.0 schema enhancement</p>
                                            <code>mysql -u username -p beersty_db < ../../database/digital_board_schema_enhancement.sql</code>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Migration Script</h6>
                                            <p class="small">Safely migrate existing data to enhanced schema</p>
                                            <code>mysql -u username -p beersty_db < ../../database/migration_phase4_10.sql</code>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
