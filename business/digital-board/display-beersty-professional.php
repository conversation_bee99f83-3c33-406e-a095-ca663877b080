<?php
/**
 * Beersty Professional Template - Based on Reference Design
 * Two-column layout with beer images and multiple pricing options
 */

// Sample beer data with multiple sizes
$sampleBeers = [
    ['name' => 'Hoppy IPA', 'brewery' => 'Demo Craft Brewery', 'style' => 'India Pale Ale', 'abv' => 6.8, 'ibu' => 68, 'price_16oz' => 8.50, 'price_32oz' => 15.00],
    ['name' => 'Golden Wheat', 'brewery' => 'Demo Craft Brewery', 'style' => 'Wheat Beer', 'abv' => 4.5, 'ibu' => 12, 'price_16oz' => 6.00, 'price_32oz' => 11.00],
    ['name' => 'Midnight Stout', 'brewery' => 'Demo Craft Brewery', 'style' => 'Imperial Stout', 'abv' => 7.2, 'ibu' => 35, 'price_16oz' => 9.00, 'price_32oz' => 16.50],
    ['name' => 'Crisp Lager', 'brewery' => 'Demo Craft Brewery', 'style' => 'German Lager', 'abv' => 4.8, 'ibu' => 18, 'price_16oz' => 5.50, 'price_32oz' => 10.00],
    ['name' => 'Robust Porter', 'brewery' => 'Demo Craft Brewery', 'style' => 'Robust Porter', 'abv' => 6.2, 'ibu' => 28, 'price_16oz' => 7.50, 'price_32oz' => 13.50],
    ['name' => 'Czech Pilsner', 'brewery' => 'Demo Craft Brewery', 'style' => 'Czech Pilsner', 'abv' => 5.2, 'ibu' => 35, 'price_16oz' => 7.00, 'price_32oz' => 12.50],
    ['name' => 'Nutty Brown', 'brewery' => 'Demo Craft Brewery', 'style' => 'English Brown Ale', 'abv' => 5.8, 'ibu' => 22, 'price_16oz' => 6.50, 'price_32oz' => 11.50],
    ['name' => 'American Pale', 'brewery' => 'Demo Craft Brewery', 'style' => 'American Pale Ale', 'abv' => 5.5, 'ibu' => 42, 'price_16oz' => 6.75, 'price_32oz' => 12.00],
    ['name' => 'Seasonal Special', 'brewery' => 'Demo Craft Brewery', 'style' => 'Pumpkin Ale', 'abv' => 6.0, 'ibu' => 25, 'price_16oz' => 8.00, 'price_32oz' => 14.50],
    ['name' => 'House Blend', 'brewery' => 'Demo Craft Brewery', 'style' => 'Porter/IPA Blend', 'abv' => 6.5, 'ibu' => 45, 'price_16oz' => 7.75, 'price_32oz' => 14.00],
    ['name' => 'Double IPA', 'brewery' => 'Demo Craft Brewery', 'style' => 'Double IPA', 'abv' => 8.2, 'ibu' => 85, 'price_16oz' => 9.50, 'price_32oz' => 17.00],
    ['name' => 'Sour Cherry', 'brewery' => 'Demo Craft Brewery', 'style' => 'Sour Ale', 'abv' => 4.2, 'ibu' => 8, 'price_16oz' => 8.25, 'price_32oz' => 15.50],
    ['name' => 'Barrel Aged', 'brewery' => 'Demo Craft Brewery', 'style' => 'Barrel Aged Stout', 'abv' => 9.5, 'ibu' => 40, 'price_16oz' => 12.00, 'price_32oz' => 22.00],
    ['name' => 'Session IPA', 'brewery' => 'Demo Craft Brewery', 'style' => 'Session IPA', 'abv' => 4.0, 'ibu' => 50, 'price_16oz' => 5.75, 'price_32oz' => 10.50]
];

// Split beers into two columns
$leftColumn = array_slice($sampleBeers, 0, 7);
$rightColumn = array_slice($sampleBeers, 7, 7);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beersty Professional Template - Digital Beer Board</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #f8f9fa;
            overflow: hidden;
        }
        .menu-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 1%;
            box-sizing: border-box;
            background-color: #000;
        }
        .full-screen {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding-bottom: 70px;
            box-sizing: border-box;
            width: 100%;
        }
        .header {
            background-color: #2c2c2c;
            padding: 10px 0;
            margin-bottom: 1vh;
            border-radius: 5px;
            width: 100%;
        }
        .header h1 {
            text-align: center;
            font-size: clamp(1.5rem, 4vw, 3rem);
            color: #e9ecef;
            margin: 0;
        }
        .beer-columns {
            flex: 1;
            display: flex;
            justify-content: space-between;
            gap: 1vw;
            width: 100%;
            overflow: hidden;
        }
        .beer-column {
            background-color: #343a40;
            padding: 1vh 1vw;
            border-radius: 5px;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1vh;
            width: 49%;
            height: 100%;
        }
        .beer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: clamp(4px, 0.8vw, 10px);
            background-color: #495057;
            border-radius: 3px;
            font-size: clamp(0.6rem, 1vw, 0.9rem);
            width: 100%;
            flex: 1;
            min-height: 0;
        }
        .beer-image {
            width: clamp(40px, 5vw, 60px);
            height: clamp(40px, 5vw, 60px);
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border-radius: 5px;
            margin-right: clamp(4px, 0.6vw, 6px);
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: clamp(1rem, 2vw, 1.5rem);
        }
        .beer-details {
            flex-grow: 1;
            margin-right: clamp(3px, 0.5vw, 5px);
        }
        .beer-details strong {
            color: #e9ecef;
        }
        .beer-details small {
            color: #ced4da;
            font-size: clamp(0.5rem, 0.9vw, 0.7rem);
        }
        .size-price {
            text-align: right;
            white-space: nowrap;
        }
        .size-price .badge {
            background-color: #6c757d;
            color: #fff;
            margin-left: clamp(2px, 0.4vw, 4px);
            font-size: clamp(0.5rem, 0.8vw, 0.7rem);
        }
        .ticker {
            position: fixed;
            bottom: 0;
            width: 100%;
            background: #222;
            color: #fff;
            font-size: clamp(0.8rem, 2vw, 1.2rem);
            white-space: nowrap;
            overflow: hidden;
            height: 70px;
            display: flex;
            align-items: center;
            z-index: 1000;
        }
        .ticker-text {
            display: inline-block;
            padding-left: 100%;
            animation: ticker-scroll 20s linear infinite;
        }
        @keyframes ticker-scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100%); }
        }
        
        /* Template Badge */
        .template-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #6c757d;
            color: #fff;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            z-index: 1000;
        }
        
        /* Preview Controls */
        .preview-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .control-btn {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
        
        /* Responsive Design */
        @media (min-width: 1440px) {
            .beer-item {
                padding: clamp(6px, 1vw, 10px);
                font-size: clamp(0.8rem, 1.2vw, 1rem);
            }
            .beer-image {
                width: clamp(50px, 4vw, 60px);
                height: clamp(50px, 4vw, 60px);
            }
            .beer-details small {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem);
            }
            .size-price .badge {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem);
            }
        }
        
        @media (max-width: 1366px) and (min-width: 1025px) {
            .beer-item {
                padding: clamp(5px, 0.8vw, 8px);
                font-size: clamp(0.7rem, 1vw, 0.9rem);
            }
            .beer-image {
                width: clamp(45px, 4vw, 55px);
                height: clamp(45px, 4vw, 55px);
            }
            .beer-column {
                gap: 0.7vh;
            }
        }
        
        @media (max-width: 1024px) and (min-width: 769px) {
            .beer-columns {
                flex-direction: column;
                gap: 1vh;
            }
            .beer-column {
                width: 100%;
                padding: 1vh 2vw;
            }
            .beer-item {
                padding: clamp(4px, 0.7vw, 8px);
                font-size: clamp(0.6rem, 1vw, 0.8rem);
                flex: unset;
                min-height: 60px;
            }
            .beer-image {
                width: clamp(40px, 4vw, 50px);
                height: clamp(40px, 4vw, 50px);
            }
        }
        
        @media (max-width: 768px) and (min-width: 577px) {
            .beer-columns {
                flex-direction: column;
                gap: 0.8vh;
            }
            .beer-column {
                width: 100%;
            }
            .beer-item {
                padding: clamp(3px, 0.6vw, 6px);
                font-size: clamp(0.6rem, 0.9vw, 0.7rem);
                flex: unset;
                min-height: 50px;
            }
            .beer-image {
                width: clamp(35px, 4vw, 45px);
                height: clamp(35px, 4vw, 45px);
            }
            .size-price {
                text-align: right;
            }
        }
        
        @media (max-width: 576px) {
            .beer-columns {
                flex-direction: column;
                gap: 0.5vh;
            }
            .beer-column {
                width: 100%;
                padding: 0.5vh 1vw;
            }
            .beer-item {
                flex-direction: column;
                align-items: flex-start;
                padding: clamp(3px, 0.5vw, 5px);
                font-size: clamp(0.5rem, 0.8vw, 0.7rem);
                flex: unset;
                min-height: 40px;
            }
            .beer-image {
                width: clamp(30px, 3vw, 40px);
                height: clamp(30px, 3vw, 40px);
                margin-bottom: 0.3vh;
            }
            .size-price {
                text-align: left;
                margin-top: 0.3vh;
            }
            .ticker {
                height: 50px;
                font-size: clamp(0.6rem, 1.5vw, 0.9rem);
            }
        }
    </style>
</head>
<body>
    <!-- Template Badge -->
    <div class="template-badge">
        Beersty Professional Template
    </div>
    
    <!-- Preview Controls -->
    <div class="preview-controls">
        <button class="control-btn" onclick="window.close()">
            <i class="fas fa-times me-1"></i>Close
        </button>
        <button class="control-btn" onclick="toggleFullscreen()">
            <i class="fas fa-expand me-1"></i>Fullscreen
        </button>
    </div>
    
    <!-- Full Screen Content -->
    <div class="menu-wrapper">
        <div class="full-screen">
            <!-- Header -->
            <div class="header">
                <h1>Demo Craft Brewery Menu</h1>
            </div>

            <!-- Beer Columns -->
            <div class="beer-columns">
                <!-- Left Column -->
                <div class="beer-column">
                    <?php foreach ($leftColumn as $beer): ?>
                        <div class="beer-item">
                            <div class="beer-image">
                                <i class="fas fa-beer"></i>
                            </div>
                            <div class="beer-details">
                                <strong><?php echo htmlspecialchars($beer['name']); ?></strong> <br>
                                <small><?php echo htmlspecialchars($beer['brewery']); ?> - <?php echo htmlspecialchars($beer['style']); ?> (<?php echo $beer['abv']; ?>% ABV - <?php echo $beer['ibu']; ?> IBU)</small>
                            </div>
                            <div class="size-price">
                                <span class="badge">16oz</span> $<?php echo number_format($beer['price_16oz'], 2); ?> <br>
                                <span class="badge">Tall 32oz</span> $<?php echo number_format($beer['price_32oz'], 2); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Right Column -->
                <div class="beer-column">
                    <?php foreach ($rightColumn as $beer): ?>
                        <div class="beer-item">
                            <div class="beer-image">
                                <i class="fas fa-beer"></i>
                            </div>
                            <div class="beer-details">
                                <strong><?php echo htmlspecialchars($beer['name']); ?></strong> <br>
                                <small><?php echo htmlspecialchars($beer['brewery']); ?> - <?php echo htmlspecialchars($beer['style']); ?> (<?php echo $beer['abv']; ?>% ABV - <?php echo $beer['ibu']; ?> IBU)</small>
                            </div>
                            <div class="size-price">
                                <span class="badge">16oz</span> $<?php echo number_format($beer['price_16oz'], 2); ?> <br>
                                <span class="badge">Tall 32oz</span> $<?php echo number_format($beer['price_32oz'], 2); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Ticker -->
            <div class="ticker">
                <div class="ticker-text">Welcome to Demo Craft Brewery! Experience the Beersty Professional template design - Rate your beer & get featured here! 🍻</div>
            </div>
        </div>
    </div>

    <script>
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    window.close();
                }
            }
            
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
        
        console.log('Beersty Professional Template loaded successfully!');
    </script>
</body>
</html>
