<?php
/**
 * Mobile-Optimized Digital Board Dashboard
 * Phase 6 - Mobile & Responsive Optimization
 * 
 * Touch-friendly dashboard interface for mobile devices
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/enhanced-login.php');
    exit;
}

$pageTitle = 'Mobile Dashboard - Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#ffc107">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Digital Board">
    
    <!-- PWA Links -->
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="../../assets/images/icons/icon-192x192.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/digital-board-mobile.css" rel="stylesheet">
    
    <style>
        /* Dashboard-specific mobile styles */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--mobile-primary);
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--mobile-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .action-card {
            background: linear-gradient(135deg, var(--mobile-primary), var(--mobile-primary-dark));
            color: var(--mobile-background);
            border-radius: 16px;
            padding: 24px 16px;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .action-card:hover {
            color: var(--mobile-background);
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 12px;
        }
        
        .action-label {
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .recent-activity {
            background: var(--mobile-surface);
            border: 1px solid var(--mobile-primary-dark);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .activity-header {
            background: var(--mobile-primary);
            color: var(--mobile-background);
            padding: 16px;
            font-weight: 600;
        }
        
        .activity-item {
            padding: 16px;
            border-bottom: 1px solid rgba(214, 154, 107, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 193, 7, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--mobile-primary);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 0.75rem;
            color: var(--mobile-text-muted);
        }
        
        .pwa-install-banner {
            background: linear-gradient(135deg, var(--mobile-secondary), var(--mobile-primary-dark));
            color: var(--mobile-text);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
            display: none;
        }
        
        .pwa-install-banner.show {
            display: block;
        }
        
        .offline-indicator {
            position: fixed;
            top: calc(var(--mobile-header-height) + 8px);
            left: 16px;
            right: 16px;
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-align: center;
            font-size: 0.875rem;
            z-index: 1001;
            transform: translateY(-100px);
            transition: transform 0.3s ease;
        }
        
        .offline-indicator.show {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Offline Indicator -->
    <div id="offlineIndicator" class="offline-indicator">
        <i class="fas fa-wifi-slash"></i>
        You're offline. Some features may be limited.
    </div>

    <!-- PWA Install Banner -->
    <div id="pwaInstallBanner" class="pwa-install-banner">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <strong>Install App</strong>
                <p class="mb-0 small">Add to home screen for better experience</p>
            </div>
            <button class="mobile-btn mobile-btn-primary" onclick="installPWA()">
                <i class="fas fa-download"></i>
                Install
            </button>
        </div>
    </div>

    <!-- Mobile Content -->
    <div class="mobile-content">
        <!-- Welcome Section -->
        <div class="mb-4">
            <h1 class="h3 mb-2">Welcome back!</h1>
            <p class="text-muted mb-0">Manage your digital boards from anywhere</p>
        </div>

        <!-- Dashboard Stats -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalBoards">-</div>
                <div class="stat-label">Active Boards</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTemplates">-</div>
                <div class="stat-label">Templates</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSlideshows">-</div>
                <div class="stat-label">Slideshows</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMedia">-</div>
                <div class="stat-label">Media Files</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="templates.php" class="action-card">
                <i class="fas fa-layer-group action-icon"></i>
                <span class="action-label">Templates</span>
            </a>
            <a href="slideshow-builder.php" class="action-card">
                <i class="fas fa-play-circle action-icon"></i>
                <span class="action-label">Slideshows</span>
            </a>
            <a href="media-manager.php" class="action-card">
                <i class="fas fa-images action-icon"></i>
                <span class="action-label">Media</span>
            </a>
            <a href="user-management.php" class="action-card">
                <i class="fas fa-users action-icon"></i>
                <span class="action-label">Users</span>
            </a>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <div class="activity-header">
                <i class="fas fa-clock"></i>
                Recent Activity
            </div>
            <div id="recentActivity">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Template created</div>
                        <div class="activity-time">2 hours ago</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Slideshow updated</div>
                        <div class="activity-time">4 hours ago</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Media uploaded</div>
                        <div class="activity-time">1 day ago</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="mobile-bottom-nav">
        <a href="mobile-dashboard.php" class="mobile-nav-item active">
            <i class="fas fa-tachometer-alt mobile-nav-icon"></i>
            <span class="mobile-nav-text">Dashboard</span>
        </a>
        <a href="templates.php" class="mobile-nav-item">
            <i class="fas fa-layer-group mobile-nav-icon"></i>
            <span class="mobile-nav-text">Templates</span>
        </a>
        <a href="slideshow-builder.php" class="mobile-nav-item">
            <i class="fas fa-play-circle mobile-nav-icon"></i>
            <span class="mobile-nav-text">Slideshows</span>
        </a>
        <a href="media-manager.php" class="mobile-nav-item">
            <i class="fas fa-images mobile-nav-icon"></i>
            <span class="mobile-nav-text">Media</span>
        </a>
        <a href="user-management.php" class="mobile-nav-item">
            <i class="fas fa-users mobile-nav-icon"></i>
            <span class="mobile-nav-text">Users</span>
        </a>
    </nav>

    <!-- Floating Action Button -->
    <button class="mobile-fab" onclick="showQuickActions()">
        <i class="fas fa-plus"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/digital-board-mobile.js"></script>
    
    <script>
        // Dashboard-specific functionality
        class MobileDashboard {
            constructor() {
                this.init();
            }
            
            init() {
                this.loadDashboardData();
                this.setupPWA();
                this.setupOfflineDetection();
                this.setupPullToRefresh();
            }
            
            async loadDashboardData() {
                try {
                    // Load dashboard statistics
                    const stats = await this.fetchDashboardStats();
                    this.updateStats(stats);
                    
                    // Load recent activity
                    const activity = await this.fetchRecentActivity();
                    this.updateActivity(activity);
                } catch (error) {
                    console.error('Failed to load dashboard data:', error);
                    this.showOfflineData();
                }
            }
            
            async fetchDashboardStats() {
                // Simulate API call - replace with actual API
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve({
                            boards: 5,
                            templates: 12,
                            slideshows: 8,
                            media: 45
                        });
                    }, 1000);
                });
            }
            
            async fetchRecentActivity() {
                // Simulate API call - replace with actual API
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve([
                            { type: 'template', title: 'Template created', time: '2 hours ago' },
                            { type: 'slideshow', title: 'Slideshow updated', time: '4 hours ago' },
                            { type: 'media', title: 'Media uploaded', time: '1 day ago' }
                        ]);
                    }, 800);
                });
            }
            
            updateStats(stats) {
                document.getElementById('totalBoards').textContent = stats.boards;
                document.getElementById('totalTemplates').textContent = stats.templates;
                document.getElementById('totalSlideshows').textContent = stats.slideshows;
                document.getElementById('totalMedia').textContent = stats.media;
            }
            
            updateActivity(activities) {
                const container = document.getElementById('recentActivity');
                container.innerHTML = activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-time">${activity.time}</div>
                        </div>
                    </div>
                `).join('');
            }
            
            getActivityIcon(type) {
                const icons = {
                    template: 'layer-group',
                    slideshow: 'play-circle',
                    media: 'images',
                    user: 'users'
                };
                return icons[type] || 'circle';
            }
            
            showOfflineData() {
                // Show cached data or placeholder
                this.updateStats({ boards: '-', templates: '-', slideshows: '-', media: '-' });
            }
            
            setupPWA() {
                // PWA installation
                let deferredPrompt;
                
                window.addEventListener('beforeinstallprompt', (e) => {
                    e.preventDefault();
                    deferredPrompt = e;
                    document.getElementById('pwaInstallBanner').classList.add('show');
                });
                
                window.installPWA = async () => {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        const { outcome } = await deferredPrompt.userChoice;
                        console.log('PWA install outcome:', outcome);
                        deferredPrompt = null;
                        document.getElementById('pwaInstallBanner').classList.remove('show');
                    }
                };
                
                // Register service worker
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('./sw.js')
                        .then(registration => {
                            console.log('Service Worker registered:', registration);
                        })
                        .catch(error => {
                            console.error('Service Worker registration failed:', error);
                        });
                }
            }
            
            setupOfflineDetection() {
                const offlineIndicator = document.getElementById('offlineIndicator');
                
                window.addEventListener('online', () => {
                    offlineIndicator.classList.remove('show');
                    this.loadDashboardData(); // Refresh data when back online
                });
                
                window.addEventListener('offline', () => {
                    offlineIndicator.classList.add('show');
                });
                
                // Check initial state
                if (!navigator.onLine) {
                    offlineIndicator.classList.add('show');
                }
            }
            
            setupPullToRefresh() {
                window.addEventListener('mobilePullToRefresh', () => {
                    this.loadDashboardData();
                });
            }
        }
        
        // Quick actions menu
        function showQuickActions() {
            const actions = [
                { icon: 'plus', label: 'New Template', url: 'template-builder.php' },
                { icon: 'play', label: 'New Slideshow', url: 'slideshow-builder.php' },
                { icon: 'upload', label: 'Upload Media', url: 'media-manager.php' },
                { icon: 'user-plus', label: 'Add User', url: 'user-management.php' }
            ];
            
            // Show action sheet (simplified implementation)
            const actionSheet = actions.map(action => 
                `<a href="${action.url}" class="mobile-btn mobile-btn-outline mobile-btn-full mb-2">
                    <i class="fas fa-${action.icon}"></i> ${action.label}
                </a>`
            ).join('');
            
            // You would implement a proper action sheet modal here
            console.log('Quick actions:', actions);
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            window.mobileDashboard = new MobileDashboard();
        });
    </script>
</body>
</html>
