/**
 * Service Worker for Digital Board PWA
 * Phase 6 - Mobile & Responsive Optimization
 * 
 * Provides offline functionality and caching for digital board system
 */

const CACHE_NAME = 'digital-board-v6.0.0';
const STATIC_CACHE = 'digital-board-static-v6.0.0';
const DYNAMIC_CACHE = 'digital-board-dynamic-v6.0.0';
const API_CACHE = 'digital-board-api-v6.0.0';

// Files to cache immediately
const STATIC_ASSETS = [
    './',
    './index.php',
    './templates.php',
    './slideshow-builder.php',
    './media-manager.php',
    './user-management.php',
    '../../assets/css/digital-board-mobile.css',
    '../../assets/css/mobile.css',
    '../../assets/css/pwa.css',
    '../../assets/js/digital-board-mobile.js',
    '../../assets/js/mobile.js',
    '../../assets/images/beersty-logo.png',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// API endpoints to cache
const API_ENDPOINTS = [
    '../../api/templates.php',
    '../../api/slideshows.php',
    '../../api/media.php',
    '../../api/users.php'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static assets
            caches.open(STATIC_CACHE).then((cache) => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            }),
            
            // Skip waiting to activate immediately
            self.skipWaiting()
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== API_CACHE) {
                            console.log('Service Worker: Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            
            // Take control of all clients
            self.clients.claim()
        ])
    );
});

// Fetch event - handle requests with caching strategies
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(url)) {
        event.respondWith(cacheFirst(request, STATIC_CACHE));
    } else if (isAPIRequest(url)) {
        event.respondWith(networkFirst(request, API_CACHE));
    } else if (isPageRequest(url)) {
        event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
    } else if (isMediaRequest(url)) {
        event.respondWith(cacheFirst(request, DYNAMIC_CACHE));
    } else {
        event.respondWith(networkFirst(request, DYNAMIC_CACHE));
    }
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync-media-upload') {
        event.waitUntil(syncMediaUploads());
    } else if (event.tag === 'background-sync-user-actions') {
        event.waitUntil(syncUserActions());
    }
});

// Push notifications
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'New update available',
        icon: '../../assets/images/icons/icon-192x192.png',
        badge: '../../assets/images/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Update',
                icon: '../../assets/images/icons/action-view.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '../../assets/images/icons/action-close.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Digital Board Update', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('./')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    } else if (event.data && event.data.type === 'CACHE_MEDIA') {
        event.waitUntil(cacheMedia(event.data.url));
    } else if (event.data && event.data.type === 'CLEAR_CACHE') {
        event.waitUntil(clearCache(event.data.cacheName));
    }
});

// Caching strategies
async function cacheFirst(request, cacheName) {
    try {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        return getOfflineFallback(request);
    }
}

async function networkFirst(request, cacheName) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(cacheName);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return getOfflineFallback(request);
    }
}

async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => {
        return cachedResponse || getOfflineFallback(request);
    });
    
    return cachedResponse || fetchPromise;
}

// Helper functions
function isStaticAsset(url) {
    return url.pathname.includes('/assets/') ||
           url.pathname.endsWith('.css') ||
           url.pathname.endsWith('.js') ||
           url.pathname.endsWith('.png') ||
           url.pathname.endsWith('.jpg') ||
           url.pathname.endsWith('.jpeg') ||
           url.pathname.endsWith('.gif') ||
           url.pathname.endsWith('.webp') ||
           url.hostname.includes('cdn.jsdelivr.net') ||
           url.hostname.includes('cdnjs.cloudflare.com');
}

function isAPIRequest(url) {
    return url.pathname.includes('/api/') ||
           API_ENDPOINTS.some(endpoint => url.pathname.includes(endpoint));
}

function isPageRequest(url) {
    return url.pathname.endsWith('.php') ||
           url.pathname.endsWith('/') ||
           !url.pathname.includes('.');
}

function isMediaRequest(url) {
    return url.pathname.includes('/uploads/') ||
           url.pathname.includes('/media/');
}

function getOfflineFallback(request) {
    const url = new URL(request.url);
    
    if (isPageRequest(url)) {
        return caches.match('./offline.html') || 
               new Response('Offline - Please check your connection', {
                   status: 503,
                   statusText: 'Service Unavailable'
               });
    }
    
    if (isAPIRequest(url)) {
        return new Response(JSON.stringify({
            success: false,
            error: 'Offline - Data not available',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    return new Response('Resource not available offline', {
        status: 503,
        statusText: 'Service Unavailable'
    });
}

// Background sync functions
async function syncMediaUploads() {
    try {
        const pendingUploads = await getStoredData('pendingMediaUploads');
        
        for (const upload of pendingUploads) {
            try {
                const response = await fetch('../../api/media.php', {
                    method: 'POST',
                    body: upload.formData
                });
                
                if (response.ok) {
                    await removeStoredData('pendingMediaUploads', upload.id);
                    console.log('Background sync: Media upload completed');
                }
            } catch (error) {
                console.error('Background sync: Media upload failed:', error);
            }
        }
    } catch (error) {
        console.error('Background sync: Failed to sync media uploads:', error);
    }
}

async function syncUserActions() {
    try {
        const pendingActions = await getStoredData('pendingUserActions');
        
        for (const action of pendingActions) {
            try {
                const response = await fetch(action.url, {
                    method: action.method,
                    headers: action.headers,
                    body: action.body
                });
                
                if (response.ok) {
                    await removeStoredData('pendingUserActions', action.id);
                    console.log('Background sync: User action completed');
                }
            } catch (error) {
                console.error('Background sync: User action failed:', error);
            }
        }
    } catch (error) {
        console.error('Background sync: Failed to sync user actions:', error);
    }
}

// IndexedDB helpers
async function getStoredData(storeName) {
    // Simplified implementation - in production use proper IndexedDB
    return [];
}

async function removeStoredData(storeName, id) {
    // Simplified implementation - in production use proper IndexedDB
    return true;
}

async function cacheMedia(url) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        const response = await fetch(url);
        
        if (response.ok) {
            await cache.put(url, response);
            console.log('Service Worker: Media cached:', url);
        }
    } catch (error) {
        console.error('Service Worker: Failed to cache media:', error);
    }
}

async function clearCache(cacheName) {
    try {
        const deleted = await caches.delete(cacheName);
        console.log('Service Worker: Cache cleared:', cacheName, deleted);
        return deleted;
    } catch (error) {
        console.error('Service Worker: Failed to clear cache:', error);
        return false;
    }
}
