<?php
/**
 * Digital Board Slideshow Player
 * Fullscreen slideshow presentation with transitions
 */

$slideshowId = $_GET['slideshow_id'] ?? 'demo-slideshow-1';

// Demo slideshow data (in production, this would come from database)
$slideshow = [
    'id' => 'demo-slideshow-1',
    'name' => 'Demo Brewery Slideshow',
    'is_active' => true,
    'loop' => true,
    'auto_advance' => true,
    'global_transition' => 'fade',
    'slides' => [
        [
            'id' => 'slide-1',
            'type' => 'beer_board',
            'title' => 'Current Beer Menu',
            'template' => 'beersty-professional',
            'duration' => 15,
            'transition' => 'fade',
            'order' => 1
        ],
        [
            'id' => 'slide-2',
            'type' => 'image',
            'title' => 'Happy Hour Special',
            'content' => 'Demo promotional content',
            'duration' => 8,
            'transition' => 'slide_left',
            'order' => 2
        ],
        [
            'id' => 'slide-3',
            'type' => 'video',
            'title' => 'Brewery Tour',
            'content' => 'Demo video content',
            'duration' => 20,
            'transition' => 'fade',
            'order' => 3
        ],
        [
            'id' => 'slide-4',
            'type' => 'custom_html',
            'title' => 'Events Calendar',
            'content' => 'Demo events content',
            'duration' => 12,
            'transition' => 'slide_up',
            'order' => 4
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($slideshow['name']); ?> - Slideshow Player</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: #000;
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
        }
        
        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: all 1s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .slide.active {
            opacity: 1;
            z-index: 10;
        }
        
        .slide.next {
            z-index: 5;
        }
        
        /* Transition Effects */
        .slide.fade-in { opacity: 1; }
        .slide.fade-out { opacity: 0; }
        
        .slide.slide-left-in { transform: translateX(0); }
        .slide.slide-left-out { transform: translateX(-100%); }
        .slide.slide-left-next { transform: translateX(100%); }
        
        .slide.slide-right-in { transform: translateX(0); }
        .slide.slide-right-out { transform: translateX(100%); }
        .slide.slide-right-next { transform: translateX(-100%); }
        
        .slide.slide-up-in { transform: translateY(0); }
        .slide.slide-up-out { transform: translateY(-100%); }
        .slide.slide-up-next { transform: translateY(100%); }
        
        .slide.slide-down-in { transform: translateY(0); }
        .slide.slide-down-out { transform: translateY(100%); }
        .slide.slide-down-next { transform: translateY(-100%); }
        
        .slide.zoom-in { transform: scale(1); }
        .slide.zoom-out { transform: scale(0.8); }
        .slide.zoom-next { transform: scale(1.2); }
        
        /* Slide Content Styles */
        .beer-board-slide {
            width: 100%;
            height: 100%;
        }
        
        .beer-board-slide iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .image-slide {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }
        
        .image-slide .overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 2rem;
            text-align: center;
        }
        
        .image-slide .overlay h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .video-slide {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .video-slide video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .custom-slide {
            width: 100%;
            height: 100%;
            padding: 2rem;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        .demo-content {
            text-align: center;
            padding: 2rem;
        }
        
        .demo-content h2 {
            font-size: 4rem;
            margin-bottom: 2rem;
            color: #ffc107;
        }
        
        .demo-content p {
            font-size: 1.5rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        /* Controls */
        .slideshow-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 1rem;
            border-radius: 25px;
        }
        
        .control-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        
        .control-btn.active {
            background: #ffc107;
            color: #000;
        }
        
        /* Progress Bar */
        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: rgba(255,255,255,0.2);
            z-index: 1000;
        }
        
        .progress-bar {
            height: 100%;
            background: #ffc107;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        /* Slide Indicator */
        .slide-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            padding: 1rem;
            border-radius: 10px;
            z-index: 1000;
        }
        
        .slide-dots {
            display: flex;
            gap: 10px;
            margin-top: 0.5rem;
        }
        
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dot.active {
            background: #ffc107;
            transform: scale(1.2);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .demo-content h2 { font-size: 2.5rem; }
            .demo-content p { font-size: 1.2rem; }
            .slideshow-controls { bottom: 10px; padding: 0.5rem; }
            .control-btn { padding: 0.5rem 0.75rem; }
        }
    </style>
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <!-- Slide Indicator -->
    <div class="slide-indicator">
        <div class="small">
            <span id="currentSlideNum">1</span> / <span id="totalSlides"><?php echo count($slideshow['slides']); ?></span>
        </div>
        <div class="slide-dots" id="slideDots">
            <?php foreach ($slideshow['slides'] as $index => $slide): ?>
                <div class="dot <?php echo $index === 0 ? 'active' : ''; ?>" onclick="goToSlide(<?php echo $index; ?>)"></div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Slideshow Container -->
    <div class="slideshow-container" id="slideshowContainer">
        <?php foreach ($slideshow['slides'] as $index => $slide): ?>
            <div class="slide <?php echo $index === 0 ? 'active' : ''; ?>" 
                 data-slide-id="<?php echo $slide['id']; ?>"
                 data-duration="<?php echo $slide['duration']; ?>"
                 data-transition="<?php echo $slide['transition']; ?>">
                
                <?php if ($slide['type'] === 'beer_board'): ?>
                    <div class="beer-board-slide">
                        <iframe src="display-<?php echo $slide['template'] ?? 'beersty-professional'; ?>.php"></iframe>
                    </div>
                
                <?php elseif ($slide['type'] === 'image'): ?>
                    <div class="image-slide" style="background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="overlay">
                            <h2><?php echo htmlspecialchars($slide['title']); ?></h2>
                            <p>Demo promotional content - Happy Hour 4-6 PM Daily!</p>
                        </div>
                    </div>
                
                <?php elseif ($slide['type'] === 'video'): ?>
                    <div class="video-slide">
                        <div class="demo-content">
                            <i class="fas fa-video fa-5x mb-4" style="color: #ffc107;"></i>
                            <h2><?php echo htmlspecialchars($slide['title']); ?></h2>
                            <p>Video content would play here - Brewery tour, promotional videos, or custom content</p>
                        </div>
                    </div>
                
                <?php elseif ($slide['type'] === 'custom_html'): ?>
                    <div class="custom-slide">
                        <div class="demo-content">
                            <i class="fas fa-calendar fa-5x mb-4" style="color: #ffc107;"></i>
                            <h2><?php echo htmlspecialchars($slide['title']); ?></h2>
                            <p>Custom HTML content - Events calendar, special announcements, or any custom design</p>
                        </div>
                    </div>
                
                <?php else: ?>
                    <div class="demo-content">
                        <i class="fas fa-file fa-5x mb-4" style="color: #ffc107;"></i>
                        <h2><?php echo htmlspecialchars($slide['title']); ?></h2>
                        <p><?php echo htmlspecialchars($slide['content'] ?? 'Demo slide content'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Controls -->
    <div class="slideshow-controls">
        <button class="control-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="control-btn active" id="playPauseBtn" onclick="togglePlayPause()">
            <i class="fas fa-pause"></i>
        </button>
        <button class="control-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
        <button class="control-btn" onclick="toggleFullscreen()">
            <i class="fas fa-expand"></i>
        </button>
        <button class="control-btn" onclick="window.close()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <script>
        const slideshow = <?php echo json_encode($slideshow); ?>;
        let currentSlideIndex = 0;
        let isPlaying = true;
        let slideTimer = null;
        let progressTimer = null;
        let startTime = null;
        
        // Initialize slideshow
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Slideshow Player initialized');
            console.log('Slideshow:', slideshow.name);
            console.log('Total slides:', slideshow.slides.length);
            
            // Auto-enter fullscreen
            setTimeout(() => {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.log('Fullscreen not available:', err);
                    });
                }
            }, 1000);
            
            startSlideshow();
        });
        
        function startSlideshow() {
            if (slideshow.auto_advance) {
                playSlide(currentSlideIndex);
            }
        }
        
        function playSlide(index) {
            if (index >= slideshow.slides.length) {
                if (slideshow.loop) {
                    index = 0;
                } else {
                    isPlaying = false;
                    updatePlayPauseButton();
                    return;
                }
            }
            
            const slide = slideshow.slides[index];
            const duration = slide.duration * 1000; // Convert to milliseconds
            
            // Update UI
            showSlide(index);
            updateIndicators(index);
            
            // Start progress bar
            startProgress(duration);
            
            // Set timer for next slide
            if (isPlaying && slideshow.auto_advance) {
                slideTimer = setTimeout(() => {
                    currentSlideIndex++;
                    playSlide(currentSlideIndex);
                }, duration);
            }
        }
        
        function showSlide(index) {
            const slides = document.querySelectorAll('.slide');
            const currentSlide = slideshow.slides[index];
            
            // Remove active class from all slides
            slides.forEach(slide => {
                slide.classList.remove('active');
            });
            
            // Add active class to current slide
            slides[index].classList.add('active');
            
            // Apply transition effect
            applyTransition(slides[index], currentSlide.transition);
        }
        
        function applyTransition(slideElement, transition) {
            // Remove any existing transition classes
            slideElement.classList.remove(
                'fade-in', 'fade-out',
                'slide-left-in', 'slide-left-out', 'slide-left-next',
                'slide-right-in', 'slide-right-out', 'slide-right-next',
                'slide-up-in', 'slide-up-out', 'slide-up-next',
                'slide-down-in', 'slide-down-out', 'slide-down-next',
                'zoom-in', 'zoom-out', 'zoom-next'
            );
            
            // Apply transition
            setTimeout(() => {
                switch(transition) {
                    case 'fade':
                        slideElement.classList.add('fade-in');
                        break;
                    case 'slide_left':
                        slideElement.classList.add('slide-left-in');
                        break;
                    case 'slide_right':
                        slideElement.classList.add('slide-right-in');
                        break;
                    case 'slide_up':
                        slideElement.classList.add('slide-up-in');
                        break;
                    case 'slide_down':
                        slideElement.classList.add('slide-down-in');
                        break;
                    case 'zoom_in':
                        slideElement.classList.add('zoom-in');
                        break;
                    default:
                        slideElement.classList.add('fade-in');
                }
            }, 50);
        }
        
        function updateIndicators(index) {
            // Update slide number
            document.getElementById('currentSlideNum').textContent = index + 1;
            
            // Update dots
            const dots = document.querySelectorAll('.dot');
            dots.forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
        }
        
        function startProgress(duration) {
            const progressBar = document.getElementById('progressBar');
            startTime = Date.now();
            
            function updateProgress() {
                if (!isPlaying) return;
                
                const elapsed = Date.now() - startTime;
                const progress = Math.min((elapsed / duration) * 100, 100);
                progressBar.style.width = progress + '%';
                
                if (progress < 100) {
                    progressTimer = requestAnimationFrame(updateProgress);
                } else {
                    progressBar.style.width = '0%';
                }
            }
            
            updateProgress();
        }
        
        function togglePlayPause() {
            isPlaying = !isPlaying;
            
            if (isPlaying) {
                playSlide(currentSlideIndex);
            } else {
                clearTimeout(slideTimer);
                cancelAnimationFrame(progressTimer);
            }
            
            updatePlayPauseButton();
        }
        
        function updatePlayPauseButton() {
            const btn = document.getElementById('playPauseBtn');
            const icon = btn.querySelector('i');
            
            if (isPlaying) {
                icon.className = 'fas fa-pause';
                btn.classList.add('active');
            } else {
                icon.className = 'fas fa-play';
                btn.classList.remove('active');
            }
        }
        
        function nextSlide() {
            clearTimeout(slideTimer);
            cancelAnimationFrame(progressTimer);
            
            currentSlideIndex++;
            if (currentSlideIndex >= slideshow.slides.length) {
                currentSlideIndex = slideshow.loop ? 0 : slideshow.slides.length - 1;
            }
            
            if (isPlaying) {
                playSlide(currentSlideIndex);
            } else {
                showSlide(currentSlideIndex);
                updateIndicators(currentSlideIndex);
            }
        }
        
        function previousSlide() {
            clearTimeout(slideTimer);
            cancelAnimationFrame(progressTimer);
            
            currentSlideIndex--;
            if (currentSlideIndex < 0) {
                currentSlideIndex = slideshow.loop ? slideshow.slides.length - 1 : 0;
            }
            
            if (isPlaying) {
                playSlide(currentSlideIndex);
            } else {
                showSlide(currentSlideIndex);
                updateIndicators(currentSlideIndex);
            }
        }
        
        function goToSlide(index) {
            clearTimeout(slideTimer);
            cancelAnimationFrame(progressTimer);
            
            currentSlideIndex = index;
            
            if (isPlaying) {
                playSlide(currentSlideIndex);
            } else {
                showSlide(currentSlideIndex);
                updateIndicators(currentSlideIndex);
            }
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Keyboard controls
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowLeft':
                    previousSlide();
                    break;
                case 'ArrowRight':
                case ' ':
                    nextSlide();
                    break;
                case 'p':
                case 'P':
                    togglePlayPause();
                    break;
                case 'f':
                case 'F':
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    } else {
                        window.close();
                    }
                    break;
            }
        });
        
        // Hide controls after inactivity
        let controlsTimeout;
        function showControls() {
            document.querySelector('.slideshow-controls').style.opacity = '1';
            document.querySelector('.slide-indicator').style.opacity = '1';
            document.querySelector('.progress-container').style.opacity = '1';
            
            clearTimeout(controlsTimeout);
            controlsTimeout = setTimeout(() => {
                document.querySelector('.slideshow-controls').style.opacity = '0';
                document.querySelector('.slide-indicator').style.opacity = '0';
                document.querySelector('.progress-container').style.opacity = '0';
            }, 3000);
        }
        
        document.addEventListener('mousemove', showControls);
        document.addEventListener('click', showControls);
        
        // Initial controls visibility
        showControls();
    </script>
</body>
</html>
