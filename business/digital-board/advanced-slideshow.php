<?php
/**
 * Advanced Slideshow Features
 * Phase 7 - Advanced Features
 * 
 * Advanced slideshow management with scheduling, conditional display, and interactivity
 */

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/SocialMediaIntegration.php';
require_once '../../includes/ExternalAPIManager.php';

// Check authentication
$user = getCurrentUser();
if (!$user) {
    header('Location: ../../auth/enhanced-login.php');
    exit;
}

// Initialize services
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    $socialMedia = new SocialMediaIntegration($pdo);
    $apiManager = new ExternalAPIManager($pdo);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

$slideshowId = $_GET['slideshow_id'] ?? null;
$action = $_GET['action'] ?? 'edit';

$pageTitle = 'Advanced Slideshow Features - Digital Board System';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/admin.css" rel="stylesheet">
    <link href="../../assets/css/digital-board-mobile.css" rel="stylesheet">
    
    <!-- Date/Time Picker -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    
    <style>
        .feature-card {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
        }
        
        .feature-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .feature-title {
            color: #ffc107;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .feature-description {
            color: #f5f5dc;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .schedule-timeline {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(214, 154, 107, 0.2);
        }
        
        .timeline-item:last-child {
            border-bottom: none;
        }
        
        .timeline-time {
            min-width: 100px;
            color: #ffc107;
            font-weight: 600;
        }
        
        .timeline-content {
            flex: 1;
            margin-left: 20px;
            color: #f5f5dc;
        }
        
        .timeline-actions {
            display: flex;
            gap: 8px;
        }
        
        .condition-builder {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .condition-group {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .condition-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .condition-row:last-child {
            margin-bottom: 0;
        }
        
        .interactive-elements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        
        .interactive-element {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .interactive-element:hover {
            background: #6f4c3e;
            transform: scale(1.05);
        }
        
        .interactive-element.selected {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
        
        .element-icon {
            font-size: 2rem;
            color: #ffc107;
            margin-bottom: 12px;
        }
        
        .element-title {
            color: #f5f5dc;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .element-description {
            color: #f5f5dc;
            font-size: 0.85rem;
            opacity: 0.8;
        }
        
        .content-source-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        
        .content-source {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .source-icon {
            font-size: 3rem;
            color: #ffc107;
            margin-bottom: 16px;
        }
        
        .source-title {
            color: #f5f5dc;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .source-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .source-status.connected {
            background: #28a745;
            color: white;
        }
        
        .source-status.disconnected {
            background: #dc3545;
            color: white;
        }
        
        .preview-container {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            position: relative;
        }
        
        .preview-screen {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #2c1810, #6f4c3e);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #f5f5dc;
            font-size: 1.2rem;
        }
        
        .automation-rules {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .rule-item {
            background: #2c1810;
            border: 1px solid #d69a6b;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .rule-content {
            flex: 1;
        }
        
        .rule-title {
            color: #ffc107;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .rule-description {
            color: #f5f5dc;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .rule-toggle {
            margin-left: 16px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../index.php">
                <img src="../../assets/images/beersty-logo.png" alt="Beersty" height="40">
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i> <?= htmlspecialchars($user['name'] ?? $user['email']) ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../../auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-magic"></i>
                    Advanced Slideshow Features
                </h1>
                <p class="text-muted mb-0">Create dynamic, interactive, and automated slideshows</p>
            </div>
            
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-success" onclick="saveAdvancedSettings()">
                    <i class="fas fa-save"></i> Save Settings
                </button>
                <button type="button" class="btn btn-primary" onclick="previewSlideshow()">
                    <i class="fas fa-eye"></i> Preview
                </button>
                <a href="slideshow-builder.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Builder
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Left Column - Features -->
            <div class="col-md-8">
                <!-- Scheduling Feature -->
                <div class="feature-card">
                    <div class="feature-header">
                        <div>
                            <h5 class="feature-title">
                                <i class="fas fa-calendar-alt"></i>
                                Smart Scheduling
                            </h5>
                            <p class="feature-description">Schedule slides to display at specific times and dates</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableScheduling" checked>
                        </div>
                    </div>
                    
                    <div class="schedule-timeline">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Today's Schedule</h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="addScheduleItem()">
                                <i class="fas fa-plus"></i> Add Schedule
                            </button>
                        </div>
                        
                        <div id="scheduleTimeline">
                            <div class="timeline-item">
                                <div class="timeline-time">9:00 AM</div>
                                <div class="timeline-content">
                                    <strong>Morning Specials</strong><br>
                                    <small>Breakfast menu and coffee promotions</small>
                                </div>
                                <div class="timeline-actions">
                                    <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-time">5:00 PM</div>
                                <div class="timeline-content">
                                    <strong>Happy Hour</strong><br>
                                    <small>Drink specials and appetizers</small>
                                </div>
                                <div class="timeline-actions">
                                    <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conditional Display -->
                <div class="feature-card">
                    <div class="feature-header">
                        <div>
                            <h5 class="feature-title">
                                <i class="fas fa-code-branch"></i>
                                Conditional Display
                            </h5>
                            <p class="feature-description">Show slides based on weather, events, inventory, or custom conditions</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableConditional" checked>
                        </div>
                    </div>
                    
                    <div class="condition-builder">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Display Rules</h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="addCondition()">
                                <i class="fas fa-plus"></i> Add Rule
                            </button>
                        </div>
                        
                        <div class="condition-group">
                            <div class="condition-row">
                                <select class="form-select" style="max-width: 150px;">
                                    <option>Weather</option>
                                    <option>Time</option>
                                    <option>Inventory</option>
                                    <option>Events</option>
                                </select>
                                <select class="form-select" style="max-width: 120px;">
                                    <option>is</option>
                                    <option>is not</option>
                                    <option>greater than</option>
                                    <option>less than</option>
                                </select>
                                <input type="text" class="form-control" placeholder="Value" style="max-width: 150px;">
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="condition-row">
                                <span class="badge bg-secondary">AND</span>
                                <select class="form-select" style="max-width: 150px;">
                                    <option>Temperature</option>
                                    <option>Day of Week</option>
                                    <option>Stock Level</option>
                                </select>
                                <select class="form-select" style="max-width: 120px;">
                                    <option>greater than</option>
                                    <option>less than</option>
                                    <option>equals</option>
                                </select>
                                <input type="text" class="form-control" placeholder="70°F" style="max-width: 150px;">
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Interactive Elements -->
                <div class="feature-card">
                    <div class="feature-header">
                        <div>
                            <h5 class="feature-title">
                                <i class="fas fa-hand-pointer"></i>
                                Interactive Elements
                            </h5>
                            <p class="feature-description">Add touch interactions and customer engagement features</p>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableInteractive">
                        </div>
                    </div>
                    
                    <div class="interactive-elements">
                        <div class="interactive-element" onclick="toggleElement(this)">
                            <div class="element-icon"><i class="fas fa-qrcode"></i></div>
                            <div class="element-title">QR Codes</div>
                            <div class="element-description">Link to menus, websites, or special offers</div>
                        </div>
                        <div class="interactive-element" onclick="toggleElement(this)">
                            <div class="element-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="element-title">Rating System</div>
                            <div class="element-description">Collect customer feedback on beers and food</div>
                        </div>
                        <div class="interactive-element" onclick="toggleElement(this)">
                            <div class="element-icon"><i class="fas fa-poll"></i></div>
                            <div class="element-title">Polls & Surveys</div>
                            <div class="element-description">Engage customers with interactive polls</div>
                        </div>
                        <div class="interactive-element" onclick="toggleElement(this)">
                            <div class="element-icon"><i class="fas fa-share-alt"></i></div>
                            <div class="element-title">Social Sharing</div>
                            <div class="element-description">Encourage social media check-ins</div>
                        </div>
                    </div>
                </div>

                <!-- Dynamic Content Sources -->
                <div class="feature-card">
                    <div class="feature-header">
                        <div>
                            <h5 class="feature-title">
                                <i class="fas fa-sync-alt"></i>
                                Dynamic Content Sources
                            </h5>
                            <p class="feature-description">Automatically update slides with live data from external sources</p>
                        </div>
                    </div>
                    
                    <div class="content-source-grid">
                        <div class="content-source">
                            <div class="source-icon"><i class="fab fa-instagram"></i></div>
                            <div class="source-title">Instagram Feed</div>
                            <div class="source-status connected">Connected</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="configureSource('instagram')">
                                Configure
                            </button>
                        </div>
                        <div class="content-source">
                            <div class="source-icon"><i class="fas fa-cloud-sun"></i></div>
                            <div class="source-title">Weather Data</div>
                            <div class="source-status connected">Connected</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="configureSource('weather')">
                                Configure
                            </button>
                        </div>
                        <div class="content-source">
                            <div class="source-icon"><i class="fas fa-calendar-check"></i></div>
                            <div class="source-title">Local Events</div>
                            <div class="source-status disconnected">Disconnected</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="configureSource('events')">
                                Configure
                            </button>
                        </div>
                        <div class="content-source">
                            <div class="source-icon"><i class="fas fa-cash-register"></i></div>
                            <div class="source-title">POS System</div>
                            <div class="source-status disconnected">Disconnected</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="configureSource('pos')">
                                Configure
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Preview & Automation -->
            <div class="col-md-4">
                <!-- Live Preview -->
                <div class="feature-card">
                    <h5 class="feature-title">
                        <i class="fas fa-tv"></i>
                        Live Preview
                    </h5>
                    <div class="preview-container">
                        <div class="preview-screen">
                            <div class="text-center">
                                <i class="fas fa-play-circle fa-3x mb-3"></i>
                                <p>Preview will appear here</p>
                                <button class="btn btn-primary" onclick="startPreview()">
                                    Start Preview
                                </button>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <small class="text-muted">Preview updates automatically as you make changes</small>
                        </div>
                    </div>
                </div>

                <!-- Automation Rules -->
                <div class="feature-card">
                    <h5 class="feature-title">
                        <i class="fas fa-robot"></i>
                        Automation Rules
                    </h5>
                    <div class="automation-rules">
                        <div class="rule-item">
                            <div class="rule-content">
                                <div class="rule-title">Auto-refresh Social Media</div>
                                <div class="rule-description">Update Instagram feed every 5 minutes</div>
                            </div>
                            <div class="form-check form-switch rule-toggle">
                                <input class="form-check-input" type="checkbox" checked>
                            </div>
                        </div>
                        <div class="rule-item">
                            <div class="rule-content">
                                <div class="rule-title">Weather-based Content</div>
                                <div class="rule-description">Show patio specials when sunny</div>
                            </div>
                            <div class="form-check form-switch rule-toggle">
                                <input class="form-check-input" type="checkbox" checked>
                            </div>
                        </div>
                        <div class="rule-item">
                            <div class="rule-content">
                                <div class="rule-title">Inventory Alerts</div>
                                <div class="rule-description">Hide items when out of stock</div>
                            </div>
                            <div class="form-check form-switch rule-toggle">
                                <input class="form-check-input" type="checkbox">
                            </div>
                        </div>
                    </div>
                    
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="addAutomationRule()">
                        <i class="fas fa-plus"></i> Add Rule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="../../assets/js/digital-board-mobile.js"></script>
    
    <script>
        // Advanced slideshow functionality
        class AdvancedSlideshow {
            constructor() {
                this.settings = {
                    scheduling: true,
                    conditional: true,
                    interactive: false,
                    automation: true
                };
                this.init();
            }
            
            init() {
                this.setupDateTimePickers();
                this.loadSettings();
            }
            
            setupDateTimePickers() {
                flatpickr('.datetime-picker', {
                    enableTime: true,
                    dateFormat: "Y-m-d H:i",
                    time_24hr: false
                });
            }
            
            loadSettings() {
                // Load existing settings from server
                fetch('../../api/slideshow-settings.php?slideshow_id=' + getCurrentSlideshowId())
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.applySettings(data.settings);
                        }
                    })
                    .catch(error => console.error('Load settings error:', error));
            }
            
            applySettings(settings) {
                // Apply loaded settings to UI
                Object.keys(settings).forEach(key => {
                    const checkbox = document.getElementById('enable' + key.charAt(0).toUpperCase() + key.slice(1));
                    if (checkbox) {
                        checkbox.checked = settings[key];
                    }
                });
            }
        }
        
        // Global functions
        function toggleElement(element) {
            element.classList.toggle('selected');
        }
        
        function addScheduleItem() {
            // Implementation for adding schedule items
            console.log('Add schedule item');
        }
        
        function addCondition() {
            // Implementation for adding conditions
            console.log('Add condition');
        }
        
        function configureSource(source) {
            // Implementation for configuring content sources
            console.log('Configure source:', source);
        }
        
        function addAutomationRule() {
            // Implementation for adding automation rules
            console.log('Add automation rule');
        }
        
        function saveAdvancedSettings() {
            // Implementation for saving settings
            console.log('Save advanced settings');
        }
        
        function previewSlideshow() {
            // Implementation for preview
            console.log('Preview slideshow');
        }
        
        function startPreview() {
            // Implementation for starting preview
            console.log('Start preview');
        }
        
        function getCurrentSlideshowId() {
            return new URLSearchParams(window.location.search).get('slideshow_id') || '1';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            window.advancedSlideshow = new AdvancedSlideshow();
        });
    </script>
</body>
</html>
