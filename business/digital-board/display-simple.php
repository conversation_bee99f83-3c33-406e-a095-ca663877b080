<?php
/**
 * Digital Beer Board - Simple Display (No Database Dependencies)
 * Working demo display for testing
 */

// Get parameters
$breweryId = $_GET['brewery_id'] ?? 'demo-brewery-1';
$boardId = $_GET['board_id'] ?? 'demo-board-12345';

// Demo data - works without database
$brewery = [
    'name' => 'Demo Craft Brewery',
    'city' => 'Demo City',
    'state' => 'CA',
    'logo_url' => null
];

$settings = [
    'theme' => 'dark',
    'layout' => 'grid',
    'refresh_interval' => 300,
    'show_prices' => true,
    'show_descriptions' => true,
    'ticker_message' => 'Welcome to Demo Craft Brewery! Try our award-winning beers fresh from the tap!'
];

$beerMenu = [
    [
        'id' => 1,
        'name' => 'Hoppy IPA',
        'description' => 'Our signature India Pale Ale with citrus and pine notes. Brewed with Cascade, Centennial, and Simcoe hops.',
        'style_name' => 'IPA',
        'abv' => 6.8,
        'ibu' => 68,
        'srm' => 8,
        'price' => 8.50,
        'tap_number' => 1,
        'is_available' => 1
    ],
    [
        'id' => 2,
        'name' => 'Golden Wheat',
        'description' => 'Light and refreshing wheat beer perfect for any season. Smooth and creamy with a hint of citrus.',
        'style_name' => 'Wheat Beer',
        'abv' => 4.5,
        'ibu' => 12,
        'srm' => 4,
        'price' => 6.00,
        'tap_number' => 2,
        'is_available' => 1
    ],
    [
        'id' => 3,
        'name' => 'Midnight Stout',
        'description' => 'Rich and creamy stout with notes of chocolate, coffee, and vanilla. A perfect winter warmer.',
        'style_name' => 'Stout',
        'abv' => 7.2,
        'ibu' => 35,
        'srm' => 40,
        'price' => 9.00,
        'tap_number' => 3,
        'is_available' => 1
    ],
    [
        'id' => 4,
        'name' => 'Crisp Lager',
        'description' => 'Our most popular beer - a crisp and clean lager that goes with everything. Light and refreshing.',
        'style_name' => 'Lager',
        'abv' => 4.8,
        'ibu' => 18,
        'srm' => 3,
        'price' => 5.50,
        'tap_number' => 4,
        'is_available' => 1
    ],
    [
        'id' => 5,
        'name' => 'Robust Porter',
        'description' => 'Dark and malty porter with hints of caramel and toffee. Smooth finish with a touch of roasted barley.',
        'style_name' => 'Porter',
        'abv' => 6.2,
        'ibu' => 28,
        'srm' => 32,
        'price' => 7.50,
        'tap_number' => 5,
        'is_available' => 1
    ],
    [
        'id' => 6,
        'name' => 'Czech Pilsner',
        'description' => 'Traditional Czech-style pilsner with noble hops. Crisp, clean, and perfectly balanced.',
        'style_name' => 'Pilsner',
        'abv' => 5.2,
        'ibu' => 35,
        'srm' => 4,
        'price' => 7.00,
        'tap_number' => 6,
        'is_available' => 1
    ],
    [
        'id' => 7,
        'name' => 'Nutty Brown',
        'description' => 'Malty brown ale with notes of nuts and caramel. Medium-bodied with a smooth finish.',
        'style_name' => 'Brown Ale',
        'abv' => 5.8,
        'ibu' => 22,
        'srm' => 18,
        'price' => 6.50,
        'tap_number' => 7,
        'is_available' => 1
    ],
    [
        'id' => 8,
        'name' => 'American Pale',
        'description' => 'Balanced American pale ale with citrus hop character. Perfect introduction to craft beer.',
        'style_name' => 'Pale Ale',
        'abv' => 5.5,
        'ibu' => 42,
        'srm' => 6,
        'price' => 6.75,
        'tap_number' => 8,
        'is_available' => 1
    ],
    [
        'id' => 9,
        'name' => 'Seasonal Special',
        'description' => 'Our rotating seasonal beer - currently a pumpkin spice ale with cinnamon and nutmeg.',
        'style_name' => 'Brown Ale',
        'abv' => 6.0,
        'ibu' => 25,
        'srm' => 12,
        'price' => 8.00,
        'tap_number' => null,
        'is_available' => 1
    ],
    [
        'id' => 10,
        'name' => 'House Blend',
        'description' => 'A unique blend of our porter and IPA - surprisingly delicious and complex.',
        'style_name' => 'Porter',
        'abv' => 6.5,
        'ibu' => 45,
        'srm' => 20,
        'price' => 7.75,
        'tap_number' => null,
        'is_available' => 1
    ]
];

$theme = $settings['theme'];
$layout = $settings['layout'];
$refreshInterval = $settings['refresh_interval'] * 1000;
$showPrices = $settings['show_prices'];
$showDescriptions = $settings['show_descriptions'];
$tickerMessage = $settings['ticker_message'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($brewery['name']); ?> - Digital Beer Board</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
        }
        
        .digital-board {
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }
        
        .board-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 3px solid #ffc107;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .brewery-name {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .brewery-location {
            font-size: 1.5rem;
            opacity: 0.8;
        }
        
        .beer-grid {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }
        
        .grid-layout {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }
        
        .beer-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
            position: relative;
        }
        
        .beer-item:hover {
            transform: translateY(-5px);
        }
        
        .tap-number {
            position: absolute;
            top: -10px;
            left: 20px;
            background: #ffc107;
            color: #000;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .beer-name {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .beer-style {
            color: #ffc107;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .beer-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .beer-specs {
            display: flex;
            gap: 2rem;
        }
        
        .spec-item {
            text-align: center;
        }
        
        .spec-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffc107;
        }
        
        .spec-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .beer-price {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        
        .beer-description {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .ticker {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #ffc107;
            color: #000;
            padding: 1rem;
            font-size: 1.3rem;
            font-weight: bold;
            text-align: center;
            animation: ticker-scroll 30s linear infinite;
        }
        
        @keyframes ticker-scroll {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        .last-updated {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .brewery-name { font-size: 2rem; }
            .brewery-location { font-size: 1.2rem; }
            .beer-name { font-size: 1.4rem; }
            .beer-specs { gap: 1rem; }
            .grid-layout { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="digital-board">
        <!-- Header -->
        <div class="board-header">
            <div class="brewery-name"><?php echo htmlspecialchars($brewery['name']); ?></div>
            <div class="brewery-location">
                <?php echo htmlspecialchars($brewery['city'] . ', ' . $brewery['state']); ?>
            </div>
        </div>
        
        <!-- Beer Menu -->
        <div class="beer-grid">
            <div class="grid-layout">
                <?php foreach ($beerMenu as $beer): ?>
                    <div class="beer-item">
                        <?php if ($beer['tap_number']): ?>
                            <div class="tap-number"><?php echo $beer['tap_number']; ?></div>
                        <?php endif; ?>
                        
                        <div class="beer-content">
                            <div class="beer-name"><?php echo htmlspecialchars($beer['name']); ?></div>
                            
                            <?php if ($beer['style_name']): ?>
                                <div class="beer-style"><?php echo htmlspecialchars($beer['style_name']); ?></div>
                            <?php endif; ?>
                            
                            <div class="beer-details">
                                <div class="beer-specs">
                                    <?php if ($beer['abv']): ?>
                                        <div class="spec-item">
                                            <div class="spec-value"><?php echo $beer['abv']; ?>%</div>
                                            <div class="spec-label">ABV</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($beer['ibu']): ?>
                                        <div class="spec-item">
                                            <div class="spec-value"><?php echo $beer['ibu']; ?></div>
                                            <div class="spec-label">IBU</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($beer['srm']): ?>
                                        <div class="spec-item">
                                            <div class="spec-value"><?php echo $beer['srm']; ?></div>
                                            <div class="spec-label">SRM</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($showPrices && $beer['price']): ?>
                                    <div class="beer-price">$<?php echo number_format($beer['price'], 2); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($showDescriptions && $beer['description']): ?>
                                <div class="beer-description">
                                    <?php echo htmlspecialchars($beer['description']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Ticker Message -->
        <?php if ($tickerMessage): ?>
            <div class="ticker">
                <?php echo htmlspecialchars($tickerMessage); ?>
            </div>
        <?php endif; ?>
        
        <!-- Last Updated -->
        <div class="last-updated">
            <i class="fas fa-clock me-2"></i>
            Updated: <span id="lastUpdated"><?php echo date('g:i A'); ?></span>
        </div>
    </div>
    
    <script>
        function updateTimestamp() {
            const now = new Date();
            document.getElementById('lastUpdated').textContent = now.toLocaleTimeString([], {
                hour: 'numeric',
                minute: '2-digit'
            });
        }
        
        // Update timestamp every minute
        setInterval(updateTimestamp, 60000);
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // F5 or Ctrl+R to refresh
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                window.location.reload();
            }
            
            // F11 to toggle fullscreen
            if (e.key === 'F11') {
                e.preventDefault();
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
        });
        
        console.log('Digital Beer Board Demo loaded successfully!');
    </script>
</body>
</html>
