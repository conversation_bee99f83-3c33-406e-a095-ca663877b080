<?php
/**
 * Fix bindValue() method in UserSharedStatement class
 */

$sharedConfigPath = '/home/<USER>/.beersty-shared/config/user_shared_database.php';

// Read the current file
$content = file_get_contents($sharedConfigPath);

// Replace the UserSharedStatement class with the updated version
$oldClass = 'class UserSharedStatement {
    private $db;
    private $sql;
    private $results;
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function execute($params = []) {
        $this->results = $this->db->executeQuery($this->sql, $params);
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}';

$newClass = 'class UserSharedStatement {
    private $db;
    private $sql;
    private $results;
    private $boundParams = [];
    
    public function __construct($db, $sql) {
        $this->db = $db;
        $this->sql = $sql;
    }
    
    public function bindValue($parameter, $value, $dataType = null) {
        // Store bound parameters for later use in execute()
        if (is_int($parameter)) {
            // Parameter is 1-based index, convert to 0-based
            $this->boundParams[$parameter - 1] = $value;
        } else {
            // Named parameter
            $this->boundParams[$parameter] = $value;
        }
        return true;
    }
    
    public function bindParam($parameter, &$variable, $dataType = null, $length = null, $driverOptions = null) {
        // For compatibility, treat bindParam like bindValue
        return $this->bindValue($parameter, $variable, $dataType);
    }
    
    public function execute($params = []) {
        // Merge bound parameters with execute parameters
        $allParams = array_merge($this->boundParams, $params);
        
        // Sort by key to ensure proper order for positional parameters
        ksort($allParams);
        
        $this->results = $this->db->executeQuery($this->sql, array_values($allParams));
        
        // Reset bound parameters after execution
        $this->boundParams = [];
        
        return true;
    }
    
    public function fetch($fetchStyle = null) {
        if (empty($this->results)) {
            return false;
        }
        return array_shift($this->results);
    }
    
    public function fetchAll($fetchStyle = null) {
        return $this->results ?: [];
    }
    
    public function fetchColumn($columnNumber = 0) {
        if (empty($this->results)) {
            return false;
        }
        $row = $this->results[0];
        $values = array_values($row);
        return $values[$columnNumber] ?? false;
    }
    
    public function rowCount() {
        return count($this->results);
    }
}';

// Replace the class in the content
$updatedContent = str_replace($oldClass, $newClass, $content);

// Write the updated content back to the file
if (file_put_contents($sharedConfigPath, $updatedContent)) {
    echo "<h1>✅ bindValue() Method Added Successfully!</h1>";
    echo "<p>The UserSharedStatement class has been updated with the missing bindValue() method.</p>";
    echo "<p><strong>Updated file:</strong> <code>$sharedConfigPath</code></p>";
    echo "<p><strong>Added methods:</strong></p>";
    echo "<ul>";
    echo "<li><code>bindValue(\$parameter, \$value, \$dataType = null)</code></li>";
    echo "<li><code>bindParam(\$parameter, &\$variable, \$dataType = null, \$length = null, \$driverOptions = null)</code></li>";
    echo "</ul>";
    echo "<p><a href='/admin/breweries.php'>🎯 Test Breweries Admin Page</a></p>";
} else {
    echo "<h1>❌ Update Failed</h1>";
    echo "<p>Failed to update the shared database configuration.</p>";
}
?>
