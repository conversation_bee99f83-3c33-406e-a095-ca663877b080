<?php
/**
 * Mobile Bottom Navigation Component
 * Phase 9: Design & Mobile Optimization
 * Touch-optimized bottom navigation for mobile devices
 */

// Only show for logged-in users with social roles
if (!isLoggedIn() || !in_array(getCurrentUser()['role'], ['beer_enthusiast', 'beer_expert', 'customer'])) {
    return;
}

$currentPath = $_SERVER['REQUEST_URI'];
$user = getCurrentUser();

// Navigation items
$navItems = [
    [
        'icon' => 'fas fa-home',
        'text' => 'Home',
        'url' => '/beersty/',
        'active' => $currentPath === '/beersty/' || $currentPath === '/beersty/index.php'
    ],
    [
        'icon' => 'fas fa-compass',
        'text' => 'Discover',
        'url' => '/beersty/beers/discover.php',
        'active' => strpos($currentPath, '/beers/discover') !== false || strpos($currentPath, '/discover/') !== false
    ],
    [
        'icon' => 'fas fa-plus-circle',
        'text' => 'Check In',
        'url' => '/beersty/social/checkin.php',
        'active' => strpos($currentPath, '/social/checkin') !== false,
        'primary' => true
    ],
    [
        'icon' => 'fas fa-stream',
        'text' => 'Feed',
        'url' => '/beersty/social/feed.php',
        'active' => strpos($currentPath, '/social/feed') !== false
    ],
    [
        'icon' => 'fas fa-user',
        'text' => 'Profile',
        'url' => '/beersty/user/profile.php',
        'active' => strpos($currentPath, '/user/profile') !== false || strpos($currentPath, '/user/statistics') !== false
    ]
];
?>

<!-- Mobile Bottom Navigation (Phase 9) -->
<nav class="mobile-bottom-nav d-md-none">
    <?php foreach ($navItems as $item): ?>
        <div class="nav-item">
            <a href="<?php echo $item['url']; ?>" 
               class="nav-link <?php echo $item['active'] ? 'active' : ''; ?> <?php echo isset($item['primary']) ? 'primary-action' : ''; ?>"
               <?php if (isset($item['primary'])): ?>
                   data-bs-toggle="tooltip" 
                   data-bs-placement="top" 
                   title="Quick Check-in"
               <?php endif; ?>>
                <div class="nav-icon">
                    <i class="<?php echo $item['icon']; ?>"></i>
                </div>
                <div class="nav-text">
                    <?php echo $item['text']; ?>
                </div>
            </a>
        </div>
    <?php endforeach; ?>
</nav>

<style>
/* Mobile Bottom Navigation Styles */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: white;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-bottom-nav .nav-item {
    flex: 1;
    text-align: center;
}

.mobile-bottom-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 12px;
    margin: 0.25rem;
    position: relative;
}

.mobile-bottom-nav .nav-link:hover,
.mobile-bottom-nav .nav-link.active {
    color: #f8b500;
    background-color: rgba(248, 181, 0, 0.1);
    transform: translateY(-2px);
}

.mobile-bottom-nav .nav-link.primary-action {
    background: linear-gradient(135deg, #f8b500 0%, #ffd700 100%);
    color: white;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    margin: 0.25rem auto;
    box-shadow: 0 4px 15px rgba(248, 181, 0, 0.3);
}

.mobile-bottom-nav .nav-link.primary-action:hover {
    background: linear-gradient(135deg, #e6a200 0%, #e6c200 100%);
    transform: translateY(-4px);
    box-shadow: 0 6px 20px rgba(248, 181, 0, 0.4);
}

.mobile-bottom-nav .nav-link.primary-action .nav-text {
    display: none;
}

.mobile-bottom-nav .nav-icon {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.mobile-bottom-nav .nav-link.primary-action .nav-icon {
    font-size: 1.5rem;
    margin-bottom: 0;
}

.mobile-bottom-nav .nav-text {
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
}

/* Add bottom padding to body when bottom nav is present */
body {
    padding-bottom: 85px;
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-bottom-nav {
        height: 60px;
    }
    
    .mobile-bottom-nav .nav-icon {
        font-size: 1.1rem;
    }
    
    .mobile-bottom-nav .nav-text {
        font-size: 0.7rem;
    }
    
    .mobile-bottom-nav .nav-link.primary-action {
        width: 48px;
        height: 48px;
    }
    
    body {
        padding-bottom: 75px;
    }
}

/* Touch feedback */
.mobile-bottom-nav .nav-link:active {
    transform: scale(0.95);
}

.mobile-bottom-nav .nav-link.primary-action:active {
    transform: scale(0.9) translateY(-2px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .mobile-bottom-nav {
        background-color: #2d3748;
        border-top-color: #4a5568;
    }
    
    .mobile-bottom-nav .nav-link {
        color: #a0aec0;
    }
    
    .mobile-bottom-nav .nav-link:hover,
    .mobile-bottom-nav .nav-link.active {
        background-color: rgba(248, 181, 0, 0.2);
    }
}

/* Hide on desktop */
@media (min-width: 769px) {
    .mobile-bottom-nav {
        display: none !important;
    }
    
    body {
        padding-bottom: 0 !important;
    }
}

/* Notification badges for mobile nav */
.mobile-bottom-nav .nav-link .badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
    border-radius: 50px;
    background: #dc3545;
    color: white;
    min-width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Animation for active state */
.mobile-bottom-nav .nav-link.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #f8b500;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: translateX(-50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: translateX(-50%) scale(1);
        opacity: 1;
    }
}

/* Safe area insets for devices with notches */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .mobile-bottom-nav {
        padding-bottom: env(safe-area-inset-bottom);
        height: calc(70px + env(safe-area-inset-bottom));
    }
    
    body {
        padding-bottom: calc(85px + env(safe-area-inset-bottom));
    }
    
    @media (max-width: 768px) and (orientation: landscape) {
        .mobile-bottom-nav {
            height: calc(60px + env(safe-area-inset-bottom));
        }
        
        body {
            padding-bottom: calc(75px + env(safe-area-inset-bottom));
        }
    }
}
</style>

<script>
// Mobile bottom navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const bottomNav = document.querySelector('.mobile-bottom-nav');
    if (!bottomNav) return;
    
    // Add touch feedback
    const navLinks = bottomNav.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        link.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 100);
        });
        
        link.addEventListener('touchcancel', function() {
            this.style.transform = '';
        });
    });
    
    // Handle primary action (check-in) with haptic feedback
    const primaryAction = bottomNav.querySelector('.primary-action');
    if (primaryAction && 'vibrate' in navigator) {
        primaryAction.addEventListener('click', function() {
            navigator.vibrate(50); // Short vibration
        });
    }
    
    // Auto-hide on scroll (optional)
    let lastScrollY = window.scrollY;
    let ticking = false;
    
    function updateBottomNav() {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
            // Scrolling down
            bottomNav.style.transform = 'translateY(100%)';
        } else {
            // Scrolling up
            bottomNav.style.transform = 'translateY(0)';
        }
        
        lastScrollY = currentScrollY;
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateBottomNav);
            ticking = true;
        }
    }, { passive: true });
});
</script>
