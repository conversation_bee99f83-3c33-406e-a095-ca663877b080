<?php
/**
 * Photo Gallery Component
 * Displays a gallery of photos with lightbox functionality
 */

require_once '../includes/PhotoManager.php';

function renderPhotoGallery($type, $targetId, $options = []) {
    $photoManager = new PhotoManager();
    $photos = $photoManager->getPhotos($type, $targetId, $options['limit'] ?? null);
    
    $galleryId = 'gallery-' . uniqid();
    $showUpload = $options['show_upload'] ?? false;
    $canUpload = $options['can_upload'] ?? false;
    $columns = $options['columns'] ?? 3;
    $showTitles = $options['show_titles'] ?? true;
    $showUserInfo = $options['show_user_info'] ?? true;
    
    ob_start();
    ?>
    
    <div class="photo-gallery" id="<?php echo $galleryId; ?>">
        <!-- Gallery Header -->
        <div class="gallery-header d-flex justify-content-between align-items-center mb-3">
            <h5 class="gallery-title mb-0">
                <i class="fas fa-images me-2"></i>
                Photos 
                <?php if (!empty($photos)): ?>
                    <span class="badge bg-primary"><?php echo count($photos); ?></span>
                <?php endif; ?>
            </h5>
            
            <?php if ($showUpload && $canUpload): ?>
                <button class="btn btn-outline-primary btn-sm" onclick="openPhotoUpload('<?php echo $type; ?>', '<?php echo $targetId; ?>')">
                    <i class="fas fa-plus me-1"></i>Add Photos
                </button>
            <?php endif; ?>
        </div>
        
        <!-- Photo Grid -->
        <?php if (empty($photos)): ?>
            <div class="empty-gallery text-center py-5">
                <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">No photos yet</h6>
                <?php if ($showUpload && $canUpload): ?>
                    <p class="text-muted mb-3">Be the first to share a photo!</p>
                    <button class="btn btn-primary" onclick="openPhotoUpload('<?php echo $type; ?>', '<?php echo $targetId; ?>')">
                        <i class="fas fa-camera me-2"></i>Upload Photo
                    </button>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="row g-3 photo-grid" data-columns="<?php echo $columns; ?>">
                <?php foreach ($photos as $index => $photo): ?>
                    <div class="col-lg-<?php echo 12 / $columns; ?> col-md-6">
                        <div class="photo-item" data-photo-id="<?php echo $photo['id']; ?>">
                            <div class="photo-card">
                                <div class="photo-image-container">
                                    <img src="<?php echo htmlspecialchars($photo['thumbnail_path'] ? '/' . $photo['thumbnail_path'] : '/' . $photo['file_path']); ?>" 
                                         alt="<?php echo htmlspecialchars($photo['alt_text'] ?: $photo['title'] ?: 'Photo'); ?>"
                                         class="photo-image"
                                         onclick="openLightbox('<?php echo $galleryId; ?>', <?php echo $index; ?>)"
                                         loading="lazy">
                                    
                                    <!-- Photo Overlay -->
                                    <div class="photo-overlay">
                                        <div class="photo-actions">
                                            <button class="btn btn-sm btn-light" onclick="openLightbox('<?php echo $galleryId; ?>', <?php echo $index; ?>)">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <?php if ($photo['like_count'] > 0): ?>
                                                <span class="photo-likes">
                                                    <i class="fas fa-heart text-danger"></i>
                                                    <?php echo number_format($photo['like_count']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($showTitles && ($photo['title'] || $showUserInfo)): ?>
                                    <div class="photo-info">
                                        <?php if ($photo['title']): ?>
                                            <h6 class="photo-title"><?php echo htmlspecialchars($photo['title']); ?></h6>
                                        <?php endif; ?>
                                        
                                        <?php if ($showUserInfo): ?>
                                            <div class="photo-meta">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php 
                                                    $userName = trim($photo['first_name'] . ' ' . $photo['last_name']);
                                                    echo htmlspecialchars($userName ?: $photo['username'] ?: 'Anonymous');
                                                    ?>
                                                </small>
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo formatDateTime($photo['created_at']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Lightbox Modal -->
    <div class="modal fade" id="lightbox-<?php echo $galleryId; ?>" tabindex="-1">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content bg-dark">
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white" id="lightbox-title"></h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="lightbox-container">
                        <img id="lightbox-image" src="" alt="" class="img-fluid w-100">
                        
                        <!-- Navigation -->
                        <button class="lightbox-nav lightbox-prev" onclick="navigateLightbox('<?php echo $galleryId; ?>', -1)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="lightbox-nav lightbox-next" onclick="navigateLightbox('<?php echo $galleryId; ?>', 1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        
                        <!-- Photo Info Overlay -->
                        <div class="lightbox-info">
                            <div id="lightbox-description"></div>
                            <div id="lightbox-meta" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    // Store photo data for lightbox
    window.galleryData = window.galleryData || {};
    window.galleryData['<?php echo $galleryId; ?>'] = <?php echo json_encode($photos); ?>;
    </script>
    
    <?php
    return ob_get_clean();
}

/**
 * Render a simple photo upload form
 */
function renderPhotoUploadForm($type, $targetId, $options = []) {
    $formId = 'upload-form-' . uniqid();
    $multiple = $options['multiple'] ?? true;
    $maxFiles = $options['max_files'] ?? 10;
    
    ob_start();
    ?>
    
    <div class="photo-upload-form" id="<?php echo $formId; ?>">
        <form class="upload-form" enctype="multipart/form-data">
            <input type="hidden" name="type" value="<?php echo htmlspecialchars($type); ?>">
            <input type="hidden" name="target_id" value="<?php echo htmlspecialchars($targetId); ?>">
            
            <div class="upload-area">
                <div class="upload-dropzone" onclick="document.getElementById('photo-files-<?php echo $formId; ?>').click()">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h6>Drop photos here or click to browse</h6>
                    <p class="text-muted">
                        <?php if ($multiple): ?>
                            Upload up to <?php echo $maxFiles; ?> photos (JPEG, PNG, GIF, WebP)
                        <?php else: ?>
                            Upload a photo (JPEG, PNG, GIF, WebP)
                        <?php endif; ?>
                    </p>
                    <p class="text-muted small">Maximum file size: 5MB per photo</p>
                </div>
                
                <input type="file" 
                       id="photo-files-<?php echo $formId; ?>" 
                       name="photos[]" 
                       accept="image/*" 
                       <?php echo $multiple ? 'multiple' : ''; ?>
                       style="display: none;"
                       onchange="handleFileSelection('<?php echo $formId; ?>')">
            </div>
            
            <!-- File Preview -->
            <div class="file-preview" id="preview-<?php echo $formId; ?>" style="display: none;">
                <h6>Selected Photos:</h6>
                <div class="preview-grid" id="preview-grid-<?php echo $formId; ?>"></div>
            </div>
            
            <!-- Upload Options -->
            <div class="upload-options mt-3" style="display: none;" id="options-<?php echo $formId; ?>">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Title (optional)</label>
                        <input type="text" class="form-control" name="title" placeholder="Photo title">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Description (optional)</label>
                        <textarea class="form-control" name="description" rows="2" placeholder="Photo description"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Upload Button -->
            <div class="upload-actions mt-3" style="display: none;" id="actions-<?php echo $formId; ?>">
                <button type="button" class="btn btn-primary" onclick="uploadPhotos('<?php echo $formId; ?>')">
                    <i class="fas fa-upload me-2"></i>Upload Photos
                </button>
                <button type="button" class="btn btn-secondary ms-2" onclick="clearSelection('<?php echo $formId; ?>')">
                    <i class="fas fa-times me-2"></i>Clear
                </button>
            </div>
            
            <!-- Progress -->
            <div class="upload-progress mt-3" style="display: none;" id="progress-<?php echo $formId; ?>">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="progress-text text-center mt-2">Uploading...</div>
            </div>
        </form>
    </div>
    
    <?php
    return ob_get_clean();
}
?>
