<?php
// Create CSV Import Log Table
require_once 'config/config.php';

echo "=== Creating CSV Import Log Table ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Create csv_import_log table
    $sql = "
        CREATE TABLE IF NOT EXISTS csv_import_log (
            id VARCHAR(36) PRIMARY KEY,
            place_id VARCHAR(36) NOT NULL,
            menu_type ENUM('beer', 'food') NOT NULL,
            imported_count INT DEFAULT 0,
            error_count INT DEFAULT 0,
            warning_count INT DEFAULT 0,
            imported_by VARCHAR(36),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (place_id) REFERENCES breweries(id) ON DELETE CASCADE,
            FOREIGN KEY (imported_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_place_id (place_id),
            INDEX idx_menu_type (menu_type),
            INDEX idx_created_at (created_at)
        )
    ";
    
    $pdo->exec($sql);
    echo "✓ Created csv_import_log table" . PHP_EOL;
    
    // Check if table was created successfully
    $stmt = $pdo->query("SHOW TABLES LIKE 'csv_import_log'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✓ Table verified successfully" . PHP_EOL;
        
        // Show table structure
        echo "\nTable structure:" . PHP_EOL;
        $stmt = $pdo->query("DESCRIBE csv_import_log");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "  • " . $column['Field'] . " (" . $column['Type'] . ")" . PHP_EOL;
        }
    } else {
        echo "❌ Table creation failed" . PHP_EOL;
    }
    
    echo "\n=== CSV Import Log Table Setup Complete ===" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
