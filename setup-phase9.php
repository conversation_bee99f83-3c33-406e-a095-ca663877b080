<?php
/**
 * Phase 9 Setup Script
 * Design & Mobile Optimization
 */

require_once 'config/config.php';

echo "<h1>📱 Phase 9 Setup: Design & Mobile Optimization</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Setting up Phase 9 mobile and PWA infrastructure...</h2>";
    
    // Read and execute the Phase 9 SQL file
    $sqlFile = 'database/phase9_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt) && !preg_match('/^\/\*/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    $createdTables = [];
    $createdIndexes = [];
    $createdProcedures = [];
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $conn->exec($statement);
                $successCount++;
                
                // Track what was created
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    $createdTables[] = $tableName;
                    echo "<p>✅ Created table: <strong>$tableName</strong></p>";
                } elseif (strpos($statement, 'CREATE INDEX') !== false) {
                    preg_match('/CREATE INDEX.*?(\w+)/', $statement, $matches);
                    $indexName = $matches[1] ?? 'unknown';
                    $createdIndexes[] = $indexName;
                    echo "<p>✅ Created index: <strong>$indexName</strong></p>";
                } elseif (strpos($statement, 'CREATE PROCEDURE') !== false) {
                    preg_match('/CREATE PROCEDURE.*?(\w+)/', $statement, $matches);
                    $procedureName = $matches[1] ?? 'unknown';
                    $createdProcedures[] = $procedureName;
                    echo "<p>✅ Created procedure: <strong>$procedureName</strong></p>";
                } elseif (strpos($statement, 'CREATE OR REPLACE VIEW') !== false) {
                    preg_match('/CREATE OR REPLACE VIEW.*?(\w+)/', $statement, $matches);
                    $viewName = $matches[1] ?? 'unknown';
                    echo "<p>✅ Created view: <strong>$viewName</strong></p>";
                } elseif (strpos($statement, 'INSERT IGNORE') !== false) {
                    echo "<p>✅ Initialized default preferences</p>";
                }
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<p>❌ Error executing statement: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>📱 Setup Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p><strong>✅ Successful operations:</strong> $successCount</p>";
    echo "<p><strong>❌ Failed operations:</strong> $errorCount</p>";
    echo "<p><strong>📋 Tables created:</strong> " . count($createdTables) . "</p>";
    echo "<p><strong>🔍 Indexes created:</strong> " . count($createdIndexes) . "</p>";
    echo "<p><strong>⚙️ Procedures created:</strong> " . count($createdProcedures) . "</p>";
    echo "</div>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Phase 9 Setup Complete!</h3>";
        echo "<p>Design & Mobile Optimization infrastructure has been successfully set up. You now have access to:</p>";
        echo "<ul>";
        echo "<li><strong>Progressive Web App (PWA):</strong> Install prompts, offline functionality, and app-like experience</li>";
        echo "<li><strong>Mobile-First Design:</strong> Touch-optimized interfaces and responsive layouts</li>";
        echo "<li><strong>Mobile Bottom Navigation:</strong> Native app-style navigation for mobile devices</li>";
        echo "<li><strong>Offline Support:</strong> Service worker caching and background sync</li>";
        echo "<li><strong>Mobile Analytics:</strong> Device tracking and interaction analytics</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🚀 New Features Available</h2>";
        echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>📱 Mobile Features:</h4>";
        echo "<ul>";
        echo "<li><strong>Mobile Bottom Navigation:</strong> Quick access to key features on mobile devices</li>";
        echo "<li><strong>Touch Gestures:</strong> Swipe, pull-to-refresh, and touch feedback</li>";
        echo "<li><strong>Responsive Design:</strong> Optimized layouts for all screen sizes</li>";
        echo "<li><strong>Mobile Forms:</strong> Touch-friendly inputs with proper keyboard types</li>";
        echo "<li><strong>Haptic Feedback:</strong> Vibration feedback for supported devices</li>";
        echo "</ul>";
        
        echo "<h4>🔧 PWA Features:</h4>";
        echo "<ul>";
        echo "<li><strong>App Installation:</strong> Install Beersty as a native-like app</li>";
        echo "<li><strong>Offline Functionality:</strong> Browse cached content when offline</li>";
        echo "<li><strong>Background Sync:</strong> Sync actions when connection returns</li>";
        echo "<li><strong>Push Notifications:</strong> Stay updated with beer activities</li>";
        echo "<li><strong>App Shortcuts:</strong> Quick access to check-ins and discovery</li>";
        echo "</ul>";
        
        echo "<h4>🎨 Design Enhancements:</h4>";
        echo "<ul>";
        echo "<li><strong>Modern UI:</strong> Updated design system with beer-themed elements</li>";
        echo "<li><strong>Dark Mode Support:</strong> Automatic theme switching based on preferences</li>";
        echo "<li><strong>Accessibility:</strong> Screen reader support and keyboard navigation</li>";
        echo "<li><strong>Performance:</strong> Lazy loading and optimized animations</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔧 Technical Enhancements</h2>";
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<ul>";
        echo "<li><strong>Service Worker:</strong> Caching strategy for offline functionality</li>";
        echo "<li><strong>Web App Manifest:</strong> Native app installation support</li>";
        echo "<li><strong>Mobile Analytics:</strong> Device and interaction tracking</li>";
        echo "<li><strong>PWA Preferences:</strong> User customization for mobile experience</li>";
        echo "<li><strong>Offline Sync Queue:</strong> Background synchronization of user actions</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>📊 Mobile Analytics</h2>";
        echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p>Phase 9 includes comprehensive mobile analytics:</p>";
        echo "<ul>";
        echo "<li><strong>Device Tracking:</strong> Screen sizes, capabilities, and performance</li>";
        echo "<li><strong>PWA Analytics:</strong> Installation rates and usage patterns</li>";
        echo "<li><strong>Mobile Interactions:</strong> Touch gestures and navigation patterns</li>";
        echo "<li><strong>Offline Usage:</strong> Track how users interact when offline</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔗 Quick Links</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p><strong>Test Mobile Features:</strong></p>";
        echo "<p><a href='/beersty/' class='btn btn-primary' style='margin-right: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>View Mobile Site</a>";
        echo "<a href='/beersty/test-phase9.php' class='btn btn-success' style='margin-right: 10px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Test PWA Features</a></p>";
        
        echo "<p><strong>Mobile Testing Tips:</strong></p>";
        echo "<ul>";
        echo "<li>Open the site on a mobile device or use browser dev tools mobile view</li>";
        echo "<li>Look for the install prompt to add Beersty to your home screen</li>";
        echo "<li>Try the mobile bottom navigation for quick access</li>";
        echo "<li>Test offline functionality by disabling network connection</li>";
        echo "<li>Use touch gestures like swipe and pull-to-refresh</li>";
        echo "</ul>";
        echo "</div>";
        
        // Test mobile functionality
        echo "<h2>🧪 Testing Mobile Features</h2>";
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        
        try {
            // Test if mobile tables exist
            $stmt = $conn->query("SELECT COUNT(*) as count FROM user_pwa_preferences");
            $result = $stmt->fetch();
            echo "<p>✅ PWA preferences table: {$result['count']} users configured</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM user_devices");
            $result = $stmt->fetch();
            echo "<p>✅ Device tracking table: {$result['count']} devices tracked</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM pwa_analytics");
            $result = $stmt->fetch();
            echo "<p>✅ PWA analytics table: {$result['count']} events tracked</p>";
            
            // Test mobile view
            $stmt = $conn->query("SELECT COUNT(*) as count FROM mobile_user_summary");
            $result = $stmt->fetch();
            echo "<p>✅ Mobile user summary view: {$result['count']} users in view</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Mobile testing error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Setup Completed with Errors</h3>";
        echo "<p>Some operations failed. Please check the error messages above and try running the setup again.</p>";
        echo "<p>Common issues:</p>";
        echo "<ul>";
        echo "<li>Database permissions for creating tables and procedures</li>";
        echo "<li>MySQL version compatibility</li>";
        echo "<li>Existing table conflicts</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='test-phase9.php'>Test Phase 9 Features →</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
