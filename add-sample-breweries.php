<?php
// Add Sample Breweries for Menu Management Testing
require_once 'config/config.php';

echo "=== Adding Sample Breweries ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Check if breweries already exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM breweries");
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "Found $count existing breweries." . PHP_EOL;
    } else {
        echo "No breweries found. Adding sample breweries..." . PHP_EOL;
        
        // Sample breweries data
        $breweries = [
            [
                'name' => 'Craft Beer Co.',
                'address' => '123 Brewery St',
                'city' => 'Beer City',
                'state' => 'MI',
                'zip' => '12345',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'description' => 'A local craft brewery specializing in IPAs and stouts.',
                'brewery_type' => 'micro'
            ],
            [
                'name' => 'Hoppy Trails Brewing',
                'address' => '456 Hop Lane',
                'city' => 'Hoptown',
                'state' => 'MI',
                'zip' => '67890',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'description' => 'Family-owned brewery with a focus on traditional ales.',
                'brewery_type' => 'brewpub'
            ],
            [
                'name' => 'Golden Grain Brewery',
                'address' => '789 Malt Ave',
                'city' => 'Grainville',
                'state' => 'MI',
                'zip' => '54321',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'description' => 'Award-winning brewery known for wheat beers and lagers.',
                'brewery_type' => 'regional'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO breweries (id, name, address, city, state, zip, phone, email, description, brewery_type, created_at) 
            VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        foreach ($breweries as $brewery) {
            $stmt->execute([
                $brewery['name'],
                $brewery['address'],
                $brewery['city'],
                $brewery['state'],
                $brewery['zip'],
                $brewery['phone'],
                $brewery['email'],
                $brewery['description'],
                $brewery['brewery_type']
            ]);
            echo "✓ Added: " . $brewery['name'] . PHP_EOL;
        }
    }
    
    // Show current breweries
    echo "\n--- Current Breweries ---" . PHP_EOL;
    $stmt = $pdo->query("SELECT id, name, city, state FROM breweries ORDER BY name");
    $breweries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($breweries as $brewery) {
        echo "• " . $brewery['name'] . " (" . $brewery['city'] . ", " . $brewery['state'] . ")" . PHP_EOL;
        echo "  ID: " . $brewery['id'] . PHP_EOL;
    }
    
    echo "\n=== Sample Breweries Ready ===" . PHP_EOL;
    echo "You can now test menu management at:" . PHP_EOL;
    echo "http://localhost:8000/admin/menu-management.php" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
