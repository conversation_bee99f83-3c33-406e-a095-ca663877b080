
import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/auth";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const Index = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  return (
    <div className="container mx-auto py-16 px-4 min-h-screen flex flex-col items-center justify-center">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      <div className="max-w-3xl mx-auto text-center">
        <h1 className="text-5xl font-bold mb-6">Brewery Management System</h1>
        <p className="text-xl text-muted-foreground mb-8">
          A complete platform for managing breweries, their profiles, and connecting with customers
        </p>
        
        <div className="flex flex-wrap justify-center gap-4">
          {!user && (
            <>
              <Button size="lg" onClick={() => navigate("/auth")}>
                Log In
              </Button>
              <Button size="lg" variant="outline" onClick={() => navigate("/auth?tab=register")}>
                Register
              </Button>
            </>
          )}
          
          {user && user.role === 'admin' && (
            <>
              <Button size="lg" onClick={() => navigate("/admin")}>
                Admin Dashboard
              </Button>
              <Button size="lg" variant="outline" onClick={() => navigate("/import")}>
                Data Import
              </Button>
              <Button size="lg" variant="destructive" onClick={logout}>
                Log Out
              </Button>
            </>
          )}
          
          {user && user.role === 'brewery' && (
            <>
              <Button size="lg" onClick={() => navigate("/brewery-profile")}>
                Manage Brewery
              </Button>
              <Button size="lg" variant="outline" onClick={logout}>
                Log Out
              </Button>
            </>
          )}
        </div>
      </div>
      
      <div className="mt-20 max-w-4xl">
        <h2 className="text-3xl font-bold mb-6 text-center">Key Features</h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-bold mb-2">Brewery Management</h3>
            <p className="text-muted-foreground">
              Comprehensive tools for brewery administrators to manage and import brewery data
            </p>
          </div>
          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-bold mb-2">Profile Updates</h3>
            <p className="text-muted-foreground">
              Allow breweries to update their profile information, logos, and feature images
            </p>
          </div>
          <div className="bg-card p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-bold mb-2">Menu Publishing</h3>
            <p className="text-muted-foreground">
              Publish and update food and beer menus to keep customers informed
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
