
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from '@/auth';
import { Navigate } from 'react-router-dom';
import { Beer, UtensilsCrossed, FileText, ArrowLeft } from 'lucide-react';
import BeerMenuTab from '@/components/menu/BeerMenuTab';
import FoodMenuTab from '@/components/menu/FoodMenuTab';

const MenuManagement: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Ensure user is authenticated
  if (!user) {
    return <Navigate to="/auth" />;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Menu Management</h1>
          <p className="text-muted-foreground mt-1">
            Manage your beer and food menu items, and import/export data via CSV
          </p>
        </div>
        <div className="flex gap-4">
          <Button 
            variant="outline" 
            onClick={() => navigate('/brewery-profile')}
            className="flex items-center"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Profile
          </Button>
        </div>
      </div>

      <Tabs defaultValue="beer" className="w-full">
        <TabsList className="mb-8">
          <TabsTrigger value="beer">
            <Beer className="mr-2 h-4 w-4" />
            Beer Menus
          </TabsTrigger>
          <TabsTrigger value="food">
            <UtensilsCrossed className="mr-2 h-4 w-4" />
            Food Menu
          </TabsTrigger>
          <TabsTrigger value="help">
            <FileText className="mr-2 h-4 w-4" />
            CSV Help
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="beer">
          <BeerMenuTab breweryId={user.breweryId} />
        </TabsContent>
        
        <TabsContent value="food">
          <FoodMenuTab breweryId={user.breweryId} />
        </TabsContent>
        
        <TabsContent value="help">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold">CSV Import/Export Help</h2>
            
            <div className="bg-card border rounded-lg p-6 space-y-4">
              <h3 className="text-lg font-semibold">Beer Menu CSV Format</h3>
              <p>The CSV file for beer menu items should include the following columns:</p>
              <div className="bg-muted rounded-md p-3 overflow-x-auto">
                <code className="text-xs">
                  name,description,type,abv,ibu,price,thumbnail,added_date,seasonal,origin,featured
                </code>
              </div>
              
              <h3 className="text-lg font-semibold mt-6">Food Menu CSV Format</h3>
              <p>The CSV file for food menu items should include the following columns:</p>
              <div className="bg-muted rounded-md p-3 overflow-x-auto">
                <code className="text-xs">
                  name,description,category,price,is_vegetarian,is_gluten_free,thumbnail,added_date,spicy_level,allergens,featured
                </code>
              </div>
              
              <h3 className="text-lg font-semibold mt-6">Tips for CSV Import</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Use UTF-8 encoding for all CSV files</li>
                <li>Headers must match exactly as shown above (case-sensitive)</li>
                <li>For boolean fields (like "seasonal" or "featured"), use "true" or "false"</li>
                <li>Dates should be formatted as YYYY-MM-DD</li>
                <li>Missing values will be replaced with defaults</li>
              </ul>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MenuManagement;
