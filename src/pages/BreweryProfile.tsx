
import React from 'react';
import BreweryProfileComponent from '@/components/brewery/BreweryProfile';
import { useAuth } from '@/auth';
import { Navigate, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const BreweryProfile: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  
  // Ensure user is authenticated
  if (!user) {
    return <Navigate to="/auth" />;
  }
  
  // Special handling for admins
  if (user.role === 'admin') {
    return (
      <div className="container mx-auto py-8">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Administrator Access</CardTitle>
            <CardDescription>
              As an administrator, you have access to all system features. You can create a test brewery to explore brewery management features, 
              or return to the admin dashboard to manage the platform.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <Button 
                onClick={() => navigate('/admin')}
                className="flex-1"
              >
                Go to Admin Dashboard
              </Button>
              <Button 
                onClick={() => navigate('/import')}
                variant="outline"
                className="flex-1"
              >
                Go to Data Import
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <BreweryProfileComponent user={user} />
      </div>
    );
  }
  
  // Pass the user object to the component to avoid nested useAuth calls
  return <BreweryProfileComponent user={user} />;
};

export default BreweryProfile;
