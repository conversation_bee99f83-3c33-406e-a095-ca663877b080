
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from '@/auth';
import { Navigate } from 'react-router-dom';
import { Home, Users, Beer, Settings } from 'lucide-react';
import OverviewTabContent from '@/components/admin/OverviewTabContent';
import BreweriesTabContent from '@/components/admin/BreweriesTabContent';
import BeerMenuDashboard from '@/components/admin/BeerMenuDashboard';
import GenericTabContent from '@/components/admin/GenericTabContent';
import { useDashboardStats } from '@/components/admin/useDashboardStats';

const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { stats, isLoading, error } = useDashboardStats();
  
  // Ensure only admin users can access this page
  if (!user || user.role !== 'admin') {
    return <Navigate to="/" />;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Manage breweries, users, and system settings
          </p>
        </div>
        <div className="flex gap-4">
          <Button variant="outline" onClick={() => navigate('/import')}>
            Data Import
          </Button>
          <Button onClick={() => navigate('/')}>
            Return to Home
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-8">
          <TabsTrigger value="overview">
            <Home className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="breweries">
            <Users className="mr-2 h-4 w-4" />
            Breweries
          </TabsTrigger>
          <TabsTrigger value="beer-menu">
            <Beer className="mr-2 h-4 w-4" />
            Beer Menu
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <OverviewTabContent stats={stats} isLoading={isLoading} />
        </TabsContent>
        
        <TabsContent value="breweries">
          <BreweriesTabContent loading={isLoading} totalBreweries={stats.breweryCount} />
        </TabsContent>
        
        <TabsContent value="beer-menu">
          <BeerMenuDashboard />
        </TabsContent>
        
        <TabsContent value="settings">
          <GenericTabContent 
            title="System Settings" 
            description="Configure system-wide settings and preferences"
            icon={Settings}
            message="System settings configuration will be available soon."
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboard;
