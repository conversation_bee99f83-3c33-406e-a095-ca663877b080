
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tv, AlertTriangle } from 'lucide-react';
import { DigitalBoard as DigitalBoardType, DigitalBoardSettings } from '@/types/digitalBoard';
import { BeerMenuItem } from '@/components/menu/types/menuTypes';

interface SimpleBrewery {
  id: string;
  name: string;
  city?: string;
  state?: string;
}

const DigitalBoard: React.FC = () => {
  const { breweryId, boardId } = useParams<{ breweryId: string; boardId: string }>();
  const [brewery, setBrewery] = useState<SimpleBrewery | null>(null);
  const [board, setBoard] = useState<DigitalBoardType | null>(null);
  const [beerMenu, setBeerMenu] = useState<BeerMenuItem[]>([]);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch brewery and board data
  useEffect(() => {
    const fetchData = async () => {
      if (!breweryId) {
        setError('Brewery ID is required');
        setLoading(false);
        return;
      }
      
      try {
        // Fetch brewery data
        const { data: breweryData, error: breweryError } = await supabase
          .from('breweries')
          .select('id, name, city, state')
          .eq('id', breweryId)
          .single();
        
        if (breweryError) throw breweryError;
        setBrewery(breweryData as SimpleBrewery);
        
        // Fetch board configuration
        const boardQuery = supabase
          .from('digital_boards')
          .select('*')
          .eq('brewery_id', breweryId);
          
        if (boardId) {
          boardQuery.eq('board_id', boardId);
        }
        
        const { data: boardData, error: boardError } = await boardQuery.single();
        
        if (boardError) throw boardError;
        
        // Convert from db format to our application type
        const boardInfo: DigitalBoardType = {
          ...boardData,
          settings: boardData.settings as unknown as DigitalBoardSettings
        };
        
        if (!boardInfo.is_active) {
          setError('This digital board is currently inactive');
          setLoading(false);
          return;
        }
        
        setBoard(boardInfo);
        
        // Fetch beer menu items
        const { data: menuData, error: menuError } = await supabase
          .from('beer_menu')
          .select('*')
          .eq('brewery_id', breweryId);
        
        if (menuError) throw menuError;
        setBeerMenu(menuData as BeerMenuItem[]);
        
      } catch (err: any) {
        console.error('Error loading digital board:', err);
        setError(err.message || 'Failed to load digital board');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [breweryId, boardId]);
  
  // Auto-rotate beer menu items if enabled
  useEffect(() => {
    if (!board || !board.settings.autoRotate || beerMenu.length === 0) return;
    
    const interval = setInterval(() => {
      setCurrentItemIndex(prevIndex => 
        prevIndex >= beerMenu.length - 1 ? 0 : prevIndex + 1
      );
    }, board.settings.displayTime * 1000);
    
    return () => clearInterval(interval);
  }, [board, beerMenu]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card>
          <CardContent className="p-8">
            <div className="flex flex-col items-center">
              <Tv className="h-12 w-12 animate-pulse text-primary" />
              <h2 className="mt-4 text-xl font-semibold">Loading Digital Beer Board...</h2>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  if (error || !brewery || !board) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <Card className="max-w-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-destructive" />
              Digital Board Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Unable to load digital board'}</p>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Display the currently selected beer
  const currentBeer = beerMenu[currentItemIndex] || null;
  
  return (
    <div className="min-h-screen bg-background p-4 md:p-8 flex flex-col">
      <header className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tv className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">{brewery.name} Beer Board</h1>
          </div>
          <div className="text-sm text-muted-foreground">
            Updated: {new Date().toLocaleString()}
          </div>
        </div>
      </header>
      
      <main className="flex-1">
        {beerMenu.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Card className="max-w-md">
              <CardContent className="p-8">
                <p className="text-center">No beer menu items found</p>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-8 h-full">
            {/* Left section - Beer Navigation */}
            <div className="lg:col-span-1 lg:border-r pr-4 hidden lg:block">
              <h3 className="text-lg font-medium mb-4">On Tap</h3>
              <ul className="space-y-2">
                {beerMenu.map((beer, index) => (
                  <li key={beer.id}>
                    <button
                      onClick={() => setCurrentItemIndex(index)}
                      className={`text-left w-full p-2 rounded ${
                        index === currentItemIndex 
                          ? 'bg-primary/10 font-medium' 
                          : 'hover:bg-muted'
                      }`}
                    >
                      {beer.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Right section - Current Beer Display */}
            <div className="lg:col-span-5">
              {currentBeer && (
                <Card className="h-full flex flex-col bg-card border">
                  <CardHeader>
                    <CardTitle className="text-3xl">{currentBeer.name}</CardTitle>
                    <div className="flex flex-wrap gap-4 mt-2">
                      {currentBeer.type && (
                        <div className="text-muted-foreground">
                          <span className="font-medium">Style:</span> {currentBeer.type}
                        </div>
                      )}
                      {currentBeer.abv && (
                        <div className="text-muted-foreground">
                          <span className="font-medium">ABV:</span> {currentBeer.abv}%
                        </div>
                      )}
                      {currentBeer.ibu && (
                        <div className="text-muted-foreground">
                          <span className="font-medium">IBU:</span> {currentBeer.ibu}
                        </div>
                      )}
                      {board.settings.showPrices && currentBeer.price && (
                        <div className="text-muted-foreground">
                          <span className="font-medium">Price:</span> {currentBeer.price}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="flex-1 flex flex-col md:flex-row gap-8">
                    {board.settings.showImages && currentBeer.thumbnail && (
                      <div className="md:w-1/3 mb-4 md:mb-0">
                        <div 
                          className="w-full aspect-square rounded-lg bg-muted/50 bg-center bg-cover"
                          style={{ backgroundImage: `url(${currentBeer.thumbnail})` }}
                        />
                      </div>
                    )}
                    
                    <div className={board.settings.showImages && currentBeer.thumbnail ? "md:w-2/3" : "w-full"}>
                      {board.settings.showDescription && currentBeer.description && (
                        <div className="space-y-4">
                          <h3 className="text-xl font-medium">Description</h3>
                          <p className="text-lg leading-relaxed">{currentBeer.description}</p>
                        </div>
                      )}
                      
                      {/* Navigation for small screens */}
                      <div className="mt-8 flex justify-between lg:hidden">
                        <button 
                          onClick={() => setCurrentItemIndex(prev => 
                            prev <= 0 ? beerMenu.length - 1 : prev - 1
                          )}
                          className="p-2 border rounded hover:bg-muted"
                        >
                          Previous
                        </button>
                        <span className="self-center">
                          {currentItemIndex + 1} of {beerMenu.length}
                        </span>
                        <button 
                          onClick={() => setCurrentItemIndex(prev => 
                            prev >= beerMenu.length - 1 ? 0 : prev + 1
                          )}
                          className="p-2 border rounded hover:bg-muted"
                        >
                          Next
                        </button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </main>
      
      <footer className="mt-8 text-center text-sm text-muted-foreground">
        <p>Powered by BreweryBuddy Digital Beer Board</p>
      </footer>
    </div>
  );
};

export default DigitalBoard;
