
import React, { useEffect, useState } from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import BreweryGrid from '@/components/brewery/BreweryGrid';
import BreweryDataView from '@/components/brewery/BreweryDataView';
import { useBreweryFetch } from '@/hooks/brewery/useBreweryFetch';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';

const BreweryListing: React.FC = () => {
  const { breweries, isLoading, reloadBreweries } = useBreweryFetch();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Load breweries on mount
    loadBreweries();
  }, []);

  // Wrapper for reloadBreweries with error handling
  const loadBreweries = async () => {
    try {
      console.log("Loading breweries...");
      await reloadBreweries();
      console.log("Loaded breweries:", breweries);
    } catch (error) {
      console.error("Error loading breweries:", error);
      toast({
        title: "Error loading breweries",
        description: "There was a problem loading the brewery data. Please try refreshing.",
        variant: "destructive"
      });
    }
  };
  
  // Handle refresh button click with loading state
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await reloadBreweries();
      toast({
        title: "Data refreshed",
        description: `Loaded ${breweries.length} breweries successfully`,
      });
    } catch (error) {
      console.error("Error refreshing breweries:", error);
      toast({
        title: "Refresh failed",
        description: "There was a problem refreshing the brewery data.",
        variant: "destructive"
      });
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Breweries</h1>
        <div className="flex gap-2">
          <Button 
            onClick={handleRefresh}
            variant="outline"
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          <Button 
            onClick={() => navigate('/import')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Import Data
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <Skeleton key={i} className="h-40 w-full rounded-md" />
            ))}
          </div>
        </div>
      ) : (
        <Tabs defaultValue="grid" className="space-y-4">
          <TabsList>
            <TabsTrigger value="grid">Grid View</TabsTrigger>
            <TabsTrigger value="table">Table View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="grid">
            <Card>
              <CardContent className="pt-6">
                <BreweryGrid 
                  breweries={breweries}
                  onViewDetails={(brewery) => navigate(`/brewery/${brewery.id}`)}
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="table">
            <Card>
              <CardContent className="pt-6 px-0">
                <BreweryDataView 
                  breweries={breweries}
                  onRefresh={loadBreweries}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
      
      {/* Display message if no breweries found after loading */}
      {!isLoading && breweries.length === 0 && (
        <div className="text-center py-10 border rounded-lg bg-background shadow-sm">
          <p className="text-muted-foreground mb-4">No breweries found in the system.</p>
          <Button 
            onClick={() => navigate('/import')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Import Brewery Data
          </Button>
        </div>
      )}
    </div>
  );
};

export default BreweryListing;
