
import React, { useState, useEffect } from 'react';
import { useSearchParams, useLocation, useNavigate } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import { useAuth } from '@/auth';
import { toast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';

const Auth: React.FC = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const navigate = useNavigate();
  const showRegister = searchParams.get('register') === 'true';
  const showReset = searchParams.get('tab') === 'reset';
  const breweryId = searchParams.get('breweryId');
  const [activeTab, setActiveTab] = useState<string>(
    showReset ? 'reset' : (showRegister ? 'register' : 'login')
  );
  const { user } = useAuth();

  console.log('Auth: Rendered with params:', { showRegister, showReset, breweryId, pathname: location.pathname });

  useEffect(() => {
    if (showReset) {
      setActiveTab('reset');
    } else {
      setActiveTab(showRegister ? 'register' : 'login');
    }
  }, [showRegister, showReset]);

  useEffect(() => {
    // Check for auth error in the URL
    const errorDescription = searchParams.get('error_description');
    if (errorDescription) {
      console.error('Auth: Error from URL:', errorDescription);
      toast({
        title: "Authentication Error",
        description: decodeURIComponent(errorDescription),
        variant: "destructive",
      });
    }
    
    // Check for OAuth return
    const accessToken = searchParams.get('access_token');
    if (accessToken) {
      console.log('Auth: Got access token from URL, login successful');
      toast({
        title: "Login Successful",
        description: "You've been successfully authenticated",
      });
    }
  }, [location, searchParams]);

  // Handle successful login/registration
  const handleAuthSuccess = () => {
    console.log("Auth: handleAuthSuccess called, redirecting", { breweryId, user });
    // If we have a brewery ID in the URL, redirect to that brewery's profile page
    if (breweryId) {
      console.log(`Auth: Redirecting to brewery profile with ID ${breweryId}`);
      navigate(`/brewery/${breweryId}`);
    } else {
      console.log('Auth: Redirecting to brewery-profile');
      navigate('/brewery-profile');
    }
  };

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      console.log("Auth: User already logged in, redirecting", { user });
      // If we have a brewery ID in the URL, redirect to that brewery's profile page
      if (breweryId) {
        console.log(`Auth: Redirecting to brewery profile with ID ${breweryId}`);
        navigate(`/brewery/${breweryId}`);
      } else {
        console.log('Auth: Redirecting to brewery-profile');
        navigate('/brewery-profile');
      }
    }
  }, [user, breweryId, navigate]);

  return (
    <div className="container flex h-screen items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold tracking-tight">
            {activeTab === 'reset' ? 'Reset Your Password' : (breweryId ? 'Claim Your Brewery' : 'Welcome Back')}
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="register">Register</TabsTrigger>
              <TabsTrigger value="reset">Reset</TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <LoginForm onSuccess={handleAuthSuccess} />
              <div className="mt-4 text-center">
                <Link 
                  to="/auth?tab=reset" 
                  className="text-sm text-blue-600 hover:underline"
                >
                  Forgot your password?
                </Link>
              </div>
            </TabsContent>
            
            <TabsContent value="register">
              <RegisterForm breweryIdFromUrl={breweryId || undefined} />
            </TabsContent>

            <TabsContent value="reset">
              <ResetPasswordForm />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Auth;
