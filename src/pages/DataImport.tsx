
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useAuth } from '@/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import BreweryImport from '@/components/brewery/BreweryImport';
import ImportActions from '@/components/brewery/ImportActions';
import CsvRequirements from '@/components/brewery/CsvRequirements';
import { useBreweryImportState } from '@/components/brewery/hooks/useBreweryImportState';
import { toast } from '@/hooks/use-toast';

const DataImport: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [breweryCount, setBreweryCount] = useState(0);
  
  // Use the import state hook
  const importState = useBreweryImportState({
    reloadBreweries: async () => {
      // Get brewery count from localStorage
      const breweries = localStorage.getItem('breweries') 
        ? JSON.parse(localStorage.getItem('breweries') || '[]')
        : [];
      setBreweryCount(breweries.length);
      return Promise.resolve();
    }
  });
  
  // Use a simple function to delete all breweries
  const [isDeleting, setIsDeleting] = useState(false);
  
  const deleteAll = async () => {
    setIsDeleting(true);
    try {
      localStorage.removeItem('breweries');
      setBreweryCount(0);
      importState.setError(null);
      toast({
        title: "All breweries deleted",
        description: "Successfully deleted all brewery data"
      });
    } catch (error) {
      console.error("Error deleting breweries:", error);
      toast({
        title: "Error deleting breweries",
        description: "There was a problem deleting the brewery data",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Handle file import
  const handleImportClick = async () => {
    console.log("Import button clicked", importState.selectedFile);
    if (importState.selectedFile) {
      // This calls the actual import function with the selected file
      await importState.handleFileUpload({
        target: {
          files: [importState.selectedFile]
        }
      } as any);
    } else {
      toast({
        title: "No file selected",
        description: "Please select a CSV file to import",
        variant: "destructive"
      });
    }
  };
  
  // Load initial brewery count
  useEffect(() => {
    const breweries = localStorage.getItem('breweries') 
      ? JSON.parse(localStorage.getItem('breweries') || '[]')
      : [];
    setBreweryCount(breweries.length);
  }, []);
  
  // Redirect non-admin users
  if (user && user.role !== 'admin') {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You don't have permission to access this page. Only administrators can import data.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Data Import</CardTitle>
          <CardDescription>
            Import brewery data from CSV files. The system will automatically map fields and process the data.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BreweryImport 
            handleFileUpload={importState.handleChange}
            onImportClick={handleImportClick}
            selectedFile={importState.selectedFile}
            isLoading={importState.isLoading}
            progress={typeof importState.progress === 'number' ? importState.progress : 0}
            currentChunk={importState.currentChunk}
            totalChunks={importState.totalChunks}
            rowsProcessed={importState.rowsProcessed}
            totalRows={importState.totalRows}
            errorMessage={importState.errorMessage}
            fieldMapping={importState.fieldMapping}
          />
          
          <CsvRequirements />
          
          <ImportActions 
            onDeleteAll={deleteAll}
            onBack={() => navigate('/breweries')}
            isDeleteLoading={isDeleting}
            breweryCount={breweryCount}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default DataImport;
