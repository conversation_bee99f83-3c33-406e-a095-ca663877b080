
/**
 * Field mapping definitions for CSV imports
 * Defines standard fields and their variations
 */

// Define field categories for better organization
export const FIELD_CATEGORIES = {
  BASIC: "Basic Information",
  LOCATION: "Location",
  CONTACT: "Contact Information",
  MEDIA: "Media & Images"
};

// Define known field variations with categories for brewery data
export const fieldMappingDefinitions: Record<string, {
  category: string;
  variations: string[];
  required: boolean;
}> = {
  // Basic information
  name: {
    category: FIELD_CATEGORIES.BASIC,
    variations: ['name', 'brewery name', 'brewery_name', 'company name', 'company_name', 'title', 'business name', 'org name', 'establishment name'],
    required: true
  },
  description: {
    category: FIELD_CATEGORIES.BASIC,
    variations: ['description', 'desc', 'about', 'brewery description', 'summary', 'details', 'info', 'bio', 'overview', 'about us'],
    required: false
  },
  
  // Location fields
  address: {
    category: FIELD_CATEGORIES.LOCATION,
    variations: [
      'address', 'street address', 'street_address', 'location', 'addr', 'street', 
      'address1', 'street line 1', 'address line 1', 'physical address', 'location address',
      'mailing address', 'main address', 'primary address', 'business address', 'location_address',
      'establishment address', 'full address', 'address_line_1', 'addressline1', 'address 1'
    ],
    required: false
  },
  city: {
    category: FIELD_CATEGORIES.LOCATION,
    variations: ['city', 'town', 'municipality', 'locality', 'city name', 'township'],
    required: false
  },
  state: {
    category: FIELD_CATEGORIES.LOCATION,
    variations: ['state', 'province', 'region', 'state_id', 'state id', 'st', 'state code', 'state_code', 'state abbreviation', 'state_abbr'],
    required: false
  },
  zip: {
    category: FIELD_CATEGORIES.LOCATION,
    variations: ['zip', 'zipcode', 'zip_code', 'zip code', 'postal', 'postal_code', 'postal code', 'postcode', 'zip5', 'zipcode5', 'zip-code', 'postal-code', 'post code', 'postalcode', 'zip+4', 'postal+4'],
    required: false
  },
  
  // Contact information
  phone: {
    category: FIELD_CATEGORIES.CONTACT,
    variations: ['phone', 'telephone', 'phone_number', 'phone number', 'contact', 'contact_number', 'tel', 'telephone_number', 'main phone', 'primary phone'],
    required: false
  },
  website: {
    category: FIELD_CATEGORIES.CONTACT,
    variations: [
      'website', 'web', 'url', 'web_address', 'site', 'homepage', 'web site', 
      'web_site', 'www', 'domain', 'website url', 'web address', 'web url', 
      'website_url', 'site url', 'company website', 'company_website', 
      'business website', 'online', 'website_address', 'web_url', 'official website',
      'online address', 'online presence', 'web link', 'site link'
    ],
    required: false
  },
  email: {
    category: FIELD_CATEGORIES.CONTACT,
    variations: ['email', 'e-mail', 'email_address', 'email address', 'contact email', 'contact_email', 'mail', 'e mail', 'primary email'],
    required: false
  },
  
  // Media
  logo: {
    category: FIELD_CATEGORIES.MEDIA,
    variations: ['logo', 'logo_url', 'logo url', 'brand', 'brand_image', 'logo_image', 'logo image', 'brewery logo', 'company logo', 'logo path'],
    required: false
  },
  featureImage: {
    category: FIELD_CATEGORIES.MEDIA,
    variations: ['feature_image', 'featureImage', 'feature image', 'main image', 'main_image', 'banner', 'hero', 'cover', 'featured image', 'primary image', 'header image'],
    required: false
  }
};
