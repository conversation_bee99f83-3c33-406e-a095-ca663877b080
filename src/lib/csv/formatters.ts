
/**
 * Utilities for formatting and detecting field types in CSV data
 */

// Format a phone number to a standard format
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return "";
  
  // Remove any non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, "");
  
  // Format based on length
  if (digitsOnly.length === 10) {
    return `(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}`;
  } else if (digitsOnly.length === 11 && digitsOnly[0] === "1") {
    // Handle US numbers with leading 1
    return `(${digitsOnly.substring(1, 4)}) ${digitsOnly.substring(4, 7)}-${digitsOnly.substring(7)}`;
  }
  
  // Return original if we can't format
  return phoneNumber;
};

// Format a website URL to ensure it has http:// or https://
export const formatWebsiteUrl = (url: string): string => {
  if (!url) return "";
  
  // Trim any whitespace
  const trimmedUrl = url.trim();
  if (trimmedUrl === "") return "";
  
  // If URL doesn't start with http:// or https://, add https://
  if (!trimmedUrl.match(/^https?:\/\//i)) {
    return `https://${trimmedUrl}`;
  }
  
  return trimmedUrl;
};

// Format a zip code to a standard format
export const formatZipCode = (zipCode: string): string => {
  if (!zipCode) return "";
  
  // Remove any non-digit characters
  const digitsOnly = zipCode.replace(/\D/g, "");
  
  // Format based on length
  if (digitsOnly.length === 5) {
    return digitsOnly;
  } else if (digitsOnly.length === 9) {
    return `${digitsOnly.substring(0, 5)}-${digitsOnly.substring(5)}`;
  }
  
  // Return original if we can't format
  return zipCode;
};

// Simple address formatting
export const formatAddress = (address: string): string => {
  if (!address) return "";
  
  // Basic cleanup, remove double spaces and trim
  return address.replace(/\s+/g, " ").trim();
};

/**
 * Detect field type based on field name and value content
 * Returns the determined type and formatted value
 */
export const detectFieldType = (
  fieldName: string, 
  value: string
): { type: string; formattedValue: string } => {
  if (!value || typeof value !== 'string' || value.trim() === '') {
    return { type: 'text', formattedValue: value || '' };
  }
  
  // Normalize field name for comparison
  const normalizedField = fieldName.toLowerCase();
  
  // Phone number detection
  if (normalizedField.includes('phone') || normalizedField.includes('tel')) {
    // Check if value contains at least 10 digits
    if (value.replace(/\D/g, '').length >= 10) {
      return { type: 'tel', formattedValue: formatPhoneNumber(value) };
    }
  }
  
  // Website URL detection - improved to catch more website variants
  if (normalizedField.includes('web') || normalizedField.includes('url') || 
      normalizedField.includes('site') || normalizedField.includes('link') ||
      normalizedField.includes('www') || normalizedField === 'url') {
    // Improved URL detection logic
    const formattedUrl = formatWebsiteUrl(value);
    return { type: 'url', formattedValue: formattedUrl };
  }
  
  // Email detection
  if (normalizedField.includes('email') || normalizedField.includes('mail')) {
    // Simple email check
    if (value.includes('@') && value.includes('.') && !value.includes(' ')) {
      return { type: 'email', formattedValue: value.trim() };
    }
  }
  
  // Zip code detection
  if (normalizedField.includes('zip') || normalizedField.includes('postal')) {
    // Check if value contains 5 or 9 digits
    const digits = value.replace(/\D/g, '');
    if (digits.length === 5 || digits.length === 9) {
      return { type: 'zip', formattedValue: formatZipCode(value) };
    }
  }
  
  // Address detection - improved to catch more address variants
  if (normalizedField.includes('address') || normalizedField.includes('street') || 
      normalizedField.includes('location') || normalizedField === 'address' || 
      normalizedField === 'addr' || normalizedField.includes('address1')) {
    // Address usually contains words, numbers, and possibly commas
    return { type: 'address', formattedValue: formatAddress(value) };
  }
  
  // Default to text
  return { type: 'text', formattedValue: value.trim() };
};
