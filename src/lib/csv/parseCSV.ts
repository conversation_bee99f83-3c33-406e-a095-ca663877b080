
import { parseCSVRow } from "./parseCSVRow";
import { processCSVContent } from "./processCSVContent";

/**
 * Advanced CSV parser with improved field mapping and type detection
 */
export const parseCSV = async (file: File): Promise<{ 
  data: Record<string, any>[]; 
  columns: string[];
  duplicates: number;
  fieldMapping: Record<string, string>;
  fieldTypes: Record<string, string>;
}> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const text = event.target?.result as string;
        if (!text) {
          throw new Error("Failed to read file");
        }
        
        console.log("CSV content preview:", text.substring(0, 200) + "..."); 
        
        // Process CSV content using enhanced processor
        const result = processCSVContent(text);
        
        resolve(result);
      } catch (error) {
        console.error("CSV parsing error:", error);
        reject(error);
      }
    };
    
    reader.onerror = (error) => {
      console.error("Error reading file:", error);
      reject(new Error("Error reading file"));
    };
    
    console.log("Starting to read CSV file");
    reader.readAsText(file);
  });
};
