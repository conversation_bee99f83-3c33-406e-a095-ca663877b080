
/**
 * Utilities for normalizing and validating CSV headers
 */

/**
 * Normalize a header name for comparison
 * Removes special characters, converts to lowercase, etc.
 */
export const normalizeHeaderName = (header: string): string => {
  return header
    .trim()
    .toLowerCase()
    .replace(/[_\s-+]+/g, ' ')   // Convert underscores, hyphens, plus signs and multiple spaces to single space
    .replace(/[^\w\s]/g, '')     // Remove special characters
    .trim();
};

/**
 * Get all required field names
 */
export const getRequiredFields = (fieldMappingDefinitions: Record<string, { required: boolean }>): string[] => {
  return Object.entries(fieldMappingDefinitions)
    .filter(([_, def]) => def.required)
    .map(([field]) => field);
};

/**
 * Get all recommended field names (non-required)
 */
export const getRecommendedFields = (fieldMappingDefinitions: Record<string, { required: boolean }>): string[] => {
  return Object.entries(fieldMappingDefinitions)
    .filter(([_, def]) => !def.required)
    .map(([field]) => field);
};

/**
 * Check if field mapping contains all required fields
 */
export const validateRequiredFields = (
  fieldMapping: Record<string, string>,
  fieldMappingDefinitions: Record<string, { required: boolean }>
): {
  valid: boolean;
  missingFields: string[];
} => {
  const requiredFields = getRequiredFields(fieldMappingDefinitions);
  const mappedFields = Object.values(fieldMapping);
  
  const missingFields = requiredFields.filter(field => !mappedFields.includes(field));
  
  return {
    valid: missingFields.length === 0,
    missingFields
  };
};
