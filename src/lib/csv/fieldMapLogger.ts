
/**
 * Logging utilities for field mapping results
 */

/**
 * Log detailed field mapping results for debugging
 */
export const logFieldMappingResults = (
  mapping: Record<string, string>, 
  headers: string[],
  fieldMappingDefinitions: Record<string, { 
    category: string,
    required: boolean 
  }>,
  categories: Record<string, string>
): void => {
  // Group mapped fields by category
  const mappedByCategory: Record<string, {original: string, mapped: string}[]> = {};
  
  // Initialize categories
  Object.values(categories).forEach(category => {
    mappedByCategory[category] = [];
  });
  mappedByCategory["Other"] = [];
  
  // Group fields by category
  headers.forEach(header => {
    const mappedField = mapping[header];
    const fieldDef = fieldMappingDefinitions[mappedField];
    const category = fieldDef ? fieldDef.category : "Other";
    
    mappedByCategory[category].push({
      original: header,
      mapped: mappedField
    });
  });
  
  // Log results by category
  console.log("Field mapping by category:");
  Object.entries(mappedByCategory).forEach(([category, fields]) => {
    if (fields.length > 0) {
      console.log(`\n${category}:`);
      fields.forEach(field => {
        console.log(`  ${field.original} → ${field.mapped}${field.original !== field.mapped ? ' ✓' : ''}`);
      });
    }
  });
  
  // Check for required fields
  const requiredFields = Object.entries(fieldMappingDefinitions)
    .filter(([_, def]) => def.required)
    .map(([field]) => field);
    
  const missingRequired = requiredFields.filter(field => 
    !Object.values(mapping).includes(field)
  );
  
  if (missingRequired.length > 0) {
    console.warn(`Missing required fields: ${missingRequired.join(', ')}`);
  }
};
