
import { getRequiredFields, getRecommendedFields } from './fieldMappingUtils';

/**
 * Validate CSV data to ensure required fields are present
 * and warn about missing recommended fields
 */
export const validateCSVData = (
  headers: string[],
  fieldMapping: Record<string, string>
): void => {
  // Get standardized fields after mapping
  const mappedFields = Object.values(fieldMapping);
  
  // Check for required fields
  const requiredFields = getRequiredFields();
  const missingRequired = requiredFields.filter(field => !mappedFields.includes(field));
  
  if (missingRequired.length > 0) {
    throw new Error(`CSV must contain the following required fields: ${missingRequired.join(', ')}`);
  }
  
  // Warn about missing recommended columns
  const recommendedFields = getRecommendedFields();
  const missingRecommended = recommendedFields.filter(field => !mappedFields.includes(field));
  
  if (missingRecommended.length > 0) {
    console.warn(`Missing recommended columns: ${missingRecommended.join(', ')}`);
  }
};
