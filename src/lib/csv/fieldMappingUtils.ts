
/**
 * Field mapping utilities for CSV imports
 * Maps common variations of field names to standardized field names
 */

import { calculateStringSimilarity } from "./stringUtils";
import { fieldMappingDefinitions, FIELD_CATEGORIES } from "./fieldDefinitions";
import { normalizeHeaderName, validateRequiredFields as validateFields } from "./fieldNormalizer";
import { performFuzzyMatching } from "./fuzzyMatcher";
import { logFieldMappingResults } from "./fieldMapLogger";

/**
 * Create a mapping between CSV headers and standardized field names
 * Uses exact matching first, then falls back to fuzzy matching
 */
export const createFieldMapping = (headers: string[]): Record<string, string> => {
  const mapping: Record<string, string> = {};
  const unmappedHeaders = new Set(headers);
  
  // Step 1: Process exact matches (case insensitive)
  headers.forEach(header => {
    const normalizedHeader = normalizeHeaderName(header);
    
    // Check for exact match in our field definitions
    for (const [standardField, fieldDef] of Object.entries(fieldMappingDefinitions)) {
      if (fieldDef.variations.some(variation => normalizeHeaderName(variation) === normalizedHeader)) {
        mapping[header] = standardField;
        unmappedHeaders.delete(header);
        break;
      }
    }
  });
  
  // Log the results of exact matching
  console.log(`Exact matching mapped ${headers.length - unmappedHeaders.size} of ${headers.length} fields`);
  console.log("Fields mapped by exact matching:", Object.entries(mapping).map(([original, mapped]) => `${original} -> ${mapped}`));
  
  // Step 2: For headers that didn't match exactly, try fuzzy matching
  if (unmappedHeaders.size > 0) {
    const unmappedArray = Array.from(unmappedHeaders);
    const fuzzyMatches = performFuzzyMatching(unmappedArray, fieldMappingDefinitions);
    
    // Apply fuzzy matches
    fuzzyMatches.forEach(match => {
      mapping[match.header] = match.standardField;
      unmappedHeaders.delete(match.header);
    });
    
    console.log(`Fuzzy matching mapped an additional ${fuzzyMatches.length} fields`);
    console.log("Fields mapped by fuzzy matching:", fuzzyMatches.map(match => `${match.header} -> ${match.standardField} (score: ${match.score})`));
  }
  
  // Step 3: For any remaining unmapped headers, copy as-is
  unmappedHeaders.forEach(header => {
    mapping[header] = header;
  });
  
  // Log special attention to important fields
  const importantFields = ['website', 'address'];
  importantFields.forEach(field => {
    const mappedFrom = Object.entries(mapping).find(([_, mapped]) => mapped === field);
    if (mappedFrom) {
      console.log(`Important field '${field}' mapped from original field '${mappedFrom[0]}'`);
    } else {
      console.warn(`Important field '${field}' was not mapped from any original field`);
    }
  });
  
  // Log the final mapping results
  logFieldMappingResults(mapping, headers, fieldMappingDefinitions, FIELD_CATEGORIES);
  
  return mapping;
};

/**
 * Apply field mapping to transform a row based on the mapping
 */
export const applyFieldMapping = (
  row: Record<string, any>, 
  fieldMapping: Record<string, string>
): Record<string, any> => {
  const mappedRow: Record<string, any> = {};
  
  // Apply mapping and transform data
  Object.entries(row).forEach(([originalField, value]) => {
    const targetField = fieldMapping[originalField] || originalField;
    
    // Don't overwrite existing values with empty ones
    if (mappedRow[targetField] && (!value || value === "")) {
      return;
    }
    
    // Special handling for website fields
    if (targetField === 'website' && value) {
      // Ensure website has proper formatting
      const formattedWebsite = value.toString().trim();
      if (formattedWebsite && !formattedWebsite.match(/^https?:\/\//i)) {
        mappedRow[targetField] = `https://${formattedWebsite}`;
      } else {
        mappedRow[targetField] = formattedWebsite;
      }
      return;
    }
    
    // Special handling for address fields
    if (targetField === 'address' && value) {
      mappedRow[targetField] = value.toString().replace(/\s+/g, " ").trim();
      return;
    }
    
    mappedRow[targetField] = value;
  });
  
  return mappedRow;
};

// Import and re-export functions from fieldNormalizer
import { getRequiredFields as getFieldsRequired, getRecommendedFields as getFieldsRecommended } from "./fieldNormalizer";

// Re-export functions with consistent signatures
export const getRequiredFields = (): string[] => {
  return getFieldsRequired(fieldMappingDefinitions);
};

export const getRecommendedFields = (): string[] => {
  return getFieldsRecommended(fieldMappingDefinitions);
};

export const validateRequiredFields = (fieldMapping: Record<string, string>): {
  valid: boolean;
  missingFields: string[];
} => {
  return validateFields(fieldMapping, fieldMappingDefinitions);
};
