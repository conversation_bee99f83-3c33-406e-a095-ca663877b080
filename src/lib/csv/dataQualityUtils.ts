
/**
 * Log data quality metrics and calculate completion percentages
 */
export const logDataQualityMetrics = (
  data: Record<string, any>[], 
  emptyFieldCounts: Record<string, number>
): Record<string, number> => {
  console.log(`Parsed ${data.length} rows of data with ${Object.values(emptyFieldCounts).reduce((a, b) => a + b, 0)} empty fields`);
  console.log("Field completion analysis:", emptyFieldCounts);
  
  // Calculate completion percentage for each field
  const completionStats: Record<string, number> = {};
  Object.keys(emptyFieldCounts).forEach(field => {
    const emptyCount = emptyFieldCounts[field];
    const totalCount = data.length;
    const completionPercentage = Math.round(((totalCount - emptyCount) / totalCount) * 100);
    completionStats[field] = completionPercentage;
  });
  
  console.log("Field completion percentages:", completionStats);
  return completionStats;
};

/**
 * Analyze CSV data quality and generate metrics report
 */
export const analyzeCSVDataQuality = (
  data: Record<string, any>[]
): {
  totalFields: number;
  emptyFields: number;
  completionPercentage: number;
  fieldCompletionStats: Record<string, { total: number; empty: number; percentage: number }>;
} => {
  if (data.length === 0) {
    return {
      totalFields: 0,
      emptyFields: 0,
      completionPercentage: 0,
      fieldCompletionStats: {}
    };
  }
  
  // Get all unique fields from data
  const allFields = new Set<string>();
  data.forEach(row => {
    Object.keys(row).forEach(field => allFields.add(field));
  });
  
  // Initialize counters
  const fieldStats: Record<string, { total: number; empty: number; percentage: number }> = {};
  let totalFields = 0;
  let emptyFields = 0;
  
  // Initialize field stats
  Array.from(allFields).forEach(field => {
    fieldStats[field] = { total: data.length, empty: 0, percentage: 0 };
  });
  
  // Count empty fields
  data.forEach(row => {
    Array.from(allFields).forEach(field => {
      totalFields++;
      if (!row[field] || String(row[field]).trim() === '') {
        fieldStats[field].empty++;
        emptyFields++;
      }
    });
  });
  
  // Calculate percentages
  Object.keys(fieldStats).forEach(field => {
    const stats = fieldStats[field];
    stats.percentage = Math.round(((stats.total - stats.empty) / stats.total) * 100);
  });
  
  const completionPercentage = Math.round(((totalFields - emptyFields) / totalFields) * 100);
  
  return {
    totalFields,
    emptyFields,
    completionPercentage,
    fieldCompletionStats: fieldStats
  };
};
