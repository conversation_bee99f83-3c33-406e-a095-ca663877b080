
/**
 * Parses a single CSV row, handling quoted values and commas within quotes
 */
export const parseCSVRow = (row: string): string[] => {
  // Better handling of CSV row parsing, including handling commas in quoted values
  const result: string[] = [];
  let cell = "";
  let inQuotes = false;
  
  for (let i = 0; i < row.length; i++) {
    const char = row[i];
    
    if (char === '"') {
      // Handle quotes
      if (inQuotes && i < row.length - 1 && row[i + 1] === '"') {
        // Double quotes inside quotes - add a single quote and skip the next character
        cell += '"';
        i++;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      // End of cell
      result.push(cell.trim());
      cell = "";
    } else {
      cell += char;
    }
  }
  
  // Add the last cell
  result.push(cell.trim());
  
  return result;
};
