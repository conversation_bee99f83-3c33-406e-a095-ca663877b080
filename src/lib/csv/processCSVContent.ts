
import { parseCSVRow } from "./parseCSVRow";
import { validateCSVData } from "./validation";
import { createFieldMapping, applyFieldMapping } from "./fieldMappingUtils";
import { detectFieldType } from "./formatters";
import { logDataQualityMetrics } from "./dataQualityUtils";

/**
 * Process the content of a CSV file
 */
export const processCSVContent = (text: string): {
  data: Record<string, any>[];
  columns: string[];
  duplicates: number;
  fieldMapping: Record<string, string>;
  fieldTypes: Record<string, string>;
} => {
  // Split the CSV by lines
  const lines = text.split(/\r\n|\n/);
  console.log(`Found ${lines.length} lines in CSV`);
  
  if (lines.length < 2) {
    throw new Error("CSV file must have at least a header row and one data row");
  }
  
  // Parse header row to get column names
  const headers = parseCSVRow(lines[0]);
  console.log("CSV headers:", headers);
  
  // Create field mapping from headers to standardized fields
  const fieldMapping = createFieldMapping(headers);
  console.log("Field mapping created:", fieldMapping);
  
  // Get standardized columns after mapping
  const standardizedColumns = Array.from(new Set(Object.values(fieldMapping)));
  
  // Validate required fields
  validateCSVData(headers, fieldMapping);
  
  // Parse and process data rows
  const { 
    data, 
    duplicateCount, 
    fieldTypes, 
    emptyFieldCounts 
  } = processDataRows(lines, headers, fieldMapping, standardizedColumns);
  
  // Log data quality metrics
  const completionStats = logDataQualityMetrics(data, emptyFieldCounts);
  
  console.log("Detected field types:", fieldTypes);
  console.log("Parsed data first item:", data.length > 0 ? data[0] : "No data");
  
  return { 
    data, 
    columns: standardizedColumns, 
    duplicates: duplicateCount,
    fieldMapping,
    fieldTypes
  };
};

/**
 * Process data rows from CSV
 */
const processDataRows = (
  lines: string[],
  headers: string[],
  fieldMapping: Record<string, string>,
  standardizedColumns: string[]
): {
  data: Record<string, any>[];
  duplicateCount: number;
  fieldTypes: Record<string, string>;
  emptyFieldCounts: Record<string, number>;
  invalidRowCount: number;
} => {
  // Track duplicates and verify field accuracy
  const existingIds = new Set<string>();
  const existingNames = new Set<string>();
  let duplicateCount = 0;
  let emptyFieldCounts: Record<string, number> = {};
  let fieldTypes: Record<string, string> = {};
  let invalidRowCount = 0;
  
  // Initialize empty field counters for standardized fields
  standardizedColumns.forEach(header => {
    emptyFieldCounts[header] = 0;
    // Set initial field type guesses based on field name
    const normalizedField = header.toLowerCase();
    if (normalizedField.includes('phone') || normalizedField.includes('tel')) {
      fieldTypes[header] = 'tel';
    } else if (normalizedField.includes('zip') || normalizedField.includes('postal')) {
      fieldTypes[header] = 'zip';
    } else if (normalizedField.includes('address') || normalizedField.includes('street')) {
      fieldTypes[header] = 'address';
    } else if (normalizedField.includes('web') || normalizedField.includes('url')) {
      fieldTypes[header] = 'url';
    } else if (normalizedField.includes('email') || normalizedField.includes('mail')) {
      fieldTypes[header] = 'email';
    } else {
      fieldTypes[header] = 'text';
    }
  });
  
  console.log("Initial field type guesses:", fieldTypes);
  
  // Parse data rows with field validation
  const data = lines.slice(1)
    .filter(line => line.trim() !== "") // Skip empty lines
    .map((line, index) => {
      const values = parseCSVRow(line);
      let row: Record<string, any> = {};
      
      // Populate fields from CSV
      headers.forEach((header, idx) => {
        row[header] = values[idx] || "";
      });
      
      // Apply field mapping to transform data to standard fields
      row = applyFieldMapping(row, fieldMapping);
      
      // Check if this row has a name
      if (!row.name || row.name.trim() === '') {
        console.warn(`Row ${index + 2} has no name, may be filtered later`, row);
      }
      
      // Count fields with actual data to detect empty rows
      let fieldsWithData = 0;
      
      // Track empty fields for data quality analysis
      standardizedColumns.forEach(field => {
        if (!row[field] || row[field] === "") {
          emptyFieldCounts[field]++;
        } else {
          fieldsWithData++;
          
          // Only detect field types from non-empty values
          const { type, formattedValue } = detectFieldType(field, row[field]);
          
          // Update field type if we found a more specific type
          if (type !== 'text' && fieldTypes[field] === 'text') {
            fieldTypes[field] = type;
            console.log(`Detected field type for ${field}: ${type}`);
          }
          
          // Apply the formatted value
          row[field] = formattedValue;
        }
      });
      
      // Check if this is essentially an empty row (less than 2 fields with data)
      if (fieldsWithData < 2) {
        console.warn(`Row ${index + 2} has only ${fieldsWithData} fields with data`);
        invalidRowCount++;
      }
      
      // Generate id if not present
      if (!row.id) {
        row.id = `brewery-${index}-${Date.now()}`;
      }
      
      // Check for duplicates - create unique IDs and names
      if (existingIds.has(row.id)) {
        const uniqueId = `brewery-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        console.log(`Duplicate ID found: ${row.id}, changed to ${uniqueId}`);
        row.id = uniqueId;
        duplicateCount++;
      }
      
      if (row.name && existingNames.has(row.name)) {
        const uniqueName = `${row.name} (${Math.floor(Math.random() * 1000)})`;
        console.log(`Duplicate name found: ${row.name}, changed to ${uniqueName}`);
        row.name = uniqueName;
        duplicateCount++;
      }
      
      existingIds.add(row.id);
      if (row.name) existingNames.add(row.name);
      
      return row;
    });
    
  console.log(`Found ${invalidRowCount} potentially invalid rows (less than 2 fields with data)`);
  
  return {
    data,
    duplicateCount,
    fieldTypes,
    emptyFieldCounts,
    invalidRowCount
  };
};
