
/**
 * Fuzzy matching utilities for field mapping
 */

import { normalizeHeaderName } from './fieldNormalizer';
import { calculateStringSimilarity } from './stringUtils';

// Minimum similarity threshold for fuzzy matching
const SIMILARITY_THRESHOLD = 0.7;

/**
 * Perform fuzzy matching on unmapped headers
 */
export const performFuzzyMatching = (
  unmappedHeaders: string[],
  fieldMappingDefinitions: Record<string, { variations: string[] }>
): Array<{header: string, standardField: string, score: number}> => {
  const matches: Array<{header: string, standardField: string, score: number}> = [];
  
  unmappedHeaders.forEach(header => {
    const normalizedHeader = normalizeHeaderName(header);
    let bestMatch: {field: string, score: number} | null = null;
    
    // Check against all field variations
    for (const [standardField, fieldDef] of Object.entries(fieldMappingDefinitions)) {
      for (const variation of fieldDef.variations) {
        const normalizedVariation = normalizeHeaderName(variation);
        
        // Check for substring matching
        if (normalizedHeader.includes(normalizedVariation) || 
            normalizedVariation.includes(normalizedHeader)) {
          // Calculate how much of the string is matched
          const matchLength = Math.min(normalizedHeader.length, normalizedVariation.length);
          const maxLength = Math.max(normalizedHeader.length, normalizedVariation.length);
          const score = matchLength / maxLength;
          
          if (!bestMatch || score > bestMatch.score) {
            bestMatch = { field: standardField, score };
          }
        } else {
          // Use string similarity for non-substring matches
          const score = calculateStringSimilarity(normalizedHeader, normalizedVariation);
          if (score > SIMILARITY_THRESHOLD && (!bestMatch || score > bestMatch.score)) {
            bestMatch = { field: standardField, score };
          }
        }
      }
    }
    
    // Add to matches if we found a good match
    if (bestMatch && bestMatch.score > SIMILARITY_THRESHOLD) {
      matches.push({
        header,
        standardField: bestMatch.field,
        score: bestMatch.score
      });
      console.log(`Fuzzy matched "${header}" to "${bestMatch.field}" with score ${bestMatch.score.toFixed(2)}`);
    }
  });
  
  // Sort matches by score (highest first) to handle potential conflicts
  return matches.sort((a, b) => b.score - a.score);
};
