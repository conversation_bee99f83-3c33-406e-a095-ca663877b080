
/**
 * String similarity utilities for field mapping
 */

/**
 * Calculate string similarity using Levenshtein distance algorithm
 * Returns a value between 0 (completely different) and 1 (identical)
 */
export const calculateStringSimilarity = (str1: string, str2: string): number => {
  // If either string is empty, return 0
  if (!str1.length || !str2.length) return 0;
  
  // If strings are identical, return 1
  if (str1 === str2) return 1;
  
  // Calculate Levenshtein distance
  const distance = levenshteinDistance(str1, str2);
  
  // Convert to similarity score (0-1)
  const maxLength = Math.max(str1.length, str2.length);
  return 1 - distance / maxLength;
};

/**
 * Calculate Levenshtein distance between two strings
 * Measures the minimum number of single-character edits required to change one string into another
 */
const levenshteinDistance = (str1: string, str2: string): number => {
  const track = Array(str2.length + 1).fill(null).map(() => 
    Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i += 1) {
    track[0][i] = i;
  }
  
  for (let j = 0; j <= str2.length; j += 1) {
    track[j][0] = j;
  }
  
  for (let j = 1; j <= str2.length; j += 1) {
    for (let i = 1; i <= str1.length; i += 1) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      track[j][i] = Math.min(
        track[j][i - 1] + 1, // deletion
        track[j - 1][i] + 1, // insertion
        track[j - 1][i - 1] + indicator, // substitution
      );
    }
  }
  
  return track[str2.length][str1.length];
};

/**
 * Simpler similarity check based on shared words
 * Useful for multi-word field names
 */
export const sharedWordSimilarity = (str1: string, str2: string): number => {
  // Split strings into words and filter out empty strings
  const words1 = str1.toLowerCase().split(/\W+/).filter(Boolean);
  const words2 = str2.toLowerCase().split(/\W+/).filter(Boolean);
  
  if (!words1.length || !words2.length) return 0;
  
  // Count shared words
  const set1 = new Set(words1);
  const sharedWords = words2.filter(word => set1.has(word)).length;
  
  // Calculate similarity based on shared words relative to total unique words
  const uniqueWords = new Set([...words1, ...words2]).size;
  return sharedWords / uniqueWords;
};
