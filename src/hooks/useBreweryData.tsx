
import { useState, useEffect, useCallback } from 'react';
import { Brewery, SocialLinks } from '@/types/brewery.patch'; // Use patch version
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/auth';
import { toast } from '@/hooks/use-toast';
import { mapBreweryFromDb, mapBreweryToDb } from './brewery/utils/breweryDataMappers';

export const useBreweryData = (breweryId?: string) => {
  const [brewery, setBrewery] = useState<Brewery | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchBreweryData = useCallback(async () => {
    // Use either the passed breweryId or get it from the user object
    const targetBreweryId = breweryId || user?.breweryId;
    
    if (!targetBreweryId) {
      setIsLoading(false);
      setError("No brewery ID provided.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      let { data, error } = await supabase
        .from('breweries')
        .select('*')
        .eq('id', targetBreweryId)
        .single();

      if (error) {
        throw error;
      }

      // Use the mapper function to convert DB data to our app format
      const breweryData = mapBreweryFromDb(data);
      setBrewery(breweryData);
    } catch (err: any) {
      console.error('Error fetching brewery data:', err);
      setError(err.message || 'Error fetching brewery data');
      
      toast({
        title: "Error loading brewery",
        description: err.message || "Failed to load brewery data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [breweryId, user]);

  useEffect(() => {
    fetchBreweryData();
  }, [fetchBreweryData]);

  const updateBrewery = async (updatedData: Partial<Brewery>) => {
    if (!brewery || !brewery.id) {
      return { success: false, message: 'No brewery to update' };
    }

    setIsLoading(true);
    try {
      // Convert to database format
      const dbData = mapBreweryToDb(updatedData);

      const { error } = await supabase
        .from('breweries')
        .update(dbData)
        .eq('id', brewery.id);

      if (error) throw error;

      // Update local state
      setBrewery(prev => prev ? { ...prev, ...updatedData } : null);

      toast({
        title: "Update successful",
        description: "Your brewery information has been updated.",
      });

      return { success: true };
    } catch (err: any) {
      console.error('Error updating brewery:', err);
      
      toast({
        title: "Update failed",
        description: err.message || "Failed to update brewery information",
        variant: "destructive",
      });
      
      return { success: false, message: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  return { 
    brewery, 
    isLoading, 
    error, 
    updateBrewery,
    fetchBrewery: fetchBreweryData 
  };
};
