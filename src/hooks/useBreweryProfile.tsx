
import { useState } from 'react';
import { AuthUser, Brewery, SocialLinks } from '@/types/brewery.patch'; // Use patch version
import { ProfileFormValues } from '@/components/brewery/profile/ProfileFormSchema';
import { useBreweryData } from './brewery/useBreweryData';
import { useTestBrewery } from './brewery/useTestBrewery';
import { useProfileSubmit } from './brewery/useProfileSubmit';
import { useBreweryImageUpload } from './brewery/useBreweryImageUpload';
import { useSocialLinks } from './brewery/useSocialLinks';

export const useBreweryProfile = (user: AuthUser | null) => {
  // Check if user is null
  if (!user) {
    // Return safe defaults for all values
    return {
      brewery: null,
      isLoading: false,
      error: "User not available",
      socialLinks: {},
      uploadProgress: 0,
      fileErrors: [],
      handleProfileSubmit: () => Promise.resolve(),
      handleCreateTestBrewery: () => Promise.resolve(),
      handleImageUpload: () => Promise.resolve(""),
      handleSocialLinksUpdate: () => {},
      handleAvatarUpdate: () => {},
      refreshBrewery: () => Promise.resolve()
    };
  }
  
  // Initialize our own state for brewery
  const [breweryState, setBreweryState] = useState<Brewery | null>(null);
  
  // Use the smaller, focused hooks
  const { brewery, isLoading, error, updateBrewery, fetchBrewery } = useBreweryData(user.breweryId);
  const { isCreating, handleCreateTestBrewery } = useTestBrewery(user, setBreweryState);
  const { isSubmitting, handleProfileSubmit } = useProfileSubmit(brewery, setBreweryState);
  const { 
    isUploading, 
    uploadProgress, 
    fileErrors, 
    handleImageUploadWrapper 
  } = useBreweryImageUpload(brewery, setBreweryState);
  const { socialLinks, handleSocialLinksUpdate } = useSocialLinks();
  
  // Combine loading states
  const combinedIsLoading = isLoading || isCreating || isSubmitting || isUploading;
  
  // Use the data from our local state or from the hook
  const displayBrewery = breweryState || brewery;

  // Handle avatar update
  const handleAvatarUpdate = (avatarUrl: string) => {
    if (displayBrewery) {
      setBreweryState({
        ...displayBrewery,
        avatar: avatarUrl
      });
    }
  };
  
  // Create a wrapper for handleImageUploadWrapper to match expected signature
  const handleImageUpload = (type: string, file: File) => {
    return handleImageUploadWrapper(type, file);
  };
  
  return {
    brewery: displayBrewery,
    isLoading: combinedIsLoading,
    error,
    socialLinks,
    uploadProgress,
    fileErrors,
    handleProfileSubmit,
    handleCreateTestBrewery,
    handleImageUpload,
    handleSocialLinksUpdate,
    handleAvatarUpdate,
    refreshBrewery: fetchBrewery
  };
};
