
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Brewery } from '@/types/brewery.patch'; // Use patch version that includes verified and brewery_type
import { breweryFormSchema, BreweryFormValues } from '@/components/brewery/schemas/breweryFormSchema';

interface UseBreweryProfileFormProps {
  breweryId?: string;
  userId?: string;
  createMode?: boolean;
}

export const useBreweryProfileForm = ({ breweryId, userId, createMode = false }: UseBreweryProfileFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [brewery, setBrewery] = useState<Brewery | null>(null);

  const form = useForm<BreweryFormValues>({
    resolver: zodResolver(breweryFormSchema),
    defaultValues: {
      name: '',
      description: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      phone: '',
      website: '',
      email: '',
      logo: '',
      featureImage: '',
      brewery_type: 'micro', // Set default value for brewery_type
    },
  });

  const fetchBreweryData = async (id: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('breweries')
        .select('*')
        .eq('id', id)
        .maybeSingle();
      
      if (error) {
        throw error;
      }
      
      if (data) {
        // Process social_links to ensure it's a valid object or create a default one
        let socialLinksData = { facebook: '', instagram: '', twitter: '' };
        
        if (data.social_links) {
          try {
            // If social_links is a string, try to parse it
            if (typeof data.social_links === 'string') {
              socialLinksData = JSON.parse(data.social_links);
            }
            // If it's an object, use it directly
            else if (typeof data.social_links === 'object' && !Array.isArray(data.social_links)) {
              // Safe access of properties with optional chaining and fallbacks
              const links = data.social_links as Record<string, any>;
              socialLinksData = {
                facebook: links.facebook || '',
                instagram: links.instagram || '',
                twitter: links.twitter || ''
              };
            }
          } catch (e) {
            console.error('Error parsing social links:', e);
          }
        }
        
        // Create the brewery object with correct type field and verified field
        const breweryData: Brewery = {
          id: data.id,
          name: data.name || '',
          address: data.address || '',
          city: data.city || '',
          state: data.state || '',
          zip: data.zip || '',
          phone: data.phone || '',
          website: data.website || '',
          description: data.description || '',
          logo: data.logo || '',
          featureImage: data.feature_image || '',
          email: data.email || '',
          socialLinks: socialLinksData,
          claimable: data.claimable || false,
          verificationOpen: data.verification_open || false,
          claimed: data.claimed || false,
          verified: Boolean(data.verified) || false, // Ensure verified is a boolean
          brewery_type: data.brewery_type || 'micro', // Ensure brewery_type is properly set
          avatar: data.avatar || '',
          followerCount: data.follower_count || 0,
          likeCount: data.like_count || 0,
          isFollowing: false,
          isLiked: false
        };
        
        setBrewery(breweryData);
        
        // Reset form with the loaded data
        form.reset({
          name: data.name || '',
          description: data.description || '',
          address: data.address || '',
          city: data.city || '',
          state: data.state || '',
          zip: data.zip || '',
          phone: data.phone || '',
          website: data.website || '',
          email: data.email || '',
          logo: data.logo || '',
          featureImage: data.feature_image || '',
          brewery_type: data.brewery_type || 'micro', // Ensure brewery_type is correctly initialized
        });
      }
    } catch (error) {
      console.error('Error fetching brewery data:', error);
      toast({
        title: "Error fetching brewery data",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: BreweryFormValues) => {
    if (!breweryId && !createMode) {
      toast({
        title: "Error",
        description: "No brewery ID associated with your account",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    try {
      if (breweryId) {
        const { error } = await supabase
          .from('breweries')
          .update({
            name: data.name,
            description: data.description,
            address: data.address,
            city: data.city,
            state: data.state,
            zip: data.zip,
            phone: data.phone,
            website: data.website,
            email: data.email,
            logo: data.logo,
            feature_image: data.featureImage,
            brewery_type: data.brewery_type, // Include brewery_type in the update
          })
          .eq('id', breweryId);
        
        if (error) {
          throw error;
        }
        
        await fetchBreweryData(breweryId);
        
        toast({
          title: "Profile updated",
          description: "Your brewery profile has been updated successfully",
        });
      }
    } catch (error) {
      console.error('Error updating brewery profile:', error);
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (breweryId) {
      fetchBreweryData(breweryId);
    }
  }, [breweryId]);

  return {
    form,
    isLoading,
    brewery,
    onSubmit
  };
};
