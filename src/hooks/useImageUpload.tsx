
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

// Define allowed file types and max size
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/gif"];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const useImageUpload = () => {
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [fileErrors, setFileErrors] = useState<Record<string, string>>({});

  const validateFile = (file: File, field: string): boolean => {
    // Check file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setFileErrors({
        ...fileErrors,
        [field]: `Invalid file type. Allowed types: JPG, PNG, GIF`
      });
      return false;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setFileErrors({
        ...fileErrors,
        [field]: `File too large. Maximum size: 5MB`
      });
      return false;
    }

    // Clear previous error if exists
    if (fileErrors[field]) {
      const newErrors = { ...fileErrors };
      delete newErrors[field];
      setFileErrors(newErrors);
    }
    
    return true;
  };

  const handleFileUpload = (
    field: string, 
    file: File,
    onInputChange: (key: string, value: any) => void
  ) => {
    if (!file) return;

    if (!validateFile(file, field)) {
      return;
    }

    // Reset progress
    setUploadProgress(prev => ({ ...prev, [field]: 0 }));
    
    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        const currentProgress = prev[field] || 0;
        const newProgress = Math.min(currentProgress + 10, 100);
        
        if (newProgress === 100) {
          clearInterval(interval);
          
          // Create a local URL for the file for preview
          const fileUrl = URL.createObjectURL(file);
          onInputChange(field, fileUrl);

          // Store the actual file in the editedValues for later processing
          onInputChange(`${field}File`, file);
          
          toast({
            title: "File upload complete",
            description: `${file.name} has been uploaded successfully`,
          });
        }
        
        return { ...prev, [field]: newProgress };
      });
    }, 200); // Update every 200ms for smooth progress
  };

  return {
    uploadProgress,
    fileErrors,
    handleFileUpload
  };
};
