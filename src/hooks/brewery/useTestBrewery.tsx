
import { useState } from 'react';
import { AuthUser, Brewery } from '@/types/brewery.patch'; // Use patch version
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export const useTestBrewery = (
  user: AuthUser,
  setBreweryState: React.Dispatch<React.SetStateAction<Brewery | null>>
) => {
  const [isCreating, setIsCreating] = useState(false);
  
  const handleCreateTestBrewery = async () => {
    if (!user || user.role !== 'admin') {
      toast({
        title: "Permission denied",
        description: "Only administrators can create test breweries",
        variant: "destructive",
      });
      return;
    }
    
    setIsCreating(true);
    try {
      // Create a unique ID for the test brewery
      const breweryId = `test-brewery-${Date.now()}`;
      
      // Create the test brewery in Supabase
      const { error } = await supabase
        .from('breweries')
        .insert({
          id: breweryId,
          name: `Test Brewery (${user.email})`,
          description: "This is a test brewery created by an administrator",
          city: "Test City",
          state: "Test State",
          address: "123 Test Street",
          phone: "************",
          website: "https://testbrewery.com",
          claimable: false,
          claimed: true,
          verification_open: true,
          social_links: { facebook: '', twitter: '', instagram: '' },
          brewery_type: 'micro', // Add default brewery type
          verified: false, // Add verified field explicitly as false
          follower_count: 0,
          like_count: 0
        });
        
      if (error) throw error;
      
      // Update the user's brewery ID
      const { error: userError } = await supabase
        .from('profiles')
        .update({ brewery_id: breweryId })
        .eq('id', user.id);
        
      if (userError) throw userError;
      
      // Create a new brewery object for local state
      const newBrewery: Brewery = {
        id: breweryId,
        name: `Test Brewery (${user.email})`,
        description: "This is a test brewery created by an administrator",
        city: "Test City",
        state: "Test State",
        address: "123 Test Street",
        phone: "************",
        website: "https://testbrewery.com",
        zip: '',
        email: '',
        logo: '',
        featureImage: '',
        claimable: false,
        claimed: true,
        verificationOpen: true,
        socialLinks: { facebook: '', twitter: '', instagram: '' },
        brewery_type: 'micro', // Add default brewery type
        verified: false, // Add verified field explicitly
        avatar: '',
        followerCount: 0,
        likeCount: 0,
        isFollowing: false,
        isLiked: false
      };
      
      // Update local state
      setBreweryState(newBrewery);
      
      toast({
        title: "Test brewery created",
        description: "A new test brewery has been created for your account",
      });
      
    } catch (err: any) {
      console.error('Error creating test brewery:', err);
      
      toast({
        title: "Creation failed",
        description: err.message || "Failed to create test brewery",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };
  
  return {
    isCreating,
    handleCreateTestBrewery
  };
};
