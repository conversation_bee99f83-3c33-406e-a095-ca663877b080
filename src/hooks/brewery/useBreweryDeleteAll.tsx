
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface UseBreweryDeleteAllProps {
  setBreweries: (breweries: any[]) => void;
  onSuccess?: () => void;
}

export const useBreweryDeleteAll = ({ setBreweries, onSuccess }: UseBreweryDeleteAllProps) => {
  const [isDeleteAllLoading, setIsDeleteAllLoading] = useState(false);

  const handleDeleteAllBreweries = async () => {
    setIsDeleteAllLoading(true);
    
    try {
      // First, remove all brewery_id references from profiles
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ brewery_id: null })
        .not('brewery_id', 'is', null);
      
      if (profileError) {
        throw new Error(profileError.message);
      }
      
      // Now we can delete all breweries
      const { error } = await supabase
        .from('breweries')
        .delete()
        .not('id', 'is', null);
      
      if (error) {
        throw new Error(error.message);
      }
      
      // Update state
      setBreweries([]);
      
      toast({
        title: "All breweries deleted",
        description: "All brewery data has been removed successfully",
      });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error deleting breweries:', err);
      
      toast({
        title: "Error deleting data",
        description: err.message || "Failed to delete brewery data",
        variant: "destructive",
      });
    } finally {
      setIsDeleteAllLoading(false);
    }
  };

  return {
    isDeleteAllLoading,
    handleDeleteAllBreweries
  };
};
