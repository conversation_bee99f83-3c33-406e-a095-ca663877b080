
import { useState, useEffect, useMemo } from "react";
import { Brewery } from "@/types/brewery";

export const useBreweryState = (
  filteredBreweries: Brewery[],
  searchTerm?: string,
  filterColumn?: string,
  stateFilter?: string,
  verificationFilter?: string
) => {
  // Editing state
  const [editingBrewery, setEditingBrewery] = useState<string | null>(null);
  const [breweryToDelete, setBreweryToDelete] = useState<Brewery | null>(null);
  const [editedValues, setEditedValues] = useState<Partial<Brewery>>({});
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Sorting state
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  
  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterColumn, stateFilter, verificationFilter]);
  
  // Calculate total pages
  const totalPages = Math.ceil(filteredBreweries.length / itemsPerPage);
  
  // Handle sorting
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key, direction });
  };
  
  // Get the current page data
  const displayData = useMemo(() => {
    // Sort the data if sortConfig is set
    const sortedData = [...filteredBreweries];
    
    if (sortConfig) {
      sortedData.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof Brewery] || '';
        const bValue = b[sortConfig.key as keyof Brewery] || '';
        
        // Handle string comparison
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        // Handle number comparison
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortConfig.direction === 'asc' 
            ? aValue - bValue
            : bValue - aValue;
        }
        
        // Default comparison for other types
        return 0;
      });
    }
    
    // Apply pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredBreweries, currentPage, itemsPerPage, sortConfig]);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Edit handler
  const handleEdit = (id: string) => {
    setEditingBrewery(id);
    const brewery = filteredBreweries.find(b => b.id === id);
    if (brewery) {
      setEditedValues({ ...brewery });
    }
  };
  
  // Save handler
  const handleSave = (id: string) => {
    // This would normally update the data in the backend
    setEditingBrewery(null);
    setEditedValues({});
  };
  
  // Delete handler
  const handleDelete = (brewery: Brewery) => {
    setBreweryToDelete(brewery);
  };
  
  // Confirm delete handler
  const confirmDeleteBrewery = () => {
    // This would normally delete the data in the backend
    setBreweryToDelete(null);
  };
  
  // Input change handler
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedValues(prev => ({ ...prev, [name]: value }));
  };
  
  return {
    editingBrewery,
    setEditingBrewery,
    breweryToDelete,
    setBreweryToDelete,
    editedValues,
    setEditedValues,
    currentPage,
    setCurrentPage,
    sortConfig,
    setSortConfig,
    handleEdit,
    handleDelete,
    handleSave,
    handleInputChange,
    confirmDeleteBrewery,
    handleSort,
    displayData,
    totalPages,
    handlePageChange
  };
};
