
import { useState, useCallback } from 'react';
import { ImportProgress, UpdateProgress, UpdateChunkProgress, UpdateRowProgress } from './types/importTypes';

interface UseImportProgressReturn {
  progress: ImportProgress;
  updateProgress: UpdateProgress;
  updateChunkProgress: UpdateChunkProgress;
  updateRowProgress: UpdateRowProgress;
  resetProgress: () => void;
  currentChunk?: number;
  totalChunks?: number;
  rowsProcessed?: number;
  totalRows?: number;
}

/**
 * Hook to manage import progress state and updates
 * This tracks overall progress, chunk progress, and row processing progress
 */
export const useImportProgress = (): UseImportProgressReturn => {
  const [progress, setProgress] = useState<ImportProgress>({
    percentage: 0,
  });

  /**
   * Reset progress to initial state
   */
  const resetProgress = useCallback(() => {
    setProgress({
      percentage: 0,
    });
  }, []);

  /**
   * Update the overall progress percentage
   * @param progressValue - Progress percentage (0-100)
   */
  const updateProgress = useCallback((progressValue: number) => {
    setProgress(prev => ({
      ...prev,
      percentage: Math.min(Math.max(0, progressValue), 100),
    }));
  }, []);

  /**
   * Update the current chunk processing information
   * @param current - Current chunk being processed
   * @param total - Total number of chunks
   */
  const updateChunkProgress = useCallback((current: number, total: number) => {
    setProgress(prev => {
      // Calculate overall progress based on chunk progress
      const calculatedProgress = Math.min(
        Math.floor((current / total) * 100),
        99 // Cap at 99% until completely done
      );

      return {
        ...prev,
        percentage: calculatedProgress,
        currentChunk: current,
        totalChunks: total,
      };
    });
  }, []);

  /**
   * Update the row processing information
   * @param processed - Number of rows processed
   * @param total - Total number of rows
   */
  const updateRowProgress = useCallback((processed: number, total: number) => {
    setProgress(prev => {
      return {
        ...prev,
        rowsProcessed: processed,
        totalRows: total,
      };
    });
  }, []);

  return {
    progress,
    updateProgress,
    updateChunkProgress,
    updateRowProgress,
    resetProgress,
    currentChunk: progress.currentChunk,
    totalChunks: progress.totalChunks,
    rowsProcessed: progress.rowsProcessed,
    totalRows: progress.totalRows
  };
};
