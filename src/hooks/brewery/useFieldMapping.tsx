
import { useState } from "react";
import { validateRequiredFields } from "@/lib/csv";
import { toast } from "@/hooks/use-toast";

export const useFieldMapping = () => {
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [fieldTypes, setFieldTypes] = useState<Record<string, string>>({});

  const updateFieldMapping = (mappedFields: Record<string, string> | undefined) => {
    if (!mappedFields || Object.keys(mappedFields).length === 0) {
      console.warn("No field mapping received");
      return false;
    }

    console.log("Setting field mapping:", mappedFields);
    setFieldMapping(mappedFields);
    
    // Validate required fields
    const { valid, missingFields } = validateRequiredFields(mappedFields);
    if (!valid) {
      const missingFieldsMsg = `CSV missing required fields: ${missingFields.join(', ')}`;
      console.error(missingFieldsMsg);
      toast({
        title: "Invalid CSV format",
        description: missingFieldsMsg,
        variant: "destructive"
      });
      return false;
    }
    
    // Count the fields that were successfully mapped (different from original)
    const mappedFieldCount = Object.entries(mappedFields)
      .filter(([original, mapped]) => original !== mapped).length;
      
    if (mappedFieldCount > 0) {
      toast({
        title: "Smart field mapping applied",
        description: `${mappedFieldCount} fields were automatically mapped to standard fields`,
      });
    }
    
    return true;
  };

  const updateFieldTypes = (detectedTypes: Record<string, string> | undefined) => {
    if (!detectedTypes || Object.keys(detectedTypes).length === 0) {
      console.warn("No field types detected");
      return;
    }

    console.log("Setting field types:", detectedTypes);
    setFieldTypes(detectedTypes);
    
    // Count special field types that were detected
    const specialFields = Object.entries(detectedTypes)
      .filter(([field, type]) => type !== 'text').length;
      
    if (specialFields > 0) {
      toast({
        title: "Field types detected",
        description: `${specialFields} special field types detected (phone, zip, address, etc.)`,
      });
    }
  };

  return {
    fieldMapping,
    fieldTypes,
    updateFieldMapping,
    updateFieldTypes
  };
};
