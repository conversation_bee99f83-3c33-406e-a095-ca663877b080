
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export const useBreweryLikes = (
  breweryId: string, 
  userId: string | undefined,
  isLiked: boolean,
  setIsLiked: (value: boolean) => void,
  setLikeCount: (updater: (prev: number) => number) => void
) => {
  const toggleLike = useCallback(async () => {
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to like breweries",
        variant: "destructive",
      });
      return;
    }

    try {
      if (isLiked) {
        // Unlike
        const { error } = await supabase
          .from('brewery_likes')
          .delete()
          .eq('brewery_id', breweryId)
          .eq('user_id', userId);
          
        if (error) throw error;
        
        setIsLiked(false);
        setLikeCount(prev => Math.max(0, prev - 1));
        
        toast({
          title: "Unliked",
          description: "You no longer like this brewery",
        });
      } else {
        // Like
        const { error } = await supabase
          .from('brewery_likes')
          .insert({
            brewery_id: breweryId,
            user_id: userId
          });
          
        if (error) throw error;
        
        setIsLiked(true);
        setLikeCount(prev => prev + 1);
        
        toast({
          title: "Liked",
          description: "You now like this brewery",
        });
      }
    } catch (error: any) {
      console.error('Error toggling like status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update like status",
        variant: "destructive",
      });
    }
  }, [breweryId, userId, isLiked, setIsLiked, setLikeCount]);

  return { toggleLike };
};
