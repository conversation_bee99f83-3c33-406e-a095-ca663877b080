
import { useState, useMemo, useEffect } from "react";
import { Brewery } from "@/types/brewery.patch";

export interface UseBreweryPaginationProps {
  breweries: Brewery[];
  filteredBreweries?: Brewery[];
  itemsPerPage?: number;
  initialPage?: number;
}

export const useBreweryPagination = ({ 
  breweries, 
  filteredBreweries,
  itemsPerPage: propsItemsPerPage = 10,
  initialPage = 1
}: UseBreweryPaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [itemsPerPage, setItemsPerPage] = useState(propsItemsPerPage);
  
  // Use filteredBreweries if provided, otherwise use breweries
  const breweriesToUse = filteredBreweries || breweries;

  // Calculate total items and pages
  const totalItems = useMemo(() => breweriesToUse.length, [breweriesToUse]);
  const totalPages = useMemo(() => 
    Math.max(1, Math.ceil(totalItems / itemsPerPage)), 
    [totalItems, itemsPerPage]
  );
  
  // Update itemsPerPage when it changes from props
  useEffect(() => {
    if (propsItemsPerPage !== itemsPerPage) {
      setItemsPerPage(propsItemsPerPage);
    }
  }, [propsItemsPerPage]);

  // Reset to page 1 if current page is out of bounds after filtering or changing page size
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [totalPages, currentPage]);

  // Get paginated data
  const displayData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return breweriesToUse.slice(startIndex, endIndex);
  }, [breweriesToUse, currentPage, itemsPerPage]);

  // Page change handler
  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  // Page size change handler
  const handlePageSizeChange = (size: number) => {
    setItemsPerPage(size);
    // Reset to first page when changing page size to avoid empty results
    setCurrentPage(1);
  };

  return {
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage: handlePageSizeChange,
    displayData,
    totalPages,
    totalItems,
    handlePageChange,
    pageSize: itemsPerPage, // For compatibility with existing code
    changePage: handlePageChange, // For compatibility with existing code
  };
};
