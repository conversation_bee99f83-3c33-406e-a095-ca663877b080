
import { useState } from 'react';
import { Brewery } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { mapBreweryToDb } from './utils/breweryDataMappers';

interface UpdateBreweryResult {
  success: boolean;
  message?: string;
}

interface UseBreweryUpdateReturn {
  isUpdating: boolean;
  updateBrewery: (brewery: Brewery, updatedData: Partial<Brewery>) => Promise<UpdateBreweryResult>;
}

export const useBreweryUpdate = (setBrewery: (brewery: Brewery | null) => void): UseBreweryUpdateReturn => {
  const [isUpdating, setIsUpdating] = useState(false);

  const updateBrewery = async (brewery: Brewery, updatedData: Partial<Brewery>): Promise<UpdateBreweryResult> => {
    if (!brewery || !brewery.id) {
      return { success: false, message: 'No brewery to update' };
    }

    setIsUpdating(true);
    try {
      // Convert to database format
      const dbData = mapBreweryToDb(updatedData);

      const { error } = await supabase
        .from('breweries')
        .update(dbData)
        .eq('id', brewery.id);

      if (error) throw error;

      // Update local state
      setBrewery({ ...brewery, ...updatedData });

      toast({
        title: "Update successful",
        description: "Your brewery information has been updated.",
      });

      return { success: true };
    } catch (err: any) {
      console.error('Error updating brewery:', err);
      
      toast({
        title: "Update failed",
        description: err.message || "Failed to update brewery information",
        variant: "destructive",
      });
      
      return { success: false, message: err.message };
    } finally {
      setIsUpdating(false);
    }
  };

  return { isUpdating, updateBrewery };
};
