
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useInteractionStatus = (
  breweryId: string,
  userId: string | undefined,
  setIsFollowing: (value: boolean) => void,
  setIsLiked: (value: boolean) => void,
  setFollowerCount: (value: number) => void,
  setLikeCount: (value: number) => void,
  setIsLoading: (value: boolean) => void
) => {
  const checkInteractionStatus = useCallback(async () => {
    if (!userId || !breweryId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Check if user is following the brewery
      const { data: followData, error: followError } = await supabase
        .from('brewery_followers')
        .select('*')
        .eq('brewery_id', breweryId)
        .eq('user_id', userId)
        .maybeSingle();

      if (followError) throw followError;
      
      // Check if user has liked the brewery
      const { data: likeData, error: likeError } = await supabase
        .from('brewery_likes')
        .select('*')
        .eq('brewery_id', breweryId)
        .eq('user_id', userId)
        .maybeSingle();
        
      if (likeError) throw likeError;

      // Get counts
      const { data: breweryData, error: breweryError } = await supabase
        .from('breweries')
        .select('follower_count, like_count')
        .eq('id', breweryId)
        .single();
        
      if (breweryError) throw breweryError;

      setIsFollowing(!!followData);
      setIsLiked(!!likeData);
      setFollowerCount(breweryData.follower_count || 0);
      setLikeCount(breweryData.like_count || 0);
    } catch (error: any) {
      console.error('Error checking interaction status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [breweryId, userId, setIsFollowing, setIsLiked, setFollowerCount, setLikeCount, setIsLoading]);

  return { checkInteractionStatus };
};
