
import { useState } from 'react';
import { Brewery } from '@/types/brewery.patch';
import { useNavigate } from 'react-router-dom';

interface UseBreweryHandlersProps {
  setCurrentPage: (page: number) => void;
  setViewMode?: (mode: 'table' | 'grid') => void;
  setShowDeleteDialog?: (show: boolean) => void;
  setSelectedBrewery?: (brewery: Brewery | null) => void;
  setFilters?: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  currentPage?: number;
  itemsPerPage?: number;
  totalPages?: number;
  filteredBreweries?: Brewery[];
  onRefresh?: () => Promise<void>;
  setSortConfig?: (sortConfig: { key: string; direction: 'asc' | 'desc' } | null) => void;
  sortConfig?: { key: string; direction: 'asc' | 'desc' } | null;
}

export const useBreweryHandlers = ({
  setCurrentPage,
  setViewMode,
  setShowDeleteDialog,
  setSelectedBrewery,
  setFilters,
  currentPage = 1,
  itemsPerPage = 10,
  totalPages = 1,
  filteredBreweries = [],
  onRefresh,
  setSortConfig,
  sortConfig
}: UseBreweryHandlersProps) => {
  const navigate = useNavigate();
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handleViewModeChange = (mode: 'table' | 'grid') => {
    setViewMode && setViewMode(mode);
  };
  
  const handleSort = (key: string) => {
    if (!setSortConfig || !sortConfig) return;
    
    let direction: 'asc' | 'desc' = 'asc';
    
    if (sortConfig?.key === key) {
      direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
    }
    
    setSortConfig({ key, direction });
  };
  
  const handleFilterChange = (key: string, value: string) => {
    if (!setFilters) return;
    
    setFilters(prevFilters => ({
      ...prevFilters,
      [key]: value
    }));
    setCurrentPage(1); // Reset to first page when filters change
  };
  
  const handleDeleteClick = (brewery: Brewery) => {
    if (setSelectedBrewery && setShowDeleteDialog) {
      setSelectedBrewery(brewery);
      setShowDeleteDialog(true);
    }
  };
  
  const handleEditClick = (id: string) => {
    navigate(`/brewery/${id}`);
  };
  
  const handleDeleteConfirm = async () => {
    if (setShowDeleteDialog && setSelectedBrewery && onRefresh) {
      // Handle delete confirmation
      setShowDeleteDialog(false);
      setSelectedBrewery(null);
      await onRefresh();
    }
  };
  
  const handleSearch = (term: string) => {
    if (!setFilters) return;
    
    setFilters(prevFilters => ({
      ...prevFilters,
      searchTerm: term
    }));
    setCurrentPage(1);
  };

  return {
    handlePageChange,
    handleSort,
    handleFilterChange,
    handleViewModeChange,
    handleDeleteClick,
    handleEditClick,
    handleDeleteConfirm,
    handleSearch
  };
};
