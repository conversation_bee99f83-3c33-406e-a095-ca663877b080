
import { useState, useCallback } from "react";
import { toast } from "@/hooks/use-toast";
import { useBrewerySupabaseImport } from "./useBrewerySupabaseImport";
import { useBreweryLocalImport } from "./useBreweryLocalImport";
import { completeImportProcess } from "./utils/importSuccessUtils";
import { processLocalStorageImport, processSupabaseImport } from "./utils/importProcessingUtils";
import { useImportProgress } from "./useImportProgress";

interface UseImportExecutionProps {
  fetchBreweries: () => Promise<void>;
  progressHook: ReturnType<typeof useImportProgress>;
}

export const useImportExecution = ({
  fetchBreweries,
  progressHook
}: UseImportExecutionProps) => {
  const [lastUpdateTime, setLastUpdateTime] = useState(0);
  const { importToSupabase } = useBrewerySupabaseImport();
  const { importToLocalStorage } = useBreweryLocalImport();
  
  const processImport = useCallback(async (
    breweryData: any[],
    duplicates: number,
    fileName: string
  ) => {
    const {
      updateChunkProgress,
      updateProgress,
      updateRowProgress,
    } = progressHook;
    
    const useSupabase = import.meta.env.VITE_USE_SUPABASE === "true";
    console.log(`Processing import using ${useSupabase ? "Supabase" : "LocalStorage"}`);
    
    try {
      if (useSupabase) {
        const { successCount, errorCount } = await processSupabaseImport({
          breweryData,
          importToSupabase,
          updateChunkProgress,
          updateProgress,
          updateRowProgress,
          setLastUpdateTime
        });
        
        completeImportProcess(successCount, 0, errorCount, duplicates, fileName);
      } else {
        const { successCount, updatedCount } = await processLocalStorageImport({
          breweryData,
          importToLocalStorage,
          updateChunkProgress,
          updateProgress,
          updateRowProgress,
          setLastUpdateTime
        });
        
        completeImportProcess(successCount, updatedCount, 0, duplicates, fileName);
      }
      
      // Refresh breweries list
      await fetchBreweries();
      
    } catch (error) {
      console.error("Import process error:", error);
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "An unknown error occurred during the import process",
        variant: "destructive"
      });
      throw error;
    }
  }, [fetchBreweries, importToLocalStorage, importToSupabase, progressHook]);
  
  return {
    processImport,
    lastUpdateTime
  };
};
