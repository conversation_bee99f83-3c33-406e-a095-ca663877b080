
import { supabase } from "@/integrations/supabase/client";
import { Brewery } from "@/types/brewery";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";

export const useBreweryManipulation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const updateBrewery = async (id: string, data: Partial<Brewery>) => {
    try {
      const { error } = await supabase
        .from('breweries')
        .update(data)
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      // Invalidate the cache for breweries to trigger a refetch
      await queryClient.invalidateQueries({ queryKey: ['breweries'] });
      
      toast({
        title: "Brewery updated",
        description: "The brewery has been successfully updated",
      });
    } catch (error) {
      console.error("Error updating brewery:", error);
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
      throw error;
    }
  };
  
  const deleteBrewery = async (id: string) => {
    try {
      const { error } = await supabase
        .from('breweries')
        .delete()
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      // Invalidate the cache for breweries to trigger a refetch
      await queryClient.invalidateQueries({ queryKey: ['breweries'] });
      
      toast({
        title: "Brewery deleted",
        description: "The brewery has been successfully deleted",
      });
    } catch (error) {
      console.error("Error deleting brewery:", error);
      toast({
        title: "Deletion failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive",
      });
      throw error;
    }
  };
  
  const deleteAllBreweries = async () => {
    try {
      const { error } = await supabase
        .from('breweries')
        .delete()
        .neq('id', null);
      
      if (error) {
        throw error;
      }
      
      // Invalidate the cache for breweries to trigger a refetch
      await queryClient.invalidateQueries({ queryKey: ['breweries'] });
      
      toast({
        title: "All breweries deleted",
        description: "All breweries have been successfully deleted",
      });
    } catch (error) {
      console.error("Error deleting all breweries:", error);
      toast({
        title: "Deletion failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      throw error;
    }
  };

  return {
    updateBrewery,
    deleteBrewery,
    deleteAllBreweries
  };
};
