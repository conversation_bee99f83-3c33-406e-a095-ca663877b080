
import { useState, useEffect } from 'react';
import { useAuth } from '@/auth';
import { useBreweryFollowers } from './useBreweryFollowers';
import { useBreweryLikes } from './useBreweryLikes';
import { useInteractionStatus } from './useInteractionStatus';
import type { UseBrewerySocialStatsReturn } from './types/socialStatsTypes';

export type { UseBrewerySocialStatsReturn };
export type { BrewerySocialStatsState, BrewerySocialStatsActions } from './types/socialStatsTypes';

export const useBrewerySocialStats = (breweryId: string): UseBrewerySocialStatsReturn => {
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [likeCount, setLikeCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Use the separated hooks
  const { checkInteractionStatus } = useInteractionStatus(
    breweryId,
    user?.id,
    setIsFollowing,
    setIsLiked,
    setFollowerCount,
    setLikeCount,
    setIsLoading
  );

  const { toggleFollow } = useBreweryFollowers(
    breweryId,
    user?.id,
    isFollowing,
    setIsFollowing,
    setFollowerCount
  );

  const { toggleLike } = useBreweryLikes(
    breweryId,
    user?.id,
    isLiked,
    setIsLiked,
    setLikeCount
  );

  // Check interaction status on mount and when dependencies change
  useEffect(() => {
    checkInteractionStatus();
  }, [checkInteractionStatus]);

  const refreshStats = async () => {
    await checkInteractionStatus();
  };

  return {
    isFollowing,
    isLiked,
    followerCount,
    likeCount,
    isLoading,
    toggleFollow,
    toggleLike,
    refreshStats
  };
};
