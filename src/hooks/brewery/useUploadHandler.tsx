
import { useCallback, useState } from "react";
import { toast } from "@/hooks/use-toast";
import { parseCSV } from "@/lib/csv";

interface UseUploadHandlerProps {
  fetchBreweries: () => Promise<void>;
  setIsLoading: (isLoading: boolean) => void;
  setColumns: (columns: string[]) => void;
  setCurrentPage: (page: number) => void;
}

export const useUploadHandler = ({
  fetchBreweries,
  setIsLoading,
  setColumns,
  setCurrentPage
}: UseUploadHandlerProps) => {
  const [progress, setProgress] = useState(0);
  
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log("File upload handler called", event);
    
    // Validate we have a proper file from the input
    if (!event.target || !event.target.files) {
      console.error("Invalid event or missing files property");
      toast({
        title: "Upload error",
        description: "Failed to access the selected file",
        variant: "destructive"
      });
      return;
    }
    
    const file = event.target.files[0];
    
    // Guard clause - only proceed if we have a valid file
    if (!file) {
      console.log("No file selected");
      toast({
        title: "No file selected",
        description: "Please select a valid CSV file",
        variant: "destructive"
      });
      return;
    }
    
    console.log(`Preparing to import file: ${file.name}, size: ${file.size} bytes`);
    
    // Basic file validation
    if (!file.name.endsWith('.csv')) {
      toast({
        title: "Invalid file type",
        description: "Please select a CSV file",
        variant: "destructive"
      });
      return;
    }

    console.log(`Starting import for file: ${file.name}`);
    setIsLoading(true);
    setProgress(10);
    
    try {
      // Process CSV data
      setProgress(25);
      console.log("Parsing CSV...");
      const { data: breweryData, columns } = await parseCSV(file);
      
      console.log("Parsing complete:", { 
        rowCount: breweryData.length, 
        columns
      });
      
      if (breweryData.length === 0) {
        throw new Error("No valid data found in the CSV file");
      }
      
      setProgress(50);
      
      // Process each brewery record
      setProgress(75);
      
      // Store in localStorage as a simple solution
      const existingBreweriesJSON = localStorage.getItem("breweries");
      const existingBreweries = existingBreweriesJSON 
        ? JSON.parse(existingBreweriesJSON) 
        : [];
      
      // Add new breweries with timestamps
      const enrichedData = breweryData.map(brewery => ({
        ...brewery,
        id: brewery.id || `brewery-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      // Save to localStorage
      localStorage.setItem("breweries", JSON.stringify([...existingBreweries, ...enrichedData]));
      
      setProgress(100);
      
      // Update columns and reset page
      setColumns(columns);
      setCurrentPage(1);
      
      toast({
        title: "Import completed",
        description: `Successfully imported ${breweryData.length} items from ${file.name}`,
      });
      
      // Refresh breweries list
      await fetchBreweries();
      
    } catch (error) {
      console.error("Import process error:", error);
      toast({
        title: "Import failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      // Reset progress after a delay
      setTimeout(() => setProgress(0), 3000);
    }
  }, [fetchBreweries, setColumns, setCurrentPage, setIsLoading]);

  return {
    handleFileUpload,
    progress
  };
};
