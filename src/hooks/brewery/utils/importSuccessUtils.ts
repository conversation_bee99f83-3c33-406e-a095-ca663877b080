
import { toast } from "@/hooks/use-toast";
import { createImportSuccessMessage } from "./breweryImportUtils";

/**
 * Display success message after import is complete
 */
export const completeImportProcess = (
  successCount: number, 
  updatedCount: number, 
  errorCount: number, 
  duplicates: number,
  fileName: string
) => {
  // Create a success message
  const message = createImportSuccessMessage(
    successCount, 
    updatedCount, 
    errorCount, 
    duplicates,
    fileName
  );
  
  console.log(message);
  
  // Show success toast
  toast({
    title: `Import completed${errorCount > 0 ? ' with warnings' : ''}`,
    description: message,
    variant: errorCount > 0 ? "destructive" : "default"
  });
};
