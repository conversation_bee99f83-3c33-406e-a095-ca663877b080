
import { toast } from "@/hooks/use-toast";

/**
 * Handle file upload and process CSV data
 */
export const handleFileSizeWarning = (fileSize: number): void => {
  // Show initial toast for large files
  if (fileSize > 1 * 1024 * 1024) { // 1MB threshold
    toast({
      title: "Processing large file",
      description: "Please wait while we process your data. This may take a minute for large files.",
    });
  }
};

/**
 * Show warning toast for large datasets
 */
export const showLargeDatasetWarning = (dataLength: number): void => {
  if (dataLength > 500) {
    toast({
      title: "Large dataset detected",
      description: `Processing ${dataLength} records. This might take several minutes. Please be patient.`,
    });
  }
};
