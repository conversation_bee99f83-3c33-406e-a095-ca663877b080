
import { Brewery, SocialLinks } from '@/types/brewery.patch'; // Use patch version
import { Json } from '@/integrations/supabase/types';

/**
 * Converts social links from database format to application format
 */
export const mapSocialLinksFromDb = (dbSocialLinks: Json | null): SocialLinks => {
  // Default empty social links
  let socialLinks: SocialLinks = { facebook: '', instagram: '', twitter: '' };
  
  if (!dbSocialLinks) return socialLinks;
  
  if (typeof dbSocialLinks === 'object') {
    // Safely convert from Json type to SocialLinks
    if (typeof dbSocialLinks === 'object' && dbSocialLinks !== null && !Array.isArray(dbSocialLinks)) {
      const links = dbSocialLinks as Record<string, any>;
      socialLinks = {
        facebook: links.facebook || '',
        instagram: links.instagram || '',
        twitter: links.twitter || ''
      };
    }
  } else if (typeof dbSocialLinks === 'string') {
    try {
      const parsed = JSON.parse(dbSocialLinks);
      socialLinks = {
        facebook: parsed.facebook || '',
        instagram: parsed.instagram || '',
        twitter: parsed.twitter || ''
      };
    } catch (e) {
      console.error("Error parsing social links string:", e);
    }
  }
  
  return socialLinks;
};

/**
 * Maps database brewery data to application Brewery type
 */
export const mapBreweryFromDb = (data: any, isFollowing: boolean = false, isLiked: boolean = false): Brewery => {
  const socialLinks = mapSocialLinksFromDb(data.social_links);
  
  return {
    id: data.id,
    name: data.name || 'Unnamed Brewery',
    address: data.address || '',
    city: data.city || '',
    state: data.state || '',
    zip: data.zip || '',
    phone: data.phone || '',
    website: data.website || '',
    description: data.description || '',
    logo: data.logo || '',
    featureImage: data.feature_image || '',
    email: data.email || '',
    brewery_type: data.brewery_type || 'micro', // Ensure brewery_type is correctly mapped from DB
    socialLinks: socialLinks,
    // Add the verification fields with safe defaults
    claimable: data.claimable !== undefined ? data.claimable : false,
    verificationOpen: data.verification_open !== undefined ? data.verification_open : false,
    claimed: data.claimed !== undefined ? data.claimed : false,
    verified: data.verified !== undefined ? Boolean(data.verified) : false, // Ensure verified is always a boolean
    // Add social profile fields
    avatar: data.avatar || data.logo || '',
    followerCount: data.follower_count || 0,
    likeCount: data.like_count || 0,
    isFollowing,
    isLiked
  };
};

/**
 * Maps application Brewery type to database format for update operations
 */
export const mapBreweryToDb = (brewery: Partial<Brewery>): Record<string, any> => {
  const dbData: any = {
    ...brewery,
    feature_image: brewery.featureImage,
    social_links: brewery.socialLinks,
    verification_open: brewery.verificationOpen,
    follower_count: brewery.followerCount,
    like_count: brewery.likeCount,
    brewery_type: brewery.brewery_type, // Ensure brewery_type is mapped to DB format
    verified: brewery.verified // Ensure verified is mapped to DB format
  };

  // Remove application-specific properties
  delete dbData.featureImage;
  delete dbData.socialLinks;
  delete dbData.verificationOpen;
  delete dbData.password;
  delete dbData.role;
  delete dbData.menu;
  delete dbData.isFollowing;
  delete dbData.isLiked;
  
  return dbData;
};
