
import { Brewery } from "@/types/brewery";
import { toast } from "@/hooks/use-toast";

// Configure chunk size for better performance
export const CHUNK_SIZE = 250;

/**
 * Split data into manageable chunks
 */
export const createDataChunks = <T>(data: T[], chunkSize: number = CHUNK_SIZE): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < data.length; i += chunkSize) {
    chunks.push(data.slice(i, i + chunkSize));
  }
  return chunks;
};

/**
 * Process a short delay to prevent UI freezing between chunks
 */
export const processDelay = async (delayMs: number = 50): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, delayMs));
};

/**
 * Show progress toast for large datasets
 */
export const showProgressToast = (currentIndex: number, totalChunks: number, totalItems: number): void => {
  // Show toast every 2 chunks for large datasets or every chunk for small datasets
  const showInterval = totalChunks > 10 ? 2 : 1;
  
  if (currentIndex > 0 && currentIndex % showInterval === 0) {
    const percentComplete = Math.round((currentIndex / totalChunks) * 100);
    toast({
      title: "Import progress",
      description: `Processed ${currentIndex} of ${totalChunks} chunks (${percentComplete}%)`,
    });
  }
};

/**
 * Calculate progress percentage based on chunked processing
 * Uses a more direct approach to ensure progress increases reliably
 */
export const calculateProgressPercentage = (
  baseProgress: number,
  chunkIndex: number, 
  totalChunks: number, 
  range: number = 70
): number => {
  if (totalChunks <= 0) return baseProgress;
  
  // Start calculation from the base progress (e.g., 25%)
  // and ensure progress is proportional to completed chunks
  const progressPortion = range / totalChunks;
  const progressValue = baseProgress + (chunkIndex * progressPortion);
  
  // Ensure progress is at least increasing by 1%
  const minProgress = baseProgress + Math.floor((chunkIndex / totalChunks) * range);
  
  // Log calculation for debugging
  console.log(`Progress calculation: base=${baseProgress}, chunk=${chunkIndex}/${totalChunks}, calculated=${progressValue}%`);
  
  return Math.max(minProgress, progressValue);
};
