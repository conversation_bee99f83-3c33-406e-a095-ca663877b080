
import { Brewery } from "@/types/brewery";
import { parseCSV } from "@/lib/csv";
import { toast } from "@/hooks/use-toast";
import { formatWebsiteUrl, formatAddress } from "@/lib/csv/formatters";

/**
 * Process a CSV file and map data to brewery structure
 */
export const processBreweryCSV = async (file: File): Promise<{
  breweryData: Brewery[];
  columns: string[];
  duplicates: number;
}> => {
  console.log(`Processing CSV file: ${file.name}, size: ${file.size} bytes`);
  
  try {
    // Process CSV file
    console.log("Processing CSV file...");
    const result = await parseCSV(file);
    
    if (result.data.length === 0) {
      throw new Error("No valid data found in the CSV file");
    }
    
    console.log(`CSV processed: ${result.data.length} rows found`);
    
    // Analyze data quality
    const dataQualityCheck = analyzeDataQuality(result.data);
    displayDataQualityToast(dataQualityCheck);
    
    // Map the data to ensure consistent field structure
    const rawBreweryData = result.data.map((item: any) => {
      // Format website URL if it exists
      if (item.website) {
        item.website = formatWebsiteUrl(item.website);
      }
      
      // Format address if it exists
      if (item.address) {
        item.address = formatAddress(item.address);
      }
      
      return {
        ...item,
        id: item.id || `brewery-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        logo: item.logo || "",
        featureImage: item.featureImage || "",
        state: item.state || "",
        website: item.website || "",
        address: item.address || ""
      };
    });
    
    // Check for duplicates with improved detection
    const { dedupedBreweries, duplicateCount } = deduplicateBreweries(rawBreweryData);
    if (duplicateCount > 0) {
      toast({
        title: "Duplicates detected",
        description: `Found and resolved ${duplicateCount} duplicate breweries in import data`,
        variant: "default"
      });
    }
    
    // Log a few sample items to verify field mapping worked correctly
    console.log("Sample brewery items after processing:");
    dedupedBreweries.slice(0, 3).forEach((brewery, index) => {
      console.log(`Brewery ${index + 1}:`, {
        name: brewery.name,
        website: brewery.website,
        address: brewery.address
      });
    });
    
    // Update columns to include essential fields if they don't exist
    let updatedColumns = [...result.columns];
    const essentialColumns = ["logo", "featureImage", "state", "website", "address"];
    
    for (const col of essentialColumns) {
      if (!updatedColumns.includes(col)) {
        updatedColumns.push(col);
      }
    }
    
    return {
      breweryData: dedupedBreweries,
      columns: updatedColumns,
      duplicates: duplicateCount
    };
  } catch (error) {
    console.error("Error processing CSV:", error);
    throw error;
  }
};

/**
 * Analyze data quality of parsed CSV data
 */
const analyzeDataQuality = (data: Record<string, any>[]): Record<string, any> => {
  if (data.length === 0) return {};
  
  // Get all field names from the first row
  const fields = Object.keys(data[0]);
  
  // Initialize counters
  const fieldStats: Record<string, any> = {};
  fields.forEach(field => {
    fieldStats[field] = {
      empty: 0,
      total: data.length,
      completionRate: 0
    };
  });
  
  // Count empty values for each field
  data.forEach(row => {
    fields.forEach(field => {
      if (!row[field] || row[field].toString().trim() === '') {
        fieldStats[field].empty++;
      }
    });
  });
  
  // Calculate completion rates
  fields.forEach(field => {
    const nonEmpty = fieldStats[field].total - fieldStats[field].empty;
    fieldStats[field].completionRate = Math.round((nonEmpty / fieldStats[field].total) * 100);
  });
  
  console.log("Data quality analysis:", fieldStats);
  
  // Special attention to important fields
  const importantFields = ['website', 'address'];
  importantFields.forEach(field => {
    if (fieldStats[field]) {
      console.log(`Field '${field}' completion rate: ${fieldStats[field].completionRate}%`);
    } else {
      console.warn(`Important field '${field}' not found in data`);
    }
  });
  
  return fieldStats;
};

/**
 * Deduplicate breweries based on name, city, state and address
 * Returns deduplicated breweries and the count of duplicates found
 */
export const deduplicateBreweries = (breweries: any[]): { 
  dedupedBreweries: any[], 
  duplicateCount: number 
} => {
  const uniqueBreweries = new Map();
  let duplicateCount = 0;
  
  // First pass - identify duplicates based on composite key
  breweries.forEach(brewery => {
    if (!brewery.name) return; // Skip breweries without names
    
    // Create a unique key based on name, city, state, and address
    const key = [
      (brewery.name || '').toLowerCase().trim(), 
      (brewery.city || '').toLowerCase().trim(), 
      (brewery.state || '').toLowerCase().trim(),
      (brewery.address || '').toLowerCase().trim()
    ].join('|');
    
    if (!uniqueBreweries.has(key)) {
      uniqueBreweries.set(key, brewery);
    } else {
      duplicateCount++;
      
      // Get existing brewery
      const existingBrewery = uniqueBreweries.get(key);
      
      // Merge with existing entry if new brewery has more fields
      const mergedBrewery = { ...existingBrewery };
      
      // For each property in the new brewery, update if empty in existing
      Object.entries(brewery).forEach(([key, value]) => {
        if (value && (!mergedBrewery[key] || String(mergedBrewery[key]).trim() === '')) {
          mergedBrewery[key] = value;
        }
      });
      
      uniqueBreweries.set(key, mergedBrewery);
    }
  });
  
  console.log(`Found and handled ${duplicateCount} duplicate breweries`);
  
  return { 
    dedupedBreweries: Array.from(uniqueBreweries.values()),
    duplicateCount
  };
};

/**
 * Display a toast with data quality information
 */
const displayDataQualityToast = (dataQuality: Record<string, any>) => {
  // Find fields with poor completion rates
  const poorQualityFields: string[] = [];
  const warnThreshold = 70; // Warn if less than 70% of records have this field
  
  Object.entries(dataQuality).forEach(([field, stats]: [string, any]) => {
    if (stats.completionRate < warnThreshold) {
      poorQualityFields.push(`${field} (${stats.completionRate}%)`);
    }
  });
  
  if (poorQualityFields.length > 0) {
    toast({
      title: "Data quality warning",
      description: `Low completion rates detected in: ${poorQualityFields.join(', ')}`,
      variant: "destructive",
    });
  } else {
    toast({
      title: "Data quality check passed",
      description: "All fields have good completion rates",
      variant: "default",
    });
  }
};

/**
 * Create success message for import operation
 */
export const createImportSuccessMessage = (
  successCount: number, 
  updatedCount: number, 
  errorCount: number, 
  duplicates: number,
  fileName: string
): string => {
  let message = `Imported ${successCount} breweries from ${fileName}`;
  if (updatedCount > 0) {
    message += `, updated ${updatedCount} existing breweries`;
  }
  if (duplicates > 0) {
    message += `, resolved ${duplicates} duplicates`;
  }
  if (errorCount > 0) {
    message += `, failed to import ${errorCount} breweries`;
  }
  
  return message;
};
