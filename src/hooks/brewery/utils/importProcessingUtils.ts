
import { Brewery } from "@/types/brewery.patch";
import { UpdateChunkProgress, UpdateProgress, UpdateRowProgress } from "../types/importTypes";
import { chunk } from "@/lib/utils";

/**
 * Process import using Supabase
 */
export const processSupabaseImport = async ({
  breweryData,
  importToSupabase,
  updateChunkProgress,
  updateProgress,
  updateRowProgress,
  setLastUpdateTime
}: {
  breweryData: Brewery[];
  importToSupabase: (breweries: Brewery[]) => Promise<{ successCount: number; errorCount: number }>;
  updateChunkProgress: UpdateChunkProgress;
  updateProgress: UpdateProgress;
  updateRowProgress: UpdateRowProgress;
  setLastUpdateTime: (time: number) => void;
}) => {
  updateProgress(40);
  
  // Set row progress targets
  updateRowProgress(0, breweryData.length);
  
  // Process in chunks to prevent timeouts and memory issues
  const CHUNK_SIZE = 100;
  const chunks = chunk(breweryData, CHUNK_SIZE);
  
  // Update chunk progress
  updateChunkProgress(0, chunks.length);
  console.log(`Processing ${breweryData.length} breweries in ${chunks.length} chunks`);
  
  let successCount = 0;
  let errorCount = 0;
  let currentProcessedRows = 0;
  
  for (let i = 0; i < chunks.length; i++) {
    updateChunkProgress(i + 1, chunks.length);
    const currentChunk = chunks[i];
    
    try {
      console.log(`Processing chunk ${i + 1}/${chunks.length} with ${currentChunk.length} breweries`);
      
      // Process each chunk with Supabase import
      const result = await importToSupabase(currentChunk);
      
      // Update counts
      successCount += result.successCount;
      errorCount += result.errorCount;
      
      // Update row progress
      currentProcessedRows += currentChunk.length;
      updateRowProgress(currentProcessedRows, breweryData.length);
      
      // Update overall progress (40-95%)
      const progressPercent = 40 + Math.round((i + 1) / chunks.length * 55);
      updateProgress(Math.min(95, progressPercent));
      
      // Keep track of last update time
      setLastUpdateTime(Date.now());
      
      // Add small delay to prevent UI freezing
      await new Promise(resolve => setTimeout(resolve, 50));
      
    } catch (error) {
      console.error(`Error processing chunk ${i + 1}:`, error);
      errorCount += currentChunk.length;
    }
  }
  
  return { successCount, errorCount };
};

/**
 * Process import using LocalStorage
 */
export const processLocalStorageImport = async ({
  breweryData,
  importToLocalStorage,
  updateChunkProgress,
  updateProgress,
  updateRowProgress,
  setLastUpdateTime
}: {
  breweryData: Brewery[];
  importToLocalStorage: (breweries: Brewery[]) => Promise<{ successCount: number; updatedCount: number }>;
  updateChunkProgress: UpdateChunkProgress;
  updateProgress: UpdateProgress;
  updateRowProgress: UpdateRowProgress;
  setLastUpdateTime: (time: number) => void;
}) => {
  updateProgress(40);
  
  // Process in smaller chunks for localStorage to prevent browser freezing
  const CHUNK_SIZE = 50;
  const chunks = chunk(breweryData, CHUNK_SIZE);
  
  // Update progress targets
  updateChunkProgress(0, chunks.length);
  updateRowProgress(0, breweryData.length);
  
  let successCount = 0;
  let updatedCount = 0;
  let currentProcessedRows = 0;
  
  for (let i = 0; i < chunks.length; i++) {
    updateChunkProgress(i + 1, chunks.length);
    
    try {
      // Process this chunk with localStorage
      const result = await importToLocalStorage(chunks[i]);
      
      // Update counts
      successCount += result.successCount;
      updatedCount += result.updatedCount;
      
      // Update row progress
      currentProcessedRows += chunks[i].length;
      updateRowProgress(currentProcessedRows, breweryData.length);
      
      // Update overall progress (40-95%)
      const progressPercent = 40 + Math.round((i + 1) / chunks.length * 55);
      updateProgress(Math.min(95, progressPercent));
      
      // Keep track of last update time
      setLastUpdateTime(Date.now());
      
      // Add small delay to prevent UI freezing
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(`Error processing chunk ${i + 1}:`, error);
    }
  }
  
  return { successCount, updatedCount };
};

/**
 * Remove duplicates from brewery data based on name, city, and state
 */
export const deduplicateBreweries = (breweries: Brewery[]): Brewery[] => {
  const uniqueBreweries = new Map<string, Brewery>();
  
  breweries.forEach(brewery => {
    if (!brewery.name) return; // Skip breweries without names
    
    // Create a unique key based on name, city, state, and address
    const key = [
      (brewery.name || '').toLowerCase().trim(), 
      (brewery.city || '').toLowerCase().trim(), 
      (brewery.state || '').toLowerCase().trim(),
      (brewery.address || '').toLowerCase().trim()
    ].join('|');
    
    // Only add if we haven't seen this brewery before, or if the new one has more data
    if (!uniqueBreweries.has(key)) {
      uniqueBreweries.set(key, brewery);
    } else {
      // Merge with existing entry if new brewery has more fields
      const existing = uniqueBreweries.get(key)!;
      const mergedBrewery = { ...existing };
      
      // For each property in the new brewery, update if empty in existing
      Object.entries(brewery).forEach(([key, value]) => {
        if (value && (!mergedBrewery[key as keyof Brewery] || 
                       String(mergedBrewery[key as keyof Brewery]).trim() === '')) {
          (mergedBrewery as any)[key] = value;
        }
      });
      
      uniqueBreweries.set(key, mergedBrewery);
    }
  });
  
  return Array.from(uniqueBreweries.values());
};
