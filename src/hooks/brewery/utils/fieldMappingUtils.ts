
/**
 * Field mapping utilities for CSV imports
 * Maps common variations of field names to standardized field names
 */

// Define known field variations for brewery data
export const fieldMappingDefinitions: Record<string, string[]> = {
  // Basic information
  name: ['name', 'brewery name', 'brewery_name', 'company name', 'company_name', 'title'],
  description: ['description', 'desc', 'about', 'brewery description', 'summary', 'details', 'info'],
  
  // Location fields
  address: ['address', 'street address', 'street_address', 'location', 'addr', 'street', 'address1', 'street line 1'],
  city: ['city', 'town', 'municipality', 'locality'],
  state: ['state', 'province', 'region', 'state_id', 'state id', 'st', 'state code', 'state_code'],
  zip: ['zip', 'zipcode', 'zip_code', 'zip code', 'postal', 'postal_code', 'postal code', 'postcode'],
  
  // Contact information
  phone: ['phone', 'telephone', 'phone_number', 'phone number', 'contact', 'contact_number', 'tel', 'telephone_number'],
  website: ['website', 'web', 'url', 'web_address', 'site', 'homepage', 'web site', 'web_site'],
  email: ['email', 'e-mail', 'email_address', 'email address', 'contact email', 'contact_email'],
  
  // Media
  logo: ['logo', 'logo_url', 'logo url', 'brand', 'brand_image', 'logo_image', 'logo image'],
  featureImage: ['feature_image', 'featureImage', 'feature image', 'main image', 'main_image', 'banner', 'hero', 'cover']
};

/**
 * Create a mapping between CSV headers and standardized field names
 */
export const createFieldMapping = (headers: string[]): Record<string, string> => {
  const mapping: Record<string, string> = {};
  
  // First process exact matches (case insensitive)
  headers.forEach(header => {
    const normalizedHeader = header.trim().toLowerCase();
    
    // Check for exact match in our field definitions
    for (const [standardField, variations] of Object.entries(fieldMappingDefinitions)) {
      if (variations.includes(normalizedHeader)) {
        mapping[header] = standardField;
        break;
      }
    }
  });
  
  // For headers that didn't match exactly, try fuzzy matching
  headers.forEach(header => {
    if (mapping[header]) return; // Skip if already mapped
    
    const normalizedHeader = header.trim().toLowerCase();
    
    // Try to find the best match based on substring or similarity
    for (const [standardField, variations] of Object.entries(fieldMappingDefinitions)) {
      // Check if the header contains any of our known variations
      for (const variation of variations) {
        if (normalizedHeader.includes(variation) || 
            variation.includes(normalizedHeader)) {
          mapping[header] = standardField;
          console.log(`Fuzzy matched "${header}" to "${standardField}" field`);
          return;
        }
      }
    }
    
    // If still no match, copy as-is (can't recognize this field)
    if (!mapping[header]) {
      mapping[header] = header;
    }
  });
  
  console.log("Field mapping created:", mapping);
  return mapping;
};

/**
 * Apply field mapping to transform a row based on the mapping
 */
export const applyFieldMapping = (
  row: Record<string, any>, 
  fieldMapping: Record<string, string>
): Record<string, any> => {
  const mappedRow: Record<string, any> = {};
  
  // Apply mapping and transform data
  Object.entries(row).forEach(([originalField, value]) => {
    const targetField = fieldMapping[originalField] || originalField;
    
    // Don't overwrite existing values with empty ones
    if (mappedRow[targetField] && !value) {
      return;
    }
    
    mappedRow[targetField] = value;
  });
  
  return mappedRow;
};
