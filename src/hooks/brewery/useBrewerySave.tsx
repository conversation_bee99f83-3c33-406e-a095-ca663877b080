
import { Brewery } from "@/types/brewery";
import { toast } from "@/hooks/use-toast";

interface UseBrewerySaveProps {
  breweries: Brewery[];
  setBreweries: (breweries: Brewery[]) => void;
  editingBrewery: Brewery | null;
  editedValues: Record<string, any>;
  closeEditor: () => void;
}

export const useBrewerySave = ({
  breweries,
  setBreweries,
  editingBrewery,
  editedValues,
  closeEditor
}: UseBrewerySaveProps) => {
  
  const handleSave = () => {
    if (!editingBrewery) return;
    
    try {
      // Find the index of the brewery being edited
      const index = breweries.findIndex(b => b.id === editingBrewery.id);
      
      if (index !== -1) {
        // Update the brewery data
        const updatedBreweries = [...breweries];
        updatedBreweries[index] = {
          ...editingBrewery,
          ...editedValues
        };
        
        // Update the state
        setBreweries(updatedBreweries);
        closeEditor();
        
        toast({
          title: "Brewery updated",
          description: "The brewery has been successfully updated",
        });
      }
    } catch (error) {
      console.error("Error saving brewery:", error);
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };
  
  return { handleSave };
};
