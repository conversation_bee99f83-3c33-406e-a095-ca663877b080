
import { useState } from 'react';
import { Brewery } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useImageUpload } from '../useImageUpload';

export const useBreweryImageUpload = (
  brewery: Brewery | null,
  setBreweryState: React.Dispatch<React.SetStateAction<Brewery | null>>
) => {
  const [isUploading, setIsUploading] = useState(false);
  const { uploadProgress, fileErrors, handleFileUpload } = useImageUpload();
  
  const handleImageUploadWrapper = async (type: string, file: File): Promise<string> => {
    if (!brewery || !brewery.id) {
      toast({
        title: "Upload error",
        description: "Brewery not found. Save your profile first.",
        variant: "destructive",
      });
      return "";
    }
    
    setIsUploading(true);
    let publicUrl = "";
    
    try {
      // Create a file path with the brewery ID and timestamp
      const fileExt = file.name.split('.').pop();
      const filePath = `breweries/${brewery.id}/${type}-${Date.now()}.${fileExt}`;
      
      // Handle the image upload using the useImageUpload hook
      // This only updates UI state for preview/progress
      handleFileUpload(type, file, () => {});
      
      // Actually upload the file to Supabase storage
      const { data, error } = await supabase.storage
        .from('brewery-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (error) throw error;
      
      // Get public URL for the uploaded file
      const { data: urlData } = supabase.storage
        .from('brewery-images')
        .getPublicUrl(filePath);
      
      publicUrl = urlData.publicUrl;
      
      // Update the brewery record in database
      const updateData = type === 'logo' 
        ? { logo: publicUrl } 
        : { feature_image: publicUrl };
        
      const { error: updateError } = await supabase
        .from('breweries')
        .update(updateData)
        .eq('id', brewery.id);
        
      if (updateError) throw updateError;
      
      // Update local state
      setBreweryState(prev => {
        if (!prev) return null;
        
        if (type === 'logo') {
          return { ...prev, logo: publicUrl };
        } else if (type === 'featureImage') {
          return { ...prev, featureImage: publicUrl };
        }
        
        return prev;
      });
      
      toast({
        title: "Upload successful",
        description: `Brewery ${type} has been updated successfully`,
      });
      
      return publicUrl;
    } catch (err: any) {
      console.error(`Error uploading ${type}:`, err);
      
      toast({
        title: "Upload failed",
        description: err.message || `Failed to upload ${type}`,
        variant: "destructive",
      });
      return publicUrl;
    } finally {
      setIsUploading(false);
    }
  };
  
  return {
    isUploading,
    uploadProgress,
    fileErrors,
    handleImageUploadWrapper
  };
};
