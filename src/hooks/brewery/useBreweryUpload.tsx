
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { SocialLinks } from '@/types/brewery.patch';

export const useBreweryUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Function to deduplicate breweries
  const deduplicateBreweries = (data: any[]) => {
    const uniqueBreweries = new Map();
    
    // Use a composite key of name + city + state + address for uniqueness
    data.forEach(brewery => {
      const key = `${brewery.name?.toLowerCase() || ''}|${brewery.city?.toLowerCase() || ''}|${brewery.state?.toLowerCase() || ''}|${brewery.address?.toLowerCase() || ''}`;
      
      if (!uniqueBreweries.has(key)) {
        uniqueBreweries.set(key, brewery);
      }
    });
    
    return Array.from(uniqueBreweries.values());
  };

  // Process data in chunks to avoid overwhelming the database
  const chunk = <T,>(array: T[], size: number): T[][] => {
    const chunked: T[][] = [];
    let index = 0;
    while (index < array.length) {
      chunked.push(array.slice(index, index + size));
      index += size;
    }
    return chunked;
  };

  const uploadBreweries = useCallback(async (data: any[]) => {
    setIsUploading(true);
    setProgress(0);
    setError(null);
    
    try {
      // Deduplicate data
      const uniqueBreweries = deduplicateBreweries(data);
      
      console.log(`Processing ${uniqueBreweries.length} unique breweries...`);
      
      // Process in chunks of 50
      const chunks = chunk(uniqueBreweries, 50);
      
      let processedCount = 0;
      
      for (const breweryChunk of chunks) {
        const promises = breweryChunk.map(async (brewery) => {
          // Convert social links to proper format
          const socialLinksObj = brewery.social_links ? brewery.social_links : null;
          
          // Check if brewery already exists by name, city, state, and address
          const { data: existingBreweries, error: searchError } = await supabase
            .from('breweries')
            .select('id, name, city, state, address')
            .eq('name', brewery.name || '')
            .eq('city', brewery.city || '')
            .eq('state', brewery.state || '');
            
          if (searchError) {
            console.error('Error searching for existing brewery:', searchError);
            return;
          }
          
          // Further filter by address to ensure uniqueness
          const existingBrewery = existingBreweries.find(b => 
            b.address?.toLowerCase() === brewery.address?.toLowerCase()
          );
          
          // If brewery exists, update it - otherwise create a new one
          if (existingBrewery) {
            // Update existing brewery
            const { error: updateError } = await supabase
              .from('breweries')
              .update({
                ...brewery,
                social_links: socialLinksObj as any
              })
              .eq('id', existingBrewery.id);
              
            if (updateError) {
              console.error('Error updating brewery:', updateError);
            }
            
            return existingBrewery.id;
          } else {
            // Insert new brewery
            const { data: newBrewery, error: insertError } = await supabase
              .from('breweries')
              .insert({
                ...brewery,
                social_links: socialLinksObj as any
              })
              .select('id')
              .single();
              
            if (insertError) {
              console.error('Error inserting brewery:', insertError);
              return null;
            }
            
            return newBrewery?.id;
          }
        });
        
        await Promise.all(promises);
        
        processedCount += breweryChunk.length;
        setProgress(Math.round((processedCount / uniqueBreweries.length) * 100));
      }
      
      console.log('Brewery upload completed successfully!');
      
      return {
        success: true,
        count: uniqueBreweries.length
      };
    } catch (err: any) {
      console.error('Error uploading breweries:', err);
      setError(err.message || 'Unknown error occurred during upload');
      
      return {
        success: false,
        error: err.message || 'Unknown error occurred during upload',
        count: 0
      };
    } finally {
      setIsUploading(false);
    }
  }, []);

  return {
    uploadBreweries,
    isUploading,
    progress,
    error
  };
};
