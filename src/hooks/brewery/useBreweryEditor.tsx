
import { Brewery } from "@/types/brewery";
import { useEditorState } from "./useEditorState";
import { useBrewerySave } from "./useBrewerySave";

interface UseBreweryEditorProps {
  breweries: Brewery[];
  setBreweries: (breweries: Brewery[]) => void;
  fieldTypes?: Record<string, string>;
}

export const useBreweryEditor = ({
  breweries,
  setBreweries,
  fieldTypes = {}
}: UseBreweryEditorProps) => {
  // Use the smaller, focused hooks
  const {
    isEditorOpen,
    editingBrewery,
    editedValues,
    openEditor,
    closeEditor,
    handleInputChange,
    setEditedValues
  } = useEditorState();
  
  const { handleSave } = useBrewerySave({
    breweries,
    setBreweries,
    editingBrewery,
    editedValues,
    closeEditor
  });
  
  return {
    isEditorOpen,
    editingBrewery,
    editedValues,
    openEditor,
    closeEditor,
    handleInputChange,
    handleSave,
    fieldTypes
  };
};
