
import { useState } from 'react';

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

interface UseBrewerySortingProps {
  initialSortKey?: string;
  initialDirection?: 'asc' | 'desc';
}

export const useBrewerySorting = ({
  initialSortKey = 'name',
  initialDirection = 'asc'
}: UseBrewerySortingProps = {}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: initialSortKey,
    direction: initialDirection
  });

  // Toggle sort direction or set new sort key
  const toggleSort = (key: string) => {
    if (sortConfig.key === key) {
      // Toggle direction if same key
      setSortConfig({
        key,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc'
      });
    } else {
      // Set new key with default direction
      setSortConfig({
        key,
        direction: 'asc'
      });
    }
  };

  return {
    sortConfig,
    setSortConfig,
    toggleSort
  };
};
