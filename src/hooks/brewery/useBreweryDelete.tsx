
import { useState } from "react";
import { Brewery } from "@/types/brewery";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { Dispatch, SetStateAction } from "react";

interface UseBreweryDeleteProps {
  setBreweries: Dispatch<SetStateAction<Brewery[]>>;
}

export const useBreweryDelete = ({ setBreweries }: UseBreweryDeleteProps) => {
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [breweryToDelete, setBreweryToDelete] = useState<Brewery | null>(null);

  const handleDelete = async (id: string) => {
    setIsDeleteLoading(true);
    try {
      const { error } = await supabase.from("breweries").delete().eq("id", id);

      if (error) throw error;

      // Update the local state to remove the deleted brewery
      setBreweries((prevBreweries) => 
        prevBreweries.filter((brewery) => brewery.id !== id)
      );

      toast({
        title: "Brewery deleted",
        description: "The brewery has been successfully deleted",
      });
    } catch (error: any) {
      console.error("Error deleting brewery:", error);
      toast({
        title: "Deletion failed",
        description: error.message || "Failed to delete brewery",
        variant: "destructive",
      });
    } finally {
      setIsDeleteLoading(false);
      setBreweryToDelete(null);
    }
  };

  const handleDeleteBrewery = (brewery: Brewery) => {
    setBreweryToDelete(brewery);
  };

  const confirmDeleteBrewery = async () => {
    if (breweryToDelete) {
      await handleDelete(breweryToDelete.id);
    }
  };

  return {
    isDeleteLoading,
    breweryToDelete,
    setBreweryToDelete,
    handleDelete,
    handleDeleteBrewery,
    confirmDeleteBrewery,
  };
};
