
import { Brewery } from "@/types/brewery";

export interface BreweryImportFieldMap {
  [key: string]: string;
}

export interface ImportSource {
  type: 'csv' | 'sample' | 'supabase';
  content?: string;
  file?: File;
}

export interface ImportValidation {
  isValid: boolean;
  errorMessage?: string;
  fileSize?: number;
  fileType?: string;
}

export interface ImportProgress {
  percentage: number;
  currentChunk?: number;
  totalChunks?: number;
  rowsProcessed?: number;
  totalRows?: number;
  progress?: number;
}

export interface ImportResult {
  success: boolean;
  message: string;
  breweries?: Brewery[];
  insertedCount?: number;
  errors?: string[];
}

export interface UseBulkImportReturn {
  isLoading: boolean;
  error: string | null;
  progress: ImportProgress;
  result: ImportResult | null;
  importBreweries: (source: ImportSource) => Promise<void>;
  reset: () => void;
}

export interface UseFileProcessingReturn {
  isProcessing: boolean;
  fileContent: string | null;
  fileErrorMessage: string | null;
  processFile: (file: File) => Promise<string | null>;
}

export interface UseFieldMappingReturn {
  fieldMap: BreweryImportFieldMap;
  headers: string[];
  setFieldMap: (map: BreweryImportFieldMap) => void;
  resetFieldMap: () => void;
  generateFieldMap: (headers: string[]) => BreweryImportFieldMap;
}

export interface UseImportProgressReturn {
  progress: ImportProgress;
  updateProgress: UpdateProgress;
  updateChunkProgress: UpdateChunkProgress;
  updateRowProgress: UpdateRowProgress;
  resetProgress: () => void;
  currentChunk?: number;
  totalChunks?: number;
  rowsProcessed?: number;
  totalRows?: number;
}

export type UpdateProgress = (progressValue: number) => void;
export type UpdateChunkProgress = (current: number, total: number) => void;
export type UpdateRowProgress = (processed: number, total: number) => void;

export interface DashboardStats {
  totalBreweries: number;
  totalVerified: number;
  totalUnverified: number;
  totalClaimed: number;
  totalUnclaimed: number;
  averageRating: number;
  stats?: {
    totalBreweries: number;
    totalVerified: number;
    totalUnverified: number;
    totalClaimed: number;
    totalUnclaimed: number;
    averageRating: number;
    unclaimedBreweries?: number;
    claimedBreweries?: number;
    verifiedBreweries?: number;
  };
  loading?: boolean;
}
