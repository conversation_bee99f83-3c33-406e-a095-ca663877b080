
import { Brewery } from '@/types/brewery';

export interface UseBrewerySocialStatsReturn {
  isFollowing: boolean;
  isLiked: boolean;
  followerCount: number;
  likeCount: number;
  toggleFollow: () => Promise<void>;
  toggleLike: () => Promise<void>;
  refreshStats: () => Promise<void>;
  isLoading: boolean;
  checkInteractionStatus?: () => Promise<void>;
}

export interface UseInteractionStatusReturn {
  isFollowing: boolean;
  isLiked: boolean;
  followerCount: number;
  likeCount: number;
  setIsFollowing: (value: boolean) => void;
  setIsLiked: (value: boolean) => void;
  setFollowerCount: (updater: (prev: number) => number) => void;
  setLikeCount: (updater: (prev: number) => number) => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
}

export interface BrewerySocialStatsState {
  isFollowing: boolean;
  isLiked: boolean;
  followerCount: number;
  likeCount: number;
  isLoading: boolean;
}

export interface BrewerySocialStatsActions {
  toggleFollow: () => Promise<void>;
  toggleLike: () => Promise<void>;
  refreshStats: () => Promise<void>;
}
