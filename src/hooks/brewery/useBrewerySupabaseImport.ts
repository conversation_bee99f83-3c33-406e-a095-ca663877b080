
import { Brewery } from "@/types/brewery";
import { supabase } from "@/integrations/supabase/client";

export const useBrewerySupabaseImport = () => {
  /**
   * Import brewery data to Supabase
   */
  const importToSupabase = async (breweryData: Brewery[]): Promise<{
    successCount: number;
    errorCount: number;
  }> => {
    console.log("Attempting to upload to Supabase...", breweryData.length, "records");
    
    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    
    // Filter out empty or invalid breweries
    const validBreweries = breweryData.filter(brewery => {
      // Check for name
      if (!brewery.name || brewery.name.trim() === '') {
        console.warn("Skipping brewery without name:", brewery);
        skippedCount++;
        return false;
      }
      
      // Check for at least one additional field with data
      const hasAdditionalData = Object.entries(brewery).some(([key, value]) => 
        key !== 'name' && key !== 'id' && value && String(value).trim() !== ''
      );
      
      if (!hasAdditionalData) {
        console.warn("Skipping brewery with only name:", brewery);
        skippedCount++;
        return false;
      }
      
      return true;
    });
    
    console.log(`Filtered ${skippedCount} invalid breweries, proceeding with ${validBreweries.length}`);
    
    if (validBreweries.length === 0) {
      console.warn("No valid breweries to import after filtering");
      return { successCount: 0, errorCount: 0 };
    }
    
    // Check for existing breweries first to avoid duplicates
    try {
      // Get all brewery names for duplicate checking
      const { data: existingBreweries } = await supabase
        .from('breweries')
        .select('name, city, state, id');
      
      const existingBreweryMap = new Map();
      
      // Create a map of existing breweries for quick lookup
      existingBreweries?.forEach(brewery => {
        const key = `${(brewery.name || '').toLowerCase()}|${(brewery.city || '').toLowerCase()}|${(brewery.state || '').toLowerCase()}`;
        existingBreweryMap.set(key, brewery.id);
      });
      
      // Process each brewery individually to properly handle duplicates
      for (const brewery of validBreweries) {
        try {
          const key = `${(brewery.name || '').toLowerCase()}|${(brewery.city || '').toLowerCase()}|${(brewery.state || '').toLowerCase()}`;
          const existingId = existingBreweryMap.get(key);
          
          // If brewery exists, update instead of insert
          if (existingId) {
            console.log(`Brewery exists: ${brewery.name} in ${brewery.city}, ${brewery.state}. Updating.`);
            
            const { error } = await supabase
              .from('breweries')
              .update({
                address: brewery.address,
                phone: brewery.phone,
                website: brewery.website,
                description: brewery.description,
                logo: brewery.logo,
                feature_image: brewery.featureImage || brewery.feature_image,
                email: brewery.email,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingId);
            
            if (error) {
              console.error('Error updating brewery:', error);
              errorCount++;
            } else {
              successCount++;
            }
          } else {
            // Insert new brewery
            const { error } = await supabase
              .from('breweries')
              .insert({
                id: brewery.id,
                name: brewery.name,
                address: brewery.address,
                city: brewery.city,
                state: brewery.state,
                zip: brewery.zip,
                phone: brewery.phone,
                website: brewery.website,
                description: brewery.description,
                logo: brewery.logo,
                feature_image: brewery.featureImage || brewery.feature_image,
                email: brewery.email
              });
            
            if (error) {
              console.error('Error inserting brewery:', error);
              errorCount++;
            } else {
              successCount++;
            }
          }
        } catch (individualError) {
          console.error('Individual brewery processing error:', individualError);
          errorCount++;
        }
      }
    } catch (error) {
      console.error('Error during duplicate checking or batch processing:', error);
      errorCount += validBreweries.length;
    }
    
    console.log(`Supabase import results - Success: ${successCount}, Errors: ${errorCount}, Skipped: ${skippedCount}`);
    return { successCount, errorCount };
  };

  return {
    importToSupabase
  };
};
