import { useState, useEffect, useCallback } from 'react';
import { Brewery } from '@/types/brewery.patch'; // Ensure we use the patched version
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface UseBreweryFetchProps {
  limit?: number;
  initialPage?: number;
}

export const useBreweryFetch = (props?: UseBreweryFetchProps) => {
  const [breweries, setBreweries] = useState<Brewery[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const fetchBreweries = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Check if we have breweries in localStorage (non-Supabase mode)
      const localBreweriesJSON = localStorage.getItem('breweries');
      
      if (localBreweriesJSON) {
        try {
          const localBreweries = JSON.parse(localBreweriesJSON);
          console.log("Fetched breweries from localStorage:", localBreweries.length);
          
          // Ensure all required fields exist on each brewery object
          const formattedBreweries = localBreweries.map((brewery: any) => {
            return {
              id: brewery.id || `brewery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              name: brewery.name || 'Unnamed Brewery',
              brewery_type: brewery.brewery_type || 'micro',
              street: brewery.street || brewery.address || '',
              city: brewery.city || '',
              state: brewery.state || '',
              postal_code: brewery.postal_code || brewery.zip || '',
              country: brewery.country || 'United States',
              longitude: brewery.longitude || null,
              latitude: brewery.latitude || null,
              phone: brewery.phone || '',
              website_url: brewery.website_url || brewery.website || '',
              updated_at: brewery.updated_at || new Date().toISOString(),
              created_at: brewery.created_at || new Date().toISOString(),
              address: brewery.address || brewery.street || '',
              email: brewery.email || '',
              description: brewery.description || '',
              zip: brewery.zip || brewery.postal_code || '',
              verified: brewery.verified || false,
              claimed: brewery.claimed || false,
              verificationOpen: brewery.verificationOpen || true,
              ...brewery // Keep any additional fields
            };
          });
          
          setBreweries(formattedBreweries);
          setIsLoading(false);
          return;
        } catch (err) {
          console.error("Error parsing local breweries:", err);
          // Continue to try Supabase if local storage fails
        }
      }
      
      // If no local breweries or parsing failed, try Supabase
      let query = supabase
        .from('breweries')
        .select('*')
        .order('name', { ascending: true });
      
      // Apply pagination if limit is provided
      if (props?.limit && props.limit > 0) {
        const page = props.initialPage || 1;
        const from = (page - 1) * props.limit;
        const to = from + props.limit - 1;
        
        query = query.range(from, to);
      }
      
      const { data, error } = await query;
      
      if (error) {
        throw error;
      }
      
      // Convert to Brewery objects if we have data
      if (data && Array.isArray(data)) {
        // Process breweries from Supabase
        const breweryData = data.map(item => {
          // Create standardized brewery object
          let socialLinks = { facebook: '', twitter: '', instagram: '' };
          if (item.social_links) {
            if (typeof item.social_links === 'string') {
              try {
                socialLinks = JSON.parse(item.social_links);
              } catch (e) {
                console.error('Error parsing social links:', e);
              }
            } else if (typeof item.social_links === 'object' && !Array.isArray(item.social_links)) {
              // Safe access with type assertion for the object case
              const links = item.social_links as Record<string, any>;
              socialLinks = {
                facebook: links.facebook || '',
                twitter: links.twitter || '',
                instagram: links.instagram || '',
              };
            }
          }
          
          return {
            id: item.id,
            name: item.name || '',
            description: item.description || '',
            city: item.city || '',
            state: item.state || '',
            address: item.address || '',
            zip: item.zip || '',
            phone: item.phone || '',
            website: item.website || '',
            email: item.email || '',
            logo: item.logo || '',
            featureImage: item.feature_image || '',
            claimed: item.claimed || false,
            claimable: item.claimable || false,
            verificationOpen: item.verification_open || false,
            socialLinks,
            brewery_type: item.brewery_type || 'micro',
            verified: Boolean(item.verified) || false,
            avatar: item.avatar || '',
            followerCount: item.follower_count || 0,
            likeCount: item.like_count || 0,
            isFollowing: false,
            isLiked: false
          } as Brewery;
        });
        
        setBreweries(breweryData);
      } else {
        // If no data from Supabase, set empty array
        setBreweries([]);
        console.log("No breweries found in Supabase or localStorage");
      }
    } catch (err: any) {
      console.error('Error fetching breweries:', err);
      setError(err.message || 'Error fetching breweries');
      
      toast({
        title: "Error fetching breweries",
        description: err.message || "An error occurred while fetching breweries",
        variant: "destructive",
      });
      
      // Set to empty array on error
      setBreweries([]);
    } finally {
      setIsLoading(false);
    }
  }, [props?.limit, props?.initialPage]);
  
  // Load breweries on mount
  useEffect(() => {
    fetchBreweries();
  }, [fetchBreweries]);
  
  // Provide a function to reload breweries
  const reloadBreweries = useCallback(async () => {
    return fetchBreweries();
  }, [fetchBreweries]);
  
  return {
    breweries,
    setBreweries, // Expose setBreweries for use with the delete all function
    isLoading,
    error,
    reloadBreweries
  };
};
