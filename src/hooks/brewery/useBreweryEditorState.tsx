
import { useState } from "react";
import { Brewery } from "@/types/brewery";
import { toast } from "@/hooks/use-toast";

export const useBreweryEditorState = (breweries: Brewery[], setBreweries: React.Dispatch<React.SetStateAction<Brewery[]>>) => {
  // Editor state
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingBrewery, setEditingBrewery] = useState<Brewery | null>(null);
  const [editedValues, setEditedValues] = useState<Partial<Brewery>>({});

  const openEditor = (brewery: Brewery) => {
    setEditingBrewery(brewery);
    setEditedValues(brewery);
    setIsEditorOpen(true);
  };

  const closeEditor = () => {
    setIsEditorOpen(false);
    setEditingBrewery(null);
    setEditedValues({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = () => {
    if (!editingBrewery) return;

    const updatedBreweries = breweries.map(brewery =>
      brewery.id === editingBrewery.id ? { ...brewery, ...editedValues } : brewery
    );
    localStorage.setItem("breweries", JSON.stringify(updatedBreweries));
    setBreweries(updatedBreweries);
    closeEditor();
    toast({
      title: "Brewery updated",
      description: `${editingBrewery.name} has been updated successfully.`,
    });
  };

  return {
    isEditorOpen,
    editingBrewery,
    editedValues,
    openEditor,
    closeEditor,
    handleInputChange,
    handleSave
  };
};
