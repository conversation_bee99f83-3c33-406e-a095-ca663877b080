
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { handleFileSizeWarning } from "./utils/fileProcessingUtils";

export const useFileValidation = () => {
  const [fileData, setFileData] = useState<{
    fileName: string;
    fileSize: number | null;
  }>({ fileName: "", fileSize: null });

  const validateFile = (file: File | undefined) => {
    if (!file) {
      console.log("No file selected");
      return false;
    }

    console.log(`File selected: ${file.name}, size: ${file.size} bytes`);
    
    try {
      handleFileSizeWarning(file.size);
      setFileData({ fileName: file.name, fileSize: file.size });
      return true;
    } catch (error) {
      console.error("File validation error:", error);
      toast({
        title: "Invalid file",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
      return false;
    }
  };

  const clearFileData = () => {
    setFileData({ fileName: "", fileSize: null });
  };

  return {
    fileData,
    validateFile,
    clearFileData
  };
};
