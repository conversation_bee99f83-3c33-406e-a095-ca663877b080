
import { useState, useMemo } from 'react';
import { Brewery } from '@/types/brewery.patch';

export interface UseBreweryFiltersProps {
  breweries: Brewery[];
  initialFilters?: {
    searchTerm?: string;
    filterColumn?: string;
    stateFilter?: string;
    cityFilter?: string;
    verificationFilter?: string;
  };
}

export const useBreweryFilters = (props: UseBreweryFiltersProps) => {
  const { breweries, initialFilters } = props;
  
  // Search and filtering state
  const [searchTerm, setSearchTerm] = useState(initialFilters?.searchTerm || '');
  const [filterColumn, setFilterColumn] = useState(initialFilters?.filterColumn || 'all');
  const [stateFilter, setStateFilter] = useState(initialFilters?.stateFilter || 'all');
  const [cityFilter, setCityFilter] = useState(initialFilters?.cityFilter || 'all');
  const [verificationFilter, setVerificationFilter] = useState(initialFilters?.verificationFilter || 'all');
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Extract available states for filtering
  const availableStates = useMemo(() => {
    const states = new Set<string>();
    breweries.forEach(brewery => {
      if (brewery.state) {
        states.add(brewery.state);
      }
    });
    return Array.from(states).sort();
  }, [breweries]);
  
  // Extract available cities for filtering
  const availableCities = useMemo(() => {
    const cities = new Set<string>();
    breweries.forEach(brewery => {
      if (brewery.city) {
        cities.add(brewery.city);
      }
    });
    return Array.from(cities).sort();
  }, [breweries]);
  
  // Filter options for columns
  const filterOptions = useMemo(() => {
    const allFields = new Set<string>();
    
    // Add common brewery fields
    ['name', 'brewery_type', 'street', 'city', 'state', 'zip', 'phone', 'website', 'email'].forEach(field => {
      allFields.add(field);
    });
    
    // Add any other fields from breweries data
    breweries.forEach(brewery => {
      Object.keys(brewery).forEach(key => {
        if (!['id', 'created_at', 'updated_at', 'socialLinks', 'claimed', 'verified', 'verificationOpen'].includes(key)) {
          allFields.add(key);
        }
      });
    });
    
    return Array.from(allFields).sort();
  }, [breweries]);
  
  // Filter breweries based on search term and filters
  const filteredBreweries = useMemo(() => {
    return breweries.filter(brewery => {
      // Handle state filter
      if (stateFilter !== 'all' && brewery.state !== stateFilter) {
        return false;
      }
      
      // Handle city filter
      if (cityFilter !== 'all' && brewery.city !== cityFilter) {
        return false;
      }
      
      // Handle verification filter
      if (verificationFilter !== 'all') {
        if (verificationFilter === 'verified' && !brewery.verified) return false;
        if (verificationFilter === 'claimed' && !brewery.claimed) return false;
        if (verificationFilter === 'unclaimed' && (!brewery.verificationOpen || brewery.claimed)) return false;
      }
      
      // Handle search term
      if (!searchTerm.trim()) {
        return true;
      }
      
      const term = searchTerm.toLowerCase();
      
      // If specific column filter is set
      if (filterColumn !== 'all') {
        const value = String(brewery[filterColumn as keyof Brewery] || '').toLowerCase();
        return value.includes(term);
      }
      
      // Search across all searchable fields
      return (
        (brewery.name?.toLowerCase().includes(term) || false) ||
        (brewery.brewery_type?.toLowerCase().includes(term) || false) || // Fixed property name
        (brewery.city?.toLowerCase().includes(term) || false) ||
        (brewery.state?.toLowerCase().includes(term) || false) ||
        (brewery.zip?.toLowerCase().includes(term) || false) ||
        (brewery.phone?.toLowerCase().includes(term) || false) ||
        (brewery.website?.toLowerCase().includes(term) || false) ||
        (brewery.email?.toLowerCase().includes(term) || false)
      );
    });
  }, [breweries, searchTerm, filterColumn, stateFilter, cityFilter, verificationFilter]);
  
  // Handle filter value application (compatibility with old interface)
  const [filterValue, setFilterValue] = useState('');
  const applyFilter = () => {
    // This is maintained for backwards compatibility
    console.log("Filter applied:", filterColumn, filterValue);
    // The actual filtering happens in the useMemo above
  };
  
  return {
    searchTerm,
    setSearchTerm,
    filterColumn,
    setFilterColumn,
    filterValue,
    setFilterValue,
    applyFilter,
    stateFilter,
    setStateFilter,
    cityFilter,
    setCityFilter,
    verificationFilter,
    setVerificationFilter,
    itemsPerPage,
    setItemsPerPage,
    availableStates,
    availableCities,
    filterOptions,
    filteredBreweries
  };
};
