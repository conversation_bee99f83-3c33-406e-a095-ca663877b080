
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

export const useImportErrorHandling = () => {
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleImportError = (error: unknown) => {
    console.error("Import error:", error);
    const message = error instanceof Error ? error.message : "Unknown error occurred";
    setErrorMessage(message);
    
    toast({
      title: "Import failed",
      description: message,
      variant: "destructive",
    });
    
    return message;
  };

  const clearError = () => {
    setErrorMessage(null);
  };

  return {
    errorMessage,
    handleImportError,
    clearError
  };
};
