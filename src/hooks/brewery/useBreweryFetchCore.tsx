
import { useCallback } from "react";
import { Brewery } from "@/types/brewery";
import { toast } from "@/hooks/use-toast";

export const useBreweryFetchCore = (
  setBreweries: React.Dispatch<React.SetStateAction<Brewery[]>>,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setColumns: React.Dispatch<React.SetStateAction<string[]>>,
  setFieldTypes: React.Dispatch<React.SetStateAction<Record<string, string>>>
) => {
  const fetchBreweries = useCallback(async () => {
    setIsLoading(true);
    try {
      const storedBreweries = localStorage.getItem("breweries");
      const initialBreweries = storedBreweries ? JSON.parse(storedBreweries) : [];
      setBreweries(initialBreweries);
      
      // Enhanced column extraction with type detection
      if (initialBreweries.length > 0) {
        const firstBrewery = initialBreweries[0];
        const extractedColumns = Object.keys(firstBrewery);
        setColumns(extractedColumns);
        
        // Automatically detect field types from existing data
        const detectedTypes = extractedColumns.reduce((types, field) => {
          const fieldValue = firstBrewery[field];
          const normalizedField = field.toLowerCase();
          
          // Infer type based on field name and sample value
          if (normalizedField.includes('phone') || normalizedField.includes('tel')) {
            types[field] = 'tel';
          } else if (normalizedField.includes('web') || normalizedField.includes('url') || 
                   (typeof fieldValue === 'string' && fieldValue.includes('http'))) {
            types[field] = 'url';
          } else if (normalizedField.includes('email') || 
                   (typeof fieldValue === 'string' && fieldValue.includes('@'))) {
            types[field] = 'email';
          } else if (normalizedField.includes('zip') || normalizedField.includes('postal')) {
            types[field] = 'zip';
          } else if (normalizedField.includes('address') || normalizedField.includes('street')) {
            types[field] = 'address';
          } else {
            types[field] = 'text';
          }
          return types;
        }, {} as Record<string, string>);
        
        setFieldTypes(detectedTypes);
      }
    } catch (error) {
      console.error("Error fetching breweries:", error);
      toast({
        title: "Error fetching breweries",
        description: "Failed to load brewery data from local storage",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [setBreweries, setIsLoading, setColumns, setFieldTypes]);

  return {
    fetchBreweries
  };
};
