
import { useState } from 'react';
import { Brewery } from '@/types/brewery.patch';

export interface UseFilterApplicationProps {
  onFilterApply?: (filter: { column: string; value: string }) => void;
}

export const useFilterApplication = (props: UseFilterApplicationProps = {}) => {
  const { onFilterApply } = props;
  
  const [filterValue, setFilterValue] = useState('');
  const [filterColumn, setFilterColumn] = useState('all');
  
  const applyFilter = () => {
    if (onFilterApply) {
      onFilterApply({ column: filterColumn, value: filterValue });
    }
    console.log(`Applied filter: ${filterColumn} = ${filterValue}`);
  };
  
  return {
    filterValue,
    setFilterValue,
    filterColumn,
    setFilterColumn,
    applyFilter
  };
};
