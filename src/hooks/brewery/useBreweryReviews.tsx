
import { useState, useCallback } from 'react';
import { useAuth } from '@/auth';
import { supabase } from '@/integrations/supabase/client';
import { Review, ReviewFormData } from '@/types/review';
import { toast } from '@/hooks/use-toast';

export const useBreweryReviews = (breweryId: string) => {
  const { user } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userReview, setUserReview] = useState<Review | null>(null);

  const fetchReviews = useCallback(async () => {
    try {
      setIsLoading(true);
      // First get the reviews
      const { data, error } = await supabase
        .from('brewery_reviews')
        .select('*')
        .eq('brewery_id', breweryId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching reviews:', error);
        toast({
          title: 'Error',
          description: 'Failed to load reviews. Please try again later.',
          variant: 'destructive',
        });
        return;
      }

      // For each review, try to get the user profile info separately
      const formattedReviews: Review[] = await Promise.all(
        data.map(async (review) => {
          // Fetch user profile for this review
          const { data: profileData } = await supabase
            .from('profiles')
            .select('email')
            .eq('id', review.user_id)
            .single();
          
          return {
            ...review,
            user_name: profileData?.email?.split('@')[0] || 'Anonymous',
          };
        })
      );

      setReviews(formattedReviews);

      // Check if the current user has a review
      if (user) {
        const userReviewData = data.find(review => review.user_id === user.id);
        setUserReview(userReviewData || null);
      }
    } catch (error) {
      console.error('Error in fetchReviews:', error);
    } finally {
      setIsLoading(false);
    }
  }, [breweryId, user]);

  const submitReview = async (formData: ReviewFormData) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please log in to submit a review',
        variant: 'destructive',
      });
      return;
    }

    try {
      const reviewData = {
        brewery_id: breweryId,
        user_id: user.id,
        rating: formData.rating,
        title: formData.title,
        content: formData.content,
      };

      let query;
      
      // Update if the user already has a review, otherwise insert
      if (userReview) {
        query = supabase
          .from('brewery_reviews')
          .update(reviewData)
          .eq('id', userReview.id);
      } else {
        query = supabase
          .from('brewery_reviews')
          .insert(reviewData);
      }

      const { error } = await query;

      if (error) {
        console.error('Error submitting review:', error);
        toast({
          title: 'Error',
          description: 'Failed to submit review. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Success',
        description: userReview ? 'Your review has been updated.' : 'Your review has been submitted.',
      });

      // Refresh reviews
      fetchReviews();
    } catch (error) {
      console.error('Error in submitReview:', error);
    }
  };

  const deleteReview = async (reviewId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('brewery_reviews')
        .delete()
        .eq('id', reviewId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting review:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete review. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Success',
        description: 'Your review has been deleted.',
      });

      setUserReview(null);
      fetchReviews();
    } catch (error) {
      console.error('Error in deleteReview:', error);
    }
  };

  return {
    reviews,
    isLoading,
    userReview,
    fetchReviews,
    submitReview,
    deleteReview,
  };
};
