
import { Brewery } from "@/types/brewery";
import { toast } from "@/hooks/use-toast";

export const useBreweryOperations = (
  breweries: Brewery[],
  setBreweries: React.Dispatch<React.SetStateAction<Brewery[]>>
) => {
  const handleDelete = (id: string) => {
    const updatedBreweries = breweries.filter(brewery => brewery.id !== id);
    localStorage.setItem("breweries", JSON.stringify(updatedBreweries));
    setBreweries(updatedBreweries);
    toast({
      title: "Brewery deleted",
      description: "The brewery has been successfully deleted.",
    });
  };

  const handleDeleteAllBreweries = () => {
    localStorage.removeItem("breweries");
    setBreweries([]);
    toast({
      title: "All breweries deleted",
      description: "All brewery data has been successfully deleted.",
    });
  };

  return {
    handleDelete,
    handleDeleteAllBreweries
  };
};
