
import { useState } from "react";
import { Brewery } from "@/types/brewery";

interface UseEditorStateProps {
  initialBrewery?: Brewery | null;
}

export const useEditorState = ({ initialBrewery = null }: UseEditorStateProps = {}) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingBrewery, setEditingBrewery] = useState<Brewery | null>(initialBrewery);
  const [editedValues, setEditedValues] = useState<Record<string, any>>({});
  
  const openEditor = (brewery: Brewery) => {
    setEditingBrewery(brewery);
    setEditedValues(brewery);
    setIsEditorOpen(true);
  };
  
  const closeEditor = () => {
    setIsEditorOpen(false);
    setEditingBrewery(null);
    setEditedValues({});
  };
  
  const handleInputChange = (
    key: string, 
    value: any
  ) => {
    setEditedValues(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  return {
    isEditorOpen,
    editingBrewery,
    editedValues,
    openEditor,
    closeEditor,
    handleInputChange,
    setEditedValues
  };
};
