
import { useState, useEffect, useCallback } from 'react';
import { GalleryImage } from '@/types/brewery.patch';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export const useGallery = (breweryId: string) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadingImage, setUploadingImage] = useState(false);

  const fetchGalleryImages = useCallback(async () => {
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('brewery_gallery')
        .select('*')
        .eq('brewery_id', breweryId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setImages(data || []);
    } catch (error) {
      console.error('Error fetching gallery images:', error);
      toast({
        title: 'Failed to load gallery',
        description: 'Could not load gallery images. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [breweryId]);

  useEffect(() => {
    fetchGalleryImages();
  }, [fetchGalleryImages]);

  const handleImageUpload = async (file: File, title: string) => {
    if (!breweryId) {
      toast({
        title: 'Upload failed',
        description: 'No brewery selected for image upload',
        variant: 'destructive',
      });
      return;
    }
    
    setUploadingImage(true);
    
    try {
      // Upload image to storage
      const fileName = `${Date.now()}-${file.name}`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('brewery-images')
        .upload(`gallery/${breweryId}/${fileName}`, file);
      
      if (uploadError) throw uploadError;
      
      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('brewery-images')
        .getPublicUrl(uploadData.path);
      
      // Save image metadata to database
      const { error: insertError } = await supabase
        .from('brewery_gallery')
        .insert({
          brewery_id: breweryId,
          image_url: publicUrlData.publicUrl,
          title: title || null,
        });
      
      if (insertError) throw insertError;
      
      // Refresh gallery
      fetchGalleryImages();
      
      toast({
        title: 'Image uploaded',
        description: 'Image was successfully added to your gallery',
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload failed',
        description: 'Failed to upload image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setUploadingImage(false);
    }
  };

  const handleDeleteImage = async (imageId: string, imageUrl: string) => {
    try {
      // Delete from database
      const { error: deleteError } = await supabase
        .from('brewery_gallery')
        .delete()
        .eq('id', imageId);
      
      if (deleteError) throw deleteError;
      
      // Try to delete from storage
      // Extract path from URL
      const urlParts = imageUrl.split('/');
      const filename = urlParts[urlParts.length - 1];
      const path = `gallery/${breweryId}/${filename}`;
      
      await supabase.storage
        .from('brewery-images')
        .remove([path])
        .catch(err => console.warn('Could not delete file from storage:', err));
      
      // Update state
      setImages(images.filter(img => img.id !== imageId));
      
      toast({
        title: 'Image deleted',
        description: 'Image was successfully removed from your gallery',
      });
    } catch (error) {
      console.error('Error deleting image:', error);
      toast({
        title: 'Delete failed',
        description: 'Failed to delete image. Please try again.',
        variant: 'destructive',
      });
      throw error;
    }
  };

  return {
    images,
    isLoading,
    uploadingImage,
    handleImageUpload,
    handleDeleteImage
  };
};
