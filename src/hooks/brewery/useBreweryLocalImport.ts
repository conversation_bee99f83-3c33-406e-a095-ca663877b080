
import { useState } from "react";
import { Brewery } from "@/types/brewery";

/**
 * Hook for importing brewery data to local storage
 */
export const useBreweryLocalImport = () => {
  const [status, setStatus] = useState<string>("idle");
  
  /**
   * Import brewery data to local storage
   */
  const importToLocalStorage = async (breweryData: Brewery[]): Promise<{
    successCount: number;
    updatedCount: number;
  }> => {
    console.log("Importing to localStorage:", breweryData.length, "records");
    setStatus("importing");
    
    try {
      // Get existing breweries from local storage
      const existingBreweriesJSON = localStorage.getItem("breweries");
      const existingBreweries = existingBreweriesJSON 
        ? JSON.parse(existingBreweriesJSON) as Brewery[] 
        : [];
      
      // Track existing IDs for update vs insert
      const existingIds = new Set(existingBreweries.map(b => b.id));
      
      let successCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;
      
      // Process each brewery
      for (const brewery of breweryData) {
        try {
          // Validate required fields - stricter validation
          if (!brewery.name || brewery.name.trim() === '') {
            console.warn("Skipping brewery with no name:", brewery);
            skippedCount++;
            continue;
          }
          
          // Additional data quality check - ensure we have at least one more field with data
          const hasAdditionalData = Object.entries(brewery).some(([key, value]) => 
            key !== 'name' && key !== 'id' && value && String(value).trim() !== ''
          );
          
          if (!hasAdditionalData) {
            console.warn("Skipping brewery with only name:", brewery);
            skippedCount++;
            continue;
          }
          
          // Check if this is an update or insert
          if (existingIds.has(brewery.id)) {
            // Update existing brewery
            const index = existingBreweries.findIndex(b => b.id === brewery.id);
            if (index !== -1) {
              existingBreweries[index] = { 
                ...existingBreweries[index], 
                ...brewery,
                updated_at: new Date().toISOString()
              };
              updatedCount++;
            }
          } else {
            // Add new brewery
            existingBreweries.push({
              ...brewery,
              id: brewery.id || `brewery-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
            successCount++;
          }
        } catch (error) {
          console.error("Error processing brewery:", brewery, error);
          // Continue with next brewery
        }
      }
      
      // Save all breweries back to local storage
      localStorage.setItem("breweries", JSON.stringify(existingBreweries));
      console.log(`LocalStorage import completed: ${successCount} added, ${updatedCount} updated, ${skippedCount} skipped`);
      
      if (skippedCount > 0) {
        console.warn(`Skipped ${skippedCount} breweries due to insufficient data`);
      }
      
      setStatus("succeeded");
      return { successCount, updatedCount };
    } catch (error) {
      console.error("Error in localStorage import:", error);
      setStatus("failed");
      throw error;
    }
  };
  
  return {
    importToLocalStorage,
    status
  };
};
