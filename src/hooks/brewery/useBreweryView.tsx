
import { useState, useEffect, useMemo } from 'react';
import { Brewery } from '@/types/brewery.patch';
import { useBreweryHandlers } from './useBreweryHandlers';
import { useBreweryFilters } from './useBreweryFilters';
import { useBreweryPagination } from './useBreweryPagination';
import { useBrewerySorting } from './useBrewerySorting';

interface UseBreweryViewProps {
  breweries: Brewery[];
  onRefresh: () => Promise<void>;
}

export const useBreweryView = ({ breweries, onRefresh }: UseBreweryViewProps) => {
  // View mode state
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('grid');
  
  // Selected brewery and delete dialog state
  const [selectedBrewery, setSelectedBrewery] = useState<Brewery | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Filters state
  const [filters, setFilters] = useState<Record<string, string>>({});
  
  // Use sorting hook
  const { sortConfig, setSortConfig } = useBrewerySorting({
    initialSortKey: 'name',
    initialDirection: 'asc'
  });
  
  // Use filter hook
  const {
    searchTerm,
    setSearchTerm,
    filterColumn,
    setFilterColumn,
    stateFilter,
    setStateFilter,
    verificationFilter,
    setVerificationFilter,
    itemsPerPage,
    setItemsPerPage,
    availableStates: breweryStates,
    filteredBreweries
  } = useBreweryFilters({ breweries });
  
  // Use sorting on filtered breweries
  const sortedBreweries = useMemo(() => {
    if (!sortConfig) return filteredBreweries;
    
    return [...filteredBreweries].sort((a, b) => {
      const aValue = a[sortConfig.key as keyof Brewery] || '';
      const bValue = b[sortConfig.key as keyof Brewery] || '';
      
      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      // Handle number comparison
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' 
          ? aValue - bValue
          : bValue - aValue;
      }
      
      // Default comparison for other types
      return 0;
    });
  }, [filteredBreweries, sortConfig]);
  
  // Use pagination hook with sorted breweries
  const {
    currentPage,
    setCurrentPage,
    displayData: paginatedBreweries,
    totalPages
  } = useBreweryPagination({
    breweries,
    filteredBreweries: sortedBreweries,
    itemsPerPage
  });
  
  // Use handlers
  const {
    handlePageChange,
    handleSort,
    handleFilterChange,
    handleViewModeChange,
    handleDeleteClick,
    handleEditClick,
    handleDeleteConfirm,
    handleSearch
  } = useBreweryHandlers({
    setCurrentPage,
    setViewMode,
    setShowDeleteDialog,
    setSelectedBrewery,
    setFilters,
    currentPage,
    itemsPerPage,
    totalPages,
    filteredBreweries,
    onRefresh,
    setSortConfig,
    sortConfig
  });
  
  // Reset page function
  const resetPage = () => {
    setCurrentPage(1);
  };
  
  // Update filters based on the search/filter inputs
  useEffect(() => {
    // Only update if we have actual changes
    if (
      filters.searchTerm !== searchTerm ||
      filters.filterColumn !== filterColumn ||
      filters.state !== stateFilter ||
      filters.verification !== verificationFilter
    ) {
      setFilters({
        searchTerm,
        filterColumn,
        state: stateFilter,
        verification: verificationFilter
      });
    }
  }, [searchTerm, filterColumn, stateFilter, verificationFilter]);

  // Update search/filter inputs based on filter state
  useEffect(() => {
    if (filters.searchTerm !== undefined) setSearchTerm(filters.searchTerm);
    if (filters.filterColumn !== undefined) setFilterColumn(filters.filterColumn);
    if (filters.state !== undefined) setStateFilter(filters.state);
    if (filters.verification !== undefined) setVerificationFilter(filters.verification);
  }, [filters]);
  
  // Render verification badge function for reuse in both card and table views
  const renderVerificationBadge = (brewery: Brewery) => {
    if (brewery.verified) {
      return 'verified';
    } else if (brewery.claimed) {
      return 'claimed';
    } else if (brewery.verificationOpen) {
      return 'unclaimed';
    }
    return null;
  };

  return {
    filteredBreweries,
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,
    viewMode,
    setViewMode,
    selectedBrewery,
    setSelectedBrewery,
    showDeleteDialog,
    setShowDeleteDialog,
    filters,
    setFilters,
    sortConfig,
    setSortConfig,
    totalPages,
    paginatedBreweries,
    breweryStates,
    handlePageChange,
    handleViewModeChange,
    handleDeleteClick,
    handleEditClick,
    handleDeleteConfirm,
    handleSearch,
    handleFilterChange,
    resetPage,
    renderVerificationBadge
  };
};
