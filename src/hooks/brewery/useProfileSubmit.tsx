
import { useState } from 'react';
import { Brewery } from '@/types/brewery.patch';
import { ProfileFormValues } from '@/components/brewery/profile/ProfileFormSchema';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export const useProfileSubmit = (
  brewery: Brewery | null, 
  setBreweryState: React.Dispatch<React.SetStateAction<Brewery | null>>
) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleProfileSubmit = async (data: ProfileFormValues): Promise<void> => {
    if (!brewery) return;
    
    setIsSubmitting(true);
    try {
      // Prepare update object
      const updatedData = {
        name: data.name,
        description: data.description,
        address: data.address,
        city: data.city,
        state: data.state,
        zip: data.zip,
        phone: data.phone,
        website: data.website,
        email: data.email,
        brewery_type: data.brewery_type // Ensure brewery_type is included in the update
      };
      
      // Update in Supabase
      const { error } = await supabase
        .from('breweries')
        .update(updatedData)
        .eq('id', brewery.id);
        
      if (error) throw error;
      
      // Update local state
      setBreweryState((prev: Brewery | null) => {
        if (!prev) return null;
        return { ...prev, ...updatedData };
      });
      
      toast({
        title: "Profile updated",
        description: "Your brewery profile has been updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating profile:', error);
      
      toast({
        title: "Update failed",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return {
    isSubmitting,
    handleProfileSubmit
  };
};
