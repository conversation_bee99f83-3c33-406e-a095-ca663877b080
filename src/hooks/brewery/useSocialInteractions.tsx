
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/auth';
import { toast } from '@/hooks/use-toast';

export const useSocialInteractions = (breweryId: string) => {
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [likeCount, setLikeCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Function to check if the user is following or has liked the brewery
  const checkInteractionStatus = useCallback(async () => {
    if (!user || !breweryId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Check if user is following the brewery
      const { data: followData, error: followError } = await supabase
        .from('brewery_followers')
        .select('*')
        .eq('brewery_id', breweryId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (followError) throw followError;
      
      // Check if user has liked the brewery
      const { data: likeData, error: likeError } = await supabase
        .from('brewery_likes')
        .select('*')
        .eq('brewery_id', breweryId)
        .eq('user_id', user.id)
        .maybeSingle();
        
      if (likeError) throw likeError;

      // Get counts
      const { data: breweryData, error: breweryError } = await supabase
        .from('breweries')
        .select('follower_count, like_count')
        .eq('id', breweryId)
        .single();
        
      if (breweryError) throw breweryError;

      setIsFollowing(!!followData);
      setIsLiked(!!likeData);
      setFollowerCount(breweryData.follower_count || 0);
      setLikeCount(breweryData.like_count || 0);
    } catch (error: any) {
      console.error('Error checking interaction status:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user, breweryId]);

  // Function to toggle following status
  const toggleFollow = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to follow breweries",
        variant: "destructive",
      });
      return;
    }

    try {
      if (isFollowing) {
        // Unfollow
        const { error } = await supabase
          .from('brewery_followers')
          .delete()
          .eq('brewery_id', breweryId)
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        setIsFollowing(false);
        setFollowerCount(prev => Math.max(0, prev - 1));
        
        toast({
          title: "Unfollowed",
          description: "You are no longer following this brewery",
        });
      } else {
        // Follow
        const { error } = await supabase
          .from('brewery_followers')
          .insert({
            brewery_id: breweryId,
            user_id: user.id
          });
          
        if (error) throw error;
        
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        
        toast({
          title: "Following",
          description: "You are now following this brewery",
        });
      }
    } catch (error: any) {
      console.error('Error toggling follow status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update follow status",
        variant: "destructive",
      });
    }
  };

  // Function to toggle like status
  const toggleLike = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to like breweries",
        variant: "destructive",
      });
      return;
    }

    try {
      if (isLiked) {
        // Unlike
        const { error } = await supabase
          .from('brewery_likes')
          .delete()
          .eq('brewery_id', breweryId)
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        setIsLiked(false);
        setLikeCount(prev => Math.max(0, prev - 1));
        
        toast({
          title: "Unliked",
          description: "You no longer like this brewery",
        });
      } else {
        // Like
        const { error } = await supabase
          .from('brewery_likes')
          .insert({
            brewery_id: breweryId,
            user_id: user.id
          });
          
        if (error) throw error;
        
        setIsLiked(true);
        setLikeCount(prev => prev + 1);
        
        toast({
          title: "Liked",
          description: "You now like this brewery",
        });
      }
    } catch (error: any) {
      console.error('Error toggling like status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update like status",
        variant: "destructive",
      });
    }
  };

  return {
    isFollowing,
    isLiked,
    followerCount,
    likeCount,
    isLoading,
    toggleFollow,
    toggleLike,
    checkInteractionStatus
  };
};
