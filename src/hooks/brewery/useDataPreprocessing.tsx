
import { useState } from "react";
import { parseCSV, validateRequiredFields, detectFieldType } from "@/lib/csv";
import { toast } from "@/hooks/use-toast";
import { showLargeDatasetWarning } from "./utils/fileProcessingUtils";

export const useDataPreprocessing = () => {
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [fieldTypes, setFieldTypes] = useState<Record<string, string>>({});
  const [dataQualityIssues, setDataQualityIssues] = useState<{
    emptyFields: Record<string, number>;
    totalRows: number;
    incompleteRows: number;
  } | null>(null);

  const preprocessData = async (file: File) => {
    console.log(`Preprocessing data from file: ${file.name}`);
    
    try {
      const result = await parseCSV(file);
      const { 
        data: rawBreweryData, 
        columns, 
        duplicates, 
        fieldMapping: mappedFields,
        fieldTypes: detectedTypes 
      } = result;
      
      console.log("Parse CSV result:", result);
      
      // Filter out empty breweries (those without a name)
      const breweryData = rawBreweryData.filter(brewery => {
        // Check if brewery has a name that's not empty
        const hasName = brewery.name && brewery.name.trim() !== '';
        if (!hasName) {
          console.warn("Filtering out brewery without name:", brewery);
        }
        return hasName;
      });
      
      console.log(`Filtered out ${rawBreweryData.length - breweryData.length} empty breweries`);
      
      // Analyze data quality
      const emptyFields: Record<string, number> = {};
      let incompleteRows = 0;
      
      // Count empty fields for each column
      columns.forEach(column => {
        emptyFields[column] = 0;
        breweryData.forEach(brewery => {
          if (!brewery[column] || brewery[column].toString().trim() === '') {
            emptyFields[column]++;
          }
        });
      });
      
      // Count rows with significant missing data
      incompleteRows = breweryData.filter(brewery => {
        const filledFields = columns.filter(column => 
          brewery[column] && brewery[column].toString().trim() !== ''
        ).length;
        // Consider a row incomplete if less than 50% of fields are filled
        return filledFields < columns.length * 0.5;
      }).length;
      
      // Set data quality metrics
      setDataQualityIssues({
        emptyFields,
        totalRows: breweryData.length,
        incompleteRows
      });
      
      // Always update field mapping even if it's the same as original
      if (mappedFields) {
        console.log("Setting field mapping:", mappedFields);
        setFieldMapping(mappedFields);
        
        // Validate required fields
        const { valid, missingFields } = validateRequiredFields(mappedFields);
        if (!valid) {
          throw new Error(`CSV missing required fields: ${missingFields.join(', ')}`);
        }
        
        // Count the fields that were successfully mapped (different from original)
        const mappedFieldCount = Object.entries(mappedFields)
          .filter(([original, mapped]) => original !== mapped).length;
          
        if (mappedFieldCount > 0) {
          toast({
            title: "Smart field mapping applied",
            description: `${mappedFieldCount} fields were automatically mapped to standard fields`,
          });
        }
      } else {
        console.warn("No field mapping received from parseCSV");
        setFieldMapping({});
      }
      
      if (detectedTypes) {
        console.log("Field types detected:", detectedTypes);
        setFieldTypes(detectedTypes);
        
        // Count special field types that were detected
        const specialFields = Object.entries(detectedTypes)
          .filter(([field, type]) => type !== 'text').length;
          
        if (specialFields > 0) {
          toast({
            title: "Field types detected",
            description: `${specialFields} special field types detected (phone, zip, address, etc.)`,
          });
        }
      }
      
      // Show warning for large datasets
      showLargeDatasetWarning(breweryData.length);
      
      // Show data quality warnings if needed
      if (incompleteRows > 0) {
        toast({
          title: "Data quality warning",
          description: `${incompleteRows} rows have significant missing data and may need attention`,
          variant: "destructive"
        });
      }
      
      return { 
        breweryData, 
        columns, 
        duplicates, 
        fieldMapping: mappedFields,
        dataQualityIssues: {
          emptyFields,
          totalRows: breweryData.length,
          incompleteRows
        }
      };
    } catch (error) {
      console.error("Error preprocessing data:", error);
      throw error;
    }
  };

  return {
    fieldMapping,
    fieldTypes,
    dataQualityIssues,
    preprocessData
  };
};
