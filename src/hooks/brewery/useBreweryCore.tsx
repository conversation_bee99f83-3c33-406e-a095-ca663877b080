import { useState, useEffect, useCallback } from 'react';
import { Brewery } from '@/types/brewery';

interface UseBreweryCoreProps {
  initialBreweries?: Brewery[];
}

export const useBreweryCore = ({ initialBreweries }: UseBreweryCoreProps = {}) => {
  const [breweries, setBreweries] = useState<Brewery[]>(initialBreweries || []);
  const [breweriesLoading, setBreweriesLoading] = useState(false);
  const [columns, setColumns] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterColumn, setFilterColumn] = useState('');
  const [stateFilter, setStateFilter] = useState('');
  const [availableStates, setAvailableStates] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);

  const fetchBreweries = useCallback(async () => {
    setBreweriesLoading(true);
    try {
      const storedBreweries = localStorage.getItem("breweries");
      const loadedBreweries = storedBreweries ? JSON.parse(storedBreweries) : [];
      setBreweries(loadedBreweries);

      // Extract columns from the first brewery object
      if (loadedBreweries.length > 0) {
        const firstBrewery = loadedBreweries[0];
        const breweryColumns = Object.keys(firstBrewery);
        setColumns(breweryColumns);

        // Extract available states
        const states = [...new Set(loadedBreweries.map((brewery: Brewery) => brewery.state).filter(Boolean))] as string[];
        setAvailableStates(states);
      } else {
        setColumns([]);
        setAvailableStates([]);
      }
    } catch (error) {
      console.error("Error fetching breweries:", error);
    } finally {
      setBreweriesLoading(false);
    }
    return breweries;
  }, [breweries]);

  useEffect(() => {
    fetchBreweries();
  }, [fetchBreweries]);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedBreweries = () => {
    if (!sortConfig) {
      return [...breweries];
    }

    return [...breweries].sort((a: any, b: any) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const handleSearch = useCallback(async (term: string) => {
    setBreweriesLoading(true);
    await fetchBreweries();
    setBreweriesLoading(false);
  }, [fetchBreweries]);

  useEffect(() => {
    if (searchTerm) {
      handleSearch(searchTerm);
    }
  }, [searchTerm, handleSearch]);

  const filteredBreweries = () => {
    let filtered = sortedBreweries();

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter((brewery: any) => {
        if (!filterColumn) {
          return Object.values(brewery).some(value =>
            value && value.toString().toLowerCase().includes(term)
          );
        } else {
          const columnValue = brewery[filterColumn];
          return columnValue && columnValue.toString().toLowerCase().includes(term);
        }
      });
    }

    if (stateFilter) {
      filtered = filtered.filter((brewery: Brewery) => brewery.state === stateFilter);
    }

    return filtered;
  };

  const totalItems = filteredBreweries().length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const paginatedBreweries = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredBreweries().slice(startIndex, endIndex);
  };

  const onPageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    breweries: paginatedBreweries(),
    breweriesLoading,
    columns,
    setColumns,
    currentPage,
    setCurrentPage,
    itemsPerPage,
    setItemsPerPage,
    searchTerm,
    setSearchTerm,
    filterColumn,
    setFilterColumn,
    stateFilter,
    setStateFilter,
    availableStates,
    totalItems,
    totalPages,
    onPageChange,
    fetchBreweries,
    sortConfig,
    handleSort
  };
};
