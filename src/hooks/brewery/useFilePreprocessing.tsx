
import { useState } from "react";
import { parseCSV } from "@/lib/csv";
import { toast } from "@/hooks/use-toast";
import { showLargeDatasetWarning } from "./utils/fileProcessingUtils";

export const useFilePreprocessing = () => {
  const [fileData, setFileData] = useState<{
    fileName: string;
    fileSize: number | null;
    rowCount: number | null;
  }>({ fileName: "", fileSize: null, rowCount: null });

  const preprocessFile = async (file: File) => {
    console.log(`Preprocessing file: ${file.name}`);
    setFileData({ fileName: file.name, fileSize: file.size, rowCount: null });
    
    try {
      const result = await parseCSV(file);
      const { 
        data: rawBreweryData, 
        columns, 
        duplicates, 
        fieldMapping: mappedFields,
        fieldTypes: detectedTypes 
      } = result;
      
      console.log("Parse CSV result:", { 
        rowCount: rawBreweryData.length, 
        columnCount: columns.length,
        duplicates
      });
      
      // Filter out empty breweries (those without a name)
      const breweryData = rawBreweryData.filter(brewery => {
        const hasName = brewery.name && brewery.name.trim() !== '';
        if (!hasName) {
          console.warn("Filtering out brewery without name:", brewery);
        }
        return hasName;
      });
      
      console.log(`Filtered out ${rawBreweryData.length - breweryData.length} empty breweries`);
      
      // Update file data with row count
      setFileData(prev => ({ ...prev, rowCount: breweryData.length }));
      
      showLargeDatasetWarning(breweryData.length);
      
      return {
        breweryData,
        columns,
        duplicates,
        fieldMapping: mappedFields,
        fieldTypes: detectedTypes
      };
    } catch (error) {
      console.error("Error preprocessing file:", error);
      toast({
        title: "File processing error",
        description: error instanceof Error ? error.message : "Unknown error processing file",
        variant: "destructive"
      });
      throw error;
    }
  };

  const clearFileData = () => {
    setFileData({ fileName: "", fileSize: null, rowCount: null });
  };

  return {
    fileData,
    preprocessFile,
    clearFileData
  };
};
