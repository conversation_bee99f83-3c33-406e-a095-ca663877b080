
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export const useBreweryFollowers = (
  breweryId: string, 
  userId: string | undefined,
  isFollowing: boolean,
  setIsFollowing: (value: boolean) => void,
  setFollowerCount: (updater: (prev: number) => number) => void
) => {
  const toggleFollow = useCallback(async () => {
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to follow breweries",
        variant: "destructive",
      });
      return;
    }

    try {
      if (isFollowing) {
        // Unfollow
        const { error } = await supabase
          .from('brewery_followers')
          .delete()
          .eq('brewery_id', breweryId)
          .eq('user_id', userId);
          
        if (error) throw error;
        
        setIsFollowing(false);
        setFollowerCount(prev => Math.max(0, prev - 1));
        
        toast({
          title: "Unfollowed",
          description: "You are no longer following this brewery",
        });
      } else {
        // Follow
        const { error } = await supabase
          .from('brewery_followers')
          .insert({
            brewery_id: breweryId,
            user_id: userId
          });
          
        if (error) throw error;
        
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        
        toast({
          title: "Following",
          description: "You are now following this brewery",
        });
      }
    } catch (error: any) {
      console.error('Error toggling follow status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update follow status",
        variant: "destructive",
      });
    }
  }, [breweryId, userId, isFollowing, setIsFollowing, setFollowerCount]);

  return { toggleFollow };
};
