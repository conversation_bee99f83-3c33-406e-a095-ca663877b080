
import { useState, useEffect } from 'react';
import { SocialLinks, Brewery } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { J<PERSON> } from '@/integrations/supabase/types';

export const useSocialLinks = () => {
  const [socialLinks, setSocialLinks] = useState<SocialLinks>({
    facebook: '',
    instagram: '',
    twitter: ''
  });

  const handleSocialLinksUpdate = async (links: SocialLinks, breweryId?: string) => {
    if (!breweryId) {
      toast({
        title: "Update error",
        description: "No brewery ID provided. Save your profile first.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Convert SocialLinks to a format compatible with Json type
      const socialLinksJson: Record<string, string> = {
        facebook: links.facebook || '',
        instagram: links.instagram || '',
        twitter: links.twitter || ''
      };
      
      // Update in Supabase
      const { error } = await supabase
        .from('breweries')
        .update({ social_links: socialLinksJson })
        .eq('id', breweryId);
        
      if (error) throw error;
      
      // Update local state
      setSocialLinks(links);
      
      toast({
        title: "Social links updated",
        description: "Your social media links have been updated successfully",
      });
      
    } catch (err: any) {
      console.error('Error updating social links:', err);
      
      toast({
        title: "Update failed",
        description: err.message || "Failed to update social media links",
        variant: "destructive",
      });
    }
  };
  
  return {
    socialLinks,
    handleSocialLinksUpdate
  };
};
