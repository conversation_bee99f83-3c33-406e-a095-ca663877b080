
// This is a patch file to extend the Brewery type with the verified field and brewery_type
// Import this file where you need the extended Brewery type

import { Brewery as BaseBrewery } from './brewery';

// Extend the Brewery interface
export interface BreweryExtended extends BaseBrewery {
  verified: boolean;
  brewery_type: string; // Changed from optional to required
}

// Use this type instead of importing directly from brewery.ts
export type Brewery = BreweryExtended;

// Re-export other types
export * from './brewery';
