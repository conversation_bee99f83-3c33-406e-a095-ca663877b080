
export interface DigitalBoardSettings {
  autoRotate: boolean;
  displayTime: number; // seconds
  showPrices: boolean;
  showDescription: boolean;
  showImages: boolean;
  enableSlideshow: boolean;
  slideshowInterval: number; // seconds
}

export interface DigitalBoard {
  id: string;
  brewery_id: string;
  board_id: string;
  settings: DigitalBoardSettings;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
