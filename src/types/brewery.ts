
export type BreweryRole = 'admin' | 'brewery' | 'customer';

export interface AuthUser {
  id: string;
  email: string;
  role: BreweryRole;
  breweryId?: string;
}

export interface SocialLinks {
  facebook: string;
  instagram: string;
  twitter: string;
}

export interface SocialLinksJson extends Record<string, string> {
  facebook: string;
  instagram: string;
  twitter: string;
}

export interface Brewery {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  website?: string;
  description?: string;
  logo?: string;
  featureImage?: string;
  email?: string;
  socialLinks: SocialLinks;
  claimable?: boolean;
  verificationOpen?: boolean;
  claimed?: boolean;
  verified?: boolean;
  // Make brewery_type required to match patch file
  brewery_type: string;
  // Add new social profile fields
  avatar?: string;
  followerCount?: number;
  likeCount?: number;
  isFollowing?: boolean;
  isLiked?: boolean;
  // Add properties for database compatibility
  created_at?: string;
  updated_at?: string;
  feature_image?: string;
}

// Gallery Types
export interface GalleryImage {
  id: string;
  brewery_id: string;
  image_url: string;
  title?: string;
  created_at: string;
  updated_at: string;
}

// Post Types
export interface Post {
  id: string;
  brewery_id: string;
  brewery_name: string;
  brewery_logo?: string;
  content: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  likes: number;
  comments_count: number;
}

export interface PostCreateRequest {
  brewery_id: string;
  content: string;
}

// Coupon Types
export interface Coupon {
  id: string;
  brewery_id: string;
  title: string;
  description: string;
  code: string;
  discount_amount: number;
  discount_type: 'percentage' | 'fixed' | 'bogo' | 'free' | 'custom';
  start_date: string;
  end_date: string;
  active: boolean;
  created_at: string;
  updated_at: string;
  // Database compatibility properties
  discount_value?: string;
  expiry_date?: string;
  is_active?: boolean;
  qr_code_url?: string;
  redemption_count?: number;
  coupon_type?: string; // New field for coupon type
}

// New types for social features
export interface BreweryFollower {
  id: string;
  user_id: string;
  brewery_id: string;
  created_at: string;
}

export interface BreweryLike {
  id: string;
  user_id: string;
  brewery_id: string;
  created_at: string;
}
