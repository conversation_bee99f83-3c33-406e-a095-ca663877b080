
import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from "./components/ui/theme-provider";
import { Toaster } from "@/components/ui/toaster"
import Index from './pages/Index';
import Auth from './pages/Auth';
import BreweryProfile from './pages/BreweryProfile';
import BreweryListing from './pages/BreweryListing';
import DataImport from './pages/DataImport';
import AdminDashboard from './pages/AdminDashboard';
import { AuthProvider } from './auth';
import MenuManagement from './pages/MenuManagement';
import DigitalBoard from './pages/DigitalBoard';
import NotFound from './pages/NotFound';

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <ThemeProvider defaultTheme="system" storageKey="ui-theme">
          <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/brewery-profile" element={<BreweryProfile />} />
              <Route path="/breweries" element={<BreweryListing />} />
              <Route path="/import" element={<DataImport />} />
              <Route path="/admin" element={<AdminDashboard />} />
              <Route path="/menu-management" element={<MenuManagement />} />
              <Route path="/digital-board/:breweryId/:boardId" element={<DigitalBoard />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
            <Toaster />
          </div>
        </ThemeProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
