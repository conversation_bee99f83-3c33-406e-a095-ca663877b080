export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      beer_menu: {
        Row: {
          abv: string | null
          added_date: string | null
          brewery_id: string
          created_at: string
          description: string | null
          featured: boolean | null
          ibu: string | null
          id: string
          name: string
          origin: string | null
          price: string | null
          seasonal: boolean | null
          thumbnail: string | null
          type: string | null
          updated_at: string
        }
        Insert: {
          abv?: string | null
          added_date?: string | null
          brewery_id: string
          created_at?: string
          description?: string | null
          featured?: boolean | null
          ibu?: string | null
          id?: string
          name: string
          origin?: string | null
          price?: string | null
          seasonal?: boolean | null
          thumbnail?: string | null
          type?: string | null
          updated_at?: string
        }
        Update: {
          abv?: string | null
          added_date?: string | null
          brewery_id?: string
          created_at?: string
          description?: string | null
          featured?: boolean | null
          ibu?: string | null
          id?: string
          name?: string
          origin?: string | null
          price?: string | null
          seasonal?: boolean | null
          thumbnail?: string | null
          type?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "beer_menu_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      breweries: {
        Row: {
          address: string | null
          avatar: string | null
          brewery_type: string | null
          city: string | null
          claimable: boolean | null
          claimed: boolean | null
          created_at: string
          description: string | null
          email: string | null
          feature_image: string | null
          follower_count: number
          id: string
          like_count: number
          logo: string | null
          name: string | null
          phone: string | null
          social_links: Json | null
          state: string | null
          updated_at: string
          verification_open: boolean | null
          verified: boolean | null
          website: string | null
          zip: string | null
        }
        Insert: {
          address?: string | null
          avatar?: string | null
          brewery_type?: string | null
          city?: string | null
          claimable?: boolean | null
          claimed?: boolean | null
          created_at?: string
          description?: string | null
          email?: string | null
          feature_image?: string | null
          follower_count?: number
          id: string
          like_count?: number
          logo?: string | null
          name?: string | null
          phone?: string | null
          social_links?: Json | null
          state?: string | null
          updated_at?: string
          verification_open?: boolean | null
          verified?: boolean | null
          website?: string | null
          zip?: string | null
        }
        Update: {
          address?: string | null
          avatar?: string | null
          brewery_type?: string | null
          city?: string | null
          claimable?: boolean | null
          claimed?: boolean | null
          created_at?: string
          description?: string | null
          email?: string | null
          feature_image?: string | null
          follower_count?: number
          id?: string
          like_count?: number
          logo?: string | null
          name?: string | null
          phone?: string | null
          social_links?: Json | null
          state?: string | null
          updated_at?: string
          verification_open?: boolean | null
          verified?: boolean | null
          website?: string | null
          zip?: string | null
        }
        Relationships: []
      }
      brewery_coupons: {
        Row: {
          brewery_id: string | null
          code: string
          created_at: string | null
          description: string
          discount_value: string
          expiry_date: string
          id: string
          is_active: boolean | null
          qr_code_url: string | null
          redemption_count: number | null
          updated_at: string | null
        }
        Insert: {
          brewery_id?: string | null
          code: string
          created_at?: string | null
          description: string
          discount_value: string
          expiry_date: string
          id?: string
          is_active?: boolean | null
          qr_code_url?: string | null
          redemption_count?: number | null
          updated_at?: string | null
        }
        Update: {
          brewery_id?: string | null
          code?: string
          created_at?: string | null
          description?: string
          discount_value?: string
          expiry_date?: string
          id?: string
          is_active?: boolean | null
          qr_code_url?: string | null
          redemption_count?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "brewery_coupons_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      brewery_followers: {
        Row: {
          brewery_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          brewery_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          brewery_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "brewery_followers_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      brewery_gallery: {
        Row: {
          brewery_id: string | null
          created_at: string | null
          id: string
          image_url: string
          title: string | null
          updated_at: string | null
        }
        Insert: {
          brewery_id?: string | null
          created_at?: string | null
          id?: string
          image_url: string
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          brewery_id?: string | null
          created_at?: string | null
          id?: string
          image_url?: string
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "brewery_gallery_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      brewery_likes: {
        Row: {
          brewery_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          brewery_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          brewery_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "brewery_likes_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      brewery_reviews: {
        Row: {
          brewery_id: string
          content: string
          created_at: string
          id: string
          rating: number
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          brewery_id: string
          content: string
          created_at?: string
          id?: string
          rating: number
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          brewery_id?: string
          content?: string
          created_at?: string
          id?: string
          rating?: number
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "brewery_reviews_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      digital_boards: {
        Row: {
          board_id: string
          brewery_id: string
          created_at: string
          id: string
          is_active: boolean
          settings: Json
          updated_at: string
        }
        Insert: {
          board_id: string
          brewery_id: string
          created_at?: string
          id?: string
          is_active?: boolean
          settings?: Json
          updated_at?: string
        }
        Update: {
          board_id?: string
          brewery_id?: string
          created_at?: string
          id?: string
          is_active?: boolean
          settings?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "digital_boards_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      food_menu: {
        Row: {
          brewery_id: string
          category: string
          created_at: string
          description: string | null
          id: string
          is_gluten_free: boolean
          is_vegetarian: boolean
          name: string
          price: string | null
          updated_at: string
        }
        Insert: {
          brewery_id: string
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          is_gluten_free?: boolean
          is_vegetarian?: boolean
          name: string
          price?: string | null
          updated_at?: string
        }
        Update: {
          brewery_id?: string
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          is_gluten_free?: boolean
          is_vegetarian?: boolean
          name?: string
          price?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "food_menu_brewery_id_fkey"
            columns: ["brewery_id"]
            isOneToOne: false
            referencedRelation: "breweries"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          brewery_id: string | null
          created_at: string
          email: string
          id: string
          role: string
          updated_at: string
        }
        Insert: {
          brewery_id?: string | null
          created_at?: string
          email: string
          id: string
          role: string
          updated_at?: string
        }
        Update: {
          brewery_id?: string | null
          created_at?: string
          email?: string
          id?: string
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
