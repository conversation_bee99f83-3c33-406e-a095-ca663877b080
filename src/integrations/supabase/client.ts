// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rfakbhtiwyiagjequfou.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJmYWtiaHRpd3lpYWdqZXF1Zm91Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIyNTE0NTMsImV4cCI6MjA1NzgyNzQ1M30.pYHlCbuQbzTSIGGH8mJ-koL3oJPnPMP3IystLaPKrWw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);