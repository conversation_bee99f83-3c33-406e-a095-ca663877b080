
import { AuthUser, BreweryRole } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface RegisterFunctionsProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const useRegisterFunctions = ({ 
  setIsLoading 
}: RegisterFunctionsProps) => {
  
  const register = async (email: string, password: string, role: BreweryRole, breweryId?: string) => {
    setIsLoading(true);
    try {
      // For the admin account specifically
      if (email === '<EMAIL>') {
        const { error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              role: 'admin'
            }
          }
        });
        
        if (signUpError) {
          throw signUpError;
        }
        
        // Update profile for admin
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ role: 'admin' })
          .eq('email', email);
          
        if (updateError) {
          console.error('Auth - Error updating admin profile:', updateError);
        }
      } else {
        // Regular user registration (brewery users)
        let breweryIdToUse = breweryId;
        
        // If it's a brewery user and no breweryId is provided, create a new brewery
        if (role === 'brewery' && !breweryIdToUse) {
          // Create a new brewery entry
          const { data: newBrewery, error: breweryError } = await supabase
            .from('breweries')
            .insert({
              id: crypto.randomUUID(), // Generate a new UUID for the brewery
              name: `${email}'s Brewery`, // Default name, can be updated later
              email: email
            })
            .select('id')
            .single();
          
          if (breweryError) {
            console.error('Auth - Error creating brewery:', breweryError);
            throw new Error('Failed to create brewery. Please try again.');
          }
          
          breweryIdToUse = newBrewery.id;
          console.log('Auth - Created new brewery with ID:', breweryIdToUse);
        }
        
        // Register the user
        const { error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              role,
              breweryId: breweryIdToUse
            }
          }
        });
        
        if (signUpError) {
          throw signUpError;
        }
        
        // Update the profile with the brewery_id
        if (breweryIdToUse) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ brewery_id: breweryIdToUse })
            .eq('email', email);
            
          if (updateError) {
            console.error('Auth - Error updating profile with brewery_id:', updateError);
          } else {
            console.log('Auth - Successfully associated profile with brewery_id:', breweryIdToUse);
          }
        }
      }
      
      toast({
        title: "Registration successful",
        description: `Welcome, ${email}! Please check your email to confirm your account before logging in.`,
      });
    } catch (error: any) {
      console.error('Auth - Registration error:', error);
      toast({
        title: "Registration failed",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    register
  };
};
