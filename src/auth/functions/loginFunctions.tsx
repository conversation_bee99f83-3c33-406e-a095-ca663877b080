
import { AuthUser } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface LoginFunctionsProps {
  setUser: React.Dispatch<React.SetStateAction<AuthUser | null>>;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const useLoginFunctions = ({ 
  setUser,
  setIsLoading 
}: LoginFunctionsProps) => {
  
  const login = async (email: string, password: string) => {
    console.log('Login: Starting login process for', email);
    setIsLoading(true);
    try {
      console.log('Auth - Login attempt with email:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error('Auth - Login error:', error);
        
        // For development mode, try to create account if it doesn't exist
        if (error.message.includes('Invalid login credentials') || 
            error.message.includes('Email not confirmed')) {
          console.log('Auth - Dev mode: Attempting to create account');
          
          // Try to create the account first
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                role: 'admin' // For dev mode, grant admin access
              }
            }
          });
          
          if (!signUpError) {
            console.log('Auth - Dev mode: Account created, attempting to sign in');
            
            // Try signing in again
            const { data: newData, error: newError } = await supabase.auth.signInWithPassword({
              email,
              password
            });
            
            if (newError) {
              console.error('Auth - Dev mode: Login still failed after account creation:', newError);
              throw newError;
            }
            
            // If we get here, login succeeded after creating the account
            console.log('Auth - Dev mode: Login successful after account creation');
            
            // Create admin profile
            const { error: profileError } = await supabase.from('profiles').insert({
              id: newData.user.id,
              email: newData.user.email || '',
              role: 'admin'
            });
            
            if (profileError) {
              console.warn('Auth - Dev mode: Error creating profile, but login successful:', profileError);
            }
            
            setUser({
              id: newData.user.id,
              email: newData.user.email || '',
              role: 'admin',
              breweryId: null
            });
            
            toast({
              title: "Dev mode: Account created and logged in",
              description: `Welcome to development mode, ${email}!`,
            });
            
            setIsLoading(false);
            return;
          } else {
            console.error('Auth - Dev mode: Failed to create account:', signUpError);
          }
        }
        
        throw error;
      }
      
      if (!data.session) {
        console.error('Auth - Login successful but no session returned');
        throw new Error('No session returned after login');
      }
      
      console.log('Auth - Login successful, session:', data.session);
      
      // Get the user's profile to determine role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, brewery_id')
        .eq('id', data.user.id)
        .single();
      
      if (profileError) {
        console.error('Auth - Error fetching user profile:', profileError);
        
        // For development mode, create a default admin profile
        const { error: insertError } = await supabase.from('profiles').insert({
          id: data.user.id,
          email: data.user.email || '',
          role: 'admin'  // For dev mode, default to admin role
        });
        
        if (insertError) {
          console.error('Auth - Dev mode: Error creating profile:', insertError);
        } else {
          console.log('Auth - Dev mode: Created admin profile for:', email);
          
          setUser({
            id: data.user.id,
            email: data.user.email || '',
            role: 'admin',
            breweryId: null
          });
          
          toast({
            title: "Dev mode: Login successful",
            description: `Welcome, Admin!`,
          });
          return;
        }
      }
      
      // Construct user object with profile data if available
      if (profile) {
        // For development mode, ensure admin role
        const userRole = email.includes('admin') ? 'admin' : profile.role;
        console.log('Auth - Setting user role:', userRole, 'with brewery ID:', profile.brewery_id);
        
        setUser({
          id: data.user.id,
          email: data.user.email || '',
          role: userRole as any,
          breweryId: profile.brewery_id
        });
      } else {
        // Fallback if no profile found
        console.log('Auth - No profile found, setting default admin role');
        setUser({
          id: data.user.id,
          email: data.user.email || '',
          role: 'admin',  // For dev mode, default to admin role
          breweryId: null
        });
      }
      
      toast({
        title: "Login successful",
        description: `Welcome back!`,
      });
    } catch (error: any) {
      console.error('Auth - Login error:', error);
      toast({
        title: "Login failed",
        description: error.message || "Please try a different email/password for development.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    console.log('Auth - Starting logout process');
    try {
      await supabase.auth.signOut();
      setUser(null);
      console.log('Auth - User logged out successfully');
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    } catch (error) {
      console.error('Auth - Logout error:', error);
      toast({
        title: "Logout failed",
        description: "An error occurred during logout",
        variant: "destructive",
      });
    }
  };

  return {
    login,
    logout
  };
};
