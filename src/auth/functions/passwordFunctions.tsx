
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface PasswordFunctionsProps {
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const usePasswordFunctions = ({ 
  setIsLoading 
}: PasswordFunctionsProps) => {
  
  const resetPassword = async (email: string) => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth?tab=reset`,
      });
      
      if (error) {
        throw error;
      }
      
      toast({
        title: "Password reset email sent",
        description: "Check your email for a link to reset your password",
      });
    } catch (error: any) {
      console.error('Auth - Password reset error:', error);
      toast({
        title: "Password reset failed",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    resetPassword
  };
};
