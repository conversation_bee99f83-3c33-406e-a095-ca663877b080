
import { useState } from 'react';
import { AuthUser } from '@/types/brewery';
import { useLoginFunctions } from './functions/loginFunctions';
import { useRegisterFunctions } from './functions/registerFunctions';
import { usePasswordFunctions } from './functions/passwordFunctions';

interface UseAuthFunctionsProps {
  user: AuthUser | null;
  setUser: React.Dispatch<React.SetStateAction<AuthUser | null>>;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const useAuthFunctions = ({ 
  user, 
  setUser, 
  setIsLoading 
}: UseAuthFunctionsProps) => {
  
  // Import all the separate function hooks
  const { login, logout } = useLoginFunctions({ setUser, setIsLoading });
  const { register } = useRegisterFunctions({ setIsLoading });
  const { resetPassword } = usePasswordFunctions({ setIsLoading });

  // Combine and return all functions
  return {
    login,
    logout,
    register,
    resetPassword
  };
};
