
import React, { useState, useEffect } from 'react';
import { AuthContext } from './AuthContext';
import { AuthUser, BreweryRole } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { Session } from '@supabase/supabase-js';
import { toast } from '@/hooks/use-toast';
import { useAuthFunctions } from './useAuthFunctions';

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const { login, logout, register, resetPassword } = useAuthFunctions({ 
    user, 
    setUser, 
    setIsLoading 
  });

  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      
      try {
        // First check if we have a session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          console.log('Auth - Found existing session:', session.user.email);
          await setUserFromSession(session);
        } else {
          console.log('Auth - No existing session found');
          setUser(null);
        }
        
        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('Auth - Auth state changed:', event, session?.user?.email);
            
            if (session) {
              await setUserFromSession(session);
            } else {
              setUser(null);
            }
          }
        );
        
        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('Auth - Error initializing auth:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeAuth();
  }, []);
  
  const setUserFromSession = async (session: Session) => {
    try {
      console.log('Auth - Setting user from session:', session.user.id);
      
      if (!session.user.email_confirmed_at) {
        console.warn('Auth - User email not confirmed:', session.user.email);
      }
      
      // Get the user's profile to determine role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();
      
      if (profileError) {
        console.error('Auth - Error fetching user profile:', profileError);
        // Try to create a profile if it doesn't exist
        if (profileError.code === 'PGRST116') {
          try {
            // Check user metadata for role
            const userRole = session.user.user_metadata?.role || 'customer';
            console.log('Auth - Creating profile with role from metadata:', userRole);
            
            const { error: insertError } = await supabase.from('profiles').insert({
              id: session.user.id,
              email: session.user.email || '',
              role: userRole
            });
            
            if (insertError) {
              console.error('Auth - Error creating missing profile:', insertError);
            } else {
              console.log('Auth - Created missing profile for user:', session.user.email);
              // Fetch the profile again
              const { data: newProfile } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', session.user.id)
                .single();
                
              if (newProfile) {
                setUser({
                  id: session.user.id,
                  email: session.user.email || '',
                  role: newProfile.role as BreweryRole,
                  breweryId: newProfile.brewery_id
                });
                return;
              }
            }
          } catch (e) {
            console.error('Auth - Error in profile creation fallback:', e);
          }
        }
        
        // If we couldn't fetch or create a profile, create a basic user object
        setUser({
          id: session.user.id,
          email: session.user.email || '',
          role: (session.user.user_metadata?.role as BreweryRole) || 'customer',
          breweryId: undefined
        });
        return;
      }
      
      console.log('Auth - Profile data retrieved:', profile);
      
      // Check if the user metadata has an admin role that should override the profile role
      if (session.user.user_metadata?.role === 'admin' && profile.role !== 'admin') {
        console.log('Auth - Updating profile role to admin based on user metadata');
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ role: 'admin' })
          .eq('id', session.user.id);
          
        if (updateError) {
          console.error('Auth - Error updating profile role:', updateError);
        }
        
        setUser({
          id: session.user.id,
          email: session.user.email || '',
          role: 'admin',
          breweryId: profile?.brewery_id
        });
        return;
      }
      
      // Special handling for admin email
      if (session.user.email === '<EMAIL>' && profile.role !== 'admin') {
        console.log('Auth - Updating profile role to admin for admin email');
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ role: 'admin' })
          .eq('id', session.user.id);
          
        if (updateError) {
          console.error('Auth - Error updating profile role for admin email:', updateError);
        } else {
          profile.role = 'admin';
        }
      }
      
      setUser({
        id: session.user.id,
        email: session.user.email || '',
        role: profile?.role as BreweryRole,
        breweryId: profile?.brewery_id
      });
    } catch (error) {
      console.error('Auth - Error in setUserFromSession:', error);
      // Create a fallback user object in case of error
      if (session.user) {
        setUser({
          id: session.user.id,
          email: session.user.email || '',
          role: (session.user.user_metadata?.role as BreweryRole) || 'customer',
          breweryId: undefined
        });
      }
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout, register, resetPassword }}>
      {children}
    </AuthContext.Provider>
  );
};
