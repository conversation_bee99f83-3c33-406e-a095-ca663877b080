
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { BeerMenuItem } from './types/menuTypes';
import { CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface BeerMenuFormProps {
  currentItem: BeerMenuItem;
  onSave: (item: BeerMenuItem) => void;
  onCancel: () => void;
}

const BeerMenuForm: React.FC<BeerMenuFormProps> = ({ 
  currentItem, 
  onSave, 
  onCancel 
}) => {
  const [formData, setFormData] = useState<BeerMenuItem>(currentItem);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSwitchChange = (name: string, value: boolean) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData({
        ...formData,
        added_date: date.toISOString().split('T')[0]
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Beer Name*</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="type">Beer Type</Label>
          <Input
            id="type"
            name="type"
            value={formData.type || ''}
            onChange={handleChange}
            placeholder="IPA, Stout, Lager, etc."
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="abv">ABV (%)</Label>
          <Input
            id="abv"
            name="abv"
            value={formData.abv || ''}
            onChange={handleChange}
            placeholder="5.5"
          />
        </div>
        <div>
          <Label htmlFor="ibu">IBU</Label>
          <Input
            id="ibu"
            name="ibu"
            value={formData.ibu || ''}
            onChange={handleChange}
            placeholder="45"
          />
        </div>
        <div>
          <Label htmlFor="price">Price</Label>
          <Input
            id="price"
            name="price"
            value={formData.price || ''}
            onChange={handleChange}
            placeholder="$6.00"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="origin">Origin</Label>
          <Input
            id="origin"
            name="origin"
            value={formData.origin || ''}
            onChange={handleChange}
            placeholder="Country, Region, or Brewery Origin"
          />
        </div>
        <div>
          <Label htmlFor="added_date">Date Added</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.added_date && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.added_date ? format(new Date(formData.added_date), 'PPP') : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.added_date ? new Date(formData.added_date) : undefined}
                onSelect={handleDateChange}
                initialFocus
                className={cn("p-3 pointer-events-auto")}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      
      <div>
        <Label htmlFor="thumbnail">Thumbnail URL</Label>
        <Input
          id="thumbnail"
          name="thumbnail"
          value={formData.thumbnail || ''}
          onChange={handleChange}
          placeholder="https://example.com/beer-image.jpg"
        />
      </div>
      
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description || ''}
          onChange={handleChange}
          rows={3}
          placeholder="Describe the flavor, aroma, and characteristics"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="seasonal"
            checked={formData.seasonal || false}
            onCheckedChange={(value) => handleSwitchChange('seasonal', value)}
          />
          <Label htmlFor="seasonal">Seasonal Beer</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            id="featured"
            checked={formData.featured || false}
            onCheckedChange={(value) => handleSwitchChange('featured', value)}
          />
          <Label htmlFor="featured">Featured Item</Label>
        </div>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Beer
        </Button>
      </div>
    </form>
  );
};

export default BeerMenuForm;
