
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { FoodMenuItem, foodCategories } from './types/menuTypes';

interface FoodMenuFormProps {
  currentItem: FoodMenuItem;
  onSave: (item: FoodMenuItem) => void;
  onCancel: () => void;
}

const FoodMenuForm: React.FC<FoodMenuFormProps> = ({ 
  currentItem, 
  onSave, 
  onCancel 
}) => {
  const [formData, setFormData] = useState<FoodMenuItem>(currentItem);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };
  
  const handleCheckboxChange = (field: string, checked: boolean) => {
    setFormData({
      ...formData,
      [field]: checked
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Submitting form data:', formData);
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Item Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="category">Category</Label>
          <Select
            value={formData.category || 'appetizer'}
            onValueChange={(value) => handleSelectChange('category', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {foodCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="price">Price</Label>
          <Input
            id="price"
            name="price"
            value={formData.price || ''}
            onChange={handleChange}
            placeholder="$12.00"
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label>Dietary Options</Label>
          <div className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_vegetarian"
                checked={formData.is_vegetarian || false}
                onCheckedChange={(checked) => 
                  handleCheckboxChange('is_vegetarian', checked === true)
                }
              />
              <label htmlFor="is_vegetarian" className="text-sm">Vegetarian</label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_gluten_free"
                checked={formData.is_gluten_free || false}
                onCheckedChange={(checked) => 
                  handleCheckboxChange('is_gluten_free', checked === true)
                }
              />
              <label htmlFor="is_gluten_free" className="text-sm">Gluten Free</label>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description || ''}
          onChange={handleChange}
          rows={3}
          placeholder="Describe the dish, ingredients, and preparation"
        />
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Item
        </Button>
      </div>
    </form>
  );
};

export default FoodMenuForm;
