
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, FilterIcon } from 'lucide-react';
import { useFoodMenu } from './hooks/useFoodMenu';
import FoodMenuForm from './FoodMenuForm';
import FoodMenuList from './FoodMenuList';
import MenuImport from './MenuImport';
import MenuExport from './MenuExport';
import { FoodMenuTabProps, foodSortOptions } from './types/menuTypes';
import BreweryPagination from '@/components/brewery/BreweryPagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const FoodMenuTab: React.FC<FoodMenuTabProps> = ({ breweryId }) => {
  const {
    menuItems,
    allMenuItems,
    groupedItems,
    isLoading,
    isEditing,
    isExporting,
    isImporting,
    currentItem,
    sortOption,
    setSortOption,
    handleAddNew,
    handleEdit,
    handleSave,
    handleDelete,
    handleCancel,
    handleExport,
    handleImport,
    currentPage,
    totalPages,
    itemsPerPage,
    totalItems,
    setCurrentPage,
    setItemsPerPage
  } = useFoodMenu(breweryId);
  
  // Wrap handleExport to ensure it returns a Promise
  const onExport = async () => {
    return handleExport();
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">Food Menu Management</h2>
        <div className="flex flex-wrap gap-2">
          <Tabs defaultValue="items" className="w-full">
            <TabsList>
              <TabsTrigger value="items">Menu Items</TabsTrigger>
              <TabsTrigger value="import">Import</TabsTrigger>
              <TabsTrigger value="export">Export</TabsTrigger>
            </TabsList>
            
            <TabsContent value="items" className="space-y-4">
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <div className="flex items-center gap-2">
                  <Select 
                    value={`${sortOption.value}-${sortOption.direction}`}
                    onValueChange={(value) => {
                      const [field, direction] = value.split('-');
                      const option = foodSortOptions.find(
                        opt => opt.value === field && opt.direction === (direction as 'asc' | 'desc')
                      );
                      if (option) {
                        setSortOption(option);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[200px]">
                      <FilterIcon className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      {foodSortOptions.map((option) => (
                        <SelectItem 
                          key={`${option.value}-${option.direction}`} 
                          value={`${option.value}-${option.direction}`}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <Button onClick={handleAddNew}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Food Item
                </Button>
              </div>
              
              {isEditing && currentItem ? (
                <Card>
                  <CardHeader>
                    <CardTitle>{currentItem.id ? 'Edit Food Item' : 'Add New Food Item'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FoodMenuForm 
                      currentItem={currentItem} 
                      onSave={handleSave} 
                      onCancel={handleCancel} 
                    />
                  </CardContent>
                </Card>
              ) : isLoading ? (
                <div className="text-center py-4">Loading food menu...</div>
              ) : Object.keys(groupedItems).length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground mb-4">No food menu items added yet</p>
                  <Button onClick={handleAddNew}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Your First Food Item
                  </Button>
                </div>
              ) : (
                <>
                  <FoodMenuList 
                    groupedItems={groupedItems} 
                    onEdit={handleEdit} 
                    onDelete={handleDelete} 
                  />
                  
                  {totalItems > 0 && (
                    <BreweryPagination
                      currentPage={currentPage}
                      pageSize={itemsPerPage}
                      totalPages={totalPages}
                      totalItems={totalItems}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setItemsPerPage}
                    />
                  )}
                </>
              )}
            </TabsContent>
            
            <TabsContent value="import">
              <MenuImport 
                menuType="food"
                onImport={handleImport}
                isLoading={isImporting}
              />
            </TabsContent>
            
            <TabsContent value="export">
              <MenuExport 
                menuType="food"
                onExport={onExport}
                itemCount={allMenuItems.length}
                isExporting={isExporting}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default FoodMenuTab;
