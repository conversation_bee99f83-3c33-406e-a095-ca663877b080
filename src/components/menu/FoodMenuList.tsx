
import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Edit, Trash2 } from 'lucide-react';
import { FoodMenuItem, foodCategories } from './types/menuTypes';

interface FoodMenuListProps {
  groupedItems: Record<string, FoodMenuItem[]>;
  onEdit: (item: FoodMenuItem) => void;
  onDelete: (id: string) => void;
}

const FoodMenuList: React.FC<FoodMenuListProps> = ({ groupedItems, onEdit, onDelete }) => {
  // Get category labels map for easy lookup
  const categoryLabels = foodCategories.reduce((acc, category) => {
    acc[category.value] = category.label;
    return acc;
  }, {} as Record<string, string>);

  return (
    <div className="space-y-8">
      {Object.keys(groupedItems)
        .filter(category => groupedItems[category]?.length > 0)
        .map(category => (
          <div key={category}>
            <h3 className="text-lg font-semibold mb-4">{categoryLabels[category] || category}</h3>
            <div className="space-y-4">
              {groupedItems[category]?.map((item, index) => (
                <div key={item.id}>
                  {index > 0 && <Separator className="my-4" />}
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="text-base font-medium">{item.name}</h4>
                        <div className="flex gap-2">
                          {item.is_vegetarian && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-md">
                              Vegetarian
                            </span>
                          )}
                          {item.is_gluten_free && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-md">
                              Gluten Free
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-muted-foreground text-sm mt-1">{item.description}</p>
                      {item.price && (
                        <div className="mt-2 text-sm font-medium">
                          {item.price}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="ghost" onClick={() => onEdit(item)}>
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button size="sm" variant="ghost" onClick={() => onDelete(item.id)}>
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
    </div>
  );
};

export default FoodMenuList;
