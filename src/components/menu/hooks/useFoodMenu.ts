
import { useState, useMemo } from 'react';
import { FoodMenuHookResult } from './types/menuHookTypes';
import { useFoodMenuState } from './food/useFoodMenuState';
import { useFoodMenuFetch } from './food/useFoodMenuFetch';
import { useFoodMenuActions } from './food/useFoodMenuActions';

export const useFoodMenu = (breweryId?: string): FoodMenuHookResult => {
  // Manage state
  const state = useFoodMenuState(breweryId);
  
  // Handle data fetching
  const { fetchMenuItems } = useFoodMenuFetch(breweryId, state);
  
  // Handle menu actions
  const actions = useFoodMenuActions(breweryId, state, fetchMenuItems);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Get paginated menu items
  const paginatedMenuItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return state.menuItems.slice(startIndex, endIndex);
  }, [state.menuItems, currentPage, itemsPerPage]);
  
  // Calculate total pages
  const totalPages = useMemo(() => 
    Math.max(1, Math.ceil(state.menuItems.length / itemsPerPage)), 
    [state.menuItems.length, itemsPerPage]
  );
  
  // Return the combined state and actions as a single interface
  return {
    menuItems: paginatedMenuItems,
    allMenuItems: state.menuItems, // Include all menu items for export
    isLoading: state.isLoading,
    isEditing: state.isEditing,
    isExporting: state.isExporting,
    isImporting: state.isImporting,
    currentItem: state.currentItem,
    sortOption: state.sortOption,
    groupedItems: state.groupedItems,
    currentPage,
    totalPages,
    itemsPerPage, 
    totalItems: state.menuItems.length,
    setCurrentPage,
    setItemsPerPage,
    ...actions
  };
};
