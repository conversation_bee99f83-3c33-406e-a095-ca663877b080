
import { BeerMenuHookResult } from './types/menuHookTypes';
import { useBeerMenuState } from './beer/useBeerMenuState';
import { useBeerMenuFetch } from './beer/useBeerMenuFetch';
import { useBeerMenuActions } from './beer/useBeerMenuActions';
import { useMemo, useState } from 'react';

export const useBeerMenu = (breweryId?: string): BeerMenuHookResult => {
  // Manage state
  const state = useBeerMenuState(breweryId);
  
  // Handle data fetching
  const { fetchMenuItems } = useBeerMenuFetch(breweryId, state);
  
  // Handle menu actions
  const actions = useBeerMenuActions(breweryId, state, fetchMenuItems);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Get paginated menu items
  const paginatedMenuItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return state.menuItems.slice(startIndex, endIndex);
  }, [state.menuItems, currentPage, itemsPerPage]);
  
  // Calculate total pages
  const totalPages = useMemo(() => 
    Math.max(1, Math.ceil(state.menuItems.length / itemsPerPage)), 
    [state.menuItems.length, itemsPerPage]
  );
  
  // Return the combined state and actions as a single interface
  return {
    menuItems: paginatedMenuItems,
    allMenuItems: state.menuItems, // Including all items for reference
    isLoading: state.isLoading,
    isEditing: state.isEditing,
    isExporting: state.isExporting,
    isImporting: state.isImporting,
    currentItem: state.currentItem,
    sortOption: state.sortOption,
    currentPage,
    totalPages,
    itemsPerPage,
    totalItems: state.menuItems.length,
    setCurrentPage,
    setItemsPerPage,
    ...actions
  };
};
