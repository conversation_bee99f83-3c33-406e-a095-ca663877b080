
import { useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { FoodMenuItem } from '../../types/menuTypes';
import { UseFoodMenuStateReturn } from './useFoodMenuState';

export const useFoodMenuFetch = (
  breweryId: string | undefined,
  state: UseFoodMenuStateReturn
) => {
  const { toast } = useToast();
  const { 
    setMenuItems, 
    setGroupedItems, 
    setIsLoading, 
    sortOption 
  } = state;
  
  const fetchMenuItems = useCallback(async () => {
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      console.log('Fetching food menu items for brewery:', breweryId);
      
      let query = supabase
        .from('food_menu')
        .select('*')
        .eq('brewery_id', breweryId);
        
      if (sortOption.direction === 'asc') {
        query = query.order(sortOption.value, { ascending: true });
      } else {
        query = query.order(sortOption.value, { ascending: false });
      }
      
      const { data, error } = await query;
        
      if (error) throw error;
      
      console.log('Food menu items retrieved:', data);
      setMenuItems(data || []);
      
      // Group the items by category
      const grouped = (data || []).reduce((acc, item) => {
        if (!acc[item.category]) {
          acc[item.category] = [];
        }
        acc[item.category].push(item);
        return acc;
      }, {} as Record<string, FoodMenuItem[]>);
      
      setGroupedItems(grouped);
    } catch (error) {
      console.error('Error fetching food menu:', error);
      toast({
        title: 'Error',
        description: 'Failed to load food menu items',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [breweryId, sortOption, toast, setMenuItems, setGroupedItems, setIsLoading]);
  
  useEffect(() => {
    fetchMenuItems();
  }, [fetchMenuItems]);
  
  return { fetchMenuItems };
};
