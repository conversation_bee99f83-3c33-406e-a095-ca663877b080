
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { FoodMenuItem } from '../../types/menuTypes';
import { UseFoodMenuStateReturn } from './useFoodMenuState';

export const useFoodMenuCore = (
  breweryId: string | undefined,
  state: UseFoodMenuStateReturn,
  fetchMenuItems: () => Promise<void>
) => {
  const { toast } = useToast();
  const { 
    defaultItem, 
    setCurrentItem, 
    setIsEditing,
    setSortOption 
  } = state;
  
  const handleAddNew = useCallback(() => {
    setCurrentItem(defaultItem);
    setIsEditing(true);
  }, [defaultItem, setCurrentItem, setIsEditing]);
  
  const handleEdit = useCallback((item: FoodMenuItem) => {
    setCurrentItem(item);
    setIsEditing(true);
  }, [setCurrentItem, setIsEditing]);
  
  const handleCancel = useCallback(() => {
    setCurrentItem(null);
    setIsEditing(false);
  }, [setCurrentItem, setIsEditing]);
  
  const handleSave = useCallback(async (formData: FoodMenuItem) => {
    if (!breweryId) {
      console.error('No brewery ID available');
      toast({
        title: 'Error',
        description: 'Brewery ID is required to save menu item',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      console.log('Saving food menu item:', formData);
      console.log('For brewery ID:', breweryId);
      
      if (formData.id) {
        const { error } = await supabase
          .from('food_menu')
          .update({
            name: formData.name,
            description: formData.description,
            category: formData.category,
            price: formData.price,
            is_vegetarian: formData.is_vegetarian,
            is_gluten_free: formData.is_gluten_free,
            thumbnail: formData.thumbnail,
            added_date: formData.added_date,
            spicy_level: formData.spicy_level,
            allergens: formData.allergens,
            featured: formData.featured
          })
          .eq('id', formData.id);
          
        if (error) throw error;
        
        toast({
          title: 'Success',
          description: 'Food menu item updated successfully'
        });
      } else {
        const { error } = await supabase
          .from('food_menu')
          .insert({
            brewery_id: breweryId,
            name: formData.name,
            description: formData.description,
            category: formData.category,
            price: formData.price,
            is_vegetarian: formData.is_vegetarian,
            is_gluten_free: formData.is_gluten_free,
            thumbnail: formData.thumbnail,
            added_date: formData.added_date,
            spicy_level: formData.spicy_level,
            allergens: formData.allergens,
            featured: formData.featured
          });
          
        if (error) throw error;
        
        toast({
          title: 'Success',
          description: 'Food menu item added successfully'
        });
      }
      
      setCurrentItem(null);
      setIsEditing(false);
      fetchMenuItems();
    } catch (error) {
      console.error('Error saving food menu item:', error);
      toast({
        title: 'Error',
        description: 'Failed to save food menu item',
        variant: 'destructive'
      });
    }
  }, [breweryId, toast, setCurrentItem, setIsEditing, fetchMenuItems]);
  
  const handleDelete = useCallback(async (id: string) => {
    if (!confirm('Are you sure you want to delete this food menu item?')) return;
    
    try {
      console.log('Deleting food menu item with ID:', id);
      const { error } = await supabase
        .from('food_menu')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      toast({
        title: 'Success',
        description: 'Food menu item deleted successfully'
      });
      
      fetchMenuItems();
    } catch (error) {
      console.error('Error deleting food menu item:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete food menu item',
        variant: 'destructive'
      });
    }
  }, [toast, fetchMenuItems]);

  return {
    setSortOption,
    handleAddNew,
    handleEdit,
    handleSave,
    handleDelete,
    handleCancel
  };
};
