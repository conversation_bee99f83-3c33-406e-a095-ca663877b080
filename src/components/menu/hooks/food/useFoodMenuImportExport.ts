
import { useCallback } from 'react';
import <PERSON> from 'papaparse';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { FoodMenuItem } from '../../types/menuTypes';
import { UseFoodMenuStateReturn } from './useFoodMenuState';

export const useFoodMenuImportExport = (
  breweryId: string | undefined,
  state: UseFoodMenuStateReturn,
  fetchMenuItems: () => Promise<void>
) => {
  const { toast } = useToast();
  const { 
    setIsExporting, 
    setIsImporting, 
    menuItems 
  } = state;
  
  // Handle menu export - updated to return Promise<void>
  const handleExport = useCallback(async (): Promise<void> => {
    if (menuItems.length === 0) {
      toast({
        title: 'No items to export',
        description: 'Your food menu currently has no items to export',
        variant: 'destructive'
      });
      return Promise.resolve();
    }
    
    setIsExporting(true);
    
    try {
      const exportData = menuItems.map(item => ({
        name: item.name,
        description: item.description,
        category: item.category,
        price: item.price,
        is_vegetarian: item.is_vegetarian,
        is_gluten_free: item.is_gluten_free,
        thumbnail: item.thumbnail,
        added_date: item.added_date,
        spicy_level: item.spicy_level,
        allergens: item.allergens,
        featured: item.featured
      }));
      
      const csv = Papa.unparse(exportData);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `food-menu-${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: 'Export successful',
        description: `Exported ${exportData.length} food menu items to CSV`
      });
      return Promise.resolve();
    } catch (error) {
      console.error('Error exporting food menu:', error);
      toast({
        title: 'Export failed',
        description: 'Failed to export food menu items',
        variant: 'destructive'
      });
      return Promise.reject(error);
    } finally {
      setIsExporting(false);
    }
  }, [menuItems, toast, setIsExporting]);
  
  // Handle menu import
  const handleImport = useCallback(async (file: File): Promise<void> => {
    if (!breweryId) {
      toast({
        title: 'Error',
        description: 'Brewery ID is required to import menu items',
        variant: 'destructive'
      });
      return Promise.reject(new Error('Brewery ID is required'));
    }
    
    setIsImporting(true);
    
    try {
      return new Promise<void>((resolve, reject) => {
        Papa.parse(file, {
          header: true,
          skipEmptyLines: true,
          complete: async (results) => {
            try {
              // Convert numeric values to strings to match the Supabase schema
              const foodItems = results.data.map((row: any) => ({
                brewery_id: breweryId,
                name: row.name || '',
                description: row.description || '',
                category: row.category || 'Other',
                price: String(parseFloat(row.price) || 0), // Convert to string
                is_vegetarian: row.is_vegetarian === 'true',
                is_gluten_free: row.is_gluten_free === 'true',
                thumbnail: row.thumbnail || '',
                added_date: row.added_date || new Date().toISOString().split('T')[0],
                spicy_level: parseInt(row.spicy_level) || 0,
                allergens: row.allergens || '',
                featured: row.featured === 'true'
              }));
              
              console.log('Preparing to import food items:', foodItems);
              
              // Insert items one by one instead of as an array
              for (const item of foodItems) {
                const { error } = await supabase
                  .from('food_menu')
                  .insert(item);
                  
                if (error) throw error;
              }
              
              toast({
                title: 'Import successful',
                description: `Imported ${foodItems.length} food menu items`
              });
              
              await fetchMenuItems();
              resolve();
            } catch (error) {
              console.error('Error importing food menu:', error);
              toast({
                title: 'Import failed',
                description: 'Failed to import food menu items',
                variant: 'destructive'
              });
              reject(error);
            } finally {
              setIsImporting(false);
            }
          },
          error: (error) => {
            console.error('Error parsing CSV:', error);
            toast({
              title: 'Import failed',
              description: 'Failed to parse CSV file',
              variant: 'destructive'
            });
            setIsImporting(false);
            reject(error);
          }
        });
      });
    } catch (error) {
      setIsImporting(false);
      return Promise.reject(error);
    }
  }, [breweryId, toast, setIsImporting, fetchMenuItems]);

  return {
    handleExport,
    handleImport
  };
};
