
import { useCallback } from 'react';
import { FoodMenuItem } from '../../types/menuTypes';
import { MenuActions } from '../types/menuHookTypes';
import { useFoodMenuCore } from './useFoodMenuCore';
import { useFoodMenuImportExport } from './useFoodMenuImportExport';

export const useFoodMenuActions = (
  breweryId: string | undefined,
  state: any,
  fetchMenuItems: () => Promise<void>
): MenuActions<FoodMenuItem> => {
  // Get core menu actions (add, edit, save, delete)
  const core = useFoodMenuCore(breweryId, state, fetchMenuItems);
  
  // Get import/export actions
  const importExport = useFoodMenuImportExport(breweryId, state, fetchMenuItems);
  
  // Combine and return all actions
  return {
    ...core,
    ...importExport
  };
};
