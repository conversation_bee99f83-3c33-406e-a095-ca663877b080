
import { useState } from 'react';
import { FoodMenuItem, MenuSortOption, foodSortOptions } from '../../types/menuTypes';
import { FoodMenuStateInternal } from '../types/menuHookTypes';

export const useFoodMenuState = (breweryId?: string): FoodMenuStateInternal => {
  const [menuItems, setMenuItems] = useState<FoodMenuItem[]>([]);
  const [groupedItems, setGroupedItems] = useState<Record<string, FoodMenuItem[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentItem, setCurrentItem] = useState<FoodMenuItem | null>(null);
  const [sortOption, setSortOption] = useState<MenuSortOption>(foodSortOptions[0]);
  
  const defaultItem: FoodMenuItem = {
    id: '',
    brewery_id: breweryId,
    name: '',
    description: '',
    category: 'appetizer',
    price: '',
    is_vegetarian: false,
    is_gluten_free: false,
    added_date: new Date().toISOString().split('T')[0],
    spicy_level: 0,
    featured: false
  };
  
  return {
    menuItems,
    groupedItems,
    isLoading,
    isExporting,
    isImporting,
    isEditing,
    currentItem,
    sortOption,
    // Internal setters for other hooks to use
    setMenuItems,
    setGroupedItems,
    setIsLoading,
    setIsExporting,
    setIsImporting,
    setIsEditing,
    setCurrentItem,
    setSortOption,
    defaultItem
  };
};

// Export additional types for internal use by other hooks
export type UseFoodMenuStateReturn = ReturnType<typeof useFoodMenuState>;
