
import { BeerMenuItem, FoodMenuItem, MenuSortOption } from '../../types/menuTypes';

// Base menu hook result interface
export interface BaseMenuHookResult<T> {
  menuItems: T[];
  allMenuItems: T[]; // All menu items without pagination
  isLoading: boolean;
  isEditing: boolean;
  isExporting: boolean;
  isImporting: boolean;
  currentItem: T | null;
  sortOption: MenuSortOption;
  setSortOption: (option: MenuSortOption) => void;
  handleAddNew: () => void;
  handleEdit: (item: T) => void;
  handleSave: (item: T) => void;
  handleDelete: (id: string) => void;
  handleCancel: () => void;
  handleExport: () => void;
  handleImport: (file: File) => Promise<void>; // Changed from (items: T[]) => void to (file: File) => Promise<void>
  
  // Pagination properties
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
  setCurrentPage: (page: number) => void;
  setItemsPerPage: (pageSize: number) => void;
}

// Internal state interface for menu hooks
export interface MenuStateInternal<T> {
  menuItems: T[];
  isLoading: boolean;
  isExporting: boolean;
  isImporting: boolean;
  isEditing: boolean;
  currentItem: T | null;
  sortOption: MenuSortOption;
  // Internal setters for other hooks to use
  setMenuItems: React.Dispatch<React.SetStateAction<T[]>>;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setIsExporting: React.Dispatch<React.SetStateAction<boolean>>;
  setIsImporting: React.Dispatch<React.SetStateAction<boolean>>;
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>;
  setCurrentItem: React.Dispatch<React.SetStateAction<T | null>>;
  setSortOption: React.Dispatch<React.SetStateAction<MenuSortOption>>;
  defaultItem: T;
}

// Interface for actions in menu hooks
export interface MenuActions<T> {
  setSortOption: (option: MenuSortOption) => void;
  handleAddNew: () => void;
  handleEdit: (item: T) => void;
  handleSave: (item: T) => void;
  handleDelete: (id: string) => void;
  handleCancel: () => void;
  handleExport: () => void;
  handleImport: (file: File) => Promise<void>; // Changed from (items: T[]) => void to (file: File) => Promise<void>
}

// Beer menu hook result interface
export interface BeerMenuHookResult extends BaseMenuHookResult<BeerMenuItem> {}

// Food menu hook result interface
export interface FoodMenuHookResult extends BaseMenuHookResult<FoodMenuItem> {
  groupedItems: Record<string, FoodMenuItem[]>;
}

// Food menu state interface
export interface FoodMenuStateInternal extends MenuStateInternal<FoodMenuItem> {
  groupedItems: Record<string, FoodMenuItem[]>;
  setGroupedItems: React.Dispatch<React.SetStateAction<Record<string, FoodMenuItem[]>>>;
}
