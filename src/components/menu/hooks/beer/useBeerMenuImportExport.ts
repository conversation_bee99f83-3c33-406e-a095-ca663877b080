
import { useCallback } from 'react';
import <PERSON> from 'papaparse';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { BeerMenuItem } from '../../types/menuTypes';
import { UseBeerMenuStateReturn } from './useBeerMenuState';

export const useBeerMenuImportExport = (
  breweryId: string | undefined,
  state: UseBeerMenuStateReturn,
  fetchMenuItems: () => Promise<void>
) => {
  const { toast } = useToast();
  const { 
    setIsExporting, 
    setIsImporting, 
    menuItems 
  } = state;
  
  // Handle menu export - updated to return Promise<void>
  const handleExport = useCallback(async (): Promise<void> => {
    if (menuItems.length === 0) {
      toast({
        title: 'No items to export',
        description: 'Your beer menu currently has no items to export',
        variant: 'destructive'
      });
      return Promise.resolve();
    }
    
    setIsExporting(true);
    
    try {
      const exportData = menuItems.map(item => ({
        name: item.name,
        description: item.description,
        type: item.type,
        abv: item.abv,
        ibu: item.ibu,
        price: item.price,
        thumbnail: item.thumbnail,
        added_date: item.added_date,
        seasonal: item.seasonal,
        origin: item.origin,
        featured: item.featured
      }));
      
      const csv = Papa.unparse(exportData);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `beer-menu-${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: 'Export successful',
        description: `Exported ${exportData.length} beer menu items to CSV`
      });
      return Promise.resolve();
    } catch (error) {
      console.error('Error exporting beer menu:', error);
      toast({
        title: 'Export failed',
        description: 'Failed to export beer menu items',
        variant: 'destructive'
      });
      return Promise.reject(error);
    } finally {
      setIsExporting(false);
    }
  }, [menuItems, toast, setIsExporting]);
  
  // Handle menu import
  const handleImport = useCallback(async (file: File): Promise<void> => {
    if (!breweryId) {
      toast({
        title: 'Error',
        description: 'Brewery ID is required to import menu items',
        variant: 'destructive'
      });
      return Promise.reject(new Error('Brewery ID is required'));
    }
    
    setIsImporting(true);
    
    try {
      return new Promise<void>((resolve, reject) => {
        Papa.parse(file, {
          header: true,
          skipEmptyLines: true,
          complete: async (results) => {
            try {
              // Convert numeric values to strings to match the Supabase schema
              const beerItems = results.data.map((row: any) => ({
                brewery_id: breweryId,
                name: row.name || '',
                description: row.description || '',
                type: row.type || 'Other',
                abv: String(parseFloat(row.abv) || 0),  // Convert to string
                ibu: String(parseInt(row.ibu) || 0),    // Convert to string
                price: String(parseFloat(row.price) || 0), // Convert to string
                thumbnail: row.thumbnail || '',
                added_date: row.added_date || new Date().toISOString().split('T')[0],
                seasonal: row.seasonal === 'true',
                origin: row.origin || 'Local',
                featured: row.featured === 'true'
              }));
              
              console.log('Preparing to import beer items:', beerItems);
              
              // Insert items one by one instead of as an array
              for (const item of beerItems) {
                const { error } = await supabase
                  .from('beer_menu')
                  .insert(item);
                  
                if (error) throw error;
              }
              
              toast({
                title: 'Import successful',
                description: `Imported ${beerItems.length} beer menu items`
              });
              
              await fetchMenuItems();
              resolve();
            } catch (error) {
              console.error('Error importing beer menu:', error);
              toast({
                title: 'Import failed',
                description: 'Failed to import beer menu items',
                variant: 'destructive'
              });
              reject(error);
            } finally {
              setIsImporting(false);
            }
          },
          error: (error) => {
            console.error('Error parsing CSV:', error);
            toast({
              title: 'Import failed',
              description: 'Failed to parse CSV file',
              variant: 'destructive'
            });
            setIsImporting(false);
            reject(error);
          }
        });
      });
    } catch (error) {
      setIsImporting(false);
      return Promise.reject(error);
    }
  }, [breweryId, toast, setIsImporting, fetchMenuItems]);

  return {
    handleExport,
    handleImport
  };
};
