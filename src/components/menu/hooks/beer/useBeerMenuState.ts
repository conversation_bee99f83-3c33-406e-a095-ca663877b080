
import { useState } from 'react';
import { BeerMenuItem, MenuSortOption, beerSortOptions } from '../../types/menuTypes';
import { MenuStateInternal } from '../types/menuHookTypes';

export const useBeerMenuState = (breweryId?: string): MenuStateInternal<BeerMenuItem> => {
  const [menuItems, setMenuItems] = useState<BeerMenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentItem, setCurrentItem] = useState<BeerMenuItem | null>(null);
  const [sortOption, setSortOption] = useState<MenuSortOption>(beerSortOptions[0]);
  
  const defaultItem: BeerMenuItem = {
    id: '',
    brewery_id: breweryId,
    name: '',
    description: '',
    type: '',
    abv: '',
    ibu: '',
    price: '',
    added_date: new Date().toISOString().split('T')[0],
    seasonal: false,
    featured: false
  };
  
  return {
    menuItems,
    isLoading,
    isExporting,
    isImporting,
    isEditing,
    currentItem,
    sortOption,
    // Internal setters for other hooks to use
    setMenuItems,
    setIsLoading,
    setIsExporting,
    setIsImporting,
    setIsEditing,
    setCurrentItem,
    setSortOption,
    defaultItem
  };
};

// Export additional types for internal use by other hooks
export type UseBeerMenuStateReturn = ReturnType<typeof useBeerMenuState>;
