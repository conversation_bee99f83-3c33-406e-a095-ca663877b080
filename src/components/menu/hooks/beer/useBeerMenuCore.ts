
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { BeerMenuItem } from '../../types/menuTypes';
import { UseBeerMenuStateReturn } from './useBeerMenuState';

export const useBeerMenuCore = (
  breweryId: string | undefined,
  state: UseBeerMenuStateReturn,
  fetchMenuItems: () => Promise<void>
) => {
  const { toast } = useToast();
  const { 
    defaultItem, 
    setCurrentItem, 
    setIsEditing,
    setSortOption 
  } = state;
  
  const handleAddNew = useCallback(() => {
    setCurrentItem(defaultItem);
    setIsEditing(true);
  }, [defaultItem, setCurrentItem, setIsEditing]);
  
  const handleEdit = useCallback((item: BeerMenuItem) => {
    setCurrentItem(item);
    setIsEditing(true);
  }, [setCurrentItem, setIsEditing]);
  
  const handleCancel = useCallback(() => {
    setCurrentItem(null);
    setIsEditing(false);
  }, [setCurrentItem, setIsEditing]);
  
  const handleSave = useCallback(async (formData: BeerMenuItem) => {
    if (!breweryId) return;
    
    try {
      if (formData.id) {
        const { error } = await supabase
          .from('beer_menu')
          .update({
            name: formData.name,
            description: formData.description,
            type: formData.type,
            abv: formData.abv,
            ibu: formData.ibu,
            price: formData.price,
            thumbnail: formData.thumbnail,
            added_date: formData.added_date,
            seasonal: formData.seasonal,
            origin: formData.origin,
            featured: formData.featured
          })
          .eq('id', formData.id);
          
        if (error) throw error;
        
        toast({
          title: 'Success',
          description: 'Beer menu item updated successfully'
        });
      } else {
        const { error } = await supabase
          .from('beer_menu')
          .insert({
            brewery_id: breweryId,
            name: formData.name,
            description: formData.description,
            type: formData.type,
            abv: formData.abv,
            ibu: formData.ibu,
            price: formData.price,
            thumbnail: formData.thumbnail,
            added_date: formData.added_date,
            seasonal: formData.seasonal,
            origin: formData.origin,
            featured: formData.featured
          });
          
        if (error) throw error;
        
        toast({
          title: 'Success',
          description: 'Beer menu item added successfully'
        });
      }
      
      setCurrentItem(null);
      setIsEditing(false);
      fetchMenuItems();
    } catch (error) {
      console.error('Error saving beer menu item:', error);
      toast({
        title: 'Error',
        description: 'Failed to save beer menu item',
        variant: 'destructive'
      });
    }
  }, [breweryId, toast, setCurrentItem, setIsEditing, fetchMenuItems]);
  
  const handleDelete = useCallback(async (id: string) => {
    if (!confirm('Are you sure you want to delete this beer menu item?')) return;
    
    try {
      const { error } = await supabase
        .from('beer_menu')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      toast({
        title: 'Success',
        description: 'Beer menu item deleted successfully'
      });
      
      fetchMenuItems();
    } catch (error) {
      console.error('Error deleting beer menu item:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete beer menu item',
        variant: 'destructive'
      });
    }
  }, [toast, fetchMenuItems]);

  return {
    setSortOption,
    handleAddNew,
    handleEdit,
    handleSave,
    handleDelete,
    handleCancel
  };
};
