
import { useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { UseBeerMenuStateReturn } from './useBeerMenuState';

export const useBeerMenuFetch = (
  breweryId: string | undefined,
  state: UseBeerMenuStateReturn
) => {
  const { toast } = useToast();
  const { setMenuItems, setIsLoading, sortOption } = state;
  
  const fetchMenuItems = useCallback(async () => {
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      let query = supabase
        .from('beer_menu')
        .select('*')
        .eq('brewery_id', breweryId);
        
      if (sortOption.direction === 'asc') {
        query = query.order(sortOption.value, { ascending: true });
      } else {
        query = query.order(sortOption.value, { ascending: false });
      }
      
      const { data, error } = await query;
        
      if (error) throw error;
      
      setMenuItems(data || []);
    } catch (error) {
      console.error('Error fetching beer menu:', error);
      toast({
        title: 'Error',
        description: 'Failed to load beer menu items',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [breweryId, sortOption, toast, setMenuItems, setIsLoading]);
  
  useEffect(() => {
    fetchMenuItems();
  }, [fetchMenuItems]);
  
  return { fetchMenuItems };
};
