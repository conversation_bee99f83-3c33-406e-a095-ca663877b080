
import { BeerMenuItem } from '../../types/menuTypes';
import { UseBeerMenuStateReturn } from './useBeerMenuState';
import { MenuActions } from '../types/menuHookTypes';
import { useBeerMenuCore } from './useBeerMenuCore';
import { useBeerMenuImportExport } from './useBeerMenuImportExport';

export const useBeerMenuActions = (
  breweryId: string | undefined,
  state: UseBeerMenuStateReturn,
  fetchMenuItems: () => Promise<void>
): MenuActions<BeerMenuItem> => {
  // Get core menu actions (add, edit, save, delete)
  const core = useBeerMenuCore(breweryId, state, fetchMenuItems);
  
  // Get import/export actions
  const importExport = useBeerMenuImportExport(breweryId, state, fetchMenuItems);
  
  // Combine and return all actions
  return {
    ...core,
    ...importExport
  };
};
