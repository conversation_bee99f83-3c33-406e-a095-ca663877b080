
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Download, FileType } from 'lucide-react';

interface MenuExportProps {
  menuType: 'beer' | 'food';
  onExport: () => Promise<void>;
  itemCount: number;
  isExporting: boolean;
}

const MenuExport: React.FC<MenuExportProps> = ({ 
  menuType, 
  onExport, 
  itemCount, 
  isExporting 
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Export {menuType === 'beer' ? 'Beer' : 'Food'} Menu</CardTitle>
        <CardDescription>
          Download your {menuType === 'beer' ? 'beer' : 'food'} menu as a CSV file
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-muted p-6 rounded-lg flex flex-col items-center">
          <FileType className="h-16 w-16 text-muted-foreground mb-4" />
          <div className="text-center">
            <h3 className="text-lg font-medium mb-1">Ready to Export</h3>
            <p className="text-sm text-muted-foreground">
              You have {itemCount} {menuType === 'beer' ? 'beer' : 'food'} menu items available for export
            </p>
          </div>
        </div>
        
        <Separator />
        
        <div className="text-sm text-muted-foreground">
          <p className="mb-2">About the export:</p>
          <ul className="list-disc pl-5 space-y-1">
            <li>All {menuType === 'beer' ? 'beer' : 'food'} menu items will be exported to a CSV file</li>
            <li>The export includes all details such as names, descriptions, prices, etc.</li>
            <li>The file can be used for backup or editing in spreadsheet applications</li>
            <li>You can re-import the file after making changes</li>
            <li>The export uses UTF-8 encoding to support special characters</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button 
          onClick={onExport} 
          disabled={isExporting || itemCount === 0}
        >
          <Download className="mr-2 h-4 w-4" />
          {isExporting ? 'Preparing Download...' : 'Export to CSV'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MenuExport;
