
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { BeerMenuItem } from './types/menuTypes';
import { Button } from '@/components/ui/button';
import { PenIcon, Trash2Icon, StarIcon } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

interface BeerMenuListProps {
  items: BeerMenuItem[];
  isLoading: boolean;
  onEdit: (item: BeerMenuItem) => void;
  onDelete: (id: string) => void;
}

const BeerMenuList: React.FC<BeerMenuListProps> = ({ 
  items, 
  isLoading, 
  onEdit, 
  onDelete 
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="p-6">
                <Skeleton className="h-6 w-1/3 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No beer menu items found. Add your first beer!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {items.map((item) => (
        <Card key={item.id} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
              {item.thumbnail && (
                <div className="md:col-span-2 bg-muted h-full max-h-40 md:max-h-full">
                  <img 
                    src={item.thumbnail} 
                    alt={item.name} 
                    className="w-full h-full object-cover aspect-square"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder.svg';
                    }}
                  />
                </div>
              )}
              
              <div className={`p-6 ${item.thumbnail ? 'md:col-span-10' : 'md:col-span-12'}`}>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-semibold">
                      {item.name}
                      {item.featured && (
                        <StarIcon className="inline-block ml-2 h-4 w-4 text-yellow-400" />
                      )}
                    </h3>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {item.type && (
                        <Badge variant="outline">{item.type}</Badge>
                      )}
                      {item.abv && (
                        <Badge variant="secondary">ABV: {item.abv}%</Badge>
                      )}
                      {item.ibu && (
                        <Badge variant="secondary">IBU: {item.ibu}</Badge>
                      )}
                      {item.seasonal && (
                        <Badge className="bg-amber-500">Seasonal</Badge>
                      )}
                      {item.origin && (
                        <Badge variant="outline">Origin: {item.origin}</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" onClick={() => onEdit(item)}>
                      <PenIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => onDelete(item.id)}>
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {item.description && (
                  <p className="text-sm text-muted-foreground mb-3">{item.description}</p>
                )}
                
                <div className="flex justify-between items-center mt-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    {item.added_date && (
                      <span>Added: {format(new Date(item.added_date), 'PP')}</span>
                    )}
                  </div>
                  {item.price && (
                    <div className="font-semibold">{item.price}</div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default BeerMenuList;
