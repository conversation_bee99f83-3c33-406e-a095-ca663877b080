
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Download, Upload, FilterIcon } from 'lucide-react';
import { useBeerMenu } from './hooks/useBeerMenu';
import { BeerMenuTabProps, beerSortOptions } from './types/menuTypes';
import BeerMenuList from './BeerMenuList';
import BeerMenuForm from './BeerMenuForm';
import MenuImport from './MenuImport';
import MenuExport from './MenuExport';
import BreweryPagination from '@/components/brewery/BreweryPagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const BeerMenuTab: React.FC<BeerMenuTabProps> = ({ breweryId }) => {
  const {
    menuItems,
    allMenuItems,
    isLoading,
    isEditing,
    isExporting,
    isImporting,
    currentItem,
    sortOption,
    setSortOption,
    handleAddNew,
    handleEdit,
    handleSave,
    handleDelete,
    handleCancel,
    handleExport,
    handleImport,
    currentPage,
    totalPages,
    itemsPerPage,
    totalItems,
    setCurrentPage,
    setItemsPerPage
  } = useBeerMenu(breweryId);

  // Wrap handleExport to ensure it returns a Promise
  const onExport = async () => {
    return handleExport();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">Beer Menus Management</h2>
        <div className="flex flex-wrap gap-2">
          <Tabs defaultValue="items" className="w-full">
            <TabsList>
              <TabsTrigger value="items">Menu Items</TabsTrigger>
              <TabsTrigger value="import">Import</TabsTrigger>
              <TabsTrigger value="export">Export</TabsTrigger>
            </TabsList>
            
            <TabsContent value="items" className="space-y-4">
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <div className="flex items-center gap-2">
                  <Select 
                    value={`${sortOption.value}-${sortOption.direction}`}
                    onValueChange={(value) => {
                      const [field, direction] = value.split('-');
                      const option = beerSortOptions.find(
                        opt => opt.value === field && opt.direction === (direction as 'asc' | 'desc')
                      );
                      if (option) {
                        setSortOption(option);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[200px]">
                      <FilterIcon className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      {beerSortOptions.map((option) => (
                        <SelectItem 
                          key={`${option.value}-${option.direction}`} 
                          value={`${option.value}-${option.direction}`}
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <Button onClick={handleAddNew}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Beer
                </Button>
              </div>
              
              {isEditing && currentItem ? (
                <Card>
                  <CardHeader>
                    <CardTitle>{currentItem.id ? 'Edit Beer' : 'Add New Beer'}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <BeerMenuForm
                      currentItem={currentItem}
                      onSave={handleSave}
                      onCancel={handleCancel}
                    />
                  </CardContent>
                </Card>
              ) : (
                <>
                  <BeerMenuList
                    items={menuItems}
                    isLoading={isLoading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                  />
                  
                  {totalItems > 0 && (
                    <BreweryPagination
                      currentPage={currentPage}
                      pageSize={itemsPerPage}
                      totalPages={totalPages}
                      totalItems={totalItems}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setItemsPerPage}
                    />
                  )}
                </>
              )}
            </TabsContent>
            
            <TabsContent value="import">
              <MenuImport 
                menuType="beer"
                onImport={handleImport}
                isLoading={isImporting}
              />
            </TabsContent>
            
            <TabsContent value="export">
              <MenuExport 
                menuType="beer"
                onExport={onExport}
                itemCount={allMenuItems.length}
                isExporting={isExporting}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default BeerMenuTab;
