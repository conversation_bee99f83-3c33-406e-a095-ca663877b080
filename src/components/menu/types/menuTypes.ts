
export interface BeerMenuItem {
  id: string;
  brewery_id?: string; 
  name: string;
  description: string;
  type: string;
  abv: string;
  ibu: string;
  price: string;
  thumbnail?: string;
  added_date?: string;
  seasonal?: boolean;
  origin?: string;
  featured?: boolean;
}

export interface BeerMenuTabProps {
  breweryId?: string;
}

export interface FoodMenuItem {
  id: string;
  brewery_id?: string; 
  name: string;
  description: string;
  category: string;
  price: string;
  is_vegetarian: boolean;
  is_gluten_free: boolean;
  thumbnail?: string;
  added_date?: string;
  spicy_level?: number;
  allergens?: string;
  featured?: boolean;
}

export interface FoodMenuTabProps {
  breweryId?: string;
}

export const foodCategories = [
  { value: 'appetizer', label: 'Appetizers' },
  { value: 'main', label: 'Main Courses' },
  { value: 'dessert', label: 'Desserts' },
  { value: 'side', label: 'Side Dishes' },
  { value: 'special', label: 'Specials' }
];

export interface MenuImportResult {
  imported: number;
  errors: number;
  warnings: number;
}

export interface MenuSortOption {
  label: string;
  value: string;
  direction: 'asc' | 'desc';
}

export const beerSortOptions: MenuSortOption[] = [
  { label: 'Name (A-Z)', value: 'name', direction: 'asc' },
  { label: 'Name (Z-A)', value: 'name', direction: 'desc' },
  { label: 'ABV (Low-High)', value: 'abv', direction: 'asc' },
  { label: 'ABV (High-Low)', value: 'abv', direction: 'desc' },
  { label: 'Date Added (Newest)', value: 'added_date', direction: 'desc' },
  { label: 'Date Added (Oldest)', value: 'added_date', direction: 'asc' },
];

export const foodSortOptions: MenuSortOption[] = [
  { label: 'Name (A-Z)', value: 'name', direction: 'asc' },
  { label: 'Name (Z-A)', value: 'name', direction: 'desc' },
  { label: 'Category', value: 'category', direction: 'asc' },
  { label: 'Price (Low-High)', value: 'price', direction: 'asc' },
  { label: 'Price (High-Low)', value: 'price', direction: 'desc' },
  { label: 'Date Added (Newest)', value: 'added_date', direction: 'desc' },
  { label: 'Date Added (Oldest)', value: 'added_date', direction: 'asc' },
];
