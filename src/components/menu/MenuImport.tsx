
import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Upload, FileText } from 'lucide-react';

interface MenuImportProps {
  menuType: 'beer' | 'food';
  onImport: (file: File) => Promise<void>;
  isLoading: boolean;
}

const MenuImport: React.FC<MenuImportProps> = ({ 
  menuType, 
  onImport, 
  isLoading 
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    
    const files = e.target.files;
    if (!files || files.length === 0) {
      setFile(null);
      return;
    }
    
    const selectedFile = files[0];
    
    // Validate file type
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Only CSV files are supported.');
      setFile(null);
      return;
    }
    
    setFile(selectedFile);
  };
  
  const handleImport = async () => {
    if (!file) {
      setError('Please select a CSV file to import.');
      return;
    }
    
    try {
      await onImport(file);
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred during import.');
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Import {menuType === 'beer' ? 'Beer' : 'Food'} Menu</CardTitle>
        <CardDescription>
          Import {menuType === 'beer' ? 'beer' : 'food'} menu items from a CSV file
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex flex-col">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <Input 
              type="file"
              id="csv-upload"
              accept=".csv"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
            />
            <label 
              htmlFor="csv-upload"
              className="flex flex-col items-center justify-center cursor-pointer"
            >
              <FileText className="w-12 h-12 text-gray-400 mb-4" />
              <span className="text-lg font-medium mb-1">
                {file ? file.name : 'Click to select a CSV file'}
              </span>
              <span className="text-sm text-muted-foreground">
                {file 
                  ? `Size: ${(file.size / 1024).toFixed(1)} KB` 
                  : 'CSV format with required headers'
                }
              </span>
            </label>
          </div>
          
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Required Columns:</h3>
            <div className="bg-muted rounded p-2 text-xs font-mono overflow-x-auto whitespace-nowrap">
              {menuType === 'beer' ? (
                <span>name,description,type,abv,ibu,price,thumbnail,added_date,seasonal,origin,featured</span>
              ) : (
                <span>name,description,category,price,is_vegetarian,is_gluten_free,thumbnail,added_date,spicy_level,allergens,featured</span>
              )}
            </div>
          </div>
        </div>
        
        <Separator />
        
        <div className="text-sm text-muted-foreground">
          <p className="mb-2">Tips for successful import:</p>
          <ul className="list-disc pl-5 space-y-1">
            <li>Ensure your CSV file uses UTF-8 encoding</li>
            <li>The first row must contain headers exactly matching the required columns</li>
            <li>For boolean fields (e.g., seasonal, featured), use "true" or "false"</li>
            <li>Dates should be in YYYY-MM-DD format</li>
            <li>You can import many records at once</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => {
          setFile(null);
          if (fileInputRef.current) fileInputRef.current.value = '';
        }}>
          Cancel
        </Button>
        <Button 
          onClick={handleImport} 
          disabled={!file || isLoading}
        >
          <Upload className="mr-2 h-4 w-4" />
          {isLoading ? 'Importing...' : 'Import Data'}
        </Button>
      </CardFooter>
    </Card>
  );
};

// Components defined within the same file for simplicity
const Input = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(
  ({ className, ...props }, ref) => {
    return (
      <input
        className={className}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export default MenuImport;
