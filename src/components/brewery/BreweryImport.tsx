
import React, { useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload, ArrowDown } from "lucide-react";
import FileUploadInput from "./FileUploadInput";
import ImportProgress from "./ImportProgress";
import ImportError from "./ImportError";

export interface BreweryImportProps {
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onImportClick?: () => void;
  selectedFile?: File | null;
  isLoading: boolean;
  progress: number;
  currentChunk?: number;
  totalChunks?: number;
  rowsProcessed?: number;
  totalRows?: number;
  errorMessage: string | null;
  fieldMapping: Record<string, string>;
}

const BreweryImport: React.FC<BreweryImportProps> = ({
  handleFileUpload,
  onImportClick,
  selectedFile,
  isLoading,
  progress,
  currentChunk,
  totalChunks,
  rowsProcessed,
  totalRows,
  errorMessage,
  fieldMapping
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-5">
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Upload Brewery Data</h3>
        <p className="text-sm text-muted-foreground">
          Upload a CSV file containing brewery information. We'll help you map the fields.
        </p>
      </div>

      {/* File upload component */}
      <FileUploadInput 
        ref={fileInputRef}
        fileName={selectedFile?.name || ""}
        rowCount={totalRows || 0}
        isLoading={isLoading}
        onFileChange={handleFileUpload}
        onBrowseClick={handleBrowseClick}
        onImportClick={onImportClick}
      />

      {/* Show Import button if file is selected and not currently loading */}
      {selectedFile && !isLoading && onImportClick && (
        <div className="mt-4 flex justify-center">
          <Button
            onClick={onImportClick}
            className="px-8 py-2 flex items-center gap-2"
            disabled={isLoading}
          >
            <ArrowDown className="h-4 w-4" />
            Import Data
          </Button>
        </div>
      )}

      {/* Progress indicator with real progress information */}
      {isLoading && progress > 0 && (
        <ImportProgress 
          progress={progress} 
          isLoading={isLoading}
          currentChunk={currentChunk}
          totalChunks={totalChunks}
          rowsProcessed={rowsProcessed}
          totalRows={totalRows}
          fieldMapping={fieldMapping}
        />
      )}

      {/* Error display */}
      {errorMessage && (
        <ImportError 
          error={errorMessage}
          fieldMapping={fieldMapping}
        />
      )}
    </div>
  );
};

export default BreweryImport;
