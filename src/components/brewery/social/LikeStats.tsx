
import React from 'react';
import { Heart, ThumbsUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LikeStatsProps {
  likeCount: number;
  isLiked: boolean;
  isLoading: boolean;
  onToggleLike: () => void;
}

const LikeStats: React.FC<LikeStatsProps> = ({
  likeCount,
  isLiked,
  isLoading,
  onToggleLike
}) => {
  return (
    <div className="flex-1 flex flex-col justify-between bg-card border rounded-lg p-3 h-full">
      <div className="flex items-center space-x-2">
        <Heart className="h-5 w-5 text-red-500" />
        <div>
          <p className="text-lg font-semibold">{likeCount}</p>
          <p className="text-xs text-muted-foreground">Likes</p>
        </div>
      </div>
      <Button
        variant={isLiked ? "outline" : "default"}
        size="sm"
        onClick={onToggleLike}
        disabled={isLoading}
        className="w-full mt-2"
      >
        {isLiked ? (
          <>
            <ThumbsUp className="mr-1 h-4 w-4" />
            Liked
          </>
        ) : (
          <>
            <ThumbsUp className="mr-1 h-4 w-4" />
            Like
          </>
        )}
      </Button>
    </div>
  );
};

export default LikeStats;
