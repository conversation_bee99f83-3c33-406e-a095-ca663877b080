
import React from 'react';
import { Users, UserPlus } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FollowerStatsProps {
  followerCount: number;
  isFollowing: boolean;
  isLoading: boolean;
  onToggleFollow: () => void;
}

const FollowerStats: React.FC<FollowerStatsProps> = ({
  followerCount,
  isFollowing,
  isLoading,
  onToggleFollow
}) => {
  return (
    <div className="flex-1 flex flex-col justify-between bg-card border rounded-lg p-3 h-full">
      <div className="flex items-center space-x-2">
        <Users className="h-5 w-5 text-blue-500" />
        <div>
          <p className="text-lg font-semibold">{followerCount}</p>
          <p className="text-xs text-muted-foreground">Followers</p>
        </div>
      </div>
      <Button
        variant={isFollowing ? "outline" : "default"}
        size="sm"
        onClick={onToggleFollow}
        disabled={isLoading}
        className="w-full mt-2"
      >
        {isFollowing ? (
          <>
            <UserPlus className="mr-1 h-4 w-4" />
            Following
          </>
        ) : (
          <>
            <UserPlus className="mr-1 h-4 w-4" />
            Follow
          </>
        )}
      </Button>
    </div>
  );
};

export default FollowerStats;
