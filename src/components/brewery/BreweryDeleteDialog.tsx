
import React from "react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Brewery } from "@/types/brewery";

interface BreweryDeleteDialogProps {
  breweryToDelete: Brewery | null;
  onClose: () => void;
  onConfirm: () => void;
}

const BreweryDeleteDialog: React.FC<BreweryDeleteDialogProps> = ({ 
  breweryToDelete, 
  onClose, 
  onConfirm 
}) => {
  return (
    <AlertDialog open={!!breweryToDelete} onOpenChange={(open) => !open && onClose()}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the brewery "{breweryToDelete?.name}". This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} className="bg-destructive text-destructive-foreground">Delete</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default BreweryDeleteDialog;
