
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { DatePicker } from '@/components/ui/date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Percent, TicketPercent, Gift, Scissors } from 'lucide-react';

// Predefined coupon types
export const COUPON_TYPES = [
  { id: 'percent_off', label: '% Off', icon: <Percent className="h-4 w-4 mr-2" /> },
  { id: 'fixed_amount', label: 'Fixed Amount Off', icon: <TicketPercent className="h-4 w-4 mr-2" /> },
  { id: 'bogo', label: 'Buy One Get One', icon: <Gift className="h-4 w-4 mr-2" /> },
  { id: 'free_item', label: 'Free Item', icon: <Scissors className="h-4 w-4 mr-2" /> }
];

interface CouponFormProps {
  newCoupon: {
    description: string;
    couponType: string;
    discountValue: string;
    expiry_date: Date;
    is_active: boolean;
  };
  setNewCoupon: React.Dispatch<React.SetStateAction<{
    description: string;
    couponType: string;
    discountValue: string;
    expiry_date: Date;
    is_active: boolean;
  }>>;
  createCoupon: () => Promise<void>;
  isLoading: boolean;
}

const CouponForm: React.FC<CouponFormProps> = ({
  newCoupon,
  setNewCoupon,
  createCoupon,
  isLoading
}) => {
  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="e.g., Weekend Special Offer"
            value={newCoupon.description}
            onChange={(e) => setNewCoupon({ ...newCoupon, description: e.target.value })}
          />
        </div>
        
        <div>
          <Label htmlFor="couponType">Coupon Type</Label>
          <Select
            value={newCoupon.couponType}
            onValueChange={(value) => setNewCoupon({ ...newCoupon, couponType: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select coupon type" />
            </SelectTrigger>
            <SelectContent>
              {COUPON_TYPES.map(type => (
                <SelectItem key={type.id} value={type.id} className="flex items-center">
                  <div className="flex items-center">
                    {type.icon}
                    {type.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {newCoupon.couponType !== 'bogo' && (
          <div>
            <Label htmlFor="discount">
              {newCoupon.couponType === 'percent_off' 
                ? 'Discount Percentage' 
                : newCoupon.couponType === 'fixed_amount' 
                  ? 'Discount Amount ($)' 
                  : 'Item Name'}
            </Label>
            <Input
              id="discount"
              placeholder={
                newCoupon.couponType === 'percent_off' 
                  ? 'e.g., 20 (for 20%)' 
                  : newCoupon.couponType === 'fixed_amount' 
                    ? 'e.g., 5 (for $5 off)' 
                    : 'e.g., Appetizer'
              }
              value={newCoupon.discountValue}
              onChange={(e) => setNewCoupon({ ...newCoupon, discountValue: e.target.value })}
            />
          </div>
        )}
        
        <div>
          <Label htmlFor="expiry">Expiry Date</Label>
          <DatePicker
            selected={newCoupon.expiry_date}
            onSelect={(date) => date && setNewCoupon({ ...newCoupon, expiry_date: date })}
          />
        </div>
      </div>
      
      <Button 
        className="w-full"
        onClick={createCoupon}
        disabled={isLoading}
      >
        {isLoading ? 'Creating...' : 'Create Coupon'}
      </Button>
    </div>
  );
};

export default CouponForm;
