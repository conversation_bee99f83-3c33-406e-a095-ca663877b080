
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Coupon } from '@/types/brewery';

export const useCouponManager = (breweryId: string) => {
  const [isLoading, setIsLoading] = useState(false);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [newCoupon, setNewCoupon] = useState({
    description: '',
    couponType: 'percent_off',
    discountValue: '',
    expiry_date: new Date(),
    is_active: true
  });
  
  const fetchCoupons = async () => {
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('brewery_coupons')
        .select('*')
        .eq('brewery_id', breweryId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setCoupons(data as Coupon[]);
    } catch (error: any) {
      console.error('Error fetching coupons:', error);
      toast({
        title: 'Error fetching coupons',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const getFormattedDiscountValue = (type: string, value: string) => {
    switch (type) {
      case 'percent_off':
        return `${value}% off`;
      case 'fixed_amount':
        return `$${value} off`;
      case 'bogo':
        return 'Buy One Get One Free';
      case 'free_item':
        return `Free ${value}`;
      default:
        return value;
    }
  };
  
  const createCoupon = async () => {
    if (!breweryId) return;
    
    if (!newCoupon.description) {
      toast({
        title: 'Missing fields',
        description: 'Please fill in the coupon description',
        variant: 'destructive'
      });
      return;
    }
    
    // For BOGO, we don't need a discount value
    if (newCoupon.couponType !== 'bogo' && !newCoupon.discountValue) {
      toast({
        title: 'Missing fields',
        description: 'Please enter a discount value',
        variant: 'destructive'
      });
      return;
    }
    
    setIsLoading(true);
    try {
      // Generate a random coupon code
      const couponCode = Math.random().toString(36).substring(2, 10).toUpperCase();
      
      // Format the discount value based on the coupon type
      const formattedDiscountValue = getFormattedDiscountValue(
        newCoupon.couponType, 
        newCoupon.discountValue
      );
      
      // Create the coupon in the database
      const { data, error } = await supabase
        .from('brewery_coupons')
        .insert({
          brewery_id: breweryId,
          code: couponCode,
          description: newCoupon.description,
          discount_value: formattedDiscountValue,
          coupon_type: newCoupon.couponType,
          expiry_date: newCoupon.expiry_date.toISOString(),
          is_active: newCoupon.is_active,
          redemption_count: 0
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Generate QR code for the coupon
      // In a real implementation, we'd use a service or edge function to generate this
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(couponCode)}`;
      
      // Update the coupon with the QR code URL
      const { error: updateError } = await supabase
        .from('brewery_coupons')
        .update({ qr_code_url: qrCodeUrl })
        .eq('id', data.id);
      
      if (updateError) throw updateError;
      
      // Add the new coupon to the list
      const newCouponWithQR = { ...data, qr_code_url: qrCodeUrl };
      setCoupons([newCouponWithQR as Coupon, ...coupons]);
      
      // Reset the form
      setNewCoupon({
        description: '',
        couponType: 'percent_off',
        discountValue: '',
        expiry_date: new Date(),
        is_active: true
      });
      
      toast({
        title: 'Coupon created',
        description: 'Your coupon has been created successfully'
      });
    } catch (error: any) {
      console.error('Error creating coupon:', error);
      toast({
        title: 'Error creating coupon',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const toggleCouponActive = async (couponId: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from('brewery_coupons')
        .update({ is_active: !currentActive })
        .eq('id', couponId);
      
      if (error) throw error;
      
      // Update the coupon in the list
      setCoupons(coupons.map(coupon => 
        coupon.id === couponId 
          ? { ...coupon, is_active: !currentActive } 
          : coupon
      ));
      
      toast({
        title: 'Coupon updated',
        description: `Coupon is now ${!currentActive ? 'active' : 'inactive'}`
      });
    } catch (error: any) {
      console.error('Error updating coupon:', error);
      toast({
        title: 'Error updating coupon',
        description: error.message,
        variant: 'destructive'
      });
    }
  };
  
  useEffect(() => {
    if (breweryId) {
      fetchCoupons();
    }
  }, [breweryId]);
  
  return {
    isLoading,
    coupons,
    newCoupon,
    setNewCoupon,
    createCoupon,
    toggleCouponActive
  };
};

export default useCouponManager;
