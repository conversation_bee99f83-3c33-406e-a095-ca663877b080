
import React from 'react';
import { QrCode } from 'lucide-react';
import { Coupon } from '@/types/brewery';
import CouponCard from './CouponCard';

interface CouponListProps {
  coupons: Coupon[];
  toggleCouponActive: (couponId: string, currentActive: boolean) => Promise<void>;
}

const CouponList: React.FC<CouponListProps> = ({ coupons, toggleCouponActive }) => {
  if (coupons.length === 0) {
    return (
      <div className="text-center py-8 border border-dashed rounded-md bg-background">
        <QrCode className="h-12 w-12 mx-auto text-muted-foreground" />
        <p className="mt-2 text-muted-foreground">No coupons created yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {coupons.map(coupon => (
        <CouponCard 
          key={coupon.id} 
          coupon={coupon} 
          toggleCouponActive={toggleCouponActive} 
        />
      ))}
    </div>
  );
};

export default CouponList;
