
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { QrCode, Percent, TicketPercent, Gift, Scissors } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { Coupon } from '@/types/brewery';
import { COUPON_TYPES } from './CouponForm';

interface CouponCardProps {
  coupon: Coupon;
  toggleCouponActive: (couponId: string, currentActive: boolean) => Promise<void>;
}

const CouponCard: React.FC<CouponCardProps> = ({ coupon, toggleCouponActive }) => {
  // Get icon based on coupon type
  const getCouponTypeIcon = (couponType: string) => {
    const type = COUPON_TYPES.find(t => t.id === couponType);
    return type ? type.icon : <TicketPercent className="h-4 w-4" />;
  };

  return (
    <Card key={coupon.id} className="overflow-hidden">
      <div className="grid md:grid-cols-3 gap-4">
        <div className="p-4 flex items-center justify-center bg-muted">
          {coupon.qr_code_url ? (
            <img 
              src={coupon.qr_code_url} 
              alt={`QR Code for ${coupon.code}`}
              className="w-32 h-32"
            />
          ) : (
            <QrCode className="w-32 h-32 text-muted-foreground" />
          )}
        </div>
        
        <div className="p-4 md:col-span-2">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="font-medium">{coupon.description}</h3>
              <p className="text-sm text-muted-foreground">Code: {coupon.code}</p>
            </div>
            <Badge variant={coupon.is_active ? 'default' : 'outline'} className="ml-2">
              {coupon.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          
          <div className="flex items-center mb-2">
            {coupon.coupon_type && getCouponTypeIcon(coupon.coupon_type)}
            <span className="ml-1 font-medium">{coupon.discount_value}</span>
          </div>
          
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div>
              <p className="text-xs text-muted-foreground">Expires</p>
              <p className="font-medium">
                {format(parseISO(coupon.expiry_date), 'PP')}
              </p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Created</p>
              <p className="font-medium">
                {format(parseISO(coupon.created_at), 'PP')}
              </p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Redemptions</p>
              <p className="font-medium">{coupon.redemption_count}</p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button 
              variant={coupon.is_active ? 'destructive' : 'default'}
              size="sm"
              onClick={() => toggleCouponActive(coupon.id, coupon.is_active)}
            >
              {coupon.is_active ? 'Deactivate' : 'Activate'}
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              asChild
            >
              <a 
                href={coupon.qr_code_url} 
                download={`coupon-${coupon.code}.png`}
                target="_blank"
                rel="noopener noreferrer"
              >
                Download QR
              </a>
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CouponCard;
