
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CouponForm from './CouponForm';
import CouponList from './CouponList';
import useCouponManager from './useCouponManager';

interface CouponManagerProps {
  breweryId: string;
}

const CouponManager: React.FC<CouponManagerProps> = ({ breweryId }) => {
  const {
    isLoading,
    coupons,
    newCoupon,
    setNewCoupon,
    createCoupon,
    toggleCouponActive
  } = useCouponManager(breweryId);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Coupon Management</CardTitle>
        <CardDescription>
          Create and manage QR code coupons for your brewery
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="create">
          <TabsList className="w-full">
            <TabsTrigger value="create">Create Coupon</TabsTrigger>
            <TabsTrigger value="manage">Manage Coupons</TabsTrigger>
          </TabsList>
          
          <TabsContent value="create" className="pt-4">
            <CouponForm
              newCoupon={newCoupon}
              setNewCoupon={setNewCoupon}
              createCoupon={createCoupon}
              isLoading={isLoading}
            />
          </TabsContent>
          
          <TabsContent value="manage" className="pt-4">
            <CouponList 
              coupons={coupons}
              toggleCouponActive={toggleCouponActive}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CouponManager;
