
import React from "react";
import { Progress } from "@/components/ui/progress";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export interface ImportProgressProps {
  progress: number;
  isLoading: boolean;
  currentChunk?: number;
  totalChunks?: number;
  rowsProcessed?: number;
  totalRows?: number;
  fieldMapping?: Record<string, string>;
}

const ImportProgress: React.FC<ImportProgressProps> = ({ 
  progress, 
  isLoading,
  currentChunk,
  totalChunks,
  rowsProcessed,
  totalRows,
  fieldMapping 
}) => {
  const isComplete = progress === 100;
  
  // Generate accurate progress status message
  const getStatusMessage = () => {
    if (isComplete) {
      return "Import complete";
    }
    
    if (progress < 15) {
      return "Reading CSV file...";
    }
    
    if (progress < 25) {
      return "Validating & mapping fields...";
    }
    
    if (totalChunks && totalChunks > 1 && currentChunk) {
      return `Processing chunk ${currentChunk} of ${totalChunks}`;
    }
    
    if (rowsProcessed !== undefined && totalRows) {
      return `Processing rows (${Math.round((rowsProcessed / totalRows) * 100)}%)`;
    }
    
    if (progress < 60) {
      return "Processing data...";
    }
    
    if (progress < 80) {
      return "Storing brewery information...";
    }
    
    return "Finalizing import...";
  };
  
  return (
    <div className="space-y-2 py-2 animate-in fade-in duration-300">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {isLoading && progress < 100 ? (
            <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
          ) : isComplete ? (
            <CheckCircle className="h-3 w-3 text-green-500" />
          ) : null}
          <span className={`text-sm font-medium ${isComplete ? "text-green-600" : ""}`}>
            {getStatusMessage()}
          </span>
        </div>
        <span className="text-sm font-medium">{Math.round(progress)}%</span>
      </div>
      
      <Progress 
        value={progress} 
        className={`h-2 ${isComplete ? "bg-green-100" : ""}`}
      />
      
      {totalRows && rowsProcessed !== undefined && (
        <div className="flex justify-between text-xs text-muted-foreground pt-1">
          <span>Processing rows: {rowsProcessed} / {totalRows}</span>
          {fieldMapping && Object.keys(fieldMapping).length > 0 && (
            <Badge variant="outline" className="text-xs">
              {Object.keys(fieldMapping).length} fields mapped
            </Badge>
          )}
        </div>
      )}
      
      {currentChunk && totalChunks && totalChunks > 1 && (
        <div className="flex justify-start text-xs text-muted-foreground">
          <span>Processing in chunks: {currentChunk}/{totalChunks}</span>
        </div>
      )}
      
      {isComplete && (
        <div className="flex items-center gap-2 text-xs text-green-600 font-medium mt-2 bg-green-50 p-2 rounded-md border border-green-100">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span>Import successfully completed!</span>
        </div>
      )}
    </div>
  );
};

export default ImportProgress;
