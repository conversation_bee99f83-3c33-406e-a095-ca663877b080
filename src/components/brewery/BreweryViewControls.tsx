
import React from 'react';
import { Button } from '@/components/ui/button';
import { TableIcon, Grid3X3Icon, RefreshCcw } from 'lucide-react';
import BreweryFilters from './BreweryFilters';

interface BreweryViewControlsProps {
  viewMode: 'table' | 'grid';
  onViewModeChange: (mode: 'table' | 'grid') => void;
  onRefresh: () => Promise<void>;
  filters: Record<string, string>;
  setFilters: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  itemsPerPage: number;
  setItemsPerPage: React.Dispatch<React.SetStateAction<number>>;
  resetPage: () => void;
  handleSearch: (term: string) => void;
  handleFilterChange: (key: string, value: string) => void;
  breweryStates: string[];
}

const BreweryViewControls: React.FC<BreweryViewControlsProps> = ({
  viewMode,
  onViewModeChange,
  onRefresh,
  filters,
  itemsPerPage,
  setItemsPerPage,
  resetPage,
  handleSearch,
  handleFilterChange,
  breweryStates
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
      <div className="flex items-center gap-2">
        <Button
          variant={viewMode === 'table' ? 'default' : 'outline'} 
          size="icon"
          onClick={() => onViewModeChange('table')}
          title="Table View"
        >
          <TableIcon className="h-4 w-4" />
        </Button>
        <Button 
          variant={viewMode === 'grid' ? 'default' : 'outline'} 
          size="icon"
          onClick={() => onViewModeChange('grid')}
          title="Grid View"
        >
          <Grid3X3Icon className="h-4 w-4" />
        </Button>
        <Button 
          variant="outline" 
          size="icon"
          onClick={() => onRefresh()}
          title="Refresh data"
        >
          <RefreshCcw className="h-4 w-4" />
        </Button>
      </div>
      
      <BreweryFilters 
        searchTerm={filters.searchTerm || ''}
        filterColumn={filters.filterColumn || ''}
        stateFilter={filters.state || ''}
        verificationFilter={filters.verification || 'all'}
        setSearchTerm={(term) => handleSearch(term)}
        setFilterColumn={(col) => handleFilterChange('filterColumn', col)}
        setStateFilter={(state) => handleFilterChange('state', state)}
        setVerificationFilter={(filter) => handleFilterChange('verification', filter)}
        columns={['name', 'city', 'state', 'email', 'phone']}
        states={breweryStates}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        resetPage={resetPage}
      />
    </div>
  );
};

export default BreweryViewControls;
