
import React from 'react';
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

const NoBreweryIdAlert: React.FC = () => {
  return (
    <div className="container mx-auto py-8">
      <Alert variant="default" className="mb-6 border-amber-300 bg-amber-50">
        <AlertTriangle className="h-4 w-4 text-amber-600" />
        <AlertTitle className="text-amber-600">Brewery ID not found</AlertTitle>
        <AlertDescription className="text-amber-700">
          You need to have a brewery ID associated with your account to access this page. 
          Please contact an administrator to update your profile.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default NoBreweryIdAlert;
