
import React from 'react';
import { format } from 'date-fns';
import { Star, Edit2, Trash2 } from 'lucide-react';
import { Review } from '@/types/review';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface ReviewItemProps {
  review: Review;
  isUserReview: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
}

const ReviewItem: React.FC<ReviewItemProps> = ({ 
  review, 
  isUserReview,
  onEdit,
  onDelete
}) => {
  const formattedDate = format(new Date(review.created_at), 'MMM d, yyyy');
  const initials = review.user_name 
    ? review.user_name.substring(0, 2).toUpperCase() 
    : 'AN';

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2 flex flex-row justify-between items-start">
        <div className="flex items-center gap-3">
          <Avatar>
            <AvatarFallback className="bg-primary text-primary-foreground">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div>
            <CardTitle className="text-lg">{review.title}</CardTitle>
            <div className="flex items-center mt-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="text-xs text-muted-foreground ml-2">
                {review.user_name} • {formattedDate}
              </span>
            </div>
          </div>
        </div>
        {isUserReview && (
          <div className="flex space-x-1">
            {onEdit && (
              <Button variant="ghost" size="icon" onClick={onEdit} aria-label="Edit review">
                <Edit2 className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button variant="ghost" size="icon" onClick={onDelete} aria-label="Delete review">
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent className="pt-2">
        <p className="text-sm whitespace-pre-line">{review.content}</p>
      </CardContent>
    </Card>
  );
};

export default ReviewItem;
