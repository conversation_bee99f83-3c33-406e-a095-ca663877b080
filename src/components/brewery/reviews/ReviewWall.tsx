
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/auth';
import { useBreweryReviews } from '@/hooks/brewery/useBreweryReviews';
import { ReviewFormData } from '@/types/review';
import ReviewForm from './ReviewForm';
import ReviewItem from './ReviewItem';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, MessageSquare, Star } from 'lucide-react';

interface ReviewWallProps {
  breweryId: string;
}

const ReviewWall: React.FC<ReviewWallProps> = ({ breweryId }) => {
  const { user } = useAuth();
  const { 
    reviews, 
    isLoading, 
    userReview, 
    fetchReviews, 
    submitReview, 
    deleteReview 
  } = useBreweryReviews(breweryId);
  
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    fetchReviews();
  }, [fetchReviews]);

  const handleSubmitReview = async (data: ReviewFormData) => {
    await submitReview(data);
    setIsEditing(false);
    setShowForm(false);
  };

  const handleEditReview = () => {
    if (!userReview) return;
    
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDeleteReview = async () => {
    if (!userReview) return;
    
    await deleteReview(userReview.id);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setShowForm(false);
  };

  const toggleForm = () => {
    if (isEditing) {
      setIsEditing(false);
    }
    setShowForm(!showForm);
  };

  const averageRating = reviews.length > 0 
    ? (reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length).toFixed(1)
    : 'N/A';

  const initialFormData = userReview
    ? {
        rating: userReview.rating,
        title: userReview.title,
        content: userReview.content,
      }
    : undefined;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Reviews
        </h2>
        {reviews.length > 0 && (
          <div className="flex items-center gap-1.5 bg-amber-50 dark:bg-amber-950 text-amber-700 dark:text-amber-300 px-3 py-1 rounded-full">
            <Star className="h-4 w-4 fill-amber-400 text-amber-400" />
            <span className="font-medium">{averageRating}</span>
            <span className="text-xs text-muted-foreground">({reviews.length})</span>
          </div>
        )}
      </div>

      {user ? (
        <>
          {!userReview && !showForm && (
            <Button onClick={toggleForm} variant="outline" className="w-full">
              Write a Review
            </Button>
          )}
          
          {showForm && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">
                  {isEditing ? 'Edit your review' : 'Write a review'}
                </h3>
                <ReviewForm 
                  initialData={isEditing ? initialFormData : undefined}
                  onSubmit={handleSubmitReview}
                  onCancel={handleCancelEdit}
                />
              </CardContent>
            </Card>
          )}
        </>
      ) : (
        <Card className="bg-muted mb-6">
          <CardContent className="py-4 flex items-center justify-center gap-2">
            <AlertCircle className="h-5 w-5 text-muted-foreground" />
            <p className="text-muted-foreground">Please log in to write a review.</p>
          </CardContent>
        </Card>
      )}

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <Card key={i} className="mb-4">
              <CardContent className="py-6 space-y-3">
                <div className="flex items-center space-x-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : reviews.length === 0 ? (
        <div className="text-center py-8 bg-muted rounded-lg">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
          <p className="text-muted-foreground">No reviews yet. Be the first to leave a review!</p>
        </div>
      ) : (
        <div>
          {reviews.map(review => (
            <ReviewItem 
              key={review.id} 
              review={review}
              isUserReview={user ? user.id === review.user_id : false}
              onEdit={user && user.id === review.user_id ? handleEditReview : undefined}
              onDelete={user && user.id === review.user_id ? handleDeleteReview : undefined}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewWall;
