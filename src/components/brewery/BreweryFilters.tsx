
import React from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface BreweryFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterColumn: string;
  setFilterColumn: (column: string) => void;
  stateFilter: string;
  setStateFilter: (state: string) => void;
  columns: string[];
  states: string[];
  itemsPerPage: number;
  setItemsPerPage: (value: number) => void;
  resetPage: () => void;
  // Add the verification filter props
  verificationFilter?: string;
  setVerificationFilter?: (value: string) => void;
}

const BreweryFilters: React.FC<BreweryFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterColumn,
  setFilterColumn,
  stateFilter,
  setStateFilter,
  columns,
  states,
  itemsPerPage,
  setItemsPerPage,
  resetPage,
  verificationFilter = 'all',
  setVerificationFilter = () => {}
}) => {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    resetPage();
  };

  const handleColumnChange = (value: string) => {
    setFilterColumn(value);
    resetPage();
  };
  
  const handleStateChange = (value: string) => {
    setStateFilter(value);
    resetPage();
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    resetPage();
  };
  
  const handleVerificationFilterChange = (value: string) => {
    setVerificationFilter(value);
    resetPage();
  };

  return (
    <div className="mb-6 space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search bar */}
        <div className="flex-1 relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search breweries..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-8"
          />
        </div>
        
        {/* Column selector */}
        <div className="w-full md:w-48">
          <Select value={filterColumn} onValueChange={handleColumnChange}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Fields</SelectItem>
              {columns
                .filter(col => 
                  !['id', 'created_at', 'updated_at', 'socialLinks'].includes(col)
                )
                .map(col => (
                  <SelectItem key={col} value={col}>
                    {col.charAt(0).toUpperCase() + col.slice(1)}
                  </SelectItem>
                ))
              }
            </SelectContent>
          </Select>
        </div>
        
        {/* State filter */}
        {states.length > 0 && (
          <div className="w-full md:w-48">
            <Select value={stateFilter} onValueChange={handleStateChange}>
              <SelectTrigger>
                <SelectValue placeholder="State" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                {states.map(state => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        
        {/* Verification status filter */}
        {setVerificationFilter && (
          <div className="w-full md:w-48">
            <Select value={verificationFilter} onValueChange={handleVerificationFilterChange}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="verified">Verified</SelectItem>
                <SelectItem value="claimed">Claimed</SelectItem>
                <SelectItem value="unclaimed">Unclaimed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      
      {/* Items per page selector */}
      <div className="flex items-center space-x-2 justify-end">
        <Label htmlFor="itemsPerPage" className="text-sm">
          Items per page:
        </Label>
        <Select
          value={itemsPerPage.toString()}
          onValueChange={handleItemsPerPageChange}
        >
          <SelectTrigger className="w-[80px]" id="itemsPerPage">
            <SelectValue placeholder="10" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="25">25</SelectItem>
            <SelectItem value="50">50</SelectItem>
            <SelectItem value="100">100</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default BreweryFilters;
