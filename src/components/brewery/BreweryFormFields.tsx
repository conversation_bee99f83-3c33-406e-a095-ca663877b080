
import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { UseFormReturn } from 'react-hook-form';
import { BreweryFormValues } from './schemas/breweryFormSchema';
import { 
  BasicInfoFields, 
  ContactFields, 
  AddressFields, 
  MediaFields 
} from './form-fields';

interface BreweryFormFieldsProps {
  form: UseFormReturn<BreweryFormValues>;
}

const BreweryFormFields: React.FC<BreweryFormFieldsProps> = ({ form }) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic brewery information section */}
        <BasicInfoFields form={form} />
        
        {/* Contact information section */}
        <ContactFields form={form} />
        
        {/* Address information section */}
        <AddressFields form={form} />
        
        {/* Media URLs section */}
        <MediaFields form={form} />
      </div>
    </>
  );
};

export default BreweryFormFields;
