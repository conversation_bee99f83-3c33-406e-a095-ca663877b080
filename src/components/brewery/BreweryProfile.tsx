
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, AlertCircle, Beer } from 'lucide-react';
import ProfileTabs from './profile/ProfileTabs';
import ProfileHeader from './profile/ProfileHeader';
import { Separator } from '@/components/ui/separator';
import { useBreweryProfile } from '@/hooks/brewery/useBreweryProfile';
import NoBreweryIdAlert from './NoBreweryIdAlert';
import ErrorDisplay from './profile/ErrorDisplay';
import { AuthUser } from '@/types/brewery.patch';

interface BreweryProfileProps {
  user?: AuthUser;
}

const BreweryProfile: React.FC<BreweryProfileProps> = ({ user: propUser }) => {
  const { user: authUser } = useAuth();
  const navigate = useNavigate();
  
  // Use the user from props if provided, otherwise use the one from auth context
  const user = propUser || authUser;
  
  // Add guard clause for missing user
  if (!user) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Authentication Error</AlertTitle>
          <AlertDescription>User information is not available. Please log in again.</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => navigate('/auth')}>Go to Login</Button>
        </div>
      </div>
    );
  }
  
  // Get brewery profile data and handlers
  const {
    brewery,
    isLoading,
    error,
    socialLinks,
    uploadProgress,
    fileErrors,
    handleProfileSubmit,
    handleCreateTestBrewery,
    handleImageUpload,
    handleSocialLinksUpdate,
    handleAvatarUpdate,
    refreshBrewery
  } = useBreweryProfile(user);
  
  // If the user doesn't have a breweryId and isn't an admin, show an alert
  if (!user.breweryId && user.role !== 'admin') {
    return <NoBreweryIdAlert />;
  }
  
  // If there's an error and user is not admin or has a breweryId, show an error alert
  if (error && !isLoading && (user.role !== 'admin' || user.breweryId)) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }
  
  // If there's an error and user is admin, show the error display with test brewery option
  if (error && !isLoading && user.role === 'admin') {
    return (
      <div className="container mx-auto py-8">
        <ErrorDisplay 
          message={error}
          user={user}
          isLoading={isLoading}
          breweryId={user.breweryId}
          onRefresh={refreshBrewery}
          onCreateTestBrewery={handleCreateTestBrewery}
        />
      </div>
    );
  }
  
  // Admin user without a brewery, show test brewery option
  const showCreateTestBrewery = !brewery && user.role === 'admin';
  
  // Ensure uploadProgress is always a number
  const normalizedUploadProgress = typeof uploadProgress === 'number' ? uploadProgress : 0;
  
  // Ensure fileErrors is the correct shape
  const normalizedFileErrors = Array.isArray(fileErrors) ? fileErrors : [];
  
  // Wrapper for image upload to ensure correct return type
  const handleImageUploadWrapper = async (type: string, file: File): Promise<string> => {
    const result = await handleImageUpload(type, file);
    return result || "";
  };
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <Button variant="outline" onClick={() => navigate(-1)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <div className="flex gap-2">
          <Button 
            onClick={() => navigate('/menu-management')}
            variant="outline"
          >
            <Beer className="mr-2 h-4 w-4" />
            Menu Management
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader className="pb-0">
          <ProfileHeader
            brewery={brewery}
            isLoading={isLoading}
            onAvatarUpdate={handleAvatarUpdate}
          />
        </CardHeader>
        
        <CardContent className="pt-6">
          <Separator className="mb-6" />
          
          {showCreateTestBrewery ? (
            <div className="flex flex-col items-center justify-center p-8">
              <h2 className="text-2xl font-bold mb-4">No Brewery Found</h2>
              <p className="text-muted-foreground mb-6 text-center max-w-md">
                As an admin, you can create a test brewery to explore the brewery management features.
              </p>
              <Button onClick={handleCreateTestBrewery} disabled={isLoading}>
                Create Test Brewery
              </Button>
            </div>
          ) : (
            brewery && (
              <ProfileTabs
                brewery={brewery}
                isLoading={isLoading}
                uploadProgress={normalizedUploadProgress}
                fileErrors={normalizedFileErrors}
                socialLinks={socialLinks}
                onProfileSubmit={handleProfileSubmit}
                onImageUpload={handleImageUploadWrapper}
                onSocialLinksUpdate={handleSocialLinksUpdate}
              />
            )
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BreweryProfile;
