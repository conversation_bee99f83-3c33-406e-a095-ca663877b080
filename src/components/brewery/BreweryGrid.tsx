
import React from 'react';
import { Brewery } from '@/types/brewery.patch'; // Use patch version
import BreweryCard from './BreweryCard';

interface BreweryGridProps {
  breweries: Brewery[];
  onViewDetails?: (brewery: Brewery) => void;
}

const BreweryGrid: React.FC<BreweryGridProps> = ({ breweries, onViewDetails }) => {
  if (breweries.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">No breweries found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
      {breweries.map((brewery) => (
        <BreweryCard 
          key={brewery.id} 
          brewery={brewery} 
          onViewDetails={onViewDetails}
        />
      ))}
    </div>
  );
};

export default BreweryGrid;
