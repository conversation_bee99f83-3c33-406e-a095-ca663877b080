
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/hooks/use-toast';
import { Edit, Loader2, Save } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface BreweryBioProps {
  breweryId: string;
  initialBio: string;
  onBioUpdate: (newBio: string) => void;
}

const BreweryBio: React.FC<BreweryBioProps> = ({ breweryId, initialBio, onBioUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [bio, setBio] = useState(initialBio || '');
  const [isSaving, setIsSaving] = useState(false);
  
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    if (!isEditing) {
      // Reset to current value when entering edit mode
      setBio(initialBio || '');
    }
  };
  
  const handleSave = async () => {
    if (!breweryId) return;
    
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('breweries')
        .update({ description: bio })
        .eq('id', breweryId);
        
      if (error) throw error;
      
      onBioUpdate(bio);
      setIsEditing(false);
      
      toast({
        title: "Bio updated",
        description: "Your brewery bio has been updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating bio:', error);
      toast({
        title: "Update failed",
        description: error.message || "There was an error updating your bio",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>About Us</CardTitle>
          <CardDescription>Share your brewery's story</CardDescription>
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleEditToggle}
          disabled={isSaving}
        >
          {isEditing ? 'Cancel' : <Edit className="h-4 w-4" />}
        </Button>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <Textarea
              value={bio}
              onChange={(e) => setBio(e.target.value)}
              placeholder="Tell people about your brewery, your story, your brewing philosophy..."
              className="min-h-[150px]"
            />
            <Button 
              onClick={handleSave}
              disabled={isSaving}
              className="w-full"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Bio
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="prose dark:prose-invert max-w-none">
            {bio ? (
              <p className="whitespace-pre-line">{bio}</p>
            ) : (
              <p className="text-muted-foreground italic">
                No bio information added yet. Click the edit button to tell your story.
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BreweryBio;
