
import React from "react";
import { Input } from "@/components/ui/input";

interface InputFieldProps {
  column: string;
  value: any;
  onInputChange: (key: string, value: any) => void;
  fieldType: string;
  placeholder: string;
}

const InputField: React.FC<InputFieldProps> = ({
  column,
  value,
  onInputChange,
  fieldType,
  placeholder
}) => {
  const getInputProps = () => {
    const commonProps = {
      id: column,
      value: value || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => onInputChange(column, e.target.value),
      className: "mt-1",
      placeholder
    };

    switch (fieldType) {
      case 'tel':
        return {
          ...commonProps,
          type: "tel",
          pattern: "[0-9()-+\\s]+"
        };
      case 'zip':
        return {
          ...commonProps,
          type: "text",
          pattern: "[0-9-]+",
          maxLength: 10
        };
      case 'url':
        return {
          ...commonProps,
          type: "url"
        };
      case 'email':
        return {
          ...commonProps,
          type: "email"
        };
      default:
        return {
          ...commonProps,
          type: "text"
        };
    }
  };

  return <Input {...getInputProps()} />;
};

export default InputField;
