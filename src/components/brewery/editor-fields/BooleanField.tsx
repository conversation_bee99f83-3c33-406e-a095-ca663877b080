
import React from "react";
import { formatLabel } from "../utils/labelFormatters";

interface BooleanFieldProps {
  column: string;
  value: any;
  onInputChange: (key: string, value: any) => void;
}

const BooleanField: React.FC<BooleanFieldProps> = ({
  column,
  value,
  onInputChange
}) => {
  const getCheckboxLabel = (column: string): string => {
    if (column === 'verificationOpen') return 'Allow users to claim this brewery';
    if (column === 'claimed') return 'Brewery has been claimed';
    if (column === 'verified') return 'Brewery is verified';
    return `Enable ${formatLabel(column).toLowerCase()}`;
  };

  return (
    <div className="flex items-center mt-1">
      <input
        id={column}
        type="checkbox"
        checked={!!value}
        onChange={(e) => onInputChange(column, e.target.checked)}
        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
      />
      <label htmlFor={column} className="ml-2 text-sm text-gray-600">
        {getCheckboxLabel(column)}
      </label>
    </div>
  );
};

export default BooleanField;
