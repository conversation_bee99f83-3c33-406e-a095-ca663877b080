
import React from "react";
import { Textarea } from "@/components/ui/textarea";

interface DescriptionFieldProps {
  column: string;
  value: any;
  onInputChange: (key: string, value: any) => void;
}

const DescriptionField: React.FC<DescriptionFieldProps> = ({
  column,
  value,
  onInputChange
}) => {
  return (
    <Textarea
      id={column}
      value={value || ''}
      onChange={(e) => onInputChange(column, e.target.value)}
      className="mt-1"
      rows={4}
      placeholder="Enter brewery description"
    />
  );
};

export default DescriptionField;
