
import React from "react";
import <PERSON>U<PERSON><PERSON><PERSON>ield from "../ImageUploadField";

interface FileFieldProps {
  column: string;
  value: any;
  onInputChange: (key: string, value: any) => void;
  uploadProgress: number;
  fileError: string;
  onFileUpload: (field: string, file: File) => void;
}

const FileField: React.FC<FileFieldProps> = ({
  column,
  value,
  onInputChange,
  uploadProgress,
  fileError,
  onFileUpload
}) => {
  return (
    <ImageUploadField
      fieldName={column}
      currentValue={value || ''}
      onChange={(newValue) => onInputChange(column, newValue)}
      onFileUpload={(file) => onFileUpload(column, file)}
      uploadProgress={uploadProgress || 0}
      error={fileError}
    />
  );
};

export default FileField;
