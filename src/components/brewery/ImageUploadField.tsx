
import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Image, Upload, AlertCircle } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ImageUploadFieldProps {
  fieldName: string;
  currentValue: string;
  onChange: (value: string) => void;
  onFileUpload: (file: File) => void;
  uploadProgress: number;
  error?: string;
}

const ImageUploadField: React.FC<ImageUploadFieldProps> = ({
  fieldName,
  currentValue,
  onChange,
  onFileUpload,
  uploadProgress,
  error
}) => {
  const isUploading = uploadProgress > 0 && uploadProgress < 100;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileUpload(file);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Input
          value={currentValue || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Enter image URL or upload file"
          className="flex-1"
          disabled={isUploading}
        />
        <div className="relative">
          <Input
            type="file"
            id={`${fieldName}-upload`}
            accept="image/jpeg,image/png,image/gif"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleFileChange}
            disabled={isUploading}
          />
          <Button 
            type="button" 
            variant="outline" 
            className="flex items-center gap-2"
            disabled={isUploading}
          >
            <Upload size={16} />
            Upload
          </Button>
        </div>
      </div>
      
      {/* File upload progress */}
      {isUploading && (
        <div className="space-y-1">
          <div className="text-sm text-muted-foreground">
            Uploading... {uploadProgress}%
          </div>
          <Progress value={uploadProgress} className="h-2" />
        </div>
      )}
      
      {/* File error message */}
      {error && (
        <Alert variant="destructive" className="py-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs ml-2">
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      {/* File information */}
      <div className="text-xs text-muted-foreground">
        Supported formats: JPG, PNG, GIF • Max size: 5MB
      </div>
      
      <div className="h-24 w-24 relative border rounded overflow-hidden">
        {currentValue ? (
          <img 
            src={currentValue} 
            alt={fieldName} 
            className="h-full w-full object-cover"
            onError={(e) => {
              // Show placeholder on error
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full w-full bg-muted">
            <Image className="h-8 w-8 text-muted-foreground" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageUploadField;
