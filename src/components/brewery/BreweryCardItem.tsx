
import React from 'react';
import { Brewery } from '@/types/brewery.patch';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { ExternalLink } from 'lucide-react';

interface BreweryCardItemProps {
  brewery: Brewery;
  onEdit: (breweryId: string) => void;
  onDelete: (brewery: Brewery) => void;
  renderVerificationBadge: (brewery: Brewery) => React.ReactNode;
}

const BreweryCardItem: React.FC<BreweryCardItemProps> = ({
  brewery,
  onEdit,
  onDelete,
  renderVerificationBadge
}) => {
  return (
    <Card key={brewery.id} className="overflow-hidden">
      <CardHeader className="p-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-12 w-12 border">
            {brewery.logo ? (
              <img 
                src={brewery.logo} 
                alt={brewery.name || 'Brewery logo'} 
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary font-semibold">
                {brewery.name?.substring(0, 2).toUpperCase() || 'BR'}
              </div>
            )}
          </Avatar>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate">
              {brewery.name || 'Unnamed Brewery'}
            </CardTitle>
            <div className="flex items-center gap-2 mt-1">
              {renderVerificationBadge(brewery)}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-0 space-y-3">
        {brewery.city && brewery.state && (
          <div className="text-sm">
            <span className="font-medium">Location:</span>{' '}
            {brewery.city}, {brewery.state} {brewery.zip && `(${brewery.zip})`}
          </div>
        )}
        
        {brewery.phone && (
          <div className="text-sm">
            <span className="font-medium">Phone:</span>{' '}
            <a href={`tel:${brewery.phone}`} className="text-primary hover:underline">
              {brewery.phone}
            </a>
          </div>
        )}
        
        {brewery.email && (
          <div className="text-sm truncate">
            <span className="font-medium">Email:</span>{' '}
            <a href={`mailto:${brewery.email}`} className="text-primary hover:underline">
              {brewery.email}
            </a>
          </div>
        )}
        
        {brewery.website && (
          <div className="text-sm truncate">
            <span className="font-medium">Website:</span>{' '}
            <a 
              href={brewery.website.startsWith('http') ? brewery.website : `https://${brewery.website}`} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary hover:underline inline-flex items-center gap-1"
            >
              {brewery.website.replace(/^https?:\/\//, '')}
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>
        )}
        
        <div className="flex justify-between pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(brewery.id)}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDelete(brewery)}
          >
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default BreweryCardItem;
