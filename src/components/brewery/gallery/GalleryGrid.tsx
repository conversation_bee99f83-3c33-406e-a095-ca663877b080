
import React from 'react';
import { GalleryImage as GalleryImageType } from '@/types/brewery.patch';
import GalleryImage from './GalleryImage';
import { Image, LayoutList, GalleryHorizontal, GalleryThumbnails } from 'lucide-react';
import { GalleryViewMode } from './GalleryViewToggle';

interface GalleryGridProps {
  images: GalleryImageType[];
  isLoading: boolean;
  viewMode: GalleryViewMode;
  onDeleteImage: (imageId: string, imageUrl: string) => Promise<void>;
}

const GalleryGrid: React.FC<GalleryGridProps> = ({ 
  images, 
  isLoading, 
  viewMode = 'grid', 
  onDeleteImage 
}) => {
  if (isLoading) {
    return <div className="text-center py-8">Loading gallery...</div>;
  }
  
  if (images.length === 0) {
    return (
      <div className="text-center py-8 bg-muted/50 rounded-lg border border-dashed">
        <Image className="h-12 w-12 mx-auto text-muted-foreground/50" />
        <p className="mt-4 text-muted-foreground">No images in your gallery yet.</p>
        <p className="text-sm text-muted-foreground/80">Upload your first image to get started.</p>
      </div>
    );
  }

  switch (viewMode) {
    case 'list':
      return (
        <div className="space-y-2">
          {images.map((image) => (
            <div key={image.id} className="flex items-center border rounded-md p-2">
              <img 
                src={image.image_url} 
                alt={image.title || 'Gallery image'} 
                className="w-16 h-16 object-cover rounded-md mr-4"
              />
              <div className="flex-1">
                <p className="font-medium">{image.title || 'Untitled image'}</p>
                <p className="text-xs text-muted-foreground">
                  {new Date(image.created_at).toLocaleDateString()}
                </p>
              </div>
              <GalleryImage.DeleteButton 
                imageId={image.id}
                imageUrl={image.image_url}
                onDelete={onDeleteImage}
              />
            </div>
          ))}
        </div>
      );
      
    case 'thumbnails':
      return (
        <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <img 
                src={image.image_url} 
                alt={image.title || 'Gallery image'} 
                className="w-full h-20 object-cover rounded-md"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-md">
                <GalleryImage.DeleteButton 
                  imageId={image.id}
                  imageUrl={image.image_url}
                  onDelete={onDeleteImage}
                  size="icon"
                />
              </div>
            </div>
          ))}
        </div>
      );
      
    case 'horizontal':
      return (
        <div className="flex overflow-x-auto pb-4 space-x-4">
          {images.map((image) => (
            <div key={image.id} className="w-64 flex-shrink-0 relative group">
              <img 
                src={image.image_url} 
                alt={image.title || 'Gallery image'} 
                className="w-full h-48 object-cover rounded-md"
              />
              {image.title && (
                <div className="p-2 bg-background/80 absolute bottom-0 w-full rounded-b-md">
                  <p className="text-sm font-medium truncate">{image.title}</p>
                </div>
              )}
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <GalleryImage.DeleteButton 
                  imageId={image.id}
                  imageUrl={image.image_url}
                  onDelete={onDeleteImage}
                  size="sm"
                />
              </div>
            </div>
          ))}
        </div>
      );
      
    case 'grid':
    default:
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image) => (
            <GalleryImage 
              key={image.id} 
              image={image} 
              onDelete={onDeleteImage} 
            />
          ))}
        </div>
      );
  }
};

export default GalleryGrid;
