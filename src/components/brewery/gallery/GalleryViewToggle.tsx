
import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  LayoutGrid, 
  GalleryHorizontal, 
  GalleryThumbnails, 
  LayoutList 
} from 'lucide-react';

export type GalleryViewMode = 'grid' | 'list' | 'thumbnails' | 'horizontal';

interface GalleryViewToggleProps {
  currentView: GalleryViewMode;
  onViewChange: (view: GalleryViewMode) => void;
}

const GalleryViewToggle: React.FC<GalleryViewToggleProps> = ({ 
  currentView, 
  onViewChange 
}) => {
  return (
    <div className="flex items-center justify-end mb-4 space-x-2">
      <div className="text-sm text-muted-foreground mr-2">View:</div>
      <div className="flex border rounded-md overflow-hidden">
        <Button
          variant={currentView === 'grid' ? 'secondary' : 'ghost'}
          size="sm"
          className="rounded-none h-8 px-2"
          onClick={() => onViewChange('grid')}
          title="Grid view"
        >
          <LayoutGrid className="h-4 w-4" />
        </Button>
        
        <Button
          variant={currentView === 'list' ? 'secondary' : 'ghost'}
          size="sm"
          className="rounded-none h-8 px-2 border-l"
          onClick={() => onViewChange('list')}
          title="List view"
        >
          <LayoutList className="h-4 w-4" />
        </Button>
        
        <Button
          variant={currentView === 'thumbnails' ? 'secondary' : 'ghost'}
          size="sm"
          className="rounded-none h-8 px-2 border-l"
          onClick={() => onViewChange('thumbnails')}
          title="Thumbnails view"
        >
          <GalleryThumbnails className="h-4 w-4" />
        </Button>
        
        <Button
          variant={currentView === 'horizontal' ? 'secondary' : 'ghost'}
          size="sm"
          className="rounded-none h-8 px-2 border-l"
          onClick={() => onViewChange('horizontal')}
          title="Horizontal view"
        >
          <GalleryHorizontal className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default GalleryViewToggle;
