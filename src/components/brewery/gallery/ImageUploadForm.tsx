
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload } from 'lucide-react';

interface ImageUploadFormProps {
  onUpload: (file: File, title: string) => Promise<void>;
  isUploading: boolean;
}

const ImageUploadForm: React.FC<ImageUploadFormProps> = ({ onUpload, isUploading }) => {
  const [title, setTitle] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedFile) {
      await onUpload(selectedFile, title);
      setSelectedFile(null);
      setTitle('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-4 mb-6">
      <div>
        <Label htmlFor="image-upload">Upload New Image</Label>
        <Input
          id="image-upload"
          type="file"
          accept="image/*"
          onChange={(e) => e.target.files && setSelectedFile(e.target.files[0])}
          className="mt-1"
        />
      </div>
      
      <div>
        <Label htmlFor="image-title">Image Title (Optional)</Label>
        <Input
          id="image-title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter a title for this image"
          className="mt-1"
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={!selectedFile || isUploading}
        className="w-full"
      >
        {isUploading ? "Uploading..." : "Upload Image"}
        {!isUploading && <Upload className="ml-2 h-4 w-4" />}
      </Button>
    </form>
  );
};

export default ImageUploadForm;
