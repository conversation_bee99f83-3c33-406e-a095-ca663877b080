
import React from 'react';
import { GalleryImage as GalleryImageType } from '@/types/brewery.patch';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Trash2 } from 'lucide-react';

interface GalleryImageProps {
  image: GalleryImageType;
  onDelete: (imageId: string, imageUrl: string) => Promise<void>;
}

interface DeleteButtonProps {
  imageId: string;
  imageUrl: string;
  onDelete: (imageId: string, imageUrl: string) => Promise<void>;
  size?: 'sm' | 'icon'; // Fix: changed 'xs' to allowed values
  className?: string; // Added className prop
}

const DeleteButton: React.FC<DeleteButtonProps> = ({ 
  imageId, 
  imageUrl, 
  onDelete, 
  size = 'sm',
  className 
}) => {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this image?')) {
      setIsDeleting(true);
      try {
        await onDelete(imageId, imageUrl);
      } catch (error) {
        console.error('Error deleting image:', error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <Button 
      variant="destructive" 
      size={size}
      onClick={handleDelete}
      disabled={isDeleting}
      className={className}
    >
      <Trash2 className={size === 'icon' ? 'h-3 w-3' : 'h-4 w-4'} />
    </Button>
  );
};

const GalleryImage: React.FC<GalleryImageProps> & { DeleteButton: typeof DeleteButton } = ({ image, onDelete }) => {
  return (
    <Card className="overflow-hidden group relative">
      <CardContent className="p-0">
        <img 
          src={image.image_url} 
          alt={image.title || 'Gallery image'} 
          className="w-full h-48 object-cover"
        />
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
          <DeleteButton 
            imageId={image.id}
            imageUrl={image.image_url}
            onDelete={onDelete}
            size="sm"
            className="absolute top-2 right-2"
          />
        </div>
        {image.title && (
          <div className="p-2 bg-background/80 absolute bottom-0 w-full">
            <p className="text-sm font-medium truncate">{image.title}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Attach the DeleteButton component to GalleryImage
GalleryImage.DeleteButton = DeleteButton;

export default GalleryImage;
