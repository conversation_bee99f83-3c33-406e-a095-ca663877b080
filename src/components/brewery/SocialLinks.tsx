
import React, { useState } from 'react';
import { SocialLinks as SocialLinksType, SocialLinksJson } from '@/types/brewery';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Save } from 'lucide-react';
import { FaFacebook, FaInstagram, FaTwitter } from 'react-icons/fa';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface SocialLinksProps {
  breweryId: string;
  initialLinks?: SocialLinksType;
  onUpdate?: (links: SocialLinksType) => void;
}

const SocialLinks: React.FC<SocialLinksProps> = ({ 
  breweryId, 
  initialLinks = { facebook: '', instagram: '', twitter: '' },
  onUpdate
}) => {
  const [links, setLinks] = useState<SocialLinksType>(initialLinks);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleInputChange = (platform: keyof SocialLinksType, value: string) => {
    setLinks(prev => ({ ...prev, [platform]: value }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      // Create a JSON-compatible object for Supabase
      const socialLinksJson: SocialLinksJson = {
        facebook: links.facebook || '',
        instagram: links.instagram || '',
        twitter: links.twitter || ''
      };

      const { error } = await supabase
        .from('breweries')
        .update({ social_links: socialLinksJson })
        .eq('id', breweryId);
      
      if (error) throw error;
      
      if (onUpdate) {
        onUpdate(links);
      }
      
      toast({
        title: "Social links updated",
        description: "Your social media links have been updated successfully."
      });
    } catch (error: any) {
      console.error('Error updating social links:', error);
      toast({
        title: "Update failed",
        description: error.message || "Could not update social links",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="bg-background border rounded-lg p-6">
      <h2 className="text-2xl font-bold mb-4">Social Media Links</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="facebook" className="flex items-center gap-2">
            <FaFacebook className="text-blue-600" /> Facebook
          </Label>
          <Input
            id="facebook"
            type="url"
            placeholder="https://facebook.com/your-page"
            value={links.facebook || ''}
            onChange={e => handleInputChange('facebook', e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="instagram" className="flex items-center gap-2">
            <FaInstagram className="text-pink-600" /> Instagram
          </Label>
          <Input
            id="instagram"
            type="url"
            placeholder="https://instagram.com/your-handle"
            value={links.instagram || ''}
            onChange={e => handleInputChange('instagram', e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="twitter" className="flex items-center gap-2">
            <FaTwitter className="text-blue-400" /> Twitter
          </Label>
          <Input
            id="twitter"
            type="url"
            placeholder="https://twitter.com/your-handle"
            value={links.twitter || ''}
            onChange={e => handleInputChange('twitter', e.target.value)}
          />
        </div>
        
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Social Links
            </>
          )}
        </Button>
      </form>
    </div>
  );
};

export default SocialLinks;
