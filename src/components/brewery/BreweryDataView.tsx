
import React, { useState, useEffect } from "react";
import { Brewery } from "@/types/brewery.patch";
import BreweryDataHeader from "./BreweryDataHeader";
import BreweryDataContent from "./BreweryDataContent";
import BreweryPagination from "./BreweryPagination";
import { useBreweryPagination } from "@/hooks/brewery/useBreweryPagination";
import { useBreweryFilters } from "@/hooks/brewery/useBreweryFilters";
import { useFilterApplication } from "@/hooks/brewery/useFilterApplication";

interface BreweryDataViewProps {
  breweries: Brewery[];
  onRefresh?: () => Promise<void>;
  displayColumns?: string[];
  fieldTypes?: Record<string, string>;
}

const BreweryDataView: React.FC<BreweryDataViewProps> = ({
  breweries,
  onRefresh,
  displayColumns = ['name', 'brewery_type', 'city', 'state', 'phone', 'website', 'email'],
  fieldTypes = {}
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  // Filter state and functionality
  const {
    searchTerm,
    setSearchTerm,
    filterColumn,
    setFilterColumn,
    stateFilter,
    setStateFilter,
    cityFilter,
    setCityFilter,
    verificationFilter,
    setVerificationFilter,
    itemsPerPage,
    setItemsPerPage,
    availableStates,
    availableCities,
    filterOptions,
    filteredBreweries
  } = useBreweryFilters({ breweries });

  // Additional filter application hook
  const {
    filterValue,
    setFilterValue,
    applyFilter
  } = useFilterApplication();
  
  // Pagination state and functionality
  const {
    currentPage,
    setCurrentPage,
    displayData,
    totalPages,
    totalItems,
    handlePageChange
  } = useBreweryPagination({
    breweries: filteredBreweries,
    itemsPerPage
  });
  
  // Handle refresh
  const handleRefresh = async () => {
    if (onRefresh) {
      setIsLoading(true);
      try {
        await onRefresh();
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Update item per page in pagination when filter changes
  useEffect(() => {
    setItemsPerPage(itemsPerPage);
  }, [itemsPerPage]);
  
  return (
    <div className="space-y-4">
      <BreweryDataHeader
        hasData={breweries.length > 0}
        onRefresh={handleRefresh}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        filterColumn={filterColumn}
        setFilterColumn={setFilterColumn}
        filterValue={filterValue}
        setFilterValue={setFilterValue}
        onApplyFilter={applyFilter}
        isLoading={isLoading}
        filterOptions={filterOptions}
        stateFilter={stateFilter}
        setStateFilter={setStateFilter}
        cityFilter={cityFilter}
        setCityFilter={setCityFilter}
        availableCities={availableCities}
        availableStates={availableStates}
        verificationFilter={verificationFilter}
        setVerificationFilter={setVerificationFilter}
      />
      
      <BreweryDataContent
        breweries={displayData}
        isLoading={isLoading}
        displayColumns={displayColumns}
        fieldTypes={fieldTypes}
      />
      
      <BreweryPagination
        currentPage={currentPage}
        pageSize={itemsPerPage}
        totalPages={totalPages}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={setItemsPerPage}
      />
    </div>
  );
};

export default BreweryDataView;
