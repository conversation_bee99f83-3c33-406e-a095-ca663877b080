
import React from "react";
import { FileDigit, FileWarning } from "lucide-react";

const CsvRequirements: React.FC = () => {
  return (
    <div className="mt-4 p-4 bg-muted/30 rounded-md">
      <div className="flex items-center mb-2">
        <FileDigit className="h-4 w-4 mr-2" />
        <h4 className="text-sm font-medium">CSV Requirements:</h4>
      </div>
      <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
        <li>Must include a 'name' column</li>
        <li>First row should contain column headers</li>
        <li>Optional columns: logo, featureImage, address, city, state, etc.</li>
        <li>Duplicates will be automatically handled</li>
      </ul>
      
      <div className="flex items-center mt-4 mb-2">
        <FileWarning className="h-4 w-4 mr-2" />
        <h4 className="text-sm font-medium">Large File Handling:</h4>
      </div>
      <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
        <li>Files with 2500+ rows are fully supported</li>
        <li>Import processes in chunks of 250 rows to prevent timeouts</li>
        <li>Detailed progress indicators will show which chunk is being processed</li>
        <li className="font-medium">Please do not refresh the page during import</li>
        <li>For extremely large datasets (10,000+ rows), consider splitting files</li>
      </ul>
    </div>
  );
};

export default CsvRequirements;
