
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Brewery } from '@/types/brewery.patch'; // Use patch version
import { profileFormSchema, ProfileFormValues } from './ProfileFormSchema';
import { Spinner } from '@/components/ui/spinner';
import BasicFields from './BasicFields';
import ContactFields from './ContactFields';
import AddressFields from './AddressFields';
import ErrorDisplay from './ErrorDisplay';

interface ProfileFormProps {
  brewery: Brewery;
  isLoading: boolean;
  onSubmit: (data: ProfileFormValues) => Promise<void>;
}

const ProfileForm: React.FC<ProfileFormProps> = ({ brewery, isLoading, onSubmit }) => {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: brewery.name || '',
      description: brewery.description || '',
      address: brewery.address || '',
      city: brewery.city || '',
      state: brewery.state || '',
      zip: brewery.zip || '',
      phone: brewery.phone || '',
      website: brewery.website || '',
      email: brewery.email || '',
      brewery_type: brewery.brewery_type || 'micro', // Set default value for brewery_type
    },
  });

  const handleSubmit = async (data: ProfileFormValues) => {
    try {
      await onSubmit(data);
      form.reset(data); // Reset form with the updated data
    } catch (error) {
      console.error('Error in form submission:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        {form.formState.errors.root && (
          <ErrorDisplay message={form.formState.errors.root.message || 'Form submission error'} />
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic brewery information section */}
          <BasicFields form={form} />
          
          {/* Contact information section */}
          <ContactFields form={form} />
          
          {/* Address information section */}
          <AddressFields form={form} />
        </div>

        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Spinner className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Profile'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ProfileForm;
