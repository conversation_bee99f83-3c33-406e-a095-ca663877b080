
import React, { useState } from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Camera } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';

interface AvatarUploadProps {
  breweryId: string;
  avatarUrl?: string;
  onAvatarUpdate: (url: string) => void;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({ 
  breweryId, 
  avatarUrl, 
  onAvatarUpdate 
}) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];
    const fileExt = file.name.split('.').pop();
    const filePath = `breweries/${breweryId}/avatar-${Date.now()}.${fileExt}`;
    
    setUploading(true);
    setProgress(0);
    
    try {
      // Set up progress tracking with FileReader
      const fileSize = file.size;
      const fileReader = new FileReader();
      let lastLoaded = 0;
      
      fileReader.onprogress = (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          setProgress(percent / 2); // First half of the progress is reading the file
        }
      };
      
      // Read the file to simulate progress
      fileReader.readAsArrayBuffer(file);
      
      // Upload the file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('brewery-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;
      
      // Set progress to 75% after successful upload
      setProgress(75);

      // Get the public URL for the uploaded file
      const { data: publicUrlData } = supabase.storage
        .from('brewery-images')
        .getPublicUrl(filePath);
      
      const avatarUrl = publicUrlData.publicUrl;
      
      // Update the brewery avatar in the database
      const { error: updateError } = await supabase
        .from('breweries')
        .update({ avatar: avatarUrl })
        .eq('id', breweryId);
        
      if (updateError) throw updateError;
      
      // Update state and set progress to 100%
      setProgress(100);
      onAvatarUpdate(avatarUrl);
      
      toast({
        title: "Avatar updated",
        description: "Your brewery avatar has been updated successfully",
      });
    } catch (error: any) {
      console.error('Error uploading avatar:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to update avatar",
        variant: "destructive",
      });
    } finally {
      // Reset progress after a short delay to show 100% completion
      setTimeout(() => {
        setUploading(false);
        setProgress(0);
      }, 1000);
    }
  };

  // Get avatar initials from brewery name or ID
  const getInitials = () => {
    if (!avatarUrl) return breweryId.substring(0, 2).toUpperCase();
    return '';
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="relative">
        <Avatar className="h-24 w-24 border-2 border-primary">
          <AvatarImage src={avatarUrl} alt="Brewery avatar" />
          <AvatarFallback>{getInitials()}</AvatarFallback>
        </Avatar>
        
        <div className="absolute bottom-0 right-0">
          <div className="relative">
            <input
              type="file"
              accept="image/*"
              id="avatar-upload"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onChange={handleAvatarChange}
              disabled={uploading}
            />
            <Button 
              size="icon" 
              variant="outline" 
              className="rounded-full bg-background"
              disabled={uploading}
            >
              <Camera className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      
      {uploading && (
        <div className="w-full max-w-xs">
          <Progress value={progress} className="h-2" />
          <p className="text-xs text-center mt-1">{progress}% uploaded</p>
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
