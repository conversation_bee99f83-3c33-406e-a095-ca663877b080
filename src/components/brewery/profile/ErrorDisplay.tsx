
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { AuthUser } from '@/types/brewery.patch';

interface ErrorDisplayProps {
  message: string;
  user?: AuthUser;
  isLoading?: boolean;
  breweryId?: string;
  onRefresh?: () => Promise<void>;
  onCreateTestBrewery?: () => Promise<void>;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  message,
  user,
  isLoading = false,
  breweryId,
  onRefresh = async () => {},
  onCreateTestBrewery = async () => {}
}) => {
  const handleRefresh = () => {
    return onRefresh();
  };

  return (
    <Card className="mx-auto max-w-3xl">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="h-12 w-12 text-red-500" />
        </div>
        <CardTitle>Brewery Profile Error</CardTitle>
        <CardDescription>
          We encountered an issue accessing your brewery profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800 font-medium">Error details:</p>
          <p className="text-red-600">{message}</p>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h3 className="font-semibold mb-2">Possible solutions:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Refresh the page and try again</li>
            <li>Check if your brewery ID is correct</li>
            {user?.role === 'admin' && <li>Create a new test brewery (admin only)</li>}
            <li>Contact support if the issue persists</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button 
          onClick={handleRefresh}
          disabled={isLoading}
          variant="outline"
          className="w-full sm:w-auto"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
        
        {user?.role === 'admin' && (
          <Button 
            onClick={onCreateTestBrewery}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Create Test Brewery
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ErrorDisplay;
