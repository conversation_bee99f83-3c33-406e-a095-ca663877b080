
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { UserCircle, MapPin, Link, Tv, Beer, Camera } from 'lucide-react';
import { Brewery } from '@/types/brewery.patch';
import ProfileForm from './ProfileForm';
import SocialLinksForm from './SocialLinksForm';
import { SocialLinks } from '@/types/brewery.patch';
import DigitalBoardManager from '@/components/digital-board/DigitalBoardManager';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import PhotoGallery from '../PhotoGallery';

interface ProfileTabsProps {
  brewery: Brewery;
  isLoading: boolean;
  uploadProgress: number;
  fileErrors: (Record<string, string> & any[]) | undefined[];
  socialLinks: SocialLinks;
  onProfileSubmit: (data: any) => Promise<void>;
  onImageUpload: (type: string, file: File) => Promise<string>;
  onSocialLinksUpdate: (links: SocialLinks, breweryId?: string) => Promise<void>;
}

const ProfileTabs: React.FC<ProfileTabsProps> = ({
  brewery,
  isLoading,
  uploadProgress,
  fileErrors,
  socialLinks,
  onProfileSubmit,
  onImageUpload,
  onSocialLinksUpdate,
}) => {
  const navigate = useNavigate();

  if (!brewery) {
    return <div>Loading brewery profile...</div>;
  }

  return (
    <Tabs defaultValue="profile" className="w-full">
      <TabsList className="mb-8">
        <TabsTrigger value="profile">
          <UserCircle className="mr-2 h-4 w-4" />
          Profile
        </TabsTrigger>
        <TabsTrigger value="social">
          <Link className="mr-2 h-4 w-4" />
          Social Links
        </TabsTrigger>
        <TabsTrigger value="gallery">
          <Camera className="mr-2 h-4 w-4" />
          Photo Gallery
        </TabsTrigger>
        <TabsTrigger value="digital-board">
          <Tv className="mr-2 h-4 w-4" />
          Digital Board
        </TabsTrigger>
        <TabsTrigger value="beer-menu">
          <Beer className="mr-2 h-4 w-4" />
          Beer Menu
        </TabsTrigger>
      </TabsList>

      <TabsContent value="profile">
        <ProfileForm 
          brewery={brewery}
          isLoading={isLoading}
          onSubmit={onProfileSubmit}
        />
      </TabsContent>
      
      <TabsContent value="social">
        <SocialLinksForm 
          breweryId={brewery.id}
          socialLinks={socialLinks}
          onSubmit={onSocialLinksUpdate}
          isLoading={isLoading}
        />
      </TabsContent>
      
      <TabsContent value="gallery">
        <PhotoGallery breweryId={brewery.id} />
      </TabsContent>
      
      <TabsContent value="digital-board">
        <DigitalBoardManager breweryId={brewery.id} breweryName={brewery.name} />
      </TabsContent>
      
      <TabsContent value="beer-menu">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">Beer Menu Management</h2>
              <p className="text-muted-foreground">Create and manage your brewery's beer menu</p>
            </div>
            <Button 
              onClick={() => navigate('/menu-management')}
              className="whitespace-nowrap"
            >
              <Beer className="mr-2 h-4 w-4" />
              Manage Beer Menu
            </Button>
          </div>
          
          <div className="bg-muted rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Beer Menu Features</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Create a complete beer menu with all your brewery's offerings</li>
              <li>Include details like ABV, IBU, descriptions, and pricing</li>
              <li>Import and export beer menu items via CSV</li>
              <li>Mark beers as seasonal or featured</li>
              <li>Automatically display your beer menu on your digital beer board</li>
              <li>Categorize beers by style (IPA, Stout, etc.)</li>
            </ul>
            
            <div className="mt-6">
              <Button 
                onClick={() => navigate('/menu-management')}
                variant="default"
              >
                Go to Menu Management
              </Button>
            </div>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default ProfileTabs;
