
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Brewery } from '@/types/brewery';
import ImageUploadField from '../ImageUploadField';

interface BreweryImageUploadProps {
  brewery: Brewery;
  onLogoUpload: (file: File) => Promise<void>;
  onFeatureImageUpload: (file: File) => Promise<void>;
  uploadProgress: Record<string, number>;
  fileErrors: Record<string, string>;
}

const BreweryImageUpload: React.FC<BreweryImageUploadProps> = ({
  brewery,
  onLogoUpload,
  onFeatureImageUpload,
  uploadProgress,
  fileErrors
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Brewery Images</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Logo</h3>
          <ImageUploadField
            fieldName="logo"
            currentValue={brewery.logo || ''}
            onChange={() => {}} // Read-only display, actual changes happen via file upload
            onFileUpload={onLogoUpload}
            uploadProgress={uploadProgress['logo'] || 0}
            error={fileErrors['logo']}
          />
        </div>
        
        <div>
          <h3 className="text-lg font-medium mb-2">Feature Image</h3>
          <ImageUploadField
            fieldName="featureImage"
            currentValue={brewery.featureImage || brewery.feature_image || ''}
            onChange={() => {}} // Read-only display, actual changes happen via file upload
            onFileUpload={onFeatureImageUpload}
            uploadProgress={uploadProgress['featureImage'] || 0}
            error={fileErrors['featureImage']}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default BreweryImageUpload;
