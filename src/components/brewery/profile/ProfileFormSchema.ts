
import { z } from 'zod';

export const profileFormSchema = z.object({
  name: z.string().min(2, { message: "Brewery name must be at least 2 characters." }),
  description: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal('')),
  email: z.string().email({ message: "Please enter a valid email address." }).optional().or(z.literal('')),
  brewery_type: z.string().default('micro'),
});

export type ProfileFormValues = z.infer<typeof profileFormSchema>;
