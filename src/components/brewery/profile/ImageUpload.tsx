
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, Image } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ImageUploadProps {
  breweryId: string;
  isLoading: boolean;
  uploadProgress: number;
  fileErrors: string[];
  onImageUpload: (file: File) => Promise<void>;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  breweryId,
  isLoading,
  uploadProgress,
  fileErrors,
  onImageUpload,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      // Basic validation
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid file type',
          description: 'Please select an image file',
          variant: 'destructive',
        });
        return;
      }
      
      setSelectedFile(file);
    }
  };
  
  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: 'No file selected',
        description: 'Please select an image to upload',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      await onImageUpload(selectedFile);
      setSelectedFile(null);
      
      // Reset file input
      const fileInput = document.getElementById('image-upload') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }
    } catch (error) {
      console.error('Error uploading image:', error);
    }
  };
  
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">Upload Brewery Photos</h3>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <input 
                type="file" 
                id="image-upload" 
                accept="image/*"
                className="hidden" 
                onChange={handleFileChange}
                disabled={isLoading}
              />
              <label htmlFor="image-upload" className="block w-full">
                <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer hover:border-primary">
                  <div className="text-center">
                    <Upload className="mx-auto h-10 w-10 text-muted-foreground" />
                    <p className="mt-2 text-sm text-muted-foreground">
                      {selectedFile ? selectedFile.name : 'Drag and drop an image, or click to browse'}
                    </p>
                  </div>
                </div>
              </label>
            </div>
            
            <Button 
              type="button" 
              onClick={handleUpload} 
              disabled={isLoading || !selectedFile}
            >
              Upload
            </Button>
          </div>
          
          {isLoading && uploadProgress > 0 && (
            <div className="mt-4">
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-xs text-muted-foreground text-right mt-1">
                {uploadProgress}%
              </p>
            </div>
          )}
          
          {fileErrors.length > 0 && (
            <div className="mt-4 text-red-500 text-sm">
              {fileErrors.map((error, index) => (
                <p key={index}>{error}</p>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="mt-8">
        <h4 className="text-md font-medium mb-4">Brewery Gallery</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Placeholder for gallery images */}
          <Card className="bg-muted flex items-center justify-center h-40">
            <Image className="h-8 w-8 text-muted-foreground" />
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ImageUpload;
