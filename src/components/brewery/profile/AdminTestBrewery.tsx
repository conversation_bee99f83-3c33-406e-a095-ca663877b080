
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { AuthUser } from '@/types/brewery.patch';

interface AdminTestBreweryProps {
  user: AuthUser;
  isLoading: boolean;
  onCreateTestBrewery: () => Promise<void>;
}

const AdminTestBrewery: React.FC<AdminTestBreweryProps> = ({ 
  user, 
  isLoading, 
  onCreateTestBrewery 
}) => {
  if (user.role !== 'admin') return null;
  
  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Create Test Brewery</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            As an administrator, you can create a test brewery to explore brewery management features.
          </p>
          <Button 
            onClick={onCreateTestBrewery}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            {isLoading ? "Creating..." : "Create Test Brewery"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminTestBrewery;
