
import React from 'react';
import { Brewery } from '@/types/brewery.patch';
import AvatarUpload from './AvatarUpload';
import SocialStats from '../SocialStats';

interface ProfileHeaderProps {
  brewery: Brewery;
  isLoading?: boolean;
  onAvatarUpdate: (avatarUrl: string) => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ brewery, isLoading, onAvatarUpdate }) => {
  if (!brewery) return null;

  return (
    <div className="mb-12">
      <div className="relative h-48 bg-gray-200 rounded-lg overflow-hidden">
        {brewery.featureImage ? (
          <img 
            src={brewery.featureImage} 
            alt={`${brewery.name} cover`} 
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-100">
            <p className="text-gray-400">No cover image</p>
          </div>
        )}
      </div>
      
      <div className="relative px-4">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-8">
          <div className="z-10 -mt-16">
            {brewery.id && (
              <AvatarUpload
                breweryId={brewery.id}
                avatarUrl={brewery.avatar}
                onAvatarUpdate={onAvatarUpdate}
              />
            )}
          </div>
          
          <div className="flex-1 mt-2 text-center md:text-left">
            <h1 className="text-3xl font-bold">{brewery.name}</h1>
            {brewery.city && brewery.state && (
              <p className="text-gray-600">{brewery.city}, {brewery.state}</p>
            )}
          </div>

          {brewery.id && (
            <div className="w-full md:w-auto mt-4 md:mt-0">
              <SocialStats 
                breweryId={brewery.id}
                initialFollowerCount={brewery.followerCount}
                initialLikeCount={brewery.likeCount}
                initialIsFollowing={brewery.isFollowing}
                initialIsLiked={brewery.isLiked}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
