
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { SocialLinks } from '@/types/brewery';
import { Facebook, Instagram, Twitter } from 'lucide-react';

interface SocialLinksFormProps {
  socialLinks: SocialLinks;
  breweryId?: string;
  isLoading: boolean;
  onSubmit: (links: SocialLinks, breweryId?: string) => Promise<void>;
}

const socialLinksSchema = z.object({
  facebook: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
});

const SocialLinksForm: React.FC<SocialLinksFormProps> = ({ socialLinks, breweryId, isLoading, onSubmit }) => {
  const form = useForm<SocialLinks>({
    resolver: zodResolver(socialLinksSchema),
    defaultValues: socialLinks,
  });

  const handleSubmit = async (values: SocialLinks) => {
    await onSubmit(values, breweryId);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="facebook"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <Facebook className="h-4 w-4" /> Facebook
                </FormLabel>
                <FormControl>
                  <Input placeholder="Facebook URL" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instagram"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <Instagram className="h-4 w-4" /> Instagram
                </FormLabel>
                <FormControl>
                  <Input placeholder="Instagram URL" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="twitter"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <Twitter className="h-4 w-4" /> Twitter
                </FormLabel>
                <FormControl>
                  <Input placeholder="Twitter URL" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Social Links'}
        </Button>
      </form>
    </Form>
  );
};

export default SocialLinksForm;
