
import React from 'react';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UseFormReturn } from 'react-hook-form';
import { ProfileFormValues } from './ProfileFormSchema';

interface BasicFieldsProps {
  form: UseFormReturn<ProfileFormValues>;
}

const BasicFields: React.FC<BasicFieldsProps> = ({ form }) => {
  return (
    <>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Brewery Name</FormLabel>
            <FormControl>
              <Input placeholder="Your brewery name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="Tell us about your brewery" 
                className="min-h-[120px]" 
                {...field} 
                value={field.value || ""}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="brewery_type"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Brewery Type</FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              value={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a brewery type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="micro">Micro Brewery</SelectItem>
                <SelectItem value="nano">Nano Brewery</SelectItem>
                <SelectItem value="regional">Regional Brewery</SelectItem>
                <SelectItem value="brewpub">Brewpub</SelectItem>
                <SelectItem value="large">Large Brewery</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="bar">Bar</SelectItem>
                <SelectItem value="contract">Contract Brewery</SelectItem>
                <SelectItem value="proprietor">Proprietor</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <FormDescription>
              The type of brewery establishment
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default BasicFields;
