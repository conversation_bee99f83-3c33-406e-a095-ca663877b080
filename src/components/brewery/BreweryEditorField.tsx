
import React from "react";
import { Label } from "@/components/ui/label";
import <PERSON>Field from "./editor-fields/FileField";
import DescriptionField from "./editor-fields/DescriptionField";
import BooleanField from "./editor-fields/BooleanField";
import InputField from "./editor-fields/InputField";
import { formatLabel, getPlaceholder } from "./utils/labelFormatters";

interface BreweryEditorFieldProps {
  column: string;
  value: any;
  onInputChange: (key: string, value: any) => void;
  uploadProgress: Record<string, number>;
  fileErrors: Record<string, string>;
  onFileUpload: (field: string, file: File) => void;
  fieldType?: string;
}

const BreweryEditorField: React.FC<BreweryEditorFieldProps> = ({
  column,
  value,
  onInputChange,
  uploadProgress,
  fileErrors,
  onFileUpload,
  fieldType = 'text'
}) => {
  // Determine if this is a special field type
  const isFileField = column === 'logo' || column === 'featureImage' || column === 'avatar';
  const isDescriptionField = column === 'description';
  const isBooleanField = typeof value === 'boolean' || 
                         column === 'claimed' || 
                         column === 'verificationOpen' || 
                         column === 'verified';

  const renderInputField = () => {
    if (isFileField) {
      return (
        <FileField
          column={column}
          value={value}
          onInputChange={onInputChange}
          uploadProgress={uploadProgress[column] || 0}
          fileError={fileErrors[column]}
          onFileUpload={onFileUpload}
        />
      );
    }

    if (isDescriptionField) {
      return (
        <DescriptionField
          column={column}
          value={value}
          onInputChange={onInputChange}
        />
      );
    }

    if (isBooleanField) {
      return (
        <BooleanField
          column={column}
          value={value}
          onInputChange={onInputChange}
        />
      );
    }

    return (
      <InputField
        column={column}
        value={value}
        onInputChange={onInputChange}
        fieldType={fieldType}
        placeholder={getPlaceholder(column, fieldType)}
      />
    );
  };

  return (
    <div>
      <Label htmlFor={column}>{formatLabel(column)}</Label>
      {renderInputField()}
    </div>
  );
};

export default BreweryEditorField;
