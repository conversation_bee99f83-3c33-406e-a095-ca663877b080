
import React, { useState } from 'react';
import { useGallery } from '@/hooks/brewery/useGallery';
import ImageUploadForm from './gallery/ImageUploadForm';
import GalleryGrid from './gallery/GalleryGrid';
import GalleryViewToggle, { GalleryViewMode } from './gallery/GalleryViewToggle';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface PhotoGalleryProps {
  breweryId: string;
}

const PhotoGallery: React.FC<PhotoGalleryProps> = ({ breweryId }) => {
  const [viewMode, setViewMode] = useState<GalleryViewMode>('grid');
  
  const {
    images,
    isLoading,
    uploadingImage,
    handleImageUpload,
    handleDeleteImage
  } = useGallery(breweryId);

  if (!breweryId) {
    return <div className="text-center py-8">Please select a brewery to view gallery.</div>;
  }

  return (
    <div className="space-y-6">
      <div className="bg-background border rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4">Photo Gallery</h2>
        
        <Card className="mb-6">
          <CardContent className="p-6">
            <ImageUploadForm 
              onUpload={handleImageUpload}
              isUploading={uploadingImage}
            />
          </CardContent>
        </Card>
        
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-medium">Gallery</h3>
            <GalleryViewToggle 
              currentView={viewMode} 
              onViewChange={setViewMode} 
            />
          </div>
          <Separator className="mb-4" />
          
          <GalleryGrid 
            images={images} 
            isLoading={isLoading} 
            viewMode={viewMode}
            onDeleteImage={handleDeleteImage} 
          />
        </div>
      </div>
    </div>
  );
};

export default PhotoGallery;
