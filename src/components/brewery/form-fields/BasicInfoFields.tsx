
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { UseFormReturn } from 'react-hook-form';
import { BreweryFormValues } from '../schemas/breweryFormSchema';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface BasicInfoFieldsProps {
  form: UseFormReturn<BreweryFormValues>;
}

const BasicInfoFields: React.FC<BasicInfoFieldsProps> = ({ form }) => {
  return (
    <>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Brewery Name</FormLabel>
            <FormControl>
              <Input placeholder="Enter brewery name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="brewery_type"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Brewery Type</FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select brewery type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="micro">Micro</SelectItem>
                <SelectItem value="nano">Nano</SelectItem>
                <SelectItem value="regional">Regional</SelectItem>
                <SelectItem value="brewpub">Brewpub</SelectItem>
                <SelectItem value="large">Large</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="bar">Bar</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="proprietor">Proprietor</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem className="col-span-1 md:col-span-2">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="Enter brewery description" 
                className="min-h-[120px]" 
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default BasicInfoFields;
