
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';
import { BreweryFormValues } from '../schemas/breweryFormSchema';

interface MediaFieldsProps {
  form: UseFormReturn<BreweryFormValues>;
}

const MediaFields: React.FC<MediaFieldsProps> = ({ form }) => {
  return (
    <>
      <FormField
        control={form.control}
        name="logo"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Logo URL</FormLabel>
            <FormControl>
              <Input placeholder="https://example.com/logo.png" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="featureImage"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Feature Image URL</FormLabel>
            <FormControl>
              <Input placeholder="https://example.com/feature.jpg" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default MediaFields;
