
import React from 'react';
import FollowerStats from './social/FollowerStats';
import LikeStats from './social/LikeStats';
import { useBrewerySocialStats } from '@/hooks/brewery/useBrewerySocialStats';

interface SocialStatsProps {
  breweryId: string;
  initialFollowerCount?: number;
  initialLikeCount?: number;
  initialIsFollowing?: boolean;
  initialIsLiked?: boolean;
}

const SocialStats: React.FC<SocialStatsProps> = ({
  breweryId,
  initialFollowerCount = 0,
  initialLikeCount = 0,
  initialIsFollowing = false,
  initialIsLiked = false
}) => {
  const {
    isFollowing,
    isLiked,
    followerCount,
    likeCount,
    isLoading,
    toggleFollow,
    toggleLike
  } = useBrewerySocialStats(breweryId);

  return (
    <div className="flex space-x-3">
      <FollowerStats 
        followerCount={followerCount}
        isFollowing={isFollowing}
        isLoading={isLoading}
        onToggleFollow={toggleFollow}
      />
      
      <LikeStats 
        likeCount={likeCount}
        isLiked={isLiked}
        isLoading={isLoading}
        onToggleLike={toggleLike}
      />
    </div>
  );
};

export default SocialStats;
