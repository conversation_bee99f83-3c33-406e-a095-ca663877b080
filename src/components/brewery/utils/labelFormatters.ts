
export const formatLabel = (str: string): string => {
  // Handle special cases first
  if (str === 'featureImage') return 'Feature Image';
  if (str === 'verificationOpen') return 'Open for Verification';
  
  // Convert camelCase or snake_case to Title Case
  return str
    .replace(/([A-Z])/g, ' $1') // Insert space before capital letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/^\w/, c => c.toUpperCase()) // Capitalize first letter
    .trim();
};

export const getPlaceholder = (column: string, fieldType: string): string => {
  switch (column) {
    case 'website':
      return 'https://www.example.com';
    case 'email':
      return '<EMAIL>';
    case 'phone':
      return '(*************';
    case 'zip':
      return '12345';
    case 'address':
      return '123 Main St';
    case 'city':
      return 'City name';
    case 'state':
      return 'State';
    case 'name':
      return 'Brewery name';
    default:
      return `Enter ${formatLabel(column).toLowerCase()}`;
  }
};
