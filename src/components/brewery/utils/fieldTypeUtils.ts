
import { formatLabel, getPlaceholder } from "./labelFormatters";

/**
 * Determines the appropriate field type based on column name or provided field types mapping
 */
export const getFieldType = (column: string, fieldTypes: Record<string, string> = {}): string => {
  // Use supplied field type if available
  if (fieldTypes[column]) {
    return fieldTypes[column];
  }
  
  // Basic field type detection by column name
  const normalizedColumn = column.toLowerCase();
  
  // Logo and image field detection
  if (normalizedColumn === 'logo' || 
      normalizedColumn === 'avatar' || 
      normalizedColumn === 'featureimage' || 
      normalizedColumn === 'feature_image' || 
      normalizedColumn.includes('image')) return 'file';
      
  // Website field detection
  if (normalizedColumn === 'website' || 
      normalizedColumn.includes('site') || 
      normalizedColumn.includes('url') || 
      normalizedColumn.includes('www')) return 'url';
      
  // Address field detection
  if (normalizedColumn === 'address' || 
      normalizedColumn.includes('street') || 
      normalizedColumn.includes('location') || 
      normalizedColumn === 'addr') return 'address';
      
  // Phone field detection
  if (normalizedColumn === 'phone' || normalizedColumn.includes('phone')) return 'tel';
  
  // Zip code field detection
  if (normalizedColumn === 'zip' || normalizedColumn.includes('zip')) return 'zip';
  
  // Email field detection
  if (normalizedColumn === 'email' || normalizedColumn.includes('mail')) return 'email';
  
  // Boolean field detection
  if (normalizedColumn === 'verified' || 
      normalizedColumn === 'claimed' || 
      normalizedColumn === 'claimable' ||
      normalizedColumn === 'verificationopen' || 
      normalizedColumn === 'verification_open' ||
      normalizedColumn.startsWith('is_') ||
      normalizedColumn.includes('enabled') ||
      normalizedColumn.includes('active')) return 'boolean';
      
  // Description field detection
  if (normalizedColumn === 'description' || 
      normalizedColumn.includes('desc') || 
      normalizedColumn.includes('about')) return 'description';
  
  // Default to text field
  return 'text';
};

/**
 * Gets placeholder text for specific field types
 */
export const getInputPlaceholder = (column: string, fieldType: string): string => {
  return getPlaceholder(column, fieldType);
};

// Export getPlaceholder to maintain API consistency
export { getPlaceholder } from './labelFormatters';
