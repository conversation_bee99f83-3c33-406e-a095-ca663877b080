import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { parseCSV } from "@/lib/csv";
import { processBreweryCSV, deduplicateBreweries } from "@/hooks/brewery/utils/breweryImportUtils";
import { useImportProgress } from "@/hooks/brewery/useImportProgress";

interface UseFileUploadProps {
  reloadBreweries?: () => Promise<void>;
  resetProgress: () => void;
  updateProgress: (value: number) => void;
  updateRowProgress: (processed: number, total: number) => void;
  updateChunkProgress: (current: number, total: number) => void;
  setError: (error: string | null) => void;
  setFieldMapping: (mapping: Record<string, string>) => void;
}

export function useFileUpload({
  reloadBreweries,
  resetProgress,
  updateProgress,
  updateRowProgress,
  updateChunkProgress,
  setError,
  setFieldMapping
}: UseFileUploadProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [duplicatesFound, setDuplicatesFound] = useState<number>(0);

  // Handle actual file upload and processing
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log("Import file upload called", event.target.files);
    setIsLoading(true);
    resetProgress();
    updateProgress(5);
    
    try {
      // Get the file
      const file = event.target.files?.[0];
      if (!file) {
        throw new Error("No file selected");
      }
      
      // Process the CSV file
      updateProgress(15);
      console.log("Processing CSV file...");
      
      const result = await parseCSV(file);
      updateProgress(25);
      
      console.log("CSV parsed successfully:", {
        rowCount: result.data.length,
        columns: result.columns,
        fieldMapping: result.fieldMapping
      });
      
      // Set field mapping from the parsed result
      setFieldMapping(result.fieldMapping);
      
      // Process data into brewery structure and deduplicate
      const { breweryData, duplicates } = await processBreweryCSV(file);
      setDuplicatesFound(duplicates);
      
      // Update row tracking
      updateRowProgress(0, breweryData.length);
      updateProgress(40);
      
      // Split data into chunks for processing
      const CHUNK_SIZE = 100;
      const chunks = [];
      for (let i = 0; i < breweryData.length; i += CHUNK_SIZE) {
        chunks.push(breweryData.slice(i, i + CHUNK_SIZE));
      }
      
      // Update chunk tracking
      updateChunkProgress(0, chunks.length);
      
      // Process each chunk with real progress updates
      let rowsProcessed = 0;
      let totalDuplicatesFound = duplicates;
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        updateChunkProgress(i + 1, chunks.length);
        
        // Process this chunk - simulate database operation
        const breweriesToStore = chunk.map(brewery => ({
          ...brewery,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));
        
        // Store in localStorage as our current implementation
        const existingBreweriesJSON = localStorage.getItem("breweries");
        const existingBreweries = existingBreweriesJSON 
          ? JSON.parse(existingBreweriesJSON) 
          : [];
          
        // Check for duplicates in existing data with better duplicate detection
        const { dedupedBreweries, duplicateCount } = deduplicateBreweries(
          [...existingBreweries, ...breweriesToStore]
        );
        
        // Update total duplicates found
        const newDuplicatesInThisChunk = duplicateCount;
        totalDuplicatesFound += newDuplicatesInThisChunk;
        setDuplicatesFound(totalDuplicatesFound);
        
        // Store updated list
        localStorage.setItem("breweries", JSON.stringify(dedupedBreweries));
        
        // Simulate delay (remove in production)
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Update row tracking
        rowsProcessed += chunk.length;
        updateRowProgress(rowsProcessed, breweryData.length);
        
        // Update overall progress based on processed chunks
        updateProgress(40 + Math.round((i + 1) / chunks.length * 55));
      }
      
      // Set final progress
      updateProgress(95);
      
      // Simulate final database operations
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Complete import
      updateProgress(100);
      
      // Reload breweries if function provided
      if (reloadBreweries) {
        await reloadBreweries();
      }
      
      toast({
        title: "Import completed",
        description: `Successfully imported ${breweryData.length} breweries from ${file.name}${
          totalDuplicatesFound > 0 ? `, resolved ${totalDuplicatesFound} duplicates` : ''
        }`
      });
      
      console.log("Import completed successfully");
      
      // Keep showing the progress for a moment so user can see completion
      setTimeout(() => setIsLoading(false), 2500);
    } catch (err) {
      console.error("Import error:", err);
      setError(err instanceof Error ? err.message : "An error occurred during import");
      
      toast({
        title: "Import failed",
        description: err instanceof Error ? err.message : "An error occurred during import",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleFileUpload,
    duplicatesFound
  };
}
