
import React from "react";
import { useImportProgress } from "@/hooks/brewery/useImportProgress";
import { useFileSelection } from "./useFileSelection";
import { useFileUpload } from "./useFileUpload";
import { useSampleImport } from "./useSampleImport";

interface UseBreweryImportStateProps {
  reloadBreweries?: () => Promise<void>;
}

export function useBreweryImportState({ reloadBreweries }: UseBreweryImportStateProps = {}) {
  // Use the improved progress hook
  const progressHook = useImportProgress();
  const { 
    progress, 
    currentChunk, 
    totalChunks, 
    rowsProcessed, 
    totalRows,
    resetProgress,
    updateProgress,
    updateChunkProgress,
    updateRowProgress
  } = progressHook;
  
  // Use the file selection hook
  const fileSelection = useFileSelection();
  const {
    fileName,
    error,
    rowCount,
    fileInputRef,
    selectedFile,
    fieldMapping,
    setError,
    setFileName,
    setRowCount,
    setSelectedFile,
    setFieldMapping,
    handleChange,
    handleBrowseClick
  } = fileSelection;
  
  // Use the file upload hook
  const fileUpload = useFileUpload({
    reloadBreweries,
    resetProgress,
    updateProgress,
    updateRowProgress,
    updateChunkProgress,
    setError,
    setFieldMapping
  });
  const {
    isLoading: isFileUploading,
    handleFileUpload
  } = fileUpload;
  
  // Use the sample import hook
  const sampleImport = useSampleImport({
    reloadBreweries,
    resetProgress,
    updateProgress,
    updateChunkProgress,
    updateRowProgress,
    setError
  });
  const {
    isLoading: isSampleImporting,
    handleImport
  } = sampleImport;
  
  // Combine loading states
  const isLoading = isFileUploading || isSampleImporting;

  return {
    fileName,
    error,
    rowCount,
    fileInputRef,
    selectedFile,
    setError,
    setFileName,
    setRowCount,
    setSelectedFile,
    handleChange,
    handleBrowseClick,
    // Add properties that DataImport.tsx expects
    isLoading,
    progress,
    fieldMapping,
    handleFileUpload,
    handleImport,
    errorMessage: error,
    // Add real progress tracking properties
    currentChunk,
    totalChunks,
    rowsProcessed,
    totalRows
  };
}
