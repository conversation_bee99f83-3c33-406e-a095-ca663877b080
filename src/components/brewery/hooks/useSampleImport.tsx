
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

interface UseSampleImportProps {
  reloadBreweries?: () => Promise<void>;
  resetProgress: () => void;
  updateProgress: (value: number) => void;
  updateChunkProgress: (current: number, total: number) => void;
  updateRowProgress: (processed: number, total: number) => void;
  setError: (error: string | null) => void;
}

export function useSampleImport({
  reloadBreweries,
  resetProgress,
  updateProgress,
  updateChunkProgress,
  updateRowProgress,
  setError
}: UseSampleImportProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Handle import button click (for sample data)
  const handleImport = async () => {
    setIsLoading(true);
    resetProgress();
    updateProgress(5);
    
    try {
      // Simulate import process with real progress updates
      updateProgress(15);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateProgress(30);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Update progress with chunk information
      updateChunkProgress(1, 3);
      updateRowProgress(0, 100);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateChunkProgress(2, 3);
      updateRowProgress(50, 100);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateChunkProgress(3, 3);
      updateRowProgress(100, 100);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateProgress(95);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateProgress(100);
      
      // Reload breweries if function provided
      if (reloadBreweries) {
        await reloadBreweries();
      }
      
      toast({
        title: "Sample import completed",
        description: "Successfully imported sample data"
      });
      
      console.log("Import completed successfully");
    } catch (err) {
      console.error("Import error:", err);
      setError(err instanceof Error ? err.message : "An error occurred during import");
      
      toast({
        title: "Sample import failed",
        description: err instanceof Error ? err.message : "An error occurred during import",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleImport
  };
}
