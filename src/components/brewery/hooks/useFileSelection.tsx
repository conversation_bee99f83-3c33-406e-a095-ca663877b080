
import React, { useState, useRef } from "react";
import { toast } from "@/hooks/use-toast";
import { parseCSV } from "@/lib/csv";

export function useFileSelection() {
  const [fileName, setFileName] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [rowCount, setRowCount] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  
  // Handle file selection without triggering upload
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log("BreweryImport handleChange called", event.target.files);
    setError(null);
    
    const file = event.target.files?.[0];
    
    if (!file) {
      setFileName("");
      setRowCount(null);
      setSelectedFile(null);
      return;
    }
    
    // Validate file type
    if (!file.name.endsWith('.csv')) {
      setError("Please upload a valid CSV file.");
      setFileName("");
      setSelectedFile(null);
      return;
    }
    
    console.log("File selected:", file.name);
    setFileName(file.name);
    setSelectedFile(file);
    
    // Get accurate row count by counting newlines
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      if (text) {
        const lines = text.split('\n').length;
        const estimatedRowCount = lines > 1 ? lines - 1 : 0; // Subtract header row
        
        setRowCount(estimatedRowCount);
      }
    };
    
    // Only read the first 5MB to estimate row count for very large files
    const chunkSize = Math.min(file.size, 5 * 1024 * 1024);
    reader.readAsText(file.slice(0, chunkSize));
  };

  const handleBrowseClick = () => {
    console.log("Browse button clicked");
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return {
    fileName,
    error,
    rowCount,
    fileInputRef,
    selectedFile,
    fieldMapping,
    setError,
    setFileName,
    setRowCount,
    setSelectedFile,
    setFieldMapping,
    handleChange,
    handleBrowseClick
  };
}
