
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { FileUp, Database } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface BreweryImportHeaderProps {
  totalItems: number;
}

const BreweryImportHeader: React.FC<BreweryImportHeaderProps> = ({ totalItems }) => {
  return (
    <CardHeader className="bg-muted/50">
      <CardTitle className="flex items-center gap-2">
        <FileUp className="h-5 w-5" />
        Import Brewery Data
      </CardTitle>
      <CardDescription>
        Upload your brewery data in CSV format. Our smart import system will map common field variations.
        {totalItems > 0 && (
          <div className="mt-2 flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span>Currently loaded: <Badge variant="outline">{totalItems} breweries</Badge></span>
          </div>
        )}
      </CardDescription>
    </CardHeader>
  );
};

export default BreweryImportHeader;
