
import React from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Brewery } from "@/types/brewery";
import { useImageUpload } from "@/hooks/useImageUpload";
import BreweryEditorHeader from "./BreweryEditorHeader";
import BreweryEditorFields from "./BreweryEditorFields";
import BreweryEditorActions from "./BreweryEditorActions";

type BreweryEditorProps = {
  isOpen: boolean;
  onClose: () => void;
  editingBrewery: Brewery | null;
  editedValues: Record<string, any>;
  columns: string[];
  onSave: () => void;
  onInputChange: (key: string, value: any) => void;
  fieldTypes?: Record<string, string>;
};

const BreweryEditor: React.FC<BreweryEditorProps> = ({
  isOpen,
  onClose,
  editingBrewery,
  editedValues,
  columns,
  onSave,
  onInputChange,
  fieldTypes = {}
}) => {
  const { uploadProgress, fileErrors, handleFileUpload } = useImageUpload();

  if (!editingBrewery) return null;

  const handleFileUploadWrapper = (field: string, file: File) => {
    handleFileUpload(field, file, onInputChange);
  };

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent side="right" className="overflow-y-auto w-full max-w-md sm:max-w-lg">
        <BreweryEditorHeader />
        
        <BreweryEditorFields
          columns={columns}
          editedValues={editedValues}
          onInputChange={onInputChange}
          uploadProgress={uploadProgress}
          fileErrors={fileErrors}
          onFileUpload={handleFileUploadWrapper}
          fieldTypes={fieldTypes}
        />
        
        <BreweryEditorActions 
          onClose={onClose} 
          onSave={onSave} 
        />
      </SheetContent>
    </Sheet>
  );
};

export default BreweryEditor;
