
import * as z from 'zod';

export const breweryFormSchema = z.object({
  name: z.string().min(1, { message: 'Brewery name is required' }),
  description: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  email: z.string().email().optional(),
  logo: z.string().optional(),
  featureImage: z.string().optional(),
  brewery_type: z.string().optional(), // Added brewery_type field
});

export type BreweryFormValues = z.infer<typeof breweryFormSchema>;
