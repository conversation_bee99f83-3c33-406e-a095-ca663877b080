
import React from "react";
import { Brewery } from "@/types/brewery.patch";
import BreweryTable from "./BreweryTable";
import { Skeleton } from "@/components/ui/skeleton";
import { Building, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface BreweryDataContentProps {
  breweries: Brewery[];
  isLoading?: boolean;
  displayColumns?: string[];
  fieldTypes?: Record<string, string>;
}

const BreweryDataContent: React.FC<BreweryDataContentProps> = ({
  breweries,
  isLoading = false,
  displayColumns = ['name', 'brewery_type', 'city', 'state', 'phone', 'website', 'email'],
  fieldTypes = {}
}) => {
  const navigate = useNavigate();
  
  // Empty state handling
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }

  if (breweries.length === 0) {
    return (
      <div className="text-center py-10 border rounded-lg bg-slate-50 dark:bg-slate-900">
        <Building className="mx-auto h-12 w-12 text-blue-500 mb-4" />
        <h3 className="text-lg font-medium">No Breweries Found</h3>
        <p className="text-muted-foreground mt-2 mb-4">
          There are no breweries matching your search criteria.
        </p>
        <Button onClick={() => navigate('/import')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Import Brewery Data
        </Button>
      </div>
    );
  }

  return (
    <div className="border rounded-md overflow-hidden">
      <BreweryTable 
        data={breweries}
        displayColumns={displayColumns}
        fieldTypes={fieldTypes}
      />
    </div>
  );
};

export default BreweryDataContent;
