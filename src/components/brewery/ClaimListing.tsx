
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { UserPlus, Check, ShieldCheck, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/auth';
import { useNavigate } from 'react-router-dom';

interface ClaimListingProps {
  breweryId: string;
  breweryName?: string;
  isVerificationOpen?: boolean;
  isClaimed?: boolean;
  isVerified?: boolean;
}

const ClaimListing: React.FC<ClaimListingProps> = ({
  breweryId,
  breweryName = 'this brewery',
  isVerificationOpen = false,
  isClaimed = false,
  isVerified = false
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleClaimClick = () => {
    if (user) {
      // If user is logged in, directly associate with brewery
      console.log(`Claiming brewery ID: ${breweryId}`);
      
      // In a real implementation, this would make a backend call
      // to associate the user with the brewery
    } else {
      // If not logged in, redirect to auth page with brewery ID as parameter
      navigate(`/auth?register=true&breweryId=${breweryId}`);
    }
  };

  // If already claimed and verified, show verified status
  if (isClaimed && isVerified) {
    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">Brewery Listing</CardTitle>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <ShieldCheck className="mr-1 h-3 w-3" /> Verified
            </Badge>
          </div>
          <CardDescription>This listing has been claimed and verified</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  // If already claimed but not verified
  if (isClaimed && !isVerified) {
    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">Brewery Listing</CardTitle>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              <Check className="mr-1 h-3 w-3" /> Claimed
            </Badge>
          </div>
          <CardDescription>This listing has been claimed and is awaiting verification</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">Brewery Listing</CardTitle>
          {isVerificationOpen && (
            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
              <AlertTriangle className="mr-1 h-3 w-3" /> Unclaimed
            </Badge>
          )}
        </div>
        <CardDescription>
          {isVerificationOpen 
            ? `Claim ${breweryName} to manage your profile, menus, and more` 
            : `This listing is not currently available for claiming`}
        </CardDescription>
      </CardHeader>
      
      {isVerificationOpen && (
        <CardFooter className="pt-2">
          <Button 
            onClick={handleClaimClick} 
            className="w-full"
          >
            <UserPlus className="mr-2 h-4 w-4" />
            Claim This Listing
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default ClaimListing;
