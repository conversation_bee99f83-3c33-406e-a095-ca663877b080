
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export interface ImportActionsProps {
  onDeleteAll: () => Promise<void> | void;
  onBack: () => void;
  isDeleteLoading: boolean;
  breweryCount: number;
}

const ImportActions: React.FC<ImportActionsProps> = ({
  onDeleteAll,
  onBack,
  isDeleteLoading,
  breweryCount = 0
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-3 mt-6">
      <Button
        variant="outline"
        size="sm"
        onClick={onBack}
        className="flex items-center gap-1"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Dashboard
      </Button>

      {breweryCount > 0 && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="destructive"
              size="sm"
              className="flex items-center gap-1"
              disabled={isDeleteLoading}
            >
              <Trash2 className="h-4 w-4" />
              {isDeleteLoading ? "Deleting..." : "Delete All Data"}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete all{" "}
                <strong>{breweryCount}</strong> breweries from your database.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={onDeleteAll}>
                Yes, delete all data
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};

export default ImportActions;
