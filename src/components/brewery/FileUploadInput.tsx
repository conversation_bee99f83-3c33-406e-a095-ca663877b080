
import React, { forwardRef, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface FileUploadInputProps {
  fileName: string;
  rowCount: number | null;
  isLoading: boolean;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBrowseClick: () => void;
  autoUpload?: boolean;
  onImportClick?: () => void;
}

const FileUploadInput = forwardRef<HTMLInputElement, FileUploadInputProps>(({
  fileName,
  rowCount,
  isLoading,
  onFileChange,
  onBrowseClick,
  autoUpload = false,
  onImportClick,
}, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // File input change handler that just passes the event through
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log("FileUploadInput handleFileChange called", event.target.files);
    if (event.target.files?.length) {
      // Pass the event to the parent component
      onFileChange(event);
    }
  };
  
  // This ensures the hidden input gets proper ref handling
  const setRefs = (el: HTMLInputElement | null) => {
    // Apply the forwarded ref
    if (typeof ref === 'function') {
      ref(el);
    } else if (ref) {
      ref.current = el;
    }
    
    // Also set our local ref
    inputRef.current = el;
  };
  
  const handleDivClick = () => {
    console.log("Div clicked, triggering file input click");
    if (!isLoading && inputRef.current) {
      inputRef.current.click();
    }
  };
  
  return (
    <div className="flex flex-col space-y-2">
      <div className="flex items-center justify-between">
        <label htmlFor="csv-upload" className="text-sm font-medium">
          Select CSV File
        </label>
        {fileName && (
          <span className="text-sm text-muted-foreground">
            Selected: <span className="font-medium text-foreground">{fileName}</span>
            {rowCount !== null && <Badge variant="outline" className="ml-2">{rowCount} rows</Badge>}
          </span>
        )}
      </div>
      
      <div className="grid gap-4">
        <div className="border rounded-md p-2 bg-background">
          <Input 
            id="csv-upload"
            type="file" 
            accept=".csv" 
            onChange={handleFileChange}
            disabled={isLoading}
            className="sr-only" // Visually hidden but still functional
            ref={setRefs}
          />
          
          <div 
            className={`border-2 border-dashed rounded-md p-8 text-center transition-colors cursor-pointer ${
              isLoading ? 'border-muted-foreground/20 cursor-not-allowed' : 'border-muted-foreground/25 hover:border-muted-foreground/50'
            }`}
            onClick={handleDivClick}
          >
            <Upload className={`mx-auto h-10 w-10 mb-2 ${isLoading ? 'text-muted-foreground/40' : 'text-muted-foreground'}`} />
            <p className="text-sm font-medium">
              {fileName ? fileName : "Drop your CSV file here or click to browse"}
            </p>
            {rowCount !== null && (
              <p className="text-xs text-muted-foreground mt-1">
                {rowCount} rows detected
              </p>
            )}
          </div>
        </div>
        
        <div className="flex justify-between items-center gap-2">
          <Button 
            onClick={handleDivClick}
            disabled={isLoading}
            variant="outline"
            className="w-full bg-background hover:bg-muted"
            type="button"
          >
            <Upload className="mr-2 h-4 w-4" />
            {fileName ? "Change File" : "Browse Files"}
          </Button>
          
          {fileName && onImportClick && (
            <Button 
              onClick={onImportClick}
              disabled={isLoading || !fileName}
              className="w-full"
              type="button"
            >
              {isLoading ? "Importing..." : "Import Data"}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
});

FileUploadInput.displayName = "FileUploadInput";

export default FileUploadInput;
