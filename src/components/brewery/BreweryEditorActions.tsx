
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, X as XIcon } from "lucide-react";

interface BreweryEditorActionsProps {
  onClose: () => void;
  onSave: () => void;
}

const BreweryEditorActions: React.FC<BreweryEditorActionsProps> = ({
  onClose,
  onSave,
}) => {
  return (
    <div className="flex justify-end space-x-2 mt-4 pb-8 z-50 bg-background">
      <Button variant="outline" onClick={onClose}>
        <XIcon className="mr-2 h-4 w-4" />
        Cancel
      </Button>
      <Button onClick={onSave} className="relative z-50">
        <Check className="mr-2 h-4 w-4" />
        Save Changes
      </Button>
    </div>
  );
};

export default BreweryEditorActions;
