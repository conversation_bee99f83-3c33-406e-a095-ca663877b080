
import React from "react";
import BreweryEditorField from "./BreweryEditorField";
import { getFieldType } from "./utils/fieldTypeUtils";

interface BreweryEditorFieldsProps {
  columns: string[];
  editedValues: Record<string, any>;
  onInputChange: (key: string, value: any) => void;
  uploadProgress: Record<string, number>;
  fileErrors: Record<string, string>;
  onFileUpload: (field: string, file: File) => void;
  fieldTypes?: Record<string, string>;
}

const BreweryEditorFields: React.FC<BreweryEditorFieldsProps> = ({
  columns,
  editedValues,
  onInputChange,
  uploadProgress,
  fileErrors,
  onFileUpload,
  fieldTypes = {}
}) => {
  return (
    <div className="grid gap-4 py-4">
      {columns.map(column => (
        <BreweryEditorField
          key={column}
          column={column}
          value={editedValues[column]}
          onInputChange={onInputChange}
          uploadProgress={uploadProgress}
          fileErrors={fileErrors}
          onFileUpload={onFileUpload}
          fieldType={getFieldType(column, fieldTypes)}
        />
      ))}
    </div>
  );
};

export default BreweryEditorFields;
