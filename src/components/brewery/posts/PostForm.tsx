
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ImageIcon, Loader2, X } from 'lucide-react';
import { PostCreateRequest } from '@/types/brewery';

interface PostFormProps {
  breweryId: string;
  onSubmit: (data: PostCreateRequest, image?: File) => Promise<void>;
}

const PostForm: React.FC<PostFormProps> = ({ breweryId, onSubmit }) => {
  const [content, setContent] = useState('');
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      // Create a preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  const removeImage = () => {
    setImage(null);
    setImagePreview(null);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() && !image) return;
    
    setIsSubmitting(true);
    try {
      const postData: PostCreateRequest = {
        content: content.trim(),
        brewery_id: breweryId
      };
      
      await onSubmit(postData, image || undefined);
      
      // Reset form
      setContent('');
      setImage(null);
      setImagePreview(null);
    } catch (error) {
      console.error('Error creating post:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Textarea
          placeholder="What's happening at your brewery?"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="min-h-24 resize-none"
          disabled={isSubmitting}
        />
      </div>
      
      {imagePreview && (
        <div className="relative rounded-md overflow-hidden">
          <img 
            src={imagePreview} 
            alt="Preview" 
            className="w-full max-h-60 object-cover"
          />
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={removeImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      <div className="flex justify-between items-center">
        <div>
          <Label htmlFor="post-image" className="cursor-pointer">
            <div className="flex items-center gap-1 text-muted-foreground hover:text-foreground transition-colors">
              <ImageIcon className="h-5 w-5" />
              <span>Add Image</span>
            </div>
            <input
              type="file"
              id="post-image"
              accept="image/*"
              onChange={handleImageChange}
              className="sr-only"
              disabled={isSubmitting}
            />
          </Label>
        </div>
        
        <Button type="submit" disabled={isSubmitting || (!content.trim() && !image)}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Posting...
            </>
          ) : (
            "Post"
          )}
        </Button>
      </div>
    </form>
  );
};

export default PostForm;
