
import React, { useState } from 'react';
import { Post } from '@/types/brewery';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { MessageSquare, ThumbsUp, Trash2 } from 'lucide-react';
import { useAuth } from '@/auth';

interface PostItemProps {
  post: Post;
  onDelete: () => void;
}

const PostItem: React.FC<PostItemProps> = ({ post, onDelete }) => {
  const { user } = useAuth();
  const [isDeleting, setIsDeleting] = useState(false);
  const [liked, setLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likes || 0);
  
  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      setIsDeleting(true);
      try {
        await onDelete();
      } catch (error) {
        console.error('Error deleting post:', error);
        setIsDeleting(false);
      }
    }
  };

  const handleLike = () => {
    if (!liked) {
      setLiked(true);
      setLikeCount(prev => prev + 1);
    } else {
      setLiked(false);
      setLikeCount(prev => prev - 1);
    }
  };

  const canDelete = user?.breweryId === post.brewery_id || user?.role === 'admin';
  const postDate = post.created_at ? new Date(post.created_at) : new Date();
  
  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3 pt-4 px-4">
        <div className="flex items-start gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={post.brewery_logo || ''} alt={post.brewery_name || 'Brewery'} />
            <AvatarFallback>{post.brewery_name?.substring(0, 2) || 'BR'}</AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <h3 className="font-semibold leading-none">{post.brewery_name}</h3>
            <p className="text-xs text-muted-foreground">
              {formatDistanceToNow(postDate, { addSuffix: true })}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-4 py-2">
        <p className="whitespace-pre-wrap">{post.content}</p>
        
        {post.image_url && (
          <div className="mt-3 rounded-md overflow-hidden">
            <img 
              src={post.image_url} 
              alt="Post attachment" 
              className="w-full object-cover max-h-96"
            />
          </div>
        )}
      </CardContent>
      <CardFooter className="px-4 pb-4 pt-1 flex justify-between">
        <div className="flex space-x-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className={`flex gap-1 items-center ${liked ? 'text-blue-500' : ''}`}
            onClick={handleLike}
          >
            <ThumbsUp className="h-4 w-4" />
            <span>{likeCount > 0 ? likeCount : ''}</span>
          </Button>
          <Button variant="ghost" size="sm" className="flex gap-1 items-center">
            <MessageSquare className="h-4 w-4" />
            <span>{post.comments_count || ''}</span>
          </Button>
        </div>
        
        {canDelete && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <Trash2 className="h-4 w-4 text-destructive" />
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default PostItem;
