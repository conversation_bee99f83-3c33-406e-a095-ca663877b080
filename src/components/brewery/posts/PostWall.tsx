
import React from 'react';
import { useBreweryPosts } from './useBreweryPosts';
import PostItem from './PostItem';
import PostForm from './PostForm';
import { Skeleton } from '@/components/ui/skeleton';

const PostWall: React.FC<{ breweryId: string }> = ({ breweryId }) => {
  const { 
    posts, 
    isLoading, 
    isSubmitting, 
    createPost, 
    likePost, 
    refreshPosts 
  } = useBreweryPosts(breweryId);

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Updates and News</h2>
      <PostForm breweryId={breweryId} onSubmit={createPost} />
      
      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-4 border rounded-lg space-y-3">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </div>
      ) : posts.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">No posts yet. Be the first to post!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {posts.map(post => (
            <PostItem 
              key={post.id} 
              post={post} 
              onDelete={() => refreshPosts()}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PostWall;
