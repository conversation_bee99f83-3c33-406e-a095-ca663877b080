
import { useState, useEffect, useCallback } from 'react';
import { Post, PostCreateRequest } from '@/types/brewery';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/auth';

export const useBreweryPosts = (breweryId?: string) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const fetchPosts = useCallback(async () => {
    if (!breweryId) return;
    
    setIsLoading(true);
    try {
      // Since we don't have the brewery_posts table yet, this is a temporary stub
      // This will be updated once we run the SQL migration to create the table
      console.log('Fetching posts for brewery:', breweryId);
      setPosts([]);
      
    } catch (error: any) {
      console.error('Error fetching posts:', error);
      toast({
        title: "Error loading posts",
        description: error.message || "Failed to load brewery posts",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [breweryId]);

  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  const createPost = async (postData: PostCreateRequest) => {
    if (!user || !user.id) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to create a post",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Since we don't have the brewery_posts table yet, this is a temporary stub
      // This will be updated once we run the SQL migration to create the table
      console.log('Creating post:', postData);
      toast({
        title: "Post created",
        description: "Your post has been published successfully",
      });
      
      // Refetch posts to get the newly created one
      fetchPosts();
    } catch (error: any) {
      console.error('Error creating post:', error);
      toast({
        title: "Error creating post",
        description: error.message || "Failed to create post",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const likePost = async (postId: string) => {
    if (!user || !user.id) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to like a post",
        variant: "destructive",
      });
      return;
    }

    try {
      // Since we don't have the brewery_posts table yet, this is a temporary stub
      // This will be updated once we run the SQL migration to create the table
      console.log('Liking post:', postId);
      
      // Update local state to show immediate feedback
      setPosts(prevPosts => 
        prevPosts.map(post => 
          post.id === postId 
            ? { ...post, likes: post.likes + 1 } 
            : post
        )
      );
    } catch (error: any) {
      console.error('Error liking post:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to like post",
        variant: "destructive",
      });
    }
  };

  return {
    posts,
    isLoading,
    isSubmitting,
    createPost,
    likePost,
    refreshPosts: fetchPosts
  };
};
