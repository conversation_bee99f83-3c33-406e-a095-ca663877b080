
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export interface SampleDataImportProps {
  onImport: () => Promise<void>;
  isLoading: boolean;
}

const SampleDataImport: React.FC<SampleDataImportProps> = ({ onImport, isLoading }) => {
  const sampleFeatures = [
    { title: "30 Breweries", description: "From across the United States" },
    { title: "Complete Data", description: "Addresses, contact info, social media" },
    { title: "Images", description: "Logos and feature images" },
    { title: "Varied Types", description: "Brewpubs, microbreweries, and large operations" }
  ];
  
  return (
    <Card className="shadow-lg">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl">Load Sample Data</CardTitle>
        <CardDescription>
          Get started quickly with our curated sample brewery data
        </CardDescription>
      </CardHeader>
      
      <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {sampleFeatures.map(feature => (
          <div key={feature.title} className="flex items-start gap-2">
            <div className="rounded-full bg-primary/10 p-1">
              <Download className="h-4 w-4 text-primary" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{feature.title}</h4>
              <p className="text-xs text-muted-foreground">{feature.description}</p>
            </div>
          </div>
        ))}
      </CardContent>
      
      <CardFooter>
        <Button 
          className="w-full" 
          onClick={onImport}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              Import Sample Data
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SampleDataImport;
