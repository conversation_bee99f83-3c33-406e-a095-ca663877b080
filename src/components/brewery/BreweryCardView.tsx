
import React from 'react';
import { Brewery } from '@/types/brewery.patch';
import BreweryCardItem from './BreweryCardItem';

interface BreweryCardViewProps {
  breweries: Brewery[];
  onEdit: (breweryId: string) => void;
  onDelete: (brewery: Brewery) => void;
  renderVerificationBadge: (brewery: Brewery) => React.ReactNode;
}

const BreweryCardView: React.FC<BreweryCardViewProps> = ({
  breweries,
  onEdit,
  onDelete,
  renderVerificationBadge
}) => {
  if (breweries.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">No breweries found matching your filters.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {breweries.map(brewery => (
        <BreweryCardItem 
          key={brewery.id}
          brewery={brewery}
          onEdit={onEdit}
          onDelete={onDelete}
          renderVerificationBadge={renderVerificationBadge}
        />
      ))}
    </div>
  );
};

export default BreweryCardView;
