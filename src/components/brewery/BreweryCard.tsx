
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Brewery } from '@/types/brewery.patch';
import { MapPin, Phone, Globe, Info, Check, ShieldCheck, AlertTriangle, Heart, Users } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/auth';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface BreweryCardProps {
  brewery: Brewery;
  onViewDetails?: (brewery: Brewery) => void;
  showVerificationStatus?: boolean;
}

const BreweryCard: React.FC<BreweryCardProps> = ({ 
  brewery, 
  onViewDetails,
  showVerificationStatus = true
}) => {
  const { user } = useAuth();
  
  // Get brewery initials for avatar fallback
  const getInitials = () => {
    if (!brewery.name) return 'B';
    return brewery.name.substring(0, 2).toUpperCase();
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="relative pb-2">
        <div className="flex items-center justify-center">
          <Avatar className="w-16 h-16 mb-2">
            <AvatarImage src={brewery.avatar || brewery.logo} alt={brewery.name} />
            <AvatarFallback>{getInitials()}</AvatarFallback>
          </Avatar>
        </div>
        
        <CardTitle className="text-xl text-center">{brewery.name || 'Unnamed Brewery'}</CardTitle>
        
        {showVerificationStatus && (
          <>
            {brewery.verificationOpen && !brewery.claimed && (
              <div className="absolute top-2 right-2">
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-300">
                  <AlertTriangle className="mr-1 h-3 w-3" /> Unclaimed
                </Badge>
              </div>
            )}
            
            {brewery.claimed && brewery.verified && (
              <div className="absolute top-2 right-2">
                <Badge className="bg-green-500">
                  <ShieldCheck className="mr-1 h-3 w-3" /> Verified
                </Badge>
              </div>
            )}
            
            {brewery.claimed && !brewery.verified && (
              <div className="absolute top-2 right-2">
                <Badge className="bg-blue-500">
                  <Check className="mr-1 h-3 w-3" /> Claimed
                </Badge>
              </div>
            )}
          </>
        )}

        <div className="flex justify-center gap-4 mt-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <Users className="h-3 w-3 mr-1" />
            <span>{brewery.followerCount || 0}</span>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Heart className="h-3 w-3 mr-1" />
            <span>{brewery.likeCount || 0}</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-grow pb-2">
        {brewery.address && (
          <div className="flex items-start mb-2">
            <MapPin className="h-4 w-4 mr-2 mt-1 flex-shrink-0 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {brewery.address}, {brewery.city}, {brewery.state} {brewery.zip}
            </p>
          </div>
        )}
        
        {brewery.phone && (
          <div className="flex items-center mb-2">
            <Phone className="h-4 w-4 mr-2 flex-shrink-0 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{brewery.phone}</p>
          </div>
        )}
        
        {brewery.website && (
          <div className="flex items-center mb-2">
            <Globe className="h-4 w-4 mr-2 flex-shrink-0 text-muted-foreground" />
            <a 
              href={brewery.website.startsWith('http') ? brewery.website : `https://${brewery.website}`} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:underline truncate"
            >
              {brewery.website}
            </a>
          </div>
        )}
        
        {brewery.description && (
          <div className="mt-3">
            <p className="text-sm line-clamp-3">{brewery.description}</p>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-2 flex justify-between">
        {onViewDetails ? (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onViewDetails(brewery)}
            className="w-full"
          >
            <Info className="h-4 w-4 mr-2" /> View Details
          </Button>
        ) : (
          <Button 
            variant="outline" 
            size="sm" 
            asChild
            className="w-full"
          >
            <Link to={`/brewery/${brewery.id}`}>
              <Info className="h-4 w-4 mr-2" /> View Details
            </Link>
          </Button>
        )}
        
        {brewery.claimable && brewery.verificationOpen && !brewery.claimed && (
          <Button 
            variant="default" 
            size="sm" 
            asChild
            className="ml-2 w-full"
          >
            <Link to={`/auth?register=true&breweryId=${brewery.id}`}>
              Claim Listing
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default BreweryCard;
