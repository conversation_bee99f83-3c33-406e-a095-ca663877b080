
import React from "react";
import { Database, AlertTriangle, Trash, RefreshCw, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface BreweryDataHeaderProps {
  hasData?: boolean;
  onDeleteAll?: () => void;
  onRefresh?: () => void;
  // Search and filter props
  searchTerm?: string;
  setSearchTerm?: (term: string) => void;
  filterColumn?: string;
  setFilterColumn?: (column: string) => void;
  filterValue?: string;
  setFilterValue?: (value: string) => void;
  onApplyFilter?: () => void;
  isLoading?: boolean;
  filterOptions?: string[];
  stateFilter?: string;
  setStateFilter?: (state: string) => void;
  cityFilter?: string;
  setCityFilter?: (city: string) => void;
  availableCities?: string[];
  availableStates?: string[];
  verificationFilter?: string;
  setVerificationFilter?: (filter: string) => void;
}

const BreweryDataHeader: React.FC<BreweryDataHeaderProps> = ({
  hasData = false,
  onDeleteAll,
  onRefresh,
  searchTerm = '',
  setSearchTerm,
  filterColumn = 'all',
  setFilterColumn,
  filterValue = '',
  setFilterValue,
  onApplyFilter,
  isLoading = false,
  filterOptions = [],
  stateFilter = 'all',
  setStateFilter,
  cityFilter = 'all',
  setCityFilter,
  availableCities = [],
  availableStates = [],
  verificationFilter = 'all',
  setVerificationFilter
}) => {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (setSearchTerm) {
      setSearchTerm(e.target.value);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Database className="h-5 w-5" />
            Imported Brewery Data
          </h2>
          <p className="text-muted-foreground">
            View, edit and manage imported brewery data
          </p>
        </div>
        
        <div className="flex gap-2">
          {onRefresh && (
            <Button 
              variant="outline" 
              size="sm"
              onClick={onRefresh}
              className="flex items-center gap-1"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          )}
          
          {hasData && onDeleteAll && (
            <Button 
              variant="destructive" 
              size="sm"
              onClick={onDeleteAll}
              className="flex items-center gap-1"
              disabled={isLoading}
            >
              <Trash className="h-4 w-4" />
              Delete All
            </Button>
          )}
        </div>
      </div>
      
      {/* Search and filter controls */}
      {setSearchTerm && (
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search breweries..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-8"
            />
          </div>
          
          {setFilterColumn && (
            <div className="w-full sm:w-48">
              <Select value={filterColumn} onValueChange={setFilterColumn}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Fields</SelectItem>
                  {filterOptions.map(option => (
                    <SelectItem key={option} value={option}>
                      {option.charAt(0).toUpperCase() + option.slice(1).replace(/_/g, ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {setStateFilter && (
            <div className="w-full sm:w-48">
              <Select value={stateFilter} onValueChange={setStateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="State" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All States</SelectItem>
                  {availableStates && availableStates.map(state => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {setCityFilter && (
            <div className="w-full sm:w-48">
              <Select value={cityFilter} onValueChange={setCityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="City" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cities</SelectItem>
                  {availableCities && availableCities.map(city => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {setFilterValue && onApplyFilter && (
            <>
              <Input
                placeholder="Filter value..."
                value={filterValue}
                onChange={(e) => setFilterValue(e.target.value)}
                className="w-full sm:w-48"
              />
              <Button 
                onClick={onApplyFilter}
                disabled={isLoading}
                size="sm"
              >
                Apply Filter
              </Button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default BreweryDataHeader;
