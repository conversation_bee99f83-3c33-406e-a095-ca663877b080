
import React from "react";
import { AlertTriangle, AlertCircle, Info } from "lucide-react";

interface ImportErrorProps {
  error: string;
  fieldMapping?: Record<string, string>;
}

const ImportError: React.FC<ImportErrorProps> = ({ error, fieldMapping }) => {
  if (!error) return null;
  
  // Determine if we have field mapping information to display
  const hasFieldMapping = fieldMapping && Object.keys(fieldMapping).length > 0;
  
  return (
    <div className="p-4 border border-destructive bg-destructive/10 dark:bg-destructive/20 rounded-md flex items-start gap-3 mb-4 animate-in fade-in">
      <AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
      <div>
        <h4 className="font-medium text-destructive mb-1">Import Error</h4>
        <p className="text-sm text-destructive dark:text-destructive/90">{error}</p>
        
        {error.includes("CSV") && (
          <p className="text-xs mt-2 text-destructive/80 dark:text-destructive/70 flex items-center">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Please check your CSV format and try again
          </p>
        )}
        
        {hasFieldMapping && (
          <div className="mt-3 p-2 border border-destructive/30 rounded bg-background/80 dark:bg-background/30">
            <div className="flex items-center mb-1">
              <Info className="h-3 w-3 mr-1 text-destructive/70" />
              <p className="text-xs font-medium text-destructive/80">Field mapping attempted:</p>
            </div>
            <div className="grid gap-1 text-xs">
              {Object.entries(fieldMapping).map(([original, mapped]) => (
                <div key={original} className="flex justify-between">
                  <span className="opacity-80">"{original}"</span>
                  <span className="mx-2">→</span>
                  <span className="font-medium">{mapped === original ? '(unchanged)' : `"${mapped}"`}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportError;
