
import React from 'react';
import { Brewery } from '@/types/brewery.patch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatLabel } from './utils/labelFormatters';
import { getFieldType } from './utils/fieldTypeUtils';

export interface BreweryTableProps {
  data: Brewery[];
  displayColumns?: string[];
  fieldTypes?: Record<string, string>;
  onRowClick?: (brewery: Brewery) => void;
}

const BreweryTable: React.FC<BreweryTableProps> = ({
  data,
  displayColumns = ['name', 'brewery_type', 'city', 'state', 'phone', 'website', 'email'],
  fieldTypes = {},
  onRowClick
}) => {
  // Render cell value based on field type
  const renderCellValue = (brewery: Brewery, column: string) => {
    // Ensure we're accessing the correct property from the brewery object
    const value = brewery[column as keyof Brewery];
    const fieldType = getFieldType(column, fieldTypes);
    
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">—</span>;
    }
    
    switch (fieldType) {
      case 'boolean':
        return value ? 'Yes' : 'No';
      case 'url':
        return (
          <a href={String(value)} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
            {String(value)}
          </a>
        );
      case 'email':
        return (
          <a href={`mailto:${String(value)}`} className="text-blue-500 hover:underline">
            {String(value)}
          </a>
        );
      case 'tel':
        return (
          <a href={`tel:${String(value)}`} className="text-blue-500 hover:underline">
            {String(value)}
          </a>
        );
      default:
        return String(value);
    }
  };
  
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {displayColumns.map((column) => (
            <TableHead key={column}>
              {formatLabel(column)}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.length === 0 ? (
          <TableRow>
            <TableCell colSpan={displayColumns.length} className="text-center py-4">
              No breweries found
            </TableCell>
          </TableRow>
        ) : (
          data.map((brewery) => (
            <TableRow 
              key={brewery.id}
              onClick={() => onRowClick && onRowClick(brewery)}
              className={onRowClick ? "cursor-pointer hover:bg-muted" : ""}
            >
              {displayColumns.map((column) => (
                <TableCell key={`${brewery.id}-${column}`}>
                  {renderCellValue(brewery, column)}
                </TableCell>
              ))}
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

export default BreweryTable;
