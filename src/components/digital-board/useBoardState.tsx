
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { DigitalBoardSettings, DigitalBoard } from '@/types/digitalBoard';
import { useToast } from '@/hooks/use-toast';

export const useBoardState = (breweryId?: string) => {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [boardEnabled, setBoardEnabled] = useState(true);
  const [existingBoard, setExistingBoard] = useState<DigitalBoard | null>(null);
  const [boardSettings, setBoardSettings] = useState<DigitalBoardSettings>({
    autoRotate: true,
    displayTime: 15, // seconds per item
    showPrices: true,
    showDescription: true,
    showImages: true,
    enableSlideshow: false,
    slideshowInterval: 10 // seconds
  });

  // Load existing board settings
  useEffect(() => {
    if (!breweryId) return;
    
    const fetchBoardData = async () => {
      try {
        const { data, error } = await supabase
          .from('digital_boards')
          .select('*')
          .eq('brewery_id', breweryId)
          .single();
        
        if (error) {
          if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            console.error("Error fetching board:", error);
          }
          return;
        }
        
        if (data) {
          // Convert from db format to our application type
          const boardData: DigitalBoard = {
            ...data,
            settings: data.settings as unknown as DigitalBoardSettings
          };
          
          setExistingBoard(boardData);
          setBoardSettings(boardData.settings);
          setBoardEnabled(boardData.is_active);
          setShowQRCode(true);
        }
      } catch (error) {
        console.error("Error fetching digital board data:", error);
      }
    };
    
    fetchBoardData();
  }, [breweryId]);

  // Generate a unique link for the digital board
  const generateBoardLink = async () => {
    if (!breweryId) {
      toast({
        title: "Error",
        description: "Brewery ID is required to generate a board link",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      // Generate a unique token for the board
      const token = Math.random().toString(36).substring(2, 15) + 
                   Math.random().toString(36).substring(2, 15);
      
      if (existingBoard) {
        // Update existing board
        const { error } = await supabase
          .from('digital_boards')
          .update({ 
            settings: boardSettings as any,
            is_active: boardEnabled
          })
          .eq('id', existingBoard.id);
        
        if (error) throw error;
      } else {
        // Create a new board
        const { data, error } = await supabase
          .from('digital_boards')
          .insert({
            brewery_id: breweryId,
            board_id: token,
            settings: boardSettings as any,
            is_active: boardEnabled
          })
          .select()
          .single();
        
        if (error) throw error;
        
        // Convert from db format to our application type
        const boardData: DigitalBoard = {
          ...data,
          settings: data.settings as unknown as DigitalBoardSettings
        };
        
        setExistingBoard(boardData);
      }
      
      setShowQRCode(true);
      toast({
        title: "Success",
        description: "Digital board link generated successfully"
      });
      
    } catch (error) {
      console.error("Error generating board link:", error);
      toast({
        title: "Error",
        description: "Failed to generate board link",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getBoardUrl = () => {
    const baseUrl = window.location.origin;
    const boardId = existingBoard?.board_id || '';
    return `${baseUrl}/digital-board/${breweryId}/${boardId}`;
  };

  const saveBoardSettings = async () => {
    if (!breweryId || !existingBoard) return;
    
    try {
      const { error } = await supabase
        .from('digital_boards')
        .update({ 
          settings: boardSettings as any,
          is_active: boardEnabled
        })
        .eq('id', existingBoard.id);
      
      if (error) throw error;
      
      toast({
        title: "Settings Saved",
        description: "Digital board settings updated successfully"
      });
    } catch (error) {
      console.error("Error saving board settings:", error);
      toast({
        title: "Error",
        description: "Failed to save board settings",
        variant: "destructive"
      });
    }
  };

  return {
    isGenerating,
    showQRCode,
    boardEnabled,
    setBoardEnabled,
    existingBoard,
    boardSettings,
    setBoardSettings,
    generateBoardLink,
    getBoardUrl,
    saveBoardSettings
  };
};
