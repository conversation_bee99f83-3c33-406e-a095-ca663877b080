
import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Co<PERSON>, Eye } from "lucide-react";
import { DigitalBoard } from "@/types/digitalBoard";
import { useToast } from "@/hooks/use-toast";

interface BoardSetupTabProps {
  breweryId?: string;
  boardEnabled: boolean;
  setBoardEnabled: (enabled: boolean) => void;
  existingBoard: DigitalBoard | null;
  isGenerating: boolean;
  generateBoardLink: () => Promise<void>;
  showQRCode: boolean;
  getBoardUrl: () => string;
}

export const BoardSetupTab: React.FC<BoardSetupTabProps> = ({
  breweryId,
  boardEnabled,
  setBoardEnabled,
  existingBoard,
  isGenerating,
  generateBoardLink,
  showQRCode,
  getBoardUrl,
}) => {
  const { toast } = useToast();

  const copyLinkToClipboard = () => {
    navigator.clipboard.writeText(getBoardUrl());
    toast({
      title: "Copied!",
      description: "Link copied to clipboard"
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 p-4 border rounded-md bg-muted/50">
        <div className="flex items-center gap-2">
          <input 
            type="checkbox" 
            checked={boardEnabled} 
            onChange={(e) => setBoardEnabled(e.target.checked)} 
            id="board-active" 
            className="h-4 w-4"
          />
          <label htmlFor="board-active">
            Digital Board {boardEnabled ? 'Active' : 'Disabled'}
          </label>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            onClick={copyLinkToClipboard}
            className="flex items-center gap-2"
            disabled={!existingBoard}
          >
            <Copy className="h-4 w-4" />
            Copy Link
          </Button>
          <Button 
            onClick={generateBoardLink} 
            disabled={isGenerating || !breweryId}
            className="flex items-center gap-2"
          >
            {isGenerating ? (
              <Progress value={80} className="w-10 h-2" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
            {isGenerating ? 'Generating...' : existingBoard ? 'Update Board Link' : 'Generate Board Link'}
          </Button>
        </div>
      </div>
      
      {showQRCode && (
        <div className="mt-4 p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Your Digital Board Link</h3>
          <div className="flex flex-col md:flex-row items-center gap-4">
            <div className="bg-white p-4 rounded-md border">
              {/* QR Code placeholder - in a real app, generate this with a library */}
              <div className="w-32 h-32 bg-gray-200 flex items-center justify-center">
                QR Code
              </div>
            </div>
            <div className="flex-1">
              <p className="text-sm text-muted-foreground mb-2">
                Use this link to access your digital beer board:
              </p>
              <div className="flex items-center gap-2">
                <Input 
                  value={getBoardUrl()} 
                  readOnly 
                  className="font-mono text-sm"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={copyLinkToClipboard}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Open this link on the TV or display you want to use for your digital beer board.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-4 bg-card p-4 rounded-md border">
        <h3 className="text-lg font-medium mb-2">How to Use Your Digital Beer Board</h3>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Generate a unique board link for your brewery</li>
          <li>Open the link on any web browser on the TV or display</li>
          <li>The board will automatically update when you make changes to your menu</li>
          <li>Customize the display settings to match your brewery's style</li>
        </ol>
      </div>
    </div>
  );
};
