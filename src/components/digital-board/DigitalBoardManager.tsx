
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tv, Link, Eye, Settings } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BoardSetupTab } from './BoardSetupTab';
import { DisplaySettingsTab } from './DisplaySettingsTab';
import { useBoardState } from './useBoardState';

interface DigitalBoardManagerProps {
  breweryId?: string;
  breweryName?: string;
}

const DigitalBoardManager: React.FC<DigitalBoardManagerProps> = ({ breweryId, breweryName }) => {
  const {
    isGenerating,
    showQRCode,
    boardEnabled,
    setBoardEnabled,
    existingBoard,
    boardSettings,
    setBoardSettings,
    generateBoardLink,
    getBoardUrl,
    saveBoardSettings
  } = useBoardState(breweryId);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tv className="h-5 w-5" />
          Digital Beer Board
        </CardTitle>
        <CardDescription>
          Create a digital display of your beer menu for TVs or monitors in your brewery
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="setup">
          <TabsList className="mb-4">
            <TabsTrigger value="setup">
              <Link className="h-4 w-4 mr-2" />
              Board Setup
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              Display Settings
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="setup">
            <BoardSetupTab
              breweryId={breweryId}
              boardEnabled={boardEnabled}
              setBoardEnabled={setBoardEnabled}
              existingBoard={existingBoard}
              isGenerating={isGenerating}
              generateBoardLink={generateBoardLink}
              showQRCode={showQRCode}
              getBoardUrl={getBoardUrl}
            />
          </TabsContent>
          
          <TabsContent value="settings">
            <DisplaySettingsTab
              boardSettings={boardSettings}
              setBoardSettings={setBoardSettings}
              existingBoard={!!existingBoard}
              saveBoardSettings={saveBoardSettings}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DigitalBoardManager;
