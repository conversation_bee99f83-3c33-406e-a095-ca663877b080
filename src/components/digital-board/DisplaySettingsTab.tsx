
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DigitalBoardSettings } from "@/types/digitalBoard";
import { SettingToggle } from './SettingToggle';
import { NumberSetting } from './NumberSetting';

interface DisplaySettingsTabProps {
  boardSettings: DigitalBoardSettings;
  setBoardSettings: (settings: DigitalBoardSettings) => void;
  existingBoard: boolean;
  saveBoardSettings: () => Promise<void>;
}

export const DisplaySettingsTab: React.FC<DisplaySettingsTabProps> = ({
  boardSettings,
  setBoardSettings,
  existingBoard,
  saveBoardSettings
}) => {
  const handleSettingChange = (key: string, value: boolean | number) => {
    setBoardSettings({
      ...boardSettings,
      [key]: value
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Display Settings</CardTitle>
        <CardDescription>
          Customize how your digital beer board appears on screens
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Content Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SettingToggle
              id="show-prices"
              label="Show Prices"
              description="Display price information for each beer"
              checked={boardSettings.showPrices}
              onCheckedChange={(value) => handleSettingChange('showPrices', value)}
            />
            
            <SettingToggle
              id="show-descriptions"
              label="Show Descriptions"
              description="Display beer descriptions on the board"
              checked={boardSettings.showDescription}
              onCheckedChange={(value) => handleSettingChange('showDescription', value)}
            />
            
            <SettingToggle
              id="show-images"
              label="Show Images"
              description="Display beer thumbnail images if available"
              checked={boardSettings.showImages}
              onCheckedChange={(value) => handleSettingChange('showImages', value)}
            />
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Rotation Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <SettingToggle
              id="auto-rotate"
              label="Auto-Rotate Beers"
              description="Automatically cycle through beer menu items"
              checked={boardSettings.autoRotate}
              onCheckedChange={(value) => handleSettingChange('autoRotate', value)}
            />
            
            {boardSettings.autoRotate && (
              <NumberSetting
                id="display-time"
                label="Display Time (seconds)"
                value={boardSettings.displayTime}
                onChange={(value) => handleSettingChange('displayTime', value)}
                min={5}
                max={60}
              />
            )}
            
            <SettingToggle
              id="enable-slideshow"
              label="Enable Slideshow"
              description="Show a fullscreen slideshow when idle"
              checked={boardSettings.enableSlideshow}
              onCheckedChange={(value) => handleSettingChange('enableSlideshow', value)}
            />
            
            {boardSettings.enableSlideshow && (
              <NumberSetting
                id="slideshow-interval"
                label="Slideshow Interval (seconds)"
                value={boardSettings.slideshowInterval}
                onChange={(value) => handleSettingChange('slideshowInterval', value)}
                min={5}
                max={60}
              />
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={saveBoardSettings}
          disabled={!existingBoard}
        >
          Save Settings
        </Button>
      </CardFooter>
    </Card>
  );
};
