
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { FaGoogle, FaFacebook, FaLinkedin } from 'react-icons/fa';
import { toast } from "@/hooks/use-toast";

interface SocialLoginProps {
  onSuccess?: () => void;
}

const SocialLogin: React.FC<SocialLoginProps> = ({ onSuccess }) => {
  const handleSocialLogin = async (provider: 'google' | 'facebook' | 'linkedin_oidc') => {
    try {
      // Get the current URL to use for redirect
      const redirectTo = `${window.location.origin}/auth`;
      console.log(`Setting redirect URL to: ${redirectTo}`);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo,
        },
      });

      if (error) {
        throw error;
      }

      console.log('Social login initiated:', data);
    } catch (error: any) {
      console.error('Social login error:', error);
      toast({
        title: "Login failed",
        description: error.message || "Social login is not configured. Please use email/password login for development.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-3 mt-6">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Social login (dev mode)
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-3 gap-2 opacity-50">
        <Button 
          variant="outline" 
          type="button" 
          onClick={() => toast({
            title: "Development mode",
            description: "Social login is disabled in development mode. Please use email/password login.",
          })}
          className="w-full"
        >
          <FaGoogle className="mr-2 h-4 w-4" />
          Google
        </Button>
        
        <Button 
          variant="outline" 
          type="button" 
          onClick={() => toast({
            title: "Development mode",
            description: "Social login is disabled in development mode. Please use email/password login.",
          })}
          className="w-full"
        >
          <FaFacebook className="mr-2 h-4 w-4" />
          Facebook
        </Button>
        
        <Button 
          variant="outline" 
          type="button" 
          onClick={() => toast({
            title: "Development mode",
            description: "Social login is disabled in development mode. Please use email/password login.",
          })}
          className="w-full"
        >
          <FaLinkedin className="mr-2 h-4 w-4" />
          LinkedIn
        </Button>
      </div>
    </div>
  );
};

export default SocialLogin;
