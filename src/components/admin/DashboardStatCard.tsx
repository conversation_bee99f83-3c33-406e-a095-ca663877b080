
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { LucideIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DashboardStatCardProps {
  title: string;
  icon: LucideIcon;
  value: number;
  isLoading: boolean;
  description?: string;
  color?: string;
  subtitle?: string;
  emptyStateMessage?: string;
  buttonText?: string;
  buttonLink?: string;
  actionComponent?: React.ReactNode;
}

const DashboardStatCard: React.FC<DashboardStatCardProps> = ({
  title,
  icon: Icon,
  value,
  isLoading,
  description,
  color = "text-primary",
  subtitle,
  emptyStateMessage,
  buttonText,
  buttonLink,
  actionComponent,
}) => {
  const navigate = useNavigate();

  // Create the appropriate icon color class from the color prop
  const iconColor = color ? (color.startsWith('text-') ? color : `text-${color}-500`) : 'text-primary';

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <Icon className={`mr-2 h-5 w-5 ${iconColor}`} />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-8 w-16" />
        ) : (
          <>
            <p className="text-3xl font-bold">{value}</p>
            <p className="text-sm text-muted-foreground">
              {value === 0 && emptyStateMessage
                ? emptyStateMessage
                : description || subtitle}
            </p>
            {actionComponent}
          </>
        )}
      </CardContent>
      {buttonText && buttonLink && (
        <CardFooter>
          <Button 
            variant="ghost" 
            className="text-sm" 
            onClick={() => navigate(buttonLink)}
          >
            {value === 0 && emptyStateMessage ? "Import Data" : buttonText}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default DashboardStatCard;
