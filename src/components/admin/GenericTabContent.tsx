
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon, HelpCircle } from 'lucide-react';

interface GenericTabContentProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  message?: string;
}

const GenericTabContent: React.FC<GenericTabContentProps> = ({ 
  title, 
  description = "Feature coming soon", 
  icon: Icon = HelpCircle, 
  message = "This feature is currently under development and will be available soon."
}) => {
  return (
    <Card className="transition-colors duration-200">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-10">
          <Icon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">{title} Coming Soon</h3>
          <p className="text-muted-foreground mt-2">
            {message}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default GenericTabContent;
