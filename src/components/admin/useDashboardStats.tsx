
import { useCallback, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export interface DashboardStats {
  adminCount: number;
  breweryCount: number;
  userCount: number;
  verifiedBreweries: number;
  unverifiedBreweries: number;
  beerMenuItems: number;
  digitalBoards: number;
}

export const useDashboardStats = () => {
  const [stats, setStats] = useState<DashboardStats>({
    adminCount: 0,
    breweryCount: 0,
    userCount: 0,
    verifiedBreweries: 0,
    unverifiedBreweries: 0,
    beerMenuItems: 0,
    digitalBoards: 0
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchStats = useCallback(async () => {
    setIsLoading(true);
    setError('');
    
    try {
      // Fetch users
      const { data: users, error: usersError } = await supabase
        .from('profiles')
        .select('*');
      
      if (usersError) throw usersError;
      
      // Fetch breweries
      const { data: breweries, error: breweriesError } = await supabase
        .from('breweries')
        .select('*');
      
      if (breweriesError) throw breweriesError;
      
      // Fetch beer menu items
      const { data: beerMenuItems, error: beerMenuError } = await supabase
        .from('beer_menu')
        .select('*');
        
      if (beerMenuError) throw beerMenuError;
      
      // Fetch digital boards
      const { data: digitalBoards, error: boardsError } = await supabase
        .from('digital_boards')
        .select('*');
        
      if (boardsError) throw boardsError;
      
      // Calculate stats
      const adminUsers = users?.filter(user => user.role === 'admin') || [];
      const breweryUsers = users?.filter(user => user.role === 'brewery') || [];
      
      // Count verified and unverified breweries
      // Note: Using verification_open as a proxy for verified status
      const verifiedBreweries = breweries?.filter(brewery => brewery.verification_open === true) || [];
      const unverifiedBreweries = breweries?.filter(brewery => brewery.verification_open !== true) || [];
      
      setStats({
        adminCount: adminUsers.length,
        breweryCount: breweries?.length || 0,
        userCount: users?.length || 0,
        verifiedBreweries: verifiedBreweries.length,
        unverifiedBreweries: unverifiedBreweries.length,
        beerMenuItems: beerMenuItems?.length || 0,
        digitalBoards: digitalBoards?.length || 0
      });
      
    } catch (err: any) {
      console.error('Error fetching dashboard stats:', err);
      setError(err.message || 'Failed to load dashboard statistics');
      
      toast({
        title: "Error",
        description: "Failed to load dashboard statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return { stats, isLoading, error };
};
