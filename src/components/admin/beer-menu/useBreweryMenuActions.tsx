
import { useToast } from '@/hooks/use-toast';

export const useBreweryMenuActions = () => {
  const { toast } = useToast();
  
  const handleExportCsv = (breweryId: string, breweryName: string) => {
    toast({
      title: 'Export started',
      description: `Exporting beer menu for ${breweryName}`
    });
    
    // In a real implementation, this would trigger the export process
  };
  
  const handleViewDigitalBoard = (breweryId: string) => {
    window.open(`/digital-board/${breweryId}`, '_blank');
  };
  
  return {
    handleExportCsv,
    handleViewDigitalBoard
  };
};

export default useBreweryMenuActions;
