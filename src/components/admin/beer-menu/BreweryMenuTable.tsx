
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Download, Monitor } from 'lucide-react';

interface BreweryWithMenuStats {
  id: string;
  name: string | null;
  city: string | null;
  state: string | null;
  beerMenuCount: number;
  hasDigitalBoard: boolean;
}

interface BreweryMenuTableProps {
  filteredBreweries: BreweryWithMenuStats[];
  isLoading: boolean;
  onExportCsv: (breweryId: string, breweryName: string) => void;
  onViewDigitalBoard: (breweryId: string) => void;
}

export const BreweryMenuTable: React.FC<BreweryMenuTableProps> = ({
  filteredBreweries,
  isLoading,
  onExportCsv,
  onViewDigitalBoard
}) => {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Brewery</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Beer Menu Items</TableHead>
            <TableHead>Digital Board</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center p-4">Loading...</TableCell>
            </TableRow>
          ) : filteredBreweries.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center p-4">No breweries found</TableCell>
            </TableRow>
          ) : (
            filteredBreweries.map((brewery) => (
              <TableRow key={brewery.id}>
                <TableCell className="font-medium">{brewery.name}</TableCell>
                <TableCell>{brewery.city}, {brewery.state}</TableCell>
                <TableCell>
                  {brewery.beerMenuCount > 0 ? (
                    <Badge variant="secondary" className="font-normal">
                      {brewery.beerMenuCount} items
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-muted-foreground font-normal">
                      No items
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {brewery.hasDigitalBoard ? (
                    <Badge variant="default" className="font-normal">
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-muted-foreground font-normal">
                      Not set up
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onExportCsv(brewery.id, brewery.name || '')}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Export
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onViewDigitalBoard(brewery.id)}
                      disabled={!brewery.hasDigitalBoard}
                    >
                      <Monitor className="h-4 w-4 mr-1" />
                      View Board
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default BreweryMenuTable;
