
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { Beer } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SearchFilter from './beer-menu/SearchFilter';
import BreweryMenuTable from './beer-menu/BreweryMenuTable';
import { useBreweryMenuActions } from './beer-menu/useBreweryMenuActions';
import BreweryPagination from '@/components/brewery/BreweryPagination';

interface BreweryWithMenuStats {
  id: string;
  name: string | null;
  city: string | null;
  state: string | null;
  beerMenuCount: number;
  hasDigitalBoard: boolean;
}

export const BeerMenuDashboard = () => {
  const [breweries, setBreweries] = useState<BreweryWithMenuStats[]>([]);
  const [filteredBreweries, setFilteredBreweries] = useState<BreweryWithMenuStats[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { handleExportCsv, handleViewDigitalBoard } = useBreweryMenuActions();
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  useEffect(() => {
    const fetchBreweryData = async () => {
      try {
        setIsLoading(true);
        
        // Simulate loading from localStorage when Supabase is not available
        const mockData = localStorage.getItem('breweries');
        if (mockData) {
          const parsedData = JSON.parse(mockData);
          
          // Transform to the format we need
          const breweryWithStats = parsedData.map((brewery: any) => ({
            id: brewery.id,
            name: brewery.name || 'Unnamed Brewery',
            city: brewery.city || '',
            state: brewery.state || '',
            beerMenuCount: Math.floor(Math.random() * 12), // Random count for mock data
            hasDigitalBoard: Math.random() > 0.5 // Random boolean for mock data
          }));
          
          setBreweries(breweryWithStats);
          setFilteredBreweries(breweryWithStats);
          setIsLoading(false);
          return;
        }
        
        // If localStorage doesn't have data, try Supabase
        const { data: breweryData, error: breweryError } = await supabase
          .from('breweries')
          .select('id, name, city, state')
          .order('name');
          
        if (breweryError) throw breweryError;
        
        // For each brewery, check if they have any beer menu items and digital board
        const breweryWithStats = await Promise.all(
          breweryData.map(async (brewery) => {
            // Count beer menu items
            const { count: beerMenuCount, error: beerMenuError } = await supabase
              .from('beer_menu')
              .select('*', { count: 'exact', head: true })
              .eq('brewery_id', brewery.id);
              
            if (beerMenuError) console.error(`Error fetching beer menu for ${brewery.id}:`, beerMenuError);
            
            // Check if they have a digital board
            let hasDigitalBoard = false;
            try {
              const { data: digitalBoard } = await supabase
                .from('digital_boards')
                .select('id')
                .eq('brewery_id', brewery.id)
                .single();
                
              hasDigitalBoard = !!digitalBoard;
            } catch (error) {
              console.error(`Error checking digital board for ${brewery.id}:`, error);
            }
            
            return {
              ...brewery,
              beerMenuCount: beerMenuCount || 0,
              hasDigitalBoard
            } as BreweryWithMenuStats;
          })
        );
        
        setBreweries(breweryWithStats);
        setFilteredBreweries(breweryWithStats);
      } catch (error) {
        console.error('Error fetching brewery data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load brewery data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchBreweryData();
  }, [toast]);
  
  // Filter breweries based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredBreweries(breweries);
      return;
    }
    
    const filtered = breweries.filter(brewery => 
      brewery.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      brewery.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      brewery.state?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredBreweries(filtered);
  }, [searchTerm, breweries]);
  
  // Get paginated items
  const paginatedItems = filteredBreweries.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // Calculate total pages
  const totalPages = Math.ceil(filteredBreweries.length / itemsPerPage);
  
  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Beer className="h-5 w-5" />
          Beer Menus Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <SearchFilter 
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        
        <BreweryMenuTable
          filteredBreweries={paginatedItems}
          isLoading={isLoading}
          onExportCsv={handleExportCsv}
          onViewDigitalBoard={handleViewDigitalBoard}
        />
        
        {filteredBreweries.length > 0 && (
          <BreweryPagination
            currentPage={currentPage}
            pageSize={itemsPerPage}
            totalPages={totalPages}
            totalItems={filteredBreweries.length}
            onPageChange={setCurrentPage}
            onPageSizeChange={setItemsPerPage}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default BeerMenuDashboard;
