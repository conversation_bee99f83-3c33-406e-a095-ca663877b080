
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building, Trash2, Plus, RefreshCw } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useNavigate } from 'react-router-dom';
import { useBreweryFetch } from '@/hooks/brewery/useBreweryFetch';
import BreweryDataView from '@/components/brewery/BreweryDataView';
import { useBreweryDeleteAll } from '@/hooks/brewery/useBreweryDeleteAll';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from '@/hooks/use-toast';

interface BreweriesTabContentProps {
  loading: boolean;
  totalBreweries: number;
}

const BreweriesTabContent: React.FC<BreweriesTabContentProps> = ({ loading, totalBreweries }) => {
  const navigate = useNavigate();
  const { breweries, isLoading: breweryLoading, reloadBreweries, setBreweries } = useBreweryFetch();
  const { isDeleteAllLoading, handleDeleteAllBreweries } = useBreweryDeleteAll({ setBreweries });
  
  // Combined loading state
  const isLoading = loading || breweryLoading;

  // Wrapper for reloadBreweries to ensure it returns Promise<void>
  const handleReloadBreweries = async () => {
    try {
      await reloadBreweries();
      toast({
        title: "Data refreshed",
        description: `Loaded ${breweries.length} breweries successfully`
      });
    } catch (error) {
      console.error("Error reloading breweries:", error);
      toast({
        title: "Error refreshing data",
        description: "There was a problem reloading brewery data",
        variant: "destructive"
      });
    }
  };

  // Define columns to be displayed, ensuring name is first
  const displayColumns = ['name', 'brewery_type', 'city', 'state', 'phone', 'website', 'email'];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-2xl">Brewery Management</CardTitle>
          <CardDescription>Manage all registered breweries on the platform</CardDescription>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleReloadBreweries} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
          
          <Button variant="outline" onClick={() => navigate('/import')}>
            <Plus className="h-4 w-4 mr-2" />
            Import Data
          </Button>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={isDeleteAllLoading || breweries.length === 0}>
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleteAllLoading ? "Deleting..." : "Delete All"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete all{" "}
                  <strong>{breweries.length}</strong> breweries from the database.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteAllBreweries}>
                  Yes, delete all breweries
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : breweries.length > 0 ? (
          <BreweryDataView 
            breweries={breweries}
            onRefresh={handleReloadBreweries}
            displayColumns={displayColumns}
          />
        ) : (
          <div className="text-center py-10 border rounded-md bg-slate-50 dark:bg-slate-900">
            <Building className="mx-auto h-12 w-12 text-blue-500 mb-4" />
            <h3 className="text-lg font-medium">No Breweries Found</h3>
            <p className="text-muted-foreground mt-2 mb-4">
              There are no brewery accounts registered yet.
            </p>
            <Button onClick={() => navigate('/import')}>
              <Plus className="h-4 w-4 mr-2" />
              Import Brewery Data
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BreweriesTabContent;
