
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import DashboardStatCard from './DashboardStatCard';
import { Beer, Users, Building2, Award, AlertTriangle, Tv } from 'lucide-react';
import { DashboardStats } from './useDashboardStats';

interface OverviewTabContentProps {
  stats: DashboardStats;
  isLoading: boolean;
}

const OverviewTabContent: React.FC<OverviewTabContentProps> = ({ 
  stats, 
  isLoading 
}) => {
  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">System Overview</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <DashboardStatCard 
          title="Total Users"
          value={stats.userCount}
          icon={Users}
          description="Total registered users"
          isLoading={isLoading}
        />
        
        <DashboardStatCard 
          title="Admin Users"
          value={stats.adminCount}
          icon={Award}
          description="Users with admin privileges"
          isLoading={isLoading}
        />
        
        <DashboardStatCard 
          title="Breweries"
          value={stats.breweryCount}
          icon={Building2}
          description="Total registered breweries"
          isLoading={isLoading}
        />
        
        <DashboardStatCard 
          title="Verified Breweries"
          value={stats.verifiedBreweries}
          icon={Award}
          description="Breweries with verified status"
          isLoading={isLoading}
          color="green"
        />
        
        <DashboardStatCard 
          title="Unverified Breweries"
          value={stats.unverifiedBreweries}
          icon={AlertTriangle}
          description="Breweries pending verification"
          isLoading={isLoading}
          color="amber"
        />
        
        <DashboardStatCard 
          title="Beer Menu Items"
          value={stats.beerMenuItems}
          icon={Beer}
          description="Total beer menu items"
          isLoading={isLoading}
          color="blue"
        />
        
        <DashboardStatCard 
          title="Digital Boards"
          value={stats.digitalBoards}
          icon={Tv}
          description="Active digital beer boards"
          isLoading={isLoading}
          color="purple"
        />
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Feature coming soon: Activity log will display recent changes and actions.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default OverviewTabContent;
