-- Quick Database Setup for Beersty
-- Run this in phpMyAdmin or MySQL command line

-- Create database
CREATE DATABASE IF NOT EXISTS beersty_db;
USE beersty_db;

-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL
);

-- Profiles table
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    role ENUM('admin', 'brewery', 'customer') NOT NULL DEFAULT 'customer',
    brewery_id VARCHAR(36) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
);

-- Breweries table
CREATE TABLE breweries (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    address TEXT NULL,
    city VARCHAR(100) NULL,
    state VARCHAR(50) NULL,
    zip VARCHAR(20) NULL,
    phone VARCHAR(20) NULL,
    website VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    description TEXT NULL,
    logo VARCHAR(255) NULL,
    feature_image VARCHAR(255) NULL,
    avatar VARCHAR(255) NULL,
    brewery_type VARCHAR(50) NOT NULL DEFAULT 'micro',
    social_links JSON NULL,
    claimable BOOLEAN DEFAULT TRUE,
    claimed BOOLEAN DEFAULT FALSE,
    verification_open BOOLEAN DEFAULT TRUE,
    verified BOOLEAN DEFAULT FALSE,
    follower_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO users (id, email, password_hash) VALUES 
('admin-user-id', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

INSERT INTO profiles (id, email, role) VALUES 
('admin-user-id', '<EMAIL>', 'admin');

-- Insert sample brewery
INSERT INTO breweries (id, name, description, city, state, brewery_type, claimed, verified) VALUES 
('sample-brewery-1', 'Demo Craft Brewery', 'A sample brewery for testing the system', 'Demo City', 'Demo State', 'micro', TRUE, TRUE);

SELECT 'Database setup complete!' as status;
