<?php
require_once 'config/config.php';

echo "<h2>🐛 Profile Debug Page</h2>";

// Check authentication
echo "<h3>1. Authentication Check:</h3>";
if (isLoggedIn()) {
    $user = getCurrentUser();
    echo "✅ User is logged in<br>";
    echo "User ID: " . htmlspecialchars($user['id']) . "<br>";
    echo "Email: " . htmlspecialchars($user['email']) . "<br>";
    echo "Role: " . htmlspecialchars($user['role'] ?? 'unknown') . "<br>";
} else {
    echo "❌ User is not logged in<br>";
    echo "<a href='/account/login/'>Please log in</a>";
    exit;
}

// Check database connection
echo "<h3>2. Database Connection:</h3>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "✅ Database connection successful<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    exit;
}

// Check if profile exists
echo "<h3>3. Profile Check:</h3>";
try {
    $stmt = $conn->prepare("SELECT * FROM profiles WHERE id = ?");
    $stmt->execute([$user['id']]);
    $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($profile) {
        echo "✅ Profile exists<br>";
        echo "Profile data: <pre>" . htmlspecialchars(json_encode($profile, JSON_PRETTY_PRINT)) . "</pre>";
    } else {
        echo "❌ Profile does not exist. Creating one...<br>";
        
        $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?)");
        $stmt->execute([$user['id'], $user['email'], $user['role'] ?? 'customer']);
        echo "✅ Profile created<br>";
        
        // Fetch the newly created profile
        $stmt = $conn->prepare("SELECT * FROM profiles WHERE id = ?");
        $stmt->execute([$user['id']]);
        $profile = $stmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    echo "❌ Profile check failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    exit;
}

// Process form if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>4. Form Processing:</h3>";
    echo "POST data received: <pre>" . htmlspecialchars(json_encode($_POST, JSON_PRETTY_PRINT)) . "</pre>";
    echo "FILES data received: <pre>" . htmlspecialchars(json_encode($_FILES, JSON_PRETTY_PRINT)) . "</pre>";
    
    $errors = [];
    
    // Get form data
    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    $username = sanitizeInput($_POST['username'] ?? '');
    $bio = sanitizeInput($_POST['bio'] ?? '');
    
    echo "Sanitized data:<br>";
    echo "First Name: " . htmlspecialchars($firstName) . "<br>";
    echo "Last Name: " . htmlspecialchars($lastName) . "<br>";
    echo "Username: " . htmlspecialchars($username) . "<br>";
    echo "Bio: " . htmlspecialchars($bio) . "<br>";
    
    // Handle avatar upload
    $avatarPath = null;
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        echo "Avatar upload detected<br>";
        echo "File name: " . htmlspecialchars($_FILES['avatar']['name']) . "<br>";
        echo "File size: " . $_FILES['avatar']['size'] . " bytes<br>";
        echo "File type: " . htmlspecialchars($_FILES['avatar']['type']) . "<br>";
        
        $uploadDir = 'uploads/avatars/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
            echo "Created upload directory<br>";
        }
        
        $fileInfo = pathinfo($_FILES['avatar']['name']);
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
        $fileExtension = strtolower($fileInfo['extension']);
        
        if (!in_array($fileExtension, $allowedTypes)) {
            $errors[] = 'Avatar must be a JPG, PNG, or GIF image.';
        } elseif ($_FILES['avatar']['size'] > 5 * 1024 * 1024) {
            $errors[] = 'Avatar file size must be less than 5MB.';
        } else {
            $fileName = $user['id'] . '_' . time() . '.' . $fileExtension;
            $uploadPath = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadPath)) {
                $avatarPath = '/uploads/avatars/' . $fileName;
                echo "✅ Avatar uploaded successfully: $avatarPath<br>";
            } else {
                $errors[] = 'Failed to upload avatar.';
                echo "❌ Failed to move uploaded file<br>";
            }
        }
    } else {
        echo "No avatar upload or upload error<br>";
        if (isset($_FILES['avatar'])) {
            echo "Upload error code: " . $_FILES['avatar']['error'] . "<br>";
        }
    }
    
    if (empty($errors)) {
        echo "Attempting database update...<br>";
        
        try {
            if ($avatarPath !== null) {
                echo "Updating with avatar<br>";
                $stmt = $conn->prepare("
                    UPDATE profiles SET 
                        first_name = ?, last_name = ?, username = ?, bio = ?, 
                        avatar = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$firstName, $lastName, $username ?: null, $bio, $avatarPath, $user['id']]);
            } else {
                echo "Updating without avatar<br>";
                $stmt = $conn->prepare("
                    UPDATE profiles SET 
                        first_name = ?, last_name = ?, username = ?, bio = ?, 
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$firstName, $lastName, $username ?: null, $bio, $user['id']]);
            }
            
            echo "✅ Profile updated successfully!<br>";
            
        } catch (Exception $e) {
            echo "❌ Database update failed: " . htmlspecialchars($e->getMessage()) . "<br>";
            echo "Error details: <pre>" . htmlspecialchars(print_r($e, true)) . "</pre>";
        }
    } else {
        echo "❌ Validation errors:<br>";
        foreach ($errors as $error) {
            echo "- " . htmlspecialchars($error) . "<br>";
        }
    }
}
?>

<h3>5. Test Form:</h3>
<form method="POST" enctype="multipart/form-data" style="max-width: 500px;">
    <div style="margin-bottom: 10px;">
        <label>First Name:</label><br>
        <input type="text" name="first_name" value="<?php echo htmlspecialchars($profile['first_name'] ?? ''); ?>" style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Last Name:</label><br>
        <input type="text" name="last_name" value="<?php echo htmlspecialchars($profile['last_name'] ?? ''); ?>" style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Username:</label><br>
        <input type="text" name="username" value="<?php echo htmlspecialchars($profile['username'] ?? ''); ?>" style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Bio:</label><br>
        <textarea name="bio" style="width: 100%; padding: 5px; height: 60px;"><?php echo htmlspecialchars($profile['bio'] ?? ''); ?></textarea>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Avatar:</label><br>
        <input type="file" name="avatar" accept="image/*" style="width: 100%; padding: 5px;">
    </div>
    
    <button type="submit" style="background: #FFC107; color: #000; padding: 10px 20px; border: none; border-radius: 5px;">
        Update Profile
    </button>
</form>

<p><a href="/user/profile.php">← Back to Profile Page</a></p>
