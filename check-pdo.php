<?php
/**
 * PDO Extension Check and Diagnostic Tool
 * This script checks if PDO is properly installed and configured
 */

echo "<h1>PDO Extension Diagnostic Tool</h1>";
echo "<hr>";

// Check if PDO extension is loaded
echo "<h2>1. PDO Extension Check</h2>";
if (extension_loaded('pdo')) {
    echo "<div style='color: green; font-weight: bold;'>✅ PDO extension is loaded</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ PDO extension is NOT loaded</div>";
    echo "<p style='color: red;'>You need to enable PDO in your php.ini file</p>";
}

// Check PDO MySQL driver
echo "<h2>2. PDO MySQL Driver Check</h2>";
if (extension_loaded('pdo_mysql')) {
    echo "<div style='color: green; font-weight: bold;'>✅ PDO MySQL driver is loaded</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ PDO MySQL driver is NOT loaded</div>";
    echo "<p style='color: red;'>You need to enable pdo_mysql in your php.ini file</p>";
}

// List all available PDO drivers
echo "<h2>3. Available PDO Drivers</h2>";
if (class_exists('PDO')) {
    $drivers = PDO::getAvailableDrivers();
    if (!empty($drivers)) {
        echo "<ul>";
        foreach ($drivers as $driver) {
            echo "<li><strong>$driver</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<div style='color: red;'>No PDO drivers available</div>";
    }
} else {
    echo "<div style='color: red;'>PDO class not available</div>";
}

// Check PHP version
echo "<h2>4. PHP Version</h2>";
echo "<div>PHP Version: <strong>" . phpversion() . "</strong></div>";

// Check php.ini location
echo "<h2>5. PHP Configuration</h2>";
echo "<div>php.ini location: <strong>" . php_ini_loaded_file() . "</strong></div>";

// Test PDO MySQL connection
echo "<h2>6. PDO MySQL Connection Test</h2>";
if (extension_loaded('pdo_mysql')) {
    try {
        // Try to connect to MySQL
        $dsn = "mysql:host=localhost;charset=utf8mb4";
        $pdo = new PDO($dsn, 'root', '', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<div style='color: green; font-weight: bold;'>✅ PDO MySQL connection successful</div>";
        
        // Test database connection
        try {
            $pdo->exec("USE beersty_db");
            echo "<div style='color: green; font-weight: bold;'>✅ Connected to beersty_db database</div>";
            
            // Test users table
            $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
            if ($stmt->rowCount() > 0) {
                echo "<div style='color: green; font-weight: bold;'>✅ Users table exists</div>";
                
                // Check users table structure
                $stmt = $pdo->query("DESCRIBE users");
                $columns = $stmt->fetchAll();
                echo "<h3>Users Table Structure:</h3>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
            } else {
                echo "<div style='color: red; font-weight: bold;'>❌ Users table does not exist</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div style='color: orange; font-weight: bold;'>⚠️ Database 'beersty_db' not found or accessible</div>";
            echo "<div style='color: orange;'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div style='color: red; font-weight: bold;'>❌ PDO MySQL connection failed</div>";
        echo "<div style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ Cannot test - PDO MySQL driver not loaded</div>";
}

// Show loaded extensions
echo "<h2>7. All Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<div style='max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>";
foreach ($extensions as $ext) {
    $color = (strpos($ext, 'pdo') !== false) ? 'color: green; font-weight: bold;' : '';
    echo "<div style='$color'>$ext</div>";
}
echo "</div>";

// Instructions for enabling PDO
echo "<h2>8. How to Enable PDO (if not enabled)</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-left: 4px solid #007cba;'>";
echo "<h3>For XAMPP:</h3>";
echo "<ol>";
echo "<li>Open XAMPP Control Panel</li>";
echo "<li>Click 'Config' next to Apache</li>";
echo "<li>Select 'PHP (php.ini)'</li>";
echo "<li>Find these lines and remove the semicolon (;) at the beginning:</li>";
echo "<ul>";
echo "<li><code>;extension=pdo_mysql</code> → <code>extension=pdo_mysql</code></li>";
echo "<li><code>;extension=mysqli</code> → <code>extension=mysqli</code></li>";
echo "</ul>";
echo "<li>Save the file</li>";
echo "<li>Restart Apache in XAMPP Control Panel</li>";
echo "</ol>";

echo "<h3>Alternative method:</h3>";
echo "<ol>";
echo "<li>Navigate to your XAMPP installation folder (usually C:\\xampp)</li>";
echo "<li>Open php\\php.ini in a text editor</li>";
echo "<li>Search for 'pdo_mysql' and uncomment the line</li>";
echo "<li>Search for 'mysqli' and uncomment the line</li>";
echo "<li>Save and restart Apache</li>";
echo "</ol>";
echo "</div>";

// Quick fix buttons
echo "<h2>9. Quick Actions</h2>";
echo "<div>";
echo "<a href='phpinfo.php' target='_blank' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; margin-right: 10px;'>View phpinfo()</a>";
echo "<a href='check-pdo.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; margin-right: 10px;'>Refresh This Page</a>";
echo "<a href='admin/user-management.php' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none;'>Test User Management</a>";
echo "</div>";

echo "<hr>";
echo "<p><em>Generated on: " . date('Y-m-d H:i:s') . "</em></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>PDO Diagnostic Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
