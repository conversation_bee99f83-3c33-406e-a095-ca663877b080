<?php
/**
 * Database Setup Script
 * Automatically setup the database and tables
 */

echo "<h1>🛠️ Database Setup</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$setupType = $_GET['type'] ?? 'mysql';

if ($setupType === 'sqlite') {
    echo "<h2>🗄️ Setting up SQLite Database</h2>";
    
    try {
        // Create data directory
        if (!is_dir('data')) {
            mkdir('data', 0755, true);
            echo "<p>✅ Created data directory</p>";
        }
        
        // Create SQLite database
        $sqliteDb = 'data/beersty.sqlite';
        $dsn = "sqlite:$sqliteDb";
        $conn = new PDO($dsn);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ SQLite database created: $sqliteDb</p>";
        
        // Create tables
        $sql = "
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(32) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME NULL
        );
        
        CREATE TABLE IF NOT EXISTS profiles (
            id VARCHAR(32) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            role VARCHAR(50) DEFAULT 'customer',
            brewery_id VARCHAR(32) NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES users(id)
        );
        
        CREATE TABLE IF NOT EXISTS breweries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            state VARCHAR(50),
            zip VARCHAR(20),
            phone VARCHAR(50),
            email VARCHAR(255),
            website VARCHAR(255),
            description TEXT,
            brewery_type VARCHAR(50) DEFAULT 'micro',
            verified BOOLEAN DEFAULT 0,
            claimed BOOLEAN DEFAULT 0,
            claimable BOOLEAN DEFAULT 1,
            follower_count INTEGER DEFAULT 0,
            like_count INTEGER DEFAULT 0,
            external_id VARCHAR(100),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        ";
        
        $conn->exec($sql);
        echo "<p>✅ Database tables created</p>";
        
        // Create admin user
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT OR REPLACE INTO users (id, email, password_hash) VALUES (?, ?, ?)");
        $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
        
        $stmt = $conn->prepare("INSERT OR REPLACE INTO profiles (id, email, role) VALUES (?, ?, 'admin')");
        $stmt->execute([$userId, '<EMAIL>']);
        
        echo "<p>✅ Admin user created</p>";
        
        // Update database config to use SQLite
        $configContent = "<?php
class Database {
    private \$host = 'localhost';
    private \$db_name = 'data/beersty.sqlite';
    private \$username = '';
    private \$password = '';
    private \$conn;
    private \$driver = 'sqlite';

    public function __construct() {
        // SQLite configuration
    }

    public function getConnection() {
        \$this->conn = null;
        
        try {
            if (\$this->driver === 'sqlite') {
                \$dsn = \"sqlite:\" . \$this->db_name;
                \$this->conn = new PDO(\$dsn);
            } else {
                \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
                \$this->conn = new PDO(\$dsn, \$this->username, \$this->password);
            }
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            \$this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            throw new Exception(\"Database connection failed\");
        }
        
        return \$this->conn;
    }

    public function testConnection() {
        try {
            \$conn = \$this->getConnection();
            return \$conn !== null;
        } catch (Exception \$e) {
            return false;
        }
    }
}
?>";
        
        file_put_contents('config/database.php', $configContent);
        echo "<p>✅ Database configuration updated to use SQLite</p>";
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 SQLite Setup Complete!</h3>";
        echo "<p>Your database is now ready to use with SQLite.</p>";
        echo "<ul>";
        echo "<li><strong>Database file:</strong> data/beersty.sqlite</li>";
        echo "<li><strong>Admin email:</strong> <EMAIL></li>";
        echo "<li><strong>Admin password:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ SQLite Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<h2>🐬 Setting up MySQL Database</h2>";
    
    try {
        // Try to connect to MySQL server
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $dbName = 'beersty_db';
        
        $dsn = "mysql:host=$host;charset=utf8mb4";
        $conn = new PDO($dsn, $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ MySQL server connection successful</p>";
        
        // Create database
        $conn->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✅ Database '$dbName' created</p>";
        
        // Connect to the specific database
        $dsn = "mysql:host=$host;dbname=$dbName;charset=utf8mb4";
        $conn = new PDO($dsn, $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create tables
        $sql = "
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(32) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL
        );
        
        CREATE TABLE IF NOT EXISTS profiles (
            id VARCHAR(32) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            role VARCHAR(50) DEFAULT 'customer',
            brewery_id VARCHAR(32) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES users(id)
        );
        
        CREATE TABLE IF NOT EXISTS breweries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT,
            city VARCHAR(100),
            state VARCHAR(50),
            zip VARCHAR(20),
            phone VARCHAR(50),
            email VARCHAR(255),
            website VARCHAR(255),
            description TEXT,
            brewery_type VARCHAR(50) DEFAULT 'micro',
            verified BOOLEAN DEFAULT 0,
            claimed BOOLEAN DEFAULT 0,
            claimable BOOLEAN DEFAULT 1,
            follower_count INT DEFAULT 0,
            like_count INT DEFAULT 0,
            external_id VARCHAR(100),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
        ";
        
        $conn->exec($sql);
        echo "<p>✅ Database tables created</p>";
        
        // Create admin user
        $userId = bin2hex(random_bytes(16));
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)");
        $stmt->execute([$userId, '<EMAIL>', $passwordHash]);
        
        $stmt = $conn->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, 'admin') ON DUPLICATE KEY UPDATE role = 'admin'");
        $stmt->execute([$userId, '<EMAIL>']);
        
        echo "<p>✅ Admin user created</p>";
        
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 MySQL Setup Complete!</h3>";
        echo "<p>Your database is now ready to use with MySQL.</p>";
        echo "<ul>";
        echo "<li><strong>Database:</strong> $dbName</li>";
        echo "<li><strong>Admin email:</strong> <EMAIL></li>";
        echo "<li><strong>Admin password:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>❌ MySQL Setup Failed</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Try the SQLite option instead, or make sure MySQL/XAMPP is running.</p>";
        echo "</div>";
    }
}

echo "<h2>🔗 Next Steps</h2>";
echo "<ul>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>Try Login Now</a></li>";
echo "<li><a href='import-michigan-breweries.php' class='btn btn-success'>Import Brewery Data</a></li>";
echo "<li><a href='test-database-connection.php' class='btn btn-info'>Test Database</a></li>";
echo "<li><a href='/' class='btn btn-secondary'>Back to Homepage</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }
.btn-success { background-color: #28a745; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
