<?php
/**
 * MySQL Installation Checker
 * Check if MySQL is installed and provide installation guidance
 */

echo "<h1>🐬 MySQL Installation Checker</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. 📋 PHP MySQL Extensions</h2>";
echo "<ul>";
echo "<li><strong>PDO:</strong> " . (extension_loaded('pdo') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Not available') . "</li>";
echo "<li><strong>MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ Available' : '❌ Not available') . "</li>";
echo "</ul>";

echo "<h2>2. 🔍 MySQL Installation Detection</h2>";

$mysqlFound = false;
$mysqlPaths = [];

// Check common MySQL installation paths
$commonPaths = [
    'C:\xampp\mysql\bin\mysql.exe',
    'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe',
    'C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe',
    'C:\Program Files (x86)\MySQL\MySQL Server 8.0\bin\mysql.exe',
    'C:\Program Files (x86)\MySQL\MySQL Server 5.7\bin\mysql.exe',
    'C:\wamp64\bin\mysql\mysql8.0.31\bin\mysql.exe',
    'C:\wamp\bin\mysql\mysql5.7.36\bin\mysql.exe'
];

echo "<h3>Checking common installation paths:</h3>";
echo "<ul>";
foreach ($commonPaths as $path) {
    if (file_exists($path)) {
        echo "<li>✅ <strong>Found:</strong> $path</li>";
        $mysqlPaths[] = $path;
        $mysqlFound = true;
    } else {
        echo "<li>❌ Not found: $path</li>";
    }
}
echo "</ul>";

echo "<h2>3. 🚀 MySQL Service Status</h2>";

// Check if MySQL service is running (Windows)
$output = [];
$returnVar = 0;
exec('sc query MySQL 2>nul', $output, $returnVar);

if ($returnVar === 0 && !empty($output)) {
    echo "<p>✅ MySQL Windows Service found</p>";
    $serviceRunning = false;
    foreach ($output as $line) {
        if (strpos($line, 'RUNNING') !== false) {
            $serviceRunning = true;
            break;
        }
    }
    
    if ($serviceRunning) {
        echo "<p>✅ MySQL service is RUNNING</p>";
    } else {
        echo "<p>❌ MySQL service is STOPPED</p>";
        echo "<p>🔧 <strong>Fix:</strong> Start MySQL service from Windows Services or XAMPP Control Panel</p>";
    }
} else {
    echo "<p>❌ MySQL Windows Service not found</p>";
}

// Check for XAMPP MySQL process
$output = [];
exec('tasklist /FI "IMAGENAME eq mysqld.exe" 2>nul', $output);
$mysqldRunning = false;
foreach ($output as $line) {
    if (strpos($line, 'mysqld.exe') !== false) {
        $mysqldRunning = true;
        break;
    }
}

if ($mysqldRunning) {
    echo "<p>✅ MySQL daemon (mysqld.exe) is running</p>";
} else {
    echo "<p>❌ MySQL daemon (mysqld.exe) is not running</p>";
}

echo "<h2>4. 🧪 Connection Test</h2>";

if ($mysqlFound || $mysqldRunning) {
    echo "<p>Attempting to connect to MySQL...</p>";
    
    try {
        $dsn = "mysql:host=localhost;charset=utf8mb4";
        $conn = new PDO($dsn, 'root', '');
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ <strong>SUCCESS!</strong> Connected to MySQL server</p>";
        
        // Check if our database exists
        $stmt = $conn->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute(['beersty_db']);
        $dbExists = $stmt->fetch();
        
        if ($dbExists) {
            echo "<p>✅ Database 'beersty_db' exists</p>";
        } else {
            echo "<p>❌ Database 'beersty_db' does not exist</p>";
            echo "<p>🔧 <strong>Fix:</strong> Need to create database</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>CONNECTION FAILED:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        
        if (strpos($e->getMessage(), 'Connection refused') !== false) {
            echo "<p>🔧 <strong>Issue:</strong> MySQL server is not running</p>";
        } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
            echo "<p>🔧 <strong>Issue:</strong> Wrong username/password</p>";
        }
    }
} else {
    echo "<p>❌ No MySQL installation detected</p>";
}

echo "<h2>5. 🛠️ Installation Options</h2>";

if (!$mysqlFound) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ MySQL Not Found - Installation Required</h3>";
    echo "<p>Choose one of these installation methods:</p>";
    
    echo "<h4>Option 1: XAMPP (Recommended for Development)</h4>";
    echo "<ul>";
    echo "<li>📥 <strong>Download:</strong> <a href='https://www.apachefriends.org/download.html' target='_blank'>XAMPP for Windows</a></li>";
    echo "<li>💾 <strong>Install:</strong> Run installer and install to C:\\xampp</li>";
    echo "<li>🚀 <strong>Start:</strong> Open XAMPP Control Panel and start MySQL</li>";
    echo "</ul>";
    
    echo "<h4>Option 2: MySQL Server (Production-like)</h4>";
    echo "<ul>";
    echo "<li>📥 <strong>Download:</strong> <a href='https://dev.mysql.com/downloads/mysql/' target='_blank'>MySQL Community Server</a></li>";
    echo "<li>💾 <strong>Install:</strong> Run MySQL installer</li>";
    echo "<li>🔧 <strong>Configure:</strong> Set root password during installation</li>";
    echo "</ul>";
    
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ MySQL Found - Start Services</h3>";
    echo "<p>MySQL is installed but may not be running. Try these steps:</p>";
    
    if (strpos($mysqlPaths[0], 'xampp') !== false) {
        echo "<h4>XAMPP Detected:</h4>";
        echo "<ul>";
        echo "<li>🎛️ Open XAMPP Control Panel</li>";
        echo "<li>🚀 Click 'Start' next to MySQL</li>";
        echo "<li>✅ Wait for status to show 'Running' (green)</li>";
        echo "</ul>";
        
        $xamppPath = dirname(dirname(dirname($mysqlPaths[0])));
        echo "<p><strong>XAMPP Path:</strong> $xamppPath</p>";
        echo "<p><a href='#' onclick=\"alert('Open: $xamppPath\\\\xampp-control.exe')\" class='btn btn-primary'>Show XAMPP Control Path</a></p>";
    } else {
        echo "<h4>Standalone MySQL Detected:</h4>";
        echo "<ul>";
        echo "<li>🔧 Start MySQL service from Windows Services</li>";
        echo "<li>💻 Or use MySQL Workbench</li>";
        echo "<li>⌨️ Or start from command line</li>";
        echo "</ul>";
    }
    echo "</div>";
}

echo "<h2>6. 🔗 Next Steps</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>After MySQL is Running:</h3>";
echo "<ol>";
echo "<li><a href='setup-database.php?type=mysql' class='btn btn-success'>🗄️ Setup Beersty Database</a></li>";
echo "<li><a href='debug-login-quick.php' class='btn btn-info'>🔍 Test Login System</a></li>";
echo "<li><a href='auth/login.php' class='btn btn-primary'>🔐 Try Login</a></li>";
echo "</ol>";
echo "</div>";

echo "<h2>7. 🆘 Quick Help</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
echo "<h4>Common Issues & Solutions:</h4>";
echo "<ul>";
echo "<li><strong>Port 3306 in use:</strong> Another MySQL instance is running</li>";
echo "<li><strong>Access denied:</strong> Check username/password (default: root/empty)</li>";
echo "<li><strong>Connection refused:</strong> MySQL service not started</li>";
echo "<li><strong>Can't connect:</strong> Firewall blocking port 3306</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    color: white;
}

.btn-primary { background-color: #007bff; }
.btn-secondary { background-color: #6c757d; }
.btn-info { background-color: #17a2b8; }
.btn-success { background-color: #28a745; }

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>
