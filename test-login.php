<?php
// Test login functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Login Test</h1>";

// Test database connection and admin user
try {
    $pdo = new PDO("mysql:host=localhost;dbname=beersty_db;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT u.id, u.email, u.password_hash, p.role FROM users u JOIN profiles p ON u.id = p.id WHERE u.email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found in database</p>";
        echo "<p>Email: " . htmlspecialchars($admin['email']) . "</p>";
        echo "<p>Role: " . htmlspecialchars($admin['role']) . "</p>";
        echo "<p>Password Hash: " . substr($admin['password_hash'], 0, 20) . "...</p>";
        
        // Test password verification
        $testPassword = 'admin123';
        if (password_verify($testPassword, $admin['password_hash'])) {
            echo "<p style='color: green;'>✓ Password 'admin123' is correct</p>";
        } else {
            echo "<p style='color: red;'>✗ Password 'admin123' does not match</p>";
            
            // Try to update password
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $updateStmt->execute([$newHash, '<EMAIL>']);
            echo "<p style='color: blue;'>ℹ Password has been reset to 'admin123'</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Admin user not found</p>";
        
        // Create admin user
        echo "<p>Creating admin user...</p>";
        
        $adminId = 'admin-user-id';
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $pdo->beginTransaction();
        
        // Insert user
        $stmt = $pdo->prepare("INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)");
        $stmt->execute([$adminId, '<EMAIL>', $passwordHash]);
        
        // Insert profile
        $stmt = $pdo->prepare("INSERT INTO profiles (id, email, role) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE role = VALUES(role)");
        $stmt->execute([$adminId, '<EMAIL>', 'admin']);
        
        $pdo->commit();
        
        echo "<p style='color: green;'>✓ Admin user created successfully</p>";
    }
    
    echo "<hr>";
    echo "<h2>Manual Login Test</h2>";
    
    if ($_POST['test_login'] ?? false) {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        echo "<h3>Testing login with:</h3>";
        echo "<p>Email: " . htmlspecialchars($email) . "</p>";
        echo "<p>Password: " . htmlspecialchars($password) . "</p>";
        
        $stmt = $pdo->prepare("SELECT u.id, u.email, u.password_hash, p.role FROM users u JOIN profiles p ON u.id = p.id WHERE u.email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password_hash'])) {
            echo "<p style='color: green;'>✓ Login would be successful!</p>";
            echo "<p>User ID: " . $user['id'] . "</p>";
            echo "<p>Role: " . $user['role'] . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Login would fail</p>";
            if (!$user) {
                echo "<p>Reason: User not found</p>";
            } else {
                echo "<p>Reason: Password incorrect</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>

<form method="POST">
    <h3>Test Login Form</h3>
    <p>
        <label>Email:</label><br>
        <input type="email" name="email" value="<EMAIL>" style="width: 300px; padding: 5px;">
    </p>
    <p>
        <label>Password:</label><br>
        <input type="password" name="password" value="admin123" style="width: 300px; padding: 5px;">
    </p>
    <p>
        <button type="submit" name="test_login" value="1" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px;">
            Test Login
        </button>
    </p>
</form>

<hr>
<p><a href="auth/login.php">Go to Real Login Page</a> | <a href="index.php">Back to Homepage</a></p>
