<?php
/**
 * Phase 8 Test Script
 * Test Analytics & Business Intelligence functionality
 */

require_once 'config/config.php';
require_once 'includes/AnalyticsService.php';
require_once 'includes/BadgeService.php';

echo "<h1>🧪 Phase 8 Test: Analytics & Business Intelligence</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Testing Database Tables...</h2>";
    
    // Test if Phase 8 tables exist
    $tables = [
        'analytics_events',
        'user_sessions',
        'daily_analytics_summary',
        'monthly_analytics_summary',
        'user_analytics_preferences'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT 1 FROM $table LIMIT 1");
            $existingTables[] = $table;
            echo "<p>✅ Table <strong>$table</strong> exists</p>";
        } catch (Exception $e) {
            $missingTables[] = $table;
            echo "<p>❌ Table <strong>$table</strong> missing</p>";
        }
    }
    
    // Test analytics indexes
    echo "<h2>Testing Analytics Indexes...</h2>";
    
    $indexes = [
        'idx_beer_checkins_year',
        'idx_beer_checkins_month',
        'idx_beer_ratings_year',
        'idx_user_activities_year'
    ];
    
    foreach ($indexes as $index) {
        try {
            $stmt = $conn->prepare("SHOW INDEX FROM beer_checkins WHERE Key_name = ?");
            $stmt->execute([$index]);
            if ($stmt->fetch()) {
                echo "<p>✅ Index <strong>$index</strong> exists</p>";
            } else {
                echo "<p>⚠️ Index <strong>$index</strong> not found</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error checking index <strong>$index</strong></p>";
        }
    }
    
    if (!empty($missingTables)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Missing Tables</h3>";
        echo "<p>Please run the Phase 8 setup script first:</p>";
        echo "<p><a href='setup-phase8.php' class='btn btn-primary'>Run Phase 8 Setup</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>Testing AnalyticsService...</h2>";
    
    // Test AnalyticsService
    $analyticsService = new AnalyticsService($conn);
    
    // Get test users
    $stmt = $conn->query("SELECT id FROM users LIMIT 3");
    $testUsers = $stmt->fetchAll();
    
    if (count($testUsers) > 0) {
        $userId = $testUsers[0]['id'];
        
        // Test user analytics
        echo "<p>Testing user analytics...</p>";
        $userAnalytics = $analyticsService->getUserAnalytics($userId, '1_year');
        if (!empty($userAnalytics)) {
            echo "<p>✅ User analytics loaded successfully</p>";
            echo "<p>   - Drinking patterns: " . (empty($userAnalytics['drinking_patterns']) ? 'No data' : 'Available') . "</p>";
            echo "<p>   - Beer preferences: " . (empty($userAnalytics['beer_preferences']) ? 'No data' : 'Available') . "</p>";
            echo "<p>   - Social analytics: " . (empty($userAnalytics['social_analytics']) ? 'No data' : 'Available') . "</p>";
        } else {
            echo "<p>⚠️ User analytics returned empty (normal for new users)</p>";
        }
        
        // Test year in review
        echo "<p>Testing year in review...</p>";
        $yearReview = $analyticsService->getYearInReview($userId, date('Y'));
        if ($yearReview !== false) {
            echo "<p>✅ Year in review service working</p>";
            echo "<p>   - Total check-ins: " . ($yearReview['total_checkins'] ?? 0) . "</p>";
            echo "<p>   - Unique beers: " . ($yearReview['unique_beers'] ?? 0) . "</p>";
        } else {
            echo "<p>❌ Year in review service failed</p>";
        }
        
        // Test year highlights
        echo "<p>Testing year highlights...</p>";
        $yearHighlights = $analyticsService->getYearHighlights($userId, date('Y'));
        if ($yearHighlights !== false) {
            echo "<p>✅ Year highlights service working</p>";
        } else {
            echo "<p>❌ Year highlights service failed</p>";
        }
        
    } else {
        echo "<p>❌ No test users found in database</p>";
    }
    
    echo "<h2>Testing Platform Analytics...</h2>";
    
    // Test platform analytics
    echo "<p>Testing platform statistics...</p>";
    $platformStats = $analyticsService->getPlatformStatistics('1_year');
    if (!empty($platformStats)) {
        echo "<p>✅ Platform statistics loaded successfully</p>";
        echo "<p>   - Total users: " . ($platformStats['total_users'] ?? 0) . "</p>";
        echo "<p>   - Total check-ins: " . ($platformStats['total_checkins'] ?? 0) . "</p>";
        echo "<p>   - Active breweries: " . ($platformStats['active_breweries'] ?? 0) . "</p>";
    } else {
        echo "<p>❌ Platform statistics failed to load</p>";
    }
    
    // Test user engagement metrics
    echo "<p>Testing user engagement metrics...</p>";
    $engagementMetrics = $analyticsService->getUserEngagementMetrics('1_year');
    if (!empty($engagementMetrics)) {
        echo "<p>✅ User engagement metrics loaded successfully</p>";
        echo "<p>   - Daily active users: " . ($engagementMetrics['dau'] ?? 0) . "</p>";
        echo "<p>   - Monthly active users: " . ($engagementMetrics['mau'] ?? 0) . "</p>";
        echo "<p>   - Retention rate: " . number_format($engagementMetrics['retention_rate'] ?? 0, 1) . "%</p>";
    } else {
        echo "<p>❌ User engagement metrics failed to load</p>";
    }
    
    // Test beer trends
    echo "<p>Testing beer trends...</p>";
    $beerTrends = $analyticsService->getBeerTrends('1_year');
    if (!empty($beerTrends)) {
        echo "<p>✅ Beer trends loaded successfully</p>";
        echo "<p>   - Popular styles: " . count($beerTrends['popular_styles'] ?? []) . " found</p>";
        echo "<p>   - Top rated beers: " . count($beerTrends['top_rated'] ?? []) . " found</p>";
    } else {
        echo "<p>❌ Beer trends failed to load</p>";
    }
    
    echo "<h2>Testing BadgeService Extensions...</h2>";
    
    // Test BadgeService year badges
    $badgeService = new BadgeService($conn);
    
    if (count($testUsers) > 0) {
        $userId = $testUsers[0]['id'];
        
        echo "<p>Testing year badges...</p>";
        $yearBadges = $badgeService->getYearBadges($userId, date('Y'));
        if ($yearBadges !== false) {
            echo "<p>✅ Year badges service working</p>";
            echo "<p>   - Badges earned this year: " . count($yearBadges) . "</p>";
        } else {
            echo "<p>❌ Year badges service failed</p>";
        }
    }
    
    echo "<h2>Testing Frontend Pages...</h2>";
    
    // Test frontend pages
    $pages = [
        '/beersty/user/statistics.php' => 'Enhanced Statistics Page',
        '/beersty/user/year-in-review.php' => 'Year in Review Page',
        '/beersty/admin/analytics.php' => 'Admin Analytics Dashboard'
    ];
    
    foreach ($pages as $page => $name) {
        $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . $page;
        echo "<p>Testing page: <strong>$name</strong> (<code>$page</code>)</p>";
        
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 302) {
                echo "<p>✅ Page accessible (redirects to login as expected)</p>";
            } elseif ($httpCode === 200) {
                echo "<p>✅ Page accessible (returns 200)</p>";
            } else {
                echo "<p>⚠️ Page returns HTTP $httpCode</p>";
            }
        } else {
            echo "<p>⚠️ cURL not available for page testing</p>";
        }
    }
    
    echo "<h2>Testing Database Performance...</h2>";
    
    // Test analytics query performance
    $performanceTests = [
        "User year analytics" => "SELECT COUNT(*) FROM beer_checkins WHERE user_id = '{$testUsers[0]['id']}' AND YEAR(created_at) = " . date('Y'),
        "Monthly activity summary" => "SELECT MONTH(created_at) as month, COUNT(*) as count FROM beer_checkins WHERE YEAR(created_at) = " . date('Y') . " GROUP BY MONTH(created_at)",
        "Popular beer styles" => "SELECT bs.name, COUNT(*) as count FROM beer_checkins bc JOIN beer_menu bm ON bc.beer_id = bm.id JOIN beer_styles bs ON bm.beer_style_id = bs.id GROUP BY bs.id LIMIT 10",
        "User engagement view" => "SELECT COUNT(*) FROM user_engagement_summary"
    ];
    
    foreach ($performanceTests as $testName => $query) {
        $startTime = microtime(true);
        try {
            $stmt = $conn->query($query);
            $result = $stmt->fetch();
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            echo "<p>✅ <strong>$testName</strong>: {$duration}ms</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>$testName</strong>: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Phase 8 Testing Complete!</h3>";
    echo "<p>Analytics & Business Intelligence functionality is working correctly.</p>";
    echo "</div>";
    
    echo "<h2>🚀 Next Steps</h2>";
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<ol>";
    echo "<li><strong>Login to test user features:</strong> <a href='/beersty/auth/login.php'>Login to your account</a></li>";
    echo "<li><strong>View enhanced analytics:</strong> <a href='/beersty/user/statistics.php'>Check your analytics dashboard</a></li>";
    echo "<li><strong>See your year in review:</strong> <a href='/beersty/user/year-in-review.php'>View your beer journey</a></li>";
    echo "<li><strong>Admin analytics:</strong> <a href='/beersty/admin/analytics.php'>Platform analytics dashboard</a></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>📊 Sample Data Recommendations</h2>";
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p>To see the full power of the analytics features, consider:</p>";
    echo "<ul>";
    echo "<li>Adding more beer check-ins with ratings</li>";
    echo "<li>Following other users to see social analytics</li>";
    echo "<li>Rating beers to see preference evolution</li>";
    echo "<li>Earning badges to see achievement timelines</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='setup-phase8.php'>← Run Setup Again</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

ol, ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
