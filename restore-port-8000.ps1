# RESTORE PORT 8000 CONFIGURATION
# Based on successful setup from social platform improvements session

Write-Host "🔄 RESTORING PORT 8000 CONFIGURATION" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow
Write-Host "Recreating the setup that was working during social platform improvements" -ForegroundColor Green

# Find XAMPP
$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $XamppPath) {
    Write-Host "❌ XAMPP not found!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Using XAMPP at: $XamppPath" -ForegroundColor Green

# Step 1: Stop current Apache
Write-Host ""
Write-Host "STEP 1: Stopping current Apache..." -ForegroundColor Cyan
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 3

# Step 2: Configure Apache for port 8000
Write-Host ""
Write-Host "STEP 2: Configuring Apache for port 8000..." -ForegroundColor Cyan

$HttpdConf = Join-Path $XamppPath "apache\conf\httpd.conf"
if (Test-Path $HttpdConf) {
    # Create backup
    $BackupConf = $HttpdConf + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
    Copy-Item $HttpdConf $BackupConf
    Write-Host "✅ Backup created: $BackupConf" -ForegroundColor Green
    
    # Read and modify httpd.conf
    $Content = Get-Content $HttpdConf
    $Modified = $false
    
    for ($i = 0; $i -lt $Content.Length; $i++) {
        $line = $Content[$i]
        
        # Change Listen port to 8000
        if ($line -match "^Listen\s+\d+") {
            $Content[$i] = "Listen 8000"
            Write-Host "✅ Changed Listen port to 8000" -ForegroundColor Green
            $Modified = $true
        }
        # Change ServerName port to 8000
        elseif ($line -match "^#?ServerName\s+localhost:\d+") {
            $Content[$i] = "ServerName localhost:8000"
            Write-Host "✅ Changed ServerName to localhost:8000" -ForegroundColor Green
            $Modified = $true
        }
        # Uncomment ServerName if it's commented
        elseif ($line -match "^#ServerName\s+localhost") {
            $Content[$i] = "ServerName localhost:8000"
            Write-Host "✅ Enabled ServerName localhost:8000" -ForegroundColor Green
            $Modified = $true
        }
    }
    
    if ($Modified) {
        $Content | Set-Content $HttpdConf -Encoding UTF8
        Write-Host "✅ Apache configured for port 8000" -ForegroundColor Green
    } else {
        Write-Host "⚠️ No changes needed - already configured?" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ httpd.conf not found at: $HttpdConf" -ForegroundColor Red
}

# Step 3: Ensure PDO is enabled (from our successful session)
Write-Host ""
Write-Host "STEP 3: Ensuring PDO MySQL is enabled..." -ForegroundColor Cyan

$PhpIni = Join-Path $XamppPath "php\php.ini"
if (Test-Path $PhpIni) {
    $Content = Get-Content $PhpIni
    $Modified = $false
    
    for ($i = 0; $i -lt $Content.Length; $i++) {
        $line = $Content[$i]
        
        if ($line -match "^;\s*extension=pdo_mysql") {
            $Content[$i] = "extension=pdo_mysql"
            Write-Host "✅ Enabled pdo_mysql" -ForegroundColor Green
            $Modified = $true
        }
        elseif ($line -match "^;\s*extension=mysqli") {
            $Content[$i] = "extension=mysqli"
            Write-Host "✅ Enabled mysqli" -ForegroundColor Green
            $Modified = $true
        }
    }
    
    if ($Modified) {
        $Content | Set-Content $PhpIni -Encoding UTF8
        Write-Host "✅ PDO configuration updated" -ForegroundColor Green
    } else {
        Write-Host "✅ PDO already enabled" -ForegroundColor Green
    }
}

# Step 4: Start Apache on port 8000
Write-Host ""
Write-Host "STEP 4: Starting Apache on port 8000..." -ForegroundColor Cyan

$ApacheExe = Join-Path $XamppPath "apache\bin\httpd.exe"
if (Test-Path $ApacheExe) {
    try {
        Start-Process $ApacheExe -WindowStyle Hidden
        Start-Sleep 5
        
        $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
        if ($Apache) {
            Write-Host "✅ Apache started successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Apache failed to start" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error starting Apache: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 5: Start MySQL
Write-Host ""
Write-Host "STEP 5: Starting MySQL..." -ForegroundColor Cyan

$MySQLBat = Join-Path $XamppPath "mysql_start.bat"
if (Test-Path $MySQLBat) {
    Start-Process $MySQLBat -WindowStyle Hidden
    Start-Sleep 5
    
    $MySQL = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
    if ($MySQL) {
        Write-Host "✅ MySQL started successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL failed to start" -ForegroundColor Red
    }
}

# Step 6: Test port 8000
Write-Host ""
Write-Host "STEP 6: Testing port 8000..." -ForegroundColor Cyan

try {
    $Response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ SUCCESS! Port 8000 is working!" -ForegroundColor Green
    Write-Host "   Status: $($Response.StatusCode)" -ForegroundColor Gray
    
    # Test project
    try {
        $ProjectResponse = Invoke-WebRequest -Uri "http://localhost:8000/beersty-lovable" -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ Project accessible at port 8000!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Project not found - check if files are in htdocs/beersty-lovable" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Port 8000 test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Open the working URLs
Write-Host ""
Write-Host "STEP 7: Opening port 8000 URLs..." -ForegroundColor Cyan

$BaseUrl = "http://localhost:8000"
$URLs = @(
    $BaseUrl,
    "$BaseUrl/beersty-lovable",
    "$BaseUrl/beersty-lovable/admin/user-management.php",
    "$BaseUrl/beersty-lovable/test-pdo-simple.php",
    "$BaseUrl/phpmyadmin"
)

foreach ($url in $URLs) {
    Write-Host "Opening: $url" -ForegroundColor White
    Start-Process $url
    Start-Sleep 1
}

# Step 8: Summary
Write-Host ""
Write-Host "🎉 PORT 8000 RESTORATION COMPLETE!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

Write-Host ""
Write-Host "YOUR RESTORED URLs (from successful session):" -ForegroundColor Yellow
Write-Host "  Main Site: http://localhost:8000/beersty-lovable" -ForegroundColor White
Write-Host "  User Management: http://localhost:8000/beersty-lovable/admin/user-management.php" -ForegroundColor White
Write-Host "  PDO Test: http://localhost:8000/beersty-lovable/test-pdo-simple.php" -ForegroundColor White
Write-Host "  phpMyAdmin: http://localhost:8000/phpmyadmin" -ForegroundColor White

Write-Host ""
Write-Host "🧪 TEST ADD USER FUNCTIONALITY:" -ForegroundColor Yellow
Write-Host "1. Go to User Management (should be opening now)" -ForegroundColor White
Write-Host "2. Click 'Add User' button" -ForegroundColor White
Write-Host "3. Fill in form and submit" -ForegroundColor White
Write-Host "4. Should work like it did during social platform session!" -ForegroundColor White

Write-Host ""
Write-Host "📝 FOR FUTURE REFERENCE:" -ForegroundColor Cyan
Write-Host "This setup uses port 8000 (not 80 or 8080)" -ForegroundColor White
Write-Host "Save this configuration for consistent development" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to exit"
