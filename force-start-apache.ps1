# FORCE START APACHE - Manual Apache startup

Write-Host "🔧 FORCE STARTING APACHE" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

# Find XAMPP
$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $XamppPath) {
    Write-Host "❌ XAMPP not found!" -ForegroundColor Red
    exit 1
}

Write-Host "Found XAMPP at: $XamppPath" -ForegroundColor Green

# Stop any existing Apache processes
Write-Host "Stopping existing Apache processes..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 2

# Stop IIS if running
Write-Host "Stopping IIS..." -ForegroundColor Yellow
Stop-Service -Name "W3SVC" -Force -ErrorAction SilentlyContinue

# Method 1: Try XAMPP Control Panel
Write-Host ""
Write-Host "METHOD 1: Starting XAMPP Control Panel..." -ForegroundColor Cyan
$XamppControl = Join-Path $XamppPath "xampp-control.exe"
Start-Process $XamppControl -WindowStyle Normal
Write-Host "✅ XAMPP Control Panel started" -ForegroundColor Green
Write-Host "👆 Click START next to Apache in the Control Panel!" -ForegroundColor Yellow

Start-Sleep 5

# Check if Apache started
$Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($Apache) {
    Write-Host "✅ Apache is now running!" -ForegroundColor Green
} else {
    Write-Host "❌ Apache still not running, trying alternative methods..." -ForegroundColor Red
    
    # Method 2: Try direct Apache start
    Write-Host ""
    Write-Host "METHOD 2: Starting Apache directly..." -ForegroundColor Cyan
    $ApacheExe = Join-Path $XamppPath "apache\bin\httpd.exe"
    if (Test-Path $ApacheExe) {
        try {
            Start-Process $ApacheExe -WindowStyle Hidden
            Start-Sleep 3
            
            $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
            if ($Apache) {
                Write-Host "✅ Apache started directly!" -ForegroundColor Green
            } else {
                Write-Host "❌ Direct start failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Error starting Apache directly: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Method 3: Try batch file
    if (-not $Apache) {
        Write-Host ""
        Write-Host "METHOD 3: Using Apache batch file..." -ForegroundColor Cyan
        $ApacheBat = Join-Path $XamppPath "apache_start.bat"
        if (Test-Path $ApacheBat) {
            Start-Process $ApacheBat -WindowStyle Normal
            Start-Sleep 3
            
            $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
            if ($Apache) {
                Write-Host "✅ Apache started via batch file!" -ForegroundColor Green
            } else {
                Write-Host "❌ Batch file start failed" -ForegroundColor Red
            }
        }
    }
}

# Final test
Write-Host ""
Write-Host "FINAL TEST..." -ForegroundColor Cyan

$Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($Apache) {
    Write-Host "✅ Apache is running!" -ForegroundColor Green
    
    # Test localhost
    Start-Sleep 2
    try {
        $Response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ localhost is responding!" -ForegroundColor Green
        Write-Host "🎉 SUCCESS! Opening localhost..." -ForegroundColor Green
        Start-Process "http://localhost"
        Start-Process "http://localhost/beersty-lovable"
    } catch {
        Write-Host "⚠️ Apache running but localhost not responding" -ForegroundColor Yellow
        Write-Host "Try: http://localhost:8080" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Apache still not running!" -ForegroundColor Red
    Write-Host ""
    Write-Host "TROUBLESHOOTING STEPS:" -ForegroundColor Yellow
    Write-Host "1. Run PowerShell as Administrator" -ForegroundColor White
    Write-Host "2. Check XAMPP logs for errors" -ForegroundColor White
    Write-Host "3. Try port 8080: .\beersty-dev-start.ps1 -Port8080" -ForegroundColor White
    Write-Host "4. Restart computer and try again" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
