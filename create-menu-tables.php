<?php
// Create Menu Management Tables
require_once 'config/config.php';

echo "=== Creating Menu Management Tables ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Create beer_styles table
    echo "Creating beer_styles table..." . PHP_EOL;
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS beer_styles (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            category VARCHAR(50) NOT NULL,
            description TEXT,
            abv_min DECIMAL(3,1) DEFAULT NULL,
            abv_max DECIMAL(3,1) DEFAULT NULL,
            ibu_min INT DEFAULT NULL,
            ibu_max INT DEFAULT NULL,
            srm_min INT DEFAULT NULL,
            srm_max INT DEFAULT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✓ beer_styles table created" . PHP_EOL;
    
    // Create brewery_beers table
    echo "Creating brewery_beers table..." . PHP_EOL;
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS brewery_beers (
            id VARCHAR(36) PRIMARY KEY,
            brewery_id VARCHAR(36) NOT NULL,
            name VARCHAR(100) NOT NULL,
            style_id VARCHAR(36),
            description TEXT,
            abv DECIMAL(3,1),
            ibu INT,
            srm INT,
            price DECIMAL(6,2),
            availability ENUM('year_round', 'seasonal', 'limited', 'one_off') DEFAULT 'year_round',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
            FOREIGN KEY (style_id) REFERENCES beer_styles(id) ON DELETE SET NULL
        )
    ");
    echo "✓ brewery_beers table created" . PHP_EOL;
    
    // Create food_categories table
    echo "Creating food_categories table..." . PHP_EOL;
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS food_categories (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✓ food_categories table created" . PHP_EOL;
    
    // Create brewery_food table
    echo "Creating brewery_food table..." . PHP_EOL;
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS brewery_food (
            id VARCHAR(36) PRIMARY KEY,
            brewery_id VARCHAR(36) NOT NULL,
            category_id VARCHAR(36),
            name VARCHAR(100) NOT NULL,
            description TEXT,
            price DECIMAL(6,2),
            ingredients TEXT,
            allergens TEXT,
            is_vegetarian BOOLEAN DEFAULT 0,
            is_vegan BOOLEAN DEFAULT 0,
            is_gluten_free BOOLEAN DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (brewery_id) REFERENCES breweries(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES food_categories(id) ON DELETE SET NULL
        )
    ");
    echo "✓ brewery_food table created" . PHP_EOL;
    
    // Insert sample beer styles
    echo "\nAdding sample beer styles..." . PHP_EOL;
    $styles = [
        ['American IPA', 'IPA', 'Hoppy American-style India Pale Ale', 5.5, 7.5, 40, 70],
        ['New England IPA', 'IPA', 'Hazy, juicy IPA with tropical hop flavors', 6.0, 8.0, 35, 65],
        ['Imperial Stout', 'Stout', 'Strong, dark beer with rich chocolate and coffee notes', 8.0, 12.0, 20, 80],
        ['Milk Stout', 'Stout', 'Sweet stout brewed with lactose', 4.0, 6.0, 15, 40],
        ['American Wheat', 'Wheat', 'Light, refreshing wheat beer', 4.0, 5.5, 10, 30],
        ['Hefeweizen', 'Wheat', 'Traditional German wheat beer', 4.5, 5.5, 8, 15],
        ['American Lager', 'Lager', 'Clean, crisp American-style lager', 4.0, 5.0, 8, 18],
        ['Pilsner', 'Lager', 'Light, hoppy Czech-style lager', 4.5, 5.5, 25, 45],
        ['American Pale Ale', 'Pale Ale', 'Balanced pale ale with American hops', 4.5, 6.2, 30, 50],
        ['Session IPA', 'IPA', 'Lower alcohol IPA with full hop flavor', 3.0, 5.0, 40, 60],
        ['Porter', 'Porter', 'Dark beer with roasted malt character', 4.0, 6.5, 18, 35],
        ['Brown Ale', 'Ale', 'Malty brown ale with caramel notes', 4.0, 6.0, 15, 25],
        ['Saison', 'Farmhouse', 'Belgian farmhouse ale with spicy yeast character', 5.0, 7.0, 20, 35],
        ['Sour Ale', 'Sour', 'Tart, acidic beer with fruit flavors', 3.0, 8.0, 5, 30],
        ['Amber Ale', 'Ale', 'Malty amber-colored ale', 4.5, 6.0, 20, 40]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO beer_styles (id, name, category, description, abv_min, abv_max, ibu_min, ibu_max, is_active, created_at) 
        VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, 1, NOW())
    ");
    
    foreach ($styles as $style) {
        $stmt->execute($style);
        echo "✓ Added beer style: " . $style[0] . PHP_EOL;
    }
    
    // Insert sample food categories
    echo "\nAdding sample food categories..." . PHP_EOL;
    $categories = [
        ['Appetizers', 'Small plates and starters', 1],
        ['Salads', 'Fresh salads and greens', 2],
        ['Sandwiches', 'Burgers, sandwiches, and wraps', 3],
        ['Entrees', 'Main course dishes', 4],
        ['Pizza', 'Wood-fired and specialty pizzas', 5],
        ['Desserts', 'Sweet treats and desserts', 6],
        ['Snacks', 'Bar snacks and small bites', 7]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO food_categories (id, name, description, sort_order, is_active, created_at) 
        VALUES (UUID(), ?, ?, ?, 1, NOW())
    ");
    
    foreach ($categories as $category) {
        $stmt->execute($category);
        echo "✓ Added food category: " . $category[0] . PHP_EOL;
    }
    
    echo "\n=== Menu Management Tables Created Successfully ===" . PHP_EOL;
    echo "✓ beer_styles table with " . count($styles) . " styles" . PHP_EOL;
    echo "✓ brewery_beers table for beer menus" . PHP_EOL;
    echo "✓ food_categories table with " . count($categories) . " categories" . PHP_EOL;
    echo "✓ brewery_food table for food menus" . PHP_EOL;
    echo "\nYou can now use the menu management system!" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
