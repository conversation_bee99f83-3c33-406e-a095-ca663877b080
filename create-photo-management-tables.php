<?php
// Create Photo Management System Tables
require_once 'config/config.php';

echo "=== Creating Photo Management System Tables ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Create photo_albums table
    echo "Creating photo_albums table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS photo_albums (
            id VARCHAR(36) PRIMARY KEY,
            owner_type ENUM('user', 'place') NOT NULL,
            owner_id VARCHAR(36) NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            cover_photo_id VARCHAR(36),
            is_public BOOLEAN DEFAULT 1,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_owner (owner_type, owner_id),
            INDEX idx_public (is_public),
            INDEX idx_sort_order (sort_order)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created photo_albums table" . PHP_EOL;
    
    // Create photos table
    echo "Creating photos table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS photos (
            id VARCHAR(36) PRIMARY KEY,
            album_id VARCHAR(36),
            owner_type ENUM('user', 'place') NOT NULL,
            owner_id VARCHAR(36) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            title VARCHAR(200),
            description TEXT,
            alt_text VARCHAR(255),
            file_size INT,
            mime_type VARCHAR(100),
            width INT,
            height INT,
            is_profile_photo BOOLEAN DEFAULT 0,
            is_cover_photo BOOLEAN DEFAULT 0,
            is_public BOOLEAN DEFAULT 1,
            sort_order INT DEFAULT 0,
            uploaded_by VARCHAR(36),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (album_id) REFERENCES photo_albums(id) ON DELETE SET NULL,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_album (album_id),
            INDEX idx_owner (owner_type, owner_id),
            INDEX idx_profile (is_profile_photo),
            INDEX idx_cover (is_cover_photo),
            INDEX idx_public (is_public),
            INDEX idx_sort_order (sort_order),
            INDEX idx_created (created_at)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created photos table" . PHP_EOL;
    
    // Create photo_tags table
    echo "Creating photo_tags table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS photo_tags (
            id VARCHAR(36) PRIMARY KEY,
            photo_id VARCHAR(36) NOT NULL,
            tag_name VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
            INDEX idx_photo (photo_id),
            INDEX idx_tag (tag_name),
            UNIQUE KEY unique_photo_tag (photo_id, tag_name)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created photo_tags table" . PHP_EOL;
    
    // Create photo_likes table
    echo "Creating photo_likes table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS photo_likes (
            id VARCHAR(36) PRIMARY KEY,
            photo_id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_photo (photo_id),
            INDEX idx_user (user_id),
            UNIQUE KEY unique_photo_like (photo_id, user_id)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created photo_likes table" . PHP_EOL;
    
    // Create photo_comments table
    echo "Creating photo_comments table..." . PHP_EOL;
    $sql = "
        CREATE TABLE IF NOT EXISTS photo_comments (
            id VARCHAR(36) PRIMARY KEY,
            photo_id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36) NOT NULL,
            comment TEXT NOT NULL,
            is_approved BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_photo (photo_id),
            INDEX idx_user (user_id),
            INDEX idx_approved (is_approved),
            INDEX idx_created (created_at)
        )
    ";
    $pdo->exec($sql);
    echo "✓ Created photo_comments table" . PHP_EOL;
    
    // Add foreign key constraint for cover_photo_id in albums
    echo "Adding foreign key constraints..." . PHP_EOL;
    try {
        $pdo->exec("ALTER TABLE photo_albums ADD FOREIGN KEY (cover_photo_id) REFERENCES photos(id) ON DELETE SET NULL");
        echo "✓ Added cover_photo_id foreign key" . PHP_EOL;
    } catch (Exception $e) {
        echo "Note: cover_photo_id foreign key may already exist" . PHP_EOL;
    }
    
    // Create default albums for existing users and places
    echo "Creating default albums..." . PHP_EOL;
    
    // Default albums for users
    $stmt = $pdo->query("SELECT id FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($users as $user_id) {
        // Check if user already has albums
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'user' AND owner_id = ?");
        $stmt->execute([$user_id]);
        $album_count = $stmt->fetchColumn();
        
        if ($album_count == 0) {
            // Create default albums
            $default_albums = [
                ['Profile Photos', 'Profile and avatar photos', 1],
                ['Cover Photos', 'Cover and banner photos', 2],
                ['Personal', 'Personal photo collection', 3]
            ];
            
            foreach ($default_albums as [$name, $description, $sort_order]) {
                $stmt = $pdo->prepare("
                    INSERT INTO photo_albums (id, owner_type, owner_id, name, description, sort_order, created_at) 
                    VALUES (UUID(), 'user', ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $name, $description, $sort_order]);
            }
        }
    }
    
    // Default albums for places
    $stmt = $pdo->query("SELECT id FROM breweries");
    $places = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($places as $place_id) {
        // Check if place already has albums
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'place' AND owner_id = ?");
        $stmt->execute([$place_id]);
        $album_count = $stmt->fetchColumn();
        
        if ($album_count == 0) {
            // Create default albums
            $default_albums = [
                ['Profile Photos', 'Logo and main profile photos', 1],
                ['Interior', 'Interior photos of the establishment', 2],
                ['Exterior', 'Exterior and building photos', 3],
                ['Food & Drinks', 'Menu items and beverages', 4],
                ['Events', 'Special events and gatherings', 5],
                ['Staff', 'Team and staff photos', 6]
            ];
            
            foreach ($default_albums as [$name, $description, $sort_order]) {
                $stmt = $pdo->prepare("
                    INSERT INTO photo_albums (id, owner_type, owner_id, name, description, sort_order, created_at) 
                    VALUES (UUID(), 'place', ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$place_id, $name, $description, $sort_order]);
            }
        }
    }
    
    // Show statistics
    echo "\n=== Photo Management System Statistics ===" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'user'");
    $user_albums = $stmt->fetchColumn();
    echo "User albums created: $user_albums" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photo_albums WHERE owner_type = 'place'");
    $place_albums = $stmt->fetchColumn();
    echo "Place albums created: $place_albums" . PHP_EOL;
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM photos");
    $photos = $stmt->fetchColumn();
    echo "Total photos: $photos" . PHP_EOL;
    
    echo "\n=== Photo Management System Setup Complete ===" . PHP_EOL;
    echo "✓ All tables created successfully" . PHP_EOL;
    echo "✓ Default albums created for users and places" . PHP_EOL;
    echo "✓ Foreign key relationships established" . PHP_EOL;
    echo "✓ Indexes created for performance" . PHP_EOL;
    
    echo "\nFeatures available:" . PHP_EOL;
    echo "• Photo albums with cover photos" . PHP_EOL;
    echo "• Photo uploads with metadata" . PHP_EOL;
    echo "• Photo tagging system" . PHP_EOL;
    echo "• Photo likes and comments" . PHP_EOL;
    echo "• Profile and cover photo management" . PHP_EOL;
    echo "• Public/private photo controls" . PHP_EOL;
    echo "• Sort ordering for albums and photos" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
