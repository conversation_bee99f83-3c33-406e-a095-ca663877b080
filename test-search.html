<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Test - Beersty</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/location-search.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Location Search Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Input</h3>
                <div class="input-group location-input-container">
                    <span class="input-group-text">
                        <i class="fas fa-map-marker-alt"></i>
                    </span>
                    <input type="text" 
                           id="test-location" 
                           name="location" 
                           class="form-control" 
                           placeholder="Type a city name..."
                           autocomplete="off">
                </div>
                
                <div class="mt-3">
                    <button id="test-api" class="btn btn-primary">Test API Directly</button>
                    <button id="test-geo" class="btn btn-success">Test Geolocation</button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Debug Output</h3>
                <div id="debug-output" class="border p-3" style="height: 400px; overflow-y: auto; background: #f8f9fa;">
                    <p>Debug information will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<div><strong>[${timestamp}]</strong> ${message}</div>`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        // Override console.log for this page
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            log('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            log('<span style="color: red;">ERROR: ' + args.join(' ') + '</span>');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log('<span style="color: orange;">WARN: ' + args.join(' ') + '</span>');
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, initializing search...');
            
            // Initialize smart location search
            const testInput = document.getElementById('test-location');
            
            if (typeof Beersty !== 'undefined' && Beersty.components) {
                log('Beersty object found, setting up autocomplete...');
                Beersty.components.setupLocationAutocomplete(testInput);
                log('Autocomplete setup complete');
            } else {
                log('<span style="color: red;">ERROR: Beersty object not found!</span>');
            }
            
            // Test API button
            document.getElementById('test-api').addEventListener('click', function() {
                log('Testing API directly...');
                fetch('/api/location-search.php?q=san&limit=3')
                    .then(response => {
                        log(`API Response Status: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        log('API Response Data: ' + JSON.stringify(data, null, 2));
                    })
                    .catch(error => {
                        log('<span style="color: red;">API Error: ' + error.message + '</span>');
                    });
            });
            
            // Test geolocation button
            document.getElementById('test-geo').addEventListener('click', function() {
                log('Testing geolocation...');
                
                if (!navigator.geolocation) {
                    log('<span style="color: red;">Geolocation not supported</span>');
                    return;
                }
                
                navigator.geolocation.getCurrentPosition(
                    position => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        const accuracy = position.coords.accuracy;
                        
                        log(`Geolocation Success: ${lat}, ${lng} (accuracy: ${accuracy}m)`);
                        
                        // Test API with geolocation
                        fetch(`/api/location-search.php?q=chi&lat=${lat}&lng=${lng}&limit=5`)
                            .then(response => response.json())
                            .then(data => {
                                log('API with Geolocation: ' + JSON.stringify(data, null, 2));
                            })
                            .catch(error => {
                                log('<span style="color: red;">API with Geo Error: ' + error.message + '</span>');
                            });
                    },
                    error => {
                        log('<span style="color: red;">Geolocation Error: ' + error.message + '</span>');
                    },
                    {
                        timeout: 15000,
                        enableHighAccuracy: true,
                        maximumAge: 60000
                    }
                );
            });
            
            // Test input events
            testInput.addEventListener('input', function() {
                log(`Input event: "${this.value}"`);
            });
            
            testInput.addEventListener('focus', function() {
                log('Input focused');
            });
        });
    </script>
</body>
</html>
