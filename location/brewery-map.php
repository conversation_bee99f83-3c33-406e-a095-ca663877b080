<?php
require_once '../config/config.php';

$pageTitle = 'Brewery Map - ' . APP_NAME;
$additionalCSS = ['/assets/css/map.css'];

$userLocation = null;
$breweries = [];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get all breweries with location data
    $stmt = $conn->prepare("
        SELECT b.*, 
               COUNT(DISTINCT bm.id) as beer_count,
               AVG(bm.average_rating) as avg_brewery_rating,
               COUNT(DISTINCT bc.id) as total_checkins
        FROM breweries b
        LEFT JOIN beer_menu bm ON b.id = bm.brewery_id AND bm.available = 1
        LEFT JOIN beer_checkins bc ON b.id = bc.brewery_id
        WHERE b.latitude IS NOT NULL AND b.longitude IS NOT NULL
        GROUP BY b.id
        ORDER BY b.name ASC
    ");
    $stmt->execute();
    $breweries = $stmt->fetchAll();
    
    // Get user's location from recent check-ins if logged in
    if (isLoggedIn()) {
        $user = getCurrentUser();
        $stmt = $conn->prepare("
            SELECT checkin_latitude, checkin_longitude 
            FROM beer_checkins 
            WHERE user_id = ? AND checkin_latitude IS NOT NULL 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$user['id']]);
        $lastLocation = $stmt->fetch();
        
        if ($lastLocation) {
            $userLocation = [
                'lat' => (float)$lastLocation['checkin_latitude'],
                'lng' => (float)$lastLocation['checkin_longitude']
            ];
        }
    }
    
} catch (Exception $e) {
    error_log("Brewery map error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Error loading brewery map.';
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 fw-bold text-primary mb-2">
                <i class="fas fa-map-marked-alt me-3"></i>Brewery Map
            </h1>
            <p class="lead text-muted">
                Discover breweries near you and around the world
            </p>
        </div>
    </div>
    
    <!-- Map Controls -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Location Search -->
                        <div class="col-md-6">
                            <label for="locationSearch" class="form-label">
                                <i class="fas fa-search me-1"></i>Search Location
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="locationSearch" 
                                       placeholder="Enter city, state, or address...">
                                <button class="btn btn-outline-secondary" type="button" id="searchLocation">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Current Location -->
                        <div class="col-md-3">
                            <label class="form-label">
                                <i class="fas fa-crosshairs me-1"></i>Your Location
                            </label>
                            <button class="btn btn-primary w-100" id="findMyLocation">
                                <i class="fas fa-location-arrow me-2"></i>Find Me
                            </button>
                        </div>
                        
                        <!-- Distance Filter -->
                        <div class="col-md-3">
                            <label for="distanceFilter" class="form-label">
                                <i class="fas fa-ruler me-1"></i>Distance
                            </label>
                            <select class="form-select" id="distanceFilter">
                                <option value="">All Distances</option>
                                <option value="5">Within 5 miles</option>
                                <option value="10">Within 10 miles</option>
                                <option value="25">Within 25 miles</option>
                                <option value="50">Within 50 miles</option>
                                <option value="100">Within 100 miles</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Map Legend -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>Map Legend
                    </h6>
                    <div class="legend-items">
                        <div class="legend-item">
                            <span class="legend-marker brewery-marker"></span>
                            <span>Brewery Location</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker user-marker"></span>
                            <span>Your Location</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker popular-marker"></span>
                            <span>Popular Brewery (10+ check-ins)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Map Container -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body p-0">
                    <div id="breweryMap" style="height: 600px; width: 100%;"></div>
                </div>
            </div>
        </div>
        
        <!-- Brewery List -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Nearby Breweries
                        <span class="badge bg-primary ms-2" id="breweryCount"><?php echo count($breweries); ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="brewery-list" id="breweryList">
                        <?php foreach ($breweries as $brewery): ?>
                            <div class="brewery-item" data-brewery-id="<?php echo $brewery['id']; ?>"
                                 data-lat="<?php echo $brewery['latitude']; ?>" 
                                 data-lng="<?php echo $brewery['longitude']; ?>">
                                <div class="brewery-info">
                                    <h6 class="brewery-name">
                                        <a href="/breweries/detail.php?id=<?php echo $brewery['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($brewery['name']); ?>
                                        </a>
                                    </h6>
                                    <p class="brewery-location text-muted mb-1">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($brewery['city']); ?><?php if ($brewery['state']): ?>, <?php echo htmlspecialchars($brewery['state']); ?><?php endif; ?>
                                    </p>
                                    <div class="brewery-stats">
                                        <small class="text-muted">
                                            <i class="fas fa-beer me-1"></i><?php echo number_format($brewery['beer_count']); ?> beers
                                            <?php if ($brewery['avg_brewery_rating']): ?>
                                                <i class="fas fa-star ms-2 me-1 text-warning"></i><?php echo number_format($brewery['avg_brewery_rating'], 1); ?>
                                            <?php endif; ?>
                                            <i class="fas fa-map-marker-alt ms-2 me-1"></i><?php echo number_format($brewery['total_checkins']); ?> check-ins
                                        </small>
                                    </div>
                                    <div class="brewery-distance text-primary fw-bold" style="display: none;">
                                        <i class="fas fa-route me-1"></i><span class="distance-text"></span>
                                    </div>
                                </div>
                                <div class="brewery-actions">
                                    <button class="btn btn-sm btn-outline-primary view-on-map" 
                                            data-brewery-id="<?php echo $brewery['id']; ?>">
                                        <i class="fas fa-map me-1"></i>View
                                    </button>
                                    <a href="/social/checkin.php?brewery_id=<?php echo $brewery['id']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-map-marker-alt me-1"></i>Check In
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Brewery Info Modal -->
<div class="modal fade" id="breweryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="breweryModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="breweryModalBody">
                <!-- Brewery details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="#" class="btn btn-primary" id="breweryModalCheckIn">
                    <i class="fas fa-map-marker-alt me-2"></i>Check In Here
                </a>
                <a href="#" class="btn btn-outline-primary" id="breweryModalView">
                    <i class="fas fa-eye me-2"></i>View Details
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize map data
const breweries = <?php echo json_encode($breweries); ?>;
const userLocation = <?php echo json_encode($userLocation); ?>;

let map;
let userMarker;
let breweryMarkers = [];
let infoWindow;

// Initialize Google Maps
function initMap() {
    // Default center (US center)
    const defaultCenter = { lat: 39.8283, lng: -98.5795 };
    const center = userLocation || defaultCenter;
    
    map = new google.maps.Map(document.getElementById('breweryMap'), {
        zoom: userLocation ? 12 : 4,
        center: center,
        styles: [
            {
                featureType: 'poi.business',
                stylers: [{ visibility: 'off' }]
            }
        ]
    });
    
    infoWindow = new google.maps.InfoWindow();
    
    // Add user location marker if available
    if (userLocation) {
        addUserMarker(userLocation);
    }
    
    // Add brewery markers
    addBreweryMarkers();
    
    // Update distances if user location is available
    if (userLocation) {
        updateBreweryDistances();
    }
}

function addUserMarker(location) {
    userMarker = new google.maps.Marker({
        position: location,
        map: map,
        title: 'Your Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#007bff">
                    <circle cx="12" cy="12" r="8" stroke="white" stroke-width="2"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(24, 24)
        }
    });
}

function addBreweryMarkers() {
    breweries.forEach(brewery => {
        const position = {
            lat: parseFloat(brewery.latitude),
            lng: parseFloat(brewery.longitude)
        };
        
        const isPopular = brewery.total_checkins >= 10;
        
        const marker = new google.maps.Marker({
            position: position,
            map: map,
            title: brewery.name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="${isPopular ? '#ffc107' : '#28a745'}">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" stroke="white" stroke-width="1"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(24, 24)
            }
        });
        
        marker.addListener('click', () => {
            showBreweryInfo(brewery);
        });
        
        breweryMarkers.push({
            marker: marker,
            brewery: brewery
        });
    });
}

function showBreweryInfo(brewery) {
    const content = `
        <div class="brewery-popup">
            <h6>${brewery.name}</h6>
            <p class="mb-2">
                <i class="fas fa-map-marker-alt me-1"></i>
                ${brewery.city}${brewery.state ? ', ' + brewery.state : ''}
            </p>
            <div class="brewery-stats mb-2">
                <small>
                    <i class="fas fa-beer me-1"></i>${brewery.beer_count} beers
                    ${brewery.avg_brewery_rating ? `<i class="fas fa-star ms-2 me-1 text-warning"></i>${parseFloat(brewery.avg_brewery_rating).toFixed(1)}` : ''}
                    <i class="fas fa-map-marker-alt ms-2 me-1"></i>${brewery.total_checkins} check-ins
                </small>
            </div>
            <div class="brewery-actions">
                <a href="/breweries/detail.php?id=${brewery.id}" class="btn btn-sm btn-outline-primary me-2">
                    <i class="fas fa-eye me-1"></i>View Details
                </a>
                <a href="/social/checkin.php?brewery_id=${brewery.id}" class="btn btn-sm btn-primary">
                    <i class="fas fa-map-marker-alt me-1"></i>Check In
                </a>
            </div>
        </div>
    `;
    
    infoWindow.setContent(content);
    infoWindow.open(map, breweryMarkers.find(bm => bm.brewery.id === brewery.id).marker);
}

function updateBreweryDistances() {
    if (!userLocation) return;
    
    const breweryItems = document.querySelectorAll('.brewery-item');
    const distances = [];
    
    breweryItems.forEach(item => {
        const lat = parseFloat(item.dataset.lat);
        const lng = parseFloat(item.dataset.lng);
        const distance = calculateDistance(userLocation.lat, userLocation.lng, lat, lng);
        
        distances.push({
            element: item,
            distance: distance,
            breweryId: item.dataset.breweryId
        });
        
        const distanceElement = item.querySelector('.brewery-distance');
        const distanceText = item.querySelector('.distance-text');
        
        if (distanceElement && distanceText) {
            distanceText.textContent = distance.toFixed(1) + ' miles away';
            distanceElement.style.display = 'block';
        }
    });
    
    // Sort by distance
    distances.sort((a, b) => a.distance - b.distance);
    
    const breweryList = document.getElementById('breweryList');
    distances.forEach(item => {
        breweryList.appendChild(item.element);
    });
}

function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 3959; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Find my location
    document.getElementById('findMyLocation').addEventListener('click', function() {
        if (navigator.geolocation) {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Finding...';
            
            navigator.geolocation.getCurrentPosition(
                position => {
                    const location = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    map.setCenter(location);
                    map.setZoom(12);
                    
                    if (userMarker) {
                        userMarker.setMap(null);
                    }
                    addUserMarker(location);
                    
                    // Update global userLocation
                    window.userLocation = location;
                    updateBreweryDistances();
                    
                    this.innerHTML = '<i class="fas fa-check me-2"></i>Found!';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-location-arrow me-2"></i>Find Me';
                    }, 2000);
                },
                error => {
                    alert('Unable to get your location');
                    this.innerHTML = '<i class="fas fa-location-arrow me-2"></i>Find Me';
                }
            );
        }
    });
    
    // View on map buttons
    document.querySelectorAll('.view-on-map').forEach(button => {
        button.addEventListener('click', function() {
            const breweryId = this.dataset.breweryId;
            const brewery = breweries.find(b => b.id === breweryId);
            
            if (brewery) {
                const position = {
                    lat: parseFloat(brewery.latitude),
                    lng: parseFloat(brewery.longitude)
                };
                
                map.setCenter(position);
                map.setZoom(15);
                
                // Find and trigger the marker
                const breweryMarker = breweryMarkers.find(bm => bm.brewery.id === breweryId);
                if (breweryMarker) {
                    google.maps.event.trigger(breweryMarker.marker, 'click');
                }
            }
        });
    });
    
    // Distance filter
    document.getElementById('distanceFilter').addEventListener('change', function() {
        const maxDistance = parseFloat(this.value);
        
        if (!userLocation || !maxDistance) {
            // Show all breweries
            document.querySelectorAll('.brewery-item').forEach(item => {
                item.style.display = 'block';
            });
            breweryMarkers.forEach(bm => {
                bm.marker.setVisible(true);
            });
            return;
        }
        
        let visibleCount = 0;
        
        document.querySelectorAll('.brewery-item').forEach(item => {
            const lat = parseFloat(item.dataset.lat);
            const lng = parseFloat(item.dataset.lng);
            const distance = calculateDistance(userLocation.lat, userLocation.lng, lat, lng);
            
            if (distance <= maxDistance) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });
        
        breweryMarkers.forEach(bm => {
            const distance = calculateDistance(
                userLocation.lat, userLocation.lng,
                parseFloat(bm.brewery.latitude),
                parseFloat(bm.brewery.longitude)
            );
            bm.marker.setVisible(distance <= maxDistance);
        });
        
        document.getElementById('breweryCount').textContent = visibleCount;
    });
});
</script>

<!-- Google Maps API -->
<script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap">
</script>

<?php include '../includes/footer.php'; ?>
