<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Visibility Test - Beersty</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/location-search.css" rel="stylesheet">
    <style>
        body {
            padding: 2rem;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-dropdown {
            position: relative;
            margin-bottom: 2rem;
        }
        .test-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
        }
        .manual-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Text Visibility Test</h1>
        <p>This page tests the visibility of dropdown text in different scenarios.</p>
        
        <div class="test-dropdown">
            <h3>Test 1: Manual Dropdown</h3>
            <input type="text" class="test-input" placeholder="This is a manual test dropdown">
            <div class="manual-suggestions">
                <div class="location-suggestion">
                    <i class="fas fa-city me-2"></i>
                    <div class="flex-grow-1">
                        <div>New York, NY</div>
                        <small>Major city</small>
                    </div>
                    <small class="ms-auto">150.2 mi</small>
                </div>
                <div class="location-suggestion">
                    <i class="fas fa-home me-2"></i>
                    <div class="flex-grow-1">
                        <div>Taylor, MI</div>
                        <small>Small city</small>
                    </div>
                    <small class="ms-auto">5.1 mi</small>
                </div>
                <div class="location-suggestion">
                    <i class="fas fa-beer me-2"></i>
                    <div class="flex-grow-1">
                        <div>Plymouth, MI</div>
                        <small>Local brewery</small>
                    </div>
                    <small class="ms-auto">8.3 mi</small>
                </div>
            </div>
        </div>
        
        <div class="test-dropdown">
            <h3>Test 2: Live Search</h3>
            <div class="input-group location-input-container">
                <span class="input-group-text">
                    <i class="fas fa-map-marker-alt"></i>
                </span>
                <input type="text" 
                       id="live-test-input" 
                       name="location" 
                       class="form-control" 
                       placeholder="Type 'taylor' or 'plymouth' to test live search"
                       autocomplete="off">
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Expected Results:</h3>
            <ul>
                <li><strong>Text Color:</strong> Black (#212529) on white background</li>
                <li><strong>Icons:</strong> Blue (#0d6efd)</li>
                <li><strong>Secondary Text:</strong> Gray (#6c757d)</li>
                <li><strong>Hover:</strong> Light gray background with maintained text contrast</li>
            </ul>
        </div>
        
        <div class="mt-4">
            <button id="force-show" class="btn btn-primary">Force Show Test Dropdown</button>
            <button id="test-api" class="btn btn-success">Test API Call</button>
        </div>
        
        <div class="mt-4">
            <h3>Debug Info:</h3>
            <div id="debug-info" class="border p-3 bg-white">
                <p>Debug information will appear here...</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
    <script>
        const debugInfo = document.getElementById('debug-info');
        
        function log(message) {
            debugInfo.innerHTML += `<div>${message}</div>`;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded');
            
            // Initialize smart location search for live test
            const liveInput = document.getElementById('live-test-input');
            
            if (typeof Beersty !== 'undefined' && Beersty.components) {
                log('Initializing smart search...');
                Beersty.components.setupLocationAutocomplete(liveInput);
                log('Smart search initialized');
            } else {
                log('ERROR: Beersty object not found');
            }
            
            // Force show test dropdown
            document.getElementById('force-show').addEventListener('click', function() {
                const manualDropdown = document.querySelector('.manual-suggestions');
                manualDropdown.style.display = manualDropdown.style.display === 'block' ? 'none' : 'block';
                log('Toggled manual dropdown visibility');
            });
            
            // Test API call
            document.getElementById('test-api').addEventListener('click', function() {
                log('Testing API call...');
                fetch('/api/location-search.php?q=taylor&limit=3')
                    .then(response => response.json())
                    .then(data => {
                        log('API Response: ' + JSON.stringify(data, null, 2));
                    })
                    .catch(error => {
                        log('API Error: ' + error.message);
                    });
            });
            
            // Add click handlers to manual suggestions
            document.querySelectorAll('.manual-suggestions .location-suggestion').forEach(item => {
                item.addEventListener('click', function() {
                    log('Clicked: ' + this.querySelector('div div').textContent);
                });
            });
        });
    </script>
</body>
</html>
