<?php
/**
 * Check PHP Extensions
 */

echo "<h1>PHP Extensions Check</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;} .success{background:#d4edda;color:#155724;padding:10px;border-radius:5px;margin:10px 0;} .error{background:#f8d7da;color:#721c24;padding:10px;border-radius:5px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:5px;margin:10px 0;}</style>";

echo "<h2>1. PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>PHP SAPI:</strong> " . php_sapi_name() . "</p>";
echo "<p><strong>PHP Configuration File:</strong> " . php_ini_loaded_file() . "</p>";

echo "<h2>2. PDO Extensions Check</h2>";

// Check if PDO is available
if (extension_loaded('pdo')) {
    echo "<div class='success'>✅ PDO extension is loaded</div>";
    
    // Get available PDO drivers
    $drivers = PDO::getAvailableDrivers();
    echo "<p><strong>Available PDO drivers:</strong> " . implode(', ', $drivers) . "</p>";
    
    if (in_array('mysql', $drivers)) {
        echo "<div class='success'>✅ PDO MySQL driver is available</div>";
    } else {
        echo "<div class='error'>❌ PDO MySQL driver is NOT available</div>";
        echo "<div class='warning'>";
        echo "<h3>🔧 How to Fix:</h3>";
        echo "<ol>";
        echo "<li>Find your php.ini file: " . (php_ini_loaded_file() ?: 'Not found') . "</li>";
        echo "<li>Look for the line: <code>;extension=pdo_mysql</code></li>";
        echo "<li>Remove the semicolon to uncomment it: <code>extension=pdo_mysql</code></li>";
        echo "<li>Restart your web server</li>";
        echo "</ol>";
        echo "</div>";
    }
} else {
    echo "<div class='error'>❌ PDO extension is NOT loaded</div>";
}

echo "<h2>3. MySQL Extensions Check</h2>";

// Check mysqli
if (extension_loaded('mysqli')) {
    echo "<div class='success'>✅ MySQLi extension is loaded</div>";
} else {
    echo "<div class='error'>❌ MySQLi extension is NOT loaded</div>";
}

// Check mysql (deprecated)
if (extension_loaded('mysql')) {
    echo "<div class='warning'>⚠️ Old MySQL extension is loaded (deprecated)</div>";
} else {
    echo "<div class='success'>✅ Old MySQL extension is not loaded (good)</div>";
}

echo "<h2>4. All Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<p><strong>Total extensions loaded:</strong> " . count($extensions) . "</p>";
echo "<details><summary>Click to see all extensions</summary>";
echo "<ul>";
foreach ($extensions as $ext) {
    echo "<li>" . htmlspecialchars($ext) . "</li>";
}
echo "</ul>";
echo "</details>";

echo "<h2>5. PHP Configuration Locations</h2>";
echo "<p><strong>Loaded php.ini:</strong> " . (php_ini_loaded_file() ?: 'None') . "</p>";

$additional = php_ini_scanned_files();
if ($additional) {
    echo "<p><strong>Additional ini files:</strong></p>";
    echo "<pre>" . htmlspecialchars($additional) . "</pre>";
} else {
    echo "<p><strong>Additional ini files:</strong> None</p>";
}

echo "<h2>6. Quick Fix for XAMPP</h2>";
echo "<div class='warning'>";
echo "<h3>🔧 XAMPP PHP Configuration Fix:</h3>";
echo "<ol>";
echo "<li><strong>Open XAMPP Control Panel</strong></li>";
echo "<li><strong>Click 'Config' next to Apache</strong></li>";
echo "<li><strong>Select 'PHP (php.ini)'</strong></li>";
echo "<li><strong>Find this line:</strong> <code>;extension=pdo_mysql</code></li>";
echo "<li><strong>Remove the semicolon:</strong> <code>extension=pdo_mysql</code></li>";
echo "<li><strong>Save the file</strong></li>";
echo "<li><strong>Restart Apache in XAMPP</strong></li>";
echo "</ol>";
echo "</div>";

echo "<h2>7. Alternative: Use Built-in PHP Server with XAMPP PHP</h2>";
echo "<div class='warning'>";
echo "<h3>🔧 Use XAMPP's PHP instead:</h3>";
echo "<p>Stop your current PHP server and run:</p>";
echo "<code>C:\\xampp\\php\\php.exe -S localhost:8000</code>";
echo "<p>This uses XAMPP's PHP which should have MySQL extensions enabled.</p>";
echo "</div>";

echo "<h2>8. Test Commands</h2>";
echo "<div style='background:#f8f9fa;padding:15px;border-radius:5px;'>";
echo "<h4>PowerShell Commands to Try:</h4>";
echo "<pre>";
echo "# Stop current PHP server (Ctrl+C in the terminal)\n";
echo "# Then run XAMPP's PHP:\n";
echo "C:\\xampp\\php\\php.exe -S localhost:8000\n";
echo "\n";
echo "# Or check XAMPP PHP extensions:\n";
echo "C:\\xampp\\php\\php.exe -m | findstr -i mysql\n";
echo "</pre>";
echo "</div>";
?>
