<?php
/**
 * Phase 7 Test Script
 * Test Notifications & Communication functionality
 */

require_once 'config/config.php';
require_once 'includes/NotificationService.php';
require_once 'includes/MessagingService.php';

echo "<h1>🧪 Phase 7 Test: Notifications & Communication</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Testing Database Tables...</h2>";
    
    // Test if Phase 7 tables exist
    $tables = [
        'notifications',
        'notification_preferences', 
        'conversations',
        'conversation_participants',
        'messages',
        'message_read_status'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT 1 FROM $table LIMIT 1");
            $existingTables[] = $table;
            echo "<p>✅ Table <strong>$table</strong> exists</p>";
        } catch (Exception $e) {
            $missingTables[] = $table;
            echo "<p>❌ Table <strong>$table</strong> missing</p>";
        }
    }
    
    if (!empty($missingTables)) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Missing Tables</h3>";
        echo "<p>Please run the Phase 7 setup script first:</p>";
        echo "<p><a href='setup-phase7.php' class='btn btn-primary'>Run Phase 7 Setup</a></p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>Testing NotificationService...</h2>";
    
    // Test NotificationService
    $notificationService = new NotificationService($conn);
    
    // Get a test user
    $stmt = $conn->query("SELECT id FROM users LIMIT 1");
    $testUser = $stmt->fetch();
    
    if ($testUser) {
        $userId = $testUser['id'];
        
        // Test creating notification preferences
        echo "<p>Testing notification preferences creation...</p>";
        $preferences = $notificationService->getUserNotificationPreferences($userId);
        if ($preferences) {
            echo "<p>✅ Notification preferences loaded successfully</p>";
        } else {
            echo "<p>❌ Failed to load notification preferences</p>";
        }
        
        // Test creating a notification
        echo "<p>Testing notification creation...</p>";
        $notificationId = $notificationService->createNotification(
            $userId,
            'achievement_unlocked',
            'Test Achievement',
            'This is a test notification for Phase 7 testing.',
            ['test' => true],
            'badge',
            'test-badge-id'
        );
        
        if ($notificationId) {
            echo "<p>✅ Test notification created successfully (ID: $notificationId)</p>";
            
            // Test getting notifications
            $notifications = $notificationService->getUnreadNotifications($userId, 5);
            echo "<p>✅ Retrieved " . count($notifications) . " unread notifications</p>";
            
            // Test marking as read
            $success = $notificationService->markAsRead($notificationId, $userId);
            if ($success) {
                echo "<p>✅ Notification marked as read successfully</p>";
            } else {
                echo "<p>❌ Failed to mark notification as read</p>";
            }
        } else {
            echo "<p>❌ Failed to create test notification</p>";
        }
    } else {
        echo "<p>❌ No test user found in database</p>";
    }
    
    echo "<h2>Testing MessagingService...</h2>";
    
    // Test MessagingService
    $messagingService = new MessagingService($conn);
    
    // Get two test users
    $stmt = $conn->query("SELECT id FROM users LIMIT 2");
    $testUsers = $stmt->fetchAll();
    
    if (count($testUsers) >= 2) {
        $user1Id = $testUsers[0]['id'];
        $user2Id = $testUsers[1]['id'];
        
        // Test creating conversation
        echo "<p>Testing conversation creation...</p>";
        $conversationId = $messagingService->getOrCreateDirectConversation($user1Id, $user2Id);
        
        if ($conversationId) {
            echo "<p>✅ Conversation created successfully (ID: $conversationId)</p>";
            
            // Test sending message
            echo "<p>Testing message sending...</p>";
            $messageId = $messagingService->sendMessage(
                $conversationId,
                $user1Id,
                'This is a test message for Phase 7 testing.',
                'text'
            );
            
            if ($messageId) {
                echo "<p>✅ Test message sent successfully (ID: $messageId)</p>";
                
                // Test getting messages
                $messages = $messagingService->getConversationMessages($conversationId, $user2Id, 10);
                echo "<p>✅ Retrieved " . count($messages) . " messages from conversation</p>";
                
                // Test getting conversations
                $conversations = $messagingService->getUserConversations($user1Id, 10);
                echo "<p>✅ Retrieved " . count($conversations) . " conversations for user</p>";
            } else {
                echo "<p>❌ Failed to send test message</p>";
            }
        } else {
            echo "<p>❌ Failed to create test conversation</p>";
        }
    } else {
        echo "<p>❌ Need at least 2 users in database for messaging tests</p>";
    }
    
    echo "<h2>Testing API Endpoints...</h2>";
    
    // Test API endpoints (basic connectivity)
    $apiEndpoints = [
        '/beersty/api/notifications.php',
        '/beersty/api/messages.php',
        '/beersty/api/notification-preferences.php'
    ];
    
    foreach ($apiEndpoints as $endpoint) {
        $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . $endpoint;
        echo "<p>Testing endpoint: <code>$endpoint</code></p>";
        
        // Note: This is a basic test - actual functionality requires authentication
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 401) {
                echo "<p>✅ Endpoint accessible (returns 401 - authentication required as expected)</p>";
            } elseif ($httpCode === 200) {
                echo "<p>✅ Endpoint accessible (returns 200)</p>";
            } else {
                echo "<p>⚠️ Endpoint returns HTTP $httpCode</p>";
            }
        } else {
            echo "<p>⚠️ cURL not available for endpoint testing</p>";
        }
    }
    
    echo "<h2>Testing Frontend Pages...</h2>";
    
    // Test frontend pages
    $pages = [
        '/beersty/user/notifications.php' => 'Notifications Page',
        '/beersty/user/messages.php' => 'Messages Page',
        '/beersty/user/notification-preferences.php' => 'Notification Preferences Page'
    ];
    
    foreach ($pages as $page => $name) {
        $fullUrl = "http://" . $_SERVER['HTTP_HOST'] . $page;
        echo "<p>Testing page: <strong>$name</strong> (<code>$page</code>)</p>";
        
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 302) {
                echo "<p>✅ Page accessible (redirects to login as expected)</p>";
            } elseif ($httpCode === 200) {
                echo "<p>✅ Page accessible (returns 200)</p>";
            } else {
                echo "<p>⚠️ Page returns HTTP $httpCode</p>";
            }
        } else {
            echo "<p>⚠️ cURL not available for page testing</p>";
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎉 Phase 7 Testing Complete!</h3>";
    echo "<p>All core functionality appears to be working correctly.</p>";
    echo "</div>";
    
    echo "<h2>🚀 Next Steps</h2>";
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<ol>";
    echo "<li><strong>Login to test:</strong> <a href='/beersty/auth/login.php'>Login to your account</a></li>";
    echo "<li><strong>View notifications:</strong> <a href='/beersty/user/notifications.php'>Check your notifications</a></li>";
    echo "<li><strong>Send a message:</strong> <a href='/beersty/user/messages.php'>Try the messaging system</a></li>";
    echo "<li><strong>Configure settings:</strong> <a href='/beersty/user/notification-preferences.php'>Set notification preferences</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

ol {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
