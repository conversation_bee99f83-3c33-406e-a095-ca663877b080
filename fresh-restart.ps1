# Fresh Restart Script - Complete XAMPP Reset with PDO Setup
# This script completely restarts all services and ensures PDO is working

Write-Host "==================================================" -ForegroundColor Yellow
Write-Host "FRESH RESTART - Complete XAMPP Reset with PDO" -ForegroundColor Yellow
Write-Host "==================================================" -ForegroundColor Yellow

# Step 1: Stop all existing processes
Write-Host ""
Write-Host "STEP 1: Stopping all existing processes..." -ForegroundColor Cyan

# Stop Apache processes
$apacheProcesses = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($apacheProcesses) {
    Write-Host "Stopping Apache processes..." -ForegroundColor Yellow
    $apacheProcesses | Stop-Process -Force
    Start-Sleep 3
    Write-Host "Apache processes stopped" -ForegroundColor Green
} else {
    Write-Host "No Apache processes running" -ForegroundColor Gray
}

# Stop MySQL processes
$mysqlProcesses = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
if ($mysqlProcesses) {
    Write-Host "Stopping MySQL processes..." -ForegroundColor Yellow
    $mysqlProcesses | Stop-Process -Force
    Start-Sleep 3
    Write-Host "MySQL processes stopped" -ForegroundColor Green
} else {
    Write-Host "No MySQL processes running" -ForegroundColor Gray
}

# Stop XAMPP Control Panel
$xamppControl = Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue
if ($xamppControl) {
    Write-Host "Stopping XAMPP Control Panel..." -ForegroundColor Yellow
    $xamppControl | Stop-Process -Force
    Start-Sleep 2
    Write-Host "XAMPP Control Panel stopped" -ForegroundColor Green
}

Write-Host "All processes stopped successfully!" -ForegroundColor Green

# Step 2: Find XAMPP installation
Write-Host ""
Write-Host "STEP 2: Locating XAMPP installation..." -ForegroundColor Cyan

$xamppPaths = @(
    "C:\xampp",
    "C:\Program Files\XAMPP", 
    "C:\Program Files (x86)\XAMPP",
    "D:\xampp",
    "E:\xampp"
)

$xamppPath = $null
foreach ($path in $xamppPaths) {
    if (Test-Path $path) {
        $xamppPath = $path
        Write-Host "Found XAMPP at: $xamppPath" -ForegroundColor Green
        break
    }
}

if (-not $xamppPath) {
    Write-Host "ERROR: XAMPP not found!" -ForegroundColor Red
    Write-Host "Please install XAMPP from: https://www.apachefriends.org/download.html" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Fix PDO in php.ini
Write-Host ""
Write-Host "STEP 3: Configuring PDO MySQL extension..." -ForegroundColor Cyan

$phpIniPath = Join-Path $xamppPath "php\php.ini"
if (Test-Path $phpIniPath) {
    Write-Host "Found php.ini at: $phpIniPath" -ForegroundColor Green
    
    # Create backup
    $backupPath = $phpIniPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
    Copy-Item $phpIniPath $backupPath
    Write-Host "Backup created: $backupPath" -ForegroundColor Green
    
    # Read and modify php.ini
    $content = Get-Content $phpIniPath
    $modified = $false
    
    # Extensions to enable
    $extensionsToEnable = @(
        "extension=pdo_mysql",
        "extension=mysqli",
        "extension=pdo"
    )
    
    # Process each line
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Enable PDO extensions
        if ($line -match "^;\s*extension=pdo_mysql") {
            $content[$i] = "extension=pdo_mysql"
            Write-Host "Enabled: pdo_mysql" -ForegroundColor Yellow
            $modified = $true
        }
        elseif ($line -match "^;\s*extension=mysqli") {
            $content[$i] = "extension=mysqli"
            Write-Host "Enabled: mysqli" -ForegroundColor Yellow
            $modified = $true
        }
        elseif ($line -match "^;\s*extension=pdo") {
            $content[$i] = "extension=pdo"
            Write-Host "Enabled: pdo" -ForegroundColor Yellow
            $modified = $true
        }
    }
    
    # Write changes
    if ($modified) {
        $content | Set-Content $phpIniPath -Encoding UTF8
        Write-Host "php.ini updated successfully!" -ForegroundColor Green
    } else {
        Write-Host "PDO extensions already enabled" -ForegroundColor Green
    }
    
} else {
    Write-Host "ERROR: php.ini not found at: $phpIniPath" -ForegroundColor Red
}

# Step 4: Start XAMPP Control Panel
Write-Host ""
Write-Host "STEP 4: Starting XAMPP Control Panel..." -ForegroundColor Cyan

$xamppControl = Join-Path $xamppPath "xampp-control.exe"
if (Test-Path $xamppControl) {
    Start-Process $xamppControl -WindowStyle Normal
    Write-Host "XAMPP Control Panel started" -ForegroundColor Green
    Start-Sleep 3
} else {
    Write-Host "ERROR: XAMPP Control Panel not found" -ForegroundColor Red
}

# Step 5: Wait for manual service start
Write-Host ""
Write-Host "STEP 5: Manual Service Startup Required" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "In the XAMPP Control Panel that just opened:" -ForegroundColor White
Write-Host "1. Click START next to Apache" -ForegroundColor White
Write-Host "2. Click START next to MySQL" -ForegroundColor White
Write-Host "3. Wait for both to show 'Running' status" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter AFTER you have started both Apache and MySQL"

# Step 6: Test services
Write-Host ""
Write-Host "STEP 6: Testing services..." -ForegroundColor Cyan

# Test Apache
Write-Host "Testing Apache..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -UseBasicParsing
    Write-Host "SUCCESS: Apache is running (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Apache is not responding" -ForegroundColor Red
    Write-Host "Make sure you started Apache in XAMPP Control Panel" -ForegroundColor Yellow
}

# Test MySQL
Write-Host "Testing MySQL..." -ForegroundColor Yellow
$mysqlClient = Join-Path $xamppPath "mysql\bin\mysql.exe"
if (Test-Path $mysqlClient) {
    try {
        $result = & $mysqlClient -u root -e "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: MySQL is running" -ForegroundColor Green
        } else {
            Write-Host "ERROR: MySQL connection failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "ERROR: MySQL test failed" -ForegroundColor Red
    }
} else {
    Write-Host "WARNING: MySQL client not found" -ForegroundColor Yellow
}

# Step 7: Test PDO
Write-Host ""
Write-Host "STEP 7: Testing PDO..." -ForegroundColor Cyan
Write-Host "Opening PDO test page..." -ForegroundColor Yellow
Start-Process "http://localhost/beersty-lovable/test-pdo-simple.php"

# Step 8: Final URLs
Write-Host ""
Write-Host "STEP 8: Opening project URLs..." -ForegroundColor Cyan
Start-Sleep 2
Start-Process "http://localhost/beersty-lovable"
Start-Sleep 1
Start-Process "http://localhost/beersty-lovable/admin/user-management.php"
Start-Sleep 1
Start-Process "http://localhost/phpmyadmin"

Write-Host ""
Write-Host "==================================================" -ForegroundColor Green
Write-Host "FRESH RESTART COMPLETE!" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green
Write-Host ""
Write-Host "URLs opened:" -ForegroundColor Cyan
Write-Host "  PDO Test: http://localhost/beersty-lovable/test-pdo-simple.php" -ForegroundColor White
Write-Host "  Main Site: http://localhost/beersty-lovable" -ForegroundColor White
Write-Host "  User Management: http://localhost/beersty-lovable/admin/user-management.php" -ForegroundColor White
Write-Host "  phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Check the PDO test page - it should show all green checkmarks" -ForegroundColor White
Write-Host "2. Test ADD USER functionality in User Management" -ForegroundColor White
Write-Host "3. If PDO test fails, restart Apache in XAMPP Control Panel" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
