# FIX APACHE PHP CONFIGURATION
# The 500 error means <PERSON><PERSON> is not properly loaded in Apache

Write-Host "FIXING APACHE PHP CONFIGURATION" -ForegroundColor Red
Write-Host "===============================" -ForegroundColor Red

# Stop Apache first
Write-Host "Stopping Apache..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep 3

# Check Apache configuration
$httpdConf = "C:\xampp\apache\conf\httpd.conf"
Write-Host "Checking Apache config: $httpdConf" -ForegroundColor Cyan

if (Test-Path $httpdConf) {
    $content = Get-Content $httpdConf
    $phpModuleFound = $false
    $addTypeFound = $false
    
    # Check if PHP module is already configured
    foreach ($line in $content) {
        if ($line -match "LoadModule.*php.*module") {
            Write-Host "Found PHP module: $line" -ForegroundColor Green
            $phpModuleFound = $true
        }
        if ($line -match "AddType.*php") {
            Write-Host "Found PHP AddType: $line" -ForegroundColor Green
            $addTypeFound = $true
        }
    }
    
    # Add PHP configuration if missing
    if (-not $phpModuleFound -or -not $addTypeFound) {
        Write-Host "Adding PHP configuration to Apache..." -ForegroundColor Yellow
        
        # Create backup
        $backup = $httpdConf + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
        Copy-Item $httpdConf $backup
        Write-Host "Backup created: $backup" -ForegroundColor Gray
        
        # Add PHP module configuration
        $newContent = @()
        $moduleAdded = $false
        
        foreach ($line in $content) {
            $newContent += $line
            
            # Add PHP module after other LoadModule lines
            if ($line -match "^LoadModule.*" -and -not $moduleAdded -and -not $phpModuleFound) {
                $newContent += "LoadModule php_module php/php8apache2_4.dll"
                Write-Host "Added PHP module" -ForegroundColor Green
                $moduleAdded = $true
            }
        }
        
        # Add PHP file type handling at the end if not found
        if (-not $addTypeFound) {
            $newContent += ""
            $newContent += "# PHP Configuration"
            $newContent += "AddType application/x-httpd-php .php"
            $newContent += "PHPIniDir php"
            Write-Host "Added PHP file type handling" -ForegroundColor Green
        }
        
        # Save the updated configuration
        $newContent | Set-Content $httpdConf -Encoding UTF8
        Write-Host "Apache configuration updated" -ForegroundColor Green
    } else {
        Write-Host "PHP already configured in Apache" -ForegroundColor Green
    }
} else {
    Write-Host "Apache config file not found!" -ForegroundColor Red
    exit 1
}

# Check if PHP DLL exists
$phpDll = "C:\xampp\php\php8apache2_4.dll"
if (Test-Path $phpDll) {
    Write-Host "✅ PHP Apache module found: $phpDll" -ForegroundColor Green
} else {
    Write-Host "❌ PHP Apache module missing: $phpDll" -ForegroundColor Red
    
    # Try alternative names
    $altDlls = @(
        "C:\xampp\php\php8_module.dll",
        "C:\xampp\php\php_module.dll",
        "C:\xampp\apache\modules\php8apache2_4.dll"
    )
    
    foreach ($dll in $altDlls) {
        if (Test-Path $dll) {
            Write-Host "Found alternative PHP module: $dll" -ForegroundColor Yellow
            break
        }
    }
}

# Start Apache
Write-Host "Starting Apache..." -ForegroundColor Cyan
$apacheExe = "C:\xampp\apache\bin\httpd.exe"

try {
    Start-Process $apacheExe -WindowStyle Hidden
    Start-Sleep 5
    
    # Check if Apache started
    $apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
    if ($apache) {
        Write-Host "✅ Apache started successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Apache failed to start" -ForegroundColor Red
        
        # Check error log
        $errorLog = "C:\xampp\apache\logs\error.log"
        if (Test-Path $errorLog) {
            Write-Host "Last few errors from Apache log:" -ForegroundColor Yellow
            $errors = Get-Content $errorLog -Tail 5 -ErrorAction SilentlyContinue
            foreach ($error in $errors) {
                Write-Host "  $error" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "❌ Error starting Apache: $($_.Exception.Message)" -ForegroundColor Red
}

# Test basic PHP
Write-Host "Testing basic PHP..." -ForegroundColor Cyan
$basicTest = '<?php echo "PHP is working! Version: " . phpversion(); ?>'
$testFile = "basic-php-test.php"
$basicTest | Set-Content $testFile -Encoding UTF8

$testUrl = "http://localhost:8000/beersty/$testFile"
try {
    Start-Sleep 2
    $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 5 -UseBasicParsing
    Write-Host "✅ Basic PHP test successful!" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Basic PHP test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Open test URLs
Write-Host "Opening test URLs..." -ForegroundColor Cyan
Start-Process $testUrl
Start-Process "http://localhost:8000/beersty/pdo-mysql-test.php"

Write-Host ""
Write-Host "APACHE PHP CONFIGURATION COMPLETE" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""
Write-Host "Test URLs:" -ForegroundColor Yellow
Write-Host "  Basic PHP: $testUrl" -ForegroundColor White
Write-Host "  PDO Test: http://localhost:8000/beersty/pdo-mysql-test.php" -ForegroundColor White
Write-Host ""
Write-Host "If still getting 500 errors, check Apache error log:" -ForegroundColor Yellow
Write-Host "  C:\xampp\apache\logs\error.log" -ForegroundColor White

Read-Host "Press Enter to continue"
