# START APACHE & MYSQL + CONFIGURE PDO
# Complete script to start services and ensure PDO MySQL is working

Write-Host "🚀 STARTING APACHE & MYSQL + PDO CONFIGURATION" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Yellow

# Find XAMPP
$XamppPaths = @("C:\xampp", "C:\Program Files\XAMPP", "C:\Program Files (x86)\XAMPP", "D:\xampp")
$XamppPath = $XamppPaths | Where-Object { Test-Path $_ } | Select-Object -First 1

if (-not $XamppPath) {
    Write-Host "❌ XAMPP not found!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Using XAMPP at: $XamppPath" -ForegroundColor Green

# Step 1: Configure PDO FIRST (before starting Apache)
Write-Host ""
Write-Host "STEP 1: Configuring PDO MySQL Extension..." -ForegroundColor Cyan

$PhpIniPath = Join-Path $XamppPath "php\php.ini"
if (Test-Path $PhpIniPath) {
    Write-Host "Found php.ini at: $PhpIniPath" -ForegroundColor Green
    
    # Create backup
    $BackupPath = $PhpIniPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
    Copy-Item $PhpIniPath $BackupPath -ErrorAction SilentlyContinue
    Write-Host "Backup created: $BackupPath" -ForegroundColor Gray
    
    # Read and modify php.ini
    $Content = Get-Content $PhpIniPath
    $Modified = $false
    
    Write-Host "Enabling PDO extensions..." -ForegroundColor Yellow
    
    for ($i = 0; $i -lt $Content.Length; $i++) {
        $line = $Content[$i]
        
        # Enable PDO MySQL
        if ($line -match "^;\s*extension=pdo_mysql") {
            $Content[$i] = "extension=pdo_mysql"
            Write-Host "✅ Enabled: pdo_mysql" -ForegroundColor Green
            $Modified = $true
        }
        # Enable MySQLi
        elseif ($line -match "^;\s*extension=mysqli") {
            $Content[$i] = "extension=mysqli"
            Write-Host "✅ Enabled: mysqli" -ForegroundColor Green
            $Modified = $true
        }
        # Enable PDO
        elseif ($line -match "^;\s*extension=pdo\s*$") {
            $Content[$i] = "extension=pdo"
            Write-Host "✅ Enabled: pdo" -ForegroundColor Green
            $Modified = $true
        }
        # Enable OpenSSL (often needed)
        elseif ($line -match "^;\s*extension=openssl") {
            $Content[$i] = "extension=openssl"
            Write-Host "✅ Enabled: openssl" -ForegroundColor Green
            $Modified = $true
        }
    }
    
    if ($Modified) {
        $Content | Set-Content $PhpIniPath -Encoding UTF8
        Write-Host "✅ php.ini updated with PDO extensions" -ForegroundColor Green
    } else {
        Write-Host "✅ PDO extensions already enabled" -ForegroundColor Green
    }
} else {
    Write-Host "❌ php.ini not found at: $PhpIniPath" -ForegroundColor Red
}

# Step 2: Start Apache
Write-Host ""
Write-Host "STEP 2: Starting Apache..." -ForegroundColor Cyan

# Try multiple methods to start Apache
$ApacheStarted = $false

# Method 1: Use XAMPP batch file
$ApacheStartBat = Join-Path $XamppPath "apache_start.bat"
if (Test-Path $ApacheStartBat) {
    Write-Host "Starting Apache via batch file..." -ForegroundColor Yellow
    Start-Process $ApacheStartBat -WindowStyle Hidden -Wait
    Start-Sleep 3
    
    $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
    if ($Apache) {
        Write-Host "✅ Apache started via batch file!" -ForegroundColor Green
        $ApacheStarted = $true
    }
}

# Method 2: Direct executable
if (-not $ApacheStarted) {
    $ApacheExe = Join-Path $XamppPath "apache\bin\httpd.exe"
    if (Test-Path $ApacheExe) {
        Write-Host "Starting Apache directly..." -ForegroundColor Yellow
        Start-Process $ApacheExe -WindowStyle Hidden
        Start-Sleep 3
        
        $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
        if ($Apache) {
            Write-Host "✅ Apache started directly!" -ForegroundColor Green
            $ApacheStarted = $true
        }
    }
}

# Method 3: Manual instruction
if (-not $ApacheStarted) {
    Write-Host "❌ Automatic Apache start failed" -ForegroundColor Red
    Write-Host "👆 MANUAL: Click START next to Apache in XAMPP Control Panel" -ForegroundColor Yellow
    Read-Host "Press Enter AFTER you click Start next to Apache"
    
    $Apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
    if ($Apache) {
        Write-Host "✅ Apache is now running!" -ForegroundColor Green
        $ApacheStarted = $true
    } else {
        Write-Host "❌ Apache still not running" -ForegroundColor Red
    }
}

# Step 3: Start MySQL
Write-Host ""
Write-Host "STEP 3: Starting MySQL..." -ForegroundColor Cyan

$MySQLStarted = $false

# Method 1: Use XAMPP batch file
$MySQLStartBat = Join-Path $XamppPath "mysql_start.bat"
if (Test-Path $MySQLStartBat) {
    Write-Host "Starting MySQL via batch file..." -ForegroundColor Yellow
    Start-Process $MySQLStartBat -WindowStyle Hidden -Wait
    Start-Sleep 3
    
    $MySQL = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
    if ($MySQL) {
        Write-Host "✅ MySQL started via batch file!" -ForegroundColor Green
        $MySQLStarted = $true
    }
}

# Method 2: Direct executable
if (-not $MySQLStarted) {
    $MySQLExe = Join-Path $XamppPath "mysql\bin\mysqld.exe"
    if (Test-Path $MySQLExe) {
        Write-Host "Starting MySQL directly..." -ForegroundColor Yellow
        $MySQLArgs = "--defaults-file=`"$XamppPath\mysql\bin\my.ini`" --standalone --console"
        Start-Process $MySQLExe -ArgumentList $MySQLArgs -WindowStyle Hidden
        Start-Sleep 3
        
        $MySQL = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
        if ($MySQL) {
            Write-Host "✅ MySQL started directly!" -ForegroundColor Green
            $MySQLStarted = $true
        }
    }
}

# Method 3: Manual instruction
if (-not $MySQLStarted) {
    Write-Host "❌ Automatic MySQL start failed" -ForegroundColor Red
    Write-Host "👆 MANUAL: Click START next to MySQL in XAMPP Control Panel" -ForegroundColor Yellow
    Read-Host "Press Enter AFTER you click Start next to MySQL"
    
    $MySQL = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue
    if ($MySQL) {
        Write-Host "✅ MySQL is now running!" -ForegroundColor Green
        $MySQLStarted = $true
    } else {
        Write-Host "❌ MySQL still not running" -ForegroundColor Red
    }
}

# Step 4: Test Services
Write-Host ""
Write-Host "STEP 4: Testing Services..." -ForegroundColor Cyan

# Test Apache
$WebServerOK = $false
$WorkingURL = $null

$TestURLs = @("http://localhost", "http://localhost:80", "http://localhost:8080", "http://127.0.0.1")

foreach ($url in $TestURLs) {
    try {
        Write-Host "Testing $url..." -ForegroundColor Gray
        $Response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ $url is working! (Status: $($Response.StatusCode))" -ForegroundColor Green
        $WebServerOK = $true
        $WorkingURL = $url
        break
    } catch {
        Write-Host "❌ $url failed" -ForegroundColor Red
    }
}

# Test MySQL
$MySQLOK = $false
if ($MySQLStarted) {
    $MySQLClient = Join-Path $XamppPath "mysql\bin\mysql.exe"
    if (Test-Path $MySQLClient) {
        try {
            $Result = & $MySQLClient -u root -e "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ MySQL connection successful" -ForegroundColor Green
                $MySQLOK = $true
            } else {
                Write-Host "❌ MySQL connection failed" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ MySQL test failed" -ForegroundColor Red
        }
    }
}

# Step 5: Test PDO
Write-Host ""
Write-Host "STEP 5: Testing PDO MySQL..." -ForegroundColor Cyan

if ($WebServerOK) {
    $PDOTestURL = "$WorkingURL/beersty-lovable/test-pdo-simple.php"
    Write-Host "Opening PDO test: $PDOTestURL" -ForegroundColor Yellow
    Start-Process $PDOTestURL
    
    # Also create a quick PDO test
    $PDOTestContent = @"
<?php
echo "<h1>PDO Test Results</h1>";
if (extension_loaded('pdo')) {
    echo "<p style='color: green;'>✅ PDO is loaded</p>";
} else {
    echo "<p style='color: red;'>❌ PDO is NOT loaded</p>";
}

if (extension_loaded('pdo_mysql')) {
    echo "<p style='color: green;'>✅ PDO MySQL is loaded</p>";
} else {
    echo "<p style='color: red;'>❌ PDO MySQL is NOT loaded</p>";
}

try {
    `$pdo = new PDO("mysql:host=localhost", "root", "");
    echo "<p style='color: green;'>✅ PDO MySQL connection successful</p>";
} catch (Exception `$e) {
    echo "<p style='color: red;'>❌ PDO MySQL connection failed: " . `$e->getMessage() . "</p>";
}
?>
"@
    
    $PDOTestFile = "test-pdo-quick.php"
    $PDOTestContent | Set-Content $PDOTestFile -Encoding UTF8
    Write-Host "Created quick PDO test: $WorkingURL/$PDOTestFile" -ForegroundColor Gray
}

# Step 6: Open URLs
if ($WebServerOK) {
    Write-Host ""
    Write-Host "STEP 6: Opening Development URLs..." -ForegroundColor Cyan
    
    $URLs = @(
        "$WorkingURL/beersty-lovable",
        "$WorkingURL/beersty-lovable/admin/user-management.php",
        "$WorkingURL/phpmyadmin"
    )
    
    foreach ($url in $URLs) {
        Write-Host "Opening: $url" -ForegroundColor White
        Start-Process $url
        Start-Sleep 1
    }
}

# Step 7: Summary
Write-Host ""
Write-Host "🎉 STARTUP COMPLETE!" -ForegroundColor Green
Write-Host "====================" -ForegroundColor Green

Write-Host ""
Write-Host "SERVICE STATUS:" -ForegroundColor Cyan
Write-Host "Apache: $(if ($ApacheStarted) { '✅ RUNNING' } else { '❌ STOPPED' })" -ForegroundColor $(if ($ApacheStarted) { 'Green' } else { 'Red' })
Write-Host "MySQL: $(if ($MySQLStarted) { '✅ RUNNING' } else { '❌ STOPPED' })" -ForegroundColor $(if ($MySQLStarted) { 'Green' } else { 'Red' })
Write-Host "Web Server: $(if ($WebServerOK) { '✅ ACCESSIBLE' } else { '❌ NOT ACCESSIBLE' })" -ForegroundColor $(if ($WebServerOK) { 'Green' } else { 'Red' })

if ($WorkingURL) {
    Write-Host ""
    Write-Host "YOUR WORKING URLS:" -ForegroundColor Yellow
    Write-Host "Main Site: $WorkingURL/beersty-lovable" -ForegroundColor White
    Write-Host "User Management: $WorkingURL/beersty-lovable/admin/user-management.php" -ForegroundColor White
    Write-Host "PDO Test: $WorkingURL/beersty-lovable/test-pdo-simple.php" -ForegroundColor White
    Write-Host "phpMyAdmin: $WorkingURL/phpmyadmin" -ForegroundColor White
}

Write-Host ""
Write-Host "🧪 TEST ADD USER FUNCTIONALITY:" -ForegroundColor Yellow
Write-Host "1. Go to User Management page" -ForegroundColor White
Write-Host "2. Click 'Add User' button" -ForegroundColor White
Write-Host "3. Fill in form and submit" -ForegroundColor White
Write-Host "4. Should work with PDO MySQL now enabled!" -ForegroundColor White

Write-Host ""
Read-Host "Press Enter to exit"
