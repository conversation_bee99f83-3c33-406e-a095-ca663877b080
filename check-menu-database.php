<?php
// Check Menu Database Status
require_once 'config/config.php';

echo "=== Checking Menu Database Status ===" . PHP_EOL;

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Check if tables exist
    echo "\nChecking table existence..." . PHP_EOL;
    
    $tables = ['place_beers', 'place_food', 'beer_styles', 'food_categories'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->fetch();
        if ($exists) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✓ $table: $count records" . PHP_EOL;
        } else {
            echo "✗ $table: Table not found" . PHP_EOL;
        }
    }
    
    // Check recent additions
    echo "\nChecking recent menu additions..." . PHP_EOL;
    
    // Check recent beers
    $stmt = $pdo->query("SELECT * FROM place_beers ORDER BY created_at DESC LIMIT 5");
    $recent_beers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent beers added:" . PHP_EOL;
    if (empty($recent_beers)) {
        echo "  No beers found" . PHP_EOL;
    } else {
        foreach ($recent_beers as $beer) {
            echo "  • " . $beer['name'] . " (Place ID: " . substr($beer['place_id'], 0, 8) . "...) - " . $beer['created_at'] . PHP_EOL;
        }
    }
    
    // Check recent food
    $stmt = $pdo->query("SELECT * FROM place_food ORDER BY created_at DESC LIMIT 5");
    $recent_food = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nRecent food items added:" . PHP_EOL;
    if (empty($recent_food)) {
        echo "  No food items found" . PHP_EOL;
    } else {
        foreach ($recent_food as $food) {
            echo "  • " . $food['name'] . " (Place ID: " . substr($food['place_id'], 0, 8) . "...) - " . $food['created_at'] . PHP_EOL;
        }
    }
    
    // Check a specific place's menu
    echo "\nChecking first place's menu..." . PHP_EOL;
    $stmt = $pdo->query("SELECT id, name FROM breweries LIMIT 1");
    $place = $stmt->fetch();
    
    if ($place) {
        $place_id = $place['id'];
        $place_name = $place['name'];
        echo "Checking menu for: $place_name" . PHP_EOL;
        
        // Count beers for this place
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM place_beers WHERE place_id = ?");
        $stmt->execute([$place_id]);
        $beer_count = $stmt->fetchColumn();
        
        // Count food for this place
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM place_food WHERE place_id = ?");
        $stmt->execute([$place_id]);
        $food_count = $stmt->fetchColumn();
        
        echo "  Beers: $beer_count items" . PHP_EOL;
        echo "  Food: $food_count items" . PHP_EOL;
    }
    
    // Test API endpoint accessibility
    echo "\nTesting API endpoint..." . PHP_EOL;
    
    // Check if API file exists
    if (file_exists('api/menu-management.php')) {
        echo "✓ API file exists" . PHP_EOL;
        
        // Test with a simple GET request simulation
        $_GET['place_id'] = $place['id'] ?? 'test';
        $_GET['type'] = 'both';
        
        ob_start();
        include 'api/menu-management.php';
        $api_output = ob_get_clean();
        
        if (strpos($api_output, 'success') !== false || strpos($api_output, 'beers') !== false) {
            echo "✓ API responds correctly" . PHP_EOL;
        } else {
            echo "✗ API response issue: " . substr($api_output, 0, 100) . "..." . PHP_EOL;
        }
    } else {
        echo "✗ API file not found" . PHP_EOL;
    }
    
    echo "\n=== Database Check Complete ===" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
