# Nginx Configuration for Beersty Digital Board System
# Phase 8 - Production Deployment & Optimization

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker connections and file limits
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 50m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Brotli compression (if available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Cache zones
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=1g inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=10m use_temp_path=off;
    
    # Upstream PHP-FPM
    upstream php_backend {
        server php:9000;
        keepalive 32;
    }
    
    # Security headers map
    map $sent_http_content_type $security_headers {
        default "nosniff";
    }
    
    # Main server block
    server {
        listen 80;
        listen [::]:80;
        server_name _;
        root /var/www/html;
        index index.php index.html index.htm;
        
        # Security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
        
        # Content Security Policy
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https: blob:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self' https://api.openweathermap.org; media-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;" always;
        
        # Rate limiting
        limit_req zone=general burst=50 nodelay;
        limit_conn conn_limit_per_ip 20;
        
        # Health check endpoint (no rate limiting)
        location = /health {
            access_log off;
            try_files $uri $uri/ /health.php;
        }
        
        # API endpoints with stricter rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            try_files $uri $uri/ /index.php?$query_string;
        }
        
        # Authentication endpoints with very strict rate limiting
        location ~ ^/(auth|login|register) {
            limit_req zone=login burst=5 nodelay;
            try_files $uri $uri/ /index.php?$query_string;
        }
        
        # Static assets with long-term caching
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
            
            # Enable static file caching
            proxy_cache static_cache;
            proxy_cache_valid 200 1y;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            
            try_files $uri =404;
        }
        
        # Media uploads
        location /uploads/ {
            expires 30d;
            add_header Cache-Control "public";
            try_files $uri =404;
        }
        
        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ /(config|includes|logs|backups|scripts)/ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ \.(sql|log|conf|env)$ {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # PHP processing
        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass php_backend;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            
            # FastCGI optimizations
            fastcgi_buffer_size 128k;
            fastcgi_buffers 4 256k;
            fastcgi_busy_buffers_size 256k;
            fastcgi_temp_file_write_size 256k;
            fastcgi_connect_timeout 60s;
            fastcgi_send_timeout 60s;
            fastcgi_read_timeout 60s;
            
            # Security
            fastcgi_param HTTP_PROXY "";
            fastcgi_param SERVER_NAME $host;
        }
        
        # Default location
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }
        
        # Metrics endpoint for monitoring
        location = /metrics {
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
            
            content_by_lua_block {
                local metrics = {}
                
                -- Basic metrics
                table.insert(metrics, 'nginx_up 1')
                table.insert(metrics, 'nginx_connections_active ' .. ngx.var.connections_active)
                table.insert(metrics, 'nginx_connections_reading ' .. ngx.var.connections_reading)
                table.insert(metrics, 'nginx_connections_writing ' .. ngx.var.connections_writing)
                table.insert(metrics, 'nginx_connections_waiting ' .. ngx.var.connections_waiting)
                
                ngx.header.content_type = 'text/plain'
                ngx.say(table.concat(metrics, '\n'))
            }
        }
        
        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /var/www/html/errors;
            internal;
        }
        
        location = /50x.html {
            root /var/www/html/errors;
            internal;
        }
    }
    
    # HTTPS server block (when SSL is enabled)
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name _;
        root /var/www/html;
        index index.php index.html index.htm;
        
        # SSL configuration
        ssl_certificate /etc/nginx/ssl/live/domain/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/live/domain/privkey.pem;
        ssl_trusted_certificate /etc/nginx/ssl/live/domain/chain.pem;
        
        # SSL security settings
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_session_tickets off;
        
        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        
        # OCSP stapling
        ssl_stapling on;
        ssl_stapling_verify on;
        resolver ******* ******* valid=300s;
        resolver_timeout 5s;
        
        # Include all the same location blocks as HTTP server
        include /etc/nginx/sites-available/common-locations.conf;
    }
    
    # Redirect HTTP to HTTPS
    server {
        listen 80;
        listen [::]:80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }
}
