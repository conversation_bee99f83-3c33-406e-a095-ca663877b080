<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Location Search Demo - Beersty</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/location-search.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 50%, #4a2c17 100%);
            color: #fff;
            min-height: 100vh;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #ffc107;
            margin-right: 0.5rem;
        }
        .search-demo {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 10px;
            padding: 1.5rem;
        }
        .demo-input {
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
        }
        .demo-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="text-center mb-4">
            <h1 class="display-4 mb-3">
                <i class="fas fa-search me-3"></i>
                Smart Location Search Demo
            </h1>
            <p class="lead">Experience intelligent location suggestions with geolocation-based results</p>
        </div>

        <div class="demo-card">
            <h2 class="h3 mb-3">
                <i class="fas fa-magic me-2"></i>
                Features
            </h2>
            <ul class="feature-list">
                <li><i class="fas fa-location-arrow"></i> <strong>Geolocation-aware:</strong> Suggestions prioritized by your location</li>
                <li><i class="fas fa-database"></i> <strong>Local database:</strong> Cities with breweries from our database</li>
                <li><i class="fas fa-city"></i> <strong>Popular cities:</strong> Major US cities with distance calculations</li>
                <li><i class="fas fa-keyboard"></i> <strong>Smart autocomplete:</strong> Real-time suggestions as you type</li>
                <li><i class="fas fa-sort"></i> <strong>Intelligent sorting:</strong> Results sorted by relevance and distance</li>
                <li><i class="fas fa-mobile-alt"></i> <strong>Mobile-friendly:</strong> Responsive design with touch support</li>
            </ul>
        </div>

        <div class="demo-card">
            <h2 class="h3 mb-3">
                <i class="fas fa-flask me-2"></i>
                Try It Out
            </h2>
            <div class="search-demo">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group location-input-container">
                            <span class="input-group-text">
                                <i class="fas fa-map-marker-alt"></i>
                            </span>
                            <input type="text"
                                   id="demo-location-input"
                                   class="form-control demo-input"
                                   placeholder="Type a city name (e.g., 'san', 'new', 'chi')"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" 
                                id="demo-geo-btn" 
                                class="btn btn-primary w-100"
                                title="Use my current location">
                            <i class="fas fa-crosshairs me-2"></i>
                            Detect Location
                        </button>
                    </div>
                </div>
                
                <div id="demo-status" class="status-indicator status-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Start typing to see smart location suggestions appear below the input field.
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2 class="h3 mb-3">
                <i class="fas fa-keyboard me-2"></i>
                Keyboard Shortcuts
            </h2>
            <div class="row">
                <div class="col-md-6">
                    <p><kbd>Ctrl/Cmd + L</kbd> - Focus location input</p>
                    <p><kbd>Ctrl/Cmd + G</kbd> - Trigger geolocation</p>
                </div>
                <div class="col-md-6">
                    <p><kbd>↑ ↓</kbd> - Navigate suggestions</p>
                    <p><kbd>Enter</kbd> - Select suggestion</p>
                    <p><kbd>Esc</kbd> - Close suggestions</p>
                </div>
            </div>
        </div>

        <div class="demo-card">
            <h2 class="h3 mb-3">
                <i class="fas fa-code me-2"></i>
                Implementation Details
            </h2>
            <div class="row">
                <div class="col-md-6">
                    <h4>Frontend Technologies:</h4>
                    <ul>
                        <li>Vanilla JavaScript (ES6+)</li>
                        <li>Bootstrap 5 for styling</li>
                        <li>Font Awesome icons</li>
                        <li>CSS3 animations</li>
                        <li>Responsive design</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Backend Features:</h4>
                    <ul>
                        <li>PHP REST API</li>
                        <li>MySQL database integration</li>
                        <li>OpenStreetMap Nominatim API</li>
                        <li>Distance calculations</li>
                        <li>Intelligent result ranking</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="/" class="btn btn-outline-light btn-lg">
                <i class="fas fa-home me-2"></i>
                Back to Home Page
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const demoInput = document.getElementById('demo-location-input');
            const demoGeoBtn = document.getElementById('demo-geo-btn');
            const demoStatus = document.getElementById('demo-status');
            
            // Initialize smart location search for demo input
            if (typeof Beersty !== 'undefined' && Beersty.components) {
                Beersty.components.setupLocationAutocomplete(demoInput);
                
                // Add demo-specific event listeners
                demoInput.addEventListener('input', function() {
                    if (this.value.length >= 2) {
                        updateStatus('Searching for locations...', 'info');
                    } else if (this.value.length === 0) {
                        updateStatus('Start typing to see smart location suggestions.', 'info');
                    }
                });
                
                demoInput.addEventListener('change', function() {
                    if (this.value) {
                        updateStatus(`Selected: ${this.value}`, 'success');
                    }
                });
            }
            
            // Geolocation demo button
            demoGeoBtn.addEventListener('click', function() {
                const originalHTML = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Detecting...';
                this.disabled = true;
                
                if (!navigator.geolocation) {
                    updateStatus('Geolocation is not supported by this browser.', 'warning');
                    this.innerHTML = originalHTML;
                    this.disabled = false;
                    return;
                }
                
                navigator.geolocation.getCurrentPosition(
                    position => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        
                        demoInput.dataset.userLat = lat;
                        demoInput.dataset.userLng = lng;
                        demoInput.closest('.location-input-container').classList.add('has-location');
                        
                        updateStatus(`Location detected: ${lat.toFixed(4)}, ${lng.toFixed(4)}. Now try typing a city name!`, 'success');
                        
                        this.innerHTML = '<i class="fas fa-check me-2"></i>Located!';
                        setTimeout(() => {
                            this.innerHTML = originalHTML;
                            this.disabled = false;
                        }, 2000);
                    },
                    error => {
                        updateStatus(`Location detection failed: ${error.message}`, 'warning');
                        this.innerHTML = originalHTML;
                        this.disabled = false;
                    },
                    {
                        timeout: 10000,
                        enableHighAccuracy: false,
                        maximumAge: 300000
                    }
                );
            });
            
            function updateStatus(message, type) {
                demoStatus.className = `status-indicator status-${type}`;
                demoStatus.innerHTML = `<i class="fas fa-${getStatusIcon(type)} me-2"></i>${message}`;
            }
            
            function getStatusIcon(type) {
                const icons = {
                    'info': 'info-circle',
                    'success': 'check-circle',
                    'warning': 'exclamation-triangle'
                };
                return icons[type] || 'info-circle';
            }
        });
    </script>
</body>
</html>
