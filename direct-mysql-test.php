<?php
/**
 * Direct MySQL Connection Test
 * Tests the exact same connection as the login system
 */

echo "<h1>Direct MySQL Connection Test</h1>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;} .success{background:#d4edda;color:#155724;padding:10px;border-radius:5px;margin:10px 0;} .error{background:#f8d7da;color:#721c24;padding:10px;border-radius:5px;margin:10px 0;} .info{background:#d1ecf1;color:#0c5460;padding:10px;border-radius:5px;margin:10px 0;}</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. Testing Raw PDO Connection</h2>";
try {
    $dsn = "mysql:host=localhost;dbname=beersty_db;charset=utf8mb4";
    $conn = new PDO($dsn, 'root', '');
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>SUCCESS: Raw PDO connection works!</div>";
    
    // Test query
    $stmt = $conn->prepare("SELECT DATABASE() as db_name, VERSION() as version");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p><strong>Connected to:</strong> " . htmlspecialchars($result['db_name']) . "</p>";
    echo "<p><strong>MySQL Version:</strong> " . htmlspecialchars($result['version']) . "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>FAILED: Raw PDO connection failed</div>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    
    // Try without database name
    echo "<h3>Trying without database name...</h3>";
    try {
        $dsn2 = "mysql:host=localhost;charset=utf8mb4";
        $conn2 = new PDO($dsn2, 'root', '');
        $conn2->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<div class='success'>SUCCESS: Connection without database works!</div>";
        
        // Check if database exists
        $stmt = $conn2->prepare("SHOW DATABASES LIKE 'beersty_db'");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            echo "<div class='info'>Database 'beersty_db' exists</div>";
        } else {
            echo "<div class='error'>Database 'beersty_db' does NOT exist</div>";
            
            // Create it
            echo "<p>Creating database...</p>";
            $conn2->exec("CREATE DATABASE beersty_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<div class='success'>Database created!</div>";
        }
        
    } catch (Exception $e2) {
        echo "<div class='error'>FAILED: Even basic connection failed</div>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e2->getMessage()) . "</p>";
        exit;
    }
}

echo "<h2>2. Testing Database Class</h2>";
try {
    // Include the config
    require_once 'config/config.php';
    
    echo "<div class='info'>Config loaded successfully</div>";
    
    // Test Database class
    $db = new Database();
    echo "<div class='info'>Database class instantiated</div>";
    
    $testConn = $db->getConnection();
    echo "<div class='success'>SUCCESS: Database class connection works!</div>";
    
    // Test query
    $stmt = $testConn->prepare("SELECT 1 as test");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p><strong>Test query result:</strong> " . $result['test'] . "</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>FAILED: Database class connection failed</div>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    // Check if config file exists
    if (!file_exists('config/config.php')) {
        echo "<div class='error'>config/config.php file not found!</div>";
    } else {
        echo "<div class='info'>config/config.php file exists</div>";
    }
    
    if (!file_exists('config/database.php')) {
        echo "<div class='error'>config/database.php file not found!</div>";
    } else {
        echo "<div class='info'>config/database.php file exists</div>";
    }
}

echo "<h2>3. Testing Login Process Simulation</h2>";
if (isset($testConn)) {
    try {
        $email = '<EMAIL>';
        
        // Check if users table exists
        $stmt = $testConn->prepare("SHOW TABLES LIKE 'users'");
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            echo "<div class='error'>Users table does not exist!</div>";
            echo "<p><a href='xampp-setup.php' style='background:#007bff;color:white;padding:10px;text-decoration:none;border-radius:3px;'>Create Tables</a></p>";
        } else {
            echo "<div class='success'>Users table exists</div>";
            
            // Check if admin user exists
            $stmt = $testConn->prepare("SELECT COUNT(*) as count FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $userCount = $stmt->fetch()['count'];
            
            if ($userCount == 0) {
                echo "<div class='error'>Admin user does not exist!</div>";
                echo "<p><a href='debug-login.php' style='background:#28a745;color:white;padding:10px;text-decoration:none;border-radius:3px;'>Create Admin User</a></p>";
            } else {
                echo "<div class='success'>Admin user exists</div>";
                
                // Test the exact login query
                $stmt = $testConn->prepare("
                    SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
                    FROM users u 
                    JOIN profiles p ON u.id = p.id 
                    WHERE u.email = ?
                ");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user) {
                    echo "<div class='success'>Login query works! User found with role: " . htmlspecialchars($user['role']) . "</div>";
                } else {
                    echo "<div class='error'>Login query failed - user not found or no profile</div>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>Login simulation failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

echo "<h2>4. Summary</h2>";
echo "<div class='info'>";
echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php'>Try Login Page Again</a></li>";
echo "<li><a href='debug-login.php'>Run Full Debug Tool</a></li>";
echo "<li><a href='xampp-setup.php'>Setup Database Tables</a></li>";
echo "</ul>";
echo "</div>";
?>
