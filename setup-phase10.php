<?php
/**
 * Phase 10 Setup Script
 * Advanced Features & API Development
 */

require_once 'config/config.php';

echo "<h1>🚀 Phase 10 Setup: Advanced Features & API Development</h1>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h2>Setting up Phase 10 advanced features and API infrastructure...</h2>";
    
    // Read and execute the Phase 10 SQL file
    $sqlFile = 'database/phase10_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt) && !preg_match('/^\/\*/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    $createdTables = [];
    $createdIndexes = [];
    $createdProcedures = [];
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $conn->exec($statement);
                $successCount++;
                
                // Track what was created
                if (strpos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?(\w+)/', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    $createdTables[] = $tableName;
                    echo "<p>✅ Created table: <strong>$tableName</strong></p>";
                } elseif (strpos($statement, 'CREATE INDEX') !== false) {
                    preg_match('/CREATE INDEX.*?(\w+)/', $statement, $matches);
                    $indexName = $matches[1] ?? 'unknown';
                    $createdIndexes[] = $indexName;
                    echo "<p>✅ Created index: <strong>$indexName</strong></p>";
                } elseif (strpos($statement, 'CREATE PROCEDURE') !== false) {
                    preg_match('/CREATE PROCEDURE.*?(\w+)/', $statement, $matches);
                    $procedureName = $matches[1] ?? 'unknown';
                    $createdProcedures[] = $procedureName;
                    echo "<p>✅ Created procedure: <strong>$procedureName</strong></p>";
                } elseif (strpos($statement, 'CREATE OR REPLACE VIEW') !== false) {
                    preg_match('/CREATE OR REPLACE VIEW.*?(\w+)/', $statement, $matches);
                    $viewName = $matches[1] ?? 'unknown';
                    echo "<p>✅ Created view: <strong>$viewName</strong></p>";
                } elseif (strpos($statement, 'INSERT IGNORE') !== false) {
                    echo "<p>✅ Initialized default data</p>";
                }
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "<p>❌ Error executing statement: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>🚀 Setup Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<p><strong>✅ Successful operations:</strong> $successCount</p>";
    echo "<p><strong>❌ Failed operations:</strong> $errorCount</p>";
    echo "<p><strong>📋 Tables created:</strong> " . count($createdTables) . "</p>";
    echo "<p><strong>🔍 Indexes created:</strong> " . count($createdIndexes) . "</p>";
    echo "<p><strong>⚙️ Procedures created:</strong> " . count($createdProcedures) . "</p>";
    echo "</div>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>🎉 Phase 10 Setup Complete!</h3>";
        echo "<p>Advanced Features & API Development infrastructure has been successfully set up. You now have access to:</p>";
        echo "<ul>";
        echo "<li><strong>Public API:</strong> RESTful API with authentication and rate limiting</li>";
        echo "<li><strong>Social Media Sharing:</strong> Share content across multiple platforms</li>";
        echo "<li><strong>QR Code Generation:</strong> Generate QR codes for beers, breweries, and profiles</li>";
        echo "<li><strong>Payment System:</strong> Subscription management with Stripe integration</li>";
        echo "<li><strong>Webhook System:</strong> Real-time notifications for third-party integrations</li>";
        echo "<li><strong>Data Export:</strong> Export user data in multiple formats</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔑 API Features</h2>";
        echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h4>🌐 Public API:</h4>";
        echo "<ul>";
        echo "<li><strong>RESTful Endpoints:</strong> Comprehensive API for beers, breweries, users, and more</li>";
        echo "<li><strong>Authentication:</strong> API key-based authentication with multiple tiers</li>";
        echo "<li><strong>Rate Limiting:</strong> Configurable rate limits based on subscription tier</li>";
        echo "<li><strong>Documentation:</strong> Interactive API documentation with testing tools</li>";
        echo "<li><strong>Analytics:</strong> Detailed API usage analytics and monitoring</li>";
        echo "</ul>";
        
        echo "<h4>🔗 Third-Party Integrations:</h4>";
        echo "<ul>";
        echo "<li><strong>Social Media:</strong> Share to Facebook, Twitter, Instagram, LinkedIn, and more</li>";
        echo "<li><strong>Calendar Integration:</strong> Google Calendar and Outlook integration</li>";
        echo "<li><strong>Payment Processing:</strong> Stripe integration for subscriptions</li>";
        echo "<li><strong>QR Codes:</strong> Generate QR codes for various content types</li>";
        echo "<li><strong>Webhooks:</strong> Real-time notifications for external systems</li>";
        echo "</ul>";
        
        echo "<h4>💰 Subscription Features:</h4>";
        echo "<ul>";
        echo "<li><strong>Basic Plan:</strong> Free tier with basic API access (100 requests/hour)</li>";
        echo "<li><strong>Premium Plan:</strong> $9.99/month with enhanced features (1,000 requests/hour)</li>";
        echo "<li><strong>Pro Plan:</strong> $19.99/month with full API access (5,000 requests/hour)</li>";
        echo "<li><strong>Payment Management:</strong> Subscription lifecycle and billing management</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>📊 Advanced Analytics</h2>";
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<ul>";
        echo "<li><strong>API Usage Analytics:</strong> Track API requests, response times, and error rates</li>";
        echo "<li><strong>Social Sharing Analytics:</strong> Monitor sharing activity across platforms</li>";
        echo "<li><strong>QR Code Analytics:</strong> Track QR code scans and usage patterns</li>";
        echo "<li><strong>Webhook Analytics:</strong> Monitor webhook delivery success rates</li>";
        echo "<li><strong>Payment Analytics:</strong> Track subscription metrics and revenue</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔧 Technical Infrastructure</h2>";
        echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<ul>";
        echo "<li><strong>API Service:</strong> Comprehensive API management with authentication</li>";
        echo "<li><strong>Social Sharing Service:</strong> Multi-platform sharing functionality</li>";
        echo "<li><strong>QR Code Service:</strong> QR code generation and tracking</li>";
        echo "<li><strong>Payment Service:</strong> Stripe integration for subscriptions</li>";
        echo "<li><strong>Webhook Service:</strong> Reliable webhook delivery system</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🔗 Quick Links</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p><strong>API Documentation:</strong></p>";
        echo "<p><a href='/beersty/api/docs/' class='btn btn-primary' style='margin-right: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>View API Docs</a>";
        echo "<a href='/beersty/test-phase10.php' class='btn btn-success' style='margin-right: 10px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>Test Phase 10 Features</a></p>";
        
        echo "<p><strong>API Testing:</strong></p>";
        echo "<ul>";
        echo "<li>Visit the API documentation for interactive testing tools</li>";
        echo "<li>Generate API keys from your account settings</li>";
        echo "<li>Test endpoints with the built-in API tester</li>";
        echo "<li>Monitor API usage and analytics</li>";
        echo "</ul>";
        echo "</div>";
        
        // Test API functionality
        echo "<h2>🧪 Testing API Infrastructure</h2>";
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        
        try {
            // Test if API tables exist
            $stmt = $conn->query("SELECT COUNT(*) as count FROM api_keys");
            $result = $stmt->fetch();
            echo "<p>✅ API keys table: {$result['count']} keys configured</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM api_requests");
            $result = $stmt->fetch();
            echo "<p>✅ API requests table: {$result['count']} requests logged</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM social_shares");
            $result = $stmt->fetch();
            echo "<p>✅ Social shares table: {$result['count']} shares tracked</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM qr_codes");
            $result = $stmt->fetch();
            echo "<p>✅ QR codes table: {$result['count']} codes generated</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM user_subscriptions");
            $result = $stmt->fetch();
            echo "<p>✅ User subscriptions table: {$result['count']} subscriptions</p>";
            
            $stmt = $conn->query("SELECT COUNT(*) as count FROM webhooks");
            $result = $stmt->fetch();
            echo "<p>✅ Webhooks table: {$result['count']} webhooks configured</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ API testing error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "</div>";
        
        // Generate sample API key for admin user
        try {
            $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
            $stmt->execute();
            $adminUser = $stmt->fetch();
            
            if ($adminUser) {
                $apiKey = 'bst_' . bin2hex(random_bytes(32));
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO api_keys (user_id, api_key, name, tier, permissions, expires_at)
                    VALUES (?, ?, 'Admin API Key', 'premium', ?, DATE_ADD(NOW(), INTERVAL 1 YEAR))
                ");
                $stmt->execute([
                    $adminUser['id'],
                    $apiKey,
                    json_encode(['*'])
                ]);
                
                echo "<div style='background: #d1ecf1; color: #0c5460; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
                echo "<h4>🔑 Sample API Key Generated</h4>";
                echo "<p>A premium API key has been generated for the admin user:</p>";
                echo "<p><code style='background: #bee5eb; padding: 0.5rem; border-radius: 4px;'>$apiKey</code></p>";
                echo "<p><small>Use this key to test the API endpoints. You can generate additional keys from the user settings.</small></p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            // Ignore errors in sample key generation
        }
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3>⚠️ Setup Completed with Errors</h3>";
        echo "<p>Some operations failed. Please check the error messages above and try running the setup again.</p>";
        echo "<p>Common issues:</p>";
        echo "<ul>";
        echo "<li>Database permissions for creating tables and procedures</li>";
        echo "<li>MySQL version compatibility</li>";
        echo "<li>Existing table conflicts</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/beersty/'>← Back to Beersty</a> | <a href='test-phase10.php'>Test Phase 10 Features →</a> | <a href='/beersty/api/docs/'>View API Documentation →</a></p>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
