<?php
require_once 'config/config.php';

echo "<h2>🔧 Creating User Activities Table</h2>";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<h3>📋 Checking if user_activities table exists:</h3>";
    
    // Check if table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'user_activities'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "<p>✅ user_activities table already exists</p>";
    } else {
        echo "<p>❌ user_activities table does not exist. Creating it...</p>";
        
        // Create user_activities table
        $sql = "
        CREATE TABLE user_activities (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            activity_type VARCHAR(100) NOT NULL,
            metadata JSON NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_activities_user_id (user_id),
            INDEX idx_user_activities_type (activity_type),
            INDEX idx_user_activities_created (created_at)
        )";
        
        $conn->exec($sql);
        echo "<p>✅ user_activities table created successfully!</p>";
    }
    
    echo "<h3>📋 Table Structure:</h3>";
    
    // Show table structure
    $stmt = $conn->query("DESCRIBE user_activities");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Testing Activity Logging:</h3>";
    
    // Test activity logging
    if (isLoggedIn()) {
        $user = getCurrentUser();
        
        try {
            $activityStmt = $conn->prepare("
                INSERT INTO user_activities (user_id, activity_type, metadata, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $metadata = json_encode(['test' => 'activity_logging_test', 'timestamp' => time()]);
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $activityStmt->execute([
                $user['id'], 
                'test_activity', 
                $metadata,
                $ipAddress,
                $userAgent
            ]);
            
            echo "<p>✅ Activity logging test successful!</p>";
            
            // Show the logged activity
            $stmt = $conn->prepare("SELECT * FROM user_activities WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$user['id']]);
            $activity = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($activity) {
                echo "<p>Latest activity: <pre>" . htmlspecialchars(json_encode($activity, JSON_PRETTY_PRINT)) . "</pre></p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Activity logging test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>⚠️ Not logged in - cannot test activity logging</p>";
    }
    
    echo "<h3>✅ Fix Complete!</h3>";
    echo "<p>The user_activities table is now ready. You can go back to the <a href='/user/profile.php'>profile page</a> and try updating your profile again.</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>🔗 Next Steps:</h3>";
echo "<p>1. Go back to <a href='/user/profile.php'>Profile Page</a> and try updating your profile</p>";
echo "<p>2. The avatar upload should now work properly</p>";
echo "<p>3. If you still get errors, check <a href='/debug-profile.php'>debug-profile.php</a> for more details</p>";
?>
