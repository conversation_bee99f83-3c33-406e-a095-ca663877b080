# Simple XAMPP Restart Script with PDO Check
Write-Host "=== Beersty XAMPP Restart ===" -ForegroundColor Green

# Stop existing processes
Write-Host "Stopping existing services..." -ForegroundColor Yellow
Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "mysqld" -ErrorAction SilentlyContinue | Stop-Process -Force  
Get-Process -Name "xampp-control" -ErrorAction SilentlyContinue | Stop-Process -Force

Start-Sleep -Seconds 3
Write-Host "Services stopped." -ForegroundColor Green

# Add XAMPP PHP to PATH
if (Test-Path "C:\xampp\php\php.exe") {
    $env:PATH = "C:\xampp\php;$env:PATH"
    Write-Host "XAMPP PHP added to PATH" -ForegroundColor Green
} else {
    Write-Host "ERROR: XAMPP not found at C:\xampp\" -ForegroundColor Red
    exit 1
}

# Check PHP version
Write-Host "`nChecking PHP..." -ForegroundColor Yellow
$phpVersion = php -v 2>$null | Select-Object -First 1
Write-Host $phpVersion -ForegroundColor Green

# Check PDO extensions
Write-Host "`nChecking PDO extensions..." -ForegroundColor Yellow
$pdoCheck = php -m | Select-String "pdo"
$pdoMysqlCheck = php -m | Select-String "pdo_mysql"

if ($pdoCheck) {
    Write-Host "PDO: Available ✓" -ForegroundColor Green
} else {
    Write-Host "PDO: Missing ✗" -ForegroundColor Red
}

if ($pdoMysqlCheck) {
    Write-Host "PDO MySQL: Available ✓" -ForegroundColor Green  
} else {
    Write-Host "PDO MySQL: Missing ✗" -ForegroundColor Red
}

# Start XAMPP Control Panel
Write-Host "`nStarting XAMPP Control Panel..." -ForegroundColor Yellow
if (Test-Path "C:\xampp\xampp-control.exe") {
    Start-Process "C:\xampp\xampp-control.exe"
    Start-Sleep -Seconds 3
    Write-Host "XAMPP Control Panel started." -ForegroundColor Green
} else {
    Write-Host "ERROR: XAMPP Control Panel not found!" -ForegroundColor Red
    exit 1
}

# Instructions
Write-Host "`n=== MANUAL STEPS REQUIRED ===" -ForegroundColor Cyan
Write-Host "1. In XAMPP Control Panel, click START for Apache" -ForegroundColor White
Write-Host "2. In XAMPP Control Panel, click START for MySQL" -ForegroundColor White  
Write-Host "3. Wait for both to show green 'Running' status" -ForegroundColor White
Write-Host "`nPress Enter when both services are running..." -ForegroundColor Yellow
Read-Host

# Verify services
Write-Host "`nVerifying services..." -ForegroundColor Yellow
$apache = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
$mysql = Get-Process -Name "mysqld" -ErrorAction SilentlyContinue

if ($apache) {
    Write-Host "Apache: Running ✓" -ForegroundColor Green
} else {
    Write-Host "Apache: Not Running ✗" -ForegroundColor Red
}

if ($mysql) {
    Write-Host "MySQL: Running ✓" -ForegroundColor Green
} else {
    Write-Host "MySQL: Not Running ✗" -ForegroundColor Red
}

# Test database connection
if ($mysql) {
    Write-Host "`nTesting database connection..." -ForegroundColor Yellow
    $dbTest = php -r "try { new PDO('mysql:host=localhost', 'root', ''); echo 'SUCCESS'; } catch(Exception `$e) { echo 'FAILED'; }"
    
    if ($dbTest -eq "SUCCESS") {
        Write-Host "Database connection: SUCCESS ✓" -ForegroundColor Green
    } else {
        Write-Host "Database connection: FAILED ✗" -ForegroundColor Red
    }
}

# Ready to start development server
Write-Host "`n=== READY TO START ===" -ForegroundColor Green
Write-Host "Available URLs:" -ForegroundColor Cyan
Write-Host "• Development server: http://localhost:8000" -ForegroundColor White
Write-Host "• phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor White
Write-Host "• XAMPP Dashboard: http://localhost" -ForegroundColor White

Write-Host "`nStarting development server on port 8000..." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop" -ForegroundColor Red
Start-Sleep -Seconds 2

php -S localhost:8000
