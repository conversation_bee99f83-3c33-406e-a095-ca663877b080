<?php
/**
 * Fix Database Connection Issues
 * This script will set up the database properly for login
 */

echo "<h1>🔧 Fixing Database Connection</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Step 1: Testing Database Class</h2>";

require_once 'config/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connection successful!</p>";
    
    // Check what type of database we're using
    $driver = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<p><strong>Database Type:</strong> " . strtoupper($driver) . "</p>";
    
    if ($driver === 'sqlite') {
        echo "<p>📁 <strong>SQLite Database Location:</strong> database/beersty.sqlite</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h3>🛠️ Attempting to Fix...</h3>";
    
    // Create database directory if it doesn't exist
    if (!is_dir('database')) {
        mkdir('database', 0755, true);
        echo "<p>✅ Created database directory</p>";
    }
    
    // Try to create SQLite database manually
    try {
        $sqlite_path = __DIR__ . '/database/beersty.sqlite';
        $dsn = "sqlite:" . $sqlite_path;
        $conn = new PDO($dsn);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ SQLite database created successfully</p>";
        
        // Create tables
        echo "<h3>Creating Tables...</h3>";
        
        // Users table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                email_verified INTEGER DEFAULT 0,
                last_login DATETIME NULL
            )
        ");
        echo "<p>✅ Users table created</p>";
        
        // Profiles table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS profiles (
                id TEXT PRIMARY KEY,
                email TEXT NOT NULL,
                role TEXT DEFAULT 'customer' CHECK(role IN ('admin', 'brewery', 'customer')),
                first_name TEXT,
                last_name TEXT,
                brewery_id TEXT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        echo "<p>✅ Profiles table created</p>";
        
        // Breweries table
        $conn->exec("
            CREATE TABLE IF NOT EXISTS breweries (
                id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                name TEXT NOT NULL,
                description TEXT,
                city TEXT,
                state TEXT,
                brewery_type TEXT DEFAULT 'micro' CHECK(brewery_type IN ('micro', 'nano', 'regional', 'large', 'planning', 'bar', 'contract', 'proprietor')),
                claimed INTEGER DEFAULT 0,
                verified INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
        echo "<p>✅ Breweries table created</p>";
        
        // Check if admin user exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $adminExists = $stmt->fetchColumn();
        
        if ($adminExists == 0) {
            echo "<h3>Creating Admin User...</h3>";
            
            $adminId = 'admin-user-id';
            $adminEmail = '<EMAIL>';
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            
            // Insert admin user
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash, email_verified) VALUES (?, ?, ?, 1)");
            $stmt->execute([$adminId, $adminEmail, $adminPassword]);
            echo "<p>✅ Admin user created</p>";
            
            // Insert admin profile
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role, first_name, last_name) VALUES (?, ?, 'admin', 'Admin', 'User')");
            $stmt->execute([$adminId, $adminEmail]);
            echo "<p>✅ Admin profile created</p>";
            
        } else {
            echo "<p>ℹ️ Admin user already exists</p>";
        }
        
    } catch (Exception $sqlite_e) {
        echo "<p>❌ SQLite setup also failed: " . htmlspecialchars($sqlite_e->getMessage()) . "</p>";
        exit;
    }
}

echo "<h2>Step 2: Testing Login Functionality</h2>";

try {
    // Test if we can find the admin user
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id 
        FROM users u 
        JOIN profiles p ON u.id = p.id 
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ Admin user found in database</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</p>";
        echo "<p><strong>User ID:</strong> " . htmlspecialchars($user['id']) . "</p>";
        
        // Test password verification
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<p>✅ Password verification successful</p>";
        } else {
            echo "<p>❌ Password verification failed</p>";
        }
        
    } else {
        echo "<p>❌ Admin user not found in database</p>";
        
        // Try to create it again
        echo "<h3>Creating Admin User (Retry)...</h3>";
        
        $adminId = 'admin-user-id-' . time();
        $adminEmail = '<EMAIL>';
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        try {
            // Insert admin user
            $stmt = $conn->prepare("INSERT INTO users (id, email, password_hash, email_verified) VALUES (?, ?, ?, 1)");
            $stmt->execute([$adminId, $adminEmail, $adminPassword]);
            echo "<p>✅ Admin user created (retry)</p>";
            
            // Insert admin profile
            $stmt = $conn->prepare("INSERT INTO profiles (id, email, role, first_name, last_name) VALUES (?, ?, 'admin', 'Admin', 'User')");
            $stmt->execute([$adminId, $adminEmail]);
            echo "<p>✅ Admin profile created (retry)</p>";
            
        } catch (Exception $create_e) {
            echo "<p>❌ Failed to create admin user: " . htmlspecialchars($create_e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error testing login: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Step 3: Database Summary</h2>";

try {
    // Count users
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users");
    $stmt->execute();
    $userCount = $stmt->fetchColumn();
    echo "<p><strong>Total Users:</strong> $userCount</p>";
    
    // Count profiles
    $stmt = $conn->prepare("SELECT COUNT(*) FROM profiles");
    $stmt->execute();
    $profileCount = $stmt->fetchColumn();
    echo "<p><strong>Total Profiles:</strong> $profileCount</p>";
    
    // List all users
    $stmt = $conn->prepare("SELECT u.email, p.role FROM users u JOIN profiles p ON u.id = p.id");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    echo "<h4>All Users:</h4>";
    echo "<ul>";
    foreach ($users as $user) {
        echo "<li>" . htmlspecialchars($user['email']) . " (" . htmlspecialchars($user['role']) . ")</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ Error getting database summary: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🎉 Database Setup Complete!</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ You can now login with:</h4>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> admin123</p>";
echo "<p><a href='/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔐 Go to Login Page</a></p>";
echo "</div>";

echo "<h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='/auth/login.php'>Login Page</a></li>";
echo "<li><a href='/admin/dashboard.php'>Admin Dashboard</a></li>";
echo "<li><a href='/'>Homepage</a></li>";
echo "<li><a href='/final-dark-test.php'>Dark Mode Test</a></li>";
echo "</ul>";
?>
