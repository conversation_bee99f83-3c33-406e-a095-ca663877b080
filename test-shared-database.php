<?php
/**
 * Test Shared Database Implementation
 */

echo "<h1>🔗 Testing Shared Database</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
    .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    echo "<h2>Step 1: Testing Database Connection</h2>";
    
    require_once 'config/database.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "<p>✅ Database connection successful!</p>";
    
    // Check what type of database we're using
    $driver = $conn->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<p><strong>Database Type:</strong> " . strtoupper($driver) . "</p>";
    
    if ($driver === 'user_shared_json') {
        echo "<div class='success'>";
        echo "<h3>🎉 Using User-Space Shared Database!</h3>";
        echo "<p>The application is successfully using the shared database in your home directory.</p>";
        echo "</div>";
    }
    
    echo "<h2>Step 2: Testing Database Structure</h2>";
    
    // Check database symlinks
    $dbPath = __DIR__ . '/database';
    $uploadsPath = __DIR__ . '/uploads';
    
    if (is_link($dbPath)) {
        $target = readlink($dbPath);
        echo "<p>✅ Database is symlinked to: <code>$target</code></p>";
    } else {
        echo "<p>❌ Database is not symlinked</p>";
    }
    
    if (is_link($uploadsPath)) {
        $target = readlink($uploadsPath);
        echo "<p>✅ Uploads is symlinked to: <code>$target</code></p>";
    } else {
        echo "<p>❌ Uploads is not symlinked</p>";
    }
    
    echo "<h2>Step 3: Testing User Query</h2>";
    
    // Test the exact query used in login
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id
        FROM users u
        JOIN profiles p ON u.id = p.id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ Admin user found in shared database!</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($user['role']) . "</p>";
        echo "<p><strong>User ID:</strong> " . htmlspecialchars($user['id']) . "</p>";
        echo "<p><strong>First Name:</strong> " . htmlspecialchars($user['first_name'] ?? 'N/A') . "</p>";
        echo "<p><strong>Last Name:</strong> " . htmlspecialchars($user['last_name'] ?? 'N/A') . "</p>";
        
        // Test password verification
        if (password_verify('admin123', $user['password_hash'])) {
            echo "<p>✅ Password verification successful!</p>";
        } else {
            echo "<p>❌ Password verification failed!</p>";
        }
        
    } else {
        echo "<p>❌ Admin user not found in shared database!</p>";
    }
    
    echo "<h2>Step 4: Testing Login Simulation</h2>";
    
    // Simulate the exact login process
    $email = '<EMAIL>';
    $password = 'admin123';
    
    echo "<p>Testing login with:</p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> " . htmlspecialchars($email) . "</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    
    // Get user by email
    $stmt = $conn->prepare("
        SELECT u.id, u.email, u.password_hash, p.role, p.brewery_id, p.first_name, p.last_name
        FROM users u
        JOIN profiles p ON u.id = p.id
        WHERE u.email = ?
    ");
    $stmt->execute([$email]);
    $loginUser = $stmt->fetch();
    
    if ($loginUser && password_verify($password, $loginUser['password_hash'])) {
        echo "<div class='success'>";
        echo "<h3>✅ LOGIN SIMULATION SUCCESSFUL!</h3>";
        echo "<p>User would be redirected to admin dashboard</p>";
        echo "</div>";
        
        // Test session variables that would be set
        echo "<h4>Session Variables that would be set:</h4>";
        echo "<ul>";
        echo "<li>user_id: " . htmlspecialchars($loginUser['id']) . "</li>";
        echo "<li>user_email: " . htmlspecialchars($loginUser['email']) . "</li>";
        echo "<li>user_role: " . htmlspecialchars($loginUser['role']) . "</li>";
        echo "<li>brewery_id: " . htmlspecialchars($loginUser['brewery_id'] ?? 'null') . "</li>";
        echo "<li>first_name: " . htmlspecialchars($loginUser['first_name'] ?? 'N/A') . "</li>";
        echo "<li>last_name: " . htmlspecialchars($loginUser['last_name'] ?? 'N/A') . "</li>";
        echo "</ul>";
        
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ LOGIN SIMULATION FAILED!</h3>";
        if (!$loginUser) {
            echo "<p>Reason: User not found</p>";
        } else {
            echo "<p>Reason: Password verification failed</p>";
        }
        echo "</div>";
    }
    
    echo "<h2>Step 5: Shared Database Information</h2>";
    
    $homeDir = $_SERVER['HOME'] ?? '/home/' . get_current_user();
    $sharedPath = $homeDir . '/.beersty-shared';
    $sharedDbFile = $sharedPath . '/database/beersty_shared.json';
    
    if (file_exists($sharedDbFile)) {
        echo "<p>✅ Shared database file exists: <code>$sharedDbFile</code></p>";
        
        $json = file_get_contents($sharedDbFile);
        $data = json_decode($json, true);
        
        if ($data) {
            echo "<p>✅ JSON data is valid</p>";
            echo "<p><strong>Users count:</strong> " . count($data['users'] ?? []) . "</p>";
            echo "<p><strong>Profiles count:</strong> " . count($data['profiles'] ?? []) . "</p>";
            echo "<p><strong>Created by:</strong> " . htmlspecialchars($data['metadata']['created_by'] ?? 'unknown') . "</p>";
            echo "<p><strong>Created at:</strong> " . htmlspecialchars($data['metadata']['created_at'] ?? 'unknown') . "</p>";
            echo "<p><strong>Last modified:</strong> " . htmlspecialchars($data['metadata']['last_modified'] ?? 'unknown') . "</p>";
            
            echo "<h4>File Permissions:</h4>";
            $perms = fileperms($sharedDbFile);
            echo "<p><strong>Permissions:</strong> " . substr(sprintf('%o', $perms), -4) . "</p>";
            
        } else {
            echo "<p>❌ JSON data is invalid</p>";
        }
    } else {
        echo "<p>❌ Shared database file does not exist: <code>$sharedDbFile</code></p>";
    }
    
    echo "<h2>Step 6: Multi-User Setup Instructions</h2>";
    
    echo "<div class='info'>";
    echo "<h4>📋 For Other Users on This System:</h4>";
    echo "<p>To set up another user to use this shared database:</p>";
    echo "<ol>";
    echo "<li><strong>Give them access to the shared directory:</strong>";
    echo "<div class='code'>chmod -R 755 $sharedPath</div>";
    echo "</li>";
    echo "<li><strong>From their project directory, run:</strong>";
    echo "<div class='code'>php -S localhost:8001 router.php</div>";
    echo "<div class='code'>http://localhost:8001/setup-user-shared-db.php?action=link_existing&shared_path=" . urlencode($sharedPath) . "</div>";
    echo "</li>";
    echo "<li><strong>Or manually create symlinks:</strong>";
    echo "<div class='code'>";
    echo "cd /path/to/their/beersty-project<br>";
    echo "mv database database_backup_\$(date +%Y%m%d_%H%M%S)<br>";
    echo "mv uploads uploads_backup_\$(date +%Y%m%d_%H%M%S)<br>";
    echo "ln -s $sharedPath/database database<br>";
    echo "ln -s $sharedPath/uploads uploads";
    echo "</div>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . htmlspecialchars($e->getLine()) . "</p>";
    echo "</div>";
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div class='success'>";
echo "<p>If all tests above are successful, you can now:</p>";
echo "<ul>";
echo "<li><strong>Login:</strong> <a href='/auth/login.php'>Try logging in</a> with <EMAIL> / admin123</li>";
echo "<li><strong>Share Database:</strong> Other users can link to your shared database</li>";
echo "<li><strong>Collaborate:</strong> All users will see the same data and changes</li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>⚠️ Important Notes:</h4>";
echo "<ul>";
echo "<li>All users sharing this database will have the same login credentials</li>";
echo "<li>Changes made by one user will be visible to all users immediately</li>";
echo "<li>File uploads are also shared between all users</li>";
echo "<li>Make sure to backup the shared database regularly</li>";
echo "</ul>";
echo "</div>";
?>
